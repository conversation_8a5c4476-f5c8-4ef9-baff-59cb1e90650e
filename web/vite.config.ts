import react from '@vitejs/plugin-react';
import path from 'path';
import {
  TITLE_HEIGHT,
  TITLE_DESC_HEIGHT,
  PARAM_GAP,
  PARAM_HEIGHT,
  NODE_HEIGHT,
  NODE_WIDTH,
} from './src/utils/constant';
import { defineConfig } from 'vite';

let target = '';

let localTarget = 'http://localhost:8000';
// 本地开发
// target = 'http://localhost:8000';

// 测试环境
target = 'http://langbase-workflow.yf-onlinetest4.netease.com';

// dev环境
// target = 'http://langbase-workflow.yf-dev2.netease.com'
// target='http://*************:8888'
// target = 'http://qa-adage.igame.163.com';
//
// 线上环境
// target = 'https://langbase.netease.com';

// 预发环境
// target = 'https://langbase-pre.netease.com';

const envMap = {
  'https://langbase.netease.com': 'online',
  'https://langbase-pre.netease.com': 'pre',
  'http://langbase-workflow.yf-onlinetest4.netease.com': 'test',
  'http://localhost:8000': 'test', // 这里自行修改，如果本地server连接到线上，这里改成online
  'http://langbase-workflow.yf-dev2.netease.com': 'test'
};

const devEnv = envMap[target];

// https://vitejs.dev/config/
export default defineConfig({
  define: {
    'process.env.TEST_ENV': process.env.TEST_ENV,
    'process.env.DEV_ENV': JSON.stringify(devEnv),
  },
  server: {
    host: '0.0.0.0',
    hmr: {
      overlay: true,
      timeout: 30000,
    },
    watch: {
      usePolling: true,
    },
    proxy: {
      // '/animation': {
      //   target: 'https://qa.igame.163.com',
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/animation/, '')
      // },
      '/oauth': {
        target,
        headers: {
          'langbase-dev': 'true',
        },
        changeOrigin: true,
      },
      // '/api/v1/app/trigger': {
      //   target: localTarget,
      //   changeOrigin: true,
      // },
      // '/api/v1/workspace/f97bf87b-a8ec-4bc9-a8e3-662e111cd2e4/components': {
      //   target: localTarget,
      //   changeOrigin: true,
      // },
      // '/api/v1/components': {
      //   target: localTarget,
      //   changeOrigin: true,
      // },
      // '/api/v1/chat': {
      //   target: localTarget,
      //   changeOrigin: true,
      // },
      '/api': {
        target,
        headers: {
          'langbase-dev': 'true',
        },
        changeOrigin: true,
      },
      '/docs': {
        target,
        changeOrigin: true,
      },
      '/openapi.json': {
        target: 'http://langbase-local.yf-dev2.netease.com:8000',
        changeOrigin: true,
      },
    },
  },
  plugins: [
    react({
      babel: {
        plugins: [
          ['@babel/plugin-proposal-decorators', { legacy: true }],
          ['@babel/plugin-proposal-class-properties', { loose: false }],
        ],
      },
    }),
  ],
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          'title-height': `${TITLE_HEIGHT}px`,
          'param-height': `${PARAM_HEIGHT}px`,
          'param-gap': `${PARAM_GAP}px`,
          'node-width': `${NODE_WIDTH}px`,
          'node-height': `${NODE_HEIGHT}px`,
          'desc-height': `${TITLE_DESC_HEIGHT}px`,
        },
        charset: false,
        javascriptEnabled: true,
      },
    },
  },
  resolve: {
    alias: [
      {
        find: /^~/,
        replacement: '',
      },
      {
        find: '@',
        replacement: path.resolve(__dirname, './src'),
      },
      {
        find: '@api',
        replacement: path.resolve(__dirname, './src/api'),
      },
      {
        find: '@utils',
        replacement: path.resolve(__dirname, './src/utils'),
      },
      {
        find: '@components',
        replacement: path.resolve(__dirname, './src/components'),
      },
      {
        find: '@store',
        replacement: path.resolve(__dirname, './src/store'),
      },
      {
        find: '@interface',
        replacement: path.resolve(__dirname, './src/interface'),
      },
      {
        find: '@hooks',
        replacement: path.resolve(__dirname, './src/hooks'),
      },
      // '@': path.resolve(__dirname, './src'),
      // '@api': path.resolve(__dirname, './src/api'),
      // '@components': path.resolve(__dirname, './src/components'),
      // '@hooks': path.resolve(__dirname, './src/hooks'),
      // '@interface': path.resolve(__dirname, './src/interface'),
      // '@pages': path.resolve(__dirname, './src/pages'),
      // '@store': path.resolve(__dirname, './src/store'),
      // '@utils': path.resolve(__dirname, './src/utils'),
    ],
  },
  build: {
    commonjsOptions: {
      ignoreDynamicRequires: false,
    },
  },
});
