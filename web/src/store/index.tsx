/* eslint-disable no-fallthrough */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { createContext, ReactChild, useCallback, useReducer } from 'react';

import { GroupApi, UserApi } from '@/api';
import { AppApi } from '@/api/app';

import { WorkspaceApi } from '../api/workspace';
import {
  AppDetailModel,
  GlobalModel,
  GlobalModelProvider,
  GroupModel,
  UserModel,
  UserRoles,
  WorkspaceModel,
} from '../interface';
import { DefaultModelApi, ModelProviderApi } from '@/api/model-provider';

interface GlobalState {
  workspaces?: WorkspaceModel[];
  groups?: GroupModel[];
  workspace?: WorkspaceModel;
  group?: GroupModel;
  app?: AppDetailModel;
  user?: UserModel;
  roles?: UserRoles;
  running?: boolean;
  isMultiSelectionActive?: boolean;
  role?: string;
  workspaceRole?: any;
  groupRole?: any;
  modelList?: GlobalModel[];
  modelProviderList?: GlobalModelProvider[];
  appRole?: any;
}

export type GlobalResource = keyof GlobalState;

export type GlobalStateKey = GlobalResource;

interface PatchGlobalState {
  [index: string]: any;
}

interface IGlobalContext {
  globalState: GlobalState;
  updateGlobalState: (key: GlobalStateKey | PatchGlobalState, value?: any) => void;
  fetchGlobalState: (key: GlobalResource, params?: any, cb?: () => void) => void;
}

const initialState = {
  modelList: [],
};

const context = createContext({
  globalState: initialState,
} as IGlobalContext);

const { Provider } = context;

const StateProvider = ({ children }: { children: ReactChild }) => {
  // 此处尝试过使用 useState, 向子组件提供 setGlobalState 来改变全局变量，但会出现如下问题：
  // 1. 子组件的用法需要改为 setGlobalState({...globalState, [key]: value});
  // 2. 如果提供 setGlobalState 的包装便捷函数, 则子组件的 useEffect 需要监听该函数，否则改变的 state 可能是脏的，会存在竞争覆盖问题。
  // 综上使用 useReducer
  const [globalState, dispatch] = useReducer(
    (state: GlobalState, action: { type: string; payload: PatchGlobalState }) => {
      switch (action.type) {
        case 'UPDATE_STATE': {
          return {
            ...state,
            ...action.payload,
          };
        }
        default:
          return state;
      }
    },
    initialState,
  );

  function updateGlobalState(key: GlobalStateKey | PatchGlobalState, value?: any) {
    // @ts-ignore
    if (process.env.NODE_ENV === 'development') {
      console.log('[Global State Update]', key, value);
    }
    console.log('updateGlobalState', key, value);
    if (typeof key === 'string') {
      dispatch({
        type: 'UPDATE_STATE',
        payload: {
          [key]: value,
        },
      });
    } else {
      // 如果是更新group，顺带更新modelList
      if (key.group) {
        getModelList(key.group.id);
      }
      // 如果更新workspace，顺带更新modelProviderList
      if (key.workspace) {
        getModelList(undefined, key.workspace.id);
        getModelProviderList();
      }
      dispatch({
        type: 'UPDATE_STATE',
        payload: key,
      });
    }
  }

  const getModelProviderList = () => {
    return ModelProviderApi.listGlobalModelProvider().then((res) => {
      updateGlobalState('modelProviderList', res);
    });
  }

  const getModelList = (groupId?: string, workspaceId?: string) => {
    const state = (window as any)._GLOBAL_STATE;
    // 如果groupId不存在，则不获取modelList
    if (!groupId) {
      DefaultModelApi.listGlobalModel({
        workspaceId: workspaceId || state.workspace.id,
      }).then((res) => {
        updateGlobalState('modelList', res);
      });
    }
    DefaultModelApi.listGlobalModel({
      workspaceId: workspaceId || state.workspace.id,
      groupId: groupId
    }).then((res) => {
      updateGlobalState('modelList', res);
    });
  };

  // 此处必须由 api 调用的子组件提供 参数，若从 globalState 获取参数的话, 子组件的 useEffect 必须订阅该函数，且状态依赖不好维护
  function fetchGlobalState(key: GlobalResource, params?: any, cb?: () => void) {
    const update = (data: any) => {
      updateGlobalState(key, data);
      updateGlobalState('running', false);
    };


    switch (key) {
      case 'workspaces':
        WorkspaceApi.list().then(update).finally(cb);
        break;
      case 'groups':
        GroupApi.list(params).then(update).finally(cb);
        break;
      case 'app':
        AppApi.getAppDetail(params)
          .then((res) => {
            update({
              ...res,
              id: params,
            });
          })
          .finally(cb);
        UserApi.getResourceRole('app', params).then((role) => {
          updateGlobalState('appRole', role);
        });
        break;
      case 'modelProviderList':
        ModelProviderApi.listGlobalModelProvider().then(update).finally(cb);
        break;
      case 'roles':
        UserApi.userRoles().then(update).finally(cb);
      default:
        break;
    }
  }

  (window as any)._GLOBAL_STATE = globalState;

  return (
    <Provider
      value={{
        globalState,
        updateGlobalState: useCallback(updateGlobalState, []),
        fetchGlobalState: useCallback(fetchGlobalState, []),
      }}
    >
      {children}
    </Provider>
  );
};

export const globalContext = context;
export const GlobalContextProvider = StateProvider;
