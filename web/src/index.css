html {
  height: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu',
    'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
}

#root {
  height: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

#error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.code-editor {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.playground-img {
  border: 1px solid #91919133;
  max-width: 300px;
}

.ant-modal {
  top: 40px !important;
}

.provider {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 5px;
}

.v1 {
  color: red;
}

.v2 {
  color: orange;
}

.v4 {
  color: rgb(42, 219, 219);
}

.v5 {
  color: green;
}

.model-table .ant-table-cell {
  padding: 10px 8px !important;
}
.model-name {
  font-weight: 600;
}
.model-alias {
  font-size: 12px;
  color: #999;
}
.model-table .selected-row {
  background-color: #e6f4ff;
}
.model-table .selected-row .ant-table-cell-fix-left {
  background-color: #e6f4ff;
}
.magic-icon {
  background: linear-gradient(107deg, #2cb5ff 8%, #6b4eff 62%, #963aff 99%);
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-left: 0px;
  margin-inline-start: 0px !important;
}
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.text-16px {
  font-size: 16px;
}
.model-modal.ant-modal {
  top: 0 !important;
}

.popo-component .popo-name {
  font-size: 12px;
  color: #999;
}
.top-bar {
  position: fixed;
  top: -1px;
  width: 100vw;
  height: 1px;
  z-index: 10000;
  box-shadow: 0px -1px 13px 1px #ff4d4f;
}
.ant-picker-cell-inner {
  position: relative;
}

.red-dot {
  position: relative;
  color: red;
  margin: 0 2px;
}

.red-dot::after {
  content: ' ';
  position: absolute;
  top: 0;
  right: -2px;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: red;
}

#langbase-chat-list {
    user-select: text;
    h4 {
        text-align: left;
    }
  /* 基础表格样式 */
  table {
    width: 100%;
    border-collapse: collapse; /* 合并边框 */
    margin: 20px 0;
    font-size: 14px;
    text-align: left;
    border: 1px solid #f5f5f5;
    border-radius: 10px;
    overflow: hidden;
  }

  /* 表头样式 */
  table th {
    background-color: #fff;
    font-weight: bold;
    padding: 12px 15px;
    border: 1px solid #ddd; /* 表头边框 */
  }

  /* 表格内容样式 */
  table td {
    padding: 12px 15px;
    border: 1px solid #ddd; /* 单元格边框 */
    background-color: #f9f9f9;
  }
  .langbot-reason {
    h1,h2,h3 {
        font-size: 12px;
    }
    table {
        font-size: 12px;
    }
    blockquote {
        margin: 0;
    }
    pre {
        background-color: #fffdfd;
        padding: 2px 10px;
        border-radius: 10px;
    }
  }
}
