import React, { useState, useRef, useEffect } from 'react';
import { SendOutlined, PaperClipOutlined, FileTextOutlined, FilePdfOutlined, FileWordOutlined, DeleteOutlined, LoadingOutlined, FileExcelOutlined, EyeOutlined, PictureOutlined, AudioOutlined, VideoCameraOutlined, FileMarkdownOutlined, FileImageOutlined } from '@ant-design/icons';
import { Input, Button, Space, Tooltip, Tag, Modal, Spin, message, Image, Flex } from 'antd';
import styled from 'styled-components';
import { getAppId } from '@/utils/state';
import { uploadFile } from '@/utils/common';
import axios from 'axios';

const { TextArea } = Input;

// styled-components 样式定义
const InputComposerWrapper = styled.div`
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  width: 100%;
`;

const StyledTextArea = styled(TextArea)`
  &.composer-textarea {
    border: none !important;
    resize: none !important;
    padding: 12px !important;
    font-size: 14px;
    line-height: 1.5;
    /* 6行文字高度加上padding */
    /* max-height: calc(6 * 1.5em + 24px); */
    overflow-y: auto;
    
    &:focus {
      box-shadow: none !important;
    }
  }
  
  /* 确保文本不会遮挡按钮区 */
  &.ant-input-textarea-show-count::after {
    margin-bottom: 40px;
  }
`;

const ComposerToolbar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
`;

const ToolbarLeft = styled.div`
  display: flex;
  align-items: center;
`;

const ToolbarRight = styled.div`
  display: flex;
  align-items: center;
`;

// 文件展示区域样式
const FilePreviewWrapper = styled.div`
  margin: 5px;
  display: inline-block;
  max-width: 30%;
  background-color: #f8f8f8;
  border-radius: 12px;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FilePreviewHeader = styled.div`
  font-size: 12px;
  color: #888;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const FilePreviewContent = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const FileIcon = styled.div`
  font-size: 24px;
  color: #1890ff;
  flex-shrink: 0;
`;

const FileInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const FileName = styled.div`
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const FileDetails = styled.div`
  font-size: 12px;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const DeleteButton = styled.div`
  cursor: pointer;
  color: #999;
  padding: 4px;
  flex-shrink: 0;
  
  &:hover {
    color: #ff4d4f;
  }
`;

// 添加预览按钮样式
const PreviewButton = styled.div`
  cursor: pointer;
  color: #999;
  padding: 4px;
  
  &:hover {
    color: #40a9ff;
  }
`;

// 图片预览区域样式
const ImagePreviewsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
`;

const ImagePreviewItem = styled.div`
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
`;

const ImagePreview = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  position: absolute;
  bottom: 0;
  left: 0;
`;

const ImageDeleteButton = styled.div`
  position: absolute;
  top: 2px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  
  &:hover {
    background-color: rgba(255, 0, 0, 0.7);
  }
`;

// 获取文件图标
const getFileIcon = (fileType: string, status: FileUploadStatus) => {
  // 如果是上传中状态，显示加载动画
  if (status === 'uploading') {
    return <LoadingOutlined style={{ color: '#1890ff' }} spin />;
  }

  // 根据文件类型显示不同图标
  if (fileType.includes('pdf')) {
    return <FilePdfOutlined style={{ color: 'red' }} />;
  } else if (fileType.includes('csv') || fileType.includes('excel') || fileType.includes('spreadsheet') || fileType.includes('xls')) {
    return <FileExcelOutlined style={{ color: 'green' }} />;
  } else if (fileType.includes('word') || fileType.includes('doc')) {
    return <FileWordOutlined style={{ color: 'blue' }} />;
  } else {
    return <FileTextOutlined style={{ color: '#a0a0a0' }} />;
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes < 1024) {
    return bytes + 'B';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + 'KB';
  } else {
    return (bytes / (1024 * 1024)).toFixed(2) + 'MB';
  }
};

// 获取简化的文件类型名称
const getSimpleFileType = (fileType: string): string => {
  if (fileType.includes('pdf')) {
    return 'PDF';
  } else if (fileType.includes('csv') || fileType.includes('excel') || fileType.includes('spreadsheet') || fileType.includes('xls')) {
    return 'EXCEL';
  } else if (fileType.includes('word') || fileType.includes('doc')) {
    return 'WORD';
  } else if (fileType.includes('markdown') || fileType.includes('md')) {
    return 'MD';
  } else if (fileType.includes('text/plain')) {
    return 'TXT';
  } else {
    // 从MIME类型中提取简单类型
    const parts = fileType.split('/');
    return parts.length > 1 ? parts[1].toUpperCase() : parts[0].toUpperCase();
  }
};

// 添加上传状态类型
type FileUploadStatus = 'uploading' | 'success' | 'expired' | 'error';

// 扩展文件状态接口，添加预设文件支持
interface UploadFileState {
  file?: File;
  name: string;
  type: string;
  status: FileUploadStatus;
  data?: any; // 上传成功后的服务器返回数据
  error?: string; // 上传失败的错误信息
  url?: string; // 文件URL
  key?: string; // 文件唯一标识
}


// 统一的媒体项接口
interface MediaItem {
  type: 'file' | 'image' | 'audio' | 'video';  // 添加 audio 和 video 类型
  prompt?: string;
  content: {
    url: string;
    key: string;
    size?: number;
    name: string;
    type: string;
    status?: string;
    previewUrl?: string;
  };
}

// 组件Props接口
interface InputComposerProps {
  placeholder?: string;
  onSend: (type: string, content: any) => void;
  accept?: string;
  presetMedia?: MediaItem[]; // 修改为统一的媒体项数组
  maxTokens?: number;
  maxSize?: number;
  enableView?: boolean;
  enableAudio?: boolean;
  enableVideo?: boolean;
  fileView?: boolean;
  size?: 'small' | 'default';
}

// 修改文件检查方法，使用后端API进行检查
const checkFileExists = async (url: string): Promise<boolean> => {
  try {
    // 使用后端API检查文件是否存在
    const response = await axios.get(url, {
      timeout: 3000 // 设置3秒超时
    });
    return true;
  } catch (error) {
    // 检查是否是超时错误
    if (error instanceof Error && (error?.message.includes('timeout'))) {
      console.warn('获取文件内容超时，文件可能过大');
      return true;
    }
    console.error('检查文件是否存在失败:', error);
    return false;
  }
};

// 添加文件预览函数
const fetchFileContent = async (key: string) => {
  try {
    const response = await axios.get('/api/v1/get-cache-file', {
      params: { key }
    });
    console.log("response.data", response.data);
    return response?.data?.data || '';
  } catch (error) {
    console.error('获取文件内容失败:', error);
    message.error('获取文件内容失败');
    throw error;
  }
};

const InputComposer = (props: InputComposerProps) => {
  const {
    placeholder,
    onSend,
    accept = ".txt,.md,.docx,.doc,.pdf,.csv,.xlsx,.xls",
    presetMedia = [], // 修改为数组，默认为空数组
    maxSize = 30,
    maxTokens = 64,
    enableView = true,
    enableAudio = false,
    enableVideo = false,
    fileView = true,
    size = 'default'
  } = props;

  const [value, setValue] = useState('');
  const [uploadedFile, setUploadedFile] = useState<UploadFileState | null>(null);
  const [uploadedImages, setUploadedImages] = useState<MediaItem[]>([]);
  const textAreaRef = useRef(null);
  const fileInputRef = useRef(null);
  const imageInputRef = useRef(null);
  const mediaInputRef = useRef(null);

  // 记录上一次的maxTokens值
  const prevMaxTokensRef = useRef(maxTokens);

  // 添加文件预览状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImageVisible, setPreviewImageVisible] = useState(false);
  const [previewContent, setPreviewContent] = useState<string>('');
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  // 添加音视频状态管理
  const [uploadedMedia, setUploadedMedia] = useState<MediaItem[]>([]);

  // 处理预设媒体
  useEffect(() => {
    console.log("presetMedia..", presetMedia);
    if (presetMedia && presetMedia.length > 0) {
      // 处理预设媒体项
      presetMedia.forEach(item => {
        if (item.type === 'file') {
          // 检查链接是否有效
          checkFileExists(item.content.url).then(exists => {
            // 将预设文件信息转换为内部文件状态格式
            setUploadedFile({
              name: item.content.name,
              type: item.content.type,
              status: exists ? 'success' : 'expired',
              data: {
                size: item.content.size || 0,
                key: item.content.key,
                url: item.content.url,
                name: item.content.name,
                type: item.content.type
              },
              url: item.content.url,
              key: item.content.key,
              error: exists ? undefined : '文件链接已过期或不存在'
            });
          });
        } else if (item.type === 'image' && enableView) {
          // 只有在enableView为true时才添加图片
          setUploadedImages(prev => [...prev, item]);
        } else if (item.type === 'audio' && enableAudio) {
          setUploadedMedia(prev => [...prev, item]);
        } else if (item.type === 'video' && enableVideo) {
          setUploadedMedia(prev => [...prev, item]);
        }
      });
    }
  }, [presetMedia, enableView]);

  // 当enableView变化时，如果为false则清空图片数据
  useEffect(() => {
    if (!enableView) {
      // 清空所有图片预览URL
      uploadedImages.forEach(image => {
        if (image.content.previewUrl) {
          URL.revokeObjectURL(image.content.previewUrl);
        }
      });
      setUploadedImages([]);
    }
  }, [enableView]);

  // 当maxTokens变化时，清空文件数据
  useEffect(() => {
    // 检查maxTokens是否发生变化
    if (prevMaxTokensRef.current !== maxTokens) {
      setUploadedFile(null);
      prevMaxTokensRef.current = maxTokens;
    }
  }, [maxTokens]);

  // 处理输入变化
  const handleChange = (e) => {
    setValue(e.target.value);
  };

  // 处理发送
  const handleSend = () => {
    if (uploadedFile && uploadedFile.status === 'success') {
      // 如果有上传成功的文件，发送文件
      const mediaData: MediaItem[] = [{
        type: 'file',
        content: {
          url: uploadedFile.data.url,
          key: uploadedFile.data.key,
          size: uploadedFile.data.size,
          name: uploadedFile.name,
          type: uploadedFile.type
        }
      }];

      onSend('media', JSON.stringify({
        items: mediaData,
        prompt: value.trim(),
      }));
    } else if (uploadedImages.length > 0) {
      // 如果有上传的图片，发送它们
      const mediaData = {
        items: uploadedImages,
        prompt: value.trim()
      };
      onSend('media', JSON.stringify(mediaData));
    } else if (uploadedMedia.length > 0) {
      // 如果有上传的音频或视频，发送它们
      const mediaData = {
        items: uploadedMedia,
        prompt: value.trim()
      };
      onSend('media', JSON.stringify(mediaData));
    } else if (value.trim()) {
      // 否则发送文本
      onSend('text', value);
    }
    setValue('');
  };

  // 处理文件选择
  const handleFileSelect = async (e) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // 立即显示文件，状态为上传中
      setUploadedFile({
        file,
        name: file.name,
        type: file.type || getMimeTypeFromExtension(file.name),
        status: 'uploading'
      });

      const appId = getAppId();

      try {
        const result = await uploadFile(file, appId, {
          acceptedTypes: [
            'text/plain',                                // .txt
            'text/markdown',                             // .md
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
            'application/msword',                        // .doc
            'application/pdf',                           // .pdf
            'text/csv',                                  // .csv
            'application/vnd.ms-excel',                  // .xls
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' // .xlsx
          ],
          maxSize: maxSize,
          maxTokens: maxTokens,
        }, false);
        console.log("result", result);

        if (result.success && result.data) {
          // 更新为上传成功状态
          setUploadedFile(prev => ({
            ...prev!,
            status: 'success',
            data: result.data
          }));
        } else {
          // 更新为上传失败状态
          setUploadedFile(prev => ({
            ...prev!,
            status: 'error',
            error: result.message || '上传失败'
          }));
        }
      } catch (error) {
        // 处理上传异常
        setUploadedFile(prev => ({
          ...prev!,
          status: 'error',
          error: (error as any)?.message || '上传失败'
        }));
      }
    }

    // 重置文件输入框，以便可以选择相同的文件
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 删除上传的文件
  const handleDeleteFile = () => {
    setUploadedFile(null);
  };

  // 删除上传的图片
  const handleDeleteImage = (index) => {
    setUploadedImages(prev => {
      const newImages = [...prev];
      // 释放URL对象
      if (newImages[index].content.previewUrl) {
        URL.revokeObjectURL(newImages[index].content.previewUrl);
      }
      newImages.splice(index, 1);
      return newImages;
    });
  };

  // 打开文件选择对话框
  const openFileSelector = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };


  // 处理按键事件，支持Enter发送，Shift+Enter换行
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
    // Ctrl+Enter 也可以发送
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      handleSend();
    }
  };

  // 获取文件大小（如果没有size属性）
  const getFileSize = (file: UploadFileState) => {
    if (file.data?.size) {
      return formatFileSize(file.data.size);
    }
    return '';
  };

  // 处理预览按钮点击
  const handlePreview = async () => {
    if (!uploadedFile?.data?.key) return;

    setPreviewVisible(true);
    setPreviewLoading(true);

    try {
      // 尝试获取文件内容
      const content = await fetchFileContent(uploadedFile.data.key);
      setPreviewContent(content);
    } catch (error) {
      setPreviewContent('文件内容加载失败，可能文件已过期或不存在');
      message.error('文件内容加载失败');
    } finally {
      setPreviewLoading(false);
    }
  };

  // 关闭预览弹窗
  const handleClosePreview = () => {
    setPreviewVisible(false);
  };


  // 添加从文件扩展名获取MIME类型的辅助函数
  const getMimeTypeFromExtension = (filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase();

    const mimeTypes = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'doc': 'application/msword',
      'pdf': 'application/pdf',
      'csv': 'text/csv',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    };

    return mimeTypes[extension as keyof typeof mimeTypes] || 'application/octet-stream';
  };

  // 预览图片
  const handlePreviewImage = (url) => {
    setPreviewImage(url);
    setPreviewImageVisible(true);
  };

  // 判断是否可以发送消息
  const canSend = value.trim() !== '';

  // 获取媒体类型
  const getMediaType = (fileType: string): 'image' | 'audio' | 'video' | null => {
    if (fileType.startsWith('image/')) return 'image';
    if (fileType.startsWith('audio/')) return 'audio';
    if (fileType.startsWith('video/')) return 'video';
    return null;
  };

  // 修改媒体文件选择器的 accept 属性生成函数
  const getMediaAcceptTypes = (enableView: boolean, enableAudio: boolean, enableVideo: boolean) => {
    const accepts = [];
    if (enableView) accepts.push("image/jpeg,image/jpg,image/png,image/gif,image/webp");
    if (enableAudio) accepts.push("audio/mp3,audio/wav,audio/ogg,audio/m4a,audio/aac,audio/mpeg");
    if (enableVideo) accepts.push("video/mp4,video/webm,video/ogg");
    return accepts.join(',');
  };

  // 修改工具栏按钮的提示文本
  const getMediaUploadTooltip = (enableView: boolean, enableAudio: boolean, enableVideo: boolean) => {
    const types = [];
    if (enableView) types.push('图片');
    if (enableAudio) types.push('音频');
    if (enableVideo) types.push('视频');
    return `添加媒体文件 (支持${types.join('、')})`;
  };

  // 修改工具栏按钮的渲染函数
  const renderToolbarButtons = () => (
    <Space>
      {enableView && (
        <Tooltip title={uploadedFile ? "请先删除文件后再上传媒体文件" : "上传图片"}>
          <Button
            type="text"
            icon={<FileImageOutlined style={{ color: uploadedFile ? '#d9d9d9' : undefined }} />}
            onClick={uploadedFile ? undefined : () => mediaInputRef.current?.click()}
            disabled={!!uploadedFile}
          />
        </Tooltip>
      )}
      {enableAudio && (
        <Tooltip title={uploadedFile ? "请先删除文件后再上传媒体文件" : "上传音频"}>
          <Button
            type="text"
            icon={<AudioOutlined style={{ color: uploadedFile ? '#d9d9d9' : undefined }} />}
            onClick={uploadedFile ? undefined : () => mediaInputRef.current?.click()}
            disabled={!!uploadedFile}
          />
        </Tooltip>
      )}
      {enableVideo && (
        <Tooltip title={uploadedFile ? "请先删除文件后再上传媒体文件" : "上传视频"}>
          <Button
            type="text"
            icon={<VideoCameraOutlined style={{ color: uploadedFile ? '#d9d9d9' : undefined }} />}
            onClick={uploadedFile ? undefined : () => mediaInputRef.current?.click()}
            disabled={!!uploadedFile}
          />
        </Tooltip>
      )}
      <Tooltip title={
        uploadedImages.length > 0 ?
          "请先删除多媒体文件后再上传文件" :
          <div>
            <div>添加附件 (支持 .txt, .md, .docx, .doc, .pdf, .csv, .xlsx, .xls，最大{maxSize}MB)，纯文本内容不超过{maxTokens / 10}万个字符</div>
            <div style={{ marginTop: '8px' }}>
              <div>• TXT/CSV文件：最大约{maxTokens}KB</div>
              <div>• Excel文件：最大约{Math.round(maxTokens * 0.6)}KB</div>
              <div>• PDF/Word文件：最大约{maxSize}MB(根据文中图片资源大小而定)</div>
            </div>
          </div>
      }>
        <Button
          type="text"
          icon={<PaperClipOutlined style={{ color: uploadedImages.length > 0 ? '#d9d9d9' : undefined }} />}
          onClick={uploadedImages.length > 0 ? undefined : openFileSelector}
          disabled={uploadedImages.length > 0}
        />
      </Tooltip>
      <Button
        type="primary"
        icon={<SendOutlined />}
        onClick={handleSend}
        disabled={!canSend}
      >
        发送
      </Button>
    </Space>
  );

  // 修改媒体预览区域的渲染函数
  const renderMediaPreviews = () => (
    <>
      {uploadedMedia.length > 0 && (
        <div style={{ padding: '8px' }}>
          {uploadedMedia.map((media, index) => (
            <div key={index} style={{ marginBottom: '8px' }}>
              {media.type === 'image' && (
                <ImagePreviewItem>
                  <ImagePreview
                    src={media.content.previewUrl || media.content.url}
                    alt={media.content.name}
                    onClick={() => handlePreviewImage(media.content.previewUrl || media.content.url)}
                  />
                  <ImageDeleteButton onClick={() => handleDeleteMedia(index)}>
                    <DeleteOutlined style={{ fontSize: '12px' }} />
                  </ImageDeleteButton>
                </ImagePreviewItem>
              )}
              {(media.type === 'audio' || media.type === 'video') && (
                <FilePreviewWrapper>
                  <FilePreviewContent>
                    <FileIcon>
                      {media.type === 'audio' ? <AudioOutlined /> : <VideoCameraOutlined />}
                    </FileIcon>
                    <FileInfo>
                      <FileName>{media.content.name}</FileName>
                      <FileDetails>
                        {formatFileSize(media.content.size || 0)}
                      </FileDetails>
                    </FileInfo>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <PreviewButton onClick={() => window.open(media.content.url, '_blank')}>
                        <EyeOutlined />
                      </PreviewButton>
                      <DeleteButton onClick={() => handleDeleteMedia(index)}>
                        <DeleteOutlined />
                      </DeleteButton>
                    </div>
                  </FilePreviewContent>
                </FilePreviewWrapper>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );

  // 删除媒体文件
  const handleDeleteMedia = (index: number) => {
    setUploadedMedia(prev => {
      const newMedia = [...prev];
      if (newMedia[index].content.previewUrl) {
        URL.revokeObjectURL(newMedia[index].content.previewUrl);
      }
      newMedia.splice(index, 1);
      return newMedia;
    });
  };

  // 修改媒体文件选择处理函数
  const handleMediaSelect = async (e) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      const appId = getAppId();
      const mediaType = getMediaType(file.type);

      // 检查文件类型是否被允许
      if (!mediaType ||
        (mediaType === 'image' && !enableView) ||
        (mediaType === 'audio' && !enableAudio) ||
        (mediaType === 'video' && !enableVideo)) {
        message.error('不支持的文件类型');
        return;
      }

      const result = await uploadFile(file, appId, {
        permanent: true,
        acceptedTypes: [
          ...(enableView ? ['image/jpeg', 'image/png', 'image/gif', 'image/webp'] : []),
          ...(enableAudio ? ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/aac', 'audio/mpeg'] : []),
          ...(enableVideo ? ['video/mp4', 'video/webm', 'video/ogg'] : [])
        ],
        maxSize: 30
      });

      if (result.success && result.data) {
        const previewUrl = URL.createObjectURL(file);

        const newMedia: MediaItem = {
          type: mediaType,
          content: {
            ...result.data,
            size: file.size,
            previewUrl: result?.data?.url || previewUrl
          }
        };

        setUploadedMedia(prev => [...prev, newMedia]);
      }
    }

    if (mediaInputRef.current) {
      mediaInputRef.current.value = '';
    }
  };

  return (
    <InputComposerWrapper>
      {renderMediaPreviews()}
      {/* 文件预览区域 - 气泡样式 */}
      {uploadedFile && (
        <div style={{ padding: '4px 8px' }}>
          <FilePreviewWrapper style={{
            opacity: uploadedFile.status === 'uploading' ? 0.6 : 1,
            cursor: uploadedFile.status === 'uploading' ? 'wait' : 'default'
          }}>
            <FilePreviewHeader>
              {uploadedFile.status === 'uploading' ? '文件上传中...' :
                uploadedFile.status === 'expired' ? '文件已过期' :
                  uploadedFile.status === 'error' ? '上传失败' :
                    '仅识别附件中的文字'}
            </FilePreviewHeader>
            <FilePreviewContent>
              <FileIcon>{getFileIcon(uploadedFile.type, uploadedFile.status)}</FileIcon>
              <FileInfo>
                <FileName>{uploadedFile.name}</FileName>
                <FileDetails>
                  {getSimpleFileType(uploadedFile.type)} {getFileSize(uploadedFile)}
                  {(uploadedFile.status === 'error' || uploadedFile.status === 'expired') && (
                    <span style={{ color: '#ff4d4f' }}> - {uploadedFile.error}</span>
                  )}
                </FileDetails>
              </FileInfo>

              {/* 添加预览按钮 */}
              {uploadedFile.status === 'success' && uploadedFile.data?.key && (
                <div style={{ display: 'flex', gap: '8px' }}>
                  <PreviewButton onClick={handlePreview}>
                    <EyeOutlined />
                  </PreviewButton>
                  <DeleteButton onClick={handleDeleteFile}>
                    <DeleteOutlined />
                  </DeleteButton>
                </div>
              )}

              {/* 非成功状态只显示删除按钮 */}
              {(uploadedFile.status !== 'success' || !uploadedFile.data?.key) && (
                <DeleteButton onClick={handleDeleteFile}>
                  <DeleteOutlined />
                </DeleteButton>
              )}
            </FilePreviewContent>
            <div style={{ fontSize: '10px', marginTop: '4px', color: '#999' }}>
              <span style={{ fontWeight: 'bold' }}>注意：</span>
              <span>上传文件临时保存7天，过期后请重新上传</span>
            </div>
          </FilePreviewWrapper>
        </div>
      )}

      {/* 文件内容预览弹窗 */}
      <Modal
        title={`文件预览: ${uploadedFile?.name || ''}`}
        open={previewVisible}
        onCancel={handleClosePreview}
        footer={null}
        width="80%"
        style={{ top: 20 }}
        bodyStyle={{ maxHeight: 'calc(90vh - 100px)', overflow: 'auto' }}
      >
        <Spin spinning={previewLoading}>
          <pre style={{
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            padding: '16px',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px',
            maxHeight: 'calc(90vh - 150px)',
            overflow: 'auto'
          }}>
            {previewContent}
          </pre>
        </Spin>
      </Modal>

      {/* 图片预览区域 */}
      {uploadedImages.length > 0 && (
        <ImagePreviewsContainer>
          {uploadedImages.map((image, index) => (
            <ImagePreviewItem key={index}>
              <ImagePreview
                src={image.content.previewUrl || image.content.url}
                alt={image.content.name}
                onClick={() => handlePreviewImage(image.content.previewUrl || image.content.url)}
              />
              <ImageDeleteButton onClick={() => handleDeleteImage(index)}>
                <DeleteOutlined style={{ fontSize: '12px' }} />
              </ImageDeleteButton>
            </ImagePreviewItem>
          ))}
        </ImagePreviewsContainer>
      )}

      <Flex vertical={size === 'default'}>
        <StyledTextArea
          className="composer-textarea"
          ref={textAreaRef}
          value={value}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={(uploadedFile || uploadedImages.length > 0) ? "添加消息..." : (placeholder || "请输入内容...")}
          autoSize={{ minRows: 1, maxRows: 6 }}
        />

        <ComposerToolbar>
          <ToolbarLeft>
            <Space>
              {/* <Button type="text" icon={<span>DeepSeek</span>} />
            <Button type="text" icon={<span>深度思考(R1)</span>} />
            <Button type="text" icon={<span>联网搜索</span>} /> */}
            </Space>
          </ToolbarLeft>

          <ToolbarRight>
            {renderToolbarButtons()}
          </ToolbarRight>
        </ComposerToolbar>
      </Flex>

      {/* 隐藏的文件输入框 */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept={accept}
        onChange={handleFileSelect}
      />

      {/* 修改媒体文件输入框 */}
      {(enableView || enableAudio || enableVideo) && (
        <input
          type="file"
          ref={mediaInputRef}
          style={{ display: 'none' }}
          accept={getMediaAcceptTypes(enableView, enableAudio, enableVideo)}
          onChange={handleMediaSelect}
        />
      )}

      {/* 图片预览弹窗 */}
      <Modal
        visible={previewImageVisible}
        footer={null}
        onCancel={() => setPreviewImageVisible(false)}

        width="60%"
        centered
      >
        <img alt="预览图片" style={{ width: '100%', marginTop: '24px' }} src={previewImage} />
      </Modal>
    </InputComposerWrapper>
  );
};

// 添加默认属性

export default InputComposer;

export const withInfo = (presetMedia: MediaItem[], maxTokens: number) => {
  console.log("presetMedia123", presetMedia, maxTokens);
  return (props: Omit<InputComposerProps, 'maxTokens' | 'presetMedia'>) => {
    return <InputComposer {...props} presetMedia={presetMedia} maxTokens={maxTokens} />
  }
}