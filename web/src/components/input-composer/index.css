.input-composer {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: #fff;
  }
  
  .composer-textarea {
    border: none !important;
    resize: none !important;
    padding: 12px !important;
    font-size: 14px;
    line-height: 1.5;
    /* 6行文字高度加上padding */
    /* max-height: calc(6 * 1.5em + 24px); */
    overflow-y: auto;
  }
  
  .composer-textarea:focus {
    box-shadow: none !important;
  }
  
  .composer-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;
  }
  
  .toolbar-left {
    display: flex;
    align-items: center;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
  }
  
  /* 确保文本不会遮挡按钮区 */
  .ant-input-textarea-show-count::after {
    margin-bottom: 40px;
  }