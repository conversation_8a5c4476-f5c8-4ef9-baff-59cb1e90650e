import { copyText } from '@/utils/common';
import { CopyOutlined, EyeOutlined, CodeOutlined, DownloadOutlined } from '@ant-design/icons';
import { Button, Modal, Dropdown, MenuProps } from 'antd';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import styled from 'styled-components';
import { useState } from 'react';

const Block = styled.div`
  border-radius: 0.8em;
  overflow: hidden;
  position: relative;
  border: 1px solid rgb(52, 53, 65);
  margin: 5px 0;
  box-shadow: 3px 1px 5px 0px rgba(52, 53, 65, 0.2);
  background: rgb(52, 53, 65);
  .bar {
    display: flex;
    align-items: center;
    height: 32px;
    background-color: rgb(52, 53, 65);
    color: rgb(217, 217, 227);
    font-size: 12px;
    padding: 0 16px;
    border-top-left-radius: 0.8em;
    border-top-right-radius: 0.8em;
  }
  .copy-button {
    right: 0;
  }
  .button {
    position: absolute;
    color: #fff;
    &:hover {
      color: #fff !important;
    }
  }
  .preview-button {
    right: 60px;
  }
  .filling {
    color: #567fd8;
  }
  .code {
    font-size: 12px !important;
  }
  .arrow {
    margin-left: auto;
    color: #aaa;
  }
  .up {
    transform: rotate(180deg);
  }
  .semi-icon {
    vertical-align: sub;
    margin-right: 8px;
  }
  .hidden {
    display: none;
  }
`;

const CopyButton = ({ text }: { text: string }) => {
  return <Button className="copy-button button" type="text" icon={<CopyOutlined style={{ color: '#fff' }} />} onClick={() => {
    copyText(text);
  }}>
    复制
  </Button>
}

const CodeButton = ({ onToggle, showCode }: { onToggle: () => void, showCode: boolean }) => {
  return (
    <Button 
      type="text" 
      className="preview-button button"
      icon={showCode ? <EyeOutlined style={{ color: '#fff' }} /> : <CodeOutlined style={{ color: '#fff' }} />}
      onClick={onToggle}
    >
      {showCode ? '预览' : '代码'}
    </Button>
  );
};

const DownloadButton = ({ content, lang }: { content: string, lang: string }) => {
  const downloadHtml = () => {
    const blob = new Blob([content], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'page.html';
    link.click();
    URL.revokeObjectURL(url);
  };

  const downloadFile = (format: 'svg' | 'png') => {
    if (format === 'svg') {
      const blob = new Blob([content], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'image.svg';
      link.click();
      URL.revokeObjectURL(url);
      return;
    }

    const container = document.createElement('div');
    container.innerHTML = content;
    const svg = container.querySelector('svg');
    
    if (svg) {
      let svgWidth = parseInt(svg.getAttribute('width') || '800');
      let svgHeight = parseInt(svg.getAttribute('height') || '600');
      
      if (!svg.getAttribute('width') && !svg.getAttribute('height')) {
        const viewBox = svg.getAttribute('viewBox');
        if (viewBox) {
          const [, , vbWidth, vbHeight] = viewBox.split(' ').map(Number);
          svgWidth = vbWidth;
          svgHeight = vbHeight;
        }
      }

      const scale = 2;
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = svgWidth * scale;
      canvas.height = svgHeight * scale;
      
      if (ctx) {
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
      }

      const image = new Image();
      image.onload = () => {
        if (ctx) {
          ctx.fillStyle = 'white';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          
          ctx.scale(scale, scale);
          ctx.drawImage(image, 0, 0, svgWidth, svgHeight);
          
          const pngUrl = canvas.toDataURL('image/png', 1.0);
          const downloadLink = document.createElement('a');
          downloadLink.download = 'image.png';
          downloadLink.href = pngUrl;
          downloadLink.click();
        }
      };

      // 使用 encodeURIComponent 处理 SVG 内容
      const svgBase64 = btoa(unescape(encodeURIComponent(content)));
      image.src = `data:image/svg+xml;base64,${svgBase64}`;
    }
  };

  if (lang === 'html') {
    return (
      <Button 
        type="text" 
        className="download-button button"
        style={{ right: '120px' }}
        icon={<DownloadOutlined style={{ color: '#fff' }} />}
        onClick={downloadHtml}
      >
        下载
      </Button>
    );
  }

  const items: MenuProps['items'] = [
    {
      key: 'svg',
      label: '下载 SVG',
      onClick: () => downloadFile('svg')
    },
    {
      key: 'png',
      label: '下载 PNG',
      onClick: () => downloadFile('png')
    }
  ];

  return (
    <Dropdown menu={{ items }} placement="bottom">
      <Button 
        type="text" 
        className="download-button button"
        style={{ right: '120px' }}
        icon={<DownloadOutlined style={{ color: '#fff' }} />}
      >
        下载
      </Button>
    </Dropdown>
  );
};

export const CodeSnippet = (props: { lang: string, content: string, children: React.ReactNode }) => {
  const { lang, content, children } = props;
  const [showCode, setShowCode] = useState(!['html', 'svg'].includes(lang));

  const toggleView = () => {
    setShowCode(!showCode);
  };

  const renderPreview = () => {
    if (lang === 'html') {
      return (
        <iframe 
          srcDoc={content}
          style={{ 
            width: '100%', 
            height: '600px',
            border: 'none',
            backgroundColor: 'white'
          }}
          sandbox="allow-scripts"
          title="HTML预览"
        />
      );
    } else if (lang === 'svg') {
      return (
        <div 
          style={{ 
            width: '100%',
            minHeight: '200px',
            padding: '20px',
            backgroundColor: 'white',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      );
    }
    return null;
  };

  return <Block style={{ margin: '10px 40px 10px 0px' }}>
    <div className="bar">
      {lang}
      {['html', 'svg'].includes(lang) && <CodeButton onToggle={toggleView} showCode={showCode} />}
      {['svg', 'html'].includes(lang) && <DownloadButton content={content} lang={lang} />}
      <CopyButton text={content}></CopyButton>
    </div>
    {(['html', 'svg'].includes(lang) && !showCode) ? (
      renderPreview()
    ) : (
      <SyntaxHighlighter
        language={lang}
        // @ts-ignore
        style={oneDark}
        customStyle={{
          marginTop: 0,
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
        }}
      >
        {children}
      </SyntaxHighlighter>
    )}
  </Block>
}