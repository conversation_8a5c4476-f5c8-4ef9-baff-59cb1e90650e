import { AppApi } from "@/api/app";
import { templateIdMap } from "@/constants";
import { usePathQuery } from "@/hooks/useQuery";
import { IAppType } from "@/interface";
import { env } from "@/utils/common";
import { getDefaultConfig } from "@/utils/model-helper";
import { ExportOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import { Flex, Form, Input, message, Modal, Select, Tooltip } from "antd";
import { useState } from "react";

const templateAppss = [
  { label: '正确性评测', value: '4db093ec-163d-4ad9-ac45-c80093501ca8' },
  { label: '评测应用模板', value: '822a91ad-bfbf-43a1-99a3-e1346f2fcf27' }
]

const AppCopyModal = (props) => {
  const { trigger, modalProps, submitData, onChange } = props;
  const [open, setOpen] = useState(false);

  const [form] = Form.useForm();
  const { pushAddQuery } = usePathQuery();

  const { run: createApp, loading } = useRequest(
    async (app) => {
      const { id, app_config_id, createdAt, createdBy, updatedAt, updatedBy, ...rest } = app;
      console.log('rest',rest,'submitData',submitData);
      const appRes = await AppApi.createApp(
        {
          ...rest,
          ...submitData
        },
        submitData?.groupID || app.groupID
      );

      console.log('appRes', appRes);

      return appRes;
    },
    {
      manual: true,
      onSuccess: (data) => {
        // // @ts-ignore
        // messageApi.open({
        //   type: 'success',
        //   content: '应用创建成功',
        // });
        onCancel();
        message.success('应用创建成功');
        onChange?.();
        // pushAddQuery('/app/dev', {
        //   appId: data.id,
        //   type: data.type,
        // });
        window.open(`/app/dev?appId=${data.id}`, '_blank')
      },
      onError: err => {
        // @ts-ignore
        appCopyModalRef.current?.setLoading(false);
      }
    },
  );

  const { data: templateApps } = useRequest(async () => {
    const res = await AppApi.listAppsByGroup(templateIdMap[env], {
      // @ts-ignore
      appType: IAppType.Evaluator,
      pageSize: 100,
      pageNumber: 1,
      showAll: true,
      userID: ''
    });
    return res.items?.map(item => {
      return {
        label: item.name,
        value: item.id,
      }
    })
  }, {

  })

  const onCancel = () => {
    setOpen(false);
  }

  const onOpen = () => {
    setOpen(true);
  };

  const onOk = async () => {
    const val = await form.validateFields();
    if (val.templateId) {
      // 获取应用详情
      const appDetail = await AppApi.getAppDetail(val.templateId);
      console.log('appDetail', appDetail);

      const { createdAt, createdBy, updatedAt, app_config_id, name, description, ...restProps } = appDetail;
      createApp({
        ...restProps,
        type: IAppType.Evaluator,
        name: val?.name,
        description: val?.description
      });
    } else {
      // 获取默认app配置
      const defaultConfig = getDefaultConfig(IAppType.AgentCompletion);
      createApp({
        name: val?.name,
        description: val?.description,
        // 新建评测应用
        type: IAppType.Evaluator,
        config: {
          ...defaultConfig,
        }
      });
    }
  }

  return <>
    <div onClick={onOpen}>
      {trigger || '复制应用'}
    </div>
    <Modal
      title="新建评测应用"
      onCancel={onCancel}
      onOk={onOk}
      open={open}
      okButtonProps={{
        loading
      }}
      cancelButtonProps={{
        disabled: loading
      }}
      {...modalProps}>
      <Form form={form} layout="vertical">
        <Form.Item name="name" label="应用名称" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        {/* {value?.type === IAppType.Workflow && <Form.Item name="workflowId" label="英文名" rules={[{ required: true }]}>
          <Input
            placeholder="唯一标识（支持英文、数字、横线和下划线）"
          />
        </Form.Item>} */}
        <Form.Item name="templateId" label="评测模板" rules={[{ required: true }]}>
          <Select options={templateApps}
            optionRender={op => {
              const { data } = op || {};
              return <Flex justify="space-between">
                <Flex gap={4} align="center">
                  {data.label}
                </Flex>
                <Flex style={{ color: '#333' }}>
                  <Tooltip title={`查看【${data.label}】`}>
                    <ExportOutlined onClick={e => {
                      e.stopPropagation();
                      window.open(`/app/${data.value}?appId=${data?.value}`)
                    }} />
                  </Tooltip>
                </Flex>
              </Flex>
            }} />
        </Form.Item>
        <Form.Item name="description" label="应用功能介绍">
          <Input.TextArea />
        </Form.Item>
      </Form>
    </Modal>
  </>
};

export default AppCopyModal;
