/* eslint-disable react/jsx-no-undef */
// @ts-nocheck
import type { InputRef } from 'antd';
import {
  Button,
  Collapse,
  Form,
  Input,
  Pagination,
  Popconfirm,
  Result,
  Table,
  Tag,
} from 'antd';
import type { FormInstance } from 'antd/es/form';
import { pick } from 'lodash';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import { ComponentApi } from '@/api/component';
import { EnvApi } from '@/api/env';
import { useRole } from '@/hooks/useAdmin';
import { getGroupId, getWorkspaceId } from '@/utils/state';

const EditableContext = React.createContext<FormInstance<any> | null>(null);

interface Item {
  key: string;
  name: string;
  age: string;
  address: string;
}

interface EditableRowProps {
  index: number;
}

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  // console.log('props', props);
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  children: React.ReactNode;
  EditRender: React.ReactNode;
  dataIndex: keyof Item;
  record: Item;
  handleSave: (record: Item) => void;
}

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  handleSave,
  EditRender,
  value,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false);
  const inputRef = useRef<InputRef>(null);
  const form = useContext(EditableContext)!;

  useEffect(() => {
    if (editing) {
      inputRef.current!.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
    form.setFieldsValue({ [dataIndex]: value ? value(record) : record[dataIndex] });
  };

  const save = async () => {
    try {
      const values = await form.validateFields();
      toggleEdit();
      handleSave({ ...record, ...values });
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
      window.corona.error('Save failed', errInfo);
    }
  };

  let childNode = children;

  if (editable) {
    childNode = editing ? (
      <Form.Item
        style={{ margin: 0 }}
        name={dataIndex}
        rules={[
          {
            required: true,
            message: `${title}必填`,
          },
        ]}
      >
        {EditRender ? (
          React.cloneElement(EditRender, {
            onPressEnter: save,
            onBlur: save,
            ref: inputRef,
          })
        ) : (
          <Input ref={inputRef} onPressEnter={save} onBlur={save} />
        )}
      </Form.Item>
    ) : (
      <div
        className="editable-cell-value-wrap"
        style={{ paddingRight: 24 }}
        onClick={toggleEdit}
      >
        {children[1] || '点击编辑'}
      </div>
    );
  }

  return <td {...restProps}>{childNode}</td>;
};

type EditableTableProps = Parameters<typeof Table>[0];

interface DataType {
  key: React.Key;
  id: string;
  name: string;
  value: string;
}

type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

const EnvComp = ({ envs, getList, type = 'app', role }) => {
  const [dataSource, setDataSource] = useState<DataType[]>();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (envs) {
      setLoading(false);
      setDataSource(
        envs.map((e) => ({
          ...e,
          key: e.id,
          value: e.value,
        })),
      );
    }
  }, [envs]);

  const handleDelete = async (key: React.Key) => {
    const newData = [...dataSource];
    const index = newData.findIndex((item) => key === item.key);
    const item = newData[index];
    newData.splice(index, 1, {
      ...item,
      value: '',
    });
    setDataSource(newData);
    setLoading(true);
    await EnvApi.remove(item.id);
    await getList();
    setLoading(false);
  };

  const defaultColumns: (ColumnTypes[number] & {
    editable?: boolean;
    dataIndex: string;
    EditRender?: React.ReactNode;
  })[] = [
    {
      title: '环境变量名',
      dataIndex: 'name',
      width: '30%',
      editable: false,
    },
    {
      title: '环境变量值',
      dataIndex: 'value',
      width: '50%',
      editable: true,
      value: (record) => (record.scopeType !== type ? '' : record.value),
      render: (val, record) => {
        console.log('record', record, type);
        if (record.scopeType !== type && val) {
          return (
            <span style={{ color: '#999' }}>
              <Tag>{record.scopeType}</Tag>
              {val}
            </span>
          );
        }
        return val || '点击编辑';
      },
    },
    {
      title: '操作',
      dataIndex: '操作',
      render: (_, record: { id: React.Key }) =>
        dataSource.length >= 1 ? (
          <Popconfirm title="确定重置吗?" onConfirm={() => handleDelete(record.id)}>
            <Button disabled={record.scopeType !== type || role !== 'admin'}>
              重置值
            </Button>
          </Popconfirm>
        ) : null,
    },
  ];

  const handleSave = async (row: DataType) => {
    console.log('row', row);
    const newData = [...dataSource];
    const index = newData.findIndex((item) => row.key === item.key);
    const item = newData[index];
    console.log('item', item);
    newData.splice(index, 1, {
      ...item,
      ...row,
    });
    setDataSource(newData);
    // 如果有值，则可以进行修改操作
    setLoading(true);
    if (row.value && row.value !== '点击编辑') {
      if (item.scopeType === type) {
        await EnvApi.update(row, row.id);
      } else {
        await EnvApi.create(pick(row, ['name', 'value', 'componentID', 'param']), type);
      }
    }
    await getList();
    setLoading(false);
  };

  const components = {
    body: {
      row: EditableRow,
      cell: EditableCell,
    },
  };

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: DataType) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        EditRender: col.EditRender,
        value: col.value,
        title: col.title,
        handleSave,
      }),
    };
  });

  return (
    <div>
      <Table
        loading={loading}
        components={components}
        rowClassName={() => 'editable-row'}
        bordered
        pagination={false}
        dataSource={dataSource}
        columns={columns as ColumnTypes}
      />
    </div>
  );
};

export default EnvComp;

function EnvChildren(props) {
  const { id, type, scopeId, role } = props;
  const [list, setList] = useState([]);
  async function getList() {
    const res = await EnvApi.list(id, 'components', type, scopeId);
    setList(res as any);
  }

  useEffect(() => {
    getList();
  }, []);

  console.log('envs', list);
  return <EnvComp envs={list} getList={getList} type={type} role={role}></EnvComp>;
}

export function EnvSetting({ type }) {
  const role = useRole(type);
  const hasRight = role === 'admin' || role === 'developer';
  const workspaceId = getWorkspaceId();
  const groupId = getGroupId();
  const [page, setPage] = useState({
    current: 1,
    totoal: 1,
  });
  const [list, setList] = useState([]);
  async function getList(page = 1) {
    const res = await ComponentApi.listWithEnv(workspaceId, page, 5, true);
    console.log('rs', res);
    setList(res.items);
    setPage({
      current: page,
      total: res.total,
    });
  }

  useEffect(() => {
    if (hasRight) {
      getList();
    }
  }, [hasRight]);

  console.log('list', list);
  const items = useMemo(() => {
    return list.map((v) => ({
      id: v.name,
      label: v.name,
      children: (
        <EnvChildren
          id={v.id}
          role={role}
          type={type}
          scopeId={type === 'workspace' ? workspaceId : groupId}
        ></EnvChildren>
      ),
    }));
  }, [list]);

  const handleChange = (a) => {
    console.log('aa', a);
  };

  if (!hasRight) {
    return <Result status="403" title="无权限" subTitle="对不起，您暂时无权限查看." />;
  }

  return (
    <div>
      <Collapse key={page.current} items={items} onChange={handleChange}></Collapse>
      <Pagination
        {...page}
        pageSize={5}
        onChange={getList}
        style={{
          float: 'right',
          marginTop: '20px',
        }}
      ></Pagination>
    </div>
  );
}
