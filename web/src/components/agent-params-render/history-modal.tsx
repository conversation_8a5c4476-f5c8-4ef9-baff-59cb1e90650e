import { Mo<PERSON>, <PERSON>, Button, Space } from 'antd';
import { useState } from 'react';
import ReactJson from 'react-json-view';

interface HistoryModalProps {
  visible: boolean;
  onClose: () => void;
  historyList: Array<{
    id: string;
    title: string;
    params: any;
    createTime: string;
  }>;
  onApply: (params: any) => void;
}

const HistoryModal: React.FC<HistoryModalProps> = ({
  visible,
  onClose,
  historyList,
  onApply,
}) => {
  const [selectedHistory, setSelectedHistory] = useState<any>(null);

  return (
    <Modal
      title={<p>
        <span>历史运行记录</span>
        <span style={{ fontSize: '12px', color: '#999', fontWeight: 'normal' }}>(最多展示10条本地记录)</span>
      </p>}
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
    >
      <div style={{ display: 'flex', height: '500px' }}>
        {/* 左侧列表 */}
        <div style={{ width: '300px', borderRight: '1px solid #f0f0f0', overflowY: 'auto' }}>
          <List
            dataSource={historyList}
            renderItem={(item) => (
              <List.Item
                style={{
                  cursor: 'pointer',
                  backgroundColor: selectedHistory?.id === item.id ? '#f0f0f0' : 'transparent',
                  padding: '8px 16px',
                }}
                onClick={() => setSelectedHistory(item)}
              >
                <div>
                  <div>{item.title}</div>
                  <div style={{ fontSize: '12px', color: '#999' }}>{item.createTime}</div>
                </div>
              </List.Item>
            )}
          />
        </div>
        
        {/* 右侧JSON展示 */}
        <div style={{ flex: 1, padding: '0 16px', display: 'flex', flexDirection: 'column' }}>
          <Space style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              disabled={!selectedHistory}
              onClick={() => selectedHistory && onApply(selectedHistory.params)}
            >
              应用参数
            </Button>
          </Space>
          <div style={{ flex: 1, overflowY: 'auto' }}>
            {selectedHistory ? (
              <ReactJson
                src={selectedHistory.params}
                name={false}
                theme="rjv-default"
                displayDataTypes={false}
                displayObjectSize={false}
                enableClipboard={false}
                style={{ backgroundColor: 'transparent' }}
              />
            ) : (
              <div style={{ textAlign: 'center', color: '#999', marginTop: '100px' }}>
                请选择左侧历史记录查看详情
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default HistoryModal; 