import { CaretRightOutlined, HistoryOutlined } from '@ant-design/icons';
import { Button, Form, Flex, Typography, Space } from 'antd';
import { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';

import { ParamTypeEnum } from '@/interface/agent';

import { LForm, LFormInput, LFormInputNumber, LFormSelect, LFormSwitch, LFormTextArea } from '../form';
import { useGlobalState } from '@/hooks/useGlobalState';
import { IModelSettingProps } from '../model-select';
import { isModelSupportAudio, isModelSupportImage, isModelSupportVideo } from '@/utils/model-helper';
import { LFormUploadWithPopo } from '../form/form-items/form-upload_withpopo';
import Uploader from '../uploader';
import { LFormDatePicker } from '../form/form-items/form-date';
import { getLocalAppData, getLocalData, saveLocalAppData } from '@/utils/common';
import dayjs from 'dayjs';
import HistoryModal from './history-modal';

const { Title, Paragraph } = Typography;

const layout = {
  labelCol: {
    // span: 6
  },
  wrapperCol: {
    // span: 18
  },
};

interface IProps {
  showCommitBtn?: boolean;
  commitBtnName?: string;
  commitBtnIcon?: ReactNode;
  paramsInPrompt: any[];
  onCommit?: (params: any) => void;
  onChange?: (params: any) => void;
  layout?: 'vertical' | 'horizontal';
  labelAlign?: 'left' | 'right';
  extra?: ReactNode;
  style?: React.CSSProperties;
  showImage?: boolean;
  modelConfig?: IModelSettingProps;
  modelsConfig?: { models: IModelSettingProps[], retryConfig: any };
}

export function AgentParamsRender(props: IProps) {
  const {
    showCommitBtn = true,
    commitBtnName = '运行',
    commitBtnIcon = <CaretRightOutlined rev={undefined} />,
    paramsInPrompt,
    modelConfig,
    modelsConfig,
    onCommit,
    onChange,
    extra,
    style = {},
    ...otherProps
  } = props;
  const [form] = Form.useForm();
  const { globalState } = useGlobalState();
  const { modelList } = globalState;
  const [imageList, setImageList] = useState([]);
  const [audioList, setAudioList] = useState([]);
  const [videoList, setVideoList] = useState([]);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [historyList, setHistoryList] = useState<any[]>(() => {
    const localFileData = getLocalAppData('debug') || [];
    return localFileData.map((item: any, index: number) => ({
      id: item.title, // 使用时间戳作为id
      title: `运行记录 ${index + 1}`,
      params: JSON.parse(item.value),
      createTime: item.title,
    }));
  });
  const models = modelsConfig?.models || [modelConfig];
  const pickModel = models[0];
  const model = modelList.find(m => m.name === pickModel.modelName);
  const isSupportImage = isModelSupportImage(model);
  const isSupportAudio = isModelSupportAudio(model);
  const isSupportVideo = isModelSupportVideo(model);

  useEffect(() => {
    setImageList([]);
    setAudioList([]);
    setVideoList([]);
  }, [model]);

  // 生成型应用，通过按钮触发提交
  function onFinish(params: any) {
    const newParams = { ...params };
    const localFileData = getLocalAppData('debug') || [];

    // 检查是否有相同内容
    const paramStr = JSON.stringify(params);
    const isDuplicate = localFileData.some(item => item.value === paramStr);

    if (!isDuplicate) {
      const timestamp = dayjs().format('YYYY-MM-DD HH:mm:ss');
      const newItem = {
        value: paramStr,
        title: timestamp,
      };

      // 更新本地存储
      localFileData.unshift(newItem);
      saveLocalAppData('debug', localFileData);

      // 更新状态
      const historyItem = {
        id: timestamp,
        params: newParams,
        createTime: timestamp,
      };
      setHistoryList([historyItem, ...historyList].slice(0, 10).map((item: any, index: number) => ({
        ...item,
        title: `运行记录 ${index + 1}`,
      })));
    }

    if (onCommit) {
      if (imageList.length > 0) {
        newParams.imageUrl = imageList.join(',');
      }
      if (audioList.length > 0) {
        newParams.audioUrl = audioList.join(',');
      }
      if (videoList.length > 0) {
        newParams.videoUrl = videoList.join(',');
      }
      onCommit(newParams);
    }
  }

  // 在表单变更后，立即触发变更
  function onValuesChange(_v: any, params: any) {
    if (onChange) {
      onChange(params);
    }
  }

  const handleApplyHistory = (params: any) => {
    form.setFieldsValue(params);
    setHistoryVisible(false);
  };

  return (
    <UserInputContainer style={style}>
      {extra}
      {(isSupportImage || isSupportAudio || isSupportVideo) && (
        <div style={{ marginBottom: '10px' }}>
          <Title level={5} style={{ marginTop: 0 }}>
            上传媒体文件
            <span style={{ color: '#999', fontSize: '12px', fontWeight: 'normal' }}>
              (支持{[
                isSupportImage && '图片',
                isSupportAudio && '音频',
                isSupportVideo && '视频'
              ].filter(Boolean).join('/')}，上传后会自动启用对应能力，只需要在提示词中描述即可，例如：图片/音频/视频里有什么)
            </span>
          </Title>
          <Uploader
            key={`${model}`}
            width={80}
            height={80}
            permanent={true}
            accept={[
              isSupportImage && "image/jpeg,image/jpg,image/bmp,image/jpeg2000,image/png,image/gif,image/webp",
              isSupportAudio && "audio/mp3,audio/wav,audio/ogg,audio/m4a,audio/aac,audio/flac,audio/mp4,audio/mpeg,audio/webm",
              isSupportVideo && "video/mp4,video/webm,video/ogg,video/avi,video/mov,video/wmv,video/flv,video/mpeg"
            ].filter(Boolean).join(',')}
            value={''}
            max={15}
            onChange={(v) => {
              console.log('onChange...', v);
              // 根据文件类型分类存储
              const imageUrls = v.filter(item => item.url.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/i)).map(v => v.url);
              const audioUrls = v.filter(item => item.url.match(/\.(mp3|wav|ogg|m4a|aac|flac)$/i)).map(v => v.url);
              const videoUrls = v.filter(item => item.url.match(/\.(mp4|webm|avi|mov|wmv|flv|mpeg)$/i)).map(v => v.url);

              setImageList(imageUrls);
              setAudioList(audioUrls);
              setVideoList(videoUrls);
            }}
            type="Media"
          />
        </div>
      )}
      <Flex>
        {paramsInPrompt && paramsInPrompt.length > 0 && <Title level={5}>变量参数</Title>}
      </Flex>
      <LForm
        form={form}
        onFinish={onFinish}
        {...layout}
        {...otherProps}
        onValuesChange={onValuesChange}
      >

        {paramsInPrompt &&
          paramsInPrompt.map((param) => {
            if (!param.key) {
              return;
            }
            if (param.type === ParamTypeEnum.number) {
              return (
                <LFormInputNumber
                  key={param.key}
                  label={param.title || param.key}
                  name={param.key}
                  rules={[
                    {
                      required: param.required,
                      message: `请输入${param.title || param.key}`,
                    },
                  ]}
                />
              )
            }
            if (param.type === ParamTypeEnum.boolean) {
              return (
                <LFormSwitch
                  key={param.key}
                  label={param.title || param.key}
                  name={param.key}
                  rules={[
                    {
                      required: param.required,
                      message: `请输入${param.title || param.key}`,
                    },
                  ]}
                />
              )

            }
            if (param.type === ParamTypeEnum.date) {
              return (
                <LFormDatePicker
                  key={param.key}
                  label={param.title || param.key}
                  name={param.key}
                  rules={[
                    {
                      required: param.required,
                      message: `请输入${param.title || param.key}`,
                    },
                  ]}
                />
              )
            }
            if (param.type === ParamTypeEnum.file) {
              return (
                <LFormUploadWithPopo
                  key={param.key}
                  label={param.title || param.key}
                  name={param.key}
                  rules={[
                    {
                      required: param.required,
                      message: `请上传${param.title || param.key}`,
                    },
                  ]}
                />
              );
            }
            if (param.type === ParamTypeEnum.textInput) {
              return (
                <LFormInput
                  key={param.key}
                  label={param.title || param.key}
                  name={param.key}
                  rules={[
                    {
                      required: param.required,
                      message: `请输入${param.title || param.key}`,
                    },
                  ]}
                />
              );
            }
            if (param.type === ParamTypeEnum.select) {
              const options = param.config.options?.map((it: { value: string }) => ({
                id: it.value,
                name: it.value,
              }));
              return (
                <LFormSelect
                  key={param.key}
                  label={param.title || param.key}
                  name={param.key}
                  options={options || []}
                  rules={[
                    {
                      required: param.required,
                      message: `请选择${param.title || param.key}`,
                    },
                  ]}
                />
              );
            }
            if (param.type === ParamTypeEnum.textArea) {
              return (
                <LFormTextArea
                  key={param.key}
                  label={param.title || param.key}
                  name={param.key}
                  rows={2}
                  rules={[
                    {
                      required: param.required,
                      message: `请输入${param.title || param.key}`,
                    },
                  ]}
                />
              );
            }
          })}
      </LForm>
      {showCommitBtn && (
        <div style={{ display: 'flex', flexDirection: 'row-reverse' }}>
          <Button
            className="rightFloat"
            type="primary"
            icon={commitBtnIcon}
            onClick={() => form.submit()}
          >
            {commitBtnName}
          </Button>
          <Space>
            <Button
              type="link"
              icon={<HistoryOutlined />}
              onClick={() => setHistoryVisible(true)}
            >
              历史记录
            </Button>
          </Space>
        </div>
      )}
      <HistoryModal
        visible={historyVisible}
        onClose={() => setHistoryVisible(false)}
        historyList={historyList}
        onApply={handleApplyHistory}
      />
    </UserInputContainer>
  );
}

const UserInputContainer = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;

  .rightFloat {
    align-self: flex-end;
  }
`;
