import { IAppType } from "@/interface";
import { Form, Input, Modal } from "antd";
import { forwardRef, useImperativeHandle, useState } from "react";

const AppCopyModal = (props, ref) => {
  const { onChange } = props;
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [value, setValue] = useState(null);

  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    show: (val) => {
      form.setFieldsValue(val);
      setValue(val);
      setOpen(true);
    },
    close: () => {
      onCancel();
    },
    setLoading: (val) => {
      setLoading(val);
    },
    ...form
  }));

  const onCancel = () => {
    form.resetFields();
    setLoading(false);
    setOpen(false);
  };

  const onOk = async () => {
    const val = await form.validateFields();
    console.log('onoK', 'val', val, 'value', value);
    setLoading(true);

    onChange?.({
      ...(value || {}),
      ...(val || {})
    });

  };

  return <Modal
    title="复制"
    open={open}
    onCancel={onCancel}
    okButtonProps={{
      loading
    }}
    cancelButtonProps={{
      disabled: loading
    }}
    onOk={onOk}
  >
    <Form form={form} layout="vertical">
      <Form.Item name="name" label="应用名称" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      {value?.type === IAppType.Workflow && <Form.Item name="workflowId" label="英文名" rules={[{ required: true }]}>
        <Input
          placeholder="唯一标识（支持英文、数字、横线和下划线）"
        />
      </Form.Item>}
      <Form.Item name="description" label="应用功能介绍">
        <Input.TextArea />
      </Form.Item>

    </Form>
  </Modal>
};

export default forwardRef(AppCopyModal);