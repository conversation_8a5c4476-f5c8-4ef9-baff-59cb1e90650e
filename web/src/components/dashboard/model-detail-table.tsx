import { Table } from 'antd';
import styled from 'styled-components';
import { appDetailColumns, modelDetailColumns } from './const';
import { useMemo } from 'react';

const ModelDetailTableWrapper = styled.div`
  .ant-table-cell {
    padding: 16px !important;
  }
`;

export const ModelDetailTable = ({ record, groups, type, sort, query }) => {
  const sortData = useMemo(() => {
    return record.details.sort((a, b) => {
      if (sort.order === 'ascend') {
        return a[sort.field] - b[sort.field];
      } else {
        return b[sort.field] - a[sort.field];
      }
    });
  }, [record.details, sort]);
  return (
    <ModelDetailTableWrapper>
      <Table
        showHeader={false}
        columns={type === 'model' ? modelDetailColumns(groups, query) : appDetailColumns(groups, query)}
        dataSource={sortData}
        pagination={false}
        size="small"
      />
    </ModelDetailTableWrapper>
  );
};
