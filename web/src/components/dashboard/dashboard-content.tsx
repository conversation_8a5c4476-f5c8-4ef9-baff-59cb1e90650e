import { useCallback, useMemo, useState } from 'react';
import { DatePicker, Table } from 'antd';
import dayjs from 'dayjs';
import { ModelDetailTable } from './model-detail-table';
import { appColumns, modelColumns } from './const';


// 组件
const DataDashboardContent = ({ data, groups, type, query }: { data: any[], groups: any[], type: 'app' | 'model', query: any }) => {
  const [page, setPage] = useState({
    current: 1,
    pageSize: 10,
  });
  const [sort, setSort] = useState({
    field: 'fee',
    order: 'descend',
  });

  const columns = useMemo(() => {
    return type === 'app' ? appColumns(groups, query) : modelColumns(groups, query)
  }, [type])

  const onChange = useCallback((pagination, filters, sorter, extra) => {
    console.log("pagination", pagination, filters, sorter, extra)
    setPage(pagination)
    setSort(sorter)
  }, [])


  return (
    <Table
      pagination={{
        current: page.current,
        pageSize: page.pageSize,
        total: data?.length || 0,
      }}
      columns={columns}
      dataSource={data || []}
      onChange={onChange}
      rowKey={record => record.name ?? record.model_name}
      expandable={{
          expandedRowRender: (record) => {
            return <ModelDetailTable record={record} groups={groups} type={type} sort={sort} query={query}/>
          },
          rowExpandable: (record) => record.details.length > 1
        }}
      />
  );
};

export default DataDashboardContent;