import React, { useCallback, useMemo, useState } from 'react';
import { Select, DatePicker, Table, Pagination, TimeRangePickerProps, TableProps, Statistic, Switch, Radio, Spin } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';
import { MetricApi } from '@/api/metric';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppstoreOutlined, DollarOutlined, MessageOutlined } from '@ant-design/icons';
import { formatNumber } from '@/utils/common';
import DataDashboardContent from './dashboard-content';
import { useQuery } from '@/hooks/useQuery';
const { RangePicker } = DatePicker;

interface DateRange {
  start: dayjs.Dayjs;
  end: dayjs.Dayjs;
}

// 组件
const DataDashboard = ({ isGroup = false }: { isGroup?: boolean }) => {
  const query = useQuery();
  const groupId = query.parsedQuery.groupId || 'all';
  // const [group, setGroup] = useState(groupId);
  const { globalState } = useGlobalState();
  const [type, setType] = useState<'app' | 'model'>('app');
  const { groups } = globalState;
  const [sort, setSort] = useState({
    sortField: "tokens",
    sortOrder: "descend"
  });

  const [dateRange, setDateRange] = useState<DateRange>({
    start: dayjs().add(-3, 'd'),
    end: dayjs()
  });

  const { data } = useRequest(() => {
    if (type === 'app') {
      return MetricApi.getAppMetrics(groupId as string, dateRange, sort)
    } else {
      return MetricApi.getModelMetrics(groupId as string, dateRange, sort)
    }
  }, {
    refreshDeps: [dateRange, groupId, sort, type]
  })

  // 计算总消息数和总Token数
  const totalStats = useMemo(() => {
    if (!data || !data.length) return { messages: 0, tokens: 0, fee: 0, activeAppCount: 0 };

    return (data as any[]).reduce((pre, cur) => {
      pre.messages += Number(cur.messages || 0);
      pre.tokens += Number(cur.tokens || 0);
      pre.fee += Number(cur.fee || 0);
      pre.activeAppCount += cur.messages > 9 ? 1 : 0;
      return pre;
    }, {
      messages: 0,
      tokens: 0,
      fee: 0,
      activeAppCount: 0
    });
  }, [data]);


  const rangePresets: TimeRangePickerProps['presets'] = [
    { label: '本周', value: [dayjs().startOf('week'), dayjs()] as any },
    { label: '上周', value: [dayjs().startOf('week').subtract(1, 'week'), dayjs().endOf('week').subtract(1, 'week')] as any },
    { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] as any },
    { label: '最近七天', value: [dayjs().add(-7, 'd'), dayjs()] as any },
    { label: '本月', value: [dayjs().startOf('month').startOf('day'), dayjs()] as any },
    { label: '上月', value: [dayjs().startOf('month').subtract(1, 'month'), dayjs().endOf('month').subtract(1, 'month')] as any },
    { label: '最近一季度', value: [dayjs().add(-90, 'd'), dayjs()] as any },
  ];

  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    // Can not select days before today and today
    return current && current >= dayjs().endOf('day');
  };

  const handleGroupChange = value => {
    query.addQuery({
      groupId: value,
    });
    // 重新筛选数据
  };

  const onRangeSelect = useCallback((range) => {
    console.log('range', range);
    setDateRange({
      start: range[0],
      end: range[1],
    });
  }, []);

  const groupOptions = useMemo(() => {
    return [{
      name: '全部业务组',
      id: 'all',
    }].concat(groups || []).map(v => ({
      label: v.name,
      value: v.id
    }));

  }, [groups])

  if (!groups?.length) {
    return <Spin />
  }

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            {!isGroup && <Select showSearch
              allowClear
              onClear={() => {
                query.addQuery({
                  groupId: 'all',
                });
              }}
              filterOption={(input, option) =>
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              value={groupId} style={{ width: 200 }} onChange={handleGroupChange} options={groupOptions}>
            </Select>
            }
            <RangePicker
              defaultValue={[dayjs().subtract(3, 'day'), dayjs()] as any}
              placeholder={['', '今天']}
              allowEmpty={[false, true]}
              disabledDate={disabledDate}
              presets={rangePresets}
              onChange={onRangeSelect}
            />
          </div>
          <Radio.Group
            options={[{ label: '应用维度', value: 'app' }, { label: '模型维度', value: 'model' }]}
            defaultValue={type}
            optionType="button"
            buttonStyle="solid"
            onChange={e => setType(e.target.value)}
          />
          <div style={{ display: 'flex', gap: '20px', borderRadius: '6px' }}>
            <Statistic
              title={`活跃${type === 'app' ? '应用' : '模型'}数`}
              valueStyle={{ color: '#3f8600' }}
              prefix={<AppstoreOutlined />}
              value={totalStats.activeAppCount}
              style={{ marginRight: 20 }}
            />
            <Statistic
              title="费用总数"
              valueStyle={{ color: '#f85c5c' }}
              prefix={<DollarOutlined />}
              value={totalStats.fee.toFixed(0)}
              style={{ marginRight: 20 }}
            />
            <Statistic
              title="消息总数"
              valueStyle={{ color: '#f99225' }}
              prefix={<MessageOutlined />}
              formatter={value => formatNumber(value)}
              value={totalStats.messages}
              style={{ marginRight: 20 }}
            />
            <Statistic
              title="Token总数"
              formatter={value => formatNumber(value)}
              valueStyle={{ color: 'gray' }}
              value={totalStats.tokens}
            />
          </div>

        </div>
      </div>
      <DataDashboardContent data={data} groups={groups} type={type} query={query} />
    </div>
  );
};

export default DataDashboard;