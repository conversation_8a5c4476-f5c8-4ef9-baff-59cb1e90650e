import { getModelMap } from '@/utils/common';
import React from 'react';

const Fee = ({ fee }) => {
  let color = '#1890ff';
  if (fee > 80) {
    color = '#ff4d4f';
  } else if (fee > 50) {
    color = '#faad14';
  } else if (fee > 10) {
    color = '#13c2c2';
  }
  return <span><span style={{ color, fontWeight: 'bold' }}>{fee}</span>元</span>
}

const sortFn = sortField => (a, b) => {
  const modelMap = getModelMap();
  if (sortField === 'model_fee') {
    const modelA = modelMap[a.model_name] || { fee: { input: 0, output: 0 } };
    const modelB = modelMap[b.model_name] || { fee: { input: 0, output: 0 } };
    return (modelA.fee.input + modelA.fee.output) - (modelB.fee.input + modelB.fee.output)
  }
  return a[sortField] - b[sortField]
}

const modelRender = isModel => (_, record) => {
  const modelName = record.details?.[0]?.model_name || record.model_name || '';
  if (!modelName) {
    return '暂未记录'
  }
  if (record?.details?.length > 1 && !isModel) {
    return '多种模型'
  } else {
    const model = getModelMap()[modelName];
    if (model) {
      return model?.name;
    }
    return modelName + '（外部）';
  }
}

export const appColumns = (groups, query) => [
  {
    title: '业务组',
    dataIndex: 'group_id',
    key: 'groupId',
    width: 200,
    render: (text) => {
      const find = (groups || [])?.find(item => item.id === text);
      const handleGroupChange = (value) => {
        query.addQuery({
          groupId: value,
        });
      }
      return <a onClick={() => handleGroupChange(text)}>{find?.name || ''}</a>
    }

  },
  {
    title: '应用名',
    dataIndex: 'name',
    key: 'name',
    width: 400,
    render: (name, record) => <a href={`/app/dev?appId=${record.app_id}`} target='_blank'>{name}</a>
  },
  {
    title: '模型',
    dataIndex: 'model',
    key: 'model',
    width: 300,
    render: modelRender(false)
  },
  {
    title: 'Token总数',
    dataIndex: 'tokens',
    key: 'tokens',
    sorter: sortFn('tokens'),
    width: 200,
  },
  {
    title: '消息总数',
    dataIndex: 'messages',
    key: 'messages',
    sorter: sortFn('messages'),
    width: 200,
  },
  {
    title: '预估成本',
    dataIndex: 'fee',
    key: 'fee',
    width: 200,
    sorter: sortFn('fee'),
    render: (text) => text.toFixed(2),
  },
];

export const modelColumns = (groups, query) => [
  {
    title: '模型名',
    dataIndex: 'model_name',
    key: 'model_name',
    width: 400,
    render: modelRender(true)
  },
  {
    title: '模型费用(/百万tokens)',
    dataIndex: 'model_name',
    key: 'model_fee',
    width: 200,
    sorter: sortFn('model_fee'),
    render: (text, record) => {
      const model = getModelMap()[text];
      if (model) {
        return <span>输入<Fee fee={model.fee.input} />，输出<Fee fee={model.fee.output} /></span>
      }
      return record?.tokens ? <span>平均成本预估<Fee fee={(record.fee / (record.tokens || 1) * 1000000).toFixed(2)} /></span> : '暂未记录'
    }
  },
  {
    title: 'Token总数',
    dataIndex: 'tokens',
    key: 'tokens',
    sorter: sortFn('tokens'),
    width: 200,
  },
  {
    title: '消息总数',
    dataIndex: 'messages',
    key: 'messages',
    sorter: sortFn('messages'),
    width: 200,
  },
  {
    title: '预估成本',
    dataIndex: 'fee',
    width: 200,
    key: 'fee',
    sorter: sortFn('fee'),
    render: (text) => text.toFixed(2),
  },
]

export const appDetailColumns = (groups, query) => [
  {
    title: '模型ID',
    dataIndex: 'model_id',
    key: 'model_id',
    width: 230,
    render: (text) => <div></div>
  },
  {
    title: '模型ID',
    dataIndex: 'model_id2',
    key: 'model_id2',
    width: 400,
    render: (text) => <div></div>
  },
  {
    title: '模型名称',
    dataIndex: 'model_name',
    key: 'model_name',
    width: 300,
    render: modelRender(true)
  },
  {
    title: 'Token数',
    dataIndex: 'tokens',
    key: 'tokens',
    width: 200,
  },
  {
    title: '消息数',
    dataIndex: 'messages',
    key: 'messages',
    width: 180,
  },
  {
    title: '费用',
    dataIndex: 'fee',
    key: 'fee',
    width: 180,
    render: (text) => text.toFixed(2),
  },
];

export const modelDetailColumns = (groups, query) => [
  {
    title: '业务组',
    dataIndex: 'group_id',
    key: 'groupId',
    width: 200,
    render: (text) => {
      const find = (groups || [])?.find(item => item.id === text);
      const handleGroupChange = (value) => {
        query.addQuery({
          groupId: value,
        });
      }
      return <a onClick={() => handleGroupChange(text)}>{find?.name || ''}</a>
    }
  },
  {
    title: '应用名',
    dataIndex: 'name',
    key: 'name',
    width: 240,
    render: (name, record) => <a href={`/app/dev?appId=${record.app_id}`} target='_blank'>{name}</a>
  },
  {
    title: 'Token总数',
    dataIndex: 'tokens',
    key: 'tokens',
    sorter: sortFn('tokens'),
    width: 200,
  },
  {
    title: '消息总数',
    dataIndex: 'messages',
    key: 'messages',
    sorter: sortFn('messages'),
    width: 200,
  },
  {
    title: '预估成本',
    dataIndex: 'fee',
    key: 'fee',
    width: 190,
    sorter: sortFn('fee'),
    render: (text) => text.toFixed(2),
  },
];