import React, { useState } from 'react';
import { DownOutlined, SettingOutlined } from '@ant-design/icons';
import { Modal, Row, Col, Button } from 'antd';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useGlobalState } from '@/hooks/useGlobalState';
import CustomSelect from '../custom-select';
import { usePathQuery } from '@/hooks/useQuery';
import { useAdmin } from '@/hooks/useAdmin';
import { useRequest } from 'ahooks';
import { WorkspaceApi } from '@/api';

interface WorkspaceNameProps {
  hasName: boolean;
}

const WorkspaceBox = styled.div`
  position: relative;
  margin: 0 10px;
`;

const WorkspaceCard = styled.div`
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    flex-direction: column;
    p {
      margin: 0;
      width: 80%;
      text-align: center;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .desc {
      color: #999;
      height: auto;
    }
    &.selected {
      border-color: #1677ff;
    }
    border: 1px #ddd solid;
    border-radius: 4px;
    margin-bottom: 8px;
`;

const WorkspaceText = styled.span`
  color: #666;
  font-size: 14px;
`;

const WorkspaceButton = styled.span`
  cursor: pointer;
  color: #666;
  font-size: 14px;
`;

const WorkspaceLabel = styled.span``;

const WorkspaceName = styled.span<WorkspaceNameProps>`
  color: ${props => props.hasName ? '#000' : '#666'};
  vertical-align: bottom;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100px;
`;

const StyledDownOutlined = styled(DownOutlined)`
  margin-left: 4px;
  font-size: 12px;
`;

export const WorkspaceSelect = () => {
  const { globalState } = useGlobalState();
  const { workspaces, workspace } = globalState;
  const { pushRetainQuery } = usePathQuery();
  const navTo = useNavigate();
  const isAdmin = useAdmin('workspace');

  const handleRouterChange = (val) => {
    if (val === 'setting') {
      pushRetainQuery('/workspace', ['workspaceId']);
    } else {
      navTo(`/?workspaceId=${val}`);
    }
  }

  const handleClick = (event) => {
    event.preventDefault();
    event.stopPropagation(); // 阻止事件冒泡
  }
  const find = (workspaces || []).find(v => v.id === workspace?.id);

  return <WorkspaceBox>
    {/* <WorkspaceModal onChange={handleRouterChange}>
      <CustomSelect
        // onMouseDown={handleClick}
        // onClick={handleClick}
        style={{ width: 130 }}
        className='workspace-select'
        label="租户: "
        disabled
        value={workspace?.id}
        placeholder="选择租户"
        options={(workspaces || []).map(v => ({ label: v.name, value: v.id }))} />
    </WorkspaceModal> */}
    <WorkspaceModal onChange={handleRouterChange}>
      <WorkspaceText>
        <WorkspaceButton>
          租户：
          <WorkspaceName hasName={!!find?.name}>
            {find?.name ? find.name : '选择租户'}
          </WorkspaceName>
          <StyledDownOutlined />
        </WorkspaceButton>
      </WorkspaceText>
    </WorkspaceModal>
    {isAdmin &&
      <SettingOutlined
        style={{
          position: 'absolute',
          right: -4,
          top: 1,
          color: '#666'
        }}
        onClick={() => handleRouterChange('setting')} />
    }
  </WorkspaceBox>
}



const WorkspaceModal = ({ children, onChange }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selected, setSelected] = useState(false);

  const showModal = (ev) => {
    ev.preventDefault()
    ev.stopPropagation();
    setIsModalOpen(true);
  };

  const handleOk = () => {
    if (selected) {
      onChange(selected);
    }
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const { data = [] } = useRequest(() => WorkspaceApi.listNetease())
  const { data: myWorkspace = [] } = useRequest(() => WorkspaceApi.list())

  return (
    <>
      {React.cloneElement(children, { onClick: showModal })}
      <Modal title="选择租户" open={isModalOpen} onOk={handleOk} onCancel={handleCancel} width={900}>
        <h4 style={{ margin: '10px 0' }}>我加入的</h4>
        <Row gutter={8}>
          {(myWorkspace || []).map((v: any) => {
            return <Col className="gutter-row" span={4}>
              <WorkspaceCard onClick={() => setSelected(v.id)} className={selected === v.id ? 'selected' : ''}>
                <p className='title'>{v.name}</p>
                <p className='desc'>  {v.description ? `@${v.description}` : '暂无描述'}</p>
              </WorkspaceCard>
            </Col>
          })}
        </Row>
        <h4 style={{ margin: '10px 0' }}>全部租户</h4>
        <Row gutter={8}>
          {(data || []).map((v: any) => {
            return <Col className="gutter-row" span={4}>
              <WorkspaceCard onClick={() => setSelected(v.id)} className={selected === v.id ? 'selected' : ''}>
                <p className='title'>{v.name}</p>
                <p className='desc'>@{v.description}</p>
              </WorkspaceCard>
            </Col>
          })}
        </Row>
      </Modal>
    </>
  );
};