import { useEffect, useMemo } from 'react';
import React from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import styled from 'styled-components';

import { getRetainQuery } from '@/hooks/useQuery';
import logo from '@/logo-langbase.svg';

import { useQuery2GlobalState } from '../../hooks/useQuery2GloablState';
import { LayoutContainer } from '../layout-container';
import { LAST_VISITED_WORKSPACE } from '@/utils/constant';
import Side from './side';

const withoutSideList = ['app'];

export function Root() {
  // 全局只可引用一次，否则会导致数据重复请求
  useQuery2GlobalState();
  const query = getRetainQuery(['workspaceId', 'type']);
  const pathPrefix = useMemo(() => location.pathname.split('/')[1], [location.pathname]);

  useEffect(() => {
    const url = new URL(location.toString())
    const workspaceId = url.searchParams.get('workspaceId')
    if (workspaceId) {
      localStorage.setItem(LAST_VISITED_WORKSPACE, workspaceId);
    }
  }, [location.search])

  const side = useMemo(() =>
    <Side logo={<Link to={`/?${query}`}>
      <img src={'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56900304832/64a0/9bc8/4f1a/f685ea0741706a072ef72652ec62ea3f.png'} alt="alt" style={{ width: "151px", margin: '10px auto', display: 'block' }} />
    </Link>}></Side>
    , [])

  return (
    <RootContainer>
      {side}
      <MainContainer>
        <Body style={{ 'userSelect': 'none' }}>
          <Outlet />
        </Body>
      </MainContainer>
    </RootContainer>
  );
}

export function PreviewRoot() {
  return (
    <>
      <Outlet />
    </>
  );
}

const RootContainer = styled.div`
display: flex; 
height: 100%;
background: linear-gradient(-320deg, rgb(148 95 198 / 14%) 1%, rgba(76, 105, 255, 0.1) 13%, rgba(104, 192, 255, 0.05) 25%, rgb(51 129 211 / 10%) 44%);
`

const MainContainer = styled.div`
  display: flex; 
  flex-direction: column; 
  flex: 1;
  border-radius: 20px;
  margin: 20px;
  margin-left: 0;
  overflow: hidden;
  background: #f5f8fc;
`;

const Header = styled(LayoutContainer)`
  height: 64px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  line-height: 50px;
  box-sizing: border-box;

  img {
    vertical-align: middle;
  }
`;

const Body = styled.div`
  flex-grow: 1;
`;
