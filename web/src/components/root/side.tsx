import React, { useMemo, useRef, useState } from 'react';
import { PlusOutlined, MessageOutlined, SearchOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons';
import type { MenuProps, TourProps } from 'antd';
import { Menu, Input, Button, Tour, Popover, Avatar } from 'antd';
import qs from 'querystring';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import Login from '../login';
import { useGlobalState } from '@/hooks/useGlobalState';
import { ResourceAvatar } from '../resource-card';
import { CreateAppCard } from '@/pages/home/<USER>/create-app';
import { $ } from '@/utils/globalState';
import { HelpIcon } from '../icons';
import { getLocalData, saveLocalData } from '@/utils/common';
import { WorkspaceSelect } from './workspace-select';
import { useAdmin } from '@/hooks/useAdmin';
import { getColor } from '@/utils/constant';

const Sider = styled.div`
  background: transparent;
  padding: 20px;
  user-select: none;
`;

const Doc = styled.div`
width: 25px;
height: 25px;
cursor: pointer;
position: absolute;
bottom: 33px;
left: 190px;
color: #666;
display: flex;
align-items: center;
justify-content: center;
`;

const GroupMenuBox = styled(Menu)`
  .ant-menu-item-group-list {
    max-height: calc(100vh - 380px);
    overflow-y: auto;
  }
`;

const Search = styled(Input)`
    width: 120px;
    position: absolute;
    left: 75px;
    background: transparent;
    border: none;
    border-bottom: 1px solid #ddd;
    border-radius: 0;
    input {
      background: transparent;
    }
`;

type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group',
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type,
  } as MenuItem;
}

const getLink = (title, path) => {
  const handleClick = () => {
    $.setState({
      backPath: path
    })
  }
  return <Link to={path} onClick={handleClick}>{title}</Link>
}

const defaultItems: MenuProps['items'] = [
  getItem(getLink('智聊', '/'), '/', <MessageOutlined />),
  getItem(getLink('我的应用', '/overview'), '/overview', <UserOutlined />)
];

// 根据名称生成固定颜色
const getColorByName = (name: string) => {
  const colors = [
    '#7b8cf1', '#7bc5f1', '#54d843', '#ecc432', 
    '#f264dc', '#f264ab', '#e8db55', '#f9986a', 
    '#abeb73', '#73cceb'
  ];
  
  // 使用名称的字符编码总和作为种子
  let seed = 0;
  for (let i = 0; i < name.length; i++) {
    seed += name.charCodeAt(i);
  }
  
  // 确保相同名称总是得到相同颜色
  return colors[seed % colors.length];
};

const GroupMenu = React.memo(() => {
  const isAdmin = useAdmin('workspace');
  const { globalState } = useGlobalState();
  const { workspace = { id: '' }, groups } = globalState;
  const [keyword, setKeyword] = useState('');
  const query: any = qs.parse(location.search.replace('?', '')) || {};
  let defaultKey = location.pathname;
  if (defaultKey === '/group/apps') {
    defaultKey += `?${query.groupId}`
  };

  const sortedGroups = useMemo(() => {
    return (groups || [])
      .filter(g => g.name.toLowerCase().includes(keyword.toLowerCase()))
      .sort((a, b) => {
        // internal_user 排在前面，external_user 排在后面
        if (a.role === 'internal_user' && b.role === 'external_user') return -1;
        if (a.role === 'external_user' && b.role === 'internal_user') return 1;
        return 0;
      });
  }, [groups, keyword]);

  const items = useMemo(() => getItem('业务组', 'grp', null,
    sortedGroups.map(g => getItem(
      <span style={{
        color: g.role === 'external_user' ? '#999' : 'inherit',
        cursor: g.role === 'external_user' ? 'not-allowed' : 'pointer'
      }}>
        {getLink(g.name, `/group/apps?workspaceId=${workspace.id}&groupId=${g.id}`)}
      </span>,
      `/group/apps?${g.id}`,
      <span style={{
        backgroundColor: g.role === 'external_user' ? '#cccccc' : 'rgb(236, 196, 50)' ,
        borderRadius: '50%',
        width: 14,
        height: 14,
        color: '#fff',
        opacity: g.role === 'external_user' ? 0.5 : 1
      }}></span>
      // <Avatar
      //   // shape="square"
      //   // src={<img src="https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/58420991246/868b/ae08/4f01/d2945bf1bf62e6b243f46fa59aaccb63.png" alt="avatar" />}
      //   style={{
      //     backgroundColor: '#7b8cf1',
      //     width: 10,
      //     height: 10,
      //     color: '#fff',
      //     opacity: g.role === 'external_user' ? 0.5 : 1
      //   }}>
      //   {/* {g.name.slice(0, 1)} */}
      // </Avatar>,
      // <ResourceAvatar 
      //   width={20} 
      //   height={20} 
      //   className='avatar' 
      //   unikey={g.id} 
      //   alt={g.name} 
      //   foreground={[232, 176, 127, 255]}
      //   style={{ 
      //     opacity: g.role === 'external_user' ? 0.5 : 1 
      //   }}
      // />
    )),
    'group'
  ), [sortedGroups, workspace.id]);

  const handleSearch = (ev) => {
    const keyword = ev.target.value;
    setKeyword(keyword);
  }

  return <div style={{ position: 'relative' }}>
    <GroupMenuBox
      style={{ width: 200, background: 'transparent', border: 'none', }}
      key={workspace.id}
      defaultSelectedKeys={[defaultKey]}
      mode="inline"
      items={isAdmin ? defaultItems.concat([getItem(getLink('全部应用', '/all'), '/all', <TeamOutlined />), items]) : defaultItems.concat(items)}
    />
    <Search placeholder=""
      size="small"
      style={{ top: isAdmin ? 144 : 98 }}
      prefix={<SearchOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
      onChange={handleSearch} />

  </div>
})





const Side: React.FC<any> = React.memo(({ logo }) => {
  const docRef = useRef(null);
  const { globalState } = useGlobalState();
  const { workspace, groups } = globalState;
  const groupMenus = useMemo(() => <GroupMenu />, [])

  const [open, setOpen] = useState(!getLocalData('closeTour') && window.location.pathname === '/');
  const handleClose = () => {
    saveLocalData('closeTour', 1);
    setOpen(false);
  }
  const steps: TourProps['steps'] = [
    {
      title: '帮助文档站点正式上线',
      description: '可以点击查看帮助文档，随着新功能的开放，文档也会持续更新',
      cover: <img
        alt="tour.png"
        src="https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44414675343/c56c/ce79/c484/1fd4ca414d958986cc2cd46d71706fbe.png"
      />,
      target: () => docRef.current,
    },
  ];
  return (
    <Sider>
      {logo}
      <WorkspaceSelect></WorkspaceSelect>
      <CreateAppCard workspace={workspace} groups={groups} render={onClick => <Button onClick={onClick} type="primary" style={{ width: "100%", margin: '10px 0' }} icon={<PlusOutlined />} >创建应用</Button>}></CreateAppCard>
      {groupMenus}
      <Login />
      <Popover content="帮助文档">
        <Doc ref={docRef} onClick={() => window.open('https://music-doc.st.netease.com/st/langbase-doc/', '_blank')}>
          <HelpIcon></HelpIcon>
        </Doc>
      </Popover>
      <Tour open={open} onClose={handleClose} steps={steps} />
    </Sider >
  );
});

export default Side;