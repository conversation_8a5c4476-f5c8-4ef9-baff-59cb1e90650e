import { Flex, InputN<PERSON>ber, Slider } from "antd";

const SliderInput = (props) => {
  const { value, onChange, defaultValue, ...rest } = props;

  return <Flex align="center">
    <Slider
      max={5}
      min={0}
      step={1}
      style={{ width: 100, marginRight: 16 }}
      defaultValue={defaultValue}
      value={value}
      onChange={(val) => onChange(val)}
      {...rest}
    />
    <InputNumber
      size="small"
      style={{ width: 60 }}
      max={5}
      min={0}
      step={1}
      defaultValue={defaultValue}
      value={value}
      onChange={(val) => onChange(val)}
      {...rest}
    />
  </Flex>
}

export default SliderInput;
