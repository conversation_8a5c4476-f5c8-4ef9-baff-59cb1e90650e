import { Flex, Form, InputNumber } from "antd";
import { LFormSliderInput } from "../form/form-items/form-range";
import SliderInput from "../slider-input";

const RetryConfig = (props) => {
  const { value, onChange, formProps } = props;
  return <div style={{ width: '100%' }}>
    <h4 className="title" style={{ textAlign: 'left' }}>重试配置1111111
    </h4>
    <div style={{ width: '100%' }}>
      <Form
        style={{ width: '100%' }}
        labelCol={{ span: 12 }}
        wrapperCol={{ span: 12 }}
        labelAlign="left"
        {...formProps}
      >
        <Form.Item label="重试次数">
          {/* <InputNumber
            value={value}
            onChange={v => onChange({
              retryCount: v
            })}
            defaultValue={0} /> */}
          <SliderInput
            defaultValue={0}
            value={value}
            onChange={v => {
              console.log('onChange.v', v);
              onChange({
                retryCount: v
              })
            }} />
        </Form.Item>
        {/* <LFormSliderInput
          label="重试次数"
          name="retryCount"
          min={0}
          max={5}
          step={1}
          value={value}
          //@ts-ignore
          onChange={v => onChange({
            retryCount: v
          })}
          defaultValue={0}
        /> */}
      </Form>
      {/* <Flex justify="space-between">
        <Flex>重试次数</Flex>
        <Flex>
          <InputNumber
            value={value}
            onChange={v => onChange({
              retryCount: v
            })}
            defaultValue={0} />
        </Flex>
      </Flex> */}
    </div>
  </div>
};

export default RetryConfig;
