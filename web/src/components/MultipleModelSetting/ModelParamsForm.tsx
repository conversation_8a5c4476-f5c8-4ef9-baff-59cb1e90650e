import { LForm, LFormSwitch } from '@/components/form';
import { Form } from 'antd';
import { useEffect, useMemo } from 'react';
import { LFormSliderInput } from '@/components/form/form-items/form-range';
import { getModelConfigSchema } from '@/utils/model-helper';
import { TextTip } from '@/components/text-tip';
import styled from 'styled-components';

const StyledForm = styled.div`
  width: 100%;
  .ant-form-item {
    margin-bottom: 12px;
  }
`;


const ModelParamsForm = (props) => {
  const { modelList, modelName, app, onChange, value } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(value);
  }, [value]);

  const renderFormItem = useMemo(() => {
    const targetModel = modelList.find((it) => it.name === modelName);
    const schema = getModelConfigSchema(targetModel, app?.type);
    return schema.map((schemaConfig) => {
      const {
        paramKey,
        paramName,
        description,
        validRange,
        type,
        default: initialValues,
        step = 0.01,
      } = schemaConfig;

      const { range: backendRange } =
        targetModel?.config[paramKey] || {};
      const range = backendRange || validRange;
      const label = (
        <TextTip
          text={`${paramName} ${paramKey}`}
          tip={
            <div>
              {(description || []).map((it, idx) => (
                <p key={`description-${idx}`}>{it}</p>
              ))}{' '}
            </div>
          }
        />
      ) as any

      if (type === 'switch') {
        return <LFormSwitch
          name={paramKey}
          label={label}
        ></LFormSwitch>
      }

      return (
        <LFormSliderInput
          key={`sliderInput-${paramKey}`}
          name={paramKey}
          label={label}
          min={range[0]}
          max={range[1]}
          step={step}
        // defaultValue={defaultValue}
        />
      );
    });
  }, [modelName, modelList]);

  return <StyledForm>
    <LForm
      style={{ width: '100%' }}
      form={form}
      labelCol={{ span: 12 }}
      wrapperCol={{ span: 12 }}
      labelAlign="left"
      // onFieldsChange={handleModelParamsChange}
      onValuesChange={(changedValue, allValues) => {

        onChange?.(allValues)
      }}

    >
      {renderFormItem}
    </LForm>
  </StyledForm>
}


export default ModelParamsForm;
