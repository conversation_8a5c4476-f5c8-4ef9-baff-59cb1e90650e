import { RobotOutlined, CloseOutlined } from '@ant-design/icons';
import { Button, Popover } from 'antd';
import { isEqual } from 'lodash';
import { useEffect, useState } from 'react';
import SettingContent from './SettingContent';
import { IModelSettingProps } from './type';
import { IAppType } from '@/interface';
import { modelsConfigCnMap } from '@/interface/models';

export const modelsConfigStr = value => {
  let str = `
【配置类型】:${modelsConfigCnMap[value?.type || 'basic']}    
  `;

  const models = ['advanced', 'master_backup'].includes(value?.type) ? value?.models : (value?.models?.[0] ? [value?.models?.[0]] : []);

  models?.forEach((item, index) => {
    str += `
【语言模型】:${item.modelName},
【模型设置】:${JSON.stringify(item.modelParams || {}, null, 2)},
      `;


    if (value?.type === 'advanced') {
      str += `
【使用概率】:${item.ratio}
      `;
    }

    if (value?.type === 'master_backup') {
      str += `
【主要模型】:${index === 0 ? '是' : '否'}
      `;
    }
  });

  str += `
【重试次数】:${value?.retryConfig?.retryCount || 0}
  `;

  return str;
}

export interface IProps {
  value: {
    models: IModelSettingProps[];
    retryConfig?: {
      retryCount?: number;
    },
    type: string;
  };
  onChange: any;
  destroyModal?: boolean;
  disabledList?: RegExp[];
  style?: React.CSSProperties;
  onConfirm?: (value: IModelSettingProps) => void;
  isChatExplore?: boolean;
  appType: IAppType;
}

export function MultipleModelSetting(props: IProps) {
  const { value, onChange, destroyModal = false, disabledList = [], onConfirm, style, isChatExplore = false, appType } = props;
  const [open, setOpen] = useState(false);
  // 找出调用概率最大的model，用作默认展示
  const [maxRatioModel, setMaxRatioModel] = useState('');

  useEffect(() => {
    let maxRatio = value?.models?.[0]?.ratio;
    let maxModelName = value?.models?.[0]?.modelName;

    value?.models?.forEach(model => {
      if (model?.ratio > maxRatio) {
        maxRatio = model.ratio;
        maxModelName = model.modelName;
      }
    });

    setMaxRatioModel(maxModelName);

  }, [value?.models]);

  const handleChange = (v) => {
    if (!isEqual(v, value)) {
      console.log("save", v, value);
      onChange && onChange(v);
    }
    setOpen(false);
  };


  return (
    <>
      <Popover
        title={
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>模型及参数</span>
            <Button
              type="text"
              size="small"
              onClick={() => setOpen(false)}
            >
              <CloseOutlined />
            </Button>
          </div>
        }
        destroyTooltipOnHide={destroyModal}
        open={open}
        trigger="click"
        arrow={false}
        content={
          <div>
            <SettingContent
              value={value}
              onChange={handleChange}
              disabledList={disabledList}
              // onConfirm={handleConfirm}
              isChatExplore={isChatExplore}
              appType={appType}
            />
          </div>
        }
      >
        <Button onClick={() => setOpen(!open)} style={{
          ...style
        }}>
          {/* {value?.models?.map(item => <Tag key={item.modelName}>{item.modelName}</Tag>)} */}
          {maxRatioModel}{value?.models?.length > 1 ? '...' : ''}
          <RobotOutlined rev={undefined} />
        </Button>
      </Popover>
    </>
  );
}
