import { Button, Flex, InputNumber, message, Select, Slider, Tabs, Tag, Tooltip, Typography } from "antd";
import RetryConfig from "./RetryConfig";
import ModelParamsForm from "./ModelParamsForm";
import { EditOutlined, QuestionCircleOutlined, RobotOutlined } from "@ant-design/icons";
import ModelModal from "../model-modal";
import styled from "styled-components";
import { useEffect, useState } from "react";
import { IModelSettingProps } from "./type";
import { useGlobalState } from "@/hooks/useGlobalState";
import { IAppType } from "@/interface";
import { pick } from 'lodash';

interface IProps {
  // value: IModelSettingProps;
  value: {
    models: IModelSettingProps[];
    retryConfig?: {
      retryCount?: number;
    },
    // 基础模式 高级模式
    type?: string;
  };
  onChange: any;
  destroyModal?: boolean;
  disabledList?: RegExp[];
  style?: React.CSSProperties;
  onConfirm?: (value: IModelSettingProps) => void;
  isChatExplore?: boolean;
}

interface ISettingContentProps extends IProps {
  onConfirm?: (value: IModelSettingProps) => void;
  isChatExplore?: boolean;
  appType?: IAppType;
}

const SettingContent = (props: ISettingContentProps) => {
  const { value: parentValue, onChange: onParentChange, disabledList, onConfirm, appType } = props;
  const [editModelName, setEditModelName] = useState(undefined);
  const [editModelParams, setEditModelParams] = useState({});
  const { globalState } = useGlobalState();
  const { app, modelList } = globalState;
  const [modelSelectSettings, setModelSelectSettings] = useState<any>();

  const [value, setValue] = useState(parentValue);
  const { models, retryConfig } = value || {};

  useEffect(() => {
    setValue(parentValue);
  }, [parentValue]);

  useEffect(() => {
    if (editModelName?.startsWith('o3') || editModelName?.startsWith('o4') || editModelName?.startsWith('o5')) {
      setEditModelParams(models?.[0]?.modelParams);
    } else {
      setEditModelParams({ ...models?.[0]?.modelParams, ...pick(editModelParams, ['temperature', 'top_p', 'presence_penalty', 'frequency_penalty']) });
    }
  }, [editModelName]);

  const onChange = newValues => {
    setValue(newValues);
  };

  const handleConfirm = () => {
    const totalRatio = value?.models?.reduce((acc, cur) => {
      return acc + cur.ratio
    }, 0);
    if (value?.models?.length < 1) {
      message.error('最少选择一个模型');
      return false;
    }
    if (totalRatio > 1) {
      message.error('模型调用概率总和不能超过1');
      return false;
    }
    if (totalRatio < 1) {
      message.error('模型调用概率总和需等于1');
      return false;
    }
    onParentChange(value);
  };

  useEffect(() => {
    if (!modelList?.length) return;
    if (!editModelName) {
      setEditModelName(models?.[0]?.modelName);
    }

    setModelSelectSettings({
      selectedModels: models?.map(it => {
        return {
          key: it.modelName,
          name: it.modelName
        }
      }),
      multiple: {
        cost: 1,
        speed: 1,
        context: 1,
        performance: 1
      }
    });
  }, [models, modelList]);

  const onTypeChange = val => {
    setEditModelName(value?.models?.[0]?.modelName)
    onChange({
      ...(value || {}),
      models: [{
        ...(value?.models?.[0] || {}),
        ratio: 1
      }],
      type: val
    });
  }


  const onRetryConfigChange = (val) => {
    onChange({
      ...(value || {}),
      retryConfig: val
    });
  }

  const onRatioChange = (modelName, val) => {
    const newModels = models?.map(item => {
      if (item.modelName === modelName) {
        return {
          ...item,
          ratio: val
        }
      }
      return item;
    });
    onChange({
      ...(value || {}),
      models: newModels
    });
  }


  const handleModelParamsChange = (modelName, val) => {
    setEditModelParams(val);
    const newModels = models?.map(item => {
      if (item.modelName === modelName) {
        return {
          ...item,
          modelParams: val
        }
      }
      return item;
    });
    onChange({
      ...(value || {}),
      models: newModels
    });
  }

  const handleModelChange = (name?, val?) => {
    let newModels = [];


    // 现在的多选逻辑
    if (name === 'models') {
      const newValue = appType === IAppType.AgentCompletion ? val?.selectedModels : (val?.selectedModels?.[0] ? [val?.selectedModels?.[0]] : []);

      newModels = newValue?.map((modelItem, index) => {
        const targetModel = modelList.find((it) => it.name === modelItem.name);
        const defaultModelParams = Object.keys(targetModel?.config || {})?.reduce((acc, cur) => {
          acc[cur] = targetModel?.config?.[cur].default
          return acc;
        }, {});
        return {
          modelName: modelItem.name,
          providerKind: targetModel?.providerKind || 'openai',
          model: targetModel,
          modelParams: defaultModelParams,
          ratio: index === 0 ? 1 : 0
        }
      });
      setEditModelName(newModels?.[0]?.modelName);
    }
    onChange({
      ...(value || {}),
      models: newModels
    });
  }

  const onModelClick = currentModel => {
    setEditModelName(currentModel.modelName);
  };

  const onDeleteModel = (e, currentModel) => {
    e.preventDefault();
    let index = 0;

    const newModels = models?.filter((item, i) => {
      if (item.modelName === currentModel.modelName) {
        index = i;
        return false
      }
      return true;
    });

    if (currentModel.modelName === editModelName) {
      const newEditModelName = (newModels?.[index] || newModels?.[index + 1] || newModels?.[index - 1])?.modelName;
      setEditModelName(newEditModelName);
    }

    onChange({
      ...(value || {}),
      models: newModels
    });
  };

  // 是否是生成型
  const isCompletion = appType === IAppType.AgentCompletion;

  // 是否是高级模式，高级模式是多选
  const isAdvancedType = value?.type === 'advanced';

  return (
    <>
      <StyledContent>
        <div className="">
          <Flex justify='space-between' style={{ marginBottom: 14 }}>
            <Flex align="center">
              <span className="title">语言模型
              </span>
            </Flex>
            {isCompletion && <Flex>
              <Select
                disabled={!isCompletion}
                value={value?.type || 'basic'}
                onChange={onTypeChange}
                bordered={false}
                options={[
                  { label: '基础', value: 'basic' },
                  { label: '高级', value: 'advanced' }
                ]} />
              <Tooltip title="高级模式最多可配置三个模型，根据概率随机调用，各模型调用概率总和等于1。"><QuestionCircleOutlined style={{ color: 'rgba(0,0,0,0.5)' }} /></Tooltip>
            </Flex>}
          </Flex>

          {isAdvancedType &&
            <Flex justify="space-between" align="center"
              style={{ marginBottom: '10px' }}>
              <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                最多可选择三个模型，根据概率随机调用，各模型调用概率总和等于1。
              </Typography.Text>
              {/* <Typography.Text style={{ marginRight: '20px', fontSize: '12px'  }}>调用概率</Typography.Text> */}
            </Flex>
          }

          {isAdvancedType ?
            <Flex vertical style={{ width: '100%' }}>
              <Flex vertical gap={6}>
                {models?.map(item => {
                  const data: any = modelList?.find(it => it.name == item.modelName) || {};
                  console.log('item.modelName', item.modelName);
                  return <Flex vertical>
                    <Flex align='center' justify='space-between'>
                      <Flex vertical>
                        <Tooltip
                          color='#fff'
                          title={<Desc>
                            {data.description}
                            <p className='fee'>费用: 输入{data.fee?.input}元 | 输出{data.fee?.output}元 (/百万Token) <a href={data.url} target='_blank'>模型相关介绍</a></p>
                          </Desc>}>
                          <Tag
                            color={editModelName === item.modelName ? 'processing' : 'default'}
                            style={{ alignItems: 'center', cursor: 'pointer' }}
                            closable={models?.length > 1}
                            onClick={() => onModelClick(item)}
                            onClose={(e) => onDeleteModel(e, item)}
                          >{item.modelName}</Tag>
                        </Tooltip>
                      </Flex>
                      <Flex align='center'>
                        {isCompletion &&
                          <Flex style={{ marginLeft: 4 }}>
                            <Slider
                              max={1}
                              min={0}
                              step={0.1}
                              style={{ width: 120, marginRight: 16 }}
                              value={item.ratio}
                              onChange={(val) => onRatioChange(item.modelName, val)}

                            />
                            <InputNumber
                              max={1}
                              min={0}
                              step={0.1}
                              value={item.ratio}
                              onChange={(val) => onRatioChange(item.modelName, val)}
                            />
                          </Flex>}
                      </Flex>
                    </Flex>
                  </Flex>
                }
                )}
                <Flex>
                  <ModelModal
                    title="选择模型"
                    modelList={modelList}
                    disabledList={disabledList}
                    onConfirm={(data) => handleModelChange('models', data)}
                    defaultValue={modelSelectSettings}
                    required
                  >
                    <Button size="small">
                      <span style={{ color: 'rgba(0,0,0,0.5)', cursor: 'pointer', fontSize: '12px' }}>
                        修改模型 <EditOutlined />
                      </span>
                    </Button>
                  </ModelModal>
                </Flex>
              </Flex>
            </Flex> :
            <Flex>
              <ModelModal
                title="选择模型"
                modelList={modelList}
                disabledList={disabledList}
                onConfirm={(data) => handleModelChange('models', data)}
                defaultValue={modelSelectSettings}
                required
              >

                {/* <Tooltip title="选择模型">
                <EditOutlined style={{ cursor: 'pointer' }} />
              </Tooltip> */}
                <Button>
                  {models?.[0]?.modelName}
                  <RobotOutlined />
                </Button>
              </ModelModal>
            </Flex>}
        </div>
        <hr />
        {editModelName && <div>
          <span className="title">模型设置 - {editModelName}</span>
          <Flex style={{ width: '100%' }} align='center'>
            <ModelParamsForm
              modelName={editModelName}
              modelList={modelList}
              app={app}
              value={editModelParams}
              onChange={val => handleModelParamsChange(editModelName, val)}
            />
          </Flex>
        </div>}
        <hr />
        <RetryConfig value={retryConfig?.retryCount} onChange={onRetryConfigChange} />
        <Flex justify='end'>
          <Button type="primary" onClick={handleConfirm}>确定</Button>
        </Flex>
      </StyledContent>
    </>
  );
}

export default SettingContent;

const StyledContent = styled.div`
  width: 500px;
  display: flex;
  flex-direction: column;
  padding: 10px;

  .title {
    font-size: 16px;
    font-weight: 500;
  }

  .row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }

  .providerKind {
    color: #eee;
    font-size: 14px;
  }

  hr {
    width: 100%;
    border-color: rgba(255, 255, 255, 0.3);
    margin: 20px 0px;
  }
`;

const Desc = styled.pre`
  white-space: pre-line;
  color: #999;
  font-size: 12px;
  margin: 0;

  .fee {
    margin: 0;
    margin-top: 5px;
    color: #da4712;
  }
`;
