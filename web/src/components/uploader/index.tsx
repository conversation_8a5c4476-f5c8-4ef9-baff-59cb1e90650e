import { env } from '@/utils/common';
import { getAppId } from '@/utils/state';
import { PlusOutlined } from '@ant-design/icons';
import { Button, message, Modal, Upload } from 'antd';
import type { UploadProps } from 'antd';
import React, { useState } from 'react';
import styled from 'styled-components';

type FileType = Parameters<UploadProps['beforeUpload']>[0];

const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });

interface UploadImageProps {
  value?: any;
  accept?: string;
  onChange?: (value: any) => void;
  disabled?: boolean;
  type?: 'Image' | 'Audio' | 'Video' | 'ImageNosKey' | 'AudioNosKey' | 'VideoNosKey' | 'Url' | 'NosKey' | 'Media';
  max?: number;
  desc?: string;
  width?: number;
  height?: number;
  uploadProps?: any;
  permanent?: boolean;
  sizeLimit?: number;
  beforeUpload?: (value: any) => Promise<boolean>;
}

interface CustomUploadWrapperProps {
  width?: number;
  height?: number;
}

const CustomUploadWrapper = styled.div<CustomUploadWrapperProps>`
.ant-upload-wrapper {
  height: ${props => props.height}px; /* 设置图片卡片的高度 */
  padding: ${props => props.height ? 0 : 10}px!important;

  .ant-upload {
    width: ${props => props.width}px!important; /* 设置图片卡片的宽度 */
    height: ${props => props.height}px!important; /* 设置图片卡片的高度 */
  }
}
.ant-upload-list-picture-card .ant-upload-list-item {
  width: ${props => props.width}px!important; /* 设置图片卡片的宽度 */
  height: ${props => props.height}px!important; /* 设置图片卡片的高度 */
}
.ant-upload-list-item-container {
  width: ${props => props.width}px!important; /* 设置图片卡片的宽度 */
  height: ${props => props.height}px!important; /* 设置图片卡片的高度 */
}

.ant-upload-list-picture-card .ant-upload-list-item-thumbnail {
  // width: 100%; /* 确保图片占满卡片 */
  // height: 100%; /* 确保图片占满卡片 */
  object-fit: cover; /* 保持图片的纵横比 */
}
`;

const acceptMap = {
  'Image': 'image/*',
  'Audio': 'audio/*',
  'Video': 'video/*',
  'ImageNosKey': 'image/*',
  'AudioNosKey': 'audio/*',
  'VideoNosKey': 'video/*',
  'Media': 'image/*,audio/*,video/*',
  'Url': '*/*',
  'NosKey': '*/*'
}

const Uploader: React.FC<UploadImageProps> = ({ value, onChange, disabled, type = 'Image', max = 1, desc = 'Upload', width = 100, height = 100, uploadProps = { showUploadList: true }, accept, permanent, sizeLimit, beforeUpload: parentBeforeUpload }) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [fileList, setFileList] = useState(value && value.status !== 'removed' ? [value] : []);
  const handleCancel = () => setPreviewOpen(false);
  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    console.log('file', file);
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.uri.substring(file.url.lastIndexOf('/') + 1));
  };

  const formatFile = (file) => {
    let obj: any = file;
    if (file?.response?.data) {
      obj = {
        ...file,
        key: file?.response?.data?.key,
        url: file?.response?.data?.uri,
        name: obj.name,
        status: obj.status,
        file: file.originFileObj,
        type: getFileTypeFromUrl(file?.response?.data?.uri) || file.type
      };
    }
    console.log('formatFile', obj, file);
    return obj;
  };

  const getFileTypeFromUrl = (url: string) => {
    if (!url) return '';
    const ext = url.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(ext)) return 'image';
    if (['mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac'].includes(ext)) return 'audio';
    if (['mp4', 'webm', 'avi', 'mov', 'wmv', 'flv', 'mpeg'].includes(ext)) return 'video';
    return '';
  };

  const handleChange = (val, obj) => {
    const { fileList: newFileList } = val;
    console.log('newFileList', newFileList);
    if (!newFileList.length) {
      setFileList([]);
      onChange(null);
      return;
    }

    const formattedFiles = newFileList.map(formatFile);

    if (max === 1) {
      if (newFileList[0].status === 'done') {
        if (onChange) {
          onChange(formattedFiles[0]);
        }
      }
    } else if (max > 1) {
      const doneFiles = formattedFiles.filter(file => file.status === 'done');
      onChange(doneFiles);
    }
    setFileList(formattedFiles);
  };

  const uploadButton = () => {
    if (!uploadProps?.showUploadList && value?.url) {
      console.log('[uploader] value', value);
      const fileType = value.type || getFileTypeFromUrl(value.url);
      if (fileType === 'image') {
        return <img width={width} height={height} src={value.url} style={{ borderRadius: 4 }} />;
      } else if (fileType === 'audio') {
        return <audio controls src={value.url} style={{ width, height }} />;
      } else if (fileType === 'video') {
        return <video controls src={value.url} style={{ width, height }} />;
      }
    }

    if (width <= 50 || height <= 50) {
      return <div style={{ height, width, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <PlusOutlined />
      </div>;
    }

    return <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>{desc}</div>
    </div>;
  };

  const actionUrl = `/api/v1/app/${getAppId()}/uploadfile?${permanent ? 'permanent=true' : ''}`

  const beforeUpload = async (file: FileType) => {
    if (parentBeforeUpload) {
      try {
        const before = await parentBeforeUpload(file);
        if (!before) return false;
      } catch (error) {
        console.log('beforeUpload.error', error);
        return false
      }
    }

    if (!sizeLimit) {
      return true;
    }
    const isLimited = file.size < sizeLimit * 1024;
    if (!isLimited) {
      const unit = sizeLimit >= 1024 ? 'MB' : 'KB';
      const size = sizeLimit >= 1024
        ? (sizeLimit / 1024).toFixed(0)
        : (sizeLimit).toFixed(0);

      const fileSize = file.size / 1024; // 转换为 KB
      const fileSizeStr = fileSize >= 1024
        ? `${(fileSize / 1024).toFixed(2)}MB`
        : `${fileSize.toFixed(2)}KB`;

      message.error(`文件大小超出限制：当前大小 ${fileSizeStr}，限制大小 ${size}${unit}!`);
      return isLimited;
    }
    return isLimited;
  };

  const uploader = <Upload
    action={actionUrl}
    listType="picture-card"
    disabled={disabled}
    fileList={fileList}
    accept={accept || acceptMap[type]}
    onPreview={handlePreview}
    onChange={handleChange}
    beforeUpload={beforeUpload}
    {...uploadProps}
  >
    {fileList.length >= max && uploadProps?.showUploadList ? null : uploadButton()}
  </Upload>

  const modal = <Modal
    open={previewOpen}
    title={previewTitle}
    width={900}
    footer={null}
    onCancel={handleCancel}
  >
    {(() => {
      const fileType = getFileTypeFromUrl(previewImage);
      switch (fileType) {
        case 'image':
          return <img alt="preview" style={{ width: '100%' }} src={previewImage} />;
        case 'audio':
          return <audio controls style={{ width: '100%' }} src={previewImage} />;
        case 'video':
          return <video controls style={{ width: '100%' }} src={previewImage} />;
        default:
          return <Button type="primary" onClick={() => window.open(previewImage, '_blank')}>打开链接</Button>;
      }
    })()}
  </Modal>

  return (
    <CustomUploadWrapper width={width} height={height}>
      {uploader}
      {modal}
    </CustomUploadWrapper>
  );
};
export default Uploader;
