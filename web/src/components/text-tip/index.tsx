import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import React, { ReactElement } from 'react';

type LabelProps = {
  text: string | ReactElement;
  tip: string | ReactElement;
  hiddenIcon?: boolean;
};

const TextTip: React.FC<LabelProps> = ({ text, tip, hiddenIcon }) => (
  <Tooltip title={tip}>
    <span>
      {text} {!hiddenIcon && <QuestionCircleOutlined rev={undefined} />}
    </span>
  </Tooltip>
);

export { TextTip };
