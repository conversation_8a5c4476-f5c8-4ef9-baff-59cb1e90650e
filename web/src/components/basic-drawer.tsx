import React, { useState } from 'react';
import { But<PERSON>, Drawer } from 'antd';

type DrawerProps = {
  render?: any;
  children?: React.ReactNode;
  title?: string;
}

const BasicDrawer: React.FC<DrawerProps> = ({ render, children, ...rest }) => {
  const [open, setOpen] = useState(false);

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  return (
    <>
      {render ? React.cloneElement(render, { onClick: showDrawer }) :
        <Button type="primary" onClick={showDrawer}>
          Open
        </Button>
      }
      <Drawer {...rest} onClose={onClose} open={open} style={{ width: 900 }} width={900}>
        {children}
      </Drawer>
    </>
  );
};

export default BasicDrawer;