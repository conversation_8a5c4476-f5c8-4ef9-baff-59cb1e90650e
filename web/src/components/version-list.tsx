import { useState } from 'react';
import { Button, Table, Tag, message } from 'antd';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';
import { AppApi } from '@/api/app';
import WorkflowPreviewBtn from './workflow-preview-btn';

export const VersionList = ({ app, updateGlobalState, type = 'history' }) => {
  const [curId, setId] = useState(app.app_config_id)
  const pageSize = 5;
  const [pageNumber, setPage] = useState(1);
  const { data } = useRequest(() => {
    if (type === 'history') {
      return AppApi.getAppHistoryWithPage(pageNumber, pageSize, app.id)
    } else {
      return AppApi.getAppConfigSnapshotWithPage(app.id, pageNumber, pageSize)
    }
  }, {
    refreshDeps: [pageNumber]
  });

  const handleReload = id => {
    if (type === 'history') {
      AppApi.updateAppVersion(id, app.id).then((res) => {
        updateGlobalState('app', res);
        AppApi.copyAppConfigSnapshot(id, 'rollback').then((res) => {
          setId(id);
          message.success('回滚成功')
          window.location.reload();
        })
      })
    } else {
      AppApi.copyAppConfigSnapshot(id, 'switch').then((res) => {
        updateGlobalState('app', res);
        setId(id);
        message.success('切换成功')
        window.location.reload();
      })
    }
  }

  const handleView = id => {
    console.log('切换', id)
  }

  const columns = [{
    title: '版本号',
    dataIndex: 'message',
    render: v => v ?? '-'
  }, {
    title: '创建时间',
    dataIndex: 'createdAt',
    render: v => {
      return v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '-';
    }
  }, {
    title: '操作',
    dataIndex: 'id',
    render: (id) => {
      if (id === curId) {
        return <Tag color="success">当前版本</Tag>;
      }
      return [<WorkflowPreviewBtn text="详情" configId={id} />, <Button type="link" onClick={() => handleReload(id)}>{type === 'history' ? '回滚' : '切换'}</Button>]
    }
  }];


  return <Table dataSource={data?.items ?? []} columns={columns} pagination={{ pageSize: pageSize, total: data?.total || 0, current: pageNumber, onChange: setPage }} />
}

export default VersionList;