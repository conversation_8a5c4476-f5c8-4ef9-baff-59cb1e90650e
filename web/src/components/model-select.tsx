import { Select, Tag } from 'antd';
import { groupBy } from 'lodash';
import { useEffect, useState } from 'react';

import { GlobalModel } from '@/interface';
import { DefaultModelApi } from '@/api/model-provider';
import styled from 'styled-components';
import { useQuery } from '@/hooks/useQuery';
import { deprecate } from 'util';
import { tagMap } from '@/api/llm-model';

const MyTag = styled(Tag)`
font-size: 10px;
    padding: 2px;
    margin-right: 5px;
    line-height: 12px;
`

export interface IModelSettingProps {
  modelName: string;
  providerKind: string;
}

export interface IProps {
  value: string;
  onChange: any;
  style?: any;
  filterFn?: (v: GlobalModel) => boolean;
  destroyModal?: boolean;
  disabledList?: RegExp[];
}

const isDisabled = (disabledList = [], name) => {
  return disabledList.some(l => {
    return l.test(name);
  });
};

export function ModelSelect(props: IProps) {
  const { value, onChange, disabledList, style, filterFn } = props;
  const [modelList, setModelList] = useState([]);
  const [editModelName, setEditModelName] = useState(value );
  const { parsedQuery } = useQuery();

  useEffect(() => {
    DefaultModelApi.listGlobalModel({
      workspaceId: parsedQuery.workspaceId as string,
      groupId: parsedQuery.groupId as string
    }).then((res) => {
      setModelList(res.filter(filterFn || (() => true)));
    });
  }, [parsedQuery.workspaceId, parsedQuery.groupId])

  const groupedModelList = groupBy(modelList, 'providerName');
  const groupedOptions = Object.keys(groupedModelList).map((providerName) => {
    return {
      label: providerName,
      options: groupedModelList[providerName].filter(model => model.description !== '已废弃').map((model) => {
        const disalbed = isDisabled(disabledList, model.name);
        const title = disalbed ? '该模式下暂不可用，可以尝试在应用中使用该模型' : model.enable ? `${model.description}\n\n${model.fee.avg}元/百万Token` : '该模型暂不可用，请联系管理员录入模型账号';
        const disabledReason = model.disableReason || '';
        // console.log('model', model);
        const tags = model.tag.filter(v => tagMap[v]).map(v => <MyTag key={v} color={tagMap[v].color}>{tagMap[v].text}</MyTag>);
        if (model.fee.avg < 8) {
          tags.push(<MyTag color="lime">便宜</MyTag>)
        }
        if (model.fee.avg >= 50) {
          tags.push(<MyTag color="red">昂贵</MyTag>)
        }
        return ({
          disabled: !model.enable || disalbed,
          title: disabledReason || title,
          label: <>{tags}{`${model.description === '已废弃' ? `${model.name}（已废弃）` : model.alias || model.name}`}</>,
          value: model.name,
        })
      }),
    };
  });

  const handleModelChange = (name?, value?) => {
    let modelName = editModelName;
    if (name === 'modelName') {
      setEditModelName(value);
      modelName = value
    }
    const targetModel = modelList.find((it) => it.name === modelName);
    onChange({
      modelName,
      providerKind: targetModel?.providerKind || 'openai',
      ...targetModel
    });
  }

  return (<Select
    value={editModelName}
    onChange={v => handleModelChange('modelName', v)}
    style={{ minWidth: '240px', ...(style||{}) }}
    options={groupedOptions}
  ></Select>
  );
}


