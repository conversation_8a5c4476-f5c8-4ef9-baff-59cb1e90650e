import { Input, Modal, Form } from 'antd';
import dayjs from 'dayjs';

const { confirm } = Modal;

let pubMessage = '';

export function showPublishModal(onPublish) {
  confirm({
    title: <span>确认发布?<span style={{
      fontSize: 12,
      fontWeight: 'normal',
      color: '#999',
      marginLeft: 5
    }}>发布后线上调用立刻生效</span></span>,
    content: function(){ 
      const id = dayjs().format('MMDDHHmm');
      return <div style={{ paddingTop: 10 }}>
        <Form.Item label="版本号" rules={[{ required: true, message: '请输入版本描述' }]} required><Input disabled defaultValue={id}></Input></Form.Item>
        <Form.Item label="版本描述" rules={[{ required: true, message: '请输入版本描述' }]}><Input onChange={(ev) => pubMessage = `${id}-${ev.target.value}`}></Input></Form.Item>
      </div>
    }(),
    onOk() {
      onPublish(pubMessage);
      pubMessage = '';
    },
    onCancel() { },
  });
}