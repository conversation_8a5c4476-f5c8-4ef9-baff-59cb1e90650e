import React, { useState, useEffect } from 'react';
import { Input } from 'antd';
import styled from 'styled-components';

const { TextArea } = Input;

interface JsonEditorProps {
  value?: any;
  onChange?: (value: any) => void;
  disabled?: boolean;
}

const JsonEditor: React.FC<JsonEditorProps> = ({ value, onChange, disabled }) => {
  const [innerValue, setInnerValue] = useState('');
  const [error, setError] = useState<string>('');
  console.log('json value', value);

  // 当外部 value 改变时更新内部状态
  useEffect(() => {
    try {
      const formatted = value ? JSON.stringify(value, null, 2) : '';
      setInnerValue(formatted);
      setError('');
    } catch (e) {
      setError('Invalid JSON format');
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setInnerValue(newValue);

    try {
      if (newValue.trim() === '') {
        onChange?.(undefined);
        setError('');
        return;
      }

      const parsed = JSON.parse(newValue);
      onChange?.(parsed);
      setError('');
    } catch (e) {
      setError('Invalid JSON format');
    }
  };

  return (
    <EditorWrapper>
      <TextArea
        value={innerValue}
        onChange={handleChange}
        disabled={disabled}
        rows={4}
        style={{ 
          fontFamily: 'monospace',
          borderColor: error ? '#ff4d4f' : undefined 
        }}
        placeholder="请输入 JSON 格式的数据"
      />
      {error && <ErrorText>{error}</ErrorText>}
    </EditorWrapper>
  );
};

const EditorWrapper = styled.div`
  width: 100%;
  position: relative;
`;

const ErrorText = styled.div`
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
`;

export default JsonEditor; 