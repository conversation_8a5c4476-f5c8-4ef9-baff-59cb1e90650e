import React, { } from 'react';
import { Select } from 'antd';
import type { BaseOptionType, SelectProps } from 'antd/es/select'
import styled from 'styled-components';

const NewSelect = styled(Select)`
  border: none;

  > .ant-select-selector {
    border: none !important;
    background: transparent !important;
    box-shadow: none !important;
  }

`;

const SelectContainer = styled.div`
  font-size: 14px;
  color: #666;
  position: relative;
  display: flex;
  align-items: center;
  .ant-select-selection-item {
    font-weight: 500;
    padding-right: 18px !important;
  }
  .ant-select-arrow {
    color: #333;
  }
  .ant-select-open {
    .ant-select-arrow {
      color: #aaa;
    }
  }
  .label {
    white-space: nowrap;
  }
`;


type CustomSelectProps = {
  onChange?: any;
  label?: string;
  containerStyle?: React.CSSProperties
} & SelectProps;


const CustomSelect = ({ options, onChange, label, defaultValue, containerStyle, value, ...rest }: CustomSelectProps) => {
  const [val, setValue] = React.useState(value || defaultValue);
  const handleChange = (val) => {
    setValue(val);
    if (onChange) {
      onChange(val);
    }
  }
  return <SelectContainer style={containerStyle || {}}>
    <span className='label'>{label}</span>
    <NewSelect value={value || val} onChange={handleChange} options={options} {...rest}></NewSelect>
  </SelectContainer>

}
export default CustomSelect;