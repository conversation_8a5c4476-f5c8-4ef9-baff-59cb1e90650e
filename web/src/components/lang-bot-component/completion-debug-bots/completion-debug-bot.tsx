import CompletionResult from "@/pages/app/components/chat-bot/completion-result";
import ModelsSelect from "@/pages/app/components/chat-bot/models-select";
import { getMaxRatioModel } from "@/pages/app/components/chat-bot/utils";
import eventBus, { IEventType } from "@/utils/event-bus";
import { RobotOutlined } from "@ant-design/icons";
import { Flex } from "antd";
import { useEffect, useRef, useState } from "react";

const CompletionDebugBot = (props) => {
  const { config, showModel, debugId, index, ...rest } = props;

  const [debugModel, setDebugModel] = useState(null);
  const completionResult = useRef(null);

  useEffect(() => {
    const sendEvent = eventBus.on(IEventType.DEBUG_MESSAGE_SEND, (val) => {
      if (val?.debugId !== debugId) return;
      completionResult.current?.onCompleteRun(...val.arg);
    });

    return () => {
      eventBus.cancel(IEventType.DEBUG_MESSAGE_SEND, sendEvent);
    };
  }, []);


  useEffect(() => {
    // 获取概率最大的模型调试
    const debugModel = getMaxRatioModel(config?.config?.modelsConfig?.models);

    setDebugModel(debugModel);
  }, [JSON.stringify(config?.config?.modelsConfig?.models)]);

  const onDebugModelChange = val => {
    const model = config?.config?.modelsConfig?.models?.find(it => it.modelName === val);

    setDebugModel(model);
  };

  return <Flex vertical style={{ width: '100%', height: '100%' }}>
    <Flex justify="space-between"
      style={{
        margin: '12px 12px 12px 12px',
      }}>

      {config?.title &&
        <Flex style={{ fontWeight: 'bold' }}>
          {config?.title}
        </Flex>
      }

      <Flex>
        {showModel &&
          <ModelsSelect
            options={config?.config?.modelsConfig?.models}
            value={debugModel?.modelName}
            onChange={onDebugModelChange}
            trigger={<span
              style={{ fontSize: 14, color: '#aaa', fontWeight: 'normal', cursor: 'pointer' }} >
              {debugModel?.modelName} <RobotOutlined />
              {/* <IconButton icon={<RobotOutlined />} label="模型"></IconButton> */}
            </span>}
          />}
      </Flex>
    </Flex>

    <Flex flex={1}>
      <CompletionResult
        index={index}
        key={index}
        ref={completionResult}
        renderNavBar={() => null}
        {...rest}
        debugConfig={{
          ...(config?.config || {}),
          modelsConfig: {
            ...config?.config?.modelsConfig,
            models: debugModel ? [debugModel] : config?.config?.modelsConfig?.models
          },
        }}
      />
    </Flex>
  </Flex>
};


export default CompletionDebugBot;
