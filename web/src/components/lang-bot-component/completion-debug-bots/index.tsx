import ModelsSelect from "@/pages/app/components/chat-bot/models-select";
import { Flex, Switch } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import { v4 as uuidv4 } from 'uuid';
import CompletionDebugBot from "./completion-debug-bot";
import { getMaxRatioModel } from "@/pages/app/components/chat-bot/utils";
import { RobotOutlined } from "@ant-design/icons";
import eventBus, { IEventType } from "@/utils/event-bus";

const CompletionDebugBots = (props, ref) => {
  const { configs, ...rest } = props;
  const [topModel, setTopModel] = useState(false);
  const [debugModel, setDebugModel] = useState<any>({});

  const uuid = useRef(uuidv4());

  useImperativeHandle(ref, () => {
    return {
      onCompleteRun: val => {
        handleSend(val);
      }
    }
  });

  const handleSend = (...arg) => {
    eventBus.emit(IEventType.DEBUG_MESSAGE_SEND, {
      debugId: uuid.current,
      arg
    });
  };

  useEffect(() => {
    // 获取概率最大的模型调试
    const debugModel = getMaxRatioModel(configs?.[0]?.config?.modelsConfig?.models);

    setDebugModel(debugModel);
  }, [JSON.stringify(configs?.[0]?.config?.modelsConfig?.models)]);

  const onTopModelShow = val => {
    setTopModel(val);
  };

  const onDebugModelChange = val => {
    const model = configs?.[0]?.config?.modelsConfig?.models?.find(it => it.modelName === val);
    setDebugModel(model);
  };

  return <Flex vertical style={{ height: '100%' }}>
    {false && <Flex>
      {topModel &&
        <ModelsSelect
          options={configs?.[0]?.config?.modelsConfig?.models}
          value={debugModel?.modelName}
          onChange={onDebugModelChange}
          trigger={<span
            style={{ fontSize: 14, color: '#aaa', fontWeight: 'normal', cursor: 'pointer' }} >
            {debugModel?.modelName} <RobotOutlined />
            {/* <IconButton icon={<RobotOutlined />} label="模型"></IconButton> */}
          </span>}
        >
        </ModelsSelect>
      }
      <Flex align="center" style={{ marginLeft: '10px' }}>
        <Flex style={{ marginRight: 6 }}>
          模型联动
        </Flex>
        <Switch checked={topModel} onChange={onTopModelShow} />
      </Flex>
    </Flex>}

    <Flex flex={1} >
      {configs?.map((config, index) => {
        return <Flex flex={1}
          style={{
          }} key={index}>
          <CompletionDebugBot
            key={index}
            index={index}
            config={config}
            // showModel={!topModel}
            debugId={uuid.current}
            {...rest}
          />
        </Flex>
      })}
    </Flex>
  </Flex>
};

export default forwardRef(CompletionDebugBots);
