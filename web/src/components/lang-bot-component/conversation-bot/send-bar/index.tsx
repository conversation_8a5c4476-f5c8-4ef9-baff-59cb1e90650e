import { ClearIcon } from "@/components/icons";
import InputComposer from "@/components/input-composer";
import { ClearOutlined } from "@ant-design/icons";
import { Flex, Tooltip } from "antd";

const SendBar = (props) => {
  return <>
    <Flex style={{ width: '100%' }} align="center" gap={12}>
      <Tooltip title="清空对话">
        <Flex justify="center" align="center"
          style={{
            borderRadius: '100%',
            border: '1px solid #d9d9d9',
            width: '40px',
            height: '40px',
          }}>
          <ClearOutlined style={{ fontSize: 24 }} onClick={props?.onClearConversation} />
        </Flex>
      </Tooltip>
      <Flex style={{ width: '100%' }} flex={1}>
        <InputComposer
          onSend={props?.onSend}
          enableView={false}
          fileView={false}
          placeholder="按 Shift+Enter 添加换行"
          size="small"
        />
      </Flex>
    </Flex>
  </>
}

export default SendBar;