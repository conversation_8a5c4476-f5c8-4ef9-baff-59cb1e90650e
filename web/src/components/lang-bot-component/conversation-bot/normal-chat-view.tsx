import { ChatView } from "@music/lang";
import { INormalChatViewProps } from "../type";
import { CodeSnippet } from "@/components/code-snippet";
import { useCallback, useEffect, useRef, useState } from "react";
import eventBus, { IEventType } from "@/utils/event-bus";

interface IProps extends INormalChatViewProps {
  debugId: string;
  index: number;
  renderDep?: any;
};

const NormalChatView = (props: IProps) => {
  const { debugId, index, config, ...rest } = props;

  const composerRef = useRef(null);
  const [conversationKey, setConversationKey] = useState('' + Date.now() + '-' + index);

  const onClear = useCallback(() => {
    setConversationKey('' + Date.now() + '-' + index);
  }, []);


  useEffect(() => {
    const sendEvent = eventBus.on(IEventType.DEBUG_MESSAGE_SEND, (val) => {
      if (val?.debugId !== debugId) return;
      composerRef.current.onSend(...val.arg);
    });

    const clearEvent = eventBus.on(IEventType.DEBUG_MESSAGE_CLEAR, (val) => {
      if (val?.debugId !== debugId) return;
      onClear();
    });

    return () => {
      eventBus.cancel(IEventType.DEBUG_MESSAGE_SEND, sendEvent);
      eventBus.cancel(IEventType.DEBUG_MESSAGE_CLEAR, clearEvent);
    };
  }, []);

  return <ChatView
    // conversationId不做其他用处，只是为了刷新会话
    key={conversationKey}
    components={{
      Code: CodeSnippet
    }}
    // @ts-ignore
    Composer={(composerProps) => {
      composerRef.current = composerProps;
      return null;
    }}
    renderNavbar={() => null}
    config={config}
    showParams={false}
    userId='langbase-debug'
    debug
    host={window.location.host}
    {...rest}
  />
};

export default NormalChatView;