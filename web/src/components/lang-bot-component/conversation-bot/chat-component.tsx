import { Flex, Typography } from "antd";
import { ModelSetting } from "@/components/ModelSetting";
import { IVirtualHumanChatViewConfig } from "../type";
import NormalChatView from "./normal-chat-view";
import VirtualChatView from "./virtual-chat-vew";
import { useState } from "react";
import ModelsSelect from "@/pages/app/components/chat-bot/models-select";
import { MultipleModelSetting } from "@/components/MultipleModelSetting";
import { IAppType } from "@/interface";

const ChatComponent = (props) => {
  const { config, index, debugId, showModel, ...rest } = props;
  const { config: appConfig } = config || {};
  const defaultModelConfig = appConfig?.type === 'virtual-human' ? appConfig?.config?.modelConfig : appConfig?.modelsConfig;

  const [modelConfig, setModelConfig] = useState(defaultModelConfig);
  const [modelsConfig, setModelsConfig] = useState(defaultModelConfig);

  const onModelChange = val => {
    setModelConfig(val);
  };

  const onModelsChange = val => {
    // setModelsConfig(val);
    // 聊天助理最多只选择一个模型
    if (rest?.appType === IAppType.AgentConversation) {
      setModelsConfig({
        retryConfig: val?.retryConfig,
        models: [{
          ...(val?.models?.[0] || {}),
          ratio: 1
        }]
      });
    } else {
      setModelsConfig(val);
    }
  };

  return <Flex vertical style={{ width: '100%', height: '100%' }}>
    <Flex align="center" justify="space-between"
      style={{
        margin: '12px 12px 12px 12px',
        // borderBottom: '1px solid #ededed'
      }}>
      {config?.title && <Flex style={{ fontWeight: 'bold' }}>
        {config?.title}
      </Flex>}
      <Flex>
        {showModel && (appConfig?.type === 'virtual-human'
          ? <ModelSetting value={modelConfig} onChange={onModelChange} />
          : <MultipleModelSetting value={modelsConfig} onChange={onModelsChange} appType={rest.appType} destroyModal />
        )}
      </Flex>
    </Flex>
    <Flex
      flex={1}
      style={{ overflow: 'hidden' }}
    >
      {/* @ts-ignore */}
      {appConfig?.type === 'virtual-human' ? (
        <VirtualChatView
          key={index}
          index={index}
          debugId={debugId}
          config={{
            ...(appConfig || {}),
            config: {
              ...(appConfig?.config || {}),
              // 如果用自己的modelselect, 需要覆盖顶层的
              modelConfig: showModel ? modelConfig : appConfig?.config?.modelConfig,
              ...(showModel ? modelConfig : {})
            }
          }}
          {...rest}
        // {...basicProps}
        />
      ) : (
        <NormalChatView
          key={index}
          index={index}
          debugId={debugId}
          config={{
            ...(appConfig || {}),
            modelsConfig: showModel ? modelsConfig : appConfig?.modelsConfig

          }}
          {...rest}
        // {...basicProps}
        />
      )}
    </Flex>
  </Flex>
};

export default ChatComponent;