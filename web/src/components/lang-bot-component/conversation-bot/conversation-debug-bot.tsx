import { Flex, Typography } from "antd";
import { IChatViewConfig, INormalChatViewProps, IVirtualHumanChatViewConfig } from "../type";
import NormalChatView from "./normal-chat-view";
import SendBar from "./send-bar";
import TopToolBar from "./top-tool-bar";
import VirtualChatView from "./virtual-chat-vew";
import eventBus, { IEventType } from '@/utils/event-bus';
import { useRef, useState } from "react";
import { v4 as uuidv4 } from 'uuid';
import styled from "styled-components";
import ChatComponent from "./chat-component";

interface IProps extends Omit<INormalChatViewProps, 'config'> {
  configs: {
    title?: any;
    config: IChatViewConfig;
  }[];
  onConversationsClear?: () => void;
  onModelChange?: (model: any) => void;
  modelConfig?: any;
  modelsConfig?: any;
  renderDep: any;
  appType?: string;
  onModelsChange?: (models: any) => void;
};

const Wrapper = styled(Flex)`
  .ChatFooter {
    border-top: none;
  }
`;

const ConversationDebugBot = (props: IProps) => {
  const { configs, onConversationsClear, onModelChange, modelConfig, renderDep, modelsConfig, onModelsChange, ...rest } = props;
  const [paramsValue, setParamsValue] = useState();
  const [topModel, setTopModel] = useState(true);
  const uuid = useRef(uuidv4());

  const handleSend = (...arg) => {
    eventBus.emit(IEventType.DEBUG_MESSAGE_SEND, {
      debugId: uuid.current,
      arg
    });
  };

  const handleClearConversation = () => {
    eventBus.emit(IEventType.DEBUG_MESSAGE_CLEAR, {
      debugId: uuid.current
    });
    // onConversationsClear?.();
  };

  const onParamsChange = val => {
    setParamsValue(val);
  };

  const onTopModelShow = val => {
    setTopModel(val);
  };

  const basicProps = {
    paramsValue
  };

  return <Wrapper vertical style={{ height: '100%' }}>
    <Flex style={{ height: 40 }}>
      <TopToolBar
        config={configs?.[0]?.config}
        {...rest}
        onParamsChange={onParamsChange}
        onModelChange={onModelChange}
        modelConfig={modelConfig}
        modelsConfig={modelsConfig}
        onModelsChange={onModelsChange}
        onTopModelShow={onTopModelShow}
        showModel={topModel}
      />
    </Flex>
    <Flex
      style={{ overflow: 'hidden' }}
      flex={1}>
      {configs?.map((config, index) => {
        return <Flex
          key={index}
          vertical
          style={{
            borderRight: index === configs?.length - 1 ? null : '1px solid #ededed',
            height: '100%'
          }} flex={1}>
          <ChatComponent
            key={index}
            config={config}
            index={index}
            showModel={!topModel}
            debugId={uuid.current}
            renderDep={renderDep}
            {...rest}
            {...basicProps}
          />

        </Flex>
      })}
    </Flex>
    <Flex style={{ width: '100%' }}>
      <SendBar
        onSend={handleSend}
        onClearConversation={handleClearConversation}
      />
    </Flex>
  </Wrapper>
}

export default ConversationDebugBot;
