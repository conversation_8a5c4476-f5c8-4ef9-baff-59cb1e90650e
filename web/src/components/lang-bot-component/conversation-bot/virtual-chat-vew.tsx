import { ChatView } from "@music/lang";
import { IVirtualHumanChatViewProps } from "../type";
import { CodeSnippet } from "@/components/code-snippet";
import { Flex, Tooltip } from "antd";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import eventBus, { IEventType } from "@/utils/event-bus";
import TopBar from "@/pages/app/components/chat-bot/top-bar";
import { ClearIcon } from "@/components/icons";

interface IProps extends IVirtualHumanChatViewProps {
  debugId: string;
  index: number;
  renderDep: any;
}

const VirtualChatView = (props: IProps) => {
  const { debugId, config, index, ...rest } = props;
  const [conversationKey, setConversationKey] = useState('' + Date.now() + '-' + index);

  const composerRef = useRef(null);

  const onClear = useCallback(() => {
    setConversationKey('' + Date.now() + '-' + index);
  }, []);

  useEffect(() => {
    const sendEvent = eventBus.on(IEventType.DEBUG_MESSAGE_SEND, (val) => {
      if (val?.debugId !== debugId) return;
      composerRef.current.onSend(...val.arg);
    });

    const clearEvent = eventBus.on(IEventType.DEBUG_MESSAGE_CLEAR, (val) => {
      if (val?.debugId !== debugId) return;
      onClear();
    });

    return () => {
      eventBus.cancel(IEventType.DEBUG_MESSAGE_SEND, sendEvent);
      eventBus.cancel(IEventType.DEBUG_MESSAGE_CLEAR, clearEvent);
    };
  }, []);

  return <ChatView
    type="virtual-human"
    config={{
      conversationId: conversationKey,
      ...config,
    }}
    components={{
      Code: CodeSnippet
    }}
    showParams={false}
    // @ts-ignore
    Composer={(composerProps) => {
      composerRef.current = composerProps;
      return null;
    }}
    renderNavbar={navbar => {
      return null;
      return <TopBar
        rightContent={<Flex gap={8}>

          <Tooltip title="清空对话">
            <ClearIcon />
          </Tooltip>
        </Flex>}
      />
    }}
    {...rest}
  />
};

export default memo(VirtualChatView, (prevProps, nextProps) => {
  // 控制重渲染，配置不变情况不要重渲染
  if (prevProps?.useTts !== nextProps?.useTts) {
    return false;
  }
  if(prevProps.renderDep !== nextProps.renderDep) {
    return false;
  }
  
  if (JSON.stringify(prevProps?.config) === JSON.stringify(nextProps?.config)
    && prevProps?.greetings === nextProps?.greetings) {
    return true
  }


  return false;
});
