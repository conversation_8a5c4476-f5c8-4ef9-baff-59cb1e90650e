import ParamsDebug from "@/pages/app/components/chat-bot/params-debug";
import { INormalChatViewProps, IVirtualHumanChatViewConfig } from "../../type";
import { Checkbox, Flex, Switch } from "antd";
import { useEffect, useState } from "react";
import TopBar from "@/pages/app/components/chat-bot/top-bar";
import UseTts from "@/pages/app/components/virtual-human/groups-prompt/use-tts";
import ViewMemory from "@/pages/app/components/virtual-human/groups-prompt/view-memory";
import { IVirtualHumanConfig } from "@/interface/config";
import { SettingIcon } from "@/components/icons";
import { ModelSetting } from "@/components/ModelSetting";
import { MultipleModelSetting } from "@/components/MultipleModelSetting";

interface IProps extends INormalChatViewProps {
  onParamsChange?: (val: any) => void;
  modelConfig?: any;
  onModelChange?: (val: any) => void;
  showModel?: boolean;
  onTopModelShow?: (val: boolean) => void;

  modelsConfig?: any;
  onModelsChange?: (val: any) => void;
  appType?: any;
}

const TopToolBar = (props: IProps) => {
  const { config, onParamsChange: onParentParamsChange, modelConfig, onModelChange, appId, settingId, userId, useTts, handlers,
    showModel, onTopModelShow, modelsConfig, onModelsChange, appType } = props;
  const [paramsValue, setParamsValue] = useState<Record<string, any>>();

  // @ts-ignore
  const appConfig = config?.type === 'virtual-human' ? (config as IVirtualHumanChatViewConfig).config : config;
  const hasParams = config?.paramsInPrompt?.length > 0;

  useEffect(() => {
    // paramsInPrompt改变的时候需要刷新默认值
    const initialValue = config?.paramsInPrompt?.reduce((acc, cur) => {
      acc[cur.key] = paramsValue && paramsValue.hasOwnProperty(cur.key) ? paramsValue[cur.key] : cur.default_val
      return acc;
    }, {});
    setParamsValue(initialValue);
  }, [config?.paramsInPrompt]);

  const onParamsChange = parameters => {
    setParamsValue(parameters);
    onParentParamsChange?.(parameters);
  };

  const onShowModelChange = ev => {
    // console.log('onShowModelChange', ev);
    // const checked = ev.target.checked;
    // setShowModel(ev);
    onTopModelShow(ev);
  };

  return <Flex vertical style={{ width: '100%' }}>
    <TopBar
      logo='https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/28351044871/502c/0c77/5aaa/7358fcfc3e00d3332d8dcc4077978706.gif'
      leftContent={<Flex>
        {showModel &&
          // @ts-ignore
          (config?.type === 'virtual-human' ?
            <ModelSetting value={modelConfig} onChange={onModelChange} /> :
            <MultipleModelSetting value={modelsConfig} onChange={onModelsChange} appType={appType} />)
        }
        <Flex align="center" style={{ marginLeft: '10px' }}>
          {/* <Checkbox checked={showModel} onChange={onShowModelChange}>
            
          </Checkbox> */}
          <Flex style={{ marginRight: 6 }}>
            模型联动
          </Flex>
          <Switch checked={showModel} onChange={onShowModelChange} />
        </Flex>
      </Flex>}
      rightContent={<Flex gap={12}>
        {/* @ts-ignore */}
        {config?.type === 'virtual-human'
          && (config as IVirtualHumanConfig)?.memoryConfig?.longMemoryType !== 'NONE'
          && <ViewMemory
            appId={appId}
            config={appConfig}
            settingId={settingId}
            userId={userId}
          />}
        {hasParams &&
          <ParamsDebug
            paramsInPrompt={config?.paramsInPrompt}
            onChange={onParamsChange}
            value={paramsValue}
          />}
        {useTts ? <UseTts /> : null}
        {
          handlers?.SYSTEM_SETTING && <SettingIcon
            style={{ cursor: 'pointer' }}
            onClick={props?.handlers?.SYSTEM_SETTING}
          />
        }
      </Flex>}
    />
  </Flex>
};

export default TopToolBar;
