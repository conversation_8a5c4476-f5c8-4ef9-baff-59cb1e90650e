import {
  ICompletionConfig,
  IConversationConfig,
  IVirtualHumanConfig,
} from '@/interface/config';

// !!!虚拟人要传入的config，和生成型聊天型不一致。
export interface IVirtualHumanChatViewConfig {
  type?: string;
  workspaceId: string;
  groupId: string;
  appId: string;
  userId: string;
  conversationId?: string;

  paramsInPrompt?: any[];
  // 虚拟人sse对话
  supportSSE?: boolean;
  ttsConfig?: any;

  config?: IVirtualHumanConfig;
}

export type IChatViewConfig =
  | IConversationConfig
  | ICompletionConfig
  | IVirtualHumanChatViewConfig;

export interface INormalChatViewProps {
  appId?: string;
  settingId?: string;
  userId?: string;
  type?: 'agent' | 'agent-workflow' | 'agent-conversation' | 'chat-flow';
  env?: 'test' | 'online' | 'dev';
  showFeedback?: boolean;
  debug?: boolean;
  greetings?: string;
  config: IChatViewConfig;

  host?: string;

  /**
   * 动作响应处理
   */
  handlers?: Record<string, (data?: any) => any>;

  /**
   * 外部传入的变量值
   */
  paramsValue?: Record<string, any>;

  useTts?: boolean;
}

export interface IVirtualHumanChatViewProps extends Omit<INormalChatViewProps, 'config'> {
  config: IVirtualHumanChatViewConfig;
}
