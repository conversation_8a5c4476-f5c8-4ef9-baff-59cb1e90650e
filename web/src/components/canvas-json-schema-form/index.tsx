import { JsonSchemaForm } from './main'
import { FormItemWrapper } from './schema-form'
import './style/index.less'
import {
  NsJsonSchemaFormModel as NsJsonSchemaFormModel,
  executeJsonSchemaFormCommand,
} from './service'
import * as NsJsonSchemaForm from './interface'

export {
  NsJsonSchemaForm,
  NsJsonSchemaFormModel,
  JsonSchemaForm,
  FormItemWrapper,
  executeJsonSchemaFormCommand,
}

export type { IFromItemWrapperProps } from './schema-form'
export type { IProps as IJsonSchemaFormProps } from './interface'
