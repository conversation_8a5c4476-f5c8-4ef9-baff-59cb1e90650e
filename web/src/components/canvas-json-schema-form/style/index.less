@light-border: ~'1px solid #d9d9d9';
@header-bg: #fff;
@body-bg: #f9f9f9;

.xflow-json-schema-form {
  border-left: none;
  &-body {
    border-radius: 15px;
    position: relative;
    width: 100%;
    height: 100%;
    box-shadow: 0 1px 11px 0 rgba(206, 201, 201, 0.5);
  }
  &-header {
    display: flex;
    justify-content: space-evenly;
    background: @header-bg;
    border-bottom: @light-border;
    &-title {
      font-size: 16px;
    }
  }

  &-footer {
    display: flex;
    justify-content: space-evenly;
    background: @header-bg;
    border-top: @light-border;
    &-title {
      font-size: 16px;
    }
  }
}

.text-truncate {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
