@import (reference) '~antd/es/style/themes/default.less';

@backgroundColor: #fff;
@fontSize: 12px;
@color: #666;

.xflow-json-form {
  height: 100%;

  label {
    font-size: @fontSize;
  }

  .@{ant-prefix}-form-item {
    margin-bottom: 12px;

    .@{ant-prefix}-form-item-label {
      padding-bottom: 4px;

      & > label {
        color: @color;
      }
    }

    .@{ant-prefix}-form-item-control {
      .@{ant-prefix}-checkbox-wrapper {
        color: @color;
      }
    }
  }

  .@{ant-prefix}-form-item-explain,
  .@{ant-prefix}-form-item-extra {
    font-size: @fontSize;
  }

  .@{ant-prefix}-form-item-explain-error > div {
    word-break: break-all;
  }

  .@{ant-prefix}-form-item-extra {
    margin-top: 4px;
  }

  .tabs {
    flex-direction: column;
    height: 100%;

    .@{ant-prefix}-tabs-nav {
      box-shadow: ~'0 0 16px -5px rgba(0, 0, 0, 0.2)';
      .@{ant-prefix}-tabs-nav-list {
        width: 100%;

        .@{ant-prefix}-tabs-tab {
          font-size: @fontSize;

          &-active {
            border-bottom-width: 0;

            .@{ant-prefix}-tabs-tab-btn {
              // color: #1890ff;
            }
          }

          .@{ant-prefix}-tabs-tab-btn {
            font-weight: normal;
            text-align: center;
          }
        }
      }
    }

    .@{ant-prefix}-tabs-content-holder {
      padding: 0 16px;
      overflow-y: auto;
    }

    &.xTab {
      .@{ant-prefix}-tabs-nav {
        .@{ant-prefix}-tabs-nav-list {
          box-sizing: border-box;
          height: 40px;
          background-color: @backgroundColor;
          border-bottom: 1px solid #d9d9d9;

          .@{ant-prefix}-tabs-tab {
            background-color: @backgroundColor;

            .@{ant-prefix}-tabs-tab-btn {
              width: 100%;
            }
          }
        }
      }
    }

    &.singleTab {
      .@{ant-prefix}-tabs-tab {
        width: 100%;
      }
    }

    &.coupleTab {
      .@{ant-prefix}-tabs-tab {
        width: 50%;
        margin-right: 0;
        border-right-width: 0;

        &:last-of-type {
          border-right-width: 1px;
        }
      }
    }

    &.ternateTab {
      .@{ant-prefix}-tabs-tab {
        width: 33%;
        margin-right: 0;
        border-right-width: 0;
        &:last-of-type {
          border-right-width: 1px;
        }
      }
    }
  }
}
