import React, { CSSProperties, forwardRef, PropsWithChildren } from 'react';
import { styled } from 'styled-components';

const ContainerStyle = styled.div`
  display: flex;
  justify-content: center;
  height: 100%;

  & > div {
    margin-left: auto;
    margin-right: auto;
    height: 100%;
  }
`;


export const LayoutContainer = forwardRef(
  (
    props: PropsWithChildren<{
      className?: string;
      style?: CSSProperties;
      innerStyle?: CSSProperties;
    }>,
    ref: React.ForwardedRef<HTMLDivElement>,
  ) => {
    const { children, className, style, innerStyle = { width: '100%' } } = props;
    return (
      <ContainerStyle ref={ref} className={className} style={style}>
        <div style={innerStyle}>{children}</div>
      </ContainerStyle>
    );
  },
);

LayoutContainer.displayName = 'LayoutContainer';

export const MidContainer = forwardRef(
  (
    props: PropsWithChildren<{
      className?: string;
      style?: CSSProperties;
      innerStyle?: CSSProperties;
    }>,
    ref: React.ForwardedRef<HTMLDivElement>,
  ) => {
    const { children, className, style, innerStyle = { width: '80%' } } = props;
    return (
      <ContainerStyle ref={ref} className={className} style={style}>
        <div style={innerStyle}>{children}</div>
      </ContainerStyle>
    );
  },
);

MidContainer.displayName = 'MidContainer';