// @ts-nocheck
import { Form, Switch, SwitchProps } from 'antd';

interface ILFormSwitchProps extends SwitchProps {
  name: string;
  label: string;
  rules?: any[];
}

export function LFormSwitch(props: ILFormSwitchProps) {
  const { name, label, rules, ...otherProps } = props;
  return (
    <Form.Item
      name={name}
      label={label}
      rules={rules}
      valuePropName="checked"
      {...otherProps}
    >
      <Switch {...otherProps} />
    </Form.Item>
  );
}
