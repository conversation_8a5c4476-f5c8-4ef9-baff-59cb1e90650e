// @ts-nocheck
import { Form, Input, InputProps, InputNumber, InputNumberProps } from 'antd';
import { TextAreaProps } from 'antd/es/input';

const { TextArea } = Input;
interface ILFormInputProps extends InputProps {
  name: string;
  label: string;
  rules?: any[];
}

interface ILFormInputNumberProps extends InputNumberProps {
  name: string;
  label: string;
  rules?: any[];

}

interface ILFormTextAreaProps extends TextAreaProps {
  name: string;
  label: string;
  rules?: any[];
}

export function LFormInput(props: ILFormInputProps) {
  const { name, label, rules, ...otherProps } = props;
  return (
    <Form.Item name={name} label={label} rules={rules} {...otherProps}>
      <Input {...otherProps} />
    </Form.Item>
  );
}

export function LFormInputNumber(props: ILFormInputNumberProps) {
  const { name, label, rules, ...otherProps } = props;
  return (
    <Form.Item name={name} label={label} rules={rules} {...otherProps}>
      <InputNumber {...otherProps} />
    </Form.Item>
  );
}

export function LFormTextArea(props: ILFormTextAreaProps) {
  const { name, label, rules, ...otherProps } = props;
  return (
    <Form.Item name={name} label={label} rules={rules}>
      <TextArea {...otherProps} />
    </Form.Item>
  );
}
