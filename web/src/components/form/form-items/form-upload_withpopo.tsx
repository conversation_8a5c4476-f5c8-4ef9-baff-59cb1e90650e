// @ts-nocheck
import React from 'react';
import { Form, Button, Modal, Spin, message, Input, Row, Col, Flex, Select } from 'antd';
import { UploadOutlined, DeleteOutlined, EyeOutlined, LoadingOutlined, FilePdfOutlined, FileWordOutlined, FileTextOutlined, FileExcelOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { getAppId } from '@/utils/state';
import { uploadFile, getFileData, getPopoFileData } from '@/utils/common';
import axios from 'axios';

// 文件预览区域样式
const FilePreviewWrapper = styled.div`
  margin: 5px;
  display: inline-block;
  background-color: #f8f8f8;
  border-radius: 12px;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
`;

const FilePreviewHeader = styled.div`
  font-size: 12px;
  color: #888;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const FilePreviewContent = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const FileIcon = styled.div`
  font-size: 24px;
  color: #1890ff;
  flex-shrink: 0;
`;

const FileInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const FileName = styled.div`
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  max-width: 200px;
  text-overflow: ellipsis;
`;

const FileDetails = styled.div`
  font-size: 12px;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const DeleteButton = styled.div`
  cursor: pointer;
  color: #999;
  padding: 4px;
  flex-shrink: 0;
  
  &:hover {
    color: #ff4d4f;
  }
`;

const PreviewButton = styled.div`
  cursor: pointer;
  color: #999;
  padding: 4px;
  
  &:hover {
    color: #40a9ff;
  }
`;

// 获取文件图标
const getFileIcon = (fileType: string, status: FileUploadStatus) => {
  if (status === 'uploading') {
    return <LoadingOutlined style={{ color: '#1890ff' }} spin />;
  }

  if (fileType.includes('pdf')) {
    return <FilePdfOutlined style={{ color: 'red' }} />;
  } else if (fileType.includes('csv') || fileType.includes('excel') || fileType.includes('spreadsheet') || fileType.includes('xls')) {
    return <FileExcelOutlined style={{ color: 'green' }} />;
  } else if (fileType.includes('word') || fileType.includes('doc')) {
    return <FileWordOutlined style={{ color: 'blue' }} />;
  } else {
    return <FileTextOutlined style={{ color: '#a0a0a0' }} />;
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes < 1024) {
    return bytes + 'B';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + 'KB';
  } else {
    return (bytes / (1024 * 1024)).toFixed(2) + 'MB';
  }
};

// 获取简化的文件类型名称
const getSimpleFileType = (fileType: string): string => {
  if (fileType.includes('pdf')) {
    return 'PDF';
  } else if (fileType.includes('csv') || fileType.includes('excel') || fileType.includes('spreadsheet') || fileType.includes('xls')) {
    return 'EXCEL';
  } else if (fileType.includes('word') || fileType.includes('doc')) {
    return 'WORD';
  } else if (fileType.includes('markdown') || fileType.includes('md')) {
    return 'MD';
  } else if (fileType.includes('text/plain')) {
    return 'TXT';
  } else {
    const parts = fileType.split('/');
    return parts.length > 1 ? parts[1].toUpperCase() : parts[0].toUpperCase();
  }
};

type FileUploadStatus = 'uploading' | 'success' | 'expired' | 'error';

interface UploadFileState {
  file?: File;
  name: string;
  type: string;
  status: FileUploadStatus;
  data?: any;
  error?: string;
  url?: string;
  key?: string;
}

interface ILFormUploadProps {
  name: string;
  label: string;
  rules?: any[];
  accept?: string;
  maxSize?: number;
  maxTokens?: number;
  onChange?: (value: any) => void;
}

// 获取文件内容
const fetchFileContent = async (key: string) => {
  try {
    const response = await getFileData(key);
    return response || '';
  } catch (error) {
    console.error('获取文件内容失败:', error);
    message.error('获取文件内容失败');
    throw error;
  }
};

// 通用文件预览弹窗组件
function FilePreviewModal({ open, onCancel, title, loading, content }: {
  open: boolean;
  onCancel: () => void;
  title?: string;
  loading?: boolean;
  content?: string;
}) {
  return (
    <Modal
      title={title}
      open={open}
      onCancel={onCancel}
      footer={null}
      width="80%"
      style={{ top: 20 }}
      bodyStyle={{ maxHeight: 'calc(90vh - 100px)', overflow: 'auto' }}
    >
      <Spin spinning={loading}>
        <pre style={{
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          padding: '16px',
          backgroundColor: '#f5f5f5',
          borderRadius: '4px',
          maxHeight: 'calc(90vh - 150px)',
          overflow: 'auto'
        }}>
          {content}
        </pre>
      </Spin>
    </Modal>
  );
}

// 新增：POPO文档输入组件
function PopoDocInput({ onChange }: { onChange?: (value: any) => void }) {
  const [link, setLink] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = React.useState<any>(null);
  const [error, setError] = React.useState<string | null>(null);
  const [previewOpen, setPreviewOpen] = React.useState(false);

  const handleFetch = async () => {
    if (!link) {
      setError('请输入POPO文档链接');
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const data = await getPopoFileData(link);
      if (data) {
        setData(data);
        onChange?.({ link, renderType: 'popo-link' });
        message.success('文档获取成功');
      } else {
        setError('未获取到文档内容');
      }
    } catch (e: any) {
      setError(e?.message || '获取失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Flex style={{ marginLeft: 10 }}>
      <Input
        placeholder="请输入POPO文档链接"
        value={link}
        onChange={e => setLink(e.target.value)}
        style={{ width: 260 }}
        disabled={loading}
      />
      <Button type="primary" onClick={handleFetch} loading={loading} style={{ marginLeft: 8 }}>
        确定
      </Button>
      {data && (
        <Button icon={<EyeOutlined />} style={{ marginLeft: 8 }} onClick={() => setPreviewOpen(true)}>
          预览
        </Button>
      )}
      {error && <span style={{ color: 'red', marginLeft: 8 }}>{error}</span>}
      <FilePreviewModal
        open={previewOpen}
        onCancel={() => setPreviewOpen(false)}
        title={link ? `POPO文档预览` : '文档预览'}
        loading={false}
        content={data || ''}
      />
    </Flex>
  );
}

export function FormUploadFile(props: ILFormUploadProps) {
  const {
    accept = ".txt,.md,.docx,.doc,.pdf,.csv,.xlsx,.xls",
    maxSize = 30,
    maxTokens = 64,
    onChange,
    ...otherProps
  } = props;
  console.log('props', props);

  const [uploadedFile, setUploadedFile] = React.useState<UploadFileState | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [previewVisible, setPreviewVisible] = React.useState(false);
  const [previewContent, setPreviewContent] = React.useState<string>('');
  const [previewLoading, setPreviewLoading] = React.useState(false);
  const [uploadType, setUploadType] = React.useState<'upload' | 'link'>('upload');

  // 从文件扩展名获取MIME类型
  const getMimeTypeFromExtension = (filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const mimeTypes = {
      'txt': 'text/plain',
      'md': 'text/markdown',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'doc': 'application/msword',
      'pdf': 'application/pdf',
      'csv': 'text/csv',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    };
    return mimeTypes[extension as keyof typeof mimeTypes] || 'application/octet-stream';
  };

  // 处理文件选择
  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      setUploadedFile({
        file,
        name: file.name,
        type: file.type || getMimeTypeFromExtension(file.name),
        status: 'uploading'
      });

      const appId = getAppId();

      try {
        const result = await uploadFile(file, appId, {
          acceptedTypes: [
            'text/plain',
            'text/markdown',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'application/pdf',
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          ],
          maxSize: maxSize,
          maxTokens: maxTokens,
        }, false);

        if (result.success && result.data) {
          setUploadedFile(prev => ({
            ...prev!,
            status: 'success',
            data: result.data
          }));
          console.log('result.data', result.data);
          onChange?.({ ...result.data, renderType: 'file' });
        } else {
          setUploadedFile(prev => ({
            ...prev!,
            status: 'error',
            error: result.message || '上传失败'
          }));
          onChange?.(null);
        }
      } catch (error) {
        setUploadedFile(prev => ({
          ...prev!,
          status: 'error',
          error: (error as any)?.message || '上传失败'
        }));
        onChange?.(null);
      }
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 删除上传的文件
  const handleDeleteFile = () => {
    setUploadedFile(null);
    onChange?.(null);
  };

  // 打开文件选择对话框
  const openFileSelector = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理预览
  const handlePreview = async () => {
    if (!uploadedFile?.data?.key) return;

    setPreviewVisible(true);
    setPreviewLoading(true);

    try {
      const content = await fetchFileContent(uploadedFile.data.key);
      setPreviewContent(content);
    } catch (error) {
      setPreviewContent('文件内容加载失败，可能文件已过期或不存在');
      message.error('文件内容加载失败');
    } finally {
      setPreviewLoading(false);
    }
  };

  // 获取文件大小
  const getFileSize = (file: UploadFileState) => {
    if (file.data?.size) {
      return formatFileSize(file.data.size);
    }
    return '';
  };

  return (
    <>
      {/* 文件预览区域 */}
      {uploadedFile && (
        <FilePreviewWrapper style={{
          opacity: uploadedFile.status === 'uploading' ? 0.6 : 1,
          cursor: uploadedFile.status === 'uploading' ? 'wait' : 'default'
        }}>
          <FilePreviewHeader>
            {uploadedFile.status === 'uploading' ? '文件上传中...' :
              uploadedFile.status === 'expired' ? '文件已过期' :
                uploadedFile.status === 'error' ? '上传失败' : '已上传文件'}
          </FilePreviewHeader>
          <FilePreviewContent>
            <FileIcon>{getFileIcon(uploadedFile.type, uploadedFile.status)}</FileIcon>
            <FileInfo>
              <FileName>{uploadedFile.name}</FileName>
              <FileDetails>
                {getSimpleFileType(uploadedFile.type)} {getFileSize(uploadedFile)}
                {(uploadedFile.status === 'error' || uploadedFile.status === 'expired') && (
                  <span style={{ color: '#ff4d4f' }}> - {uploadedFile.error}</span>
                )}
              </FileDetails>
            </FileInfo>

            <div style={{ display: 'flex', gap: '8px' }}>
              {uploadedFile.status === 'success' && uploadedFile.data?.key && (
                <PreviewButton onClick={handlePreview}>
                  <EyeOutlined />
                </PreviewButton>
              )}
              <DeleteButton onClick={handleDeleteFile}>
                <DeleteOutlined />
              </DeleteButton>
            </div>
          </FilePreviewContent>
        </FilePreviewWrapper>
      )}
      {/* 上传按钮 */}
      {!uploadedFile && (
        <Button
          style={{ marginLeft: '10px' }}
          icon={<UploadOutlined />}
          onClick={openFileSelector}
        >
          上传文档
        </Button>
      )}

      {/* 隐藏的文件输入框 */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        accept={accept}
        onChange={handleFileSelect}
      />

      {/* 文件预览弹窗 */}
      <FilePreviewModal
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        title={`文件预览: ${uploadedFile?.name || ''}`}
        loading={previewLoading}
        content={previewContent}
      />
    </>
  );
}

export function UploadWithPopo(props: ILFormUploadProps) {
  const [uploadType, setUploadType] = React.useState<'upload' | 'link'>('upload');

  return <Flex>
    <Select style={{ width: 120 }} value={uploadType} onChange={setUploadType}>
      <Select.Option value="upload">本地文档</Select.Option>
      <Select.Option value="link">POPO文档</Select.Option>
    </Select>
    {uploadType === 'upload' && (
      <FormUploadFile {...props} />
    )}
    {uploadType === 'link' && (
      <PopoDocInput {...props} />
    )}
  </Flex>

}

export function LFormUploadWithPopo(props: ILFormUploadProps) {
  const {
    name,
    label,
    rules,
    ...otherProps
  } = props;

  return (
    <div>
      <Form.Item name={name} label={label} rules={rules} {...otherProps}>
        <UploadWithPopo {...otherProps}/>
      </Form.Item>
    </div>
  );
}


export default LFormUploadWithPopo;