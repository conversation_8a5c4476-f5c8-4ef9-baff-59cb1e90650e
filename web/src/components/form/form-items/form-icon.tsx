// @ts-nocheck
import { Form, Input, InputProps } from 'antd';
import styled from 'styled-components';

import { AvatarIcon } from '@/pages/app/workflow/react-node/base-node';

interface ILFormIconProps extends InputProps {
  name: string;
  label: string;
  rules?: any[];
}

const Container = styled.div`
  display: flex;
  flex-direction: column;

  .avatar-icon {
    display: flex;
    width: 20px;
    align-items: center;
    justify-content: center;
    height: 30px;
  }
`;

const Icon = styled(AvatarIcon)`
  display: flex;
`;

const CustomInput = (props) => {
  const { value, ...otherProps } = props;
  return (
    <Container>
      <Input value={value} {...otherProps} />
      <Icon icon={value}></Icon>
    </Container>
  );
};

export function LFormIcon(props: ILFormIconProps) {
  const { name, label, rules, ...otherProps } = props;
  return (
    <Form.Item name={name} label={label} rules={rules} {...otherProps}>
      <CustomInput {...otherProps} />
    </Form.Item>
  );
}
