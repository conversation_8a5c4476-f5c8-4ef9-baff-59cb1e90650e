// @ts-nocheck
import { Form, DatePicker, DatePickerProps } from 'antd';

interface ILFormDatePickerProps extends DatePickerProps {
  name: string;
  label: string;
  rules?: any[];
}

export function LFormDatePicker(props: ILFormDatePickerProps) {
  const { name, label, rules, ...otherProps } = props;
  return (
    <Form.Item
      name={name}
      label={label}
      rules={rules}
      {...otherProps}
    >
      <DatePicker {...otherProps} showTime/>
    </Form.Item>
  );
}
