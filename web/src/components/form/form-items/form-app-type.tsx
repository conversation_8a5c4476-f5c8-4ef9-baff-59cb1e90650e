import { useAdmin } from '@/hooks/useAdmin';
import { AppTypeCnMap, IAppType } from '@/interface';
import { Form, Radio, RadioChangeEvent } from 'antd';
import { RadioGroupProps } from 'antd/lib';

interface ILFormAppTypeProps extends Omit<RadioGroupProps, 'onChange'> {}

interface IAppTypeSelect {
  value?: string;
  onChange?: (value: string) => void;
}

export function LFormAppType(props: ILFormAppTypeProps) {
  const { ...others } = props;

  return (
    <Form.Item name="type" label="应用类型" >
      <AppTypeSelect {...others} />
    </Form.Item>
  );
}

function AppTypeSelect(props: IAppTypeSelect) {
  const { value, onChange, ...otherProps } = props;
  const isAdmin = useAdmin('system');
  const appTypes = [
    IAppType.AgentCompletion,
    IAppType.AgentConversation,
    IAppType.Workflow,
    IAppType.AgentWorkflow,
    // 先注释
    IAppType.VirtualHuman,
  ];

  console.log('isAdmin', isAdmin);

  return (
    <Radio.Group
      disabled={!isAdmin}
      value={value}
      onChange={(e: RadioChangeEvent) => onChange && onChange(e.target.value)}
      {...otherProps}
    >
      {appTypes.map((type) => (
        <Radio.Button key={type} value={type}>
          {AppTypeCnMap[type]}
        </Radio.Button>
      ))}
    </Radio.Group>
  );
}
