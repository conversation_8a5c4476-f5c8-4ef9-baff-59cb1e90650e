// @ts-nocheck
import { Checkbox, CheckboxProps, Form } from 'antd';

interface ILFormCheckboxProps extends CheckboxProps {
  name: string;
  label: string;
  rules?: any[];
}

export function LFormCheckBox(props: ILFormCheckboxProps) {
  const { name, label, rules, options, ...otherProps } = props;
  return (
    <Form.Item name={name} label={label} rules={rules} {...otherProps}>
      <Checkbox.Group {...otherProps} options={options || []}/>
    </Form.Item>
  );
}
