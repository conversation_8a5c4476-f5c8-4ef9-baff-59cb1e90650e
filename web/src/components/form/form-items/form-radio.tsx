// @ts-nocheck
import { Form, Radio, RadioGroupProps } from 'antd';

interface ILFormRadioGroupProps extends RadioGroupProps {
  name: string;
  label: string;
  rules?: any[];
}

export function LFormRadioGroup(props: ILFormRadioGroupProps) {
  const { name, label, rules, options, ...otherProps } = props;
  return (
    <Form.Item name={name} label={label} rules={rules} {...otherProps}>
      <Radio.Group block {...otherProps} options={options || []} />
    </Form.Item>
  );
}
