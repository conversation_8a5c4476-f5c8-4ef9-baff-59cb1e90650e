import { Form } from 'antd';
import ImageUploader, { UploadImageProps } from '@/pages/app/workflow/react-node/imageUploader';

interface ILFormUploadProps extends UploadImageProps {
  name: string;
  label: string;
  rules?: any[];
}

export function LFormImageUploader(props: ILFormUploadProps) {
  const {
    name,
    label,
    rules,
    ...otherProps
  } = props;
  return (
    <div>
      <Form.Item name={name} label={label} rules={rules} {...otherProps}>
        <ImageUploader {...otherProps} />
      </Form.Item>
    </div>
  );
}


export default LFormImageUploader;