// @ts-nocheck
import { Col, Form, InputNumber, InputNumberProps, Row, Slider } from 'antd';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';

interface ILFormInputProps extends Omit<InputNumberProps, 'onChange' | 'defaultValue'> {
  name: string;
  label: string;
  rules?: any[];
  debounceDelay?: number;
  defaultValue?: number;
  min?: number;
  max?: number;
  step?: number;
}

export function LFormSliderInput(props: ILFormInputProps) {
  const { name, label, rules, ...otherProps } = props;
  return (
    <Form.Item name={name} label={label} rules={rules}>
      <SliderInput {...otherProps} />
    </Form.Item>
  );
}

interface ISliderInput
  extends Omit<InputNumberProps, 'value' | 'onChange' | 'defaultValue'> {
  value?: number;
  onChange?: (value: number) => void;
  debounceDelay?: number;
  defaultValue?: number;
  min?: number;
  max?: number;
  step?: number;
}

function SliderInput(props: ISliderInput) {
  const { value, onChange, debounceDelay = 200, ...otherProps } = props;
  const [editValue, setEditValue] = useState<number>(
    value || otherProps.defaultValue || 0,
  );

  const debounceOnChange = useMemo(
    () =>
      debounce(onChange || (() => 0), debounceDelay, { leading: false, trailing: true }),
    [],
  );

  useEffect(() => {
    debounceOnChange(editValue);
  }, [editValue]);

  useEffect(() => {
    if (value === undefined) {
      return;
    }
    setEditValue(value);
  }, [value]);

  return (
    <Row>
      <Col span={12}>
        <Slider
          range={false}
          min={0}
          max={1}
          step={0.01}
          {...otherProps}
          value={editValue}
          onChange={setEditValue}
        />
      </Col>
      <Col span={4}>
        <InputNumber
          min={0}
          max={1}
          step={0.1}
          {...otherProps}
          style={{ margin: '0 16px' }}
          value={editValue}
          onChange={(v) => setEditValue((v as number) || 0)}
        />
      </Col>
    </Row>
  );
}
