import { Form, Input, Select } from 'antd';

interface ILFormSelectProps {
  name: string;
  label: string;
  rules?: any[];
  [key: string]: any;
  options: {
    id: string | number;
    name: string | number;
  }[];
}

export function LFormSelect(props: ILFormSelectProps) {
  const { name, label, rules, options, ...otherProps } = props;
  return (
    <Form.Item name={name} label={label} rules={rules} {...otherProps}>
      <Select {...otherProps}>
        {(options || []).map((item) => (
          <Select.Option key={item.id} value={item.id}>
            {item.name}
          </Select.Option>
        ))}
      </Select>
    </Form.Item>
  );
}
