// @ts-nocheck
import { Form } from 'antd';
import { FormProps } from 'antd/lib';

export function LForm(props: FormProps) {
  const { children, ...otherProps } = props;
  return (
    <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} {...otherProps}>
      {children}
    </Form>
  );
}

export * from './form-items/form-app-type';
export * from './form-items/form-input';
export * from './form-items/form-select';
export * from './form-items/form-item';
export * from './form-items/form-checkbox';
export * from './form-items/form-switch';
export * from './form-items/form-icon';
export * from './form-items/form-radio';
