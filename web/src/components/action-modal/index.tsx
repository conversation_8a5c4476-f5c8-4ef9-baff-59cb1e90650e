import { Button, Modal } from 'antd';
import React, { useState } from 'react';

const ActionModal = (props) => {
  const { action, title, children, onOk, onCancel } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    if (onOk) {
      onOk().then(() => {
        setIsModalOpen(false);
      })
    } else {
      setIsModalOpen(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onOk().then(() => {
        setIsModalOpen(false);
      })
    } else {
      setIsModalOpen(false);
    }
  };

  return (
    <>
      {action ? React.cloneElement(action, { onClick: showModal }) :
        <a onClick={showModal}>
          {title}
        </a>
      }
      <Modal title={title} destroyOnClose open={isModalOpen} onOk={handleOk} onCancel={handleCancel} style={{ top: 20, right: 100 }}>
        {children}
      </Modal>
    </>
  );
};

export default ActionModal;