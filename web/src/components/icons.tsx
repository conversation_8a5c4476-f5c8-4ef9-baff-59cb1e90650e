import { ReactNode } from "react";
import { Flex } from "antd";

import { createFromIconfontCN } from '@ant-design/icons';

export const IconFont = createFromIconfontCN({
  scriptUrl: '//at.alicdn.com/t/c/font_4280668_xv56i015nze.js'
});

export const POPOIcon = () => <IconFont type="icon-popo"></IconFont>

export const HelpIcon = () => <IconFont type="icon-help"></IconFont>

export const MagicIcon = (props) => <IconFont type="icon-magic" {...props}></IconFont>

export const BrainIcon = (props) => <IconFont type="icon-brain" {...props}></IconFont>

export const ClearIcon = (props) => <IconFont type="icon-clear" {...props}></IconFont>

export const EyeIcon = (props) => <IconFont type="icon-eye" {...props}></IconFont>

export const SettingIcon = (props) => <IconFont type="icon-setting" {...props}></IconFont>

export const VariableIcon = (props) => <IconFont type="icon-var" {...props}></IconFont>

export const MdIcon = props => <IconFont type="icon-file-markdown" {...props} />

export const PdfIcon = props => <IconFont type="icon-pdf" {...props} />

export const DocIcon = props => <IconFont type="icon-doc" {...props} />

export const TxtIcon = props => <IconFont type="icon-txt" {...props} />

export const TextIcon = props => <IconFont type="icon-wenjianleixing-wenben" {...props} />

export const ExcelIcon = props => <IconFont type="icon-excel" {...props} />

export const ImgIcon = props => <IconFont type="icon-wenjianleixing-tupian" {...props} />

export const TtsIcon = props => <IconFont type="icon-yuyin" {...props} />

export const CioIcon = props => <IconFont type="icon-cio" {...props} />

export const TemplateIcon = props => <IconFont type="icon-template0" {...props} />

export const Template1Icon = props => <IconFont type="icon-template1" {...props} />

export const JsonIcon = props => <IconFont type="icon-json" {...props} />

export const PythonIcon = props => <IconFont type="icon-python" {...props} />

export const JsIcon = props => <IconFont type="icon-js" {...props} />

export const Eye1Icon = props => <IconFont type="icon-eye1" {...props} />

export const ChatIcon = props => <IconFont type="icon-chat" {...props} />

export const Httpcon = props => <IconFont type="icon-Http" {...props} />

export const Uploadpcon = props => <IconFont type="icon-upload" {...props} />

export const Transdpcon = props => <IconFont type="icon-transfer" {...props} />

export const Knowledgedpcon = props => <IconFont type="icon-zhishiku07" {...props} />


interface IconButtonProps {
  icon: ReactNode;
  label: string;
  fontSize?: number;
  onClick?: () => void;
}

export const IconButton = ({
  icon,
  label,
  fontSize = 12,
  onClick
}: IconButtonProps) => {
  return (
    <span onClick={onClick}>
      <Flex
        align="center"
        gap={2}
        style={{ cursor: 'pointer' }}
      >
        {icon}
        <span style={{ fontSize }}>{label}</span>
      </Flex>
    </span>
  );
};