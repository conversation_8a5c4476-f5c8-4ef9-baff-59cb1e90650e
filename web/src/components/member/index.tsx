import { MemberOfResource, ResIdMap } from '@interface/member';
import { Card } from 'antd';
import { useEffect, useState } from 'react';

import { useQuery } from '@/hooks/useQuery';

import { MidContainer } from '../layout-container';
import ExistMember from './components/exist-members';
import InviteMember from './components/invite-members';
import MemberItem from './components/member-item';
import { EventBus } from './interface';

function MemberPage(props: { resType: MemberOfResource }) {
  const { parsedQuery } = useQuery();
  const [resId, setResId] = useState<string>('');

  useEffect(() => {
    if (parsedQuery) {
      setResId(parsedQuery[ResIdMap[props.resType]] as string);
    }
  }, [parsedQuery, props.resType]);

  const [shouldPull, setShouldPull] = useState(false);

  const eventBus: EventBus = {
    shouldPull,
    setShouldPull,
  };

  useEffect(() => {
    setShouldPull(true);
  }, [props.resType, resId]);

  return (
    <MidContainer>
      <Card>
        <InviteMember {...props} {...eventBus} resId={resId} />
        <ExistMember {...props} {...eventBus} resId={resId} />
      </Card>
    </MidContainer>
  );
}

export { ExistMember, InviteMember, MemberItem };

export default MemberPage;
