// @ts-nocheck
import { MemberApi } from '@api/member';
import { TextTip } from '@components/text-tip';
import { RoleType } from '@interface/member';
import { Button, message, Select, Tag } from 'antd';
import { difference } from 'lodash';
import React, { useState } from 'react';
import styled from 'styled-components';

import { UserModel } from '@/interface';

import { EventBus, MemberPageProp } from '../interface';
import MemberItem from './member-item';

function MemberSelect({ resType, resId, setShouldPull }: MemberPageProp & EventBus) {
  const [selectUserIds, setSelectUserIds] = useState<string[]>([]);
  const [selectUser, setSelectUser] = useState<UserModel[]>([]);
  const [searchResult, setSearchResult] = useState<UserModel[]>([]);

  const onSearch = (v: string) => {
    MemberApi.search({ name: v }).then(setSearchResult);
  };

  const onSelect = (v: string[]) => {
    const newSelect = difference(v, selectUserIds);
    setSelectUserIds([...selectUserIds, ...newSelect]);
    setSelectUser([
      ...selectUser,
      ...searchResult.filter((s) => newSelect.includes(s.id)),
    ]);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const tagRender: any = ({ value, closable }: { value: string; closable: boolean }) => {
    const user = selectUser.find((u) => u.id === value);
    const onClose = () => {
      setSelectUserIds(selectUserIds.filter((e) => e !== value));
      setSelectUser(selectUser.filter((u) => u.id !== value));
    };
    return (
      <Tag closable={closable} onClose={onClose}>
        {user?.fullname || user}
      </Tag>
    );
  };


  return (
      <Select
        style={{ width: '100%' }}
        mode="multiple"
        allowClear
        value={selectUserIds}
        tagRender={tagRender}
        onFocus={() => onSearch('')}
        onChange={onSelect}
        onSearch={onSearch}
        filterOption={(input, option) => {
          return (
            searchResult.findIndex((it) => {
              return (
                it.email.includes(input) ||
                it.name.includes(input) ||
                it.fullname.includes(input)
              );
            }) !== -1
          );
        }}
      >
        {searchResult.map((u) => {
          return (
            <Select.Option key={u.id} value={u.id}>
              <MemberItem member={u} role={null} canEdit={false} />
            </Select.Option>
          );
        })}
      </Select>
  );
}

export default MemberSelect;
