import { Member<PERSON>pi } from '@api/member';
import { useGlobalState } from '@hooks/useGlobalState';
import { useQuery } from '@hooks/useQuery';
import { ListMemberOption, Member, MemberOfResource, RoleType } from '@interface/member';
import { Input, Pagination, Select, Tag } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

import { EventBus, MemberPageProp } from '../interface';
import MemberItem, { renderRoleOptions } from './member-item';

function ExistMember({
  shouldPull,
  setShouldPull,
  resType,
  resId,
}: MemberPageProp & EventBus) {
  const [data, setData] = useState<Member[]>([]);
  const [filterRole, setFilterRole] = useState<'all' | RoleType>('all');
  const [filterSearch, setFilterSearch] = useState('');
  const [totalCount, setTotalCount] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;
  const { parsedQuery } = useQuery();
  const { globalState } = useGlobalState();
  const { app } = globalState;

  useEffect(() => {
    const options: ListMemberOption = {
      pageSize,
      pageNumber: currentPage,
    };
    if (filterSearch !== null) {
      options.name = filterSearch;
    }
    if (filterRole !== 'all') {
      options.role = filterRole;
    }

    if (shouldPull) {
      MemberApi.listMember(resType, resId, options).then((d) => {
        setData(d.items);
        setTotalCount(d.total);
        setShouldPull(false);
      });
    }
  }, [
    shouldPull,
    setShouldPull,
    resType,
    resId,
    currentPage,
    filterRole,
    filterSearch,
    parsedQuery,
  ]);

  useEffect(() => {
    setShouldPull(true);
  }, [setShouldPull, resType, resId, currentPage, filterRole, filterSearch]);

  function onSearchChange(searchText: string) {
    setFilterSearch(searchText);
    setCurrentPage(1);
  }

  function onSearchRoleChange(role: 'all' | RoleType) {
    setFilterRole(role);
    setCurrentPage(1);
  }

  function getExtraNode(data: Member) {
    if (resType !== MemberOfResource.App || data.resourceType !== resType) {
      return null;
    }
    if (data.memberID === app?.createdBy?.id) {
      return (
        <Tag color="#2db7f5" style={{ marginLeft: '4px' }}>
          创建者
        </Tag>
      );
    }
    return null;
  }
  return (
    <div style={{ marginBottom: 50 }}>
      <ExistHeader>
        <div>
          <span>已拥有访问权限的用户</span>
          {/* {resType !== MemberOfResource.Workspace && (
            <span className={styles.inheritSwitch}>
              <Switch checked={showInherit} onChange={onShowInheritChange} />
              &nbsp;&nbsp;
              <TextTip
                text="展示父级角色"
                tip="父级角色拥有本级角色权限，如 业务组开发者 具有 业务组下全部应用的开发者权限"
              />
            </span>
          )} */}
        </div>
        <div className="filterBox">
          <Select
            value={filterRole}
            onChange={onSearchRoleChange}
            className="filterSelector"
          >
            <Select.Option key="all" value="all">
              所有角色
            </Select.Option>
            {renderRoleOptions(resType, false)}
          </Select>
          <Input.Search placeholder="请输入查找用户名" onSearch={onSearchChange} />
        </div>
      </ExistHeader>
      <div>
        {data.map((it) => {
          return (
            <MemberItem
              key={it.resourceType + it.memberID}
              {...it}
              canEdit={it.resourceType === resType}
              editScope={resType}
              editScopeId={resId}
              setShouldPull={setShouldPull}
              extra={getExtraNode(it)}
            />
          );
        })}
      </div>
      <Pagination
        hideOnSinglePage
        style={{ textAlign: 'center', marginTop: '10px' }}
        current={currentPage}
        onChange={setCurrentPage}
        pageSize={pageSize}
        total={totalCount}
      />
    </div>
  );
}

export default ExistMember;

const ExistHeader = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: #eee;
  align-items: center;
  padding: 10px 10px;
  margin-top: 10px;

  .filterBox {
    display: flex;
    flex-direction: row;
  }
  .filterSelector {
    width: 150px;
    margin-right: 10px;
  }

  .inheritSwitch {
    margin-left: 20px;
    vertical-align: middle;
    display: inline-flex;
  }
`;
