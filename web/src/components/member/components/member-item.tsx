import React, { useState } from 'react';
import { Button, message, Select, Popconfirm, Tag } from 'antd';
import {
  RoleType,
  MemberOfResource,
  ResCnMap,
  RoleTypeCnMap,
  Member,
} from '@interface/member';
import { UserModel } from '@interface/user';
import UserAvatar from '@components/avatar';
import { MemberApi } from '@api/member';
import styles from './member-item.module.scss';
import styled from 'styled-components';

interface MemberItemProps {
  role: RoleType | null;
  canEdit: boolean;
  editScope?: MemberOfResource;
  editScopeId?: string;
  scope?: MemberOfResource;
  setShouldPull?: CallableFunction;
  extra?: React.ReactNode;
  member: Omit<UserModel, 'isAdmin'>;
}

export function renderRoleOptions(
  resType?: MemberOfResource | undefined,
  showPrefix = true,
) {
  const prefix = showPrefix && resType ? ResCnMap[resType] : '';
  return (
    <>
      <Select.Option key={RoleType.ADMIN} value={RoleType.ADMIN}>
        {prefix}管理员
      </Select.Option>
      <Select.Option key={RoleType.DEVELOPER} value={RoleType.DEVELOPER}>
        {prefix}开发者
      </Select.Option>
      <Select.Option key={RoleType.VIEWER} value={RoleType.VIEWER}>
        {prefix}查看者
      </Select.Option>
    </>
  );
}

const MemberItem: React.FC<MemberItemProps> = (props) => {
  const [role, setRole] = useState(props.role);
  const { editScope, editScopeId, extra } = props;

  const onChangeRole = (newRole: RoleType) => {
    if (role !== newRole && editScope && editScopeId) {
      MemberApi.addMember([props.member.id], newRole, editScope, editScopeId).then(() => {
        setRole(newRole);
        message.success('操作成功');
      });
    }
  };

  const onRemove = () => {
    if (editScope && editScopeId) {
      MemberApi.removeMember(props.member.id, editScope!, editScopeId!).then((_) => {
        message.success('操作成功');
        if (props.setShouldPull) {
          props.setShouldPull(true);
        }
      });
    }
  };
  return (
    <StyledMemberItem>
      <div>
        <UserAvatar fullname={props.member.fullname || ''} />
        <span>
          <span className="fullname">{props.member.fullname}</span>
          <span className="nickname">@{props.member.name}</span>
          {extra}
        </span>
      </div>
      {props.canEdit ? (
        <span>
          <Select
            value={role!}
            onChange={onChangeRole}
            style={{ width: '130px', marginRight: '10px' }}
          >
            {renderRoleOptions(editScope)}
          </Select>
          <Popconfirm
            title="确定将此成员移出?"
            onConfirm={onRemove}
            okText="确认"
            cancelText="取消"
          >
            <Button type="primary" danger size="small">
              移出
            </Button>
          </Popconfirm>
        </span>
      ) : (
        <span className="role" style={{ visibility: props.role ? 'visible' : 'hidden' }}>
          {ResCnMap[props.editScope!] + RoleTypeCnMap[props.role!]}
        </span>
      )}
    </StyledMemberItem>
  );
};

export default MemberItem;

const StyledMemberItem = styled.div`
  padding: 10px 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid;
  border-bottom-color: #eee;

  .fullname {
    padding-left: 10px;
    font-weight: 500;
  }

  .role {
    display: inline-block;
    color: #707070;
    font-size: 12px;
    padding: 0 7px;
    border: 1px solid #e5e5e5;
    border-radius: 100px;
    font-weight: 400;
    height: 20px;
    line-height: 20px;
  }

  .cursor {
    cursor: pointer;
  }
`;
