// @ts-nocheck
import { MemberApi } from '@api/member';
import { TextTip } from '@components/text-tip';
import { RoleType } from '@interface/member';
import { Button, message, Select, Tag } from 'antd';
import { difference } from 'lodash';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';

import { UserModel } from '@/interface';

import { EventBus, MemberPageProp } from '../interface';
import MemberItem, { renderRoleOptions } from './member-item';

function InviteMember({ resType, resId, setShouldPull }: MemberPageProp & EventBus) {
  const [selectUserIds, setSelectUserIds] = useState<string[]>([]);
  const [selectUser, setSelectUser] = useState<UserModel[]>([]);
  const [role, setRole] = useState(RoleType.VIEWER);
  const [searchResult, setSearchResult] = useState<UserModel[]>([]);
  // 从query中获取invite 
  useEffect(() => {
    const invite = new URLSearchParams(window.location.search).get('invite');
    if (invite) {
      setRole(RoleType.DEVELOPER);
      MemberApi.search({ name: invite }).then(users => {
        if (users && users.length > 0) {
          // 找到用户后，直接设置到 selectUser 和 selectUserIds
          setSelectUser(users);
          setSelectUserIds(users.map(user => user.id));
        }
      });
    }
  }, []);

  const onSearch = (v: string) => {
    console.log('search', v);
    MemberApi.search({ name: v }).then(setSearchResult);
  };

  const onSelect = (v: string[]) => {
    const newSelect = difference(v, selectUserIds);
    setSelectUserIds([...selectUserIds, ...newSelect]);
    setSelectUser([
      ...selectUser,
      ...searchResult.filter((s) => newSelect.includes(s.id)),
    ]);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const tagRender: any = ({ value, closable }: { value: string; closable: boolean }) => {
    const user = selectUser.find((u) => u.id === value);
    const onClose = () => {
      setSelectUserIds(selectUserIds.filter((e) => e !== value));
      setSelectUser(selectUser.filter((u) => u.id !== value));
    };
    return (
      <Tag closable={closable} onClose={onClose}>
        {user?.fullname || user}
      </Tag>
    );
  };

  const onInvite = () => {
    if (!selectUserIds.length) {
      message.warning('请选择用户');
      return;
    }
    MemberApi.addMember(selectUserIds, role, resType, resId).then(() => {
      setSelectUserIds([]);
      setSelectUser([]);
      setShouldPull(true);
      message.success('添加成功');
    });
  };

  return (
    <StyledInvite>
      <div className="label">被邀请人 姓名 或 邮箱</div>
      <Select
        style={{ width: '100%' }}
        mode="multiple"
        allowClear
        value={selectUserIds}
        tagRender={tagRender}
        onFocus={() => onSearch('')}
        onChange={onSelect}
        onSearch={onSearch}
        filterOption={(input, option) => {
          return (
            searchResult.findIndex((it) => {
              return (
                it.email.includes(input) ||
                it.name.includes(input) ||
                it.fullname.includes(input)
              );
            }) !== -1
          );
        }}
      >
        {searchResult.map((u) => {
          return (
            <Select.Option key={u.id} value={u.id}>
              <MemberItem member={u} role={null} canEdit={false} />
            </Select.Option>
          );
        })}
      </Select>
      <div className="label">
        <TextTip
          text="选择成员角色"
          tip={
            <span>
              成员权限赋予应遵循「最小权限原则」详见{' '}
              <a href="" target="_blank" rel="noreferrer">
                文档-成员管理
              </a>
            </span>
          }
        />
      </div>
      <Select value={role} onChange={setRole} style={{ width: '100%' }}>
        {renderRoleOptions(resType)}
      </Select>
      <br />
      <Button type="primary" style={{ marginTop: '10px' }} onClick={onInvite}>
        邀请
      </Button>
    </StyledInvite>
  );
}

export default InviteMember;

const StyledInvite = styled.div`
  border-bottom: 1px #eee solid;
  padding: 8px;
  position: relative;

  .label {
    padding: 10px 0;
    font-size: 16px;
    font-weight: 400;
  }

  .inviteSearch {
    border: 1px solid #eee;
    position: absolute;
    margin-top: 5px;
    z-index: 10;
    height: 200px;
    overflow-y: scroll;
    display: none;
    width: 100%;
    background-color: white;
    padding: 10px;
    box-shadow: 0px 0px 5px #888888;

    &.visible {
      display: block;
    }
  }
`;
