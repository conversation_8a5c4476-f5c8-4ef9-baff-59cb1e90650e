// @ts-nocheck
import type { IProps, ILayoutProps } from './interface'
import { Button } from 'antd'
import React, { useState, useCallback } from 'react'
import { useXflowPrefixCls } from '@antv/xflow-core'
import { WorkspacePanel } from '../base-panel'
import { CollapsePanelBody } from './panel-body'
import { NodePanelHeader } from './panel-header'
import { NodePanelFooter } from './panel-footer'
import { NodePanelCollapseBtn } from './panel-collapse-btn'
import { usePanelLyaoutStyle } from './utils'
import { useCollapsePanelData } from './service'
import { NsCollapsePanelModel } from './interface'
import * as NsNodeCollapsePanel from './interface'
import './style/index.less'
import { PlusOutlined } from '@ant-design/icons'

const CollapsePanelMain: React.FC<IProps> = props => {
  const { headerStyle, bodyStyle, footerStyle } = usePanelLyaoutStyle(
    props as ILayoutProps,
  )
  const { state, onActiveKeyChange, onKeywordChange } = useCollapsePanelData(props)

  return (
    <>
      <NodePanelHeader
        {...props}
        state={state}
        style={headerStyle}
        onKeywordChange={onKeywordChange}
      />
      <CollapsePanelBody
        {...props}
        state={state}
        style={bodyStyle}
        onActiveKeyChange={onActiveKeyChange}
      />
      <NodePanelFooter
        {...props}
        state={state}
        style={footerStyle}
      />
    </>
  )
}

const NodeCollapsePanel: React.FC<IProps> = props => {
  const { position, collapsible, onCollapseChange } = props
  const { width = 200, left } = position
  const prefixClz = useXflowPrefixCls('collapse-panel')

  const [isCollapsed, setIsCollapsed] = useState(false)

  const handleBtnClick = () => {
    setIsCollapsed(!isCollapsed)
    if (onCollapseChange) {
      onCollapseChange(!isCollapsed)
    }
  };

  return (
    <WorkspacePanel
      {...props}
      className={prefixClz}
      position={{ ...position, left: !isCollapsed ? left : -width }}
      style={{ transition: 'left 0.2s', ...props.style }}
    >
      <Button className={`${prefixClz}-plus ${isCollapsed ? 'collapse' : ''}`}icon={<PlusOutlined />} onClick={() => handleBtnClick(false)}></Button>
      <CollapsePanelMain {...props} prefixClz={prefixClz}
        onBack={() => handleBtnClick(true)}
      />
    </WorkspacePanel>
  )
}

export {
  NodeCollapsePanel,
  NsCollapsePanelModel,
  NsNodeCollapsePanel,
  IProps as INodeCollapsePanelProps,
}
