@light-border: ~'1px solid #d9d9d9';
@header-bg: #fff;
@body-bg: #fff;
@list-item-hover: #f5f5f5;

.xflow-collapse-panel {
  border-right: @light-border;

  .disabled {
    position: relative;
    opacity: 0.45;
    &::before {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 999;
      cursor: not-allowed;
      content: '';
    }
  }

  &-node-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    cursor: grab;
  }

  &-header {
    display: flex;
    justify-content: space-evenly;
    background: @header-bg;
    border-bottom: @light-border;
    &-title {
      font-size: 16px;
    }
    &-search {
      width: 100%;
      padding: 0 12px;
    }
  }

  &-plus {
    position: fixed;
    left: 20px;
    top: 80px;
    border-radius: 50%;
    // background: blue;
    // color: #fff;
    font-weight: bold;
    font-size: 15px;
    // ::hover {
    //   background: #27ae60;
    // }
    svg {
      transition: transform .2s;
      transform: rotate(135deg);
    }
    &.collapse {
      svg {
        transform: rotate(0deg);
      }
    }
  }

  &-body {
    padding: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background: @body-bg;
  }

  &-footer {
    display: flex;
    justify-content: space-evenly;
    background: @header-bg;
    border-top: @light-border;
    &-title {
      font-size: 16px;
    }
  }
  .xflow-collapse {
    // 搜索
    &-search-list {
      height: 100%;
      margin: 0;
      padding: 8px 0;
      overflow-y: auto;
      list-style: none;
    }
    &-search-list-item {
      margin: 0;
      padding: 4px 0;
    }

    &-list {
      height: 100%;
      margin: 0;
      padding: 0;
      overflow-y: auto;
      list-style: none;
      &-item {
        margin: 0;
        padding: 0;
        &.close {
          .xflow-collapse-content {
            height: 0;
            transform: scaleY(0);
          }
        }
      }
    }

    &-header {
      display: flex;
      justify-content: space-between;
      padding: 8px 16px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 1.5;
      cursor: pointer;
      transition: all 0.3s;
      user-select: none;
      &:hover {
        background: @list-item-hover;
      }
      &-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        svg {
          transition: all 0.5s;
        }
      }
      &-label {
        flex: 1;
      }
      &-extra {
        width: auto;
      }
    }

    &-content {
      height: 100%;
      height: auto;
      overflow: hidden;
      transform: scaleY(1);
      transition: scale 0.15s ease-in-out;
      &-item {
        padding: 4px 0;
        transition: all 0.5s;
        &:hover {
          background: @list-item-hover;
        }
      }
    }
  }
}
