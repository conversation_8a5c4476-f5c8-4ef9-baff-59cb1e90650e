import React, { useEffect, useState } from 'react';
import { Modal, Button, Form, Input, message, Typography, Divider, Select, Tooltip, Popover } from 'antd';
import { OvermindApi, ApproveCheckResult } from '@/api/overmind';
import styles from './index.module.less';
import { ApolloApi, ApproveDetailItem } from '@/api/apollo';
import { AppApi } from '@/api/app';
import { AppDetailModel } from '@/interface';
import { env } from '@/utils/common';
import dayjs from 'dayjs';
import { CloseOutlined, PlusOutlined, StopOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;

const apolloUrl = (procDefKey, procInstId) => {
  const prefix = env === 'prod' || env === 'pre' ? 'https://music-cms.hz.netease.com' : 'https://cms.qa.igame.163.com'
  return `${prefix}/apollo/subapollo/processes/process-instances-detail?procDefKey=${procDefKey}&procInstId=${procInstId}`
}

interface ApprovalModalProps {
  app: AppDetailModel;
  children: React.ReactNode;
  updateGlobalState: any;
  appName: string;
  force?: boolean;
  settingId?: string;
  channel?: string;
  action: string;
  appType: string;
  setting?: any;
  operator: string;
  onApprove?: (comment: string) => void;
  preCheck?: () => any;
  onCancel?: () => void;
}

let pollingTimer = null;

const ApprovalModal: React.FC<ApprovalModalProps> = ({
  children,
  settingId,
  preCheck,
  setting,
  channel,
  updateGlobalState,
  force,
  app,
  appName,
  action,
  appType,
  operator,
  onApprove,
  onCancel
}) => {
  const approveProc = app?.extInfo?.approve?.approveProc;
  const [visible, setVisible] = useState(false);
  const [extraData, setExtraData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [detail, setApolloDetail] = useState<ApproveDetailItem | null>(null);
  const [approveResult, setApproveResult] = useState<ApproveCheckResult | null>(null);
  const [form] = Form.useForm();

  const checkApproval = async () => {
    try {
      const { result } = await OvermindApi.approveCheck({
        appName: appName || '',
        action,
        appType: appType,
        operator
      });
      setApproveResult(result);
    } catch (error) {
      message.error('审批检查失败，请重试');
      console.error('审批检查失败:', error);
    }
  };
  useEffect(() => {
    checkApproval()
  }, []);

  // 清理轮询定时器
  const clearPollingTimer = () => {
    if (pollingTimer) {
      clearInterval(pollingTimer);
      pollingTimer = null;
    }
  };

  // 开始轮询
  const startPolling = (proc: string) => {
    // 先清理已存在的定时器
    clearPollingTimer();

    const timer = setInterval(async () => {
      const detail = await ApolloApi.approveDetail({
        proc_inst_serials: [proc]
      });

      if (detail && detail.length > 0) {
        setApolloDetail(detail[0]);

        // 如果状态不为0，停止轮询
        if (detail[0].status !== 0) {
          clearPollingTimer();
        }
      }
    }, 5000); // 每10秒轮询一次

    pollingTimer = timer;
  };

  // 修改原有的 getApolloDetail 函数
  async function getApolloDetail(proc) {
    const detail = await ApolloApi.approveDetail({
      proc_inst_serials: [proc]
    });
    console.log('detail', detail);
    if (detail && detail?.length > 0) {
      setApolloDetail(detail[0]);
      // 如果状态为0，开始轮询
      if (detail[0].status === 0) {
        startPolling(proc);
      }
    }
  }

  // 在组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearPollingTimer();
    };
  }, []);

  useEffect(() => {
    if (approveProc) {
      getApolloDetail(approveProc);
    }
  }, [approveProc]);

  async function createApprove(values: any) {
    console.log('values', values, setting)
    const proc = await ApolloApi.createApprove({
      appName,
      operator,
      settingId: setting?.label,
      channel,
      deployReason: values.comment,
      mainApprover: approveResult?.approverNames[0],
      audits: approveResult.approvers,
      priority: values.priority,
      ...(extraData || {})
    })
    // 需要把当前审批的详情绑定到当前应用中
    AppApi.updateApp({
      extInfo: {
        ...app?.extInfo,
        approve: {
          approveProc: proc?.procNo,
          extraData: {
            ...extraData,
            settingId: settingId
          }
        }
      }
    }, app?.id).then(res => {
      message.success('审批已创建');
      updateGlobalState({
        app: {
          ...app,
          extInfo: {
            ...app?.extInfo,
            approve: {
              approveProc: proc?.procNo,
              extraData: {
                ...extraData,
                settingId: settingId
              }
            }
          }
        }
      });
    })
  }
  const handleOk = () => {
    form.validateFields().then(values => {
      if (onApprove) {
        createApprove(values)
      }
      setVisible(false);
      form.resetFields();
    });
  };

  // 修改 handleCancel，关闭弹窗时也清理定时器
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    setVisible(false);
    form.resetFields();
  };

  const handleClick = async () => {
    if (preCheck) {
      const res = await preCheck();
      if (res) {
        if (typeof res === 'object') {
          setExtraData(res);
        }
        setVisible(true);
      }
    } else {
      setVisible(true);
    }
  }

  // 根据审批状态返回对应的按钮文本和类型
  const getButtonProps = (detail: ApproveDetailItem | null, approveResult: ApproveCheckResult | null) => {
    if (approveResult?.hitForbidden) {
      return {
        text: '禁止发布',
        type: 'primary' as const,
        disabled: true,
        icon: <StopOutlined />
      };
    }

    if (!detail) {
      return {
        text: '创建发布审批',
        type: 'primary' as const,
        icon: <PlusOutlined />
      };
    }

    switch (detail.status) {
      case 0: // 进行中
        return {
          text: '审批详情（进行中）',
          type: 'default' as const,
        };
      case 20: // 撤回
        return {
          text: '重新创建审批',
          type: 'primary' as const
        };
      default:
        return {
          text: '创建发布审批',
          type: 'primary' as const
        };
    }
  };

  // 在组件中使用
  const buttonProps = getButtonProps(detail, approveResult);

  const renderModalContent = () => {
    if (approveResult?.hitForbidden) {
      return <div>
        <Paragraph>禁止发布原因：{approveResult?.forbiddenMessage}</Paragraph>
        <Paragraph>禁止发布时间：{dayjs(approveResult?.forbiddenDetail.startTime).format('YYYY-MM-DD HH:mm:ss')} ~ {dayjs(approveResult?.forbiddenDetail.endTime).format('YYYY-MM-DD HH:mm:ss')}</Paragraph>
      </div>;
    }
    // 如果是审批进行中，显示审批详情
    if (detail?.status === 0) {
      return (
        <div className={styles.approvalDetail}>
          <Form className={styles.approvalForm}>
            <Form.Item label="审批编号" className={styles.formItem}>
              <Text>{detail.procInstSerial}</Text>
            </Form.Item>

            <Form.Item label="审批名称" className={styles.formItem}>
              <Text>{approveResult.detail.title}</Text>
            </Form.Item>

            <Form.Item label="审批状态" className={styles.formItem}>
              <Text>进行中</Text>
            </Form.Item>

            <Form.Item label="审批URL" className={styles.formItem}>
              <a href={apolloUrl(detail.procDefKey, detail.procInstId)} target="_blank" rel="noopener noreferrer">
                {apolloUrl(detail.procDefKey, detail.procInstId)}
              </a>
            </Form.Item>
          </Form>
        </div>
      );
    }


    // 原有的创建审批表单
    return (
      <Form form={form} className={styles.approvalForm} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }} initialValues={{ priority: 20 }} >
        <Form.Item label="审批规则" className={styles.formItem}>
          <Text>{approveResult.detail.title}</Text>
        </Form.Item>

        <Form.Item label="审批描述" className={styles.formItem}>
          <Paragraph>
            {approveResult.detail.description}
          </Paragraph>
          <Paragraph type="secondary">
            (卡点规则同步于 Overmind，如有疑问可咨询朱丽青)
          </Paragraph>
        </Form.Item>

        <Form.Item label="审批经办人" className={styles.formItem} name="approverNames">
          <Text strong>{approveResult.approverNames.join('、')}</Text>
        </Form.Item>

        <Form.Item label="优先级" className={styles.formItem} name="priority">
          <Select options={[{ label: 'P0', value: 10 }, { label: 'P1', value: 20 }, { label: 'P2', value: 30 }]} />
        </Form.Item>

        <Form.Item
          name="comment"
          label="修改说明"
          rules={[{ required: true, message: '请填写修改说明' }]}
        >
          <TextArea
            rows={4}
            placeholder="请简要说明修改内容，便于审批人理解"
          />
        </Form.Item>
      </Form>
    );
  };
  // 命中审批窗口
  if (force || !approveResult || (!approveResult?.hitWindow && !approveResult?.hitForbidden) || detail?.status === 100) {
    return children;
  }
  let title = detail?.status === 0 ? "审批详情" : "创建审批";
  if (approveResult?.hitForbidden) {
    title = "禁止发布";
  }

  return (
    <>
      {approveResult?.hitForbidden ?
        <Popover title="禁止发布" content={renderModalContent()}>
          <Button
            onClick={handleClick}
            loading={loading}
            disabled={buttonProps.disabled}
            icon={buttonProps.icon || null}
          >
            {buttonProps.text}
          </Button>
        </Popover> : <Button
          onClick={handleClick}
          loading={loading}
          disabled={buttonProps.disabled}
          icon={buttonProps.icon || null}
        >
          {buttonProps.text}
        </Button>}
      <Modal
        title={title}
        open={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
        footer={detail?.status === 0 || approveResult?.hitForbidden ? [
          <Button key="close" onClick={handleCancel}>
            关闭
          </Button>
        ] : [
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleOk}>
            创建审批
          </Button>
        ]}
      >
        {renderModalContent()}
      </Modal>
    </>
  );
};

export default ApprovalModal;
