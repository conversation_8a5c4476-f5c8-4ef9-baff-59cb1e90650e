.approvalInfo {
  padding: 16px 0;
  
  h3 {
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
  }
  
  .ruleInfo, .timeInfo, .approverInfo, .commentArea {
    margin-bottom: 16px;
  }
  
  .approverInfo {
    p {
      color: #1890ff;
      font-weight: 500;
    }
  }
  
  .commentArea {
    textarea {
      width: 100%;
      resize: none;
      border-color: #d9d9d9;
      
      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}

.approvalForm {
  .formItem {
    margin-bottom: 16px;
  }
  
  :global {
    .ant-form-item-label {
      font-weight: 500;
    }
    
    .ant-typography-secondary {
      font-size: 12px;
    }
    
    .ant-divider {
      margin: 16px 0;
      
      .ant-divider-inner-text {
        font-size: 14px;
        color: #666;
      }
    }
    
    .ant-form-item-required::before {
      display: none !important;
    }
  }
}

.approvalDetail {
  padding: 8px 0;
} 