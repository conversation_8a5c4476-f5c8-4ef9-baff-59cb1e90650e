import React, { useEffect, useState } from 'react';
import { Modal, Form, InputNumber, Input, message } from 'antd';
import PopoChat from "@music/ct-pc-paopaocaller";
import styled from 'styled-components';
import { useAdmin } from '@/hooks/useAdmin';

interface RateLimitModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (values: { rateLimit: number; reason: string }) => void;
  initialValue: number;
}

const Popo = styled(PopoChat)`
  margin: 0 4px;
  display: inline-block;
  .popo-caller-component {
    display: inline-block;
  }
  .popo-component .popo-icon {
    position: relative;
    top: 2px;
  }
`;

const EXCEED_RATE_LIMIT = 60;
const ADMIN_RATE_LIMIT = 100000;

const RateLimitModal: React.FC<RateLimitModalProps> = ({
  visible,
  onClose,
  onSave,
  initialValue
}) => {
  const [form] = Form.useForm();
  const isAdmin = useAdmin('workspace');
  const [rateLimit, setRateLimit] = useState<number>(initialValue);

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        rateLimit: initialValue,
        reason: ''
      });
      setRateLimit(initialValue);
    }
  }, [visible, initialValue, form]);

  const handleOk = () => {
    form.validateFields().then(values => {
      onSave(values);
    }).catch(info => {
      console.log('验证失败:', info);
    });
  };

  const handleRateLimitChange = (value: number | null) => {
    setRateLimit(value || 0);

    // 当限流值变化时，动态更新原因字段的验证规则
    if (value && value > 50) {
      form.validateFields(['reason']);
    }
  };

  // 判断是否需要显示警告
  const isExceedingLimit = rateLimit > (isAdmin ? ADMIN_RATE_LIMIT : EXCEED_RATE_LIMIT);
  const warningMessage = <div>目前手动能调整范围是1-{EXCEED_RATE_LIMIT}次/分钟，有更多用量需求请加群<Popo ssid="5274049" chatId="5274049" type={1} name="" />反馈</div>;


  return (
    <Modal
      title="限流配置"
      open={visible}
      onOk={handleOk}
      onCancel={onClose}
      okText="确认"
      cancelText="取消"
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="rateLimit"
          label="目标限流（次/分钟）"
          rules={[
            { required: true, message: '请输入限流值' },
            { type: 'number', max: isAdmin ? ADMIN_RATE_LIMIT : EXCEED_RATE_LIMIT, message: `限流最大值为${isAdmin ? ADMIN_RATE_LIMIT : EXCEED_RATE_LIMIT}` }
          ]}
          validateStatus={isExceedingLimit ? 'warning' : undefined}
          help={warningMessage}
        >
          <InputNumber
            min={1}
            max={isAdmin ? ADMIN_RATE_LIMIT : EXCEED_RATE_LIMIT}
            style={{ width: '100%' }}
            onChange={handleRateLimitChange}
          />
        </Form.Item>
        <Form.Item
          name="reason"
          label="调整原因"
          rules={[{
            required: isExceedingLimit,
            message: isExceedingLimit ? `超过${EXCEED_RATE_LIMIT}次/分钟请填写调整原因` : '填写调整原因'
          }]}
        >
          <Input.TextArea rows={4} placeholder="请输入调整限流的原因，平台会定期检查限流配置，如发现未填写原因，或者限流配置不合理，会自动调回默认配置" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default RateLimitModal; 