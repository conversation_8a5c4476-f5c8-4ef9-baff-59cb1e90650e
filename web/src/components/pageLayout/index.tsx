// @ts-nocheck
import { ReactElement, useEffect, useState } from 'react';
import { Link, Outlet, Route, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { Empty, Tag, Skeleton, Button, Popconfirm, Popover, Result, Tooltip, Modal, Input, InputNumber, Form, message, Rate } from 'antd';
import { ResourceAvatar } from '../resource-card';
import { SettingOutlined, LeftOutlined, SwapOutlined, RollbackOutlined, DeleteOutlined, RestOutlined, RedoOutlined, EyeOutlined, EditOutlined, StarOutlined } from '@ant-design/icons';
import { $ } from '@/utils/globalState';
import { AppTypeCnMap, AppTypeColorMap, IAppType, AppTypeAvatarMap } from '@/interface';
import dayjs from 'dayjs';
import { useAdmin } from '@/hooks/useAdmin';
import { getLocalData, saveLocalData } from '@/utils/common';
import { useLocalStorage } from '@/hooks/useStorage';
import { AppApi } from '@/api/app';
import queryString from 'querystring';
import { AvatarIcon } from '@/pages/app/workflow/react-node/base-node';
import NewSortableList from '@/pages/app/components/agent-dev/mods/new-sortable-list'
import { useGlobalState } from '@/hooks/useGlobalState';
import { usePathQuery } from '@/hooks/useQuery';
import { TopMenu } from '../top-menu';
import RateLimitModal from './RateLimitModal';

const { groupId, workspaceId } = queryString.parse(window.location.search.replace('?', ''));
export interface MenuItemConfig {
  path: string;
  tooltip?: string;
  fullPath?: boolean; // 是否全路径
  Icon?: React.ElementType;
  title: React.ReactNode;
  element?: ReactElement;
  defaultSelected?: boolean; // 是否默认选中
  hideMenu?: (search: string) => boolean;
  hide?: boolean; // 是否隐藏
  disabled?: boolean; // 是否禁用
  admin?: string[]; // 权限控制
  matchPaths?: string[]; // 可以被命中的路由
}

interface IPageProps {
  path: string;
  menuConfig: MenuItemConfig[];
  retainQueries?: string[]; // 跳转菜单时保留的查询参数项
}

const OutletBox = styled.div`
  margin-bottom: 20px;
`;

const Title = styled.div`
  height: 20px;
 .ant-tag {
    font-size: 10px;
    margin-left: 10px;
    height: 20px;
    line-height: 18px;
 }
`;

const SettingBox = styled.div`
  color: #fff;
  background: ${({ background }) => background || '#718eab'};
  width: 30px;
  height: 30px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
`;

const AppTitle = styled.div`
  gap: 5px;
  display: flex;
  font-size: 14px;
  font-weight: bold;
  flex-direction: column;
  .sub-title {
    font-weight: normal;
    font-size: 12px;
    color: #999;
  }
  .ant-tag {
    font-size: 10px;
  }
`;

export function AppPageLayout(props: IPageProps) {
  const { globalState, updateGlobalState } = useGlobalState();
  const { pushRetainQuery } = usePathQuery();
  const navigate = useNavigate();
  const { app } = globalState;
  const isStar = !!app?.starred;
  // if (!app) {
  //   return <AppLayout><Spin style={{ height: 500 }}></Spin></AppLayout>;
  // }
  const { name, id, type, extInfo } = app || {};
  const { restrictNumPerMinute, reason } = extInfo || {};
  // const { name, id } = {};
  const [localConfig, preConfig] = useLocalStorage(`app-${id}`);

  const appUpdateTime = dayjs(preConfig?.createdAt || app?.updatedAt);
  const useLocal = (localConfig?.time || 0) > appUpdateTime.valueOf();

  const handleBack = (group) => {
    if ($.state.backPath && !group) {
      navigate($.state.backPath);
    } else {
      pushRetainQuery('/group', ['workspaceId', 'groupId']);
    }
  }

  // 设置document标题为应用名称
  useEffect(() => {
    if (app?.name) {
      document.title = 'LangBase - ' + app?.name;
    } else {
      document.title = 'LangBase';
    }
  }, [app]);

  const handleChange = () => {
    const newType = type === IAppType.AgentWorkflow ? IAppType.AgentConversation : IAppType.AgentWorkflow;
    AppApi.updateApp({
      type: newType
    }, id).then(res => {
      window.location.reload();
    })
  }

  const handlePlayground = (type: IAppType) => {
    window.open(`/preview/${type}?workspaceId=${workspaceId}&groupId=${groupId}&appId=${id}&type=${type}`, '_blank');
  }


  let right = null;
  if (type === IAppType.AgentCompletion || type === IAppType.AgentConversation) {
    right = <>
      <Button type="link" onClick={() => handlePlayground(type)}><EyeOutlined />查看Playground</Button>
    </>
  }

  const reset = () => {
    saveLocalData(`app-${id}`, { time: 0 });
    window.location.reload();
  }

  const [isRateLimitModalVisible, setIsRateLimitModalVisible] = useState(false);
  const showRateLimitModal = () => {
    setIsRateLimitModalVisible(true);
  };

  const handleRateLimitClose = () => {
    setIsRateLimitModalVisible(false);
  };
  const handleRateLimitSave = (values) => {
    // 这里可以添加保存限流配置的API调用
    console.log('提交的限流配置:', values);
    AppApi.updateApp({
      extInfo: {
        restrictNumPerMinute: values.rateLimit,
        reason: values.reason
      }
    }, id).then(res => {
      message.success('限流配置已更新');
      updateGlobalState({
        app: {
          ...app,
          extInfo: { ...extInfo, restrictNumPerMinute: values.rateLimit, reason: values.reason }
        }
      });
    })
    setIsRateLimitModalVisible(false);
  };

  console.log('app', app)

  const handleStar = () => {
    const fn = isStar ? AppApi.unStarApp : AppApi.starApp;
    console.log("appId", app.id)
    fn({ appId: app.id }).then(res => {
      updateGlobalState({
        app: {
          ...app,
            starred: !isStar
          }
        });
    })
  }

  return (
    <AppLayout>
      <TopMenu style={{ paddingLeft: '15%' }} {...props} right={right}
        left={
          name ?
            <GroupTitle style={{ marginTop: -12 }}>
              <LeftOutlined onClick={handleBack} style={{ fontSize: 16, color: '#8c8c8c' }} />
              <AvatarIcon style={{ width: 30, height: 30, borderRadius: 8 }} icon={app?.extInfo?.avatar || AppTypeAvatarMap[app?.type]}></AvatarIcon>
              {/* <ResourceAvatar width={30} height={30} className='avatar' unikey={id} alt={name} foreground={AppTypeColorMap[app.type]} /> */}
              <AppTitle>
                <Title>
                  {name}
                  <Tooltip title={isStar ? '取消收藏' : '收藏'}>
                    <Rate count={1} style={{ marginLeft: 10, fontSize: 16 }} value={isStar ? 1 : 0} onClick={handleStar} />
                  </Tooltip>
                  {/* <StarOutlined style={{ fontSize: 16, color: '#8c8c8c' }} /> */}
                </Title>
                <div className='sub-title'>
                  <Tag>{AppTypeCnMap[app.type]}</Tag>
                  {app.type !== 'virtual-human' && app.type !== IAppType.Workflow && <>
                    <span style={{ fontSize: 12, marginRight: 10 }}>
                      当前限流：
                      <Tooltip title={`接口每分钟最多调用${restrictNumPerMinute || 20}次，点击调整`}>
                        <Tag color="green" style={{ cursor: 'pointer' }} onClick={showRateLimitModal}>{restrictNumPerMinute || 20}次/分钟</Tag>
                      </Tooltip>
                    </span>
                  </>}
                  {(app.type === IAppType.Workflow || app.type === IAppType.AgentWorkflow) && <>
                    <span style={{ fontSize: 12 }}>
                      当前版本：
                      {useLocal ?
                        <span>
                          <Tag color="blue">本地草稿</Tag>
                          <span style={{ fontSize: 12 }}>
                            {dayjs(localConfig?.time || 0).format('YYYY-MM-DD HH:mm:ss')}
                          </span>
                          <Tooltip title="删除本地草稿，重置到最新的一次提交">
                            <RedoOutlined onClick={reset} style={{ fontSize: 12, color: '#1677ff', marginLeft: 5, }} />
                          </Tooltip>
                        </span>
                        : <span>
                          <Tag color="orange">服务器</Tag>
                          <span style={{ fontSize: 12 }}>
                            {appUpdateTime.format('YYYY-MM-DD HH:mm:ss')}
                          </span>
                        </span>
                      }
                    </span>
                  </>}
                </div>
              </AppTitle>
            </GroupTitle>
            : <GroupTitle>
              <Skeleton.Input active width={30} />
            </GroupTitle>
        } />
      <Main>
        {name ?
          <>
            <Outlet />
            <RateLimitModal
              visible={isRateLimitModalVisible}
              onClose={handleRateLimitClose}
              onSave={handleRateLimitSave}
              initialValue={restrictNumPerMinute || 20}
            />
          </>
          : <Skeleton.Input active style={{ width: '100%', height: '100%' }} />
        }
      </Main>
    </AppLayout>
  );
}

function WorkspaceGroupLayout(props: IPageProps) {
  const { globalState } = useGlobalState();
  const { pushRetainQuery } = usePathQuery();
  const { path } = props;
  const isAdmin = useAdmin('group');
  const type = path.split('-')[0];
  if (!globalState[type]) {
    return <Empty></Empty>;
  }
  document.title = 'LangBase';
  console.log('isAdmin', isAdmin);

  const { id, name } = globalState[type];

  const avatarMap = {
    // group: <ResourceAvatar width={30} height={30} className='avatar' unikey={id} alt={name} foreground={[232, 176, 127, 255]} />,
    group: <span></span>,
    workspace: <SettingBox><SettingOutlined style={{ fontSize: 20 }}></SettingOutlined></SettingBox>,
    'group-setting': <SettingBox background="#e8b07f"><SettingOutlined style={{ fontSize: 20 }}></SettingOutlined></SettingBox>
  }

  const handleSetting = () => {
    pushRetainQuery('/group-setting', ['workspaceId', 'groupId']);
  }
  // 要不是workspace 要不是 group[-xxx]

  const handleBack = () => {
    if (path === 'workspace') {
      pushRetainQuery('/', ['workspaceId']);
    }
    if (path === 'group-setting') {
      pushRetainQuery('/group', ['workspaceId', 'groupId']);
    }
  }

  return (
    <PageLayout>
      <TopMenu style={{ paddingLeft: '15%' }} {...props} left={
        name ?
          <GroupTitle>
            {path !== 'group' ? <LeftOutlined onClick={handleBack} style={{ fontSize: 16, color: '#8c8c8c' }} /> : null}
            {avatarMap[path]}
            {name}
            {path === 'group' ? (isAdmin && <SettingOutlined onClick={handleSetting} style={{ fontSize: 16, color: '#999' }}></SettingOutlined>)
              : ' 设置'
            }
          </GroupTitle>
          : <GroupTitle>暂无组信息</GroupTitle>
      } />
      <Main>
        <>
          {path === 'group' || isAdmin ? <Outlet /> : <Result status="403" title="无权限" subTitle="对不起，您暂时无权限查看." />}
        </>
      </Main>
    </PageLayout>
  );
}

export function createMenuPage(props: IPageProps) {
  const { path, menuConfig } = props;
  let ResourceLayout = Empty;
  if (layoutMap[path]) {
    ResourceLayout = layoutMap[path];
  }
  return (
    <Route path={path} element={<ResourceLayout {...props} />}>
      <Route index element={<NavToDefaultPage {...props} />} />
      {menuConfig?.map((menu) => {
        return (
          <Route key={menu.path} path={`/${path}/${menu.path}`} element={menu.element} />
        );
      })}
      <Route path="*" element={<NavToDefaultPage {...props} />} />
    </Route>
  );
}

function NavToDefaultPage(props: IPageProps) {
  const { path, menuConfig, retainQueries = [] } = props;
  const { pushRetainQuery } = usePathQuery();
  const defaultNavigate = menuConfig.find((it) => it.defaultSelected) || menuConfig[0];
  const defaultPath = `/${path}/${defaultNavigate?.path}`;

  useEffect(() => {
    pushRetainQuery(defaultPath, retainQueries, false, true);
  }, []);
  return <></>;
}

const layoutMap = {
  'app': AppPageLayout,
  'group': WorkspaceGroupLayout,
  'workspace': WorkspaceGroupLayout,
  'group-setting': WorkspaceGroupLayout
};


const PageLayout = styled.div`
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
`;

const AppLayout = styled(PageLayout)`
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  
  background: #f5f8fc;
`

const Main = styled.div`
    padding: 20px;
    height: calc(100vh - 90px);
    overflow: hidden;
    overflow-y: auto;
    user-select: text;  
    .ant-skeleton {
      height: 100%;
      width: 100%;
    }
`;

const GroupTitle = styled.div`
      user-select: none;  
      display: flex;
      align-items: center;
      min-width: 160px;
      justify-content: start;
      gap: 10px;
      font-size: 18px;
      `;