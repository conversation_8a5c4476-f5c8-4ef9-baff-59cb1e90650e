/* eslint-disable jsx-a11y/iframe-has-title */
import { Button, Modal } from 'antd';
import React, { useState } from 'react';

export default function WorkflowPreviewBtn(props) {
  const { trigger, appId, text, configId } = props;
  const [open, setOpen] = useState(false);
  let src = `${origin}/preview/workflow`;
  if (configId) {
    src += `?configId=${configId}`;
  }
  if (appId) {
    src += `?appId=${appId}`;
  }

  return (
    <>
      {trigger || (
        <Button type="link" onClick={() => setOpen(true)}>
          {text || '查看流水线详情'}
        </Button>
      )}
      <Modal open={open} width={'80vw'} title="workflow" onCancel={() => setOpen(false)} onOk={() => setOpen(false)}>
        <div style={{ height: '800px', maxHeight: '60vh' }}>
          <iframe
            src={src}
            style={{ width: '100%', height: '100%', border: 'none' }}
          ></iframe>
        </div>
      </Modal>
    </>
  );
}
