import 'highlight.js/styles/github-dark.min.css';

import { CopyOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import styled from 'styled-components';
import { common } from 'lowlight';
import Lowlight from 'react-lowlight';

import { copyToClipboard } from '@/utils/common';
import rehypeRaw from 'rehype-raw';
import {Root, createRoot} from 'react-dom/client';
import { CodeSnippet } from './code-snippet';


const languages = Object.keys(common);

languages.forEach((key) => {
  Lowlight.registerLanguage(key, common[key]);
});

const MarkdownStyle = styled(ReactMarkdown)`
  code {
    border-radius: 8px;
    white-space: pre-wrap;
    line-height: 1.5;
  }

  img {
    max-width: 100%;
  }

  h4 {
    text-align: left;
  }
  p {
    font-size: 14px;
  }
  li {
    font-size: 14px;
    margin-bottom: 5px;
  }
  table {
    font-size: 14px;
  }
  code {
    font-size: 14px;
  }

  pre {
    background-color: rgb(13, 17, 23);
    padding: 8px;
    border-radius: 8px;
    white-space: pre-wrap;
    position: relative;
  }

  .code-copy-btn {
    color: #e7e7e7;
    position: absolute;
    right: 8px;
    top: 8px;
    font-size: 1em;
    /* width: 100px;
    height: 100px; */
    display: inline-block;
    cursor: pointer;
    /* transition: all 0.3s ease-in-out; */
    z-index: 999;
  }

  .code-copy-btn:hover {
    transform: scale(1.1);
    opacity: 0.9;
  }

  p {
    line-height: 1.8;
  }
`;

const PreCode = (props: any) => {
  const {value, inline, ...restProps} = props;
  const [copied, setCopied] = useState(false);

  if (inline) {
    return <code {...props} />;
  }
  return <CodeSnippet lang={restProps.language} content={value} >
    {value}
  </CodeSnippet>

  const copy = useCallback(() => {
    if (copied) return;
    copyToClipboard(value);
    message.success('复制成功');
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 500);
  }, [value]);

  console.log('lang', value, restProps, common);
  if (restProps.language && !languages.includes(restProps.language)) {
    restProps.language = 'bash';
  }

  return (
    <>
      <div className="code-copy-btn" onClick={copy}>
        <CopyOutlined />
      </div>
      <Lowlight value={value} {...restProps} />
    </>
  )
}

const Pre = (props: any) => {
  const {children, ...restProps} = props;
  const [root, setRoot] = useState<Root>();

  const ref = useRef(null);

  useEffect(() => {
    if (!ref.current) return;
    if (root) return;
    setRoot(createRoot(ref.current))
  }, [ref.current])

  useEffect(() => {
    if (!root) return;
    if (children.type !== 'code') return;
    const {className, children: content} = children.props;
    const re = /language-(\w+)/;
    if (!className) {
      root.render(<PreCode value={content} />);
      return;
    }
    const match = className.match(re);
    if (!match) {
      root.render(<PreCode value={content} />);
    }else {
      root.render(<PreCode language={match[1]} value={content} />);
    }
  }, [children.props?.children, root]);

  return <div style={{position: "relative"}} ref={ref}>
  </div>
};

export function Markdown(props: { content: string }) {
  const { content } = props;

  return (
    <MarkdownStyle
      className="post-markdown"
      rehypePlugins={[rehypeRaw]}
      remarkPlugins={[remarkGfm]}
      urlTransform={(url) => {return url;}} 
      components={{
        // code: Code,
        pre: Pre,
        img: (props) => {
          return <>
          <br />
          <img {...props} />
          <br />
          </>
        },
        table: ({ children }) => (
          <div className="Table">
            <table>{children}</table>
          </div>
        ),
        // a: (props) => {
        //   const {children, href, ...restProps } = props;
        //   return <a href={href} {...restProps} target="_blank">{children}</a>
        // },
      }}
    >
      {content}
    </MarkdownStyle>
  );
}

