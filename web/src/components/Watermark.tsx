import React from 'react';

const Watermark: React.FC = () => {
  const text = '预发环境，请谨慎操作';
  
  const style: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
    height: '100vh',
    zIndex: 9999,
    pointerEvents: 'none',
    userSelect: 'none',
  };

  React.useEffect(() => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 减小画布大小使水印更密集
    canvas.width = 200;  // 从 400 减小到 200
    canvas.height = 150;  // 从 300 减小到 150

    // 设置文字样式
    ctx.font = '14px Arial';  // 稍微减小字体
    ctx.fillStyle = 'rgba(204, 204, 204, 0.3)';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // 旋转文字
    ctx.translate(100, 75);  // 调整为新的画布中心点
    ctx.rotate(-15 * Math.PI / 180);
    ctx.fillText(text, 0, 0);

    // 将 canvas 转换为背景图片
    const watermarkDiv = document.getElementById('watermark');
    if (watermarkDiv) {
      watermarkDiv.style.backgroundImage = `url(${canvas.toDataURL()})`;
      watermarkDiv.style.backgroundRepeat = 'repeat';
    }
  }, []);

  return <div id="watermark" style={style} />;
};

export default Watermark;