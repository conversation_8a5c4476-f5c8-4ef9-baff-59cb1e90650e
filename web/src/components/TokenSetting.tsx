import { CopyOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, message, Modal, Result, Table, Tooltip, Tree, Typography } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { styled } from 'styled-components';

import { TokenApi } from '@/api/token';
import { TooltipTime } from '@/components/TooltipTime';
import { useRole } from '@/hooks/useAdmin';
import { Token } from '@/interface';
import { copyToClipboard } from '@/utils/common';

const { confirm } = Modal;

interface RelateResource {
  id: string;
  name: string;
}

interface OperationCardProps {
  id: string;
  type: 'app' | 'workspace' | 'group';
  subResources?: RelateResource[];
  subType?: 'app' | 'group';
}

export const TokenSetting = (props: OperationCardProps) => {
  const { id, type, subResources, subType } = props;
  const role = useRole(type);
  const hasRight = role === 'admin' || role === 'developer';
  const [messageApi, contextHolder] = message.useMessage();
  const [show, setShow] = useState(false);
  const [checks, setChecks] = useState(['all'] as any);
  const [value, setValue] = useState(null as any);

  const { data, refresh: refershTokens } = useRequest(
    () => {
      if (hasRight) {
        return TokenApi.list(type, id);
      }
      return Promise.resolve([] as any);
    },
    {
      refreshDeps: [id, hasRight],
    },
  );

  console.log('tree', subResources);

  const treeOptions = useMemo(() => {
    return [
      {
        title: '全部',
        key: 'all',
        children: subResources
          ? subResources.map((g) => ({ title: g.name, key: g.id }))
          : [],
      },
    ];
  }, [subResources]);

  const { run: createToken } = useRequest(
    (resources?) => TokenApi.create(type, id, resources),
    {
      manual: true,
      onSuccess: () => {
        messageApi.open({
          type: 'success',
          content: '创建成功',
        });
        refershTokens();
      },
    },
  );

  const { run: updateToken } = useRequest(
    (resources, tokenId) => TokenApi.update(resources, tokenId),
    {
      manual: true,
      onSuccess: () => {
        messageApi.open({
          type: 'success',
          content: '更新成功',
        });
        refershTokens();
      },
    },
  );

  const { run: deleteToken } = useRequest(TokenApi.remove, {
    manual: true,
    onSuccess: () => {
      messageApi.open({
        type: 'success',
        content: '删除成功',
      });
      refershTokens();
    },
  });

  const handleCreate = async () => {
    // 如果有保存全部，则保存workspaeId
    let resources: any = [];
    if (checks.includes('all')) {
      resources.push({
        type,
        id,
      });
    } else {
      resources = checks.map((v) => ({ id: v, type: subType }));
    }
    if (value) {
      updateToken(resources, value.tokenID);
    } else {
      createToken(resources);
    }
    setShow(false);
  };

  // const handleEdit = async (tokenID) => {
  //   const data = await TokenApi.getRelateResource(tokenID);
  //   console.log('data', data);
  //   const res = data.map((v) => {
  //     return v.resourceType === type ? 'all' : v.resourceID;
  //   });
  //   setValue({ tokenID, res });
  //   setShow(true);
  // };

  const deleteTokenConfirm = (appID: string) => {
    confirm({
      title: '确认删除',
      content:
        '删除后，当前正在使用该Token的应用将无法正常使用，并且删除无法恢复，确认删除？',
      onOk() {
        deleteToken(appID);
      },
    });
  };

  const copyToken = (token: string) => {
    copyToClipboard(token);
    messageApi.open({
      type: 'success',
      content: '复制成功',
    });
  };

  const columns: ColumnsType<Token> = [
    {
      title: 'Token',
      dataIndex: 'token',
      width: '50%',
      render(value) {
        return (
          <Tooltip title={value}>
            <Typography.Text
              ellipsis={true}
              style={{ width: '80%' }}
              onClick={() => copyToken(value)}
            >
              {value}
              <Button type="text" size="small">
                <CopyOutlined></CopyOutlined>
              </Button>
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      render: (createdBy) => {
        return createdBy && createdBy.fullname
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (createdAt) => {
        return dayjs(createdAt).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '最后使用',
      dataIndex: 'lastUsedAt',
      render: (lastUsedAt) => {
        if (!lastUsedAt) {
          return '从未';
        }
        return <TooltipTime time={lastUsedAt} />;
      },
    },
    {
      title: '',
      dataIndex: '',
      key: 'action',
      render: (_, record) => (
        <div style={{ marginRight: '40px', display: 'flex' }}>
          {/* {subType && (
            <Button
              type="link"
              size="small"
              disabled={role !== 'admin'}
              onClick={() => {
                handleEdit(record.id);
              }}
            >
              编辑
            </Button>
          )} */}
          <Button type="link" size="small" onClick={() => copyToken(record.token)}>
            复制
          </Button>
          <Button
            disabled={role !== 'admin'}
            type="link"
            size="small"
            onClick={() => deleteTokenConfirm(record.id)}
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  if (!hasRight) {
    return <Result status="403" title="无权限" subTitle="对不起，您暂时无权限查看." />;
  }

  return (
    <CrudContainer>
      <h3>API密钥</h3>
      <FloatBtn
        type="primary"
        disabled={role !== 'admin'}
        onClick={() => {
          // 如果需要选择资源范围 subType
          // if (subType) {
          //   setValue(null);
          //   setShow(true);
          // } else {
          createToken();
          // }
        }}
      >
        新增
      </FloatBtn>
      {contextHolder}
      {(data?.total ?? 0) > 0 && (
        <Table
          size="small"
          columns={columns}
          dataSource={data!.items}
          pagination={
            data!.total > 5
              ? {
                  simple: true,
                  total: data!.total,
                  pageSize: 5,
                }
              : false
          }
        />
      )}
      <Modal
        title="选择生效范围"
        visible={show}
        destroyOnClose
        onCancel={() => setShow(false)}
        onOk={handleCreate}
      >
        <Tree
          checkable
          defaultCheckedKeys={value?.res || ['all']}
          defaultExpandAll
          treeData={treeOptions}
          onCheck={setChecks}
          blockNode
        />
      </Modal>
    </CrudContainer>
  );
};

const CrudContainer = styled.div`
  padding: 20px;
  background-color: #fff;
`;

const FloatBtn = styled(Button)`
  position: absolute;
  right: 20px;
  top: 20px;
`;
