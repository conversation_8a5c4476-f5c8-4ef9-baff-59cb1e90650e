import React, { useState } from 'react';
import { Image, Modal } from 'antd';
import styled from 'styled-components';

interface VideoPreviewProps {
  src: string;
  width?: number;
  height?: number;
  style?: React.CSSProperties;
  previewImgUrl?: string;
}

const VideoPreview: React.FC<VideoPreviewProps> = ({
  previewImgUrl,
  src,
  width,
  height,
  style,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <PreviewContainer onClick={() => setIsModalOpen(true)}>
        {previewImgUrl ? (
          <Image src={previewImgUrl} width={width} height={height} style={style} preview={false} />
        ) : (
          <video src={src} width={width} height={height} style={style} muted />
        )}
        <PlayIcon>▶</PlayIcon>
      </PreviewContainer>

      <Modal
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width="auto"
        centered
      >
        <video
          src={src}
          controls
          autoPlay
          style={{ maxWidth: '80vw', maxHeight: '80vh' }}
        />
      </Modal>
    </>
  );
};

const PreviewContainer = styled.div`
  position: relative;
  display: inline-block;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
`;

const PlayIcon = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 24px;
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
  pointer-events: none;
`;

export default VideoPreview;
