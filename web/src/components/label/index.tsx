import React from 'react';
import styled from 'styled-components';

type LabelProps = {
  name: string | React.ReactNode;
  notNull?: boolean;
  children?: React.ReactNode;
  style?: object;
  labelMinWidth?: number;
  noLabel?: boolean;
};

const Label: React.FC<LabelProps> = ({
  name,
  notNull = false,
  noLabel = false,
  children = null,
  style = {},
  labelMinWidth = 80,
}) => (
  <StyledLabel style={{ ...style }}>
    <span style={{ minWidth: `${labelMinWidth}px` }} className="labelContent">
      {!noLabel && (
        <>
          {notNull && <i>*</i>}
          <span className="label">{name}:</span>
        </>
      )}
    </span>
    <div style={{ flex: 1 }}>{children}</div>
  </StyledLabel>
);

export { Label };

const StyledLabel = styled.div`
  padding: 16px 0;
  display: flex;
  flex-direction: row;
  align-items: center;

  i {
    color: red;
    padding-right: 2px;
  }

  .label {
    min-width: 50px;
    display: inline-block;
    // margin-right: 10px;
  }

  .labelContent {
    margin-right: 20px;
    text-align: right;
    display: inline-block;
  }
`;
