import { generateUUID } from '@/utils/uuid';
import { HolderOutlined } from '@ant-design/icons';
import { Table, TableProps } from 'antd';
import { isEqual, omit } from 'lodash';
import React from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

interface RecordWithRowKey {
  __rowKey: string;
}

interface IDragSortTableProps extends TableProps<any> {
  // 注意!! 返回的 record 对象是重新生成的，并非原本传入的对象。
  // FIXME 如果有更好的实现请修复此问题
  onSortChange: (sortedRecord: any[]) => void;
}

export function DragSortTable(props: IDragSortTableProps) {
  const { dataSource, columns, onSortChange } = props;
  const [dataWithRowKey, setDataWithRowKey] = useState<RecordWithRowKey[]>([]);
  const listRef = useRef(dataWithRowKey);
  listRef.current = dataWithRowKey;

  useEffect(() => {
    if (
      dataSource &&
      !isEqual(
        dataSource,
        dataWithRowKey.map((it) => omit(it, ['__rowKey'])),
      )
    ) {
      const newList = dataSource.map((item: any) => {
        return {
          ...item,
          __rowKey: generateUUID(),
        };
      });
      setDataWithRowKey(newList);
    }
  }, [dataSource]);

  const [drag, setDrag] = useState('');
  const [drop, setDrop] = useState('');

  function onDrag(uuid: string, ev: any) {
    ev.dataTransfer.setData('text/plain', ev.target.id);
    setDrag(uuid);
  }

  function onDragOver(uuid: string, ev: any) {
    setDrop(uuid);
    ev.preventDefault();
    ev.dataTransfer.dropEffect = 'move';
  }

  function onDrop() {
    setDrag('');
    setDrop('');
    onSortChange(
      listRef.current.map((it) => {
        return omit(it, '__rowKey');
      }),
    );
  }

  useEffect(() => {
    const list = listRef.current;
    if (list && drag && drop && drag !== drop) {
      const newList = [...list!];

      const dragIndex = newList.findIndex((item) => item.__rowKey === drag);
      const dropIndex = newList.findIndex((item) => item.__rowKey === drop);

      const dragItem = newList[dragIndex];
      newList.splice(dragIndex, 1);
      newList.splice(dropIndex, 0, dragItem);

      setDataWithRowKey(newList);
    }
  }, [drag, drop]);

  const DraggableRow = useMemo(
    () =>
      ({ children, ...props }: RowProps) => {
        const record = (children as any)[0]?.props?.record;
        const __rowKey = record?.__rowKey;

        return (
          <tr
            {...props}
            id={__rowKey}
            draggable={true}
            onDragStart={(e) => onDrag(__rowKey, e)}
            onDragOver={(e) => onDragOver(__rowKey, e)}
            onDrop={onDrop}
          >
            {React.Children.map(children, (child) => {
              if ((child as React.ReactElement).key === 'sort') {
                return React.cloneElement(child as React.ReactElement, {
                  children: (
                    <HolderOutlined
                      style={{ touchAction: 'none', cursor: 'move' }}
                      rev={undefined}
                    />
                  ),
                });
              }
              return child;
            })}
          </tr>
        );
      },
    [],
  );

  return (
    <Table
      pagination={false}
      dataSource={dataWithRowKey}
      columns={[
        {
          key: 'sort',
        },
        ...(columns as []),
      ]}
      components={{ body: { row: DraggableRow } }}
    />
  );
}
