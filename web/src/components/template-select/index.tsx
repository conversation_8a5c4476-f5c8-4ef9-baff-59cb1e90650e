import { But<PERSON>, Mo<PERSON>, <PERSON>, Avatar, Flex, Tag, Popconfirm } from 'antd';
import { TemplateIcon } from '../icons';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { IAppType } from '@/interface';
import { AppApi } from '@/api/app';
import { templateIdMap } from '@/constants';
import { env } from '@/utils/common';
import WorkflowPreviewBtn from '../workflow-preview-btn';


const StyledList = styled(List)`
  .ant-list-item {
    padding: 12px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }
    .ant-list-item-meta-title {
      text-align: left;
    }
  }
`;

export interface Template {
  id: string;
  name: string;
  description?: string;
  extInfo?: {
    avatar?: string;
  };
  tags?: string[];
  icon?: string;
}

interface TemplateSelectProps {
  templates: Template[];
  onSelect: (template: Template) => void;
  appType: IAppType;
  buttonStyle?: React.CSSProperties;
}

export const TemplateSelect: React.FC<TemplateSelectProps> = ({
  // templates,
  onSelect,
  appType,
  buttonStyle
}) => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleSelect = (template: Template) => {
    onSelect(template);
    setIsModalOpen(false);
  };

  useEffect(() => {
    AppApi.listAppsByGroup(templateIdMap[env], { appType: [appType], isTemplate: true }).then((res) => {
      console.log('res...', res);
      setTemplates(res.items);
    });
  }, [appType]);

  return (
    <>
      <Button
        icon={<TemplateIcon />}
        onClick={showModal}
        style={buttonStyle}
      >
        预设模板
      </Button>
      <Modal
        title="选择预设模板"
        open={isModalOpen}
        onCancel={handleCancel}
        footer={null}
        width={600}
      >
        <StyledList<Template>
          itemLayout="horizontal"
          dataSource={templates}
          renderItem={(item: Template) => (
            <List.Item
              actions={[<WorkflowPreviewBtn text="详情" appId={item?.id} />,
              <Popconfirm title="使用该模板吗？" description="使用该模板后，当前页面数据将清空" onConfirm={() => handleSelect(item)}>
                <Button type="link">{'切换'}</Button>
              </Popconfirm>
              ]}
            >
              <List.Item.Meta
                avatar={
                  item?.extInfo?.avatar ? (
                    <Avatar src={item?.extInfo?.avatar} />
                  ) : (
                    <Avatar icon={<TemplateIcon />} />
                  )
                }
                title={item.name}
                description={
                  <Flex vertical gap={8}>
                    <div>{item.description}</div>
                    {item.tags && item.tags.length > 0 && (
                      <Flex gap={8}>
                        {item.tags.map((tag) => (
                          <Tag key={tag}>{tag}</Tag>
                        ))}
                      </Flex>
                    )}
                  </Flex>
                }
              />
            </List.Item>
          )}
        />
      </Modal>
    </>
  );
}; 