import React, { useState } from 'react';
import { Modal, Form, InputNumber, message } from 'antd';

interface ExportModalProps {
  open: boolean;
  onOk: (params: { pageSize: number; totalPages: number }) => void;
  onCancel: () => void;
  total?: number;
}

const ExportModal: React.FC<ExportModalProps> = ({ open, onOk, onCancel, total = 0 }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    try {
      if (total < 1) {
        message.error('当前总数据量为0，无法导出');
        return;
      }
      const values = await form.validateFields();
      const totalCount = values.pageSize * values.totalPages;

      if (totalCount > 10000) {
        message.error('单次最多允许导出10000条数据');
        return;
      }

      setLoading(true);
      await onOk(values);
      form.resetFields();
    } catch (error) {
      console.error('Export validation failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="导出配置"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          pageSize: 5000,
          totalPages: 1,
        }}
      >
        <Form.Item
          label="每页数量"
          name="pageSize"
          rules={[
            { required: true, message: '请输入每页数量' },
            { type: 'number', min: 1, max: 6000, message: '每页数量需在1-1000之间' }
          ]}
        >
          <InputNumber disabled style={{ width: '100%' }} placeholder="请输入每页数量" />
        </Form.Item>
        <Form.Item
          label="导出页数"
          name="totalPages"
          rules={[
            { required: true, message: '请输入导出页数' },
            { type: 'number', min: 1, message: '导出页数必须大于0' }
          ]}
        >
          <InputNumber style={{ width: '100%' }} placeholder="请输入导出页数" />
        </Form.Item>
      </Form>
      <div style={{ color: '#666' }}>
        当前总数据量：{total} 条，单次最多导出5000条数据
      </div>
    </Modal>
  );
};

export default ExportModal; 