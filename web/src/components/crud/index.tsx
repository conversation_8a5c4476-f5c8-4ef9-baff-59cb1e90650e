/* eslint-disable react/require-default-props */
import {
  <PERSON><PERSON>,
  Card,
  Input,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Table,
} from 'antd';
import { isEqual, pick } from 'lodash';
import { useContext, useEffect, useRef, useState } from 'react';
import styled from 'styled-components';

import { CrudAPI, IdModel } from '../../interface';
import { globalContext, GlobalStateKey } from '../../store';
import { Label } from '../label';
import { LayoutContainer } from '../layout-container';
import React from 'react';

const { Option } = Select;

enum ModalMode {
  ADD = 'add',
  EDIT = 'edit',
}

interface CrudProps<M extends IdModel, T> {
  modelName: string;
  api: CrudAPI<M, T>;
  key?: any;
  parent?: {
    id: string | number;
    key: string;
  };
  globalStateKey?: GlobalStateKey;
  hiddenDelete?: boolean;
  schema: Schema[];
  hideAdd?: boolean;
  showCopy?: boolean; // 是否显示复制按钮
  wrapInCard?: boolean;
  moreAction?: React.ReactNode;
  defaultValues?: Partial<T>;
  clientPagination?: boolean;
  customPagination?: {
    offsetKey?: string;
    limitKey?: string;
    useOffset?: boolean;
  };
}

export type JsonType = string | number | boolean | object;

interface IOption {
  label: string | JSX.Element;
  value: JsonType;
}

type onWatchCallback = (
  watchFieldKey: string,
  watchFieldValue: JsonType,
  updateForm: (key: string, value: JsonType) => void,
) => void;

interface Schema {
  title: string | JSX.Element;
  dataIndex: string;
  defaultValue?: (model: any) => string;
  editable?: boolean; // 是否可二次编辑
  required?: boolean; // 是否为创建所需字段
  disableEditInCreate?: boolean; // 创建时是否可编辑 (一般用于联动字段)
  notNull?: boolean; // 是否非空
  valueOptions?: IOption[]; // 可选值，将展示为 Select
  type?: string;
  width?: number | string;
  ellipsis?: boolean;
  hideAdd?: boolean; // 是否隐藏添加按钮
  onEditChange?: (value: JsonType) => void;
  onEditWatch?: {
    watchFieldKeys: string[];
    callback: onWatchCallback;
  };
  hideInTable?: boolean; // 添加这个属性来在表格中隐藏
  component?: React.ReactElement;
  formItemProps?: any;
}

const PAGE_SIZE = 12;

function CrudCard<M extends IdModel, T extends Record<string, any>>(
  props: CrudProps<M, T>,
) {
  const {
    modelName,
    api,
    schema,
    parent,
    globalStateKey,
    hiddenDelete,
    hideAdd,
    key,
    moreAction,
    wrapInCard = true,
    showCopy = false,
    defaultValues,
    clientPagination = false,
    customPagination,
  } = props;

  const PAGE_SIZE = clientPagination ? 300 : 12;

  const [mode, setMode] = useState(ModalMode.ADD);
  const [editId, setEditId] = useState<number | string>(0);
  const [showAddModal, setShowAddModal] = useState(false);
  const [listData, setListData] = useState<M[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [model, setModel] = useState<T>(getFormData({}));
  const modelRef = useRef(model);
  modelRef.current = model;

  const { updateGlobalState } = useContext(globalContext);
  const [onEditWatchCallbackMap, setOnEditWatchCallbackMap] = useState<
    Record<string, onWatchCallback[]>
  >({});

  // 收集 onWatch 的回调函数
  useEffect(() => {
    const callbackMapList = {} as Record<string, onWatchCallback[]>;
    schema.forEach((s) => {
      if (s.onEditWatch) {
        const { watchFieldKeys, callback } = s.onEditWatch;
        watchFieldKeys.forEach((key) => {
          if (!callbackMapList[key]) {
            callbackMapList[key] = [];
          }
          callbackMapList[key].push(callback);
        });
      }
    });
    setOnEditWatchCallbackMap(callbackMapList);
  }, []);

  function fetchList() {
    if (parent && 'id' in parent && !parent.id) {
      return;
    }

    if (api.listWithPage) {
      if (customPagination?.useOffset) {
        const offset = (page - 1) * PAGE_SIZE;
        const params = {
          [customPagination.offsetKey || 'offset']: offset,
          [customPagination.limitKey || 'limit']: PAGE_SIZE,
          ...(parent?.id ? { [parent.key]: parent.id } : {})
        };
        
        api.listWithPage(params).then((data) => {
          if (data?.total) {
            setListData(data?.items);
            setTotal(data?.total);
            if (globalStateKey) {
              updateGlobalState(globalStateKey, data?.items || []);
            }
          }
        });
      } else {
        api.listWithPage(parent?.id, clientPagination ? 1 : page, PAGE_SIZE).then((data) => {
          if (data?.total) {
            setListData(data?.items);
            setTotal(data?.total);
            if (globalStateKey) {
              updateGlobalState(globalStateKey, data?.items || []);
            }
          }
        });
      }
    } else {
      api.list(parent?.id).then((data) => {
        setListData(data);
        setTotal(data.length);
        if (globalStateKey) {
          updateGlobalState(globalStateKey, data);
        }
      });
    }
  }

  useEffect(fetchList, [
    api,
    parent?.id,
    globalStateKey,
    updateGlobalState,
    ...(clientPagination ? [] : [page]),
    key
  ]);

  const onModelOk = () => {
    if (mode === ModalMode.ADD) {
      onCreate();
    } else {
      onUpdate();
    }
  };

  const onCreate = () => {
    const notNullKeys = schema.filter((s) => s.required && s.notNull);
    const nullKey = notNullKeys.find((k) => !model[k.dataIndex]);
    if (nullKey) {
      message.warning(`${nullKey.title} 不能为空`);
      return;
    }

    // TODO FIXME
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params = { ...model } as any;
    // if (parent) {
    //   params[parent.key] = parent.id;
    // }
    api.create(params, parent?.id).then(() => {
      message.success(`创建${modelName}成功`);
      setShowAddModal(false);
      fetchList();
    });
  };

  const onUpdate = () => {
    api.update(model, editId).then(() => {
      message.success(`更新${modelName}成功`);
      setShowAddModal(false);
      fetchList();
    });
  };

  const onEdit = (record: M) => {
    setFormData(record);
    setMode(ModalMode.EDIT);
    setShowAddModal(true);
    setEditId(record.id);
  };

  const onCopy = (record: M) => {
    setFormData(record);
    setMode(ModalMode.ADD);
    setShowAddModal(true);
  };

  const onDelete = (record: M) => {
    api.remove(record.id).then(() => {
      message.success('删除成功');
      fetchList();
    });
  };

  const onAddBtnClick = () => {
    setMode(ModalMode.ADD);
    setShowAddModal(true);
    setFormData(defaultValues || {});
  };

  function getFormData(record = {}): T {
    const editableKeys = schema.filter((s) => s.editable).map((s) => s.dataIndex);
    return pick(record, editableKeys) as T;
  }

  const setFormData = (record = {}) => {
    setModel(getFormData(record));
  };

  const setFormProps = (key: string, value: JsonType, type?: string) => {
    setModel({
      ...modelRef.current,
      [key]: type === 'number' ? Number(value) : value,
    });
    setTimeout(() => {
      if (onEditWatchCallbackMap[key]) {
        onEditWatchCallbackMap[key].forEach((cb) => {
          cb(key, value, setFormProps);
        });
      }
    }, 10);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const columns: any[] = schema
    .filter(item => !item.hideInTable)
    .map(({ title, dataIndex, width, ellipsis, ...others }) => {
      return {
        title,
        dataIndex,
        width,
        ellipsis,
        key: dataIndex,
        render: (text: JsonType) => {
          if (others.valueOptions && others.valueOptions.length) {
            const opt = others.valueOptions.find(({ value }) => isEqual(value, text));
            if (opt) {
              return opt.label;
            }
          }
          return text;
        },
        ...others,
      };
    },
    );
  columns.push({
    title: '操作',
    key: 'action',
    render: (_: string, record: M) => (
      <Space>
        {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
        {showCopy && <a onClick={() => onCopy(record)}>复制</a>}
        <a onClick={() => onEdit(record)}>编辑</a>
        {!hiddenDelete && (
          <Popconfirm
            placement="top"
            title="确认删除?"
            onConfirm={() => onDelete(record)}
          >
            <a>删除</a>
          </Popconfirm>
        )}
      </Space>
    ),
  });

  const renderForm = () => {
    const items = schema.filter((s) => {
      if (mode === ModalMode.ADD) {
        if (s.formItemProps?.style?.display === 'none') {
          return false;
        }
        return s.required;
      }
      return s.editable;
    });
    return items.map((it) => {
      const hasOptions = it.valueOptions;
      const onEditChange = it.onEditChange || (() => void 0);
      onEditChange(model[it.dataIndex]);

      if (it.component) {
        if (it.component === null) {
          return null;
        }

        const Component = React.cloneElement(it.component, {
          value: model[it.dataIndex],
          onChange: (value: any) => {
            setFormProps(it.dataIndex, value, it.type);
            onEditChange(value);
          },
          disabled: mode === ModalMode.ADD ? it.disableEditInCreate : false
        });

        return (
          <Label
            name={it.title}
            notNull={it.notNull}
            labelMinWidth={100}
            key={it.dataIndex}
            style={it.formItemProps?.style}
          >
            {Component}
          </Label>
        );
      }

      if (!hasOptions) {
        return (
          <Label
            name={it.title}
            notNull={it.notNull}
            labelMinWidth={100}
            key={it.dataIndex}
          >
            <Input
              type={it.type || 'text'}
              value={model[it.dataIndex]}
              defaultValue={it.defaultValue ? it.defaultValue(model) : ''}
              onChange={(e) => {
                setFormProps(it.dataIndex, e.target.value, it.type);
                onEditChange(e.target.value);
              }}
              disabled={it.disableEditInCreate}
            />
          </Label>
        );
      }
      return (
        <Label
          name={it.title}
          notNull={it.notNull}
          labelMinWidth={100}
          key={it.dataIndex}
        >
          <Select
            showSearch
            style={{ flexGrow: 1, width: '100%' }}
            placeholder={`选择${it.title}`}
            onChange={(v) => {
              setFormProps(it.dataIndex, JSON.parse(v));
              onEditChange(JSON.parse(v));
            }}
            value={JSON.stringify(model[it.dataIndex])}
            filterOption={(input, option) => `${option?.children}`.indexOf(input) >= 0}
            disabled={it.disableEditInCreate}
          >
            {it.valueOptions!.map((option: IOption) => (
              <Option
                key={JSON.stringify(option.value)}
                value={JSON.stringify(option.value)}
              >
                {option.label}
              </Option>
            ))}
          </Select>
        </Label>
      );
    });
  };

  const content = (
    <>
      <Table 
        dataSource={
          clientPagination 
            ? listData // 提供完整数据给 Table，让 Table 内部处理筛选
            : listData
        } 
        columns={columns} 
        pagination={
          clientPagination 
            ? { 
                total: listData.length,
                pageSize: 10,
                current: page, 
                onChange: setPage 
              }
            : { 
                total, 
                pageSize: PAGE_SIZE, 
                current: page, 
                onChange: setPage 
              }
        }
        // 添加以下属性以支持前端分页和筛选
        showSorterTooltip={false}
        onChange={(pagination, filters, sorter, extra) => {
          if (clientPagination) {
            setPage(pagination.current || 1);
          }
        }}
      />
      <Modal
        title={`${mode === ModalMode.ADD ? '新增' : '修改'}${modelName}`}
        open={showAddModal}
        onCancel={() => setShowAddModal(false)}
        onOk={onModelOk}
      >
        {renderForm()}
      </Modal>
    </>
  );

  if (!wrapInCard) {
    return (
      <CrudContainer>
        <h3>{`${modelName}`}</h3>
        <FloatDiv>
          {moreAction ? moreAction : null}
          {hideAdd ? null :
            <Button type="primary" onClick={onAddBtnClick}>
              新增
            </Button>
          }
        </FloatDiv>
        {content}
      </CrudContainer>
    );
  }

  return (
    <LayoutContainer>
      <Card
        title={`${modelName}管理`}
        extra={
          <Button type="primary" onClick={onAddBtnClick}>
            新增
          </Button>
        }
      >
        {content}
      </Card>
    </LayoutContainer>
  );
}

export default CrudCard;

const CrudContainer = styled.div`
  padding: 20px;
  background-color: #fff;
`;

const FloatDiv = styled.div`
  position: absolute;
  right: 20px;
  top: 20px;
`;
