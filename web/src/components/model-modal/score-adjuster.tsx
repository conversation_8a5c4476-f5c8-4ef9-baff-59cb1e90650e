import React, { useState } from 'react';
import { Progress } from 'antd';

interface ScoreAdjusterProps {
  initialScores: {
    performance: number;
    cost: number;
    context: number;
    speed: number;
  };
  onScoresChange: (scores: {
    performance: number;
    cost: number;
    context: number;
    speed: number;
  }) => void;
}

const ScoreAdjuster: React.FC<ScoreAdjusterProps> = ({ initialScores, onScoresChange }) => {
  const [scores, setScores] = useState(initialScores);

  const handleChange = (key: string, value: number) => {
    const newScores = {
      ...scores,
      [key]: value
    };
    setScores(newScores);
    onScoresChange(newScores);
  };

  const metrics = [
    { key: 'performance', label: '性能' },
    { key: 'cost', label: '成本控制' },
    { key: 'context', label: '上下文' },
    { key: 'speed', label: '速度' }
  ];

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between',
      width: '100%'
    }}>
      {metrics.map(({ key, label }) => (
        <div key={key} style={{ 
          display: 'flex', 
          alignItems: 'center', 
          fontSize: '14px',
          gap: '4px'
        }}>
          <span style={{ fontSize: '14px', whiteSpace: 'nowrap' }}>{label}：</span>
          <button
            onClick={() => handleChange(key, Math.max(0, scores[key] - 1))}
            style={{
              width: '16px',
              height: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '12px',
              cursor: 'pointer',
              backgroundColor: '#fafafa'
            }}
          >
            -
          </button>
          
          <Progress 
            steps={3} 
            percent={(scores[key] / 3) * 100}
            showInfo={false}
            size={14}
            strokeColor="#faad14"
            style={{ width: '48px' }}
          />

          <button
            onClick={() => handleChange(key, Math.min(3, scores[key] + 1))}
            style={{
              width: '16px',
              height: '16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '1px solid #d9d9d9',
              borderRadius: '4px',
              fontSize: '12px',
              backgroundColor: '#fafafa',
              cursor: 'pointer'
            }}
          >
            +
          </button>
        </div>
      ))}
    </div>
  );
};

export default ScoreAdjuster; 