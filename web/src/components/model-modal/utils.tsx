import { GlobalModel } from '@/interface';
import { getContextValue, getCostValue } from './helpers';
import type { DataType } from './types';
import { Rate, Button, Image, Tag, Popover, message } from 'antd';
import React from 'react';
import type { TableColumnsType } from 'antd';
import dayjs from 'dayjs';
import { CopyOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { copyToClipboard } from '@/utils/common';

export const texts = {
  cost: ['非常昂贵（>100元/M）', '昂贵（>50元/M）', '一般（>10元/M）', '较便宜（<10元/M）', '十分便宜（<2元/M）'],
  speed: ['很慢（<20 Tokens/s）', '一般（20~40 Tokens/s）', '较快（40~60 Tokens/s）', '很快（60~80 Tokens/s）', '非常快（>80 Tokens/s）'],
  performance: ['可以处理一般的文本生成/分类任务', '可以处理一般的文本生成/分类任务', '一般的逻辑推任务，答疑场景，日常对话', '需要一定的推理性任务，需要指令遵循', '多步推理，智能体，工具调用，强指令遵循'],
  context: ['8k', '16k', '32k', '128k', '>=200k']
};

export const speedTexts = ['很慢', '一般', '较快', '很快', '非常快'];

export const modelTestLabel = {
  avg: '平均速率',
  creative: '创意文本内容生成',
  json: 'JSON内容生成',
  task: '遵循多步任务执行'
};

export const getModelTestSpeed = (model: {
  model_test_result?: {
    avg: number;
  };
  model_test?: {
    avg: number;
  };
  speed?: number;
}) => {
  const model_test = model.model_test_result || model.model_test;
  if (!model_test) return model.speed || 2;
  const { avg } = model_test;
  if (avg > 70) {
    return 5;
  }
  if (avg > 50) {
    return 4;
  }
  if (avg > 30) {
    return 3;
  }
  if (avg > 15) {
    return 2;
  }
  return 1;
};

export const tagLabels: Record<string, string> = {
  'view': '图片输入',
  'video': '视频输入',
  'audio': '音频输入',
  'json': 'JSON模式',
  'web-search': '联网搜索'
};

export const tagFilter = Object.keys(tagLabels);

// 获取指标描述
const getCom = label => (v, record) => {
  let text = texts[label][v - 1];
  if (label === 'context') {
    const value = getContextValue(v);
    return <div className="flex">
      <Rate allowHalf value={value} disabled style={{ fontSize: 16 }} />
      <div className={`text v${value}`}>{v}k</div>
    </div>
  }
  if (label === 'cost') {
    const value = getCostValue(v);
    return <div className="flex">
      <Rate allowHalf value={value} disabled style={{ fontSize: 16 }} />
      <div className={`text v${value}`}>{`输入￥${record.inputCost}，输出￥${record.outputCost}`}</div>
    </div>
  }
  if (label === 'speed') {
    let popoverContent = <pre>
      暂无实时测试结果
    </pre>;
    if (record?.model_test) {
      text = `真实速率: ~${Math.round(record?.model_test?.avg)} Tokens/s，200字内容约${Math.round(200 / record?.model_test?.avg)}s`;
      popoverContent = <pre>{`${modelTestLabel.avg}: ~${Math.round(record?.model_test?.avg)} Tokens/s\n
${modelTestLabel.creative}: ~${Math.round(record?.model_test?.creative)} Tokens/s\n
${modelTestLabel.json}: ~${Math.round(record?.model_test?.json)} Tokens/s\n
${modelTestLabel.task}: ~${Math.round(record?.model_test?.task)} Tokens/s\n`}
      </pre>
    }
    return <div className="flex">
      <Rate allowHalf value={v} disabled style={{ fontSize: 16 }} />
      <Popover content={popoverContent}>
        <div className={`text v${v}`}>{text} <QuestionCircleOutlined /></div>
      </Popover>
    </div>
  }
  return <div className="flex">
    <Rate allowHalf value={v} disabled style={{ fontSize: 16 }} />
    <div className={`text v${v}`}>{text}</div>
  </div>
};

const DisabledWrapper = ({ children, message, styles }) => {
  if (!message) {
    return <div style={styles}>{children}</div>;
  }
  return (
    <Popover content={message}>
      <div style={styles}>{children}</div>
    </Popover>
  );
};

const getDisabledStyles = (record: DataType, disabledList: RegExp[] = []) => {
  const isModelDisabled = isDisabled(disabledList, record.name);
  const isModelEnabled = record.enable !== false;
  const disableReason = record.disableReason;
  const disabledMessage = isModelDisabled
    ? '该模式下暂不可用，可以尝试在应用中使用该模型'
    : disableReason || '该模型暂不可用，请联系管理员录入模型账号';

  return {
    isDisabled: isModelDisabled || !isModelEnabled,
    styles: {
      opacity: (isModelDisabled || !isModelEnabled) ? 0.5 : 1,
      cursor: (isModelDisabled || !isModelEnabled) ? 'not-allowed' : 'default'
    },
    message: (!isModelEnabled || isModelDisabled) ? disabledMessage : record.description
  };
};

// 从原来的 ModelModal 组件中抽离的工具函数
export const isDisabled = (disabledList = [], name) => {
  return disabledList.some(l => {
    return l.test(name);
  });
};

export const transformModelList = (modelList: GlobalModel[]) => {
  return modelList
    .filter(model => !model.tag?.includes('deprecated'))
    .map((model: GlobalModel, index) => ({
      key: String(index + 1),
      name: model.name,
      providerKind: model?.providerKind,
      performance: model?.performance || 2,
      alias: model?.alias,
      description: model?.description,
      disableReason: model?.disableReason,
      provider: model?.providerName,
      tags: model?.tag?.filter(v => tagFilter.includes(v)),
      context: model?.context || 8,
      enable: model?.enable,
      cost: model?.fee?.avg,
      inputCost: model?.fee?.input,
      outputCost: model?.fee?.output,
      speed: getModelTestSpeed(model),
      model_test: model?.model_test_result || null,
      createdAt: model?.createdAt || '',
    }));
};

export const getScore = (record: DataType, multiple: Record<string, number>): number => {
  const activeWeights = Object.values(multiple).filter(Boolean).length;
  if (activeWeights === 0) return 0;

  return Object.keys(multiple).reduce((pre, key) => {
    if (multiple[key] === 0) return pre;

    if (key === 'context') {
      return pre + getContextValue(record[key]);
    }
    if (key === 'cost') {
      return pre + getCostValue(record[key]);
    }
    return pre + record[key];
  }, 0) / activeWeights;
};

// 从原组件中抽离表格列配置
export const getColumns = ({
  data,
  multiple,
  selectedModels,
  setSelectedModels,
  showCandidate = true,
  disabledList,
  showOperation = true,
  globalState,
  mode = 'multiple'
}): TableColumnsType<any> => {
  const { modelProviderList } = globalState;
  const providerInfo = (modelProviderList || []).reduce((acc, curr) => {
    acc[curr.kind] = {
      icon: curr.icon,
      name: curr.description
    };
    return acc;
  }, {});
  console.log("providerImg...", providerInfo);

  const handleCopy = v => {
    copyToClipboard(v);
    message.success({
      content: '复制成功',
    })
  }

  const columns: TableColumnsType<any> = [

    {
      title: '供应商',
      dataIndex: 'providerKind',
      fixed: 'left',
      width: 100,
      filters: Array.from(new Set(data.map((item: any) => item.provider)))
        .map(provider => ({ text: provider as React.ReactNode, value: provider as React.Key })),
      onFilter: (value: boolean | React.Key, record) => record.provider === value.toString(),
      render: (v, record) => {
        console.log("record...", v, record);
        const { styles, message } = getDisabledStyles(record, disabledList);
        return (
          <DisabledWrapper styles={styles} message={message}>
            <div className="provider">
              <div><Image src={providerInfo[v]?.icon || ''} width={30} /></div>
              <div>{providerInfo[v]?.name || v}
                {!showOperation &&
                  <CopyOutlined onClick={() => handleCopy(record.providerKind)} style={{ color: '#999', fontSize: 12 }} />
                }
              </div>
            </div>
          </DisabledWrapper>
        );
      },
    },
    {
      title: '模型名称',
      dataIndex: 'name',
      fixed: 'left',
      width: 220,
      filterSearch: true,
      render: (_, record) => {
        const { styles, message } = getDisabledStyles(record, disabledList);
        return (
          <DisabledWrapper styles={styles} message={message}>
            <div className="model-name">{record.alias || record.name}</div>
            <div className="model-alias">{record.name}
              {!showOperation && <CopyOutlined onClick={() => handleCopy(record.name)} style={{ color: '#999', fontSize: 12 }} />}
            </div>
          </DisabledWrapper>
        );
      }
    },
    {
      title: '额外能力',
      key: 'tags',
      fixed: 'left',
      width: 100,
      dataIndex: 'tags',
      onFilter: (value: boolean | React.Key, record) => record.tags?.includes(value as string),
      filters: tagFilter.map(tag => ({
        text: tagLabels[tag],
        value: tag
      })),
      render: (_, { tags }) => (
        <>
          {(tags || []).map((tag) => {
            let color = 'gold';
            if (tag === 'json') {
              color = 'blue';
            }
            if (tag === 'web-search') {
              color = 'pink';
            }
            return (
              <Tag color={color} key={tag} style={{ marginBottom: 4 }}>
                {tagLabels[tag] || tag}
              </Tag>
            );
          })}
        </>
      ),
    },

    {
      title: '费用(/百万token)',
      dataIndex: 'cost',
      render: getCom('cost'),
      sorter: {
        compare: (a, b) => a.cost - b.cost,
      },
    },
    {
      title: '输出速度',
      dataIndex: 'speed',
      render: getCom('speed'),
      sorter: {
        compare: (a, b) => a.speed - b.speed,
      },
    },
    {
      title: '性能',
      dataIndex: 'performance',
      width: 200,
      render: getCom('performance'),
      sorter: {
        compare: (a, b) => a.performance - b.performance,
        multiple: multiple.performance,
      },
    },
    {
      title: '上下文长度',
      dataIndex: 'context',
      render: getCom('context'),
      sorter: {
        compare: (a, b) => a.context - b.context,
      },
    },
    {
      title: '更新时间',
      dataIndex: 'createdAt',
      width: 150,
      render: (text) => formatDateTime(text),
      sorter: {
        compare: (a, b) => {
          if (!a.createdAt) return 1;
          if (!b.createdAt) return -1;
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        },
      },
    },
  ];

  if (showOperation) {
    columns.unshift({
      title: '操作',
      key: 'operation',
      dataIndex: 'operation',
      fixed: 'left',
      width: 40,
      filters: [],
      onFilter: () => true,
      render: (_, record: DataType) => {
        const { isDisabled } = getDisabledStyles(record, disabledList);
        const isSelected = selectedModels.some(model => model.key === record.key);
        return (
          <Button
            type={isSelected ? "text" : "link"}
            disabled={isDisabled}
            onClick={() => {
              if (isSelected) {
                setSelectedModels(prev => prev.filter(model => model.key !== record.key));
              } else {
                if (mode === 'single') {
                  setSelectedModels([record]);
                  return;
                }
                setSelectedModels(prev => {
                  const newModels = [record, ...prev.filter(model => model.key !== record.key)];
                  return newModels.slice(0, showCandidate ? 3 : 1); // 只保留前3个
                });
              }
            }}
          >
            {isSelected ? '取消' : '选择'}
          </Button>
        );
      },
    });
    columns.splice(2, 0, {
      title: '综合得分',
      fixed: 'left',
      key: 'score',
      render: (_, record) => {
        const score = getScore(record, multiple);
        return <Rate allowHalf value={score} disabled style={{ fontSize: 16 }} />;
      },
      sorter: {
        compare: (a, b) => {
          if (Object.keys(multiple).length === 1) {
            return a[Object.keys(multiple)[0]] - b[Object.keys(multiple)[0]];
          }
          return getScore(a, multiple) - getScore(b, multiple);
        },
        multiple: 1,
      },
    })
  }

  return columns;
};

const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return '-';
  try {
    const date = dayjs(dateTimeStr);
    return date.format('YYYY-MM-DD');
  } catch (e) {
    return dateTimeStr;
  }
}; 