import React from 'react';
import { Table, Tag, Form, Select, Input, Rate } from 'antd';
import { AIDecisionButton } from './ai-decision';
import type { DataType } from './types';  // 需要创建这个类型文件

interface ModelModalContentProps {
  multiple: Record<string, number>;
  setMultiple: any;
  selectedModels: DataType[];
  setSelectedModels: any;
  searchText: string;
  setSearchText: (text: string) => void;
  sortedData: DataType[];
  data: DataType[];
  columns: any[];
  showOperation?: boolean;
  showCandidate?: boolean;
  pageSize?: number;
  disabledList: RegExp[];
  getScore: (record: DataType) => number;
  isDisabled: (disabledList: RegExp[], name: string) => boolean;
  mode?: string;
}

const ModelModalContent: React.FC<ModelModalContentProps> = ({
  multiple,
  setMultiple,
  selectedModels,
  setSelectedModels,
  searchText,
  setSearchText,
  showOperation = true,
  showCandidate = true,
  pageSize = 100,
  sortedData,
  data,
  columns,
  disabledList,
  getScore,
  isDisabled,
  mode
}) => {
  // 可选择的指标选项
  const multipleOptions = [
    { label: '费用', value: 'cost' },
    { label: '性能', value: 'performance' },
    { label: '输出速度', value: 'speed' },
    { label: '上下文', value: 'context' },
  ];

  // 处理指标选择变化
  const handleMultipleChange = (value: string[]) => {
    const newMultiple = {
      cost: 0,
      speed: 0,
      context: 0,
      performance: 0,
      ...multipleOptions.reduce((pre, cur) => ({
        ...pre,
        [cur.value]: value.includes(cur.value) ? 1 : 0
      }), {})
    };
    setMultiple(newMultiple);
  };

  const handleDecisionComplete = (selectedModelKeys: string[], scores: Record<string, number>) => {
    setSelectedModels(selectedModelKeys.map(key => sortedData.find(model => model.key === key)));
    setMultiple(scores);
  };

  return (
    <>
      <div style={{ display: 'flex', gap: 16, marginBottom: 0, position: 'relative' }}>
        <Form.Item label="关注指标" style={{ width: 400, margin: 0 }}>
          <Select
            mode="multiple"
            options={multipleOptions}
            value={Object.keys(multiple).filter(key => multiple[key])}
            onChange={handleMultipleChange}
          />
        </Form.Item>
        <Form.Item label="快速过滤" style={{ width: 500 }}>
          <Input
            placeholder="输入模型名称或供应筛选，例如openai, gpt……"
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            allowClear
          />
        </Form.Item>
        <AIDecisionButton
          modelData={data.filter(model =>
            model.enable !== false && !isDisabled(disabledList, model.name)
          )}
          onAnalysisChange={handleDecisionComplete}
        />
      </div>
      {showOperation && selectedModels.length > 0 && showCandidate && (
        <div style={{ marginBottom: 16, padding: 16, background: '#f5f5f5', borderRadius: 4 }}>
          {mode === 'single' ? <div style={{ marginBottom: 8 }}>当前选中：</div> : <div style={{ marginBottom: 8 }}>
            候选模型 ({selectedModels.length}/3)：第一个为默认使用模型，你可以手动点击其他候选模型作为默认模型。其余选中的模型会自动进入多模型测试中（功能开发中）。
          </div>}
          <div style={{ display: 'flex', gap: 8 }}>
            {selectedModels.map((model, index) => (
              <Tag
                key={model.key}
                closable
                color={index === 0 ? 'blue' : 'default'}
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  if (index !== 0) {
                    setSelectedModels((prev: any) => [
                      model,
                      ...prev.filter((m: any) => m.key !== model.key)
                    ]);
                  }
                }}
                onClose={() => setSelectedModels((prev: any) => prev.filter((m: any) => m.key !== model.key))}
              >
                {index === 0 && <span style={{ marginRight: 4 }}>★</span>}
                {model.name}
                {index === 0 && mode !=='single' && <span style={{ marginLeft: 4, fontSize: '12px', opacity: 0.8 }}>(默认)</span>}
              </Tag>
            ))}
          </div>
        </div>
      )}
      <Table<DataType>
        className="model-table"
        columns={columns}
        dataSource={sortedData}
        pagination={{
          pageSize,
          showSizeChanger: false
        }}
        rowClassName={(record) =>
          selectedModels.some(model => model.key === record.key) ? 'selected-row' : ''
        }
      />
    </>
  );
};

export default ModelModalContent; 