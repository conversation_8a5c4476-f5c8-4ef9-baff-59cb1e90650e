export interface DataType {
  key: React.Key;
  name: string;
  performance: number;
  cost: number;
  speed: number;
  context: number;
  description: string;
  provider: string;
  dataIndex?: string;
  tags?: string[];
  enable?: boolean;
  disableReason?: string;
  alias?: string;
  createdAt?: string;
  model_test?: {
    avg: number;
    creative: number;
    json: number;
    task: number;
  };
} 