import React from 'react';
import { CheckOutlined, ArrowDownOutlined } from '@ant-design/icons';
import './model-analysis.css';
import ScoreAdjuster from './score-adjuster';
import { Button, message, Spin } from 'antd';
import { DefaultModelApi } from '@/api/model-provider';
import { JSONParse } from '@/utils/common';
import { useGlobalState } from '@/hooks/useGlobalState';
import { getModelTestSpeed } from './utils';
import { GlobalModel } from '@/interface';

interface AnalysisData {
  taskType: string;
  tokenEstimation: string;
  focusReason: string;
  focus: {
    performance: number;
    cost: number;
    context: number;
    speed: number;
  };
  estimatedTokensPerRequest?: number;
  results?: {
    key: string;
    name: string;
    provider: string;
    providerKind: string;
    costCalculation: string;
    approximateCost: number;
    reason: string;
    relaxedRules?: string[];
  }[];
}

interface Model {
    key: string;
    name: string;
    performance: number;
    provider: string;
    tags: string[];
    context: number;
    cost: number;
    speed: number;
    model_test?: {
      avg: number;
    };
}

interface Focus {
    performance: number;
    cost: number;
    speed: number;
    context: number;
}

// 获取各项指标的具体要求
function getRequirements(focus: Focus) {
    return {
        performance: focus.performance === 0 ? 0 : 
                    focus.performance === 1 ? 3 :
                    focus.performance === 2 ? 4 : 5,
        cost: focus.cost === 0 ? Infinity :
              focus.cost === 1 ? 20 :
              focus.cost === 2 ? 10 : 5,
        speed: focus.speed === 0 ? 0 :
               focus.speed === 1 ? 2 :
               focus.speed === 2 ? 3 : 4,
        context: focus.context === 0 ? 0 :
                 focus.context === 1 ? 32 :
                 focus.context === 2 ? 64 : 100
    };
}

// 检查模型是否满足要求
function checkModelMeetsRequirements(model: Model, requirements: any) {
    const speed = getModelTestSpeed(model);
    return (
        (requirements.performance === 0 || model.performance >= requirements.performance) &&
        (requirements.cost === Infinity || model.cost <= requirements.cost) &&
        (speed === 0 || speed >= requirements.speed) &&
        (requirements.context === 0 || model.context > requirements.context)
    );
}

// 生成需要降级的模型列表
function generateRelaxedModels(model: Model, focus: Focus, requirements: any) {
    const relaxedRules = [];
    const speed = getModelTestSpeed(model);
    
    if (focus.speed > 0 && speed < requirements.speed) {
        relaxedRules.push(`speed: ${requirements.speed}->${speed}`);
    }
    if (focus.cost > 0 && model.cost > requirements.cost) {
        relaxedRules.push(`cost: ${requirements.cost}->${model.cost}`);
    }
    if (focus.context > 0 && model.context <= requirements.context) {
        relaxedRules.push(`context: >${requirements.context}k->${model.context}k`);
    }
    if (focus.performance > 0 && model.performance < requirements.performance) {
        relaxedRules.push(`performance: ${requirements.performance}->${model.performance}`);
    }

    return relaxedRules.length > 0 ? { ...model, relaxedRules } : null;
}

// 主函数
function filterModels(modelData: Model[], focus: Focus) {
    const requirements = getRequirements(focus);
    const matchModels = [];
    const relaxedModels = [];

    modelData.forEach(model => {
        if (checkModelMeetsRequirements(model, requirements)) {
            matchModels.push(model);  // Changed from model.key to model
        } else {
            const relaxedModel = generateRelaxedModels(model, focus, requirements);
            if (relaxedModel) {
                relaxedModels.push(relaxedModel);
            }
        }
    });

    return {
        matchModels,
        relaxedModels: relaxedModels.sort((a, b) => a.relaxedRules.length - b.relaxedRules.length)
    };
}

interface ModelAnalysisProps {
  data: AnalysisData;
  modelData: any;
  onSelectedModelsChange?: (selectedModels: string[], scores: any) => void;
}

const ModelAnalysis: React.FC<ModelAnalysisProps> = ({ 
  data, 
  modelData, 
  onSelectedModelsChange 
}) => {
  const { globalState } = useGlobalState();
  const { modelProviderList } = globalState;
  const providerImg = (modelProviderList || []).reduce((acc, curr) => {
    acc[curr.kind] = curr.icon;
    return acc;
  }, {});

  console.log("providerImg2...", providerImg, modelData, data);
  const [selectedModels, setSelectedModels] = React.useState<string[]>([]);
  const [scores, setScores] = React.useState(data.focus);
  const [analysisData, setAnalysisData] = React.useState(data);
  const [loading, setLoading] = React.useState(!data.results);

  const handleDecision = async () => {
    setLoading(true);
    
    try {
      const decisionData = {
        ...data,
        focus: scores,
      };

      // 先使用 filterModels 进行模型筛选
      const { matchModels, relaxedModels } = filterModels(modelData, scores);
      
      // 将筛选结果添加到决策数据中
      const enrichedDecisionData = {
        ...decisionData,
        matchModels,
        relaxedModels
      };

      const result = await DefaultModelApi.getAIDecision(enrichedDecisionData);
      const parsedContent = JSONParse(result[0].content);
      if (!parsedContent) {
        message.error('决策结果解析失败，请尝试重新决策');
        return;
      }
      setAnalysisData(prev => ({
        ...prev,
        ...parsedContent || {},
        matchModels, // 保存匹配的模型列表
        relaxedModels // 保存需要降级的模型列表
      }));
    } catch (error) {
      console.error('Decision error:', error);
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    if (!data.results) {
      handleDecision();
    }
  }, []);

  const handleScoresChange = (newScores: typeof data.focus) => {
    setScores(newScores);
    onSelectedModelsChange?.(selectedModels, newScores);
  };

  const handleModelSelect = (modelKey: string) => {
    setSelectedModels(prev => {
      const newSelected = prev.includes(modelKey)
        ? prev.filter(key => key !== modelKey)
        : prev.length < 3
          ? [...prev, modelKey]
          : prev;
      
      onSelectedModelsChange?.(newSelected, scores);
      return newSelected;
    });
  };


  // 添加指标名称映射
  const metricNames = {
    performance: '性能',
    cost: '成本',
    context: '上下文',
    speed: '速度'
  };

  // 添加指标检查函数
  const checkRelaxedMetric = (relaxedRules: string[] = [], metricName: string) => {
    return relaxedRules.some(rule => rule.startsWith(metricName));
  };

  return (
    <div className="model-analysis-container">
      <div className="analysis-header">
        <div className="analysis-summary">
          <p><strong>任务类型：</strong>{analysisData?.taskType}</p>
          <p><strong>任务分析：</strong>{analysisData?.focusReason}</p>
          <p><strong>Token预估：</strong>{analysisData?.tokenEstimation}</p>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <ScoreAdjuster initialScores={scores} onScoresChange={handleScoresChange} />
            <Button 
              type="primary" 
              onClick={handleDecision} 
              disabled={loading}
            >
              {loading ? '决策中...' : '重新决策'}
            </Button>
          </div>
        </div>
      </div>

      <div className="selection-counter">
        最多选择3个模型 ({selectedModels.length}/3)
      </div>

      <Spin spinning={loading}>
        <div className="model-cards">
          {(analysisData?.results || []).map(model => {
          return (
            <div
              key={model.key}
              className={`model-card ${selectedModels.includes(model.key) ? 'selected' : ''}`}
              onClick={() => handleModelSelect(model.key)}
            >
              <div className="select-indicator">
                <CheckOutlined style={{ opacity: selectedModels.includes(model.key) ? 1 : 0 }} />
              </div>
              <div className="model-icon">
                <img src={providerImg[model.providerKind]} alt={model.name} />
              </div>
              <h4 title={model.name}>{model.name}</h4>
              
              {/* 展示指标状态 */}
              <div className="model-metrics">
                {model.relaxedRules && model.relaxedRules.length > 0 ? (
                  // 有不满足的指标时显示具体项
                  Object.entries(metricNames).map(([key, label]) => {
                    if (checkRelaxedMetric(model.relaxedRules, key)) {
                      return (
                        <div key={key} className="metric-item">
                          <span className="metric-label">{label}:</span>
                          <span className="metric-value relaxed">
                            <ArrowDownOutlined style={{ color: '#ff4d4f' }} />
                          </span>
                        </div>
                      );
                    }
                    return null;
                  })
                ) : (
                  // 全满足时显示对钩
                  <div className="metric-item satisfied">
                    <span className="metric-label">所有条件</span>
                    <span className="metric-value">
                      <CheckOutlined style={{ color: '#52c41a' }} />
                    </span>
                  </div>
                )}
              </div>

              <div className="model-details">
                <p className="cost">预估费用: {model.approximateCost}元/天</p>
                <p className="reason" title={model.reason}>{model.reason}</p>
              </div>
            </div>
          )})}
        </div>
      </Spin>
    </div>
  );
};

export default ModelAnalysis; 