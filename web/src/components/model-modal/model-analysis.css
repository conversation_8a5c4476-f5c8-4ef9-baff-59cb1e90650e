.model-analysis-container {
    max-width: 900px;
    margin: 0 auto;
    height: 100%;
  }

  .model-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin-top: 16px;
  }

  .model-card {
    position: relative;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 130px;
    box-sizing: border-box;
  }

  .model-card:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .model-card.selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
  }

  .select-indicator {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .model-card.selected .select-indicator {
    background-color: #1890ff;
    color: white;
  }

  .model-icon {
    display: flex;
    justify-content: center;
    margin: 16px 0 8px;
  }

  .model-icon img {
    width: 40px;
    height: 40px;
  }

  h4 {
    text-align: center;
    margin: 8px 0;
    font-size: 14px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .model-details {
    font-size: 13px;
  }

  .cost {
    text-align: center;
    color: #1890ff;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .reason {
    color: #666;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

.analysis-header p {
    margin: 4px 0;
}

.model-metrics {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin: 4px 0;
  padding: 0 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 2px;
}

.metric-item.satisfied {
  color: #52c41a;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.metric-value.relaxed .anticon {
  font-size: 12px;
  color: #ff4d4f;
}

.metric-value .anticon {
  font-size: 12px;
}

/* 调整模型卡片其他相关样式 */
.model-card h4 {
  text-align: center;
  margin-bottom: 4px;
}

.model-details {
  text-align: center;
}

.model-details .cost {
  font-size: 12px;
  margin-bottom: 4px;
}

.model-details .reason {
  font-size: 12px;
  line-height: 1.4;
}

.selection-counter {
  text-align: center;
  color: #666;
  font-size: 12px;
  margin: 8px 0;
  padding: 0 12px;
}

/* 如果需要特别强调数字部分 */
.selection-counter span {
  color: #1677ff;
  font-weight: 500;
}