import React, { useState, useEffect } from 'react';
import { Table, Rate, Tag, Image, Button, Select, Modal, Form, Input, Empty, Radio, Spin, message, Popover } from 'antd';
import type { TableColumnsType } from 'antd';
import { useSafeState } from 'ahooks';
import { DefaultModelApi } from '@/api/model-provider';
import { useQuery } from '@/hooks/useQuery';
import { GlobalModel } from '@/interface';
import { useDebounce } from 'ahooks';
import { AIDecisionButton } from './ai-decision';
import { useGlobalState } from '@/hooks/useGlobalState';
import dayjs from 'dayjs';
import ModelModalContent from './model-modal-content';
import { getColumns, transformModelList } from './utils';

interface DataType {
  key: React.Key;
  name: string;
  performance: number;
  cost: number;
  speed: number;
  context: number;
  description: string;
  provider: string;
  dataIndex?: string;
  tags?: string[];
  enable?: boolean;
  disableReason?: string;
  alias?: string;
  createdAt?: string;
}

const texts = {
  cost: ['非常昂贵（>100元/M）', '昂贵（>50元/M）', '一般（>10元/M）', '较便宜（<10元/M）', '十分便宜（<2元/M）'],
  speed: ['很慢（<20 Tokens/s）', '一般（20~40 Tokens/s）', '较快（40~60 Tokens/s）', '很快（60~80 Tokens/s）', '非常快（>80 Tokens/s）'],
  performance: ['可以处理一般的文本生成/分类任务', '可以处理一般的文本生成/分类任务', '一般的逻辑推任务，答疑场景，日常对话', '需要一定的推理性任务，需要指令遵循', '多步推理，智能体，工具调用，强指令遵循'],
  context: ['8k', '16k', '32k', '128k', '>=200k']
};

const tagLabels: Record<string, string> = {
  'view': '图片视觉',
  'json': 'JSON模式',
  'web-search': '联网搜索'
};

const tagFilter = Object.keys(tagLabels);
interface ModelModalProps {
  children: React.ReactNode;
  modelList?: GlobalModel[];
  title: string;
  disabledList?: RegExp[];
  defaultValue?: {
    selectedModels: DataType[];
    multiple: {
      cost: number;
      speed: number;
      context: number;
      performance: number;
    };
  };
  onConfirm?: (data: {
    selectedModels: DataType[],
    multiple: {
      cost: number;
      speed: number;
      context: number;
      performance: number;
    }
  }) => void;
  required?: boolean;
  showCandidate?: boolean;
  //默认多选，既可以选3个，当值为single的时候没有模型池的概念，只能选一个
  mode?: string;
}

const isDisabled = (disabledList = [], name) => {
  return disabledList.some(l => {
    return l.test(name);
  });
};

const getDisabledStyles = (record: DataType, disabledList: RegExp[] = []) => {
  const isModelDisabled = isDisabled(disabledList, record.name);
  const isModelEnabled = record.enable !== false;
  const disableReason = record.disableReason;
  const disabledMessage = isModelDisabled
    ? '该模式下暂不可用，可以尝试在应用中使用该模型'
    : disableReason || '该模型暂不可用，请联系管理员录入模型账号';

  return {
    isDisabled: isModelDisabled || !isModelEnabled,
    styles: {
      opacity: (isModelDisabled || !isModelEnabled) ? 0.5 : 1,
      cursor: (isModelDisabled || !isModelEnabled) ? 'not-allowed' : 'default'
    },
    message: (!isModelEnabled || isModelDisabled) ? disabledMessage : record.description
  };
};

const DisabledWrapper = ({ children, message, styles }) => {
  if (!message) {
    return <div style={styles}>{children}</div>;
  }

  return (
    <Popover content={message}>
      <div style={styles}>{children}</div>
    </Popover>
  );
};


const ModelModal: React.FC<ModelModalProps> = ({ children, title, onConfirm, defaultValue, disabledList, modelList, required = false, showCandidate = true, mode = "multiple" }) => {
  const { globalState } = useGlobalState();
  const [data, setData] = useState<any[]>(() => {
    const transformedData = transformModelList(modelList || [])
    return transformedData;
  });
  const { parsedQuery } = useQuery();
  const [multiple, setMultiple] = useSafeState(defaultValue?.multiple || {
    cost: 1,
    speed: 1,
    context: 1,
    performance: 1,
  });
  const [open, setOpen] = useSafeState(false);
  // Add new state for selected models
  const [selectedModels, setSelectedModels] = useState<DataType[]>(() => {
    if (defaultValue?.selectedModels) {
      // 确保 defaultValue 中的模型在数据加载后能找到对应的完整数据
      return defaultValue.selectedModels;
    }
    if (!showCandidate) {
      return data.filter(model => model.enable !== false && !isDisabled(disabledList, model.name));
    }
    return [];
  });

  const [searchText, setSearchText] = useState('');
  const debouncedSearchText = useDebounce(searchText, { wait: 300 });

  useEffect(() => {
    if (data?.length === 0) {
      DefaultModelApi.listGlobalModel({
        workspaceId: parsedQuery.workspaceId as string,
        groupId: parsedQuery.groupId as string
      }).then((res) => {
        const transformedData = transformModelList(res);
        setData(transformedData);
      });
    }
  }, [parsedQuery.workspaceId, parsedQuery.groupId]);

  // 数据加载后更新 selectedModels 的完整信息
  useEffect(() => {
    if (defaultValue?.selectedModels && data.length > 0) {
      const updatedModels = defaultValue.selectedModels.map(model =>
        data.find(item => item.name === model.name) || model
      );
      setSelectedModels(updatedModels);
    }
  }, [data, defaultValue?.selectedModels]);

  const handleOk = () => {
    if (required && selectedModels?.length < 1) {
      message.error('必须选择一个模型');
      return false;
    }
    onConfirm?.({
      selectedModels,
      multiple
    });
    setSelectedModels([]);
    setOpen(false);
  };
  const handleCancel = () => {
    setSelectedModels([]);
    setOpen(false);
  };

  const handleOpen = () => {
    setOpen(true);
  }

  const getContextValue = (v: number): number => {
    if (v <= 8) {
      return 1;
    } else if (v <= 16) {
      return 2;
    } else if (v <= 32) {
      return 3;
    } else if (v <= 128) {
      return 4;
    } else {
      return 5;
    }
  };

  // 表格列定义
  const columns: TableColumnsType<DataType> = getColumns({
    showCandidate,
    data,
    multiple,
    selectedModels,
    setSelectedModels,
    disabledList,
    globalState,
    mode
  });

  const newColumns = columns.slice().sort((a, b) => {
    const aWeight = a.fixed ? 10 : multiple[(a as DataType)?.dataIndex || ''] || 0;
    const bWeight = b.fixed ? 10 : multiple[(b as DataType)?.dataIndex || ''] || 0;
    return bWeight - aWeight;
  });

  const getScore = (record: DataType): number => {
    const activeWeights = Object.values(multiple).filter(Boolean).length;
    if (activeWeights === 0) return 0;

    return Object.keys(multiple).reduce((pre, key) => {
      if (multiple[key] === 0) return pre;

      if (key === 'context') {
        return pre + getContextValue(record[key]);
      }
      if (key === 'cost') {
        return pre + getCostValue(record[key]);
      }
      return pre + record[key];
    }, 0) / activeWeights;
  }

  // Modify sortedData to include search filter
  const sortedData = React.useMemo(() => {
    let filteredData = [...data];

    if (debouncedSearchText) {
      const searchLower = debouncedSearchText.toLowerCase();
      filteredData = filteredData.filter(item =>
        item.alias?.toLowerCase().includes(searchLower) ||
        item.name.toLowerCase().includes(searchLower) ||
        item.provider.toLowerCase().includes(searchLower)
      );
    }

    return filteredData.sort((a, b) => getScore(b) - getScore(a));
  }, [multiple, data, debouncedSearchText]);

  // const handleDecisionComplete = (selectedModelKeys, scores) => {
  //   // 1. 选中对应的模型
  //   setSelectedModels(selectedModelKeys.map(key => sortedData.find(model => model.key === key)));

  //   // 2. 处理关指标
  //   setMultiple(scores);
  // };

  return (
    <>
      <Modal
        className='model-modal'
        title={title}
        open={open}
        onOk={handleOk}
        onCancel={handleCancel}
        width="100%"
        style={{ top: 0, maxWidth: '100%', margin: 0, padding: 0, position: 'fixed' }}
        styles={{ body: { height: 'calc(100vh - 110px)', overflow: 'auto' } }}
      >
        <ModelModalContent
          mode={mode}
          multiple={multiple}
          showCandidate={showCandidate}
          setMultiple={setMultiple}
          selectedModels={selectedModels}
          setSelectedModels={setSelectedModels}
          searchText={searchText}
          setSearchText={setSearchText}
          sortedData={sortedData}
          data={data}
          columns={newColumns}
          disabledList={disabledList}
          getScore={getScore}
          isDisabled={isDisabled}
          pageSize={100}
        />
      </Modal>
      <div onClick={handleOpen}>
        {children}
      </div>
    </>
  )
}

const getCostValue = (avgFee: number): number => {
  if (avgFee <= 1) {
    return 5;
  } else if (avgFee <= 5) {
    return 4;
  } else if (avgFee <= 10) {
    return 3;
  } else if (avgFee <= 50) {
    return 2;
  } else {
    return 1;
  }
};

// 更新格式化日期时间的函数
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return '-';

  try {
    const date = dayjs(dateTimeStr);
    return date.format('YYYY-MM-DD');
  } catch (e) {
    return dateTimeStr;
  }
};

export default ModelModal;