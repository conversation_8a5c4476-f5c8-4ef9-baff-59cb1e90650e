import React, { useState, useEffect } from 'react';
import { Button, Select, Modal, Form, Input, Radio, Spin, message } from 'antd';
import { DefaultModelApi } from '@/api/model-provider';
import { JSONParse } from '@/utils/common';
import { MagicIcon } from '../icons';
import ModelAnalysis from './model-analysis';

// Add new AI Decision Modal component
const AIDecisionModal = ({ 
  open, 
  onCancel, 
  modelData,
  onConfirm 
}: {
  open: boolean;
  onCancel: () => void;
  modelData: any;
  onConfirm: (models: string[], scores: any) => void;
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [currentScores, setCurrentScores] = useState<any>(null);

  useEffect(() => {
    if (open) {
      setResult(null);
      setSelectedModels([]);
      
      form.resetFields();
      form.setFieldsValue({
        dailyVolume: '1000',
        costSensitivity: 'sensitive'
      });
    }
  }, [open]);

  const handleReset = () => {
    form.resetFields();
    setResult(null);
  };

  const handleDecision = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      try {
        const response = await DefaultModelApi.getAIAnalysis({
          ...values
        });
        const res = JSONParse(response[0].content);
        console.log('res', res, response);
        setResult({...res, dailyVolume: values.dailyVolume });
      } catch (error) {
        message.error('决策失败，请重试');
        console.error('AI Decision error:', error);
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  const handleSelectedModelsChange = (models: string[], scores: any) => {
    setSelectedModels(models);
    setCurrentScores(scores);
  };

  return (
    <Modal
      title="AI 决策助手"
      open={open}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      width={900}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          onClick={() => {
            if (selectedModels.length === 0) {
              message.warning('请至少选择一个模型');
              return;
            }
            onConfirm(selectedModels, currentScores);
            onCancel(); // 关闭弹窗
          }}
        >
          确定
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        initialValues={{
          dailyVolume: '1000',
          costSensitivity: 'sensitive'
        }}
      >
        <Form.Item
          name="description"
          label="需求描述"
          rules={[{ required: true, message: '请输入需求描述' }]}
        >
          <Input.TextArea
            rows={4}
            placeholder="请描述您的具体需求..."
          />
        </Form.Item>

        <Form.Item
          name="dailyVolume"
          label="日调用量"
          rules={[{ required: true, message: '请选择日调用量' }]}
        >
          <Select placeholder="请选择日调用量">
            <Select.Option value="1000">{'<1000'}</Select.Option>
            <Select.Option value="1k">1k+</Select.Option>
            <Select.Option value="10k">1w+</Select.Option>
            <Select.Option value="100k">10w+</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="costSensitivity"
          label="成本敏感"
          rules={[{ required: true, message: '请选择成本敏感度' }]}
        >
          <Radio.Group style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <Radio value="sensitive">敏感</Radio>
              <Radio value="insensitive">不敏感</Radio>
            </div>
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button onClick={handleReset}>重置</Button>
              <Button type="primary" onClick={handleDecision}>
                开始分析
              </Button>
            </div>
          </Radio.Group>
        </Form.Item>

        <div style={{
          minHeight: '200px',
          background: '#f5f5f5',
          borderRadius: '8px',
          padding: '16px'
        }}>
          {loading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '340px'
            }}>
              <Spin tip="AI正在决策中..." />
            </div>
          ) : result ? (
            <div>
              <ModelAnalysis 
                data={result} 
                modelData={modelData} 
                onSelectedModelsChange={handleSelectedModelsChange}
              />
            </div>
          ) : (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '340px'
            }}>
              <img 
                src="https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56293344902/5b51/cc2b/8d50/0cbf4548c335bf7674558ae84779e182.png"
                alt="暂无决策结果"
                style={{ height: '200px' }}
              />
            </div>
          )}
        </div>
      </Form>
    </Modal>
  );
};

// Add new AI Decision Button component
interface AIDecisionButtonProps {
  modelData: any;
  onAnalysisChange?: (selectedModels: string[], scores: any) => void;
}

export const AIDecisionButton: React.FC<AIDecisionButtonProps> = ({ 
  modelData,
  onAnalysisChange 
}) => {
  const [aiModalOpen, setAiModalOpen] = useState(false);

  const handleConfirm = (selectedModels: string[], scores: any) => {
    onAnalysisChange?.(selectedModels, scores);
    setAiModalOpen(false);
  };

  return (
    <>
      <div
        onClick={() => setAiModalOpen(true)}
        style={{
          display: 'inline-flex',
          position: 'absolute',
          right: '0',
          top: 10,
          alignItems: 'center',
          gap: '4px',
          color: '#1677ff',
          cursor: 'pointer',
          fontSize: '14px',
          float: 'right',
          marginBottom: '16px'
        }}
      >
        <MagicIcon />
        <span className='magic-icon'>
          <span>AI决策</span>
        </span>
      </div>
      <AIDecisionModal
        open={aiModalOpen}
        onCancel={() => setAiModalOpen(false)}
        modelData={modelData}
        onConfirm={handleConfirm}
      />
    </>
  );
}; 