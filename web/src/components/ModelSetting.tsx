import { RobotOutlined, CloseOutlined, ExportOutlined } from '@ant-design/icons';
import { Button, Form, Popover, Radio, Space, Tooltip } from 'antd';
import { isEqual } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';

import { LForm, LFormSwitch } from '@/components/form';
import { LFormSliderInput } from '@/components/form/form-items/form-range';
import { TextTip } from '@/components/text-tip';
import { getModelConfigSchema } from '@/utils/model-helper';
import { useGlobalState } from '@/hooks/useGlobalState';
import ModelModal from './model-modal';
import { useLocalStorage } from '@/hooks/useStorage';
import { saveLocalData } from '@/utils/common';

interface ModelParams {
  top_p?: number;
  temperature?: number;
  max_tokens?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
}

const lively: ModelParams = {
  temperature: 0.8,
  top_p: 0.9,
  presence_penalty: 0.1,
  frequency_penalty: 0.1,
  max_tokens: 4096,
};

const balanced: ModelParams = {
  temperature: 1,
  top_p: 0.85,
  presence_penalty: 0.0,
  frequency_penalty: 0.0,
  max_tokens: 4096,
};

const exact: ModelParams = {
  temperature: 0.2,
  top_p: 0.75,
  presence_penalty: 0.5,
  frequency_penalty: 0.5,
  max_tokens: 4096,
};

interface ModelPredefinedParamsProps {
  onClick?: (param: ModelParams) => void;
  defaultValue?: 'balanced' | 'lively' | 'exact';
}

export const ModelPredefinedParams = (props: ModelPredefinedParamsProps) => {
  const { defaultValue, onClick: onClickProps } = props;
  const [value, setValue] = useState(defaultValue);

  const onClick = useCallback((value: string) => {
    if (onClickProps) {
      // @ts-ignore
      setValue(value);
      if (value === 'balanced') {
        onClickProps(balanced);
      } else if (value === 'lively') {
        onClickProps(lively);
      } else {
        onClickProps(exact);
      }
    }
  }, []);

  useEffect(() => {
    if (defaultValue) {
      onClick(defaultValue);
    }
  }, [defaultValue]);

  return (
    <Radio.Group value={value} size="large" onChange={(e) => onClick(e.target.value)} style={{ visibility: 'hidden' }}>
      <Radio.Button key="lively" value="lively">
        活泼
      </Radio.Button>
      <Radio.Button key="balanced" value="balanced">
        平衡
      </Radio.Button>
      <Radio.Button key="exact" value="exact">
        精准
      </Radio.Button>
    </Radio.Group>
  );
};

export interface IModelSettingProps {
  modelName: string;
  modelParams: ModelParams;
  providerKind: string;
}

export interface IProps {
  value: IModelSettingProps;
  onChange: any;
  destroyModal?: boolean;
  disabledList?: RegExp[];
  style?: React.CSSProperties;
  onConfirm?: (value: IModelSettingProps) => void;
  isChatExplore?: boolean;
}

interface ISettingContentProps extends IProps {
  onConfirm?: (value: IModelSettingProps) => void;
  isChatExplore?: boolean;
}

const isDisabled = (disabledList = [], name) => {
  return disabledList.some(l => {
    return l.test(name);
  });
};

function SettingContent(props: ISettingContentProps) {
  const [form] = Form.useForm();
  const { value, onChange, disabledList, onConfirm, isChatExplore } = props;
  const [editModelName, setEditModelName] = useState(value.modelName);
  const { globalState } = useGlobalState();
  const { app, modelList } = globalState;
  const [localConfig] = useLocalStorage(`app-${app?.id}`);
  const modelSelectSettings = useMemo(() => {
    if (!isChatExplore && localConfig?.modelSelectSettings) {
      return localConfig?.modelSelectSettings;
    }
    return {
      selectedModels: editModelName ? [{
        key: editModelName,
        name: editModelName,
        // 其他必要的字段可以从 modelList 中获取
        ...modelList.find(m => m.name === editModelName)
      }] : [],
      multiple: {
        cost: 1,
        speed: 1,
        context: 1,
        performance: 1
      }
    }
  }, [localConfig, editModelName]);

  useEffect(() => {
    const params = value.modelParams || {};
    const targetModel = modelList.find((it) => it.name === value.modelName);
    const schema = getModelConfigSchema(targetModel, app?.type);
    // 把所有的值都替换成合理值
    schema.map((schemaConfig) => {
      const {
        paramKey,
        validRange,
        default: initialValues,
      } = schemaConfig;

      const { range: backendRange, default: backendDefault } =
        targetModel?.config[paramKey] || {};
      const range = backendRange || validRange;
      // @ts-ignore
      let defaultValue = params[paramKey] ?? backendDefault ?? initialValues;
      // 要看范围是否正常，如果异常，需要切换会正常值
      if (defaultValue < range[0] || defaultValue > range[1]) {
        defaultValue = backendDefault ?? initialValues;
      }
      params[paramKey] = defaultValue
    });

    form.setFieldsValue(value.modelParams);
  }, [value.modelParams, value.modelName, modelList])

  const handleModelChange = (name?, value?) => {
    console.log('handleModelChange.name',name,'val',value);
    let modelName = editModelName;
    let modelParams = form.getFieldsValue();

    if (name === 'modelName') {
      const selectedModel = value.selectedModels[0];
      setEditModelName(selectedModel.name);
      modelName = selectedModel.name;

      saveLocalData(`app-${app?.id}`, {
        ...localConfig || {},
        modelSelectSettings: value
      });
    }

    const targetModel = modelList.find((it) => it.name === modelName);
    console.log("targetModel", targetModel, modelName);
    onChange({
      modelName,
      modelParams,
      providerKind: targetModel?.providerKind || 'openai',
      model: targetModel,
    });
  }

  const handleConfirm = () => {
    const targetModel = modelList.find((it) => it.name === editModelName);
    console.log('handleConfirm', editModelName, targetModel);
    onConfirm({
      modelName: editModelName,
      modelParams: form.getFieldsValue(),
      providerKind: targetModel?.providerKind || 'openai',
    });
  }

  const renderFormItem = useMemo(() => {
    const targetModel = modelList.find((it) => it.name === editModelName);
    const schema = getModelConfigSchema(targetModel, app?.type);
    console.log("schema", schema, app);
    return schema.map((schemaConfig) => {
      const {
        paramKey,
        paramName,
        description,
        validRange,
        type,
        default: initialValues,
        step = 0.01,
      } = schemaConfig;

      const { range: backendRange } =
        targetModel?.config[paramKey] || {};
      const range = backendRange || validRange;
      const label = (
        <TextTip
          text={`${paramName} ${paramKey}`}
          tip={
            <div>
              {(description || []).map((it, idx) => (
                <p key={`description-${idx}`}>{it}</p>
              ))}{' '}
            </div>
          }
        />
      ) as any

      if (type === 'switch') {
        return <LFormSwitch
          name={paramKey}
          label={label}
        ></LFormSwitch>
      }

      return (
        <LFormSliderInput
          key={`sliderInput-${paramKey}`}
          name={paramKey}
          label={label}
          min={range[0]}
          max={range[1]}
          step={step}
        // defaultValue={defaultValue}
        />
      );
    });
  }, [editModelName, modelList]);

  const editModal = useMemo(() => {
    const find = modelList.find((it) => it.name === editModelName);
    return find;
  }, [editModelName, modelList]);

  return (
    <>
      <StyledContent>
        <div className="row">
          <span className="title">语言模型</span>
          {/* <ModelSelect
              value={editModelName}
              onChange={v => handleModelChange('modelName', v)}
              disabledList={disabledList}
            ></ModelSelect> */}
          <ModelModal
            title="选择模型"
            modelList={modelList}
            disabledList={disabledList}
            onConfirm={(data) => handleModelChange('modelName', data)}
            defaultValue={modelSelectSettings}
          >
            <div style={{
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              padding: '4px 11px',
              cursor: 'pointer',
              minWidth: '120px',
              display: 'inline-block',
              lineHeight: '22px',
            }}>
              {editModelName} <RobotOutlined />
            </div>
          </ModelModal>
        </div>
        {editModal ?
          <Desc>
            {editModal.description}
            <p className='fee'>费用: 输入{editModal.fee.input}元 | 输出{editModal.fee.output}元 (/百万Token) <a href={editModal.url} target='_blank'>模型相关介绍</a></p>
          </Desc>
          : null}
        <hr />
        <div>
          <span className="title">
            <Space>
              模型设置
              <Tooltip title={<>
                <a
                  rel='noopener'
                  target='_blank'
                  href='https://music-doc.st.netease.com/st/langbase-doc/guide/core#15-%E6%A8%A1%E5%9E%8B%E8%AE%BE%E7%BD%AE'>查看详情</a>
              </>}>
                <a
                  rel='noopener'
                  target='_blank'
                  href='https://music-doc.st.netease.com/st/langbase-doc/guide/core#15-%E6%A8%A1%E5%9E%8B%E8%AE%BE%E7%BD%AE'
                  style={{
                    color: '#999'
                  }}
                  >
                  <ExportOutlined style={{ fontSize: 12 }} />
                </a>
              </Tooltip>
            </Space>
          </span>
          <LForm
            form={form}
            // defaultValue={value.modelParams as any}
            labelCol={{ span: 12 }}
            wrapperCol={{ span: 12 }}
            labelAlign="left"
            onFieldsChange={handleModelChange}
          >
            {renderFormItem}
          </LForm>
        </div>
        {onConfirm &&
          <div>
            <Button type="primary" onClick={handleConfirm}>确定</Button>
          </div>
        }
      </StyledContent>
    </>
  );
}

export function ModelSetting(props: IProps) {
  const { value, onChange, destroyModal = false, disabledList = [], onConfirm, style, isChatExplore = false } = props;
  const [open, setOpen] = useState(false);

  const handleChange = (v) => {
    if (!isEqual(v, value)) {
      console.log("save", v, value);
      onChange && onChange(v);
    }
  }

  const handleConfirm = (data) => {
    setOpen(false);
    onConfirm(data);
  }

  return (
    <>
      <Popover
        title={
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>模型及参数</span>
            <Button
              type="text"
              size="small"
              onClick={() => setOpen(false)}
            >
              <CloseOutlined />
            </Button>
          </div>
        }
        destroyTooltipOnHide={destroyModal}
        open={open}
        trigger="click"
        arrow={false}
        content={
          <SettingContent
            value={value}
            onChange={handleChange}
            disabledList={disabledList}
            onConfirm={handleConfirm}
            isChatExplore={isChatExplore}
          />
        }
      >
        <Button onClick={() => setOpen(!open)} style={style}>
          {value?.modelName} <RobotOutlined rev={undefined} />
        </Button>
      </Popover>
    </>
  );
}

const StyledContent = styled.div`
  width: 500px;
  display: flex;
  flex-direction: column;
  padding: 10px;

  .title {
    font-size: 16px;
    font-weight: 500;
  }

  .row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }

  .providerKind {
    color: #eee;
    font-size: 14px;
  }

  hr {
    width: 100%;
    border-color: rgba(255, 255, 255, 0.3);
    margin: 20px 0px;
  }
`;

const Desc = styled.pre`
  white-space: pre-line;
  color: #999;
  font-size: 12px;
  margin: 0;

  .fee {
    margin: 0;
    margin-top: 5px;
    color: #da4712;
  }
`;
