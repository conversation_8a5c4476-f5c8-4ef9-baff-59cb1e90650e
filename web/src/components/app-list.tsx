import { Col, Empty, Pagination, Row, ConfigProvider, Spin, message, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';

import { AppApi } from '@/api/app';
import { LayoutContainer } from '@/components/layout-container';
import { AppCard } from '@/components/resource-card';
import { usePathQuery, useQuery } from '@/hooks/useQuery';
import { AppModel, IAppType } from '@/interface';
// import { CreateAppCard } from '@/pages/home/<USER>/create-app';
import { AppListSearch } from '@/components/AppListSearch';
import { useGlobalState } from '@/hooks/useGlobalState';
import { NoAuthTip } from './common/NoAuthTip';
import { CopyOutlined, StarFilled, StarOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import AppCopyModal from './app-copy-modal';
import { MultipleConfigApi } from '@/api/multiple-setting';
import { env, getLocalData, saveLocalData } from '@/utils/common';
import { useLocalStorage } from '@/hooks/useStorage';
import { getCurrentUseAppKey } from '@/utils/localStorageKey';
import { templateIdMap } from '@/constants';

const PAGE_SIZE = 24;

const AppCardCol = styled(Col)`
  &:hover {
    .copy {
      display: flex;
    }
    .unstar-collect {
      display: flex;
    }
  }
`;

const TopCircle = styled.div`
  position: absolute;
  top: -15px;
  background: #fff;
  right: 5px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  box-shadow: 0px -1px 6px 1px #92909047;
  justify-content: center;
  align-items: center;
  display: none;
  cursor: pointer;
`;

const CopyCircle = styled(TopCircle)`
  color: #1677ff;
  right: 45px;
`;

const CollectCircle = styled(TopCircle)`
  color: #faad14;
  /* background-color: #faad14; */
  /* right: 45px; */
  &.star-collect {
    display: flex !important;
  }
`;

type IAppListProps = {
  isMine?: boolean;
};

const memoCache = (obj) => {
  let cache = '';
  if (JSON.stringify(obj) === cache) {
    return false;
  }
  cache = JSON.stringify(obj);
  return true;
}

export function AppList(props: IAppListProps) {
  const { isMine } = props;
  const { parsedQuery } = useQuery();
  const { globalState } = useGlobalState();
  const { user, group } = globalState;
  const [appList, setAppList] = useState<AppModel[]>([]);
  const [total, setTotal] = useState(0); // 总数
  const [pageNo, setPageNo] = useState(1);
  const [loading, setLoading] = useState(false);
  const [pageSize, setPageSize] = useState(PAGE_SIZE);
  const [appType, setAppType] = useState<IAppType>();
  const [subType, setSubType] = useState<string>();
  const [userType, setUserType] = useState<string>(isMine ? 'mine' : 'all');
  const [noAuth, setNoAuth] = useState(false);
  const [appSearch, setAppSearch] = useState<string>();
  const [useType, setUseType] = useState();

  const groupId = parsedQuery.groupId as string;
  const workspaceId = parsedQuery.workspaceId as string;
  const { pushAddQuery } = usePathQuery();
  const [messageApi, contextHolder] = message.useMessage();

  const appCopyModalRef = useRef();

  const localCurrentUseAppKey = getCurrentUseAppKey({ userId: user.id });
  const [currentUseList] = useLocalStorage(localCurrentUseAppKey);

  // const [admins, setAdmins] = useState<Member[]>([]);

  // useEffect(() => {
  //   if (groupId) {
  //     GroupApi.getGroupAdmin(groupId).then(setAdmins);
  //   }
  // }, [groupId]);

  const { run: createApp, loading: copyLoading } = useRequest(
    async (app) => {
      const { id, app_config_id, createdAt, createdBy, updatedAt, updatedBy, ...rest } = app;
      console.log('appappapp', app);
      // return;
      // const type = app.type;
      // 下面这个defaultConfig不会使用，因为template最后会覆盖，但是defaultConfig有些结构是入参校验需要通过的，所以下面一行保留即可
      // const defaultConfig: any = getDefaultConfig(type, app);

      // const defaultTemplate = getDefaultTemplateConfig(type);
      // if (defaultTemplate || template?.id) {
      //   app.type = type;
      //   if (template?.name) {
      //     app.subType = template.name;
      //   }
      //   defaultConfig.templateId = defaultTemplate || template?.id;
      // }

      if (app.type === IAppType.AgentWorkflow || app.type === IAppType.Workflow) {
        const configDetail = await AppApi.getAppConfigSnapshot(app.id);
        console.log('configDetail', configDetail);

        const newConfig = {
          ...(configDetail?.[0]?.config || {}),
        }

        if (app.type === IAppType.Workflow) {
          newConfig.workflowId = app.workflowId;
        }

        const appRes = await AppApi.createApp(
          {
            ...rest,
            config: newConfig
          },
          app.groupID
        );

        return appRes;

      }

      const appRes = await AppApi.createApp(
        {
          ...rest,
          // name: `${app.name}-副本-${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
          // @ts-ignore
          // groupID: app.groupID,
          // config: {
          //   templateId: app.id
          //   // ...defaultConfig,
          //   // businessConfig: templateConfigForm.getFieldsValue(true),
          // },
        },
        app.groupID
      );

      console.log('appRes', appRes);

      if (app.type === IAppType.VirtualHuman) {
        const settingRes = await createSetting({
          copyAppId: app.id,
          id: appRes.id,
          type: app.type,
        });

        console.log('settingRes', settingRes);

        return settingRes;

      }
      return appRes;
    },
    {
      manual: true,
      onSuccess: (data) => {
        // @ts-ignore
        appCopyModalRef.current?.close();
        messageApi.open({
          type: 'success',
          content: '应用创建成功',
        });
        pushAddQuery('/app/dev', {
          appId: data.id,
          type: data.type,
        });
      },
      onError: () => {
        // @ts-ignore
        appCopyModalRef.current?.setLoading(false);
      }
    },
  );

  const { runAsync: createSetting } = useRequest(async (app) => {
    // 获取新创建的app的默认配置
    const appRes = await MultipleConfigApi.getSettingList({ appId: app.id });

    console.log('获取新创建的 appRes', appRes);


    // 获取所有配置
    const copyAppRes = await MultipleConfigApi.getSettingList({ appId: app.copyAppId });
    console.log('获取所有配置 copyAppRes', copyAppRes);

    const results = await Promise.allSettled(copyAppRes?.items?.map(async item => {
      // 获取每一个setting的配置
      const copySettingRes = await MultipleConfigApi.getSettingVersionedConfig({
        appId: app.copyAppId,
        settingId: item.settingId,
        page: 1,
        pageSize: 1
      });

      console.log('获取每一个setting的配置 copySettingRes', copySettingRes);

      // 创建配置id
      const settingRes = await MultipleConfigApi.saveSetting({
        appId: app.id,
        name: item.name,
        description: item.name,
        // config: copySettingRes.config
      });

      console.log('创建配置 settingRes', settingRes, 'settingId', settingRes.settingId);

      // 保存配置config
      const configRes = await AppApi.saveSettingConfig({
        appId: app.id,
        settingId: settingRes.settingId,
        config: copySettingRes?.list?.[0]?.config
      });

      console.log('保存配置config configRes', configRes);

      // 删除创建app的时候默认创建的第一个settingId
      const res = await MultipleConfigApi.removeSetting({
        settingId: appRes?.items?.[0]?.settingId,
        appId: app.id
      });
      console.log('删除配置 res', res);
      return res;
    }));

    const allFinished = results.length === appRes?.items?.length; // 确保全部完成
    console.log('全部完成，状态:', allFinished, 'results', results);

    return {
      id: app.id,
      type: app.type
    }
  }, {
    manual: true
  });

  const changeSize = () => {
    const width = window.innerWidth
    if (width < 990) {
      setPageSize(9);
    } else if (width < 1700) {
      setPageSize(16);
    } else {
      setPageSize(PAGE_SIZE);
    }
  }

  useEffect(() => {
    setUserType(isMine ? 'mine' : 'all');
  }, [isMine]);

  useEffect(() => {
    window.addEventListener('resize', () => {
      changeSize();
    })
    changeSize();
  }, []);


  const refresh = (page?, size?) => {
    if (size) {
      setPageSize(size);
    }
    if (!workspaceId) {
      return;
    }
    let action: any = null;
    let type: any = [appType];
    // if (appType) {
    //   type = appType === IAppType.AgentConversation ? [IAppType.AgentConversation, IAppType.AgentWorkflow] : [appType];
    // }
    const isTemplate = templateIdMap[env] === groupId;
    console.log('isTemplate', isTemplate);
    setLoading(true);
    if (groupId) {
      action = AppApi.listAppsByGroup(groupId, {
        appType: type,
        name: appSearch,
        subType,
        showAll: isTemplate,
        userID: userType === 'mine' ? user?.id : '',
        useType,
        pageSize: size ?? pageSize,
        pageNumber: page || pageNo,
      }).then((res) => {
        setLoading(false);
        setNoAuth(false);
        return res;
      }).catch((err) => {
        setLoading(false);
        if (err?.response?.status === 403) {
          setNoAuth(true);
          setAppList([]);
        }
      })
    } else {
      const params = {
        appType: type,
        subType,
        name: appSearch,
        userID: userType === 'mine' ? user?.id : '',
        pageSize: size ?? pageSize,
        pageNumber: page || pageNo,
        useType,
      };

      if (useType === 'star') {
        params.userID = '';
      } else {
        params.userID = userType === 'mine' ? user?.id : ''
      }

      action = AppApi.listAppsByWorkspace(workspaceId, params);
    }
    setPageNo(page);
    action.then((res) => {
      const { items, total } = res;
      setAppList(items);
      setTotal(total);
    })
      .finally(() => {
        setLoading(false);
      });
  }

  useEffect(() => {

    if (useType === 'recent-view') {
      onUseFilter(useType)
      return;
    }
    if (memoCache({ groupId, workspaceId, appType, appSearch, pageSize, userType, subType, useType })) {
      // 默认刷新到第一页
      refresh(1);
    }
  }, [groupId, workspaceId, appType, appSearch, pageSize, userType, subType, useType]);

  const onSearch = (search?: string, appType?: IAppType, type?: string, subType?: string) => {
    setAppType(appType);
    setSubType(subType);
    setAppSearch(search);

    setUserType(type || '');
  }

  const onCopy = async val => {

    const appDetail = await AppApi.getAppDetail(val.id);

    console.log('appDetail', appDetail);

    // if (val.type === IAppType.VirtualHuman) {
    //   return;
    // }

    // @ts-ignore
    const { createdAt, createdBy, updatedAt, updatedBy, app_config_id, name, description, ...restProps } = appDetail;

    if (restProps.type === IAppType.VirtualHuman) {
      restProps.config = {}
    }

    // @ts-ignore
    appCopyModalRef.current.show({
      name: `${name}-副本`,
      description,
      // config: {},
      ...restProps
    });
  };

  const onStar = async (star, appData) => {

    const starService = star ? AppApi.starApp : AppApi.unStarApp;

    const res = await starService({ appId: appData?.id });

    if (res) {
      const newAppList = appList?.map(app => {
        if (app.id === appData?.id) {
          return {
            ...app,
            starred: star
          }
        }
        return app;
      });
      setAppList(newAppList);
      message.success(star ? '收藏成功' : '取消收藏成功');
    }

    // const localData = getLocalData(localStarAppKey) || [];
    // let newData = [...localData];

    // if (star) {
    //   // 是否已经存在
    //   const index = localData?.findIndex(app => app.id === appData.id);
    //   if (index > -1) return;
    //   newData.push({
    //     ...(appData || {}),
    //     groupID: groupId
    //   });
    //   saveLocalData(localStarAppKey, newData);
    // } else {
    //   newData = localData?.filter(app => app.id !== appData.id);
    //   saveLocalData(localStarAppKey, newData);
    // }
    // if (useType === 'star') {
    //   setAppList(newData);
    // }
  };

  const onUseFilter = val => {

    if (val === 'all') {
      setUseType(undefined);

      // refresh(1);
      return;
    }
    setUseType(val);
    if (val === 'recent-view') {
      setAppList(currentUseList?.filter(app => {
        if (!isMine && groupId && app.groupID !== groupId) {
          return false;
        }
        // if (isMine && app.createdBy?.id !== user.id) {
        //   return false;
        // }
        if (appType && appType !== app.type) {
          return false;
        }

        if (subType && subType !== app.subType) {
          return false;
        }

        return true
      }));
      return;
    }
    // refresh(1);
  };

  if (noAuth) {
    return <NoAuthTip type="group" name={group?.name || ''} groupId={groupId} workspaceId={workspaceId} />;
  }

  return (
    <ConfigProvider
      theme={{
        token: {
          screenXXL: 1700,
          screenXXLMin: 1700,
        },
      }}
    >
      <LayoutContainer>
        <Spin spinning={copyLoading}>
          <AppListSearch onSearch={onSearch} isMine={isMine} onUseFilter={onUseFilter} />
          {
            loading
              ? (
                <Loading>
                  <Spin />
                </Loading>
              )
              : (
                <div style={{ paddingBottom: 60 }}>
                  {appList?.length ?
                    <Row gutter={[{ xs: 8, sm: 8, md: 16, lg: 16 }, { xs: 8, sm: 8, md: 16, lg: 16 }]}>
                      {appList.map((app) => {
                        // 是否被收藏
                        const isStared = !!app.starred;
                        return (
                          <AppCardCol
                            key={app.id} xs={12} sm={24} md={8} lg={6} xl={6} xxl={6}>
                            <AppCard app={app} key={app.id} />
                            <Tooltip title="复制">
                              <CopyCircle className='copy'>
                                <CopyOutlined onClick={() => onCopy(app)} />
                              </CopyCircle>
                            </Tooltip>

                            <Tooltip title={isStared ? "取消收藏" : "收藏"}>
                              <CollectCircle className={`collect ${isStared ? 'star-collect' : 'unstar-collect'}`}
                                onClick={() => onStar(!isStared, app)}>
                                {isStared ? <StarFilled /> : <StarOutlined />}
                              </CollectCircle>
                            </Tooltip>
                          </AppCardCol>
                        )
                      })}
                      {/* {!appList.length && <Empty />} */}
                    </Row>
                    : <Empty></Empty>
                  }
                  {total > pageSize && (!useType || useType === 'all') && (<div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Pagination
                      style={{ marginTop: '8px', marginBottom: 20 }}
                      simple
                      size="small"
                      current={pageNo}
                      onChange={refresh}
                      total={total}
                      pageSize={pageSize}
                      showTotal={(total) => `总数${total}个`}
                    />
                  </div>
                  )}
                </div>
              )
          }
        </Spin>
        <AppCopyModal
          ref={appCopyModalRef}
          onChange={val => {
            createApp(val);
          }} />
      </LayoutContainer>
    </ConfigProvider>
  );
}

const Loading = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
`;
