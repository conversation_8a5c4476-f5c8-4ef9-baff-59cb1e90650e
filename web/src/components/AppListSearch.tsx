import { Input, Select, Form, Button, Radio, Checkbox } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

import { AppTypeCnMap, IAppType } from '@/interface';
import { useDebounceFn, useRequest } from 'ahooks';
import { CreateAppCard } from '@/pages/home/<USER>/create-app';
import { useGlobalState } from '@/hooks/useGlobalState';
import CustomSelect from './custom-select';
import { groupIdMap } from '@/constants';
import { env } from '@/utils/common';
import { AppApi } from '@/api/app';

interface IAppListSearchProps {
  onSearch?: (appSearch?: string, appType?: IAppType, userType?: string, subType?: string) => void;
  isMine?: boolean;
  onUseFilter?: (checked: boolean) => void;
}

export function AppListSearch(props: IAppListSearchProps) {

  const { onSearch, isMine, onUseFilter } = props;
  const { globalState } = useGlobalState();
  const { group, workspace, groups } = globalState;
  const [appType, setAppType] = useState<IAppType>(IAppType.All);
  const [subType, setSubType] = useState<string>('all');
  const [userType, setUserType] = useState(isMine ? 'mine' : 'all');
  const [appSearch, setAppSearch] = useState<string>();
  const [useType, setUseType] = useState('all');
  const enableSubType = workspace?.description === '音乐事业部';
  const { run: onCommit } = useDebounceFn(() => {
    onSearch?.(
      appSearch,
      appType === 'all' ? undefined : appType,
      userType,
      subType === 'all' ? undefined : subType
    );
  }, {
    wait: 300,
    trailing: true,
  });

  useEffect(() => {
    setUserType(isMine ? 'mine' : 'all');
  }, [isMine]);

  const groupId = groupIdMap[env];
  const { data: apps = [] } = useRequest(() => {
    if (enableSubType) {
      return AppApi.listAppsByGroup(groupId, {
        pageSize: 300
      }).then(res => {
        if (res?.items?.length) {
          return res.items;
        }
        return []
      });
    }
    return Promise.resolve([]);
  }, {
    staleTime: 60000,
  });

  const onUseChange = val => {
    setUseType(val);
    onUseFilter?.(val);
  };

  useEffect(() => {
    onCommit()
  }, [appType, appSearch, userType, subType])

  return (
    <FilterHeader>
      <div className="filter">
        {!isMine &&
          <CustomSelect
            label='创建人: '
            value={userType}
            onChange={setUserType}
          >
            <Select.Option value={'mine'}>
              我创建的
            </Select.Option>
            <Select.Option value={'all'}>
              所有应用
            </Select.Option>
          </CustomSelect>
        }
        {/* <Select
          placeholder="创建人"
          allowClear={true}
        >
        </Select> */}
        <CustomSelect
          label='应用类型: '
          value={appType}
          onChange={setAppType}
        >
          <Select.Option value={'all'}>
            所有应用
          </Select.Option>
          <Select.Option value={IAppType.AgentCompletion}>
            {AppTypeCnMap[IAppType.AgentCompletion]}
          </Select.Option>
          <Select.Option value={IAppType.AgentConversation}>
            {AppTypeCnMap[IAppType.AgentConversation]}
          </Select.Option>
          <Select.Option value={IAppType.AgentWorkflow}>
            {AppTypeCnMap[IAppType.AgentWorkflow]}
          </Select.Option>
          <Select.Option value={IAppType.Workflow}>
            {AppTypeCnMap[IAppType.Workflow]}
          </Select.Option>
          <Select.Option value={IAppType.VirtualHuman}>
            {AppTypeCnMap[IAppType.VirtualHuman]}
          </Select.Option>
          <Select.Option value={IAppType.Evaluator}>
            {AppTypeCnMap[IAppType.Evaluator]}
          </Select.Option>
        </CustomSelect>
        {enableSubType &&
          <CustomSelect
            label='业务类型: '
            popupMatchSelectWidth={false}
            value={subType}
            onChange={setSubType}
          >
            <Select.Option value={'all'}>
              所有应用
            </Select.Option>
            {
              apps.map(app => <Select.Option value={app.name}>
                {app.name}
              </Select.Option>)
            }
          </CustomSelect>
        }
        {/* <Radio.Group
          style={{ width: '200px' }}
          optionType='button'
          options={[
            { label: '我的收藏', value: 'star' },
            { label: '全部应用', value: 'all' }
          ]} /> */}
        <CustomSelect
          label="使用类型:"
          value={useType}
          popupMatchSelectWidth={false}
          options={[
            { label: '所有应用', value: 'all' },
            { label: '我的收藏', value: 'star' },
            { label: '最近访问', value: 'recent-view' }
          ]}
          onChange={onUseChange}
        />
        {/* <Checkbox style={{ width: '280px' }}
          value
          onChange={ev => onUseChange(ev)}>我的收藏</Checkbox> */}
        <Input
          placeholder="请输入应用名或应用描述"
          value={appSearch}
          onChange={(e) => setAppSearch(e.target.value)}
        ></Input>
        {/* <Button type="primary" onClick={onCommit}>
            搜索
          </Button> */}
      </div>
      <CreateAppCard workspace={workspace} groups={groups} defaultGroup={group?.id} render={onClick => <Button onClick={onClick} type="primary">创建应用</Button>}></CreateAppCard>
    </FilterHeader>
  );
}

const FilterHeader = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-left: 0px;

  .filter {
    display: flex;
    flex-direction: row;
    align-items: baseline;

    .ant-select {
      margin-right: 10px;
      min-width: 80px;
    }

    .ant-input {
      margin-right: 10px;
    }
  }
`;
