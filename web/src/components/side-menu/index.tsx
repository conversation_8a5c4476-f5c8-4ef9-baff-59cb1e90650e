import { useEffect, useState } from 'react';
import { Menu, Button } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { MenuItemConfig } from '../pageLayout';
import { getRetainQuery } from '@/hooks/useQuery';

interface ISideMenu {
  path: string;
  menuConfig: MenuItemConfig[];
  retainQueries?: string[]; // 跳转菜单时保留的查询参数项
}

const MenuContainer = styled.div`
  position: relative;
  .ant-menu {
    width: 200px
  }
  .ant-menu-inline-collapsed {
    width: 50px
  }
`;

const CollapseButon = styled.div`
position: absolute;
bottom: 0;
height: 30px;
border-top: 1px solid #eeeeee;
width: 100%;
cursor: pointer;

.collapsed {
  border: none;
}

`;

function getMenuItems(menuConfig: MenuItemConfig[], retainQueries?: string[]) {
  const search = getRetainQuery(retainQueries || []);
  return menuConfig.map((it) => {
    return {
      label: <Link to={`${it.path}?${search}`}>{it.title}</Link>,
      icon: it.Icon ? <it.Icon /> : null,
      key: it.path,
    };
  });
}

export function SideMenu(props: ISideMenu) {
  const { pathname } = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const menuItems = getMenuItems(props.menuConfig, props.retainQueries);
  const selectedMenu = props.menuConfig.find(
    (it) => pathname === '/' + props.path + '/' + it.path,
  );

  useEffect(() => {
    if (selectedMenu && selectedMenu.path === 'dev') {
      setCollapsed(true);
    } else {
      setCollapsed(false);
    }
  }, [selectedMenu]);

  console.log("selectMenu", selectedMenu);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  return (
    <MenuContainer>
      <Menu
        inlineCollapsed={collapsed}
        // style={{ width: 256 }}
        theme="light"
        selectedKeys={selectedMenu ? [selectedMenu?.path] : undefined}
        items={menuItems}
      />
      <CollapseButon onClick={toggleCollapsed} >
        <Button className="collapsed">
          {collapsed ? <MenuUnfoldOutlined rev={undefined} /> : <MenuFoldOutlined rev={undefined} />}
        </Button>
      </CollapseButon>
    </MenuContainer>
  );
}
