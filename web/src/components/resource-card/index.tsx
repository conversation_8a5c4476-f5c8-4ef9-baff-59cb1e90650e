import { Card, Tooltip, Tag, Badge } from 'antd';
import Identicon from 'identicon.js';
import styled from 'styled-components';
import { usePathQuery } from '@/hooks/useQuery';
import { AppModel, AppTypeCnMap, AppTypeColorMap, AppTypeTagColorMap, GroupModel, AppTypeAvatarMap } from '@/interface';
import React from 'react';
import { AvatarIcon } from '@/pages/app/workflow/react-node/base-node';

export const ShadowAppCard = styled(Card)`
  /* box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.12); */
  min-width: 200px;
  flex: 1;
  transition: all 0.2s;

  &:hover {
    cursor: pointer;
    box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  }
`;

const StyledCard = styled(ShadowAppCard)`
  /* min-width: 200px;
  max-width: 350px; */
  &:hover {
    .name {
      color: #284ec1;
    }
  }

  .name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 1px;
    color: #333;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    width: calc(100% - 50px)
  }
    
  .topbar {
    display: flex;
    flexDirection: row;
    align-items: center;
    justify-content: space-between;
  }

  .description {
    font-size: 12px;
    color: #999;
    line-height: 20px;
    height: 40px;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 这里是超出几行省略 */
    margin-top: 5px;
  }

  .type {
    margin-top: 1px;
    font-size: 12px;
    color: #999;
    display: inline-flex;
    padding: 2px 4px;
    background-color: #eee;
    border: 1px solid #eee;
    border-radius: 4px;
  }

  .avatar {
    position: absolute;
    right: 18px;
    top: 14px;
  }
`;

const BottomBar = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #999;
    font-size: 12px;
`

export interface AppAvatarProps extends Omit<ResourceAvatarProps, "unikey" | "foreground" | "alt"> {
  app: AppModel;
}

export const AppAvatar = (props: AppAvatarProps) => {
  const { app, ...restProps } = props;

  return <ResourceAvatar
    unikey={app.id}
    alt={app.name}
    foreground={AppTypeColorMap[app.type]}
    {...restProps}
  />
}

interface ResourceAvatarProps {
  unikey: string;
  alt?: string;
  className?: string;
  width?: number;
  height?: number;
  style?: React.CSSProperties;
  foreground?: number[];
  background?: number[];
}

export const ResourceAvatar = (props: ResourceAvatarProps) => {
  const {
    className,
    width,
    height,
    style,
    foreground = [120, 77, 77, 255],
    background = [240, 240, 240, 255],
    unikey,
    alt,
  } = props;
  const options: any = {
    foreground,
    background,
    size: 60,
    format: 'svg',
    margin: 0,
  };
  const avatarData = new Identicon(unikey, options).toString();

  const defaultStyle = {
    borderRadius: '4px',
    // border: '1px solid #eee',
  }


  return (
    <img
      style={
        {
          ...defaultStyle,
          ...style,
        }}
      className={className}
      width={width ?? 40}
      height={height ?? 40}
      src={`data:image/svg+xml;base64,${avatarData}`}
      alt={alt}
    />
  );
}

interface ResourceCardProps {
  onClick?: () => void;
  name: string;
  description?: string;
  tags?: string[];
  avatarColor?: number[];
  id?: string;
  creator?: string;
  icon?: string;
  tagColor?: string;
  isTemplate?: boolean;
}

export const ResourceCard = (props: ResourceCardProps) => {
  const { onClick, name, description, tags, tagColor, avatarColor, id, creator, icon, isTemplate } = props;

  const inner = (
    <StyledCard onClick={onClick}>
      <div className="topbar">
        <Tooltip title={name}>
          <div className="name">{name}</div>
        </Tooltip>
        {icon ? <AvatarIcon icon={icon} style={{ borderRadius: 8 }}></AvatarIcon> :
          <ResourceAvatar className='avatar' unikey={id} alt={name} foreground={avatarColor}></ResourceAvatar>
        }
      </div>

      <div className="description">{description}</div>
      <BottomBar>
        <div>
          {
            tags.map((tag, index) => (
              tag && <Tag color={tagColor}>{tag}</Tag>))
          }
        </div>
        {creator ? `创建人: ${creator}` : null}
      </BottomBar>
    </StyledCard>
  );
  if (isTemplate) {
    return (
      <Badge.Ribbon text="模板" color="#eda74fc2">
        {inner}
      </Badge.Ribbon>
    );
  }
  return inner;
};

export function AppCard(props: { app: AppModel }) {
  const { pushAddQuery } = usePathQuery();
  const { app } = props;

  return (
    <ResourceCard
      onClick={() =>
        pushAddQuery('/app/dev', {
          appId: app.id,
          type: app.type
        })
      }
      creator={app?.createdBy?.fullname}
      name={app.name}
      icon={app?.extInfo?.avatar || AppTypeAvatarMap[app.type]}
      description={app.description}
      tags={[AppTypeCnMap[app.type], app.subType]}
      tagColor={AppTypeTagColorMap[app.type]}
      avatarColor={AppTypeColorMap[app.type]}
      id={app.id}
      isTemplate={app.isTemplate}
    />
  );
}

export function GroupCard(props: { group: GroupModel }) {
  const { pushAddQuery } = usePathQuery();
  const { group } = props;

  return (
    <ResourceCard
      onClick={() =>
        pushAddQuery('/group', {
          groupId: group.id,
        })
      }
      name={group.name}
      description={group.description}
      avatarColor={[232, 176, 127, 255]}
      id={group.id}
    />
  );
}
