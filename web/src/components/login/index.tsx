import { useRequest } from 'ahooks';
import { Dropdown, Form, Input, Menu, message, Modal } from 'antd';
import { useForm } from 'antd/es/form/Form';
import classNames from 'classnames';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';

import { UserApi, WorkspaceApi } from '../../api';
import UserAvatar from '../../components/avatar';
import { useAuth } from '../../hooks/useAuth';
import { useGlobalState } from '../../hooks/useGlobalState';
import { usePathQuery } from '../../hooks/useQuery';
import { RoleLevel } from '../../interface/auth';
import { setAuth } from '../../utils/request';

// @ts-ignore
const isDev = process.env.NODE_ENV === 'development';
const openIdDomain = [
  'langbase.yf-dev2.netease.com',
  'langbase.yf-online.netease.com',
  'langbase.netease.com',
  'langbase-pre.netease.com',
  'langbase-local.yf-dev2.netease.com',
  'langbase-local.netease.com',
  'langbase-workflow.yf-dev2.netease.com',
  'langbase-workflow.yf-onlinetest4.netease.com'
];
const loginEnable = openIdDomain.includes(window.location.hostname);

export const LOGIN_NE = '/api/v1/oauth/login/netease';

function Login({ style = {} }) {
  const { globalState, updateGlobalState, fetchGlobalState } = useGlobalState();
  const { user, workspace } = globalState;
  const [workspaceModalVisible, setWorkspaceModalVisible] = useState(false);
  const { pushRetainQuery } = usePathQuery();
  const isWorkspaceAdmin = useAuth(RoleLevel.WorkspaceAdmin);
  const isSystemAdmin = useAuth(RoleLevel.SystemAdmin);
  const [modalVisible, setModalVisible] = useState(false);
  const navTo = useNavigate();
  const [form] = useForm<{ name: string; description: string }>();

  const { run: createWorkspace } = useRequest(WorkspaceApi.createWithResp, {
    manual: true,
    onSuccess: (res) => {
      message.success('创建成功');
      setModalVisible(false);
      fetchGlobalState('workspaces');
      updateGlobalState('workspace', res);
      navTo(`/overview?workspaceId=${res.id}`);
    },
  });

  useEffect(() => {
    if (user && typeof fetchGlobalState === 'function') {
      fetchGlobalState('roles');
    }
  }, [user]);

  const onLogin = useCallback(() => {
    if (loginEnable) {
      localStorage.setItem(
        'login_redirect',
        window.location.href.replace(window.location.origin, ''),
      );
      setTimeout(() => {
        window.location.href = LOGIN_NE;
      }, 1200);
      return;
    }

    if (isDev) {
      message.warning(`开发环境下测试登录功能，需绑定域名 ${openIdDomain[0]}`);
    } else {
      message.warning(`仅 ${openIdDomain.join('、')} 开启了 OpenId 登录`);
    }
  }, []);

  const onLogout = () => {
    UserApi.logout().then(() => {
      updateGlobalState('user', null);
    });
  };

  useEffect(() => {
    if (user) {
      return;
    }
    if (loginEnable) {
      UserApi.userInfo()
        .then((user) => {
          setAuth();
          updateGlobalState('user', user);
        })
        .catch((e) => {
          // eslint-disable-next-line no-console
          console.log(e);
          window.corona.info('Login err', e);
          message.warning('获取用户信息失败，跳转至登录');
          onLogin();
        });
    } else if (isDev) {
      message.warning(`开发环境下，需绑定域名 ${openIdDomain[0]} 访问`);
    } else {
      message.warning(`请访问 ${openIdDomain.join('、')} 域名`);
    }
  }, [user, onLogin, updateGlobalState]);

  const { fullname = '未登录', name = '未登录' } = user || {};
  const menu = (
    <Menu>
      <Menu.Item key="fullname">
        <span style={{ fontWeight: 500 }}>{fullname}</span>
      </Menu.Item>
      <Menu.Item key="name">
        <span>@{name}</span>
      </Menu.Item>
      <Menu.Divider />
      {/* <Menu.Item onClick={() => setWorkspaceModalVisible(true)} key="switch workspace">
        <span>切换租户</span>
      </Menu.Item> */}
      {/* {isWorkspaceAdmin && (
        <Menu.Item
          onClick={() => pushRetainQuery('/workspace', ['workspaceId'])}
          key="workspace"
        >
          <span>租户设置</span>
        </Menu.Item>
      )} */}
      {
        isWorkspaceAdmin && 
        <Menu.Item onClick={() => setModalVisible(true)} key="project">
          <span>创建租户</span>
        </Menu.Item>
      }
      {isSystemAdmin && (
        <Menu.Item
          onClick={() => pushRetainQuery('/system', ['workspaceId'])}
          key="system"
        >
          <span>系统设置</span>
        </Menu.Item>
      )}
      <Menu.Item onClick={onLogout} key="logout">
        <span>退出登录</span>
      </Menu.Item>
    </Menu>
  );

  const unloginMenu = (
    <Menu>
      <Menu.Item onClick={onLogin}>
        <span>登录</span>
      </Menu.Item>
    </Menu>
  );

  return (
    <div>
      <Modal
        title="创建租户"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form onFinish={createWorkspace} form={form}>
          <Form.Item
            label="租户名"
            name="name"
            rules={[{ required: true, message: '请输入租户名称!' }]}
          >
            <Input placeholder="请输入租户名称" />
          </Form.Item>
          <Form.Item label="描述" name="description">
            <Input placeholder="请输入租户描述" />
          </Form.Item>
        </Form>
      </Modal>
      <Dropdown overlay={user ? menu : unloginMenu} trigger={['click']}>
        <StyledLogin style={style}>
          {user && <><UserAvatar fullname={user.fullname} /></>}
          <WorkspaceName>{user?.fullname} @{workspace && workspace.name}</WorkspaceName>
          {!user && <span>未登录</span>}
        </StyledLogin>
      </Dropdown>
      <WorkspaceSelectModal
        visible={workspaceModalVisible}
        onClose={() => setWorkspaceModalVisible(false)}
      />
    </div>
  );
}

function WorkspaceSelectModal({
  visible,
  onClose,
}: {
  visible: boolean;
  onClose: () => void;
}) {
  const navTo = useNavigate();
  const { globalState } = useGlobalState();
  const { workspace, workspaces } = globalState;
  const [select, setSelected] = useState<string>('');

  useEffect(() => {
    if (workspace && select === '') {
      setSelected(workspace.id);
    }
  }, [workspace, select]);

  function changeWorkspace() {
    navTo(`/overview?workspaceId=${select}`);
    onClose();
  }

  return (
    <Modal title="切换租户" open={visible} onCancel={onClose} onOk={changeWorkspace}>
      <WorkspaceList>
        {workspaces?.map((t) => {
          return (
            // eslint-disable-next-line jsx-a11y/no-static-element-interactions, jsx-a11y/click-events-have-key-events
            <div
              onClick={() => setSelected(t.id)}
              key={t.id}
              className={classNames({
                workspaceItem: true,
                selected: select === t.id,
              })}
            >
              <span>{t.name}</span>
            </div>
          );
        })}
      </WorkspaceList>
    </Modal>
  );
}

export default Login;

const StyledLogin = styled.div`
  position: absolute;
  bottom: 30px;
  text-align: center;
  left: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 2px;
  font-size: 14px;
  padding: 0 4px;
  transition: all 0.2s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .indicator {
    color: #333;
    padding-left: 8px;
  }
`;

const WorkspaceName = styled.span`
  font-size: 15px;
  margin-right: 8px;
  width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const WorkspaceList = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: start;

  .workspaceItem {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    border: 1px #ddd solid;
    border-radius: 4px;
    text-align: center;
    box-sizing: border-box;

    padding: 10px 0;
    min-height: 40px;
    margin-bottom: 10px;
    font-size: 16px;

    // 每行4 个，居左对齐
    width: 24%;
    &:not(:nth-child(4n)) {
      margin-right: calc(4% / 3);
    }

    span:nth-child(2) {
      color: #aaa;
      font-size: 12px;
    }
  }

  .selected {
    border: 1px #1890ff solid;
  }
`;
