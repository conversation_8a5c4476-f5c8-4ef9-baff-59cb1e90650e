import { Avatar } from 'antd';
import React from 'react';

type Props = {
  imageUrl?: string;
  fullname: string;
};

const UserAvatar: React.FC<Props> = ({ imageUrl, fullname = '' }) => {
  if (imageUrl) {
    return <Avatar src={imageUrl} />;
  }
  return (
    <Avatar style={{ backgroundColor: '#eeaf0e', width: '30px', height: '30px', marginRight: '5px' }}>
      {fullname[fullname.length - 1]}
    </Avatar>
  );
};

export default UserAvatar;
