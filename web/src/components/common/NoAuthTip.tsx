import { LockTwoTone } from '@ant-design/icons';
import { Button, message } from 'antd';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { WorkspaceApi } from '@/api/workspace';
import { GroupApi } from '@/api/group';
import { copyText } from '@/utils/common';
import { Member } from '@/interface';
import { useGlobalState } from '@/hooks/useGlobalState';

interface NoAuthTipProps {
  type: 'workspace' | 'group';
  name: string;
  workspaceId?: string;
  groupId?: string;
  admins?: Array<{
    member: {
      email: string;
      fullname: string;
    }
  }>;
}

export const NoAuthTip: React.FC<NoAuthTipProps> = ({
  type,
  name,
  workspaceId,
  groupId,
  admins: initialAdmins
}) => {
  const typeText = type === 'workspace' ? '租户' : '业务组';
  const { globalState } = useGlobalState();
  const { user } = globalState;
  const [groupAdmins, setGroupAdmins] = useState([]);
  const [workspaceAdmins, setWorkspaceAdmins] = useState([]);

  useEffect(() => {
    const fetchAdmins = async () => {
      try {
        if (type === 'group' && groupId) {
          // 获取组管理员
          const groupAdmins = await GroupApi.getGroupAdmin(groupId);
          setGroupAdmins(groupAdmins);
          if (workspaceId) {
            // 获取租户管理员
            const workspaceAdmins = await WorkspaceApi.getWorkspaceAdmin(workspaceId);
            setWorkspaceAdmins(workspaceAdmins);
          }
        } else if (type === 'workspace' && workspaceId) {
          // 仅获取租户管理员
          const workspaceAdmins = await WorkspaceApi.getWorkspaceAdmin(workspaceId);
          setWorkspaceAdmins(workspaceAdmins);
        }
      } catch (error) {
        console.error('Failed to fetch admins:', error);
      }
    };

    if (!initialAdmins) {
      fetchAdmins();
    }
  }, [type, workspaceId, groupId, initialAdmins]);

  const renderAdmins = (admins: Member[]) => {
    return admins.map((it) => (
      <a
        key={it.member.email}
        target="_blank"
        href={`http://popo.netease.com/static/html/open_popo.html?ssid=${it.member.email}&sstp=0`}
        rel="noreferrer"
        style={{ marginRight: '10px' }}
      >
        {it.member.fullname}
      </a>
    ));
  };

  const renderContact = () => {
    if (type === 'workspace') {
      return (
        <div className="contact">
          请联系租户管理员：<br />
          {renderAdmins(workspaceAdmins)}
          <br />
          邀您加入
        </div>
      );
    } else {
      return (
        <div className="contact">
          请联系组管理员：<br />
          {renderAdmins(groupAdmins)}
          <br />
          或联系租户管理员：<br />
          {renderAdmins(workspaceAdmins)}
        </div>
      );
    }
  };


  const copyInviteUrl = () => {
    const inviteUrl = type === 'workspace' ? `${window.location.origin}/workspace/member?workspaceId=${workspaceId}&invite=${user.name}` : `${window.location.origin}/group-setting/member?groupId=${groupId}&workspaceId=${workspaceId}&invite=${user.name}`;
    const inviteText = `我想申请加入 LangBase 的${typeText} ${name}，麻烦帮忙通过一下，谢谢! \n${inviteUrl}`;
    copyText(inviteText);
  };

  return (
    <TipContainer>
      <LockTwoTone rev={undefined} />
      <div>您尚未加入{typeText}: {name}</div>
      {renderContact()}
      <div className="invite-url">
        点击下方按钮复制您的专属邀请链接，发送给管理员让其点击即可一键通过
        <br />
        <Button type="primary" onClick={copyInviteUrl}>
          复制专属链接和文案
        </Button>
      </div>
    </TipContainer>
  );
};

const TipContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .anticon {
    font-size: 50px;
    margin-bottom: 10px;
  }
  
  .contact {
    margin-top: 20px;
    max-width: 600px;
    line-height: 2;
    text-align: center;
  }

  .invite-url {
    margin-top: 20px;
    max-width: 600px;
    line-height: 2;
    text-align: center;
    font-size: 14px;

    button {
      margin-top: 10px;
    }
  }

  a {
    color: #1846ff;
    cursor: pointer;
  }
`; 