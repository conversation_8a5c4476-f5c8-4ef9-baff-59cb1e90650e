import { CSSProperties, FC, ReactNode } from 'react';
import { Tooltip } from 'antd';

interface EllipsisTextProps {
  children: ReactNode;
  lines?: 1 | 2;
  style?: CSSProperties;
  className?: string;
}

const EllipsisText: FC<EllipsisTextProps> = ({ children, lines = 2, style, className }) => {
  const ellipsisStyle: CSSProperties = {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    ...(lines === 1
      ? {
          whiteSpace: 'nowrap',
        }
      : {
          display: '-webkit-box',
          WebkitLineClamp: lines,
          WebkitBoxOrient: 'vertical',
          wordBreak: 'break-all',
        }),
    ...style,
  };

  return (
    <Tooltip title={typeof children === 'string' ? children : undefined}>
      <div className={className} style={ellipsisStyle}>
        {children}
      </div>
    </Tooltip>
  );
};

export default EllipsisText; 