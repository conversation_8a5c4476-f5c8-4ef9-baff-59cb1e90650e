import Uploader from "@/pages/app/workflow/react-node/imageUploader";

interface DynamicFieldProps {
  value?: any;
  type?: string;
  onChange?: (value: any) => void;
  componentProps?: object;
}

const App: React.FC<DynamicFieldProps> = ({ value, onChange, type, componentProps }) => {
  if (type === 'Uploader') {
    return <Uploader value={value} onChange={(v => onChange?.(v.map(v => v?.url)))} {...componentProps} />;
  }
}

export default App;