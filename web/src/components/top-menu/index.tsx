import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd';
import { Link, matchPath, useLocation } from 'react-router-dom';
import styled from 'styled-components';

import { getRetainQuery } from '@/hooks/useQuery';

import { MenuItemConfig } from '../pageLayout';
import React, { useMemo } from 'react';
import { TopBar } from '../top-bar';
import { IAppType } from '@/interface';
import { useAdmin } from '@/hooks/useAdmin';

interface ISideMenu {
  right?: React.ReactNode;
  left?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  path: string;
  menuConfig: MenuItemConfig[];
  retainQueries?: string[]; // 跳转菜单时保留的查询参数项
}

function getMenuItems(
  prefix: string,
  menuConfig: MenuItemConfig[],
  retainQueries?: string[],
) {
  const search = getRetainQuery(retainQueries || []);
  const searchType = getRetainQuery(['type']).replace('type=', '');

  // 权限控制
  const menus = menuConfig?.filter(menu => {
    if (menu.admin?.length > 0) {
      const isAdmin = menu.admin.reduce((acc, cur) => {
        if (useAdmin(cur)) {
          acc = true
        }
        return acc;
      }, false);
      if (isAdmin) return true;
      return false
    }
    return true;
  });

  // 仅虚拟人展示资产
  // 仅仅社交直播
  return menus.filter(it => {
    if (it.hide) {
      return false;
    }
    if (it?.hideMenu && typeof it.hideMenu === 'function') {
      return !it.hideMenu(search)
    }
    return true;
  }).filter(item => ((searchType !== IAppType.VirtualHuman && item?.title !== '资产') || searchType === IAppType.VirtualHuman)).map((it) => {
    return {
      label: it.disabled ? (
        <Tooltip title={it.tooltip || '敬请期待'} placement="top">
          {it.title}
        </Tooltip>
      ) : (
        <Link
          to={(it.fullPath ?? false ? it.path : `/${prefix}/${it.path}`) + `?${search}`}
        >
          {it.title}
        </Link>
      ),

      disabled: it.disabled,
      icon: it.Icon ? <it.Icon /> : null,
      key: it.path,
    };
  });
}

export function TopMenu(props: ISideMenu) {
  const { pathname } = useLocation();
  const { menuConfig, path, retainQueries, className, left, right } = props;
  const menuItems = getMenuItems(path, menuConfig, retainQueries);

  const selectedMenu = menuConfig.find((it) => {
    if (it.matchPaths) {
      const matched = it.matchPaths?.findIndex(otherPath => {
        return !!matchPath('/' + path + '/' + otherPath, pathname)
      });
      if (matched > -1) {
        return true;
      }
    }
    if (it.fullPath ?? false) {
      return pathname === it.path;
    } else {
      const res = matchPath('/' + path + '/' + it.path, pathname);
      return !!res;
      // return pathname === '/' + path + '/' + it.path || pathname.split("/")[1] === it.path;
    }
  });

  if (selectedMenu?.hide) {
    return;
  }

  return (
    <TopBar left={left} right={right}>
      <Menu
        style={{
          background: 'transparent',
          border: '0px',
          width: '1000px',
          justifyContent: 'center'
        }}
        className={className}
        theme="light"
        mode="horizontal"
        selectedKeys={selectedMenu ? [selectedMenu?.path] : undefined}
        items={menuItems}
      />
    </TopBar>
  );
}

