import React from 'react';
import styled from 'styled-components';

interface ITopBar {
  left?: React.ReactNode;
  right?: React.ReactNode;
  children?: React.ReactNode;
  style?: React.CSSProperties;
}

export function TopBar(props: ITopBar) {
  const { children, left, style, right } = props;

  return (
    <TopBarBox style={style || {}}>
      <Left>
        {left}
      </Left>
      {children}
      <Right>
        {right}
      </Right>
    </TopBarBox>
  );
}

const Left = styled.div`
  position: absolute;
  left: 20px;
  top: 20px;
`;

const Right = styled.div`
  position: absolute;
  right: 50px;
  top: 15px;
`;

const TopBarBox = styled.div`
  min-height: 50px;
  position: relative;
  align-items: center;
  background: transparent;
  justify-content: center;
  border-bottom: 1px solid rgba(29, 28, 35, .08);
  display: flex;
  padding: 10px 16px 0px;
  width: 100%;

  .ant-menu-item {
    cursor: pointer;
    font-size: 16px;
    height: 50px;
  }
`;