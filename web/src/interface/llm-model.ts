import { IdModel } from './crud-api';

export interface LLMModel {
  id: string;
  modelId?: string;
  alias?: string;
  fee: Record<string, any>;
  name: string;
  providerName: string;
  providerKind: string;
  description: string;
  enable: boolean;
  tag?: string[];
  url?: string;
  config?: Record<string, any>;
  type: 'llm' | 'embedding';
  context: number;
  speed: number;
  performance: number;
}

export interface LLMModelCreate {
  modelId?: string;
  alias?: string;
  fee: Record<string, any>;
  name: string;
  providerName: string;
  providerKind: string;
  description: string;
  enable: boolean;
  tag?: string[];
  url?: string;
  config?: Record<string, any>;
  type: 'llm' | 'embedding';
  context: number;
  speed: number;
  performance: number;
}

export interface TagConfig {
  text: string;
  color: string;
}



export interface LLMModelUpdate extends Partial<LLMModelCreate> {} 