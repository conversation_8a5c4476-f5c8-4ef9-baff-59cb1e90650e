import {
  AlignLeftOutlined,
  FormOutlined,
  UnorderedListOutlined,
  CalendarOutlined,
  FileOutlined
} from '@ant-design/icons';
import { ToolDetail } from './app';
import { GlobalModel } from './model-provider';
import { Input, InputNumber, Select, Switch } from 'antd';
import FormDatePicker from '@/pages/app/components/params-card/form-date-picker';

export enum ParamTypeEnum {
  textInput = 'input',
  textArea = 'area',
  select = 'list',
  date = 'date',
  number = 'number',
  boolean = 'boolean',
  file = 'file',
}

export const ParamTypeIconMap = {
  [ParamTypeEnum.textInput]: <FormOutlined rev={undefined} />,
  [ParamTypeEnum.textArea]: <AlignLeftOutlined rev={undefined} />,
  [ParamTypeEnum.select]: <UnorderedListOutlined rev={undefined} />,
  [ParamTypeEnum.date]: <CalendarOutlined />,
  [ParamTypeEnum.number]: <FormOutlined rev={undefined} />,
  [ParamTypeEnum.boolean]: <FormOutlined rev={undefined} />,
  [ParamTypeEnum.file]: <FileOutlined rev={undefined} />,
};

export const ParamTypeComponentMap = {
  [ParamTypeEnum.textInput]: Input,
  [ParamTypeEnum.textArea]: Input.TextArea,
  [ParamTypeEnum.select]: Select,
  [ParamTypeEnum.date]: FormDatePicker,
  [ParamTypeEnum.number]: InputNumber,
  [ParamTypeEnum.boolean]: Switch,
}
export interface AgentConfig {
  prePrompt?: string;
  newPrompt?: Record<string, any>;
  modelsConfig?: {
    models?: GlobalModel[];
    retryConfig?: Record<string, any>;
  };
  welcomeText?: string;
  paramsInPrompt?: IAgentParam[];
  tools?: Record<string, ToolDetail | null>,
  modelName?: string;
  modelParams?: Record<string, any>;
  model?: GlobalModel;
  providerKind?: string;
  knowledge?: any;
}

export interface IAgentParam {
  type: ParamTypeEnum;
  key: string;
  paramKey?: string;
  title: string;
  required: boolean;
  default_val?: string | boolean | number;
  dataType?: string;
  config: {
    options?: {
      value: string;
      title: string;
      uuid?: string;
    }[];
    length?: number;
  };
  // 系统变量需要用到
  category?: string;
  checked?: boolean;
  children?: IAgentParam[];

  columnType?: string;
}

export enum ParamDataTypeEnum {
  string = 'STRING',
  integer = 'INTEGER',
  long = 'LONG',
  double = 'DOUBLE',
  bool = 'BOOLEAN',
  time = 'DATE'
};

export const ParamDataTypeMap = {
  [ParamDataTypeEnum.string]: { label: '字符串', value: ParamDataTypeEnum.string },
  [ParamDataTypeEnum.integer]: { label: '整型', value: ParamDataTypeEnum.integer },
  [ParamDataTypeEnum.long]: { label: '长整型', value: ParamDataTypeEnum.long },
  [ParamDataTypeEnum.double]: { label: '浮点型', value: ParamDataTypeEnum.double },
  [ParamDataTypeEnum.bool]: { label: '布尔值', value: ParamDataTypeEnum.bool },
  [ParamDataTypeEnum.time]: { label: '日期', value: ParamDataTypeEnum.time },
};

export enum OperationTypeEnum {
  包含 = 'contain',
  不包含 = 'notcontain',
  模糊包含 = 'contain_fuzzy',
  模糊不包含 = 'notcontain_fuzzy',
  范围 = 'range',
};

// 聚合策略
export enum AggregationStrategyTypeEnum {
  聚合输入 = 'AGG_ALL',
}

export const AggregationStrategyTypeMap = {
  [AggregationStrategyTypeEnum.聚合输入]: { label: '聚合输入', value: 'AGG_ALL', desc: "将用户一个时间窗口内的内容聚合输入给大模型" }
}
