import internal from 'stream';

// 系统层面全局的模型提供商
export interface GlobalModelProvider {
  kind: string;
  description: string;
  icon: string;
}

// 系统层面全局的模型
export interface GlobalModel {
  name: string;
  providerKind: string;
  providerName?: string;
  alias?: string;
  disableReason?: string;
  modelName?: string;
  type?: string;
  url?: string;
  enable?: boolean;
  performance?: number;
  speed?: number;
  context?: number;
  tags?: string[];
  tag?: string[];
  default?: boolean;
  fee?: any;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  model_test_result?: {
    avg: number;
    creative: number;
    json: number;
    task: number;
  };
  config: {
    [key: string]: {
      range: number[];
      default: number;
    };
  };
}

// 租户或业务组绑定的模型提供商配置
export interface ModelProviderBinding {
  id: string;
  providerKind: string;
  description: string;
  endpoint: string;
  config: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// 创建模型提供商配置 DTO
export interface CreateModelProviderBindingDto {
  providerKind: string;
  description: string;
  endpoint: string;
  apiKey: string;
  config: Record<string, any>;
}

// 更新模型提供商配置 DTO
export interface UpdateModelProviderBindingDto {
  description: string;
  endpoint: string;
  apiKey: string;
  config: Record<string, any>;
}

// 租户或业务组上配置的默认模型
export interface DefaultModel {
  id: string;
  modelType: string;
  modelName: string;
  providerKind: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateDefaultModelDto {
  modelName: string;
  modelType: string;
  providerKind: string;
}
