import { MemberOfResource, RoleType } from './member';

export interface UserModel {
  id: string; // 232cf8ed-9207-xxxx-xxxx-51ac452c4c0c
  fullname: string; // '张三'
  name: string; // 'hzzhangsan'
  email: string; // 'hzz<PERSON><EMAIL>'
  isAdmin: boolean; // 是否为管理员
}

export interface UserRoleModel {
  resourceType: MemberOfResource;
  resourceID: string;
  memberID: string;
  role: RoleType;
}
export interface UserRoles {
  app: { role: RoleType; appId: string }[];
  group: { role: RoleType; groupId: string }[];
  workspace: { role: RoleType; workspaceId: string }[];
}
