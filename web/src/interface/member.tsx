// eslint-disable-next-line import/no-cycle

import { UserModel } from './user';

export enum MemberOfResource {
  Workspace = 'workspace',
  Group = 'group',
  App = 'app',
}

export const ResCnMap = {
  [MemberOfResource.Workspace]: '租户',
  [MemberOfResource.Group]: '业务组',
  [MemberOfResource.App]: '应用',
};

export const ResParentMap = {
  [MemberOfResource.App]: MemberOfResource.Group,
  [MemberOfResource.Group]: MemberOfResource.Workspace,
  [MemberOfResource.Workspace]: null,
};

export const ResIdMap = {
  [MemberOfResource.Workspace]: 'workspaceId',
  [MemberOfResource.Group]: 'groupId',
  [MemberOfResource.App]: 'appId',
};

export enum RoleType {
  ADMIN = 'admin',
  DEVELOPER = 'developer',
  VIEWER = 'viewer',
}

export const RoleTypeCnMap = {
  [RoleType.ADMIN]: '管理员',
  [RoleType.DEVELOPER]: '开发者',
  [RoleType.VIEWER]: '查看者',
};

export interface Member {
  memberID: string;
  resourceID: string;
  resourceType: MemberOfResource;
  role: RoleType;
  member: Omit<UserModel, 'isAdmin'>;
}

export interface MemberListDto {
  members: Member[];
  totalCount: number;
}

export interface ListMemberOption {
  pageSize: number;
  pageNumber: number;
  name?: string;
  role?: RoleType;
}
