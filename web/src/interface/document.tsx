export interface DocumentModel {
  id: string;
  workspaceId: string;
  knowledgeId: string;
  title: string;
  type: string;
  source: string;
  config: any;
  tagName?: string;
  doc_id?: number;
  segment?: {
    total: number;
    success: number;
  }
}

export interface DocumentUpdateModel {
  knowledgeId?: string;
  collectionId?: number;
  title: string;
  type?: string;
  source?: string;
  config?: any;
  tagName?: string;
  doc_id?: number;
}

export interface DocumentSegmentModel {
  content: string;
  createUser: string;
  createdAt: string;
  docId: number;
  id: number;
  noIndex: boolean;
  path: string;
  segment: number;
  status: number;
  updatedAt: string;
}
