import { AgentConfig } from './agent';
import { UserModel } from './user';

export enum IAppType {
  AgentCompletion = 'agent-completion',
  AgentConversation = 'agent-conversation',
  AgentWorkflow = 'agent-workflow',
  Workflow = 'workflow',
  VirtualHuman = 'virtual-human',
  StoryLine = 'story-line',
  Chatflow = 'agent-workflow',
  Evaluator = 'evaluator',
  All = 'all'
}

export type AgentAppType = IAppType.AgentCompletion | IAppType.AgentConversation;

export const AppTypeCnMap = {
  [IAppType.AgentCompletion]: '生成型',
  [IAppType.AgentConversation]: '聊天型',
  [IAppType.Workflow]: '工作流',
  [IAppType.AgentWorkflow]: '对话流',
  [IAppType.VirtualHuman]: '虚拟人',
  // [IAppType.StoryLine]: '互动剧场',
  [IAppType.Evaluator]: '评估器',
};

export const AppTypeTagColorMap = {
  [IAppType.AgentCompletion]: 'processing',
  [IAppType.AgentConversation]: 'green',
  [IAppType.Workflow]: 'gold',
  [IAppType.AgentWorkflow]: 'volcano',
  [IAppType.VirtualHuman]: 'purple',
  [IAppType.StoryLine]: 'green',
  [IAppType.Evaluator]: 'cyan',
};

export const AppTypeColorMap: Record<string, number[]> = {
  [IAppType.AgentCompletion]: [105, 177, 255, 255],
  [IAppType.AgentConversation]: [149, 222, 100, 255],
  [IAppType.Workflow]: [255, 245, 102, 255],
  [IAppType.Evaluator]: [19, 194, 194, 255],
};

// 添加新的类型映射
export const AppTypeCardColorMap = {
  [IAppType.AgentCompletion]: {
    primary: '#1890ff',
    background: 'rgba(24, 144, 255, 0.1)'
  },
  [IAppType.AgentConversation]: {
    primary: '#52c41a',
    background: 'rgba(82, 196, 26, 0.1)'
  },
  [IAppType.VirtualHuman]: {
    primary: '#722ed1',
    background: 'rgba(114, 46, 209, 0.1)'
  },
  [IAppType.AgentWorkflow]: {
    primary: '#fa8c16',
    background: 'rgb(250 85 20 / 10%)'
  },
  [IAppType.Workflow]: {
    primary: '#faad14',
    background: 'rgba(250, 173, 20, 0.1)'
  },
  [IAppType.Evaluator]: {
    primary: '#13c2c2',
    background: 'rgba(19, 194, 194, 0.1)'
  }
}; 

export const AppTypeImgMap = {
  [IAppType.AgentCompletion]:
    // 'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/30777468162/ad8d/5293/6ac6/3aca8f0601155baf22567def470a8e47.png',
    'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/35847023977/605e/9d3b/d776/9665971b12291087e58d6400d6399f45.png',
  [IAppType.AgentConversation]:
    // 'https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/30777465660/85d3/a012/cc43/af31c3c8415bce4cb25a3b95e7e76dd3.png',
    'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/35846388140/68e0/3497/f144/b11b7f10390e7b2aae21524af5a3cbbb.png',
  [IAppType.Workflow]:
    // 'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/30777468163/3aec/cd1b/1fcd/ef3b3ca3b272da5894b73cb1341f49bc.png',
    'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/35846333652/d613/1c04/ff0d/af3a65f3389fe3f24d01e39938c01d82.png',
  [IAppType.VirtualHuman]:
    // 'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/30777468163/3aec/cd1b/1fcd/ef3b3ca3b272da5894b73cb1341f49bc.png',
    'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/56731683922/7bdc/721a/ca24/b613a63f33d066b302155f5f3ba78eb9.png',
};


export const AppTypeAvatarMap = {
  [IAppType.AgentCompletion]:
    // 'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/30777468162/ad8d/5293/6ac6/3aca8f0601155baf22567def470a8e47.png',
    'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/56843902638/ff36/5f14/b45e/6448c33466d472c7b35d916ba8edef58.png',
  [IAppType.AgentConversation]:
    // 'https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/30777465660/85d3/a012/cc43/af31c3c8415bce4cb25a3b95e7e76dd3.png',
    'https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/56843895747/5881/9ccf/d9d4/fdd98848d7e3a29d12b9057bab445ce9.png',
  [IAppType.Workflow]:
    // 'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/30777468163/3aec/cd1b/1fcd/ef3b3ca3b272da5894b73cb1341f49bc.png',
    'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/56843902894/c26e/e047/7fa7/d479ecbaa4b6fbbb131757bdca98af03.png',
  [IAppType.AgentWorkflow]:
    'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/56847184038/b515/a0bf/141b/ce6de586d5d09b3c5f2347a4198db299.png',
  [IAppType.VirtualHuman]:
    // 'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/30777468163/3aec/cd1b/1fcd/ef3b3ca3b272da5894b73cb1341f49bc.png',
    'https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/57010618441/c340/56b8/6197/2fa2b464188a0d01ba5f724b8e624367.png',
  [IAppType.StoryLine]:
    'https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/59850304811/cda9/b7a0/1e75/142bdbc390f4dd9fd202b5b139a7a825.png',
  [IAppType.Evaluator]:
    'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/60507412966/681b/14e7/ad9a/834b0aaa1ebd117c72761d9418fdb7cd.png'
};

export interface AppModel {
  id: string;
  type: IAppType;
  subType?: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  workspaceID: string;
  groupID: string;
  isTemplate: boolean;
  createdBy: UserModel;
  extInfo?: Record<string, any>;
  // 是否被当前应用收藏
  starred?: boolean;
}

export interface AppDetailModel extends AppModel {
  querySample?: string;
  config: AgentConfig;
  createdBy: UserModel;
  app_config_id?: string;
}

export interface AppTemplate {
  id: string;
  name: string;
  description: string;
  type: IAppType;
  config: AgentConfig;
  createdAt: string;
  updatedAt: string;
}

export interface AppCreateModel {
  name: string;
  description?: string;
  subType?: string;
  type: IAppType;
  config: AgentConfig;
  workflowId?: string;
  extInfo?: Record<string, any>;
}

export interface AppUpdateModel {
  name?: string;
  description?: string;
  querySample?: string;
  config?: Record<string, any>;
  extInfo?: Record<string, any>;
}

export interface Metric {
  date: string;
  count: number;
}
export interface IRunState {
  status: 'running' | 'queued' | 'success' | 'failed',
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  nodes: Array<Node>;
  message: '';
}

interface Node {
  nodeId: string;
  status: 'running' | 'queued' | 'success' | 'failed',
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  message: '';
}


export interface ToolDetail {
  description_for_llm: string
  parameters: {
    type: string,
    properties: Record<string, {
      title: string,
      description: string,
      type: string
    }>,
  }  // json schema
}

export interface Tool extends ToolDetail {
  id: string
  name: string
}

export const ConfigCardCnMap = {
  triggers: '触发器',
  aggregateInputConfig:'输入设置',
  ttsConfig:'语音输出设置',
  portraitConfigs:'聊天设置',
  plugins:'插件',
  memoryConfig:'记忆设置',
  emojiConfig:'表情包',
  animationConfig: '动画设置',
  knowledgeConfig:'知识库',
  paramsInPrompt: '变量',
  outputStrategyConfig: '文本输出设置'
};
