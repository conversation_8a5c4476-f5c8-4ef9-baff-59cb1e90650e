export enum IScopeType {
  GROUP = 'group',
  SCOPED = 'scoped',
  PUBLIC = 'public'
};

export const scopTypeCnMap = {
  [IScopeType.GROUP]: '组内访问',
  [IScopeType.SCOPED]: '租户内访问',
  [IScopeType.PUBLIC]: '公开(所有租户可见)',
};

export const scopeColorMap = {
  [IScopeType.GROUP]: '#c67ee9',
  [IScopeType.SCOPED]: '#ff8380',
  [IScopeType.PUBLIC]: '#83d4d4',
};

export enum IEvaluateType {
  NOT_STARTED = 1,
  EVALUATING = 2,
  EVALUATION_COMPLETED = 3,
  EVALUATION_FAILED = 4,
  EVALUATION_PART_SUCCESS = 5,
};

export const evaluateCnMap = {
  [IEvaluateType.NOT_STARTED]: '未开始',
  [IEvaluateType.EVALUATING]: '评测中',
  [IEvaluateType.EVALUATION_COMPLETED]: '评测完成',
  [IEvaluateType.EVALUATION_FAILED]: '评测失败',
  [IEvaluateType.EVALUATION_PART_SUCCESS]: '部分成功'
};

export const evaluateColorMap = {
  [IEvaluateType.NOT_STARTED]: 'default',
  [IEvaluateType.EVALUATING]: 'processing',
  [IEvaluateType.EVALUATION_COMPLETED]: 'success',
  [IEvaluateType.EVALUATION_FAILED]: 'error',
  [IEvaluateType.EVALUATION_PART_SUCCESS]: 'warning'
};

export const evaluatorRuleCnMap = {
  EQUALS: '全等',
  CONTAINS: '包含',
  REGEX: '正则',
  RANGE: '范围'
};

export enum IEvaluatorType {
  语义匹配 = 'SEMANTIC',
  规则匹配 = 'RULE'
};

export const evaluatorTypeCnMap = {
  SEMANTIC: '语义匹配',
  RULE: '规则匹配'
};

export const evaluatorTypeColorMap = {
  SEMANTIC: 'magenta',
  RULE: 'gold'
};
