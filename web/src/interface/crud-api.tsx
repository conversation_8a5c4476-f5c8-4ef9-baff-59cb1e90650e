export interface IdModel {
  id: string | number;
}

export interface CrudAPI<M extends IdModel, T> {
  get?: (resId: string | number) => Promise<M>;
  list: (params?: any, ...rest) => Promise<M[]>;
  listWithPage?: (params?: any, page?: number, pageSize?: number, ...rest) => Promise<any>;
  create: (params: T, parentResId?: string | number) => Promise<void>;
  update: (params: T, resId: string | number) => Promise<void>;
  remove: (resId: string | number) => Promise<void>;
}
