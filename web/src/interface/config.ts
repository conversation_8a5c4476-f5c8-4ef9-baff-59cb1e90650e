// 模型配置
export interface IModel {
  modelName: string;
  providerKind: string;
  modelParams: Record<string, any>;
}

export interface IModelSetting extends IModel {
  ratio: number;
}

export interface IRetryConfig {
  retryCount: number;
}

export interface IModelsConfig {
  models: IModelSetting[];
  retryConfig: IRetryConfig;
}

// 结构化提示词
type IPromptType = 'struct' | 'raw';

interface IStructPromptItem {
  id?: string;
  content: string;
  title: string;
  type: string;
}
export interface INewPrompt {
  prompt: string;
  promptType: IPromptType;
  structPrompt: IStructPromptItem[];
}

// 条件提示词
type IFilterType = 'dimension' | 'logical';
interface IFilterItem {
  type: IFilterType;
  condition: any;
  param: {
    code: string;
    fieldSource: string;
    name: string;
    type: string;
  };
}
interface IFilter {
  type: IFilterType;
  node: 'and';
  children: IFilterItem[];
}
interface IVirtualHumanStructPromptItem {
  id: string;
  name: string;
  prompt: string;
  filter: IFilter;
}
interface GroupItem {
  id: string;
  name: string;
  prompt: string;
  promptType: IPromptType;
  structPrompt?: IVirtualHumanStructPromptItem[];
}
export interface IGroupPrompt {
  groups: GroupItem[];
}

// 变量
export enum ParamTypeEnum {
  textInput = 'input',
  textArea = 'area',
  select = 'list',
  date = 'date',
  number = 'number',
  boolean = 'boolean',
}

export enum ParamDataTypeEnum {
  string = 'STRING',
  integer = 'INTEGER',
  long = 'LONG',
  double = 'DOUBLE',
  bool = 'BOOLEAN',
  time = 'DATE',
}

export type IParamsInPrompt = {
  key: string;
  title: string;
  default_val: any;
  // 前端渲染用
  type: ParamTypeEnum;
  // 后端数据类型
  dataType?: ParamDataTypeEnum;

  // 变量分组
  category: string;
  group: string;

  // 变量配置
  config: {
    options?: {
      value: string;
      title: string;
      uuid?: string;
    }[];
    length?: number;
  };
};

// 输入设置
export interface IAggregateInputConfig {
  enable: boolean;
  maxCount: number;
  maxTime: number;
  strategy: string;
}

// 输出设置
export interface IOutputStrategyConfig {
  enable: boolean;
}

// 语音输出设置
export enum TtsTypeEnum {
  none = 'NONE',
  random = 'RANDOM',
}

export interface IttsItem {
  voiceName: string;
  voiceId: string;
  ttsType: string;
  emotionJudge: boolean;
}

export interface ITtsConfig {
  strategicConfig: {
    ttsStrategicType: TtsTypeEnum;
    params: {
      tssRate: number;
    };
  };
  ttsConfigItems: IttsItem[];
}

// 聊天设置
interface IPortraitConfigItem {
  nosKey: string;
  type: 'PORTRAIT_2D';
  url: string;
}
export type IPortraitConfigs = IPortraitConfigItem[];

// 插件
interface IPluginItem {
  componentId: string;
  name: string;
  function: {
    name: string;
    description: string;
    required: string[];
    parameters: {
      type: string;
      properties: Record<string, any>;
    };
  };
}

export type IPlugins = IPluginItem[];

// 触发器

// 记忆
export type IMemoryType = 'NONE' | 'LOCAL' | 'PUBLIC';
export interface IMemoryConfig {
  shortMemoryRound: number;
  longMemoryType: IMemoryType;
}

// 知识库

type KnowledgeType = 'text' | 'table' | 'cio';
export interface Iknowledge {
  knowledgeId: number;
  knowledgeName: string;
  type: KnowledgeType;
  // 是否使用知识库下面所有的数据
  useAll: boolean;
}

type ISearchType = 'MIX' | 'FULL_TEXT' | 'SEMANTIC';
export interface ISearchConfig {
  searchType: ISearchType;
  filter: {
    limit: number;
    similarity: number;
  };
}
export interface NewKnowledgeConfig {
  knowledge?: Iknowledge[];
  searchConfig?: ISearchConfig;
}

interface IBasicConfig {
  modelsConfig: IModelsConfig;
  paramsInPrompt?: IParamsInPrompt[];
}

export interface IVirtualHumanConfig extends IBasicConfig {
  type?: string;
  aggregateInputConfig?: IAggregateInputConfig;
  outputStrategyConfig?: IOutputStrategyConfig;
  ttsConfig?: ITtsConfig;
  memoryConfig?: any;
}

export interface IConversationConfig extends IBasicConfig {
  welcomeText?: string;
  prePrompt?: string;
}

export interface ICompletionConfig extends IBasicConfig {
  prePrompt?: string;
}
