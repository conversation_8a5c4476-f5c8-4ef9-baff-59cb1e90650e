export interface Conversation {
  name: string;
  id: string;
}

export interface ConversationResponse {
  content: string
  messageID: string
  conversationID: string
}

export interface ToolCallInMessage {
  name: string;
  arguments?: string;
  response: string;
}

export interface ConversationStreamResponse {
  content?: string;
  messageID: string;
  conversationID: string;
  toolCalls?: ToolCallInMessage[],
}

export interface CompletionStreamResponse {
  toolCalls?: ToolCallInMessage[],
  content?: string;
  messageID: string;
  reasoning_content?: string;
  error?: string;
}
