export enum AuthResId {
  AppId = 'appId',
  GroupId = 'groupId',
  workspaceId = 'workspaceId',
}

export const AuthResMap = {
  [AuthResId.AppId]: '应用',
  [AuthResId.GroupId]: '业务组',
  [AuthResId.workspaceId]: '租户',
};

export enum RoleLevel {
  UnAuth = 0, // 无角色

  AppViewer = 10, // 应用访客
  GroupViewer = 20, // 业务组访客
  WorkspaceViewer = 30, // 租户访客

  AppDeveloper = 40, // 应用开发者
  GroupDeveloper = 50, // 业务组开发者
  WorkspaceDeveloper = 60, // 租户开发者

  AppAdmin = 70, // 应用管理员
  GroupAdmin = 80, // 项目组管理员
  WorkspaceAdmin = 90, // 租户管理员
  SystemAdmin = 100, // 系统管理员
}

export const RoleLevelMap = {
  [RoleLevel.UnAuth]: '无',
  [RoleLevel.WorkspaceViewer]: '租户成员',
  [RoleLevel.AppViewer]: '应用成员',
  [RoleLevel.GroupViewer]: '业务组成员',
  [RoleLevel.AppDeveloper]: '应用开发者',
  [RoleLevel.GroupDeveloper]: '业务组开发者',
  [RoleLevel.WorkspaceDeveloper]: '租户开发者',
  [RoleLevel.AppAdmin]: '应用管理员',
  [RoleLevel.GroupAdmin]: '业务组管理员',
  [RoleLevel.WorkspaceAdmin]: '租户管理员',
  [RoleLevel.SystemAdmin]: '系统管理员',
};

export const RoleLevelResMap = {
  [RoleLevel.UnAuth]: null,
  [RoleLevel.WorkspaceViewer]: AuthResId.workspaceId,
  [RoleLevel.AppViewer]: AuthResId.AppId,
  [RoleLevel.GroupViewer]: AuthResId.GroupId,
  [RoleLevel.AppDeveloper]: AuthResId.AppId,
  [RoleLevel.GroupDeveloper]: AuthResId.GroupId,
  [RoleLevel.WorkspaceDeveloper]: AuthResId.workspaceId,
  [RoleLevel.AppAdmin]: AuthResId.AppId,
  [RoleLevel.GroupAdmin]: AuthResId.GroupId,
  [RoleLevel.WorkspaceAdmin]: AuthResId.workspaceId,
  [RoleLevel.SystemAdmin]: null,
};
