export interface KnowledgeModel {
  id: string;
  workspaceId: string;
  groupId: string;
  name: string;
  description: string;
  collectionId: number;
  type: string;
  documentCount: number;
}

export interface KnowledgeUpdateModel {
  name: string;
  description: string;
  groupId: string;
  workspaceId: string;
}

/** --------- new knowledge types --------- **/

export type KnowledgeType = 'text' | 'table' | 'image' | 'cio' | 'all';

export interface CreateKnowledgeModel {
  workspaceId: string;
  groupId: string;
  name: string;
  type: string;
  description: string;
  extInfo: string;
  config: string;
}

export interface NewKnowledgeModel {
  id: string;
  documentCount: number;
  name: string;
  groupId: string;
  workspaceId: string;
  description: string;
  type: KnowledgeType;
}

export interface DocumentListItemModal {
  name: string;
  knowledgeId: string;
  knowledgeItemId: string;
  status: 'waiting' | 'fail' | 'success';
}

export interface NewDocumentModel {
  knowledgeId: string;
  knowledgeItemId: string;
  fragmentId: string;
  serialNum: string;
  content: string;
  extData: Record<string, any>;
}

export enum FileTypeEnum {
  文件夹 = 0,
  文档 = 1,
  md = 4
}
