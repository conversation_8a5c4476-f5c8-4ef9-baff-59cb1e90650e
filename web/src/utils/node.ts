/* eslint-disable @typescript-eslint/no-unused-vars */
import { Node } from '@antv/x6';
/* eslint-disable no-promise-executor-return */
import { uuidv4 } from '@antv/xflow';
import {
  IGraphCommandService,
  MODELS,
  NsJsonSchemaForm,
  XFlowEdgeCommands,
  XFlowNodeCommands,
} from '@antv/xflow';
import { NsEdgeCmd, NsGraph, NsNodeCmd } from '@antv/xflow';
import {
  GROUP_HEIGH,
  GROUP_WIDTH,
  NODE_HEIGHT_MAP,
  NODE_TYPE,
  PARAM_GAP,
  PARAM_HEIGHT,
  TITLE_HEIGHT,
} from '@utils/constant';
import { TypeMap } from '@utils/constant';

import { getGraphInstance } from '@/utils/flow';
import { saveGraphConfigToLocal } from '@/pages/app/workflow/config/cmds/hooks-services';

export const getRealType = (type) => {
  let dateType = type;
  let innerType = type;
  if (/array<(.*)>/.exec(dateType)) {
    dateType = 'array';
    innerType = RegExp.$1;
  }
  return [dateType, innerType];
};

/**
 * 解析portId
 * @param id
 * @returns
 */
export const parsePortId = (id) => {
  const [parentId, portId, dataType, type, index] = id.split('-');
  return {
    parentId,
    portId,
    dataType,
    type,
    index,
  };
};

/**
 * 映射输入输出成ports
 * @param arr
 * @param type
 * @param start
 * @returns
 */
export const mapPorts = ({
  prefix, arr, type,start = 0, showDateType = true, labelPosition = 'inside', inX, inY
}: {
  prefix: string,
  arr: any,
  type: NsGraph.AnchorType,
  start?: number,
  showDateType?: boolean,
  labelPosition?: string,
  inX?: string | number,
  inY?: string | number,
}) => {
  if (!arr || !Array.isArray(arr) || !arr.length) {
    return [];
  }
  return arr.map((port, idx) => {
    let newShowDateType = showDateType;
    if (port.type === '_logic_') {
      newShowDateType = false;
    }
    const index = idx + start;
    let x = type === NsGraph.AnchorType.INPUT ? 0 : '100%';
    x = inX || x;
    let y: any = index * (PARAM_HEIGHT + PARAM_GAP) + TITLE_HEIGHT + 4;
    // 如果是第0个节点，则需要挂到节点的头部，标识逻辑链接点
    if (index === 0) {
      y = TITLE_HEIGHT / 2 + 10;
    }
    y = inY || y;
    port.id = port.id || uuidv4().split('-')[0];
    const id = `${prefix}-${port.id}-${port.type}-${type}`;
    const [dateType, innerType] = getRealType(port.type);
    const stroke = (TypeMap[dateType] || { color: 'gray' }).color;
    const fill =
      dateType === 'array' ? (TypeMap[innerType] || { color: '#fff' }).color : '#fff';

    let attrs: any = {
      circle: {
        r: 6,
        magnet: true,
        strokeWidth: 2,
        stroke,
        fill,
      },
    };
    if (dateType === 'array') {
      attrs = {
        rect: {
          magnet: true,
          stroke,
          fill: '#fff',
          strokeWidth: 2,
          width: 16,
          height: 16,
          x: -8,
          y: -8,
        },
        dot: {
          magnet: true,
          stroke: fill,
          fill: '#fff',
          strokeWidth: 2,
          r: 3,
        },
      };
    }

    return {
      type,
      id,
      connected: type === NsGraph.AnchorType.OUTPUT,
      group: 'group1',
      args: { x, y },
      tooltip: '123',
      dataType: port.type,
      keyName: port.name,
      subType: port.subType,
      label: {
        position: labelPosition,
      },
      markup: [
        {
          tagName: 'rect',
          selector: 'rect',
          className: 'xflow-port',
        },
        {
          tagName: 'circle',
          selector: 'dot',
        },
        {
          tagName: 'circle',
          selector: 'circle',
          className: 'xflow-port',
        },
      ],
      data: {
        type: port.type,
        key: port.name,
        subType: port.subType,
      },
      attrs: {
        ...attrs,
        text: {
          text:
            index === 0
              ? ''
              : `${port.title || port.name}${newShowDateType ? `(${port.type})` : ''}`,
        },
      },
    };
  });
};

const addForEachPorts = (node: any) => {
  const { inputs, outputs, id, type } = node;
  const prefix = id.split('-')[0];
  const logicInputs = [
    {
      name: '逻辑连接点',
      type: '_logic_',
    },
  ];
  const [, innerType] = getRealType(inputs[0].type);
  const [, innerOutputType] = getRealType(outputs[0].type);
  const innerInputs = [
    {
      name: 'item',
      title: '元素',
      id: 'forItem',
      subType: 'forItem',
      type: innerType,
    },
    {
      name: 'index',
      title: '序号',
      id: 'forIndex',
      subType: 'forIndex',
      type: 'integer',
    },
  ];
  const innerOutputs = [
    {
      name: 'output',
      title: '元素输出',
      id: 'forOutput',
      subType: 'forOutput',
      type: innerOutputType,
    },
  ];
  let portItems: any = [];
  portItems = portItems.concat(
    mapPorts({ prefix, arr: logicInputs, type: NsGraph.AnchorType.INPUT }),
  );
  // 先添加组件的 外输入
  portItems = portItems.concat(
    mapPorts({ prefix, arr: inputs, type: NsGraph.AnchorType.INPUT, start: 1, labelPosition: 'outside', inX: -10 }),
  );

  // 再添加组件的 内输入
  portItems = portItems.concat(
    mapPorts({ prefix, arr: innerInputs, type: NsGraph.AnchorType.OUTPUT, start: 2, inX: 10 }),
  );

  // 添加组件的内输出
  portItems = portItems.concat(
    mapPorts({ prefix, arr: innerOutputs, type: NsGraph.AnchorType.INPUT, start: 1, inX: '100%' }),
  );

  // 添加组件的 外输入
  portItems = portItems.concat(
    mapPorts({ prefix, arr: outputs, type: NsGraph.AnchorType.OUTPUT, start: 2, labelPosition: 'outside', inX: '100%' }),
  );
  let height = TITLE_HEIGHT + portItems.length * (PARAM_GAP + PARAM_HEIGHT) + 200;
  if (height < node.height) {
    height = node.height;
  }

  return {
    ...node,
    height,
    width: node.width || GROUP_WIDTH,
    ports: {
      groups: {
        group1: {
          position: {
            name: 'absolute',
          },
        },
      },
      items: (portItems as NsGraph.INodeAnchor[]).map((port) => {
        return port;
      }),
    },
  };
};

/**
 * 给节点添加输入输出桩
 * @param node
 * @returns
 */
export const addPorts = (node: any) => {
  const { inputs, outputs, id, type } = node;
  if (type === NODE_TYPE.FOREACH) {
    return addForEachPorts(node);
  }
  const prefix = id.split('-')[0];
  const newInputs = [
    {
      name: '逻辑连接点',
      // 随便定一个id，由于prefix不同，所以不用担心重复，这里是防止逻辑节点每次都重新生成id
      id: 'abcdef',
      type: '_logic_',
    },
    ...(inputs || []),
  ];
  let portItems: any = [];
  if (newInputs) {
    portItems = portItems.concat(
      mapPorts({ prefix, arr: newInputs, type: NsGraph.AnchorType.INPUT }),
    );
  }
  if (outputs) {
    portItems = portItems.concat(
      mapPorts({
        prefix,
        arr: outputs,
        type: NsGraph.AnchorType.OUTPUT,
        start: portItems.length,
        showDateType: type !== NODE_TYPE.SWITCH,
      }),
    );
  }
  // 根据输入输出节点来决定元素的高度
  let height = 0;
  if (portItems.length > 0) {
    height = TITLE_HEIGHT + portItems.length * (PARAM_GAP + PARAM_HEIGHT) + 20;
  }
  if (node.type === NODE_TYPE.INPUT) {
    height = NODE_HEIGHT_MAP[node.code] || height + 40;
  }
  if (node.type === NODE_TYPE.FOREACH) {
    height = node.height;
  }

  return {
    ...node,
    height,
    ports: {
      groups: {
        group1: {
          position: {
            name: 'absolute',
          },
        },
      },
      items: (portItems as NsGraph.INodeAnchor[]).map((port) => {
        return port;
      }),
    },
  };
};

/**
 * 更新特定节点
 * @param node
 * @param commandService
 * @returns
 */
export const updateNode = (
  node: NsGraph.INodeConfig,
  commandService: IGraphCommandService,
) => {
  const newNode = addPorts(node);
  const graph = getGraphInstance();
  const cell = graph.getCellById(node.id);
  // 获取当前节点的所有输入边和输出边
  const incomingEdges = graph.getIncomingEdges(cell) || [];
  const outgoingEdges = graph.getOutgoingEdges(cell) || [];
  // 如果有入参更改，则删除所有修改的输入
  if (JSON.stringify(newNode.inputs) !== JSON.stringify(cell.data.inputs)) {
    const changeEdges = incomingEdges.filter(edge => {
      const targetId = edge?.data?.targetPortId;
      const { portId } = parsePortId(targetId);
      // 如果包含逻辑连接点，则不删除
      if (targetId.includes('logic_')) {
        return false;
      }
      const find = newNode.inputs.find(node => node.id === portId);
      return !find;
    });
    graph.removeCells(changeEdges);
  }
  if (JSON.stringify(newNode.outputs) !== JSON.stringify(node.outputs)) {
    graph.removeCells(outgoingEdges);
  }
  // console.log("node", newNode, node, graph.getCellById(node.id));
  // console.log("edges", incomingEdges, outgoingEdges);
  // const ports = newNode.ports.items;
  // 先删除ports，然后再添加ports
  (cell as Node).removePorts({ silent: true });
  (cell as Node).addPorts(newNode.ports.items);
  const portIds = newNode.ports.items.map((item) => item.id);
  // 如果节点输入输出的边对应的节点已经删除，则需要删除对应边
  const needDelEdges = [
    ...incomingEdges.filter((edge) => !portIds.includes(edge.data.targetPort)),
    ...outgoingEdges.filter((edge) => !portIds.includes(edge.data.sourcePort)),
  ];
  // console.log("needremove", needDelEdges);
  graph.removeCells(needDelEdges);

  return commandService.executeCommand<NsNodeCmd.UpdateNode.IArgs>(
    XFlowNodeCommands.UPDATE_NODE.id,
    { nodeConfig: newNode },
  ).then(res => {
    // 保存数据到本地
    saveGraphConfigToLocal();
    return res;
  })
};

/**
 * 更新特定的边
 * @param edge
 * @param commandService
 * @returns
 */
export const updateEdge = (
  edge: NsGraph.IEdgeConfig,
  commandService: IGraphCommandService,
) => {
  return commandService.executeCommand<NsEdgeCmd.UpdateEdge.IArgs>(
    XFlowEdgeCommands.UPDATE_EDGE.id,
    // @ts-ignore
    { edgeConfig: edge },
  );
};

/**
 *
 */
export const resizeGroup = async (group) => {
  const isCollapsed = group.getProp('isCollapsed');
  let originSize = group.getProp('originSize');
  let hasChange = false;

  if (originSize == null) {
    originSize = group.size();
    group.prop('originSize', originSize);
  }
  let originPosition = group.prop('originPosition');
  if (originPosition == null) {
    originPosition = group.getPosition();
    group.prop('originPosition', originPosition);
  }
  const graph = group.model.graph;

  let x = originPosition.x;
  let y = originPosition.y;
  let cornerX = originPosition.x + originSize.width;
  let cornerY = originPosition.y + originSize.height;
  const children = group.getChildren();
  if (children) {
    children.forEach((child) => {
      // 如果是边，不需要重置画布大小
      if (graph.isEdge(child)) {
        return;
      }
      const bbox = child.getBBox().inflate(80);
      const corner = bbox.getCorner();

      if (bbox.x < x) {
        x = bbox.x;
        hasChange = true;
      }

      if (bbox.y < y) {
        y = bbox.y;
        hasChange = true;
      }

      if (corner.x > cornerX) {
        cornerX = corner.x;
        hasChange = true;
      }

      if (corner.y > cornerY) {
        cornerY = corner.y;
        hasChange = true;
      }
    });
  }

  if (hasChange) {
    group.prop({
      position: { x, y },
      size: { width: cornerX - x, height: cornerY - y },
    });
    const groupData: NsGraph.INodeConfig = {
      ...group.getData(),
      x,
      y,
      width: cornerX - x,
      height: cornerY - y,
    };
    if (isCollapsed !== true) {
      groupData.groupChildrenSize = { width: cornerX - x, height: cornerY - y };
    }
    group.setData(groupData);
    // group.prop('originPosition', group.getPosition())
  }
};
