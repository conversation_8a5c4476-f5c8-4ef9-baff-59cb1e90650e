import type { IApplication } from '@antv/xflow-core'
import type { Graph } from '@antv/x6'
import { debounce } from 'lodash'

// 解决配置共享问题
export const globalProps = {
  config: {},
}

export const setProps = props => {
  globalProps.config = Object.assign({}, globalProps.config, props)
}
export const getProps = (key: string) => {
  return globalProps.config?.[key]
}

const instance = new Map<string, any>()
// const appInstance = new Map<string, IApplication>()

export const setInstance = async (app: IApplication) => {
  const x6graph = await app.getGraphInstance()
  instance.set('x6graph', x6graph);
  instance.set('app', app);
  (window as any)._UNSAFE_INSTANCE = instance;
}

export const getGraphInstance = () => {
  return instance.get('x6graph') as Graph
}

export const getAppInstance = () => {
  return instance.get('app')
}

export const getGraphData = async () => {
  const app = getAppInstance()
  // @ts-ignore
  return app.getGraphData()
}

/** 更新配置时通知上传执行保存 */
export const onConfigChange = debounce(
  config => {
    const configChange = getProps('onConfigChange')
    if (!configChange || typeof configChange !== 'function') {
      return
    }
    return configChange({
      data: getGraphData(),
      ...config,
    })
  },
  300,
  {
    trailing: true,
  },
)
