import randomColor from './random-color.js';

export const getColor = (seed: string) =>
  randomColor({
    seed,
    luminosity: 'bright',
  });

// 通用原子节点
export const ATOMIC_RENDER_ID = 'ATOMIC_NODE';
// 文本渲染
export const TEXT_RENDER_ID = 'INPUT_TEXT';
// 图片渲染节点
export const IMAGE_RENDER_ID = 'INPUT_IMAGE';
export const GROUP_NODE_RENDER_ID = 'GROUP_NODE_RENDER_ID';
export const NODE_WIDTH = 240;
export const NODE_HEIGHT = 40;
export const GROUP_WIDTH = 500;
export const GROUP_HEIGH = 410;
export const PARAM_HEIGHT = 25;
export const TITLE_DESC_HEIGHT = 0;
export const TITLE_HEIGHT = 30 + TITLE_DESC_HEIGHT;
export const PARAM_GAP = 5;
export const INPUT_HEIGHT = 200;
export const LAST_VISITED_WORKSPACE = 'last_visited_workspace';

export const NODE_CODE = {
  GPT_VERSION: 'LANGBASE_PROXY_ASYNC_CHAT_VISION_V1',
  DALLE: 'AGENT_DALL_E3',
  KNOWLEDGE_LLM: 'KNOWLEDGE_LLM',
  MOONSHOOT: 'LANGBASE_PROXY_CHAT_Moonshot',
  GPT: 'LANGBASE_PROXY_CHAT_V1',
  SUB_WORKFLOW: 'SUB_WORKFLOW',
  AI_MV: 'AI_MV_V1',
  CIO: 'CIO',
  SUB_CHATFLOW: 'CHATFLOW',
  SUB_COMPLETION: 'COMPLETION',
  INPUT_OBJECT: 'INPUT_OBJECT',
  INPUT_TEXT: 'INPUT_TEXT',
  INPUT_BOOL: 'INPUT_BOOL',
  INPUT_AUDIO: 'INPUT_AUDIO',
  INPUT_ENUM: 'INPUT_ENUM',
  INPUT_VIDEO: 'INPUT_VIDEO',
  INPUT_IMAGE: 'INPUT_IMAGE',
  INPUT_UPLOAD: 'INPUT_UPLOAD',
  INPUT_DATE: 'INPUT_DATE',
  INPUT_VAR: 'INPUT_VAR',
  INPUT_PARAM: 'INPUT_PARAM',
  INPUT_NUMBER: 'INPUT_NUMBER',
  INPUT_FLOAT: 'INPUT_FLOAT',
  INPUT_CIO: 'CIO_LOADER',
  INPUT_ARRAY: 'INPUT_ARRAY',
  TRANSFORM: 'TRANSFORM',
  TEXT_SHOW: 'TEXT_SHOW',
  JOIN: 'JOIN',
  EXTRACT: 'EXTRACT',
  MOCK: 'MOCK',
  UNI_HTTP: 'UNIVERSAL_HTTP',
  PYTHON: 'LANGBASE_PROXY_SCRIPT_PYTHON3_V1',
  LLM_TEST: 'wjj_test_241212',
  FUNCTION_CALL: 'FUNCTION_CALL',
  JS: 'LANGBASE_PROXY_SCRIPT_JS',
  AIR_SHIP: 'AIRSHIP',
  TEMPLATE: 'TEMPLATE',
  TEXT_SPLIT: 'TEXT_SPLIT',
  OUTPUT: 'OUTPUT',
  SCRIPT: 'SCRIPT',
  SWITCH: 'SWITCH',
  HTTP: 'HTTP',
  FOREACH: 'FOREACH',
  ATOMIC: 'ATOMIC',
  RECOGNIZE: 'RECOGNIZE',
  SKY_EYE: 'SKYEYE',
  KNOWLEDGE: 'KNOWLEDGE',
  NEW_KNOWLEDGE: 'NEW_KNOWLEDGE',
  GROOVY: 'GROOVY',
  INTENTION: 'INTENTION',
  LLM: 'LLM',
  SELECT: 'SELECT',
  PARAMETER_EXTRACT: 'PARAMETER_EXTRACT',
};

export const BotType = {
  AGENT: 'agent',
  AGENT_WORKFLOW: 'agent-workflow',
  AGENT_CONVERSATION: 'agent-conversation',
  CHAT_FLOW: 'chat-flow',
}

export const NODE_TYPE = {
  DISPLAY: 'DISPLAY',
  INPUT: 'INPUT',
  OUTPUT: 'OUTPUT',
  SCRIPT: 'SCRIPT',
  JOIN: 'JOIN',
  ATOMIC: 'HTTP',
  SWITCH: 'SWITCH',
  LLM: 'LLM',
  GROUP: 'FOREACH',
  SELECT: 'SELECT',
  HTTP: 'HTTP',
  FOREACH: 'FOREACH',
};

export const NODE_HEIGHT_MAP = {
  // [NODE_CODE.INPUT_CIO]: 140,
  [NODE_CODE.INPUT_VAR]: 100,
  [NODE_CODE.INPUT_DATE]: 140,
  [NODE_CODE.INPUT_FLOAT]: 140,
  [NODE_CODE.INPUT_NUMBER]: 140,
  [NODE_CODE.INPUT_TEXT]: 200,
  [NODE_CODE.INPUT_IMAGE]: 210,
  [NODE_CODE.INPUT_UPLOAD]: 210,
  [NODE_CODE.INPUT_AUDIO]: 210,
  [NODE_CODE.INPUT_ENUM]: 140,
  [NODE_CODE.INPUT_VIDEO]: 210,
  [NODE_CODE.INPUT_ARRAY]: 340,
  [NODE_CODE.INPUT_OBJECT]: 300,
};

/**
 * 所有继承关系
 */
const EXTEND_MAP: any = {};

export const getExtendsMap = () => {
  return EXTEND_MAP;
};

export const getOriginType = (type) => {
  const find = TypeMap[type];
  if (!find) {
    return;
  }
  return find.extends.split('-').slice(-2)[0];
};

export const TypeMap: any = {
  none: {
    color: '#bbb',
    extends: 'none-any',
  },
  _logic_: {
    color: '#bbb',
    extends: '_logic_',
  },
  any: {
    color: '#abc',
    extends: 'any',
  },
  string: {
    color: getColor('string'),
    extends: 'string-any',
  },
  number: {
    color: getColor('number'),
    extends: 'number-any',
  },
  array: {
    color: getColor('array'),
    extends: 'array-any',
  },
  integer: {
    color: getColor('number'),
    extends: 'integer-number-any',
  },
  float: {
    color: getColor('float'),
    extends: 'float-number-any',
  },
  datetime: {
    color: getColor('datetime'),
    extends: 'datetime-integer-number-any',
  },
  object: {
    color: getColor('object'),
    extends: 'object-any',
  },
  boolean: {
    color: getColor('boolean'),
    extends: 'boolean-any',
  },
  Url: {
    color: getColor('Url'),
    extends: 'Url-string-any',
  },
  Image: {
    color: getColor('Image'),
    extends: 'Image-Url-string-any',
  },
  Video: {
    color: getColor('Video'),
    extends: 'Video-Url-string-any',
  },
  Audio: {
    color: getColor('Audio'),
    extends: 'Audio-Url-string-any',
  },
  NosKey: {
    color: getColor('NosKey-new'),
    extends: 'NosKey-string-any',
  },
  ImageNosKey: {
    color: getColor('ImageNosKey'),
    extends: 'ImageNosKey-NosKey-string-any',
  },
  AudioNosKey: {
    color: getColor('AudioNosKey'),
    extends: 'AudioNosKey-NosKey-string-any',
  },
  AudioNosKeyObject: {
    color: getColor('AudioNosKeyObject'),
    extends: 'AudioNosKeyObject-object-any',
  },
  VideoNosKey: {
    color: getColor('VideoNosKey'),
    extends: 'VideoNosKey-NosKey-string-any',
  },
  Markdown: {
    color: getColor('Markdown'),
    extends: 'Markdown-string-any',
  }
};

Object.keys(TypeMap).map((key) => {
  EXTEND_MAP[key] = TypeMap[key].extends;
});

const typeWhitelist = ['none', '_logic_', 'any', 'number'];

export const typeOptions = Object.keys(TypeMap)
  .filter((type) => !typeWhitelist.includes(type))
  .map((v) => ({ value: v, label: v }));

export const objectTypes = Object.keys(TypeMap)
  .filter((type) => TypeMap[type].extends.includes('object'))
  .map((v) => ({ value: v, label: v }));

export const transTypes = Object.keys(TypeMap)
  .filter(
    (type) =>
      TypeMap[type].extends.includes('string') ||
      TypeMap[type].extends.includes('number') ||
      TypeMap[type].extends.includes('boolean'),
  )
  .map((v) => ({ value: v, label: v }));

export const MODULE_CONFIG_MAP = {
  gpt: {
    apiKey: '$LANGBASE_PROXY_CHAT_V1_API_KEY',
    apiBase: '$LANGBASE_PROXY_CHAT_V1_API_BASE',
    providerKind: 'openai',
  },
  moonshot: {
    apiKey: '$LANGBASE_PROXY_CHAT_MOONSHOT_API_KEY',
    apiBase: '$LANGBASE_PROXY_CHAT_MOONSHOT_API_BASE',
    providerKind: 'moonshot',
  }
}

export const MODULE_NAME_MAP = {
  'gpt-3.5-turbo': 'gpt',
  'gpt-3.5-turbo-16k': 'gpt',
  'gpt-4': 'gpt',
  'gpt-4-0613': 'gpt',
  'gpt-4-vision-preview': 'gpt',
  'moonshot-v1-8k': 'moonshot',
  'moonshot-v1-32k': 'moonshot',
  'moonshot-v1-128k': 'moonshot',
}