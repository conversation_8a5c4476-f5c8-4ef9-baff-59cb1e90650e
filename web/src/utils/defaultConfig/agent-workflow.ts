export default {
  "edges": [
    {
      "id": "a81e4a950:a81e4a950-029df515-string-output-o1a0e9544:o1a0e9544-5a94b841-string-input-1",
      "targetPortId": "o1a0e9544-5a94b841-string-input-1",
      "sourcePortId": "a81e4a950-029df515-string-output",
      "source": "a81e4a950",
      "target": "o1a0e9544",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "9270db6f-4348-47bf-afd7-47dd5cfff8ad",
        "source": {
          "cell": "a81e4a950",
          "port": "a81e4a950-029df515-string-output"
        },
        "target": {
          "cell": "o1a0e9544",
          "port": "o1a0e9544-5a94b841-string-input-1"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a81e4a950-029df515-string-output",
      "targetPort": "o1a0e9544-5a94b841-string-input-1"
    },
    {
      "id": "a83611872:a83611872-0f3a8c28-string-output-a1264771c:a1264771c-752f362d-string-input",
      "targetPortId": "a1264771c-752f362d-string-input",
      "sourcePortId": "a83611872-0f3a8c28-string-output",
      "source": "a83611872",
      "target": "a1264771c",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "8b3f8624-f0cd-48b9-bef7-db566e3126f3",
        "source": {
          "cell": "a83611872",
          "port": "a83611872-0f3a8c28-string-output"
        },
        "target": {
          "cell": "a1264771c",
          "port": "a1264771c-752f362d-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a83611872-0f3a8c28-string-output",
      "targetPort": "a1264771c-752f362d-string-input"
    },
    {
      "id": "a83611872:a83611872-0f3a8c28-string-output-a7d87b7cd:a7d87b7cd-086805f4-string-input",
      "targetPortId": "a7d87b7cd-086805f4-string-input",
      "sourcePortId": "a83611872-0f3a8c28-string-output",
      "source": "a83611872",
      "target": "a7d87b7cd",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "9f569aa2-0581-46ee-9e83-088988c13a9e",
        "source": {
          "cell": "a83611872",
          "port": "a83611872-0f3a8c28-string-output"
        },
        "target": {
          "cell": "a7d87b7cd",
          "port": "a7d87b7cd-086805f4-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a83611872-0f3a8c28-string-output",
      "targetPort": "a7d87b7cd-086805f4-string-input"
    },
    {
      "id": "a83611872:a83611872-0f3a8c28-string-output-a17de7567:a17de7567-51d3f073-string-input",
      "targetPortId": "a17de7567-51d3f073-string-input",
      "sourcePortId": "a83611872-0f3a8c28-string-output",
      "source": "a83611872",
      "target": "a17de7567",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "94d0d931-5d83-4ba9-bbc2-66eba51e0da9",
        "source": {
          "cell": "a83611872",
          "port": "a83611872-0f3a8c28-string-output"
        },
        "target": {
          "cell": "a17de7567",
          "port": "a17de7567-51d3f073-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a83611872-0f3a8c28-string-output",
      "targetPort": "a17de7567-51d3f073-string-input"
    },
    {
      "id": "a17de7567:a17de7567-8112e5e5-boolean-output-a8dea06fb:a8dea06fb-52e9d653-boolean-input",
      "targetPortId": "a8dea06fb-52e9d653-boolean-input",
      "sourcePortId": "a17de7567-8112e5e5-boolean-output",
      "source": "a17de7567",
      "target": "a8dea06fb",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "92060edc-f1f9-4d31-a17f-d00985153a4b",
        "source": {
          "cell": "a17de7567",
          "port": "a17de7567-8112e5e5-boolean-output"
        },
        "target": {
          "cell": "a8dea06fb",
          "port": "a8dea06fb-52e9d653-boolean-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#024363",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a17de7567-8112e5e5-boolean-output",
      "targetPort": "a8dea06fb-52e9d653-boolean-input"
    },
    {
      "id": "a8dea06fb:a8dea06fb-3336a5c8-_logic_-output-a3767c808:a3767c808-abcdef-_logic_-input",
      "targetPortId": "a3767c808-abcdef-_logic_-input",
      "sourcePortId": "a8dea06fb-3336a5c8-_logic_-output",
      "source": "a8dea06fb",
      "target": "a3767c808",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "b6825d43-2ece-419c-a459-bf5a99be7ed5",
        "source": {
          "cell": "a8dea06fb",
          "port": "a8dea06fb-3336a5c8-_logic_-output"
        },
        "target": {
          "cell": "a3767c808",
          "port": "a3767c808-abcdef-_logic_-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#bbb",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a8dea06fb-3336a5c8-_logic_-output",
      "targetPort": "a3767c808-abcdef-_logic_-input"
    },
    {
      "id": "a3767c808:a3767c808-172c320f-string-output-a12f0f990:a12f0f990-c14da541-string-input",
      "targetPortId": "a12f0f990-c14da541-string-input",
      "sourcePortId": "a3767c808-172c320f-string-output",
      "source": "a3767c808",
      "target": "a12f0f990",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "4c9e9d18-d91f-475e-9961-b5f89f0298ad",
        "source": {
          "cell": "a3767c808",
          "port": "a3767c808-172c320f-string-output"
        },
        "target": {
          "cell": "a12f0f990",
          "port": "a12f0f990-c14da541-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a3767c808-172c320f-string-output",
      "targetPort": "a12f0f990-c14da541-string-input"
    },
    {
      "id": "a3767c808:a3767c808-795acb5f-string-output-a12f0f990:a12f0f990-df82cafc-string-input",
      "targetPortId": "a12f0f990-df82cafc-string-input",
      "sourcePortId": "a3767c808-795acb5f-string-output",
      "source": "a3767c808",
      "target": "a12f0f990",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "892ec36a-e795-4bf9-8851-cb5614ad64f6",
        "source": {
          "cell": "a3767c808",
          "port": "a3767c808-795acb5f-string-output"
        },
        "target": {
          "cell": "a12f0f990",
          "port": "a12f0f990-df82cafc-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a3767c808-795acb5f-string-output",
      "targetPort": "a12f0f990-df82cafc-string-input"
    },
    {
      "id": "a12f0f990:a12f0f990-ebc70a02-string-output-a1bdf9edd:a1bdf9edd-1325da0f-string-input",
      "targetPortId": "a1bdf9edd-1325da0f-string-input",
      "sourcePortId": "a12f0f990-ebc70a02-string-output",
      "source": "a12f0f990",
      "target": "a1bdf9edd",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "7640215c-6d30-4d8a-b256-aa9c237649cb",
        "source": {
          "cell": "a12f0f990",
          "port": "a12f0f990-ebc70a02-string-output"
        },
        "target": {
          "cell": "a1bdf9edd",
          "port": "a1bdf9edd-1325da0f-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a12f0f990-ebc70a02-string-output",
      "targetPort": "a1bdf9edd-1325da0f-string-input"
    },
    {
      "id": "a83611872:a83611872-0f3a8c28-string-output-ac0faf3ee:ac0faf3ee-52d41eb0-string-input",
      "targetPortId": "ac0faf3ee-52d41eb0-string-input",
      "sourcePortId": "a83611872-0f3a8c28-string-output",
      "source": "a83611872",
      "target": "ac0faf3ee",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "edd97422-cc53-43d2-9114-e4c6740c0e27",
        "source": {
          "cell": "a83611872",
          "port": "a83611872-0f3a8c28-string-output"
        },
        "target": {
          "cell": "ac0faf3ee",
          "port": "ac0faf3ee-52d41eb0-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a83611872-0f3a8c28-string-output",
      "targetPort": "ac0faf3ee-52d41eb0-string-input"
    },
    {
      "id": "a7d87b7cd:a7d87b7cd-f84dfaaa-_logic_-output-a1264771c:a1264771c-abcdef-_logic_-input",
      "targetPortId": "a1264771c-abcdef-_logic_-input",
      "sourcePortId": "a7d87b7cd-f84dfaaa-_logic_-output",
      "source": "a7d87b7cd",
      "target": "a1264771c",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "576a2b52-33e9-432e-b59b-898a7f19afe9",
        "source": {
          "cell": "a7d87b7cd",
          "port": "a7d87b7cd-f84dfaaa-_logic_-output"
        },
        "target": {
          "cell": "a1264771c",
          "port": "a1264771c-abcdef-_logic_-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#bbb",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a7d87b7cd-f84dfaaa-_logic_-output",
      "targetPort": "a1264771c-abcdef-_logic_-input"
    },
    {
      "id": "a1bdf9edd:a1bdf9edd-cd56559a-string-output-a81e4a950:a81e4a950-7026025c-string-input",
      "targetPortId": "a81e4a950-7026025c-string-input",
      "sourcePortId": "a1bdf9edd-cd56559a-string-output",
      "source": "a1bdf9edd",
      "target": "a81e4a950",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "bae2b514-99ea-44b2-8ed0-5b6aba4dfa4a",
        "source": {
          "cell": "a1bdf9edd",
          "port": "a1bdf9edd-cd56559a-string-output"
        },
        "target": {
          "cell": "a81e4a950",
          "port": "a81e4a950-7026025c-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a1bdf9edd-cd56559a-string-output",
      "targetPort": "a81e4a950-7026025c-string-input"
    },
    {
      "id": "a1264771c:a1264771c-7093913a-string-output-a81e4a950:a81e4a950-7026025c-string-input",
      "targetPortId": "a81e4a950-7026025c-string-input",
      "sourcePortId": "a1264771c-7093913a-string-output",
      "source": "a1264771c",
      "target": "a81e4a950",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "08c9aed7-c145-415e-b700-f93791a9956a",
        "source": {
          "cell": "a1264771c",
          "port": "a1264771c-7093913a-string-output"
        },
        "target": {
          "cell": "a81e4a950",
          "port": "a81e4a950-7026025c-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a1264771c-7093913a-string-output",
      "targetPort": "a81e4a950-7026025c-string-input"
    },
    {
      "id": "ac0faf3ee:ac0faf3ee-75a64b0f-string-output-a81e4a950:a81e4a950-7026025c-string-input",
      "targetPortId": "a81e4a950-7026025c-string-input",
      "sourcePortId": "ac0faf3ee-75a64b0f-string-output",
      "source": "ac0faf3ee",
      "target": "a81e4a950",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "fbe5560c-687b-4574-bc3c-3c27649e31b0",
        "source": {
          "cell": "ac0faf3ee",
          "port": "ac0faf3ee-75a64b0f-string-output"
        },
        "target": {
          "cell": "a81e4a950",
          "port": "a81e4a950-7026025c-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "ac0faf3ee-75a64b0f-string-output",
      "targetPort": "a81e4a950-7026025c-string-input"
    },
    {
      "id": "a17de7567:a17de7567-4957be8f-object-output-a3767c808:a3767c808-d7bcb7bb-object-input",
      "targetPortId": "a3767c808-d7bcb7bb-object-input",
      "sourcePortId": "a17de7567-4957be8f-object-output",
      "source": "a17de7567",
      "target": "a3767c808",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "7d4d9c49-0ed1-4089-afcd-60d0f1610f33",
        "source": {
          "cell": "a17de7567",
          "port": "a17de7567-4957be8f-object-output"
        },
        "target": {
          "cell": "a3767c808",
          "port": "a3767c808-d7bcb7bb-object-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#57e576",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a17de7567-4957be8f-object-output",
      "targetPort": "a3767c808-d7bcb7bb-object-input"
    },
    {
      "id": "a8dea06fb:a8dea06fb-07f18b3d-_logic_-output-a435adc26:a435adc26-abcdef-_logic_-input",
      "targetPortId": "a435adc26-abcdef-_logic_-input",
      "sourcePortId": "a8dea06fb-07f18b3d-_logic_-output",
      "source": "a8dea06fb",
      "target": "a435adc26",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "7f128075-3677-4945-83e1-c883135a08e7",
        "source": {
          "cell": "a8dea06fb",
          "port": "a8dea06fb-07f18b3d-_logic_-output"
        },
        "target": {
          "cell": "a435adc26",
          "port": "a435adc26-abcdef-_logic_-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#bbb",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a8dea06fb-07f18b3d-_logic_-output",
      "targetPort": "a435adc26-abcdef-_logic_-input"
    },
    {
      "id": "a435adc26:a435adc26-0bfe5074-string-output-a1bdf9edd:a1bdf9edd-1325da0f-string-input",
      "targetPortId": "a1bdf9edd-1325da0f-string-input",
      "sourcePortId": "a435adc26-0bfe5074-string-output",
      "source": "a435adc26",
      "target": "a1bdf9edd",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "ccaa179a-da43-46d3-a9f6-fcf18afda6ca",
        "source": {
          "cell": "a435adc26",
          "port": "a435adc26-0bfe5074-string-output"
        },
        "target": {
          "cell": "a1bdf9edd",
          "port": "a1bdf9edd-1325da0f-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a435adc26-0bfe5074-string-output",
      "targetPort": "a1bdf9edd-1325da0f-string-input"
    },
    {
      "id": "a7d87b7cd:a7d87b7cd-83bd50ce-_logic_-output-ac0faf3ee:ac0faf3ee-abcdef-_logic_-input",
      "targetPortId": "ac0faf3ee-abcdef-_logic_-input",
      "sourcePortId": "a7d87b7cd-83bd50ce-_logic_-output",
      "source": "a7d87b7cd",
      "target": "ac0faf3ee",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "58e0cfd0-c115-41e7-bf2d-f0686408c782",
        "source": {
          "cell": "a7d87b7cd",
          "port": "a7d87b7cd-83bd50ce-_logic_-output"
        },
        "target": {
          "cell": "ac0faf3ee",
          "port": "ac0faf3ee-abcdef-_logic_-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#bbb",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a7d87b7cd-83bd50ce-_logic_-output",
      "targetPort": "ac0faf3ee-abcdef-_logic_-input"
    },
    {
      "id": "a7d87b7cd:a7d87b7cd-6399c70a-_logic_-output-a17de7567:a17de7567-abcdef-_logic_-input",
      "targetPortId": "a17de7567-abcdef-_logic_-input",
      "sourcePortId": "a7d87b7cd-6399c70a-_logic_-output",
      "source": "a7d87b7cd",
      "target": "a17de7567",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "4ca39158-e966-4bbb-ab9c-d4dd3a316aee",
        "source": {
          "cell": "a7d87b7cd",
          "port": "a7d87b7cd-6399c70a-_logic_-output"
        },
        "target": {
          "cell": "a17de7567",
          "port": "a17de7567-abcdef-_logic_-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#bbb",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a7d87b7cd-6399c70a-_logic_-output",
      "targetPort": "a17de7567-abcdef-_logic_-input"
    },
    {
      "id": "a7d87b7cd:a7d87b7cd-d2465518-_logic_-output-aee808a73:aee808a73-abcdef-_logic_-input",
      "targetPortId": "aee808a73-abcdef-_logic_-input",
      "sourcePortId": "a7d87b7cd-d2465518-_logic_-output",
      "source": "a7d87b7cd",
      "target": "aee808a73",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "60f38293-ce16-4367-8c84-9420206de475",
        "source": {
          "cell": "a7d87b7cd",
          "port": "a7d87b7cd-d2465518-_logic_-output"
        },
        "target": {
          "cell": "aee808a73",
          "port": "aee808a73-abcdef-_logic_-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#bbb",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a7d87b7cd-d2465518-_logic_-output",
      "targetPort": "aee808a73-abcdef-_logic_-input"
    },
    {
      "id": "a83611872:a83611872-0f3a8c28-string-output-aee808a73:aee808a73-3d7b7048-string-input",
      "targetPortId": "aee808a73-3d7b7048-string-input",
      "sourcePortId": "a83611872-0f3a8c28-string-output",
      "source": "a83611872",
      "target": "aee808a73",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "6320cd51-9c6f-4c66-b34c-d59b3ba69266",
        "source": {
          "cell": "a83611872",
          "port": "a83611872-0f3a8c28-string-output"
        },
        "target": {
          "cell": "aee808a73",
          "port": "aee808a73-3d7b7048-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a83611872-0f3a8c28-string-output",
      "targetPort": "aee808a73-3d7b7048-string-input"
    },
    {
      "id": "aee808a73:aee808a73-1842b91f-string-output-ae4ae5151:ae4ae5151-bf720fd1-string-input",
      "targetPortId": "ae4ae5151-bf720fd1-string-input",
      "sourcePortId": "aee808a73-1842b91f-string-output",
      "source": "aee808a73",
      "target": "ae4ae5151",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "c628ab06-6511-4f1d-896c-591c1375899a",
        "source": {
          "cell": "aee808a73",
          "port": "aee808a73-1842b91f-string-output"
        },
        "target": {
          "cell": "ae4ae5151",
          "port": "ae4ae5151-bf720fd1-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "aee808a73-1842b91f-string-output",
      "targetPort": "ae4ae5151-bf720fd1-string-input"
    },
    {
      "id": "ae4ae5151:ae4ae5151-7ff05652-string-output-ae8dad6c9:ae8dad6c9-72ce25a2-string-input",
      "targetPortId": "ae8dad6c9-72ce25a2-string-input",
      "sourcePortId": "ae4ae5151-7ff05652-string-output",
      "source": "ae4ae5151",
      "target": "ae8dad6c9",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "1ba732ae-b17a-4f88-9698-4ca66d2ad6f5",
        "source": {
          "cell": "ae4ae5151",
          "port": "ae4ae5151-7ff05652-string-output"
        },
        "target": {
          "cell": "ae8dad6c9",
          "port": "ae8dad6c9-72ce25a2-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "ae4ae5151-7ff05652-string-output",
      "targetPort": "ae8dad6c9-72ce25a2-string-input"
    },
    {
      "id": "a83611872:a83611872-0f3a8c28-string-output-ae8dad6c9:ae8dad6c9-5b57ad0f-string-input",
      "targetPortId": "ae8dad6c9-5b57ad0f-string-input",
      "sourcePortId": "a83611872-0f3a8c28-string-output",
      "source": "a83611872",
      "target": "ae8dad6c9",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "4c23bb42-3469-4d81-84b3-2051b5dfc965",
        "source": {
          "cell": "a83611872",
          "port": "a83611872-0f3a8c28-string-output"
        },
        "target": {
          "cell": "ae8dad6c9",
          "port": "ae8dad6c9-5b57ad0f-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "a83611872-0f3a8c28-string-output",
      "targetPort": "ae8dad6c9-5b57ad0f-string-input"
    },
    {
      "id": "ae8dad6c9:ae8dad6c9-fe2e79e1-string-output-a81e4a950:a81e4a950-7026025c-string-input",
      "targetPortId": "a81e4a950-7026025c-string-input",
      "sourcePortId": "ae8dad6c9-fe2e79e1-string-output",
      "source": "ae8dad6c9",
      "target": "a81e4a950",
      "edge": {
        "shape": "xflow-edge",
        "attrs": {
          "line": {
            "stroke": "#d5d5d5",
            "strokeWidth": 1,
            "targetMarker": "",
            "strokeDasharray": ""
          }
        },
        "zIndex": 1,
        "highlight": false,
        "id": "ad871e25-a80a-4831-a000-74b48fd80bf5",
        "source": {
          "cell": "ae8dad6c9",
          "port": "ae8dad6c9-fe2e79e1-string-output"
        },
        "target": {
          "cell": "a81e4a950",
          "port": "a81e4a950-7026025c-string-input"
        }
      },
      "attrs": {
        "line": {
          "strokeDasharray": "",
          "targetMarker": {
            "name": "circle",
            "r": 5
          },
          "stroke": "#0929f9",
          "strokeWidth": 3
        }
      },
      "sourcePort": "ae8dad6c9-fe2e79e1-string-output",
      "targetPort": "a81e4a950-7026025c-string-input"
    }
  ],
  "inputs": [
    {
      "name": "message",
      "type": "string",
      "title": "用户输入",
      "id": "0f3a8c28"
    }
  ],
  "outputs": [
    {
      "id": "5a94b841",
      "title": "AI输出",
      "name": "output",
      "type": "string"
    }
  ],
  "nodes": [
    {
      "id": "o1a0e9544",
      "category": "OUTPUT",
      "code": "OUTPUT",
      "type": "OUTPUT",
      "description": "123",
      "x": 2140,
      "y": -100,
      "height": 100,
      "width": 240,
      "renderKey": "OUTPUT",
      "icon": "CodeOutlined",
      "inputs": [
        {
          "id": "5a94b841",
          "title": "AI输出",
          "name": "output",
          "type": "string"
        }
      ],
      "vars": [],
      "componentId": "49048006-4c0c-4258-896e-6520b9c8dcaa",
      "name": "结束",
      "ports": {
        "items": [
          {
            "id": "o1a0e9544-ab780565-_logic_-input-0",
            "args": {
              "x": 0,
              "y": 25
            },
            "data": {
              "key": "逻辑连接点",
              "type": "_logic_"
            },
            "type": "input",
            "attrs": {
              "text": {
                "text": ""
              },
              "circle": {
                "r": 6,
                "fill": "#fff",
                "magnet": true,
                "stroke": "#bbb",
                "strokeWidth": 2
              }
            },
            "group": "group1",
            "label": {
              "position": "inside"
            },
            "keyName": "逻辑连接点",
            "tooltip": null,
            "dataType": "_logic_"
          },
          {
            "id": "o1a0e9544-5a94b841-string-input-1",
            "args": {
              "x": 0,
              "y": 64
            },
            "data": {
              "key": "output",
              "type": "string"
            },
            "type": "input",
            "attrs": {
              "text": {
                "text": "AI输出(string)"
              },
              "circle": {
                "r": 6,
                "fill": "#fff",
                "magnet": true,
                "stroke": "#0929f9",
                "strokeWidth": 2
              }
            },
            "group": "group1",
            "label": {
              "position": "inside"
            },
            "keyName": "param1",
            "tooltip": null,
            "dataType": "string"
          }
        ],
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        }
      },
      "groupChildren": null,
      "groupCollapsedSize": null,
      "fixed": true
    },
    {
      "no": null,
      "id": "a83611872",
      "category": "输入",
      "code": "INPUT_VAR",
      "type": "INPUT",
      "description": "用于输入/展示文本",
      "x": -540,
      "y": -70,
      "height": 140,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "icon": "FontSizeOutlined",
      "inputs": [],
      "outputs": [
        {
          "name": "message",
          "type": "string",
          "title": "用户输入",
          "id": "0f3a8c28"
        }
      ],
      "configs": [
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "method",
          "value": "POST"
        }
      ],
      "vars": [],
      "componentId": "62b9f309-48ac-41e0-a3ca-8edf81182d9f",
      "name": "开始",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a83611872-72837c7b-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "output",
            "id": "a83611872-0f3a8c28-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "message",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "message"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "用户输入(string)"
              }
            }
          }
        ]
      },
      "groupChildren": null,
      "groupCollapsedSize": null,
      "fixed": true
    },
    {
      "no": null,
      "id": "a1264771c",
      "branch": "a7d87b7cd",
      "category": "大模型",
      "code": "LLM",
      "type": "HTTP",
      "description": "Langbase chat",
      "x": 310,
      "y": 400,
      "height": 140,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "LLM",
      "icon": "<svg t=\"1716780013900\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"9799\" width=\"200\" height=\"200\"><path d=\"M512 64c259.2 0 469.333 200.576 469.333 448S771.2 960 512 960a484.48 484.48 0 0 1-232.725-58.88L162.88 951.765a42.667 42.667 0 0 1-58.517-49.002l29.76-125.014C76.629 703.403 42.667 611.477 42.667 512 42.667 264.576 252.8 64 512 64z m0 64c-224.512 0-405.333 172.587-405.333 384 0 79.573 25.557 155.435 72.554 219.285l5.526 7.318 18.709 24.192-26.965 113.237 105.984-46.08 27.477 15.019C370.859 878.229 439.979 896 512 896c224.512 0 405.333-172.587 405.333-384S736.512 128 512 128zM354.304 469.333a42.667 42.667 0 1 1 0 85.334 42.667 42.667 0 0 1 0-85.334z m159.019 0a42.667 42.667 0 1 1 0 85.334 42.667 42.667 0 0 1 0-85.334z m158.997 0a42.667 42.667 0 1 1 0 85.334 42.667 42.667 0 0 1 0-85.334z\" fill=\"#333333\" p-id=\"9800\"></path></svg>",
      "inputs": [
        {
          "name": "content",
          "type": "string",
          "title": "用户输入",
          "id": "752f362d"
        }
      ],
      "outputs": [
        {
          "name": "content",
          "type": "string",
          "title": "GPT输出",
          "id": "7093913a"
        }
      ],
      "configs": [
        {
          "name": "asyncApi",
          "type": "boolean",
          "value": "true"
        },
        {
          "name": "url",
          "value": "http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes/langbase/proxy/chat"
        },
        {
          "name": "method",
          "value": "POST"
        },
        {
          "name": "timeout",
          "value": 20000
        },
        {
          "name": "_langbase_id",
          "type": "string",
          "value": "1fc42abf-c358-460c-a8a7-ef0629b53ebc"
        }
      ],
      "vars": [
        {
          "name": "frequency_penalty",
          "type": "float",
          "value": 0
        },
        {
          "name": "max_tokens",
          "type": "integer",
          "value": 512
        },
        {
          "name": "modelName",
          "type": "string",
          "value": "deepseek-v3-latest"
        },
        {
          "name": "presence_penalty",
          "type": "float",
          "value": 0
        },
        {
          "name": "providerKind",
          "type": "string",
          "value": "deepseek"
        },
        {
          "name": "system_prompt",
          "type": "string",
          "value": "你是一个LangBase智能助理，友好的回答用户的提问"
        },
        {
          "name": "temperature",
          "type": "float",
          "value": 1
        },
        {
          "name": "timeout",
          "type": "integer",
          "value": 20000
        },
        {
          "name": "top_p",
          "type": "float",
          "value": 0.85
        }
      ],
      "componentId": "1306b9ea-3a71-4739-b938-b4e120f4cd52",
      "name": "兜底大语言模型",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a1264771c-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "a1264771c-752f362d-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "content",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "content"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "用户输入(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "a1264771c-7093913a-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "content",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "content"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "GPT输出(string)"
              }
            }
          }
        ]
      },
      "async": true,
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": null,
      "id": "a81e4a950",
      "branch": "",
      "category": "逻辑",
      "code": "SELECT",
      "type": "SELECT",
      "description": "用于switch的多分支结果合并后进行后续处理",
      "x": 1820,
      "y": -130,
      "height": 140,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "SELECT",
      "icon": "icon-merge",
      "inputs": [
        {
          "name": "param1",
          "type": "string",
          "title": "参数1",
          "id": "7026025c"
        }
      ],
      "outputs": [
        {
          "name": "param1",
          "type": "string",
          "title": "参数1",
          "id": "029df515"
        }
      ],
      "configs": [
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "method",
          "value": "POST"
        }
      ],
      "vars": [],
      "componentId": "58184ba6-16ba-4b59-9fc5-cfdb3990c36a",
      "name": "分支合并",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a81e4a950-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "a81e4a950-7026025c-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "param1",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "param1"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "参数1(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "a81e4a950-029df515-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "param1",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "param1"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "参数1(string)"
              }
            }
          }
        ]
      },
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": null,
      "id": "a8dea06fb",
      "branch": "a7d87b7cd-a8dea06fb",
      "category": "逻辑",
      "code": "SWITCH",
      "type": "SWITCH",
      "description": "可以根据输入条件，拆分成多条分支",
      "x": 630,
      "y": -320,
      "height": 170,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "SWITCH",
      "icon": "icon-fork",
      "inputs": [
        {
          "name": "switchVal",
          "type": "boolean",
          "title": "输入",
          "id": "52e9d653"
        }
      ],
      "outputs": [
        {
          "name": "true",
          "type": "_logic_",
          "title": "真",
          "id": "3336a5c8"
        },
        {
          "name": "false",
          "type": "_logic_",
          "title": "假",
          "id": "07f18b3d"
        }
      ],
      "configs": [
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "method",
          "value": "POST"
        }
      ],
      "vars": [],
      "componentId": "00b5052b-47ad-4a04-80a2-32f00433d1ba",
      "name": "分支组件",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a8dea06fb-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "a8dea06fb-52e9d653-boolean-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "boolean",
            "keyName": "switchVal",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "boolean",
              "key": "switchVal"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#024363",
                "fill": "#fff"
              },
              "text": {
                "text": "输入(boolean)"
              }
            }
          },
          {
            "type": "output",
            "id": "a8dea06fb-3336a5c8-_logic_-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "true",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "true"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": "真"
              }
            }
          },
          {
            "type": "output",
            "id": "a8dea06fb-07f18b3d-_logic_-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 124
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "false",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "false"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": "假"
              }
            }
          }
        ]
      },
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 1,
      "id": "a1bdf9edd",
      "branch": "a7d87b7cd",
      "category": "逻辑",
      "code": "SELECT",
      "type": "SELECT",
      "description": "用于switch的多分支结果合并后进行后续处理",
      "x": 1530,
      "y": -220,
      "height": 140,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "SELECT",
      "icon": "icon-merge",
      "inputs": [
        {
          "name": "param1",
          "type": "string",
          "title": "参数1",
          "id": "1325da0f"
        }
      ],
      "outputs": [
        {
          "name": "param1",
          "type": "string",
          "title": "参数1",
          "id": "cd56559a"
        }
      ],
      "configs": [
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "method",
          "value": "POST"
        }
      ],
      "vars": [],
      "componentId": "58184ba6-16ba-4b59-9fc5-cfdb3990c36a",
      "name": "分支合并1",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a1bdf9edd-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "a1bdf9edd-1325da0f-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "param1",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "param1"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "参数1(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "a1bdf9edd-cd56559a-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "param1",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "param1"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "参数1(string)"
              }
            }
          }
        ]
      },
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 1,
      "id": "a7d87b7cd",
      "category": "大模型",
      "code": "INTENTION",
      "type": "LLM",
      "description": "根据用户的输入自动匹配对应的意图",
      "x": -50,
      "y": -70,
      "height": 260,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "INTENTION",
      "icon": "icon-brain",
      "inputs": [
        {
          "name": "content",
          "type": "string",
          "title": "文本输入",
          "id": "086805f4"
        }
      ],
      "outputs": [
        {
          "name": "AI换脸",
          "type": "_logic_",
          "title": "AI换脸",
          "id": "83bd50ce"
        },
        {
          "name": "查询歌曲信息",
          "type": "_logic_",
          "title": "查询歌曲信息",
          "id": "6399c70a"
        },
        {
          "name": "langbase问答",
          "type": "_logic_",
          "title": "langbase问答",
          "id": "d2465518"
        },
        {
          "name": "none",
          "type": "_logic_",
          "title": "未匹配",
          "id": "f84dfaaa"
        },
        {
          "name": "content",
          "type": "string",
          "title": "分类",
          "id": "bb4de87c"
        }
      ],
      "configs": [
        {
          "name": "asyncApi",
          "type": "boolean",
          "value": "true"
        },
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "method",
          "value": "POST"
        },
        {
          "name": "timeout",
          "value": 40000
        },
        {
          "name": "categorys",
          "value": [
            {
              "name": "AI换脸",
              "type": "string",
              "description": "用户提及换脸，AI换脸"
            },
            {
              "name": "查询歌曲信息",
              "type": "string",
              "description": "查询歌曲信息，歌词信息，歌曲详情"
            },
            {
              "name": "langbase问答",
              "type": "string",
              "description": "关于langbase的问答信息"
            }
          ]
        }
      ],
      "vars": [
        {
          "enum": [],
          "name": "modelName",
          "type": "string",
          "title": "模型名",
          "value": "gpt-4o-mini-2024-07-18"
        },
        {
          "name": "system_prompt",
          "type": "string",
          "title": "system prompt",
          "value": "\n你需要分析用户输入的上下文，解析用户想做的事情，从指令集中匹配一个最相关的指令，如果没有匹配的指令，返回'none'，指令集的结构如下:\n---\n- 指令: 指令描述\n---\n\n## 指令集 \n- AI换脸: 用户提及换脸，AI换脸\n- 查询歌曲信息: 查询歌曲信息，歌词信息，歌曲详情\n- langbase问答: 关于langbase的问答信息\n- none: 未匹配上面任意指令\n\n## 要求\n你只能输出[指令集]中的指令，不能输出不在[指令集]外的内容。\n\n## 示例\n假设我们指令集如下：\n- greeting: 打招呼\n- code: 生成代码\n- none: 未匹配上面任意指令\n\n### Example1\n用户输入: 你好\n输出: greeting\n\n### Example2\n用户输入: 帮我生成一个代码\n输出: code\n\n### Example3\n用户输入: 今天天气真好\n输出: none\n\n"
        },
        {
          "name": "providerKind",
          "type": "string",
          "title": "代理商",
          "value": "openai"
        },
        {
          "name": "temperature",
          "rule": "{ \"type\": \"number\", \"max\": 1, \"min\": 0 }",
          "type": "float",
          "title": "随机性",
          "value": 1
        },
        {
          "name": "max_tokens",
          "rule": "{\"type\": \"number\",  \"max\": 4096, \"min\": 100 }",
          "type": "integer",
          "title": "最大token数",
          "value": 512
        },
        {
          "name": "frequency_penalty",
          "rule": "{ \"type\": \"number\", \"max\": 1, \"min\": 0 }",
          "type": "string",
          "title": "frequency_penalty",
          "value": "0"
        },
        {
          "name": "presence_penalty",
          "rule": "{ \"type\": \"number\", \"max\": 1, \"min\": 0 }",
          "type": "float",
          "title": "presence_penalty",
          "value": 0
        }
      ],
      "componentId": "05d965fe-3bc7-4bd3-b148-b9bf1d2ae386",
      "name": "意图识别",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a7d87b7cd-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "a7d87b7cd-086805f4-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "content",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "content"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "文本输入(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "a7d87b7cd-83bd50ce-_logic_-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "AI换脸",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "AI换脸"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": "AI换脸"
              }
            }
          },
          {
            "type": "output",
            "id": "a7d87b7cd-6399c70a-_logic_-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 124
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "查询歌曲信息",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "查询歌曲信息"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": "查询歌曲信息"
              }
            }
          },
          {
            "type": "output",
            "id": "a7d87b7cd-d2465518-_logic_-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 154
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "langbase问答",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "langbase问答"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": "langbase问答"
              }
            }
          },
          {
            "type": "output",
            "id": "a7d87b7cd-f84dfaaa-_logic_-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 184
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "none",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "none"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": "未匹配"
              }
            }
          },
          {
            "type": "output",
            "id": "a7d87b7cd-bb4de87c-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 214
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "content",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "content"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "分类(string)"
              }
            }
          }
        ]
      },
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 2,
      "id": "ac0faf3ee",
      "branch": "a7d87b7cd",
      "category": "脚本",
      "code": "TEXT_SHOW",
      "type": "DISPLAY",
      "description": "通过模板格式化文本",
      "x": 250,
      "y": -660,
      "height": 140,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "TEXT_SHOW",
      "icon": "icon-template0",
      "inputs": [
        {
          "name": "input",
          "type": "string",
          "title": "模板输入",
          "id": "52d41eb0"
        }
      ],
      "outputs": [
        {
          "name": "output",
          "type": "string",
          "title": "模板输出",
          "id": "75a64b0f"
        }
      ],
      "configs": [
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "method",
          "value": "POST"
        }
      ],
      "vars": [
        {
          "name": "template",
          "type": "string",
          "title": "模板",
          "value": "请打开网址体验AI换脸：https://mp.music.163.com/magic-face?app_version=8.20.30&userid=3718857520&nm_style=sbt"
        }
      ],
      "componentId": "22835f74-7c5b-46a3-84b2-fa5431919cb2",
      "name": "AI换脸文本",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "ac0faf3ee-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "ac0faf3ee-52d41eb0-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "input",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "input"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "模板输入(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "ac0faf3ee-75a64b0f-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "output",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "output"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "模板输出(string)"
              }
            }
          }
        ]
      },
      "async": false,
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 2,
      "id": "a17de7567",
      "branch": "a7d87b7cd",
      "category": "大模型",
      "code": "PARAMETER_EXTRACT",
      "type": "LLM",
      "description": "参数提取",
      "x": 270,
      "y": -460,
      "height": 230,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "PARAMETER_EXTRACT",
      "icon": "icon-json",
      "inputs": [
        {
          "name": "content",
          "type": "string",
          "title": "文本",
          "id": "51d3f073"
        }
      ],
      "outputs": [
        {
          "name": "jsonString",
          "type": "string",
          "title": "json字符传",
          "id": "7e51b88e"
        },
        {
          "name": "data",
          "type": "object",
          "title": "json数据",
          "id": "4957be8f"
        },
        {
          "name": "success",
          "type": "boolean",
          "title": "是否成功",
          "id": "8112e5e5"
        },
        {
          "name": "errorMsg",
          "type": "string",
          "title": "错误输出",
          "id": "289a57e1"
        }
      ],
      "configs": [
        {
          "name": "asyncApi",
          "type": "boolean",
          "value": "true"
        },
        {
          "name": "parameters",
          "type": "array<object>",
          "value": [
            {
              "name": "name",
              "type": "string",
              "description": "歌曲名"
            },
            {
              "name": "type",
              "type": "string",
              "enum": [
                {
                  "label": "歌词",
                  "value": "lyric"
                },
                {
                  "label": "歌曲信息，详情",
                  "value": "detail"
                }
              ]
            }
          ]
        },
        {
          "name": "url",
          "value": "http://langbase-proxy-dev.yf-onlinetest1-gy1.service.gy.ntes/api/v1/proxy/chat"
        },
        {
          "name": "method",
          "value": "POST"
        },
        {
          "name": "timeout",
          "value": "20000"
        }
      ],
      "vars": [
        {
          "enum": [],
          "name": "modelName",
          "type": "string",
          "title": "模型名",
          "value": "gpt-4o-mini-2024-07-18"
        },
        {
          "name": "system_prompt",
          "type": "string",
          "title": "system prompt",
          "value": "You are a service that translates user requests into JSON objects of type \"ParameterType\" according to the following TypeScript definitions:\n\n```\n// The current system time is 2025-04-14 19:51:55,Monday. If time-related conversions are involved, this time can be used.The following types define the structure of an object of type Parameter\n//  If a parameter does not match successfully, set the value of that parameter to null.\n\ntype ParameterType = {\n\n    // 歌曲名 \n    name: string| null;\n    // undefined lyric:歌词 detail:歌曲信息，详情 \n    type: 'lyric' | 'detail'| null;\n};\n\n```\n\nThe following is the user request translated into a JSON object with 2 spaces of indentation and no properties with the value undefined:\n"
        },
        {
          "name": "providerKind",
          "type": "string",
          "title": "代理商",
          "value": "openai"
        },
        {
          "name": "temperature",
          "rule": "{ \"type\": \"number\", \"max\": 1, \"min\": 0 }",
          "type": "float",
          "title": "随机性",
          "value": 1
        },
        {
          "name": "max_tokens",
          "rule": "{\"type\": \"number\",  \"max\": 4096, \"min\": 100 }",
          "type": "integer",
          "title": "最大token数",
          "value": 512
        },
        {
          "name": "frequency_penalty",
          "rule": "{ \"type\": \"number\", \"max\": 1, \"min\": 0 }",
          "type": "string",
          "title": "frequency_penalty",
          "value": "0"
        },
        {
          "name": "presence_penalty",
          "rule": "{ \"type\": \"number\", \"max\": 1, \"min\": 0 }",
          "type": "float",
          "title": "presence_penalty",
          "value": 0
        },
        {
          "name": "json_object",
          "type": "boolean",
          "title": "json输出",
          "value": true
        }
      ],
      "componentId": "73701f9f-876f-4b09-9f8d-5b2c8de7b680",
      "name": "参数提取",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a17de7567-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "a17de7567-51d3f073-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "content",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "content"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "文本(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "a17de7567-7e51b88e-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "jsonString",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "jsonString"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "json字符传(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "a17de7567-4957be8f-object-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 124
            },
            "tooltip": "123",
            "dataType": "object",
            "keyName": "data",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "object",
              "key": "data"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#57e576",
                "fill": "#fff"
              },
              "text": {
                "text": "json数据(object)"
              }
            }
          },
          {
            "type": "output",
            "id": "a17de7567-8112e5e5-boolean-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 154
            },
            "tooltip": "123",
            "dataType": "boolean",
            "keyName": "success",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "boolean",
              "key": "success"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#024363",
                "fill": "#fff"
              },
              "text": {
                "text": "是否成功(boolean)"
              }
            }
          },
          {
            "type": "output",
            "id": "a17de7567-289a57e1-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 184
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "errorMsg",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "errorMsg"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "错误输出(string)"
              }
            }
          }
        ]
      },
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 1,
      "id": "a3767c808",
      "branch": "a7d87b7cd",
      "category": "脚本",
      "code": "LANGBASE_PROXY_SCRIPT_JS",
      "type": "HTTP",
      "description": "Javascript脚本",
      "x": 960,
      "y": -470,
      "height": 170,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "LANGBASE_PROXY_SCRIPT_JS",
      "icon": "icon-js",
      "inputs": [
        {
          "name": "param1",
          "type": "object",
          "title": "输入参数1",
          "id": "d7bcb7bb"
        }
      ],
      "outputs": [
        {
          "name": "name",
          "type": "string",
          "title": "歌曲名",
          "id": "172c320f"
        },
        {
          "name": "type",
          "type": "string",
          "id": "795acb5f",
          "title": "类型"
        }
      ],
      "configs": [
        {
          "name": "successExp",
          "type": "string",
          "value": "#code==200"
        },
        {
          "name": "url",
          "value": "http://langbase-proxy-online.yf-online-gy1.service.gy.ntes/api/v1/proxy/script/exec"
        },
        {
          "name": "method",
          "value": "POST"
        },
        {
          "name": "timeout",
          "value": "4000"
        },
        {
          "name": "_langbase_id",
          "type": "string",
          "value": "1fc42abf-c358-460c-a8a7-ef0629b53ebc"
        }
      ],
      "vars": [
        {
          "name": "scriptType",
          "type": "string",
          "title": "脚本类型",
          "value": "js"
        },
        {
          "name": "script",
          "type": "string",
          "title": "脚本",
          "value": "// 在这里写代码：该函数的输入和输出都是json dict格式，一般是对参数做加工\n// 注意：\n//  1. !不要! 在控制台有任何输出\n//  2. 不要使用三方包或者运行时间过长的异步方法，会导致运行出错\n//  3. 不能使用模板语法的变量\nfunction userDefinedFunction(params) {\n    const result = {};\n    result[\"name\"] = params['param1'].name;\n    result[\"type\"] = params['param1'].type;\n    return result;\n}\n\n\n// 这里不需要动\n(function main() {\n    // 获取命令行参数\n    const args = process.argv;\n    let inputs, outputs;\n    // 第一个参数是可执行文件的路径\n    // 第二个参数是正在被执行的JavaScript源码文件的路径\n    // 第三个参数是第一个输入参数\n    if (args.length !== 3) {\n        throw new Error('Invalid number of arguments ' + args.length);\n    }\n    inputs = args[2];\n    try {\n        inputs = JSON.parse(inputs);\n        outputs = userDefinedFunction(inputs)\n        console.log(JSON.stringify(outputs))\n    } catch(err) {\n        throw new Error('Invalid inputs not json format ' + inputs);\n    }\n})();"
        }
      ],
      "componentId": "7385bef3-46c9-4fe3-90a7-e1b5b8c8a24c",
      "name": "Javascript1",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a3767c808-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "a3767c808-d7bcb7bb-object-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "object",
            "keyName": "param1",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "object",
              "key": "param1"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#57e576",
                "fill": "#fff"
              },
              "text": {
                "text": "输入参数1(object)"
              }
            }
          },
          {
            "type": "output",
            "id": "a3767c808-172c320f-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "name",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "name"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "歌曲名(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "a3767c808-795acb5f-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 124
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "type",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "type"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "类型(string)"
              }
            }
          }
        ]
      },
      "async": false,
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 1,
      "id": "a12f0f990",
      "branch": "a7d87b7cd-a8dea06fb",
      "category": "请求",
      "code": "UNIVERSAL_HTTP",
      "type": "HTTP",
      "description": "HTTP接口调用",
      "x": 1280,
      "y": -420,
      "height": 170,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "UNIVERSAL_HTTP",
      "icon": "icon-Http",
      "inputs": [
        {
          "name": "name",
          "type": "string",
          "id": "c14da541",
          "title": "歌曲名"
        },
        {
          "name": "type",
          "type": "string",
          "id": "df82cafc",
          "title": "类型"
        }
      ],
      "outputs": [
        {
          "name": "data",
          "type": "string",
          "id": "ebc70a02",
          "title": "歌曲详情"
        }
      ],
      "configs": [
        {
          "name": "successExp",
          "type": "string",
          "value": "#code==200"
        },
        {
          "name": "url",
          "value": "http://adora-proxy-prod-aqz2s3.serverlesssync.yf-online-gy1.service.gy.ntes/api/ada/popo/getSong",
          "type": "string"
        },
        {
          "name": "method",
          "value": "GET",
          "type": "string"
        }
      ],
      "vars": [
        {
          "enum": [
            "GET",
            "POST"
          ],
          "name": "method",
          "type": "string",
          "title": "请求类型",
          "value": "GET"
        },
        {
          "name": "url",
          "type": "string",
          "title": "请求地址",
          "value": "http://adora-proxy-prod-aqz2s3.serverlesssync.yf-online-gy1.service.gy.ntes/api/ada/popo/getSong"
        },
        {
          "name": "headers",
          "type": "object",
          "title": "请求头",
          "value": {
            "Content-Type": "application/json"
          }
        },
        {
          "name": "timeout",
          "type": "integer",
          "title": "超时时间(ms)",
          "value": 5000
        },
        {
          "name": "successExp",
          "type": "string",
          "title": "判断条件(el表达式)",
          "value": "#code==200"
        }
      ],
      "componentId": "b7663eec-6555-428b-9e44-320b642fb776",
      "name": "查询歌曲请求",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a12f0f990-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "a12f0f990-c14da541-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "name",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "name"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "歌曲名(string)"
              }
            }
          },
          {
            "type": "input",
            "id": "a12f0f990-df82cafc-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "type",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "type"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "类型(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "a12f0f990-ebc70a02-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 124
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "data",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "data"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "歌曲详情(string)"
              }
            }
          }
        ]
      },
      "async": false,
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 3,
      "id": "a435adc26",
      "branch": "a7d87b7cd-a8dea06fb",
      "category": "脚本",
      "code": "TEXT_SHOW",
      "type": "DISPLAY",
      "description": "通过模板格式化文本",
      "x": 1020,
      "y": -220,
      "height": 110,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "TEXT_SHOW",
      "icon": "icon-template0",
      "inputs": [],
      "outputs": [
        {
          "name": "output",
          "type": "string",
          "title": "模板输出",
          "id": "0bfe5074"
        }
      ],
      "configs": [
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "method",
          "value": "POST"
        }
      ],
      "vars": [
        {
          "name": "template",
          "type": "string",
          "title": "模板",
          "value": "解析出错了，请稍后再试"
        }
      ],
      "componentId": "22835f74-7c5b-46a3-84b2-fa5431919cb2",
      "name": "出错文本展示",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "a435adc26-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "output",
            "id": "a435adc26-0bfe5074-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "output",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "output"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "模板输出(string)"
              }
            }
          }
        ]
      },
      "async": false,
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": null,
      "id": "aee808a73",
      "branch": "a7d87b7cd",
      "category": "请求",
      "code": "NEW_KNOWLEDGE",
      "type": "HTTP",
      "description": "知识库",
      "x": 350,
      "y": 20,
      "height": 200,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "NEW_KNOWLEDGE",
      "icon": "icon-knowledge",
      "inputs": [
        {
          "name": "input",
          "type": "string",
          "title": "关键词",
          "id": "3d7b7048"
        },
        {
          "name": "knowledgeItems",
          "type": "array<object>",
          "title": "文档列表",
          "id": "b6d7e3b9"
        }
      ],
      "outputs": [
        {
          "name": "knowledgeList",
          "type": "array<string>",
          "title": "文本数组",
          "id": "0470f09c"
        },
        {
          "name": "knowledgeStrList",
          "type": "string",
          "title": "文本",
          "id": "1842b91f"
        }
      ],
      "configs": [
        {
          "name": "url",
          "value": "http://music-langbase-service-pre-guiyang1.yf-online-gy1.service.gy.ntes/api/langbase/proxy/knowledge"
        },
        {
          "name": "method",
          "value": "POST"
        },
        {
          "name": "docIds",
          "value": []
        }
      ],
      "vars": [
        {
          "name": "knowledgeId",
          "type": "string",
          "title": "知识库ID",
          "value": 1
        },
        {
          "name": "limit",
          "type": "integer",
          "title": "分片数量",
          "value": 5
        },
        {
          "name": "useAll",
          "type": "boolean",
          "title": "是否使用全部文档",
          "value": true
        },
        {
          "name": "similarity",
          "type": "float",
          "description": "",
          "title": "相似度",
          "value": 0.3
        },
        {
          "name": "searchType",
          "type": "string",
          "title": "检索方式",
          "value": "SEMANTIC"
        }
      ],
      "componentId": "7313f2c0-7aae-45af-ad65-0d5733e75e90",
      "name": "知识库（新）",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "aee808a73-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "aee808a73-3d7b7048-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "input",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "input"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "关键词(string)"
              }
            }
          },
          {
            "type": "input",
            "id": "aee808a73-b6d7e3b9-array<object>-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 94
            },
            "tooltip": "123",
            "dataType": "array<object>",
            "keyName": "knowledgeItems",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "array<object>",
              "key": "knowledgeItems"
            },
            "attrs": {
              "rect": {
                "magnet": true,
                "stroke": "#e228c7",
                "fill": "#fff",
                "strokeWidth": 2,
                "width": 16,
                "height": 16,
                "x": -8,
                "y": -8
              },
              "dot": {
                "magnet": true,
                "stroke": "#57e576",
                "fill": "#fff",
                "strokeWidth": 2,
                "r": 3
              },
              "text": {
                "text": "文档列表(array<object>)"
              }
            }
          },
          {
            "type": "output",
            "id": "aee808a73-0470f09c-array<string>-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 124
            },
            "tooltip": "123",
            "dataType": "array<string>",
            "keyName": "knowledgeList",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "array<string>",
              "key": "knowledgeList"
            },
            "attrs": {
              "rect": {
                "magnet": true,
                "stroke": "#e228c7",
                "fill": "#fff",
                "strokeWidth": 2,
                "width": 16,
                "height": 16,
                "x": -8,
                "y": -8
              },
              "dot": {
                "magnet": true,
                "stroke": "#0929f9",
                "fill": "#fff",
                "strokeWidth": 2,
                "r": 3
              },
              "text": {
                "text": "文本数组(array<string>)"
              }
            }
          },
          {
            "type": "output",
            "id": "aee808a73-1842b91f-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 154
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "knowledgeStrList",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "knowledgeStrList"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "文本(string)"
              }
            }
          }
        ]
      },
      "async": false,
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 4,
      "id": "ae4ae5151",
      "branch": "a7d87b7cd",
      "category": "脚本",
      "code": "TEXT_SHOW",
      "type": "DISPLAY",
      "description": "通过模板格式化文本",
      "x": 690,
      "y": 50,
      "height": 140,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "TEXT_SHOW",
      "icon": "icon-template0",
      "inputs": [
        {
          "name": "docs",
          "type": "string",
          "title": "模板输入",
          "id": "bf720fd1"
        }
      ],
      "outputs": [
        {
          "name": "output",
          "type": "string",
          "title": "模板输出",
          "id": "7ff05652"
        }
      ],
      "configs": [
        {
          "name": "url",
          "value": "1"
        },
        {
          "name": "method",
          "value": "POST"
        }
      ],
      "vars": [
        {
          "name": "template",
          "type": "string",
          "title": "模板",
          "value": "你是一个智能助理，你需要根据用户的查询内容友好地回复。使用 <Data></Data> 标记中的内容作为你的知识:\n  <Data>\n  {docs}\n  </Data>\n  回答要求：\n  - 如果你不清楚答案，你需要澄清。\n  - 避免提及你是从 <Data></Data> 获取的知识。\n  - 保持答案与 <Data></Data> 中描述的一致。\n  - 使用 Markdown 语法优化回答格式。\n  - 使用与问题相同的语言回答。"
        }
      ],
      "componentId": "22835f74-7c5b-46a3-84b2-fa5431919cb2",
      "name": "RAG提示词组装",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "ae4ae5151-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "ae4ae5151-bf720fd1-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "docs",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "docs"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "模板输入(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "ae4ae5151-7ff05652-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "output",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "output"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "模板输出(string)"
              }
            }
          }
        ]
      },
      "async": false,
      "groupChildren": null,
      "groupCollapsedSize": null
    },
    {
      "no": 1,
      "id": "ae8dad6c9",
      "branch": "a7d87b7cd",
      "category": "大模型",
      "code": "LLM",
      "type": "HTTP",
      "description": "Langbase chat",
      "x": 1030,
      "y": -30,
      "height": 170,
      "width": 240,
      "renderKey": "ATOMIC_NODE",
      "renderCode": "LLM",
      "icon": "icon-chat",
      "inputs": [
        {
          "name": "content",
          "type": "string",
          "title": "用户输入",
          "id": "5b57ad0f"
        },
        {
          "name": "system_prompt",
          "type": "string",
          "value": "",
          "id": "72ce25a2"
        }
      ],
      "outputs": [
        {
          "name": "content",
          "type": "string",
          "title": "GPT输出",
          "id": "fe2e79e1"
        }
      ],
      "configs": [
        {
          "name": "asyncApi",
          "type": "boolean",
          "value": "true"
        },
        {
          "name": "url",
          "value": "http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes/langbase/proxy/chat"
        },
        {
          "name": "method",
          "value": "POST"
        },
        {
          "name": "timeout",
          "value": "20000"
        },
        {
          "name": "_langbase_id",
          "type": "string",
          "value": "1fc42abf-c358-460c-a8a7-ef0629b53ebc"
        }
      ],
      "vars": [
        {
          "name": "frequency_penalty",
          "type": "float",
          "value": 0
        },
        {
          "name": "max_tokens",
          "type": "integer",
          "value": 512
        },
        {
          "name": "modelName",
          "type": "string",
          "value": "deepseek-v3-latest"
        },
        {
          "name": "presence_penalty",
          "type": "float",
          "value": 0
        },
        {
          "name": "providerKind",
          "type": "string",
          "value": "openai"
        },
        {
          "name": "temperature",
          "type": "float",
          "value": 1
        },
        {
          "name": "timeout",
          "type": "integer",
          "value": 20000
        },
        {
          "name": "top_p",
          "type": "float",
          "value": 0.85
        }
      ],
      "componentId": "1306b9ea-3a71-4739-b938-b4e120f4cd52",
      "name": "大模型问答",
      "ports": {
        "groups": {
          "group1": {
            "position": {
              "name": "absolute"
            }
          }
        },
        "items": [
          {
            "type": "input",
            "id": "ae8dad6c9-abcdef-_logic_-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 25
            },
            "tooltip": "123",
            "dataType": "_logic_",
            "keyName": "逻辑连接点",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "_logic_",
              "key": "逻辑连接点"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#bbb",
                "fill": "#fff"
              },
              "text": {
                "text": ""
              }
            }
          },
          {
            "type": "input",
            "id": "ae8dad6c9-5b57ad0f-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 64
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "content",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "content"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "用户输入(string)"
              }
            }
          },
          {
            "type": "input",
            "id": "ae8dad6c9-72ce25a2-string-input",
            "connected": false,
            "group": "group1",
            "args": {
              "x": 0,
              "y": 94
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "system_prompt",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "system_prompt"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "system_prompt(string)"
              }
            }
          },
          {
            "type": "output",
            "id": "ae8dad6c9-fe2e79e1-string-output",
            "connected": true,
            "group": "group1",
            "args": {
              "x": "100%",
              "y": 124
            },
            "tooltip": "123",
            "dataType": "string",
            "keyName": "content",
            "label": {
              "position": "inside"
            },
            "markup": [
              {
                "tagName": "rect",
                "selector": "rect",
                "className": "xflow-port"
              },
              {
                "tagName": "circle",
                "selector": "dot"
              },
              {
                "tagName": "circle",
                "selector": "circle",
                "className": "xflow-port"
              }
            ],
            "data": {
              "type": "string",
              "key": "content"
            },
            "attrs": {
              "circle": {
                "r": 6,
                "magnet": true,
                "strokeWidth": 2,
                "stroke": "#0929f9",
                "fill": "#fff"
              },
              "text": {
                "text": "GPT输出(string)"
              }
            }
          }
        ]
      },
      "async": true,
      "groupChildren": null,
      "groupCollapsedSize": null
    }
  ],
  "version": "1.0",
  "workflowEngineId": "agent-workflow",
  "workflowEngineName": "agent-workflow",
  "workflowId": "agent-workflow",
  "configId": "b0eebd45-b6a0-4d24-b9e9-1665b1859f58",
  "greeting": "Hi，我是 LangBot 智能助手，你可以尝试问我以下问题：\n1. 查询歌曲信息，例如：**查询 温柔 的歌曲详情 / 歌词**\n2. **AI换脸**\n3. 问我LangBase相关信息，例如：**LangBase是什么**",
  "businessConfig": {}
}