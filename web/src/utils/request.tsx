import { message } from 'antd';
import axios, { AxiosPromise, AxiosRequestConfig } from 'axios';

import { mock } from './axiosWithMock';
import { LOGIN_NE } from '@/components/login';

// useMock(axios);

interface RequestOptions extends AxiosRequestConfig {
  needAuth?: boolean; // 请求是否需要登录，默认 true
  silence?: boolean; // 报错时，是否静默1
  useMock?: boolean; // 是否使用mock
  cache?: any // 是否使用缓存 
  proxyInfo?: any;
}

const ApiDomain = {
  dev: '//langbase.yf-dev2.netease.com/api/v1',
  test: '/api/v1', // TODO
  prod: '/api/v1', // TODO
};


export function getApiBaseUrl() {
  if (/langbase\.yf-dev2\.netease\.com/.test(window.location.host)) {
    return ApiDomain.dev;
  }
  // // TODO
  // if (/langbase\.yf-dev2\.netease\.com/.test(window.location.host)) {
  //   return ApiDomain.test;
  // }
  return ApiDomain.prod;
}

// 是否已登录
let auth = false;
const authCallback: (() => void)[] = [];

export function setAuth() {
  auth = true;
  authCallback.forEach((onAuth) => {
    onAuth();
  });
  authCallback.length = 0;
}

// 悬停请求，等待登录后，继续发起请求。
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function waitForAuth(options: RequestOptions): AxiosPromise<any> {
  return new Promise((resolve, reject) => {
    authCallback.push(() => {
      request(options).then(resolve).catch(reject);
    });
  });
}

// 实现一个简单的cache
const cache = {

}

export function request(options: RequestOptions) {
  const needAuth = 'needAuth' in options ? options.needAuth : true;
  if (needAuth && !auth) {
    return waitForAuth(options);
  }


  let baseURL = '';
  if (!options.baseURL) {
    baseURL = getApiBaseUrl();
  }
  // @ts-ignore
  if (options.useMock && process.env.NODE_ENV === 'development') {
    baseURL = baseURL.replace('/v1', '/mock');
  }

  let Cookie = '';

 
  const headers: {
    [key: string]: string;
  } = {
  };

  // if (options.url.includes('langbase')) {
  //   headers.Cookie = '_ntes_nnid=40acad86fe1390b1652c0ce1c57c7a7d,1722245019082; _ntes_nuid=40acad86fe1390b1652c0ce1c57c7a7d; ehrIsFirstIn=true; _ga_XVRD1WG0L5=GS1.2.1723014456.1.1.1723014579.0.0.0; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22190fd51c02bd4b-0696ca697d81b88-19525637-2007040-190fd51c02c15d3%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkwZmQ1MWMwMmJkNGItMDY5NmNhNjk3ZDgxYjg4LTE5NTI1NjM3LTIwMDcwNDAtMTkwZmQ1MWMwMmMxNWQzIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22190fd51c02bd4b-0696ca697d81b88-19525637-2007040-190fd51c02c15d3%22%7D; hb_MA-84C2-1746F3C61AD5_source=login.netease.com; _ga_1Z8HY72B46=GS1.1.1728912973.2.1.1728912979.0.0.0; _ga_JMGG7GD8B2=GS1.1.1729586312.33.0.1729586312.0.0.0; _ga_GFD4D2ZR03=GS1.1.1730183540.121.1.1730183540.0.0.0; hb_MA-94EC-46E866C618E4_source=docs.popo.netease.com; hb_MA-809E-C9B7A6CD76FA_source=login.netease.com; hb_MA-BC43-D95DB2189621_source=login.netease.com; hb_MA-8A97-C715F8E34BE7_source=login.netease.com; _ga=GA1.2.301167644.1721987218; mp_versions_hubble_jsSDK=DATracker.globals.1.6.14; hb_MA-A4D4-E99480416052_source=login.netease.com; hb_MA-9C9D-C9379907646C_source=login.netease.com; hb_MA-8391-8FFD554DBEE5_source=danqing.163.com; visited_surveyid176114=173339262579640076; authOpenIdToken=auth:openId:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJFSFJfSVNTVUVSIiwiZXhwIjoxNzMzODc4NjY3LCJpYXQiOjE3MzM3OTIyNjcsInVzZXJuYW1lIjoiSDI1NzQ1In0.BPgVJ9E4DAgC32V-ti30nJwsUwErFO9JKZpEb7rsu7o; authOpenIdTokenExpireAt=1733878667000; langbase|session=IjQ3NzNiNTVkZmMwYzQwZDA5MGYwNGMwNjUwMWI3MmQyIg.Z1fn0w.KKgkUkk_1UvrCINDK16LtEaBa58; op_state_id_1.0=cuqrzn77x4; op_session_id_1.0=9450B7039F4055B18195A08563DFB4792F97A20D4C2DD23A0A9FB0CE9FF1F04BD391AB0AF6BA11AFCC68FF7ABC9DDB0F3FDFB9D3AECE6F7B49B2FC24EEF696F8B263724C28AF96801703859FAF61807368FA7A7C1469B1CF1035774EBBE4F65C7C3406BBAA343968AD503F1B7C411EC8C8B4B949BE67445281FA626DEF6C38EDF5B5765ACFA5D9C1BA274034DEFAC86C406C8BAAD5EA4FB50B422B2D542533E35C1A7FC9EB82CAA39D9419EDA10DA1BA';
  // }

  if (options.proxyInfo) {
    if (options.method === 'POST') {
      // options.data = { proxyInfo: options.proxyInfo, data: options.data }
      //
      if (Object.keys(options.proxyInfo).length > 1) {
        options.data = { proxyInfo: options.proxyInfo, ...options.data }
      } 
    }
    if (options.method === 'GET' || !options.method) {
      options.params = { ...options.params }
    }
    options.url = `/proxy${options.proxyInfo.url}`
  }

  const mergeOpt = {
    ...options,
    baseURL: options.baseURL || baseURL,
    method: options.method || 'GET',
    headers: {
      ...options.headers,
      ...headers,
    },
  };

  if (options.cache) {
    if (cache[JSON.stringify(mergeOpt)]) {
      console.log("[hit cache]", options, mergeOpt, cache[JSON.stringify(mergeOpt)]);
      return Promise.resolve(cache[JSON.stringify(mergeOpt)]);
    }
  }

  // @ts-ignore
  if (mock[mergeOpt.url] && mock[mergeOpt.url].method === mergeOpt.method) {
    // @ts-ignore
    return mock[mergeOpt.url].response();
  }

  return axios(mergeOpt)
    .then((res) => {
      const { data, status } = res;
      if (status === 204) {
        return true;
      }
      // http 状态码
      if (status >= 200 && status < 300) {
        if (data && data.data) {
          return data.data;
        }
        if (data) {
          return data;
        }
        if (!options.silence) {
          message.error(data.message);
        }
      } else if (!options.silence) {
        message.error(status);
      }
      // 说明需要登录
      if (status === 401) {
        window.location.href = LOGIN_NE;
      }
      throw new Error(res?.data.message || status);
    })
    .then(res => {
      if (options.cache) {
        if (typeof options.cache === 'function' && options.cache(res)) {
          cache[JSON.stringify(mergeOpt)] = res;
        }
        if (options.cache === true) {
          cache[JSON.stringify(mergeOpt)] = res;
        }
      }
      return res;
    })
    .catch((err) => {
      window.corona.error(`request Error`, err);
      // 说明需要登录
      if (err.response?.status === 401) {
        window.location.href = LOGIN_NE;
      }
      const msg =
        err.response?.data?.info || err.response?.data?.message || err.toString();
      if (!options.silence) {
        message.error(msg);
      }
      throw err;
    });
}
