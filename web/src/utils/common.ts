import { message } from 'antd';
import qs from 'querystring';

import { CioApi } from '@/api/cio';

import { NODE_CODE, NODE_TYPE } from './constant';
import { getRealType } from './node';
import { nos2url } from '@/pages/preview/type-map';
import eventBus, { IEventType } from './event-bus';
import axios from 'axios';
import { getAppId } from './state';

let envMap = {
  'langbase.netease.com': 'online',
  'langbase-pre.netease.com': 'pre',
  'langbase-workflow.yf-onlinetest4.netease.com': 'test'
}

export let defaultModelName = 'deepseek-r1-latest';
export let defaultProvider = 'deepseek';

export const getDefaultModel = () => {
  return {
    modelName: defaultModelName,
    providerKind: defaultProvider,
    alias: defaultModelName
  }
}

export const setDefaultModel = (modelName: string, provider: string) => {
  console.log("[set-default-model]", modelName, provider);
  defaultModelName = modelName || defaultModelName;
  defaultProvider = provider || defaultProvider;
}



const isDev = process.env.NODE_ENV === 'development';
const devEnv = process.env.DEV_ENV;

export const env = isDev ? devEnv : envMap[window.location.hostname];

console.log("[env]...", env, isDev, devEnv);

export const mergeArr = (arr1, arr2) => {
  arr2.forEach(v2 => {
    const find = arr1.find(v1 => v1.name === v2.name);
    // 没有找到的，需要加入到arr1中
    if (!find) {
      arr1.push(v2)
    } else {
      find.value = v2.value;
    }
  })
}

export const obj2Arr = (obj, keyName = 'name', valueName = 'value') => {
  return Object.keys(obj).map((key) => ({
    [keyName]: key,
    [valueName]: obj[key],
  }));
};

export const arr2Obj = (arr, keyName = 'name', valueName = 'value') => {
  const obj = {};
  arr.map((v) => {
    obj[v[keyName]] = v[valueName];
  });
  return obj;
};

export const copyText = (text) => {
  const textarea = document.createElement('textarea');
  textarea.value = text;
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand('copy');
  document.body.removeChild(textarea);
  message.success('复制成功');
};

export const JSONParse = (v) => {
  let obj = null;
  try {
    obj = JSON.parse(v);
  } catch (err) {
    window.corona.info(`JSON.parse失败`, { err, text: v })
    console.log('===err===', err);
  }
  return obj;
};

export function copyToClipboard(text: string) {
  const input = document.createElement('textarea');
  input.value = text;
  document.body.appendChild(input);
  input.select();
  document.execCommand('copy');
  document.body.removeChild(input);
}

const tokenKey = 'langbase|token';

export function saveToken(appID: string, token: string) {
  localStorage.setItem(`${tokenKey}-${appID}`, token);
}

export function getToken(appID: string) {
  return localStorage.getItem(`${tokenKey}-${appID}`);
}

export const localPrefix = 'langbase'
/**
 * 
 * @param appID 
 */
export function saveLocalData(key: string, data: any) {
  console.log("[save-local]", key);
  eventBus.emit(IEventType.STORAGE_CHANGE, { key, value: data });
  return localStorage.setItem(`${localPrefix}-${key}`, JSON.stringify(data));
}

export function getLocalData(key: string) {
  const dataText = localStorage.getItem(`${localPrefix}-${key}`);
  console.log("[get-local]", key);
  return dataText ? JSON.parse(dataText) : null;
}

export function getLocalAppData(key: string) {
  const appId = getAppId();
  return getLocalData(`${appId}-${key}`);
}

export function saveLocalAppData(key: string, data: any) {
  const appId = getAppId();
  return saveLocalData(`${appId}-${key}`, data);
}

/**
 * 格式化类型
 * @param val
 * @param type
 * @returns
 */
export const formatTypeValue = (val, _type?) => {
  const [type, innerType] = getRealType(_type);
  const map = {
    float: (v) => Number(v),
    integer: (v) => Number(v),
    datetime: (v) => Number(v),
    object: (origin) => typeof origin === 'string' ? JSONParse(origin) : origin,
    boolean: (v) => v === 'true' || v === true || v === 'TRUE',
  };
  if (type === 'array') {
    if (typeof val === 'string') {
      const arr = JSONParse(val) || val.split(',');
      if (Array.isArray(arr)) {
        return arr.map((v) => formatTypeValue(v, innerType));
      }
    }
    return val;
  }
  if (map[type]) {
    return map[type](val);
  }
  return val;
};

// 通用的资源格式化方法
const formatResourceValue = (type: string) => (origin: string | any) => {
  if (typeof origin !== 'string') {
    return origin;
  }
  if (type === 'url') {
    return origin && { status: 'done', url: origin };
  }
  return origin && ({ status: 'done', key: origin, url: nos2url(origin) }) 
};

/**
 * 格式化输入组件的输入
 */

const formatRes = type => (origin) => {
  if (typeof origin === 'string') {
    return origin;
  }
  if (origin?.status === 'done') {
    return origin[type];
  }
  return undefined;
}

const formatInputMap = {
  Url: formatRes('url'),
  Image: formatRes('url'),
  Audio: formatRes('url'),
  Video: formatRes('url'),
  NosKey: formatRes('key'),
  VideoNosKey: formatRes('key'),
  ImageNosKey: formatRes('key'),
  AudioNosKey: formatRes('key'),
  object: (origin) => typeof origin === 'string' ? JSONParse(origin) : origin,
  array: (origin, type?) => {
    if (typeof origin === 'string') {
      const arr = origin.split('\n');
      const [, innerType] = getRealType(type);
      return arr.map((v) => formatTypeValue(v, innerType));
    }
    return origin;
  },
  [NODE_CODE.INPUT_CIO]: async (origin, type?, props?) => {
    // 如果没有触发
    if (props.debug) {
      const res = await CioApi.getCIOResource(props.value, props);
      return res;
    }
    return '';
  },
};

/**
 * 格式化输入组件的输入
 */
export const joinInputMap = {
  Url: formatResourceValue('url'),
  Image: formatResourceValue('url'),
  Audio: formatResourceValue('url'),
  Video: formatResourceValue('url'),
  NosKey: formatResourceValue('nos'),
  VideoNosKey: formatResourceValue('nos'),
  ImageNosKey: formatResourceValue('nos'),
  AudioNosKey: formatResourceValue('nos'),
};

/**
 * 格式化输入的值
 * @param props
 */
export const formatInputValue = async (props) => {
  const { type } = props;
  const [dataType] = getRealType(type);
  if (formatInputMap[dataType]) {
    props.value = await formatInputMap[dataType](props.value, type, props);
  }
  if (formatInputMap[props?.code]) {
    props.value = await formatInputMap[props.code](props.value, type, props);
  }
  return props;
};



/**
 * 有些特殊的情况需要进行格式化操作
 */
export const formatNodeValue = async (props) => {
  props.value = props.originValue === undefined ? props.value : props.originValue;
  if (props.type === NODE_TYPE.INPUT) {
    const type = props.outputs[0]?.type || 'string';
    const res = await formatInputValue({ ...props, type });
    props.value = res.value;
  }
  // 如果节点有vars，也需要格式化
  if (props.vars && props.vars.length > 0) {
    const vars = props.vars.map((v) => ({
      ...v,
      value: formatTypeValue(v.value, v.type)
    }));
    props.vars = vars;
  }
  return props;
};

/**
 * 上传文件到服务器
 * @param file 要上传的文件
 * @param appId 应用ID
 * @param options 上传选项
 * @returns 上传结果
 */
export const uploadFile = async (file: File, appId: string, options?: {
  permanent?: boolean,
  acceptedTypes?: string[],
  maxSize?: number, // 单位MB
  maxTokens?: number,
  onProgress?: (percent: number) => void
}, showMessage = true) => {
  const {
    permanent = false,
    acceptedTypes = [
      'text/plain',                                // .txt
      'text/markdown',                             // .md
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword',                        // .doc
      'application/pdf'                            // .pdf
    ],
    maxSize = 10, // 默认10MB
    maxTokens,
    onProgress
  } = options || {};
  let loadingMessage = null;
  // 检查文件类型
  if (acceptedTypes.length > 0 && !acceptedTypes.includes(file.type)) {
    const extensions = acceptedTypes.map(type => {
      const map = {
        'text/plain': '.txt',
        'text/markdown': '.md',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
        'application/msword': '.doc',
        'application/pdf': '.pdf'
      };
      return map[type] || type;
    }).join(', ');
    
    if (showMessage) {
      message.error(`只支持上传 ${extensions} 格式的文件!`);
    }
    return { success: false, message: '文件类型不支持' };
  }
  
  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < maxSize;
  if (!isLtMaxSize) {
    if (showMessage) {
      message.error(`文件大小不能超过${maxSize}MB!`);
    }
    return { success: false, message: '文件过大' };
  }
  
  // 创建FormData对象
  const formData = new FormData();
  formData.append('file', file);
  
  // 构建上传API URL
  const actionUrl = `/api/v1/app/${appId}/uploadfile?${qs.stringify({
    ...(maxTokens ? { max_tokens: maxTokens * 1000 } : {}),
    ...(permanent ? { permanent: true } : {}),
  })}`;
  
  
  try {
    // 显示上传中提示
    // 关闭上传中提示
    if (showMessage) {
      loadingMessage = message.loading(`正在上传 ${file.name}...`, 0);
    }
    
    // 发送上传请求
    const response = await fetch(actionUrl, {
      method: 'POST',
      body: formData,
    });
    
    // 关闭上传中提示
    if (showMessage) {
      loadingMessage();
    }

    const result = await response.json();
    console.log('result1', result);
    
    if (!response.ok) {
      if (response.status === 413) {
        throw new Error(result?.message || '纯文本内容超过模型上下文限制，请重新选择文件');
      } else {
        throw new Error('上传失败');
      }
    }
    
    
    if (result.data) {
      // 上传成功
      const fileData = {
        key: result.data.key,
        url: result.data.uri,
        name: file.name,
        type: file.type,
        size: file.size,
        status: 'done'
      };
      
      // message.success(`${file.name} 上传成功`);
      return { success: true, data: fileData };
    } else {
      throw new Error(result.message || '上传失败');
    }
  } catch (error) {
    console.log('error', error);
    if (showMessage) {
      message.error(`${file.name} 上传失败: ${(error as any)?.message}`);
    }
    return { success: false, message: (error as any)?.message };
  }
};

export const getFileData = async (key) => {
  const response = await axios.get('/api/v1/get-cache-file', {
    params: { key }
  });
  return response?.data?.data || '';
}

export const getPopoFileData = async (popo_url) => {
  const response = await axios.get('/api/v1/document/parse-popo', {
    params: { popo_url }
  });
  console.log('response', response);
  return response?.data?.data?.text_content || '';
}



export const getSize = (file) => {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject(new Error("File not found"))
      return;
    }
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (e) => {
      const img = new Image();
      img.src = e.target.result as string;
      img.onload = () => {
        const { width, height } = img;
        console.log('Image width:', width);
        console.log('Image height:', height);
        resolve({
          width,
          height
        })
      };
      img.onerror = () => {
        message.error('图片加载失败');
        reject('图片加载失败');
      };
    };
    reader.onerror = () => {
      message.error('文件读取失败');
      reject('文件读取失败');
    };
  });
}


export const formatNumber = (_num: number | string) => {
  let num = Number(_num);
  if (num >= 100000000) {
    return (num / 100000000).toFixed(2) + '亿';    // 显示如：1.23亿
  }
  if (num >= 10000) {
    return (num / 10000).toFixed(2) + '万';        // 显示如：123.45万
  }
  return num.toFixed(0);                           // 小于1000的数字直接显示
};

export const getModelMap = () => {
  const { modelList } = (window as any)?._GLOBAL_STATE || {};
  return modelList.reduce((acc, item) => {
    acc[item.name] = item;
    return acc;
  }, {});
}