// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import qs from 'querystring';
import { AppApi } from '@/api/app';
import dayjs from 'dayjs';
import { ComponentApi } from '@/api/component';
import { NODE_CODE } from './constant';
import { getGraphConfigFromLocal } from '@/pages/app/workflow/config/cmds/hooks-services';
import eventBus, { IEventType } from './event-bus';
import { pick } from 'lodash';

export const getGlobalState = (key: string) => {
  return (window as any)._GLOBAL_STATE[key];
};

export const getAppState = (key: string) => {
  if (getGlobalState('app')) {
    return getGlobalState('app')[key];
  }
  return undefined;
};

export const getWorkspaceState = (key: string) => {
  if (getGlobalState('workspace')) {
    return getGlobalState('workspace')[key];
  }
  return undefined;
};

export const getGroupState = (key: string) => {
  if (getGlobalState('group')) {
    return getGlobalState('group')[key];
  }
  return undefined;
};

export const getUserState = (key: string) => {
  if (getGlobalState('user')) {
    return getGlobalState('user')[key];
  }
  return undefined;
};

export const getAppId = () => {
  return getAppState('id') || qs.parse(window.location.search.replace('?', ''))['appId'];
};

export const getGroupId = () => {
  return (
    getGroupState('id') || qs.parse(window.location.search.replace('?', ''))['groupId']
  );
};

export const getWorkspaceId = () => {
  return (
    getWorkspaceState('id') ||
    qs.parse(window.location.search.replace('?', ''))['workspaceId']
  );
};

const alertWhiteList = [NODE_CODE.LLM, NODE_CODE.CIO, NODE_CODE.PYTHON, NODE_CODE.JS, NODE_CODE.EXTRACT, NODE_CODE.AIR_SHIP, NODE_CODE.MOCK, NODE_CODE.UNI_HTTP, NODE_CODE.SUB_WORKFLOW, NODE_CODE.SKY_EYE, NODE_CODE.KNOWLEDGE, NODE_CODE.SUB_CHATFLOW, NODE_CODE.SUB_COMPLETION];
const llmNodes = [NODE_CODE.MOONSHOOT, NODE_CODE.LLM, NODE_CODE.MOONSHOOT, NODE_CODE.KNOWLEDGE, NODE_CODE.GPT, NODE_CODE.GPT_VERSION, NODE_CODE.DALLE, NODE_CODE.JS, NODE_CODE.PYTHON, NODE_CODE.LLM_TEST];

// 保存 config 的全局变量
class GraphConfig {
  constructor() {
    this.$config = {};
    this.$controlMap = {};
    this.appId = '';
    this.originData = null;
  }

  setConfig(q) {
    this.$config = { ...this.$config, ...q };
  }

  setControlMap(q) {
    this.$controlMap = q;
  }

  setOriginData(q) {
    this.originData = q;
  }

  getOriginData() {
    return this.originData;
  }

  get config() {
    return this.$config;
  }

  get controlMap() {
    return this.$controlMap;
  }

  // 获取当前workflow的config
  async getConfig(defaultVal?: any, force?: boolean) {
    const appId = getAppId();
    if (appId !== this.appId || force) {
      this.$config = {};
      this.appId = appId;
    }
    if (this.$config.version) {
      return this.$config;
    }
    const components = await ComponentApi.list();
    const customComponents = await ComponentApi.listCustom();
    console.log('customComponents', customComponents);
    // 初始化的snapshot
    const snapshot = await AppApi.getAppConfigSnapshot('');
    const localConfig = getGraphConfigFromLocal();
    if (Array.isArray(snapshot) && snapshot.length) {
      this.$config = { ...snapshot[0].config };
      if (!snapshot[0].config?.nodes) {
        this.setConfig(defaultVal || {});
      } else {
        // 把当前信息发送出去，便于Header进行展示
        eventBus.emit(IEventType.STORAGE_CHANGE, { key: 'preConfig', value: snapshot[0] });
        // 先取本地保存的数据
        const useLocal = (localConfig?.time || 0) > (dayjs(snapshot[0]?.createdAt).valueOf());
        this.setConfig(useLocal ? localConfig.config : snapshot[0].config);
      }
      const nodes = this.$config.nodes;
      const edges = this.$config.edges;
      // 预处理
      this.$config.edges = edges.filter(edge => edge.source && edge.target);
      // 把所有的components的Id替换掉
      this.$config.nodes = nodes.map(node => {
        // 根据code找到对应的的组件
        const find = components.find(c => c.code === node.code);
        const newNode = findChanges(node, find);
        // 如果找到了，需要更新 componentId
        if (find) {
          newNode.componentId = find.id;
          // 需要更新请求的url
        }
        return newNode;
      })
      // 获取config后，需要跟当前的components做比对，替换对应的componentId
      // console.log('config12321', this.$config.nodes, newNodes, components);
      return this.$config;
    }
    this.setConfig(defaultVal || {});
    return defaultVal || null;
  }
}

export const $ = new GraphConfig();

const hostChange = {
  'langbase-proxy.yf-onlinetest4.service.163.org': 'langbase-proxy-test.yf-onlinetest1-gy1.service.gy.ntes',
  'langbase-proxy.yf-dev2.service.163.org': 'langbase-proxy-dev.yf-onlinetest1-gy1.service.gy.ntes',
  'langbase-proxy.yf-online.service.163.org': 'langbase-proxy-online.yf-online-gy1.service.gy.ntes'
}

// const hostChange = {
//   'langbase-proxy-test.yf-onlinetest1-gy1.service.gy.ntes':'langbase-proxy.yf-onlinetest4.service.163.org',
//   'langbase-proxy-dev.yf-onlinetest1-gy1.service.gy.ntes': 'langbase-proxy.yf-dev2.service.163.org'
// }

/**
 * 导入组件/组件更新后，查看有哪些变动
 */
function findChanges(node, find) {
  if (!find) {
    return node;
  }
  const newNode = { ...node };
  /**
   * 如果组件有更新
   * 
   *  需要手动确认：
   * - inputs 变动（数量变化，名称变化）
   * - vars 变动（数量，名称变化）
   * 
   *  可以直接修改
   * - configs 变动
   */
  const changes = {
    inputs: [],
    outputs: [],
    vars: [],
    configs: []
  };
  const newData = {}

  let needAlert = false;

  const needChangeConfigUrl = [NODE_CODE.PYTHON, NODE_CODE.JS, NODE_CODE.KNOWLEDGE, NODE_CODE.LLM];

  // 如果是config变化,则直接替换
  if (needChangeConfigUrl.includes(node.code)) {
    const findUrl = node.configs.find(v => v.name === 'url');
    const targetUrl = find.config.configs.find(v => v.name === 'url');
    if (findUrl && targetUrl) {
      findUrl.value = targetUrl.value;
    }
  }

  if (node.type === 'HTTP' && !alertWhiteList.includes(node.code)) {
    diff(node, find.config, 'inputs', changes, newData);
    diff(node, find.config, 'outputs', changes, newData);
    diff(node, find.config, 'configs', changes, newData);
    // 由于输入和vars可以相互转换，因此需要把转换的找到
    const newInputs = changes.inputs.filter(input => {
      const find = changes.vars.find(v => v.name === input.name && !v.type.includes('update'));
      return !find;
    });

    const newVars = changes.vars.filter(v => {
      const find = changes.inputs.find(input => v.name === input.name && !input.type.includes('update'));
      return !find;
    });

    changes.vars = newVars;
    changes.inputs = newInputs;

    console.log('changess', node.name, changes, node, find.config);
    // 如果configs有变动，则直接替换（因为configs用户无法编辑）
    // if (changes.configs.length) {
    newNode.configs = find?.config.configs
    // 如果inputs和outputs的变动，需要反应在页面让，让用户确定是否替换
    if (changes.inputs.length || changes.outputs.length || changes.vars.length) {
      needAlert = true;
    }
    newNode.changes = changes;
    newNode.needAlert = needAlert;
    if (needAlert) {

      console.log("newData", newData);
      newNode.updateData = newData;
    }
  }

  addLLmConfig(newNode);
  // 进行proxy的地址切换
  const findUrl = (newNode?.configs || []).find(v => v.name === 'url');
  Object.keys(hostChange).forEach(key => {
    if (findUrl && findUrl.value.includes(key)) {
      console.log('find', findUrl);
      findUrl.value = findUrl.value.replace(key, hostChange[key]);
      console.log('find', findUrl.value);
    }
  })

  return newNode;
}

export function addLLmConfig(node) {
  const appId = getAppId();
  // 所有的大模型都需要走计费,需要更新appId信息
  if (node.type === 'HTTP' && llmNodes.includes(node.code)) {
    if (!node.configs) {
      node.configs = []
    }
    const findId = node.configs.find(v => v.name === '_langbase_id')
    if (findId) {
      findId.value = appId
    } else {
      node.configs.push({
        name: '_langbase_id',
        type: 'string',
        value: appId
      })
    }
  }
  return node;
}

function diff(pre: any, cur: any, scope: string, changes, newData) {
  let preArr = pre[scope];
  let curArr = cur[scope];
  // 如果是输入，需要吧vars也算进去
  if (scope === 'inputs') {
    preArr = pre.inputs.concat(pre.vars);
    curArr = cur.inputs.concat(cur.vars);
  }

  const validates = ['type', 'rule', {
    name: 'enum',
    validate: (p, c) => String(p) === String(c)
  }];

  preArr.forEach(preObj => {
    const { name, title } = preObj;
    const obj = { ...preObj };
    const curObj = curArr.find(v => v.name === name);
    // 说明删除了，删除的元素不加入newData中
    if (!curObj) {
      changes[scope].push({
        type: 'delete',
        name: `${title || name}(${name})`,
        key: `${scope}.${name}`,
      });
      return;
    }

    // 如果类型变更
    validates.forEach(v => {
      let equal = false;
      if (typeof v === 'object') {
        equal = v.validate(preObj[v.name], curObj[v.name]);
      } else {
        equal = curObj[v] === preObj[v];
      }
      if (!equal) {
        // 更新对应字段
        obj[v?.name || v] = curObj[v?.name || v];
        changes[scope].push({
          type: `update_${v?.name || v}`,
          key: `${scope}.${name}.${v?.name || v}`,
          name: `${title || name}(${name})`,
          value: [preObj[v?.name || v], curObj[v?.name || v]]
        });
      }
    })
    // 将该参数放入

    // 说明原来有值，那应该是放入vars
    if ((preObj.value || preObj.value === 0) && scope === 'inputs') {
      pushData(newData, 'vars', obj);
    } else {
      pushData(newData, scope, obj);
    }
  })
  // 接下来看新增了哪些参数
  curArr.forEach(curObj => {
    const find = preArr.find(preObj => curObj?.name === preObj?.name);
    // 说明有新增的
    if (!find && typeof curObj === 'object' && curObj) {
      if ((curObj.value || curObj.value === 0) && scope === 'inputs') {
        pushData(newData, 'vars', curObj);
      } else {
        pushData(newData, scope, curObj);
      }
      changes[scope].push({
        type: 'add',
        key: `${scope}.${curObj.name}`,
        name: `${curObj.title || curObj.name}(${curObj.name})`,
      });
    }
  });
}

function pushData(data, scope, item) {
  data[scope] = data[scope] || [];
  data[scope].push(item)
}

export function mergeConfig(preConfigs, curConfigs) {
  const newConfigs = {
    ...pick(preConfigs, ['edges', 'inputs', 'outputs', 'nodes', 'greeting']),
    ...pick(curConfigs, [
      'version',
      'workflowEngineId',
      'workflowEngineName',
      'workflowId',
    ]),
  };
  return newConfigs;
}