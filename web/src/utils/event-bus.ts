interface IEvent {
  id: number;
  callback: (...args: any) => void;
}

export enum IEventType {
  NEW_CHAT_CREATE = 'new_chat_create',
  REFRESH_COMPONENTS = 'refresh_components',
  NEW_CHAT_FINISHED = 'new_chat_finished',
  STORAGE_CHANGE = 'storage_change',
  DEBUG_MESSAGE_SEND = 'debug_message_send',
  DEBUG_MESSAGE_CLEAR = 'debug_message_clear',
}

class EventBus {
  events: Record<IEventType, IEvent[]>;
  uuid: number;

  constructor() {
    this.events = Object.create(null);
    this.uuid = 0;
  }

  // 触发
  emit(type: IEventType, ...args: any) {
    const e = this.events[type];
    if (e && e.length > 0) {
      e.forEach(({ callback }: { callback: (...args: any) => void }) => {
        callback(...args);
      });
    }
  }

  // 订阅
  on(type: IEventType, callback: (...args: any) => void) {
    if (!this.events[type]) {
      this.events[type] = [];
    }
    this.events[type].push({
      id: ++this.uuid,
      callback,
    });
    return this.uuid;
  }

  // 取消订阅
  cancel(type: IEventType, eventId: number) {
    if (this.events[type]) {
      const index = this.events[type].findIndex((event) => event.id === eventId);
      if (index !== -1) {
        this.events[type].splice(index, 1);
      }
    }
  }
}

const eventBus = new EventBus();
export default eventBus;
