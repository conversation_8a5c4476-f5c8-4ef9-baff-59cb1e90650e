import {
  // refer to https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web
  EventStreamContentType,
  fetchEventSource,
} from '@microsoft/fetch-event-source';
import { JSONParse } from './common';

type Params = {
  id: string;
  type: string;
  title: string;
  description?: string;
  value?: string;
};

export type ComponentStatus = {
  componentID: string;
  input: Params[];
  output: Params[];
};

export type ChatHistoryItemResType = {
  chatId?: string;
  content?: string;
  reasoning_content?: string;
};

export interface ConversationStreamResponse {
  content?: string;
  messageID?: string;
  reasoning_content?: string;
  conversationID: string;
}

export interface StreamResponseEvent {
  event: sseResponseEventEnum;
  data: ConversationStreamResponse | EventStream;
}

export enum sseResponseEventEnum {
  error = 'error',
  answer = 'message', // animation stream
  response = 'response', // direct response, not animation
  moduleStatus = 'moduleStatus',
  appStreamResponse = 'appStreamResponse', // sse response request
  finish = 'finish',
}

export type EventStream = {
  event: sseResponseEventEnum;
  data: any;
};

type StreamFetchProps = {
  url: string;
  onMessage: (e: StreamResponseEvent) => void;
  headers?: any;
  method?: string;
  body?: any;
  abortCtrl?: AbortController;
};
type StreamResponseType = {
  responseText: string;
  reasoning_content: string;
  responseData: ChatHistoryItemResType;
};
export const streamFetch = ({
  url,
  method,
  onMessage,
  headers = {},
  body,
  abortCtrl: controller,
}: StreamFetchProps) => {
  let abortCtrl = controller;
  if (!abortCtrl) {
    abortCtrl = new AbortController();
  }
  return new Promise<StreamResponseType>(async (resolve, reject) => {
    const timeoutId = setTimeout(() => {
      abortCtrl?.abort('Time out');
    }, 60000);

    // response data
    let conversationID = '';
    let chatID = '';
    let reasonText = '';
    let responseText = '';
    let remainTextList: string[] = [];
    let remainReasonTextList: string[] = [];
    let errMsg = '';
    let responseData: ChatHistoryItemResType = {} as ChatHistoryItemResType;
    let finished = false;
    let abort = false;

    const finish = () => {
      if (errMsg) {
        return failedFinish();
      }
      return resolve({
        responseText,
        reasoning_content: reasonText,
        responseData,
      });
    };
    const failedFinish = (err?: any) => {
      finished = true;
      abortCtrl.abort(errMsg || '流响应失败');
      reject({
        message: getErrText(err, errMsg || '响应过程出现异常~'),
        responseText,
        reasoning_content: reasonText,
      });
    };

    // animate response to make it looks smooth
    function animateResponseText() {
      // abort message
      if (abort) {
        return finish();
      }
      if (finished || abortCtrl?.signal?.aborted) {
        const remainText = remainTextList.join('');
        const rText = remainReasonTextList.join('');
        onMessage({
          event: sseResponseEventEnum.answer,
          data: { content: remainText, conversationID, chatID, reasoning_content: rText } as ConversationStreamResponse,
        });
        responseText += remainText;
        return finish();
      }

      if (remainTextList.length > 0 || remainReasonTextList.length > 0) {
        const fetchCount = Math.max(1, Math.round(remainTextList.length / 60));
        const fetchReasonCount = Math.max(1, Math.round(remainReasonTextList.length / 60));
        const fetchText = remainTextList.slice(0, fetchCount).join('');
        const fetchReasonText = remainReasonTextList.slice(0, fetchReasonCount).join('');

        onMessage({
          event: sseResponseEventEnum.answer,
          data: { content: fetchText, conversationID, chatID, reasoning_content: fetchReasonText } as ConversationStreamResponse,
        });

        responseText += fetchText;
        reasonText += fetchReasonText;
        remainTextList = remainTextList.slice(fetchCount);
        remainReasonTextList = remainReasonTextList.slice(fetchReasonCount);
      }

      requestAnimationFrame(animateResponseText);
    }
    // start animation
    animateResponseText();

    try {
      // auto complete variables
      // const variables = data?.variables || {};
      // variables.cTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

      const requestData: any = {
        method: method ?? 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'text/event-stream',
          ...headers,
        },
        mode: 'cors',
        signal: abortCtrl?.signal,
        body,
      };

      // send request
      await fetchEventSource(url, {
        ...requestData,
        async onopen(res) {
          clearTimeout(timeoutId);
          const contentType = res.headers.get('content-type');

          // not stream
          if (contentType?.startsWith('text/plain')) {
            return failedFinish(await res.clone().text());
          }

          // failed stream
          if (
            !res.ok ||
            !res.headers.get('content-type')?.startsWith(EventStreamContentType) ||
            res.status !== 200
          ) {
            try {
              failedFinish(await res.clone().json());
            } catch {
              const errText = await res.clone().text();
              if (!errText.startsWith('event: error')) {
                failedFinish();
              }
            }
          }
        },
        onmessage({ event, data }) {
          function transform2CData(data: string) {
            let newData: any = {};
            let parseJson = null;
            let newEvent: sseResponseEventEnum = sseResponseEventEnum.moduleStatus;
            if (data.startsWith('data:')) {
              data = data.replace('data:', '');
              parseJson = JSONParse(data);
              if (!parseJson?.data && parseJson?.message) {
                newData.content = `${parseJson?.message}（${parseJson?.code}），请稍后再试`;
              }
              // 如果结束
              if (parseJson?.data?.totalDone) {
                abort = true;
                finish();
              }
              // 如果模块状态
              if (parseJson?.data?.responseContent?.bizCode === 'NODE') {
                newEvent = sseResponseEventEnum.moduleStatus;
              }
              if (parseJson?.data?.responseContent?.content) {
                newData = JSONParse(parseJson?.data?.responseContent?.content);
              }
              // 说明有值了
              if (newData.content) {
                if (JSONParse(newData.content)?.output) {
                  responseData = {
                    ...newData,
                    content: JSONParse(newData.content)?.output,
                  };
                } else {
                  responseData = newData;
                }
                newEvent = sseResponseEventEnum.answer;
              }
              if (!newData) {
                throw new Error('data is not valid');
              }
            }
            // 兼容老逻辑
            chatID = newData.chatID = newData.chatID || newData.chatId;
            conversationID = newData.conversationID =
              newData.conversationID || newData.conversationId;
            newData.nodeID = newData.nodeID || newData.nodeId;
            return { data: newData, event: newEvent };
          }

          // 说明需要兼容老逻辑
          // console.log('msg data1', data);
          if (data.startsWith('data:')) {
            onMessage(transform2CData(data));
            return;
          }
          if (data === '[DONE]') {
            finished = true;
            return;
          }

          // parse text to json
          const parseJson = (() => {
            try {
              return JSON.parse(data);
            } catch (error) {
              console.log('parse json error:', error);
              return {};
            }
          })();
          conversationID = parseJson?.conversationID || conversationID;
          chatID = parseJson?.chatID || chatID;

          if (!event || event === sseResponseEventEnum.answer) {
            const text: string = parseJson?.content || '';
            for (const item of text) {
              remainTextList.push(item);
            }
            // 增加推理
            const reasonText = parseJson?.reasoning_content || '';
            remainReasonTextList.push(...reasonText.split(''));
          } else if (event === sseResponseEventEnum.response) {
            const text: string = parseJson?.choices?.[0]?.delta?.content || '';
            remainTextList.push(text);
          } else if (event === sseResponseEventEnum.moduleStatus) {
            onMessage({ event, data: parseJson });
          } else if (event === sseResponseEventEnum.finish) {
            responseData = parseJson;
            abort = true;
            finish();
          } else if (event === sseResponseEventEnum.error) {
            errMsg = getErrText(parseJson, '流响应错误');
          }
        },
        onclose() {
          finished = true;
        },
        onerror(err) {
          // 必须要抛出错误，否则重试不会终止，哪怕abort也不行 https://github.com/Azure/fetch-event-source/issues/24#issuecomment-1470332423
          clearTimeout(timeoutId);
          failedFinish(getErrText(err));
          throw err; // rethrow to stop the operation
        },
        openWhenHidden: true,
      });
    } catch (err: any) {
      clearTimeout(timeoutId);
      if (abortCtrl?.signal?.aborted) {
        finished = true;
        return;
      }
      console.log(err, 'fetch error');
      failedFinish(err);
    }
  });
};

export function getErrText(err: any, def = '') {
  const msg: string = typeof err === 'string' ? err : err?.error || def || '';
  msg && console.log('error =>', msg);
  return replaceSensitiveText(msg);
}

/* replace sensitive text */
export function replaceSensitiveText(text: string) {
  // 1. http link
  text = text.replace(/(?<=https?:\/\/)[^\s]+/g, 'xxx');
  // 2. nx-xxx 全部替换成xxx
  text = text.replace(/ns-[\w-]+/g, 'xxx');

  if (errorMap[text]) {
    return errorMap[text];
  }

  return text;
}

const errorMap = {
  'ParameterInvalid(400): Switch node must have exactly one output':
    '没有找到当前分支节点的下一个输出,请检查连线',
};
