import { IModelSettingProps } from '@/components/ModelSetting';
import { ParamTypeEnum } from '@/interface/agent';
import { IAppType } from '@/interface/app';
import { DefaultModel, GlobalModel } from '@/interface/model-provider';
import defaultAgentWorkflow from './defaultConfig/agent-workflow';
import { env, getDefaultModel } from './common';
import { templates } from './defaultConfig/templates';

type ConfigSchema = {
  paramKey: string;
  paramName: string;
  description: string[];
  default: any;
  type?: string;
  validRange?: number[];
  step?: number;
};

export function isModelSupportStream(config: IModelSettingProps) {
  if (config.modelName === 'deepseek-reasoner') return false;
  return true;
  // return ['openai', 'moonshot'].includes(config.providerKind);
}

export function isModelSupportImage(model: GlobalModel) {
  return isSupport(model, 'view');
}

export function isModelSupportAudio(model: GlobalModel) {
  return isSupport(model, 'audio');
}

export function isModelSupportVideo(model: GlobalModel) {
  return isSupport(model, 'video');
}

export function isSupport(model: GlobalModel, tag) {
  return (model?.tag || []).includes(tag);
}

/**
 * 获取默认配置(会逐步下线，通过下面的getDefaultTemplateConfig代替，通过模板应用，动态性性更高)
 * @param type
 * @param values
 * @returns
 */
export function getDefaultConfig(type: IAppType, values?: any) {
  const defaultModelName = getDefaultModel().modelName;
  const defaultProviderKind = getDefaultModel().providerKind;

  // 生成型
  if (type === IAppType.AgentCompletion) {
    // @ts-ignore
    const defaultModelConfig = getModelConfigSchema({ modelName: defaultModelName, providerKind: defaultProviderKind },
      IAppType.AgentCompletion,
    );
    const defaultModelParams = defaultModelConfig?.reduce((acc, cur) => {
      return {
        ...acc,
        [cur.paramKey]: cur.default,
      };
    }, {});
    return {
      paramsInPrompt: [
        {
          type: ParamTypeEnum.textArea,
          key: 'default_input',
          title: '查询内容',
          required: false,
          config: {},
        },
        {
          type: ParamTypeEnum.select,
          key: 'language',
          title: '回答语言',
          required: false,
          config: {
            options: [
              { uuid: '1715223571359-0.5766632985093927', value: '英文', title: '英文' },
              { uuid: '1715223574380-0.1106809210105526', value: '日语', title: '日语' },
            ],
          },
        },
      ],

      modelsConfig: {
        models: [
          {
            modelName: getDefaultModel().modelName,
            modelParams: defaultModelParams,
            providerKind: getDefaultModel().providerKind,
            ratio: 1,
          },
        ],
        retryConfig: {
          retryCount: 0,
        },
      },
      prePrompt:
        '你是一个智能助理，你需要根据用户的查询内容友好地回复。以下是用户的查询内容: {{default_input}},回答语言请用 {{language}} ',
      newPrompt: {
        promptType: 'raw',
        prompt:
          '你是一个智能助理，你需要根据用户的查询内容友好地回复。以下是用户的查询内容: {{default_input}},回答语言请用 {{language}} ',
        structPrompt: [
          {
            type: 'role',
            title: '角色',
            content: '你是一个智能助理',
            placeholder: '你是一名资深的AI产品售后客服专员。',
          },
          {
            type: 'bg',
            title: '背景',
            content: '',
            placeholder: '你服务于一家AI公司，主要面向企业开发AI应用。',
          },
          {
            type: 'skill',
            title: '技能',
            content: '根据用户的查询内容友好地回复',
            placeholder: '-查询知识库：当用户咨询问题时，必须使用此技能。',
          },
          {
            type: 'task',
            title: '任务',
            content: '回复用户的查询内容{{default_input}}',
            placeholder: '从知识库中寻找最佳答案，解答客户问题。',
          },
          {
            type: 'require',
            title: '要求',
            content: '回答语言请用{{language}}',
            placeholder: '一步一步思考；认真检查你的回答是否可以解决用户的问题；',
          },
          {
            type: 'output_format',
            title: '输出格式',
            content: '文本',
            placeholder: '使用段落清晰、易读的格式',
          },
        ],
      },
    };
  }

  // 会话
  if (type === IAppType.AgentConversation) {
    // @ts-ignore
    const defaultModelConfig = getModelConfigSchema({ modelName: defaultModelName, providerKind: defaultProviderKind },
      IAppType.AgentConversation,
    );
    const defaultModelParams = defaultModelConfig?.reduce((acc, cur) => {
      return {
        ...acc,
        [cur.paramKey]: cur.default,
      };
    }, {});

    return {
      paramsInPrompt: [
        {
          type: ParamTypeEnum.select,
          key: 'language',
          title: '回答语言',
          required: false,
          config: {
            options: [
              { uuid: '1715223571359-0.5766632985093927', value: '英文', title: '英文' },
              { uuid: '1715223574380-0.1106809210105526', value: '日语', title: '日语' },
            ],
          },
        },
      ],
      modelsConfig: {
        models: [
          {
            modelName: getDefaultModel().modelName,
            modelParams: defaultModelParams,
            providerKind: getDefaultModel().providerKind,
            ratio: 1,
          },
        ],
        retryConfig: {
          retryCount: 0,
        },
      },
      prePrompt:
        '你是一个智能助理，你需要根据用户的输入内容友好地回复。回答语言请用{{language}}',
      newPrompt: {
        prompt:
          '你是一个智能助理，你需要根据用户的输入内容友好地回复。回答语言请用{{language}}',
        promptType: 'raw',
        structPrompt: [
          {
            type: 'role',
            title: '角色',
            content: '你是一个智能助理',
            placeholder: '你是一名资深的AI产品售后客服专员。',
          },
          {
            type: 'bg',
            title: '背景',
            content: '',
            placeholder: '你服务于一家AI公司，主要面向企业开发AI应用。',
          },
          {
            type: 'skill',
            title: '技能',
            content: '',
            placeholder: '-查询知识库：当用户咨询问题时，必须使用此技能。',
          },
          {
            type: 'task',
            title: '任务',
            content: '据用户的查询内容友好地回复',
            placeholder: '从知识库中寻找最佳答案，解答客户问题。',
          },
          {
            type: 'require',
            title: '要求',
            content: '回答语言请用{{language}}',
            placeholder: '一步一步思考；认真检查你的回答是否可以解决用户的问题；',
          },
          {
            type: 'output_format',
            title: '输出格式',
            content: '',
            placeholder: '使用段落清晰、易读的格式',
          },
        ],
      },
      welcomeText: '你好，请问有什么我可以帮您的嘛？',
    };
  }

  if (type === IAppType.VirtualHuman) {
    // @ts-ignore
    const defaultModelConfig = getModelConfigSchema({ modelName: defaultModelName, providerKind: defaultProviderKind },
      IAppType.AgentConversation,
    );
    const defaultModelParams = defaultModelConfig?.reduce((acc, cur) => {
      return {
        ...acc,
        [cur.paramKey]: cur.default,
      };
    }, {});
    return {
      paramsInPrompt: [],
      modelName: getDefaultModel().modelName,
      modelParams: defaultModelParams,
      providerKind: getDefaultModel().providerKind,
      // modelsConfig: {
      //   models: [{
      //     modelName: getDefaultModel().modelName,
      //     modelParams: defaultModelParams,
      //     providerKind: getDefaultModel().providerKind,
      //     ratio: 1
      //   }],
      //   retryConfig: {
      //     retryCount: 0
      //   },
      // },
      groupPrompt: {
        groups: [
          {
            name: '个人资料',
            promptType: 'raw',
            prompt: '',
            structPrompt: [],
          },
        ],
      },
      welcomeText: '你好，请问有什么我可以帮您的嘛？',
    };
  }

  if (type === IAppType.Workflow) {
    // TODO
    return {
      version: '1.0',
      workflowId: values.workflowId,
      workflowEngineId: 'f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea',
      workflowEngineName: 'f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea',
      inputs: [],
      outputs: [
        {
          id: '5a94b841',
          name: 'param1',
          type: 'string',
        },
      ],
      nodes: [
        {
          x: 0,
          y: 0,
          id: 'o1a0e9544',
          code: 'OUTPUT',
          icon: 'CodeOutlined',
          componentId: '49048006-4c0c-4258-896e-6520b9c8dcaa',
          name: '输出节点',
          type: 'OUTPUT',
          vars: [],
          ports: {
            items: [
              {
                id: 'o1a0e9544-ab780565-_logic_-input-0',
                args: {
                  x: 0,
                  y: 25,
                },
                data: {
                  key: '逻辑连接点',
                  type: '_logic_',
                },
                type: 'input',
                attrs: {
                  text: {
                    text: '',
                  },
                  circle: {
                    r: 6,
                    fill: '#fff',
                    magnet: true,
                    stroke: '#bbb',
                    strokeWidth: 2,
                  },
                },
                group: 'group1',
                label: {
                  position: 'inside',
                },
                keyName: '逻辑连接点',
                tooltip: null,
                dataType: '_logic_',
              },
              {
                id: 'o1a0e9544-5a94b841-string-input-1',
                args: {
                  x: 0,
                  y: 64,
                },
                data: {
                  key: 'param1',
                  type: 'string',
                },
                type: 'input',
                attrs: {
                  text: {
                    text: 'param1(string)',
                  },
                  circle: {
                    r: 6,
                    fill: '#fff',
                    magnet: true,
                    stroke: '#0929f9',
                    strokeWidth: 2,
                  },
                },
                group: 'group1',
                label: {
                  position: 'inside',
                },
                keyName: 'param1',
                tooltip: null,
                dataType: 'string',
              },
            ],
            groups: {
              group1: {
                position: {
                  name: 'absolute',
                },
              },
            },
          },
          width: 240,
          height: 100,
          inputs: [
            {
              id: '5a94b841',
              name: 'param1',
              type: 'string',
            },
          ],
          category: 'OUTPUT',
          renderKey: 'OUTPUT',
        },
      ],
      edges: [],
    };
  }
  // 已替换成 getDefaultTemplateConfig ，下面的配置不会生效
  if (type === IAppType.AgentWorkflow) {
    // TODO
    return defaultAgentWorkflow;
  }

  // 评估器应用
  if (type === IAppType.Evaluator) {
    // @ts-ignore
    const defaultModelConfig = getModelConfigSchema({ modelName: defaultModelName, providerKind: defaultProviderKind },
      IAppType.AgentCompletion,
    );
    const defaultModelParams = defaultModelConfig?.reduce((acc, cur) => {
      return {
        ...acc,
        [cur.paramKey]: cur.default,
      };
    }, {});
    return {
      modelsConfig: {
        models: [
          {
            modelName: getDefaultModel().modelName,
            modelParams: defaultModelParams,
            providerKind: getDefaultModel().providerKind,
            ratio: 1,
          },
        ],
        retryConfig: {
          retryCount: 0,
        },
      },
      paramsInPrompt: [
        {
          type: 'input',
          category: 'INPUT',
          dataType: 'STRING',
          key: 'reference_output',
          title: '预期输出',
          required: false,
          config: {
            length: 48,
            options: [],
          },
        },
        {
          type: 'input',
          category: 'INPUT',
          dataType: 'STRING',
          key: 'input',
          title: '输入',
          required: false,
          config: {
            length: 48,
            options: [],
          },
        },
        {
          type: 'input',
          category: 'INPUT',
          dataType: 'STRING',
          key: 'output',
          title: '输出',
          required: false,
          config: {
            length: 48,
            options: [],
          },
        },
      ],
      newPrompt: {
        prompt:
          '您是一位专业的数据标注员，负责评估模型输出是否引用了所提供文本中的真实引语。您的任务是根据以下评分标准给出评分：\n<评分标准>\n  正确引用真实引语的提交内容应：\n  - 准确指出文本中实际存在的引语。\n  - 以与文本中完全一致的措辞呈现引语，或者进行恰当的意译，且能清晰地对应到文本的特定部分。\n  - 不编造或错误归属引语。\n\n  在打分时，您应该扣除分数的情况包括：\n  - 提及文本中不存在的引语。\n  - 错误引用或歪曲现有引语的内容。\n  - 声称有引语，但在文本中找不到对应的部分。\n</评分标准>\n\n<指导说明>\n  - 仔细阅读输入的问题、模型的输出以及参考文本。\n  - 将输出中引用的引语与参考文本的内容进行对比。\n  - 确认引语引用准确且能追溯到文本中。\n</指导说明>\n\n<提醒>\n  目标是评估提交内容是否准确引用了文本中的真实引语。\n</提醒>\n\n<input>\n{{input}}\n</input>\n\n<output>\n{{output}}\n</output>\n\n使用下面的参考输出来帮助你评估响应的正确性：\n<reference_outputs>\n{{reference_output}}\n</reference_outputs>\n## 输出\n以json格式输出，包含两个字段：\n得分：一个数字，表示满足Prompt中评分标准的程度。得分范围从 0.0 到 1.0，1.0 表示完全满足评分标准，0.0 表示完全不满足评分标准。\n原因：对得分的可读解释。你必须用一句话结束理由，该句话为：因此，应该给出的分数是{你的评分}.\n\n### 输出示例\n{\n\t"score": 0.8,\n\t"reason": "得分原因"\n}',
        promptType: 'struct',
        structPrompt: [
          {
            title: '角色',
            type: 'role',
            content:
              '您是一位专业的数据标注员，负责评估模型输出是否引用了所提供文本中的真实引语。',
            placeholder: '你是一名资深的AI产品售后客服专员。',
            // id: '0647baed-0a3b-4819-878e-b9f3d09d0cfa',
          },
          {
            title: '背景',
            type: 'bg',
            content: '您的任务是根据以下评分标准给出评分。',
            placeholder: '你服务于一家AI公司，主要面向企业开发AI应用。',
            // id: '4113726c-ea4f-4cfa-844d-42a7918af46a',
          },
          {
            title: '评分标准',
            type: 'custom',
            content:
              '正确引用真实引语的提交内容应：\n-准确指出文本中实际存在的引语。\n-以与文本中完全一致的措辞呈现引语，或者进行恰当的意译，且能清晰地对应到文本的特定部分。\n-不编造或错误归属引语。\n\n在打分时，您应该扣除分数的情况包括：\n-提及文本中不存在的引语。\n-错误引用或歪曲现有引语的内容。\n-声称有引语，但在文本中找不到对应的部分。',
            placeholder: '',
            // id: '865870cc-93d5-4b4c-b8bc-fde15aa5b743',
          },
          {
            title: '指导说明',
            type: 'custom',
            content:
              '-仔细阅读输入的问题、模型的输出以及参考文本。\n-将输出中引用的引语与参考文本的内容进行对比。\n-确认引语引用准确且能追溯到文本中。\n',
            placeholder: '',
            // id: '79f1651d-2016-4aa6-9168-a424461eb5b6',
          },
          {
            title: '提醒',
            type: 'custom',
            content: '目标是评估提交内容是否准确引用了文本中的真实引语。',
            placeholder: '',
            // id: 'dc1eb28d-a124-4cc4-b2fa-da79ad7b895e',
          },
          {
            title: '输入',
            type: 'custom',
            content: '<input>\n{{input}}\n</input>',
            placeholder: '',
            // id: '84012d05-9a23-43e7-bd6a-a3dc6e30eb52',
          },
          {
            title: '输出',
            type: 'custom',
            content: '<output>\n{{output}}\n</output>',
            placeholder: '',
            // id: '0a3945a3-d12f-4916-9c06-3e6fdd764fcf',
          },
          {
            title: '参考输出',
            type: 'custom',
            content:
              '使用下面的参考输出来帮助你评估响应的正确性：\n<reference_outputs>\n{{reference_output}}\n</reference_outputs>',
            placeholder: '',
            // id: 'cb8fd3bf-2cad-49b0-a89d-0e871cff8ea6',
          },
          {
            title: '输出格式',
            type: 'output_format',
            content:
              '以json格式输出，包含两个字段：\n得分：一个数字，表示满足Prompt中评分标准的程度。得分范围从 0.0 到 1.0，1.0 表示完全满足评分标准，0.0 表示完全不满足评分标准。\n原因：对得分的可读解释。你必须用一句话结束理由，该句话为：因此，应该给出的分数是{你的评分}。\n',
            placeholder: '使用段落清晰、易读的格式',
            // id: '1eb135d6-c25c-4902-ae88-e95d547e445c',
          },
          {
            type: 'custom',
            title: '输出示例',
            content: '{\n\t"score": 0.8,\n\t"reason": "得分原因"\n}',
            placeholder: '请输入自定义主题内容',
            // id: '3de62b0b-75c4-41ee-93b4-ff008d5a4d93',
          },
        ],
      },
      prePrompt:
        '### 角色\n您是一位专业的数据标注员，负责评估模型输出是否引用了所提供文本中的真实引语。\n### 背景\n您的任务是根据以下评分标准给出评分。\n### 评分标准\n正确引用真实引语的提交内容应：\n-准确指出文本中实际存在的引语。\n-以与文本中完全一致的措辞呈现引语，或者进行恰当的意译，且能清晰地对应到文本的特定部分。\n-不编造或错误归属引语。\n\n在打分时，您应该扣除分数的情况包括：\n-提及文本中不存在的引语。\n-错误引用或歪曲现有引语的内容。\n-声称有引语，但在文本中找不到对应的部分。\n### 指导说明\n-仔细阅读输入的问题、模型的输出以及参考文本。\n-将输出中引用的引语与参考文本的内容进行对比。\n-确认引语引用准确且能追溯到文本中。\n\n### 提醒\n目标是评估提交内容是否准确引用了文本中的真实引语。\n### 输入\n<input>\n{{input}}\n</input>\n### 输出\n<output>\n{{output}}\n</output>\n### 参考输出\n使用下面的参考输出来帮助你评估响应的正确性：\n<reference_outputs>\n{{reference_output}}\n</reference_outputs>\n### 输出格式\n以json格式输出，包含两个字段：\n得分：一个数字，表示满足Prompt中评分标准的程度。得分范围从 0.0 到 1.0，1.0 表示完全满足评分标准，0.0 表示完全不满足评分标准。\n原因：对得分的可读解释。你必须用一句话结束理由，该句话为：因此，应该给出的分数是{你的评分}。\n\n### 输出示例\n{\n\t"score": 0.8,\n\t"reason": "得分原因"\n}',
    };
  }
}

export function getDefaultTemplateConfig(type: IAppType) {
  if (templates[type] && templates[type][env]) {
    return templates[type][env];
  }
  return null;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function getModelConfigSchema(
  model: GlobalModel,
  type?: IAppType,
): ConfigSchema[] {
  if (!model) {
    // 默认返回 OpenAI 的参数介绍、默认值
    return OpenAIModelConfigSchema(model, type);
  }
  const { providerKind, name } = model;
  if (providerKind === 'openai') {
    return OpenAIModelConfigSchema(model, type);
  }
  if (providerKind === 'wenxin') {
    return WenXinModelConfigSchema(model, type);
  }
  if (providerKind === 'moonshot') {
    return MoonshotModelConfigSchema(model, type);
  }
  // 默认返回 OpenAI 的参数介绍、默认值
  return OpenAIModelConfigSchema(model, type);
}

const temperatureDescription = [
  '控制回复的随机性',
  '值越大，回复越随机，越具有创意',
  '值越小，回复越确定或一致',
];
const topPDescription = [
  '控制生成多样性。',
  '值越大, 输出会包含更多的单次选项。',
  '值越小，模型会更集中在高概率的单次上，输出更确定但更缺乏多样性。',
  '核采样与随机性不建议同时修改。',
];
const presencePenaltyDesc = [
  '控制对上文已存在的话题的偏好程度。',
  '值越大, 越可能是用到新的话题。',
];
const frequencyPenaltyDesc = [
  '影响常见与罕见词汇使用。',
  '值越大, 倾向于生成不常见的词汇和表达方式。',
  '值越小，更倾向于使用常见和普遍接受的词汇或短语。',
];
const maxTokensDesc = [
  '用于限制回复的最大长度，以 token 为单位。',
  '输入 token 与 单次回复 token 之和不得超过模型的最大 token 长度。',
  '建议将该置设置在三分之二以下',
];

const maxConversationDesc = [
  '记忆上下文最多的轮数，会将之前的对话作为本次的上下文，轮数越多每次对话消耗的token数越多。',
  '同时，过多的上下文可能会影响本次回复的质量',
  '建议文本创意型任务设置为2以下，问答型设置成3-4',
];

const jsonSchemaDesc = [
  '开启JSON模式后，会将回复结果严格解析为 JSON 格式',
  '注意，还需要在文中强行明确提及 “按 JSON 格式返回”，同时注明JSON的结构',
];

const audioTimestampDesc = [
  '开启精准时间戳后，会返回音频的精准时间戳',
  '适用于需要根据音频内容进行精准时间戳识别的场景，例如滚词场景，耗时可能会增加',
];

export const OpenAIModelConfigSchema = (
  model: GlobalModel,
  appType: IAppType,
): ConfigSchema[] => {
  const schemas: ConfigSchema[] = [
    {
      paramKey: 'temperature',
      paramName: '随机性',
      description: temperatureDescription,
      validRange: [0, 2],
      default: 1,
    },
    {
      paramKey: 'top_p',
      paramName: '核采样',
      description: topPDescription,
      validRange: [0, 1],
      default: 0.85,
    },
    {
      paramKey: 'presence_penalty',
      paramName: '话题新鲜度',
      description: presencePenaltyDesc,
      validRange: [-2, 2],
      default: 0.0,
    },
    {
      paramKey: 'frequency_penalty',
      paramName: '频率惩罚度',
      description: frequencyPenaltyDesc,
      validRange: [-2, 2],
      default: 0.0,
    },
    {
      paramKey: 'max_tokens',
      paramName: '单次回复限制',
      description: maxTokensDesc,
      validRange: [1, 8192],
      step: 1,
      default: 1024,
    },
  ];
  if (appType === IAppType.AgentConversation) {
    schemas.push({
      paramKey: 'max_conversations',
      paramName: '最大会话数数量',
      description: maxConversationDesc,
      validRange: [0, 20],
      step: 1,
      default: 5,
    });
  }
  if (model?.tag?.includes('json')) {
    schemas.unshift({
      paramKey: 'json_object',
      paramName: 'JSON模式',
      type: 'switch',
      validRange: [0, 1],
      description: jsonSchemaDesc,
      default: false,
    });
  }
  if ((model?.name ?? model?.modelName ?? '').includes('gemini')) {
    schemas.unshift({
      paramKey: 'audio_timestamp',
      paramName: '精准时间戳',
      type: 'switch',
      validRange: [0, 1],
      description: audioTimestampDesc,
      default: false,
    });
  }
  return schemas;
};

export const WenXinModelConfigSchema = (
  modelName: GlobalModel,
  appType: IAppType,
): ConfigSchema[] => [
  {
    paramKey: 'temperature',
    paramName: '随机性',
    description: temperatureDescription,
    validRange: [0.01, 1],
    default: 0.95,
  },
  {
    paramKey: 'top_p',
    paramName: '核采样',
    description: topPDescription,
    validRange: [0, 1],
    default: 0.8,
  },
  {
    paramKey: 'frequency_penalty',
    paramName: '频率惩罚度',
    description: frequencyPenaltyDesc,
    validRange: [1, 2],
    default: 1.0,
  },
];

export const MoonshotModelConfigSchema = (
  modelName: GlobalModel,
  appType: IAppType,
): ConfigSchema[] => [
  {
    paramKey: 'temperature',
    paramName: '随机性',
    description: temperatureDescription,
    validRange: [0, 2],
    default: 0.3,
  },
  {
    paramKey: 'top_p',
    paramName: '核采样',
    description: topPDescription,
    validRange: [0, 1],
    default: 1.0,
  },
  {
    paramKey: 'presence_penalty',
    paramName: '话题新鲜度',
    description: presencePenaltyDesc,
    validRange: [-2, 2],
    default: 0,
  },
  {
    paramKey: 'frequency_penalty',
    paramName: '频率惩罚度',
    description: frequencyPenaltyDesc,
    validRange: [-2, 2],
    default: 0,
  },
  {
    paramKey: 'max_tokens',
    paramName: '单次回复限制',
    description: maxTokensDesc,
    validRange: [1, 8192],
    step: 1,
    default: 1024,
  },
  // {
  //   paramKey: 'max_conversations',
  //   paramName: '最大会话数数量',
  //   description: maxConversationDesc,
  //   validRange: [1, 20],
  //   step: 1,
  //   default: 5,
  // },
];

export const MockModelList = [
  {
    id: '1',
    modelType: 'llm',
    modelName: getDefaultModel().modelName,
    providerKind: getDefaultModel().providerKind,
    createdAt: '',
    updatedAt: '',
  },
  {
    id: '2',
    modelType: 'llm',
    modelName: 'gpt-4.0',
    providerKind: 'openai',
    createdAt: '',
    updatedAt: '',
  },
  {
    id: '3',
    modelType: 'llm',
    modelName: 'Claude-2',
    providerKind: 'anthropic',
    createdAt: '',
    updatedAt: '',
  },
  {
    id: '4',
    modelType: 'llm',
    modelName: 'Claude-Instant',
    providerKind: 'anthropic',
    createdAt: '',
    updatedAt: '',
  },
] as DefaultModel[];
