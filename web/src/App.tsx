import { ConfigProvider, notification } from 'antd';
import locale from 'antd/lib/locale/zh_CN';
import PopoChat from "@music/ct-pc-paopaocaller";
import { RootRouter } from './pages/router';
import { GlobalContextProvider } from './store';
import { useEffect } from 'react';
import { SystemApi } from './api/system';
import { env } from './utils/common';
import Watermark from './components/Watermark';

(window as any).corona = (window as any).corona || {
  info: (info: string) => {
    console.log(info);
  },
  error: (error: string) => {
    console.error(error);
  },
  warn: (warn: string) => {
    console.warn(warn);
  }
}

// 定义通知策略接口
interface NotifyStrategy {
  getNotification: (data: any) => {
    type: string;
    title: string;
    content: React.ReactNode;
  };
}

// 更新说明策略
const upgradeStrategy: NotifyStrategy = {
  getNotification: (data) => ({
    type: 'info',
    title: '更新说明',
    content: (
      <div>
        <pre>{data.text}</pre>
        <a href={data.link} target="_blank">查看详情</a>
      </div>
    )
  })
};

// 维护公告策略
const maintainStrategy: NotifyStrategy = {
  getNotification: (data) => ({
    type: 'warning',
    title: '维护公告',
    content: (
      <div>
        预计 <span style={{ color: 'red', fontWeight: 'bold' }}>{data.time}</span> 进行平台升级维护，{data?.extra ? <span style={{ color: 'red', fontWeight: 'bold' }}>{data.extra}，</span> : ''}期间如果使用遇到问题，请加群反馈<PopoChat ssid="5274049" chatId="5274049" type={1} name="LangBase 金牌服务群" />
      </div>
    )
  })
};

// 策略映射
const strategyMap: Record<string, NotifyStrategy> = {
  upgrade: upgradeStrategy,
  maintain: maintainStrategy,
};

const shouldShowNotification = (notifyId: string) => {
  const cacheKey = `notify-${notifyId}`;
  const cache = localStorage.getItem(cacheKey);

  if (!cache) return true;

  try {
    // 兼容旧版本的缓存格式（可能只有 timestamp）
    const cacheData = JSON.parse(cache);
    console.log('cache', cacheData);
    const count = typeof cacheData === 'object' ? (cacheData.count ?? 3) : 0;
    const timestamp = typeof cacheData === 'object' ? cacheData.timestamp : cacheData;
    
    // 超过显示次数限制
    if (count >= 3) return false;
    
    const today = new Date().setHours(0, 0, 0, 0);
    const cachedDate = new Date(timestamp).setHours(0, 0, 0, 0);

    return today > cachedDate;
  } catch {
    return true;
  }
};

function App() {
  const [api, contextHolder] = notification.useNotification();
  useEffect(() => {
    SystemApi.getNotify().then(res => {
      if (!res.result) return;
      
      const strategy = strategyMap[res.result.type];
      if (!strategy) return;

      const cacheKey = `notify-${res.result.id}`;
      if (!shouldShowNotification(res.result.id)) return;

      const notification = strategy.getNotification(res.result);
      api[notification.type]({
        message: notification.title,
        duration: 6,
        description: notification.content,
        pauseOnHover: true,
      });

      try {
        // 读取并解析现有缓存，处理所有可能的格式
        const existingCache = localStorage.getItem(cacheKey);
        let count = 0;
        if (existingCache) {
          const parsed = JSON.parse(existingCache);
          count = typeof parsed === 'object' ? (parsed.count ?? 0) : 0;
        }

        // 更新缓存
        localStorage.setItem(cacheKey, JSON.stringify({
          timestamp: new Date().getTime(),
          count: count + 1
        }));
      } catch (e) {
        // 如果存储失败，至少保证基本功能
        localStorage.setItem(cacheKey, JSON.stringify({
          timestamp: new Date().getTime(),
          count: 1
        }));
      }
    });
  }, []);

  const isPre = env === 'pre';
  console.log("[isPre]", isPre, env);

  return (
    <>
      {isPre ?
        <>
          <Watermark />
          <div className='top-bar'></div>
        </>
        : null}
      <ConfigProvider locale={locale}>
        {contextHolder}
        <GlobalContextProvider>
          <RootRouter />
        </GlobalContextProvider>
      </ConfigProvider>
    </>
  );
}

export default App;
