import { <PERSON>rud<PERSON><PERSON> } from '@interface/index';
import { request } from '@utils/request';

import { getGroupId, getWorkspaceId } from '@/utils/state';

export interface IComponent {
  id: string;
  name: string;
  alias: string;
  code: string;
  description: string;
  type: string;
  categoryID: string;
  workspaceID: string;
  scope: string;
  deprecated: boolean;
  config?: Record<string, any>;
}

interface IUpdateComponent {
  name: string;
  alias: string;
  url: string;
  code: string;
  appTypes: string[];
  description: string;
  type: string;
  categoryID: string;
  category: string;
  workspaceID: string;
  scope?: string;
  groupID?: string;
  deprecated?: boolean;
  config: Record<string, any>;
}

/**
 * 获取所有组件
 * @returns
 */
function listCustom(
  _workspaceID?: string,
  categories?: string[],
  appTypes?: string[],
  types?: string[],
  pageNumber?: number,
  pageSize?: number,
  useRaw?: boolean,
): Promise<IComponent[]> {
  return list(_workspaceID, categories, appTypes, types, pageNumber, pageSize, useRaw, true);
}

/**
 * 获取所有组件
 * @returns
 */
function list(
  _workspaceID?: string,
  categories?: string[],
  appTypes?: string[],
  types?: string[],
  pageNumber?: number,
  pageSize?: number,
  useRaw?: boolean,
  hideDeprecated?: boolean,
): Promise<IComponent[]> {
  // debugger
  const workspaceID = _workspaceID || getWorkspaceId();
  const groupId = getGroupId();
  let url = `/workspace/${workspaceID}/components?pageNumber=${
    pageNumber || 1
  }&pageSize=${pageSize || 300}`;
  if (Array.isArray(appTypes) && appTypes.length && appTypes[0] !== '全部') {
    url += `&appTypes=${appTypes}`;
  }
  if (Array.isArray(categories) && categories.length && categories[0] !== '全部') {
    url += `&categories=${categories}`;
  }
  if (Array.isArray(types)) {
    url += `&types=${types}`;
  }
  if (groupId) {
    url += `&groupId=${groupId}`;
  }
  if (hideDeprecated) {
    url += `&hideDeprecated=true`;
  }
  return request({
    url,
  }).then((res) => {
    if (useRaw) {
      return res;
    }
    return res.items ? res.items : res;
  });
}

/**
 * 获取环境变量相关组件
 * @returns
 */
function listWithEnv(
  _workspaceID?: string,
  pageNumber?: number,
  pageSize?: number,
  useRaw?: boolean,
): Promise<IComponent[]> {
  // debugger
  const workspaceID = _workspaceID || getWorkspaceId();
  const groupId = getGroupId();
  const url = `/workspace/${workspaceID}/components`;
  return request({
    url,
    params: {
      pageNumber: pageNumber || 1,
      pageSize: pageSize || 300,
      hasEnvVar: true,
      groupId
    }
  }).then((res) => {
    if (useRaw) {
      return res;
    }
    return res.items.length ? res.items : res;
  });
}

/**
 * 创建组件
 * @param data
 * @returns
 */
function create(data: IUpdateComponent, _workspaceID?: string): Promise<void> {
  const workspaceID = _workspaceID || getWorkspaceId();
  return request({
    url: `/workspace/${workspaceID}/components`,
    method: 'POST',
    data,
  });
}

/**
 * 获取组件
 * @param componentID
 * @returns
 */
function get(componentID: string): Promise<IComponent> {
  return request({
    url: `/components/${componentID}`,
  });
}

/**
 * 删除组件
 * @param componentID
 * @returns
 */
function remove(componentID: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/components/${componentID}`,
  });
}

/**
 * 更新组件
 * @param data
 * @param componentID
 * @returns
 */
function update(data: IUpdateComponent, componentID: string): Promise<void> {
  return request({
    method: 'PUT',
    data,
    url: `/components/${componentID}`,
  });
}

export const ComponentApi = {
  list,
  listCustom,
  listWithEnv,
  get,
  create,
  update,
  remove,
} as CrudAPI<IComponent, IUpdateComponent> & {
  listCustom: (params?: any, ...rest) => Promise<IComponent[]>;
};
