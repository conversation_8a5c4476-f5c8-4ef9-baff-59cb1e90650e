import { request } from '@utils/request';
import { CrudAPI } from '@/interface';
import { LLMModel, LLMModelCreate, LLMModelUpdate, TagConfig } from '@/interface/llm-model';


export const tagMap: Record<string, TagConfig> = {
  recommend: {
    text: '推荐',
    color: 'green',
  },
  fast: {
    text: '快',
    color: 'green',
  },
  slow: {
    text: '慢',
    color: 'blue',
  },
  high: {
    text: '昂贵',
    color: 'red',
  },
  low: {
    text: '便宜',
    color: 'lime',
  },
  deprecated: {
    text: '废弃',
    color: 'gray',
  },
  smart: {
    text: '性能',
    color: 'purple',
  },
  json: {
    text: 'JSON模式',
    color: 'blue',
  },
  'web-search': {
    text: '联网搜索',
    color: 'pink',
  },
  view: {
    text: '图片输入',
    color: 'gold',
  },
  audio: {
    text: '音频输入',
    color: 'gold',
  },
  video: {
    text: '视频输入',
    color: 'gold',
  },
  
  
} as const;

function listModels(params?: any): Promise<LLMModel[]> {
  return request({
    url: '/llm-models',
    params,
  }).then((res) => {
    return res.items;
  });
}

function listModelsWithPage(_: any, pageNumber?: number, pageSize?: number): Promise<LLMModel[]> {
  return request({
    url: '/llm-models',
    params: {
      pageNumber: pageNumber || 1,
      pageSize: pageSize || 10,
    },
  });
}

function createModel(data: LLMModelCreate): Promise<void> {
  return request({
    method: 'POST',
    url: '/llm-models',
    data,
  });
}

function updateModel(data: LLMModelUpdate, id: string): Promise<void> {
  return request({
    method: 'PUT',
    url: `/llm-models/${id}`,
    data,
  });
}

function deleteModel(id: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/llm-models/${id}`,
  });
}

function getModel(id: string): Promise<LLMModel> {
  return request({
    url: `/llm-models/${id}`,
  });
}

export const LLMModelApi = {
  list: listModels,
  listWithPage: listModelsWithPage,
  create: createModel,
  update: updateModel,
  remove: deleteModel,
  get: getModel,
} as CrudAPI<LLMModel, LLMModelCreate>; 