import { request } from '@utils/request';

import {
  CreateModelProviderBindingDto,
  GlobalModel,
  GlobalModelProvider,
  ModelProviderBinding,
  UpdateDefaultModelDto,
  UpdateModelProviderBindingDto,
} from '@/interface/model-provider';
import { getWorkspaceState } from '@/utils/state';

function listGlobalModelProvider(): Promise<GlobalModelProvider[]> {
  return request({
    url: '/model-provider',
  }).then((res) => {
    return res;
  });
}

function listWorkspaceModelProviderBinding(
  workspaceId: string,
): Promise<ModelProviderBinding[]> {
  return request({
    url: `/workspace/${workspaceId}/model-provider`,
  }).then((res) => {
    return res.items;
  });
}

function createWorkspaceModelProviderBinding(
  data: CreateModelProviderBindingDto,
  workspaceId: string,
): Promise<void> {
  return request({
    method: 'POST',
    url: `/workspace/${workspaceId}/model-provider`,
    data,
  });
}

function listAppModelProviderBinding(appId: string): Promise<ModelProviderBinding[]> {
  return request({
    url: `/app/${appId}/model-provider`,
  }).then((res) => {
    return res.items;
  });
}

function createAppModelProviderBinding(
  data: CreateModelProviderBindingDto,
  appId: string,
): Promise<void> {
  const workspaceId = getWorkspaceState('id');
  return request({
    method: 'POST',
    url: `/app/${appId}/model-provider`,
    data: {
      ...data,
      workspaceId
    },
  });
}


function listGroupModelProviderBinding(groupId: string): Promise<ModelProviderBinding[]> {
  return request({
    url: `/group/${groupId}/model-provider`,
  }).then((res) => {
    return res.items;
  });
}


function createGroupModelProviderBinding(
  data: CreateModelProviderBindingDto,
  groupId: string,
): Promise<void> {
  const workspaceId = getWorkspaceState('id');
  return request({
    method: 'POST',
    url: `/group/${groupId}/model-provider`,
    data: {
      ...data,
      workspaceId
    },
  });
}

function updateWorkspaceBindingWithAigw(
  aigw_id: string,
): Promise<void> {
  const workspaceId = getWorkspaceState('id');
  return request({
    method: 'POST',
    url: `/workspace/${workspaceId}/aigw_app/${aigw_id}/bind`,
  });
}

function updateGroupBindingWithAigw(
  groupId: string,
  aigw_id: string,
): Promise<void> {
  return request({
    method: 'POST',
    url: `/group/${groupId}/aigw_app/${aigw_id}/bind`,
  });
}

function deleteAigwAccount(
  aigw_id: string,
): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/aigw_app/${aigw_id}`,
  });
}


function updateModelProviderBinding(
  data: UpdateModelProviderBindingDto,
  id: string,
): Promise<void> {
  const workspaceId = getWorkspaceState('id');
  return request({
    method: 'PUT',
    url: `/model-provider/${id}`,
    data: {
      ...data,
      workspaceId
    },
  });
}

function deleteModelProviderBinding(id: string): Promise<void> {
  const workspaceId = getWorkspaceState('id');
  return request({
    method: 'DELETE',
    url: `/model-provider/${id}`,
    data: {
      workspaceId
    }
  });
}

interface IListGlobalModelDto {
  type?: string;
  workspaceId?: string;
  groupId?: string;
}

function listGlobalModel(params?: IListGlobalModelDto): Promise<GlobalModel[]> {
  return request({
    url: '/model_v2',
    cache: true,
    needAuth: false,
    params,
  }).then((res) => {
    return res;
  });
}

function listWorkspaceDefaultModel(workspaceId: string) {
  return request({
    url: `/workspace/${workspaceId}/default-model`,
  }).then((res) => {
    return res;
  });
}

function updateWorkspaceDefaultModel(data: UpdateDefaultModelDto) {
  const workspaceId = getWorkspaceState('id');
  return request({
    method: 'PUT',
    url: `/workspace/${workspaceId}/default-model`,
    data,
  });
}

function listGroupDefaultModel(groupId: string) {
  return request({
    url: `/group/${groupId}/default-model`,
  }).then((res) => {
    return res.items;
  });
}

function updateGroupDefaultModel(data: UpdateDefaultModelDto, groupId: string) {
  return request({
    method: 'PUT',
    url: `/group/${groupId}/default-model`,
    data,
  });
}

interface AIDecisionParams {
  taskType: string;
  tokenEstimation: string;
  focusReason: string;
  focus: {
    performance: number;
    cost: number;
    context: number;
    speed: number;
  };
  matchModels: {
    key: string;
    name: string;
  }[];
  relaxedModels: {
    key: string;
    name: string;
  }[];
}

function getAIDecision(data: AIDecisionParams) {
  console.log(data);
  const url = 'https://langbase.netease.com/api/v1/completion?user=null&appID=2203bc90-7ecb-49eb-b85e-eceb4b201e2e';
  return request({
    method: 'POST',
    url,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer sS94ZI2amA674dyH3YMxyFmFo6b8BXeXd9ZuXSCNHGc'
    },
    data: {
      parameters: {
        input: JSON.stringify(data)
      }
    },
  });
}

interface AIAnalysisParams {
  description: string;
  dailyVolume: string;
  costSensitivity: 'sensitive' | 'insensitive';
}

function getAIAnalysis(data: AIAnalysisParams) {
  console.log(data);
  const url = 'https://langbase.netease.com/api/v1/completion?user=null&appID=d4f41b03-0dab-4d98-945a-70c8d8e5ce4e';
  return request({
    method: 'POST',
    url,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer xtoNgFsBNbu0oWCV6WTevQ3PGhiFORtS8Jm_CpIdC_Y'
    },
    data: {
      parameters: {
        input: JSON.stringify(data)
      }
    },
  });
}


export const ModelProviderApi = {
  listGlobalModelProvider,
  listWorkspaceModelProviderBinding,
  createWorkspaceModelProviderBinding,
  listGroupModelProviderBinding,
  createGroupModelProviderBinding,
  createAppModelProviderBinding,
  listAppModelProviderBinding,
  updateModelProviderBinding,
  deleteAigwAccount,
  updateWorkspaceBindingWithAigw,
  updateGroupBindingWithAigw,
  deleteModelProviderBinding,
};

export const DefaultModelApi = {
  listGlobalModel,
  listWorkspaceDefaultModel,
  updateWorkspaceDefaultModel,
  listGroupDefaultModel,
  updateGroupDefaultModel,
  getAIDecision,
  getAIAnalysis,
};
