import { UserModel } from '@/interface';
import { MemberOfResource, RoleType } from '@/interface/member';

import { request } from '../utils/request';

async function search(params: { name: string }): Promise<UserModel[]> {
  return request({
    url: `/account`,
    params,
  }).then((res) => {
    return res.items;
  });
}

async function addMember(
  memberIds: string[],
  role: RoleType,
  resType: MemberOfResource,
  resId: string,
) {
  return request({
    url: `/${resType}/${resId}/members`,
    method: 'PUT',
    data: memberIds.map((it) => {
      return {
        memberID: it,
        role,
      };
    }),
  });
}

async function removeMember(memberID: string, resType: MemberOfResource, resId: string) {
  return request({
    url: `/${resType}/${resId}/member`,
    method: 'DELETE',
    params: {
      memberID,
    },
  });
}

async function listMember(
  resType: MemberOfResource,
  resId: string,
  params: { pageNumber: number; pageSize: number },
) {
  return request({
    url: `/${resType}/${resId}/member`,
    params,
  });
}

export const MemberApi = {
  search,
  addMember,
  removeMember,
  listMember,
};
