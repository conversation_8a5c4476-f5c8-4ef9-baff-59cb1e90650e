import { CrudAPI } from '@interface/index';
import { request } from '@utils/request';

interface ICategory {
  id: string;
  name: string;
  description: string;
}

interface IUpdateCategory {
  name: string;
  description: string;
}

/**
 * 获取所有分类
 * @returns 
 */
function list(pageSize?: number, pageNumber?: number): Promise<ICategory[]> {
  return request({
    url: `/component-categories`,
    data: { pageNumber, pageSize }
  }).then((res) => {
    return res.items;
  });
}

/**
 * 创建组件分类
 * @param data 
 * @returns 
 */
function create(data: IUpdateCategory): Promise<void> {
  return request({
    url: `/api/v1/component-categoriess`,
    method: 'POST',
    data,
  });
}

/**
 * 获取分类信息
 * @param categoryID 
 * @returns 
 */
function get(categoryID: string): Promise<ICategory> {
  return request({
    url: `/api/v1/component-categories/${categoryID}`,
  })
}

/**
 * 删除分类
 * @param categoryID 
 * @returns 
 */
function remove(categoryID: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/api/v1/component-categories/${categoryID}`,
  })
}


/**
 * 更新分类
 * @param data 
 * @param categoryID 
 * @returns 
 */
function update(data: IUpdateCategory, categoryID: string): Promise<void> {
  return request({
    method: 'PUT',
    data,
    url: `/api/v1/component-categories/${categoryID}`,
  })
}

export const CategoryApi = {
  list,
  get,
  create,
  update,
  remove
} as CrudAPI<ICategory, IUpdateCategory>
