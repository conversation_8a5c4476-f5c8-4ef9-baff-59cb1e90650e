import { ResponseItems } from '@/interface/common';

import { CrudAPI, GroupModel, GroupUpdateModel, Member } from '../interface';
import { request } from '../utils/request';
import { getGroupId } from '@/utils/state';

function listWithPage(
  workspaceId: string,
  pageNumber: number = 1,
  pageSize: number = 12,
): Promise<ResponseItems<GroupModel>> {
  if (!workspaceId) {
    return Promise.reject('workspaceId is required');
  }
  return request({
    url: `/workspace/${workspaceId}/group`,
    params: {
      pageNumber,
      pageSize,
    },
  });
}

function list(workspaceId: string): Promise<GroupModel[]> {
  return request({
    url: `/workspace/${workspaceId}/group`,
    params: {
      pageNumber: 1,
      pageSize: 300
    }
  }).then((res) => {
    return res.items;
  });
}

function get(groupId: string): Promise<GroupModel> {
  return request({
    url: `/group/${groupId}`,
  });
}

function create(
  data: { name: string; description: string },
  workspaceId: string,
): Promise<void> {
  return request({
    method: 'POST',
    url: `/workspace/${workspaceId}/group`,
    data,
  });
}

function update(data: GroupUpdateModel, id: string): Promise<void> {
  return request({
    method: 'PUT',
    url: `/group/${id}`,
    data,
  });
}

function getGroupAdmin(id: string): Promise<Member[]> {
  return request({
    url: `group/${id}/admins`,
  }).then(res => {
    return res?.items || [];
  });
}

function remove(id: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/group/${id}`,
  });
}

export const GroupApi = {
  listWithPage,
  list,
  get,
  create,
  update,
  remove,
  getGroupAdmin,
} as CrudAPI<GroupModel, GroupUpdateModel> & {
  getGroupAdmin: (groupId: string) => Promise<Member[]>;
  listWithPage: {
    (
      workspaceId: string,
      pageNumber?: number,
      pageSize?: number,
    ): Promise<ResponseItems<GroupModel>>;
  };
};
