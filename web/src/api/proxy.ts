// @ts-nocheck
import { omit } from 'lodash';
import { request } from '../utils/request';

function exec(args): Promise<any> {
  let url = `/proxy/script/exec`; 
  if (args.scriptType === 'GROOVY') {
    return execGroovy(args);
  }

  return request({
    url,
    silence: true,
    method: 'POST',
    data: {
      ...args
    }
  }).then(res => {
    console.log("res", res);
    return res;
  });
}

const execGroovy = (args): Promise<any> => {
  const data = {
    script: args.script,
    inputData: omit(args, ['script', 'scriptType', 'url'])
  }
  return request({
    raw: true,
    proxyInfo: {
      url: '/api/langbase/groovy/debug',
      // base: 'http://qa.igame.163.com',
    },
    method: 'POST',
    data,
  }).then(res => {
    console.log("res", res);
    return { code: 200, ...res };
  });
}

type TaskDaysParams = {
  endTime: number;
  limit: number;
  startTime: number;
  workflowCode: string;
}

type TaskDaysResponse = {
  days: string[];  // 格式: YYYY-MM-DD
}

/**
 * 获取任务天数
 * @param params - 查询参数
 * @param params.endTime - 结束时间戳
 * @param params.limit - 限制返回天数
 * @param params.startTime - 开始时间戳  
 * @param params.workflowCode - 工作流代码
 * @returns Promise<TaskDaysResponse> - 返回包含日期数组的响应
 * @example
 * const res = await getTaskDays({
 *   endTime: 1672502400000,
 *   limit: 10,
 *   startTime: 1672416000000,
 *   workflowCode: 'workflow_001'
 * });
 * // res = { days: ['2024-12-05', '2024-12-09'] }
 */
function getTaskDays(params: TaskDaysParams): Promise<TaskDaysResponse> {
  const url = `/proxy/task/days/query`; 
  return request({
    method: 'GET',
    url,
    params
  });
}

export const proxyApi = {
  exec,
  getTaskDays,
};

