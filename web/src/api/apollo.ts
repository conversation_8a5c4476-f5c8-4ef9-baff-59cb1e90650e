import { request } from "@/utils/request";

// 枚举定义
export enum ApproveStatus {
  ERROR = -1,
  START = 0,
  COMPLETE = 100,
  WITHDRAW = 20
}

// 接口定义
interface BizVariables {
  dynamicAssign: {
    config_audit: string[];
  };
}

interface ProcVariables {
  noDeployWindow: boolean;
  customAdmit: boolean;
}

interface MyField {
  fieldChineseName: string;
  fieldValue: string;
}

interface FormContent {
  app: string;
  setting: string;
  channel: string;
  deployReason: string;
  mainApprover: string;
  reason: string;
  procTitle: string;
  procPriority: 10 | 20 | 30; // 流程优先级(10:高 20:中 30:低)
  cardCustomField?: MyField[];
}

interface CreateApproveParams {
  appName: string;
  settingId?: string;
  channel?: string;
  deployReason?: string;
  mainApprover?: string;
  reason?: string;
  audits?: string[];
  operator?: string;
  priority: 10 | 20 | 30; // 10, 20, 30
}

interface ResetApproveParams {
  sprint_id: string;
}

interface WithdrawApproveParams {
  approve_id: string;
  reason: string;
}

interface ApproveDetailParams {
  proc_inst_serials: string[];
}

interface ApproveResponse {
  formDataId?: number;
  getDrafterEmail?: string;
  procDefKey?: string;
  procInstId?: number;
  procNo?: string;
  procStatus?: string;
}

interface ApproveDetailItem {
  procInstId: number;
  procDefKey: string;
  procDefName: string;
  procInstSerial: string;
  procInstName: string;
  priority: number;
  status: ApproveStatus;
}

function createApprove(data: CreateApproveParams): Promise<ApproveResponse> {
  const newData = {
    operator: data.operator,
    priority: data.priority,
    procVariables: {
      noDeployWindow: true,
      customAdmit: true,
    },
    bizVariables: {
      dynamicAssign: {
        config_audit: (data.audits || []).map((item) => {
          if (item.includes('@')) {
            return item.split('@')[0];
          }
          return item;
        }),
      },
    },
    formContent: {
      app: data.appName,
      setting: data.settingId || 'default',
      channel: data.channel || 'default',
      deployReason: data.deployReason,
      mainApprover: data.mainApprover,
      reason: data.reason || data.deployReason,
      procTitle: `【LangBase】${data.appName} 发布申请`,
      procPriority: data.priority,
    }
  }
  return request({
    url: `/apollo/approve`,
    method: 'POST',
    data: newData,
  });
}

function resetApprove(data: ResetApproveParams): Promise<ApproveResponse> {
  return request({
    url: `/apollo/approve/reset`,
    method: 'POST',
    data,
  });
}

function withdrawApprove(data: WithdrawApproveParams): Promise<ApproveResponse> {
  return request({
    url: `/apollo/approve/withdraw`,
    method: 'POST',
    data,
  });
}

function approveDetail(data: ApproveDetailParams): Promise<ApproveDetailItem[]> {
  return request({
    url: `/apollo/approve/detail`,
    method: 'POST',
    data,
  }).then(res => {
    return res.list
  })
}

export const ApolloApi = {
  createApprove,
  resetApprove,
  withdrawApprove,
  approveDetail,
};

export type {
  BizVariables,
  ProcVariables,
  MyField,
  FormContent,
  CreateApproveParams,
  ResetApproveParams,
  WithdrawApproveParams,
  ApproveDetailParams,
  ApproveResponse,
  ApproveDetailItem
}; 