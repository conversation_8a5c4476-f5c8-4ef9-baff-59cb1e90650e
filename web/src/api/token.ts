import { ResponseItems } from '@/interface/common';

import { Resources, TempToken, Token } from '../interface';
import { request } from '../utils/request';

function remove(tokenID: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/token/${tokenID}`,
  });
}

function list(
  resourceType: 'app' | 'workspace' | 'group',
  resourceID: string,
): Promise<ResponseItems<Token>> {
  return request({
    url: `${resourceType}/${resourceID}/token`,
  });
}

function create(
  resourceType: 'app' | 'workspace' | 'group',
  resourceID: string,
  resources?: Record<string, any>[],
): Promise<Token> {
  if (!resources) {
    return request({
      method: 'POST',
      url: `${resourceType}/${resourceID}/token`,
    });
  }
  return request({
    method: 'POST',
    data: { resources },
    url: `${resourceType}/${resourceID}/token`,
  });
}

function update(resources: Record<string, any>[], tokenID: string): Promise<TempToken> {
  return request({
    method: 'PUT',
    data: { resources },
    url: `/token/${tokenID}`,
  });
}

function getRelateResource(tokenID: string): Promise<Resources> {
  return request({
    url: `/token/${tokenID}/related-resources`,
  }).then((res) => {
    return res?.items;
  });
}

function createAppTempToken(appID: string): Promise<TempToken> {
  return request({
    method: 'POST',
    needAuth: false,
    url: `/app/${appID}/temp-token`,
  });
}

export const TokenApi = {
  remove,
  list,
  update,
  create,
  getRelateResource,
  createAppTempToken,
};
