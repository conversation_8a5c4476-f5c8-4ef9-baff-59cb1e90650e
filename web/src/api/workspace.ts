import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Member,
  WorkspaceModel,
  WorkspaceUpdateModel,
} from '@interface/index';
import { request } from '@utils/request';

function list(): Promise<WorkspaceModel[]> {
  return request({
    url: '/workspace',
    params: {
      pageSize: 300
    }
  }).then((res) => {
    return res.items;
  });
}

function listNetease(): Promise<WorkspaceModel[]> {
  return request({
    url: '/workspace',
    params: {
      source: 'netease',
      pageSize: 300
    }
  }).then((res) => {
    return (res?.items || []).filter(v => v.description);
  });
}

function get(workspaceId: string): Promise<WorkspaceModel> {
  return request({
    url: `/workspace/${workspaceId}`,
  });
}

function create(data: { name: string; description: string }): Promise<WorkspaceModel> {
  return request({
    method: 'POST',
    url: `/workspace`,
    data,
  });
}

function update(data: WorkspaceUpdateModel, id: string): Promise<void> {
  return request({
    method: 'PUT',
    url: `/workspace/${id}`,
    data,
  });
}

function remove(id: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/workspace/${id}`,
  });
}

function getBasicApp(id: string): Promise<AppModel> {
  return request({
    url: `workspace/${id}/basic-app`,
  });
}

function getWorkspaceAdmin(id: string): Promise<Member[]> {
  return request({
    url: `workspace/${id}/admin`,
  }).then(res => {
    return res?.items || [];
  });
}

export const WorkspaceApi = {
  list,
  listNetease,
  get,
  create: (data: { name: string; description: string }) =>
    create(data) as unknown as Promise<void>,
  update,
  remove,
  getBasicApp,
  getWorkspaceAdmin,
  createWithResp: create,
} as CrudAPI<WorkspaceModel, WorkspaceUpdateModel> & {
  get: (workspaceId: string) => Promise<WorkspaceModel>;
  getBasicApp: (workspaceId: string) => Promise<AppModel>;
  getWorkspaceAdmin: (workspaceId: string) => Promise<Member[]>;
  listNetease: () => Promise<WorkspaceModel[]>;
  createWithResp: (data: {
    name: string;
    description: string;
  }) => Promise<WorkspaceModel>;
};
