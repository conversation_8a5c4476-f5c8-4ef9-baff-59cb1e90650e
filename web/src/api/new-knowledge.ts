import { IChunkStrategyModel } from '@/interface/new-knowledge';
import { request } from '@/utils/request';
import { QueryWithPage, ResponseValues } from '@/interface/common';
import {
  CreateKnowledgeModel,
  NewDocumentModel,
  KnowledgeType,
  NewKnowledgeModel,
  KnowledgeModel,
  DocumentListItemModal,
} from '@/interface/knowledge';
import { env } from '@/utils/common';

interface IDocProps {
  name?: string;
  type: string;
  description?: string;
  config: string;
  reviewId?: string;
  metaData?: string;
  importType?: string;
}

interface IAddDocProps {
  knowledgeId: number;
  workspaceId: string;
  groupId: string;
  propertyList?: any[];
  knowledgeItemDetailRequestList: IDocProps[];
}

function submitFile(data: { reviews: any[]; chunkStrategy: any }) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/submitFile',
    },
    method: 'POST',
    data,
    needAuth: false,
  });
}

function fileSlicePreview(data): Promise<Record<string, any>> {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/fileSlicePreview',
    },
    method: 'POST',
    data,
    needAuth: false,
  });
}

function addDoc(data: IAddDocProps) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeItem/add',
    },
    method: 'POST',
    data,
  });
}

/** 创建知识库 */
function createKnowledge(
  data: CreateKnowledgeModel,
): Promise<Pick<NewKnowledgeModel, 'id' | 'name' | 'description'>> {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledge/add',
    },
    method: 'POST',
    data,
    silence: true,
  });
}

/** 更新知识库 */
function updateKnowledge(data: NewKnowledgeModel): Promise<Record<string, any>> {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledge/update',
    },
    method: 'POST',
    data,
    silence: true,
  });
}

/** 查询知识库详情 */
function getKnowledgeDetail(params: { id: string }): Promise<KnowledgeModel> {
  //
  return request({
    proxyInfo: {
      url: `/langbase/chat/rag/knowledge/queryById`,
    },
    params,
  });
}

/** 查询知识库列表 */
function getKnowledgeList(
  data: QueryWithPage<{
    type: KnowledgeType | '';
    name: string;
    workspaceId: string;
    groupId: string;
    pageSize?: number;
  }>,
): Promise<ResponseValues<NewKnowledgeModel>> {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledge/query',
    },
    method: 'POST',
    data,
  });
}

/** 查询知识库文档列表 */
function getDocumentList(
  data: QueryWithPage<Pick<NewDocumentModel, 'knowledgeId'> & { name: string }>,
): Promise<ResponseValues<DocumentListItemModal>> {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeItem/query',
    },
    method: 'POST',
    data,
  });
}

/** 查询知识库文档详情 */
function getDocumentDetail(
  data: QueryWithPage<Pick<NewDocumentModel, 'knowledgeId' | 'knowledgeItemId'>>,
): Promise<{
  name: string;
  knowledgeFragmentData: ResponseValues<NewDocumentModel>;
  importType: string;
  config: string;
  knowledgeConfig: string;
  metaData: string;
  content: Record<string, any>;
}> {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeItem/queryDetail',
    },
    method: 'POST',
    data,
  });
}

/** 修改知识库文档 */
function updateDocumentInfo(data) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeItem/update',
    },
    method: 'POST',
    data,
  });
}

/** 删除知识库文档 */
function deleteDocument(data) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeItem/delete',
    },
    method: 'POST',
    data,
  });
}

/** 修改文档片段 */
function updateFragmentInfo(data: {
  knowledgeId: string; //知识库ID
  knowledgeItemId: string; // 知识库 item ID
  fragmentId: string; // 分段ID
  serialNum?: number; // 分段序号（可为空
  content?: string;
  extData?: Record<string, any>;
}) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeFragment/update',
    },
    method: 'POST',
    data,
  });
}

/** 删除文档片段 */
function deleteFragment(data: {
  knowledgeId: string;
  knowledgeItemId: string;
  fragmentId: string;
}) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeFragment/delete',
    },
    method: 'POST',
    data,
  });
}

function getSheet(data) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeItem/sheet',
    },
    method: 'POST',
    data,
  });
}

function previewSheet(data) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeItem/table/preview',
    },
    method: 'POST',
    data,
  });
}

function getDeliveryList(data) {
  return request({
    proxyInfo: {
      url: '/api/backend/content/delivery/scene/langbase/list',
      base:
        env === 'online' || env === 'pre'
          ? 'http://music.163.com'
          : 'http://qa.igame.163.com',
    },
    method: 'POST',
    data,
  });
}

function getCioHeader(data: { knowledgeId: string; groupId: string; properties: any[] }) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/cioHeaders/get',
    },
    method: 'POST',
    data,
  });
}

// 当前知识库下面的item
function getKnowledgeItemList(data: { knowledgeId: string }) {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledgeItem/list',
    },
    method: 'POST',
    data,
  });
}

/** 知识库检索接口的输入参数类型 */
export interface RecallRequestParams {
  searchType: string; // 检索类型，如 "MIX"
  filter: {
    limit: number; // 结果数量限制
    similarity: number; // 相似度阈值
  };
  queryRewrite?: {
    use: boolean; // 是否使用查询重写
    prompt: string; // 重写提示词
    modelParams?: Record<string, any>; // 模型参数
  };
  groupId: string; // 组ID
  knowledgeIds: number[]; // 知识库ID列表
  knowledgeItemIds?: number[]; // 知识库条目ID列表
  query: string; // 查询文本
}

/** 知识库检索接口的返回值类型 */
export interface RecallResponseData {
  type: string; // 返回类型，如 "text"
  results: Array<{
    knowledgeItemId: number; // 知识条目ID
    fragmentId: string; // 片段ID
    serialNum: number; // 序号
    content: string; // 内容
    extData: any; // 扩展数据
    knowledgeItemMetaData: string; // 元数据，包含URL等信息
    knowledgeItemInfo: any; //文档的基本信息
  }>;
  knowledgeConfig: any; // 知识库配置
}

/** 知识库检索接口 */
function recall(data: RecallRequestParams): Promise<RecallResponseData> {
  return request({
    proxyInfo: {
      url: '/langbase/chat/rag/knowledge/recall',
    },
    method: 'POST',
    data,
  });
}

// 保存git导入任务
function submitGitTask(data) {
  return request({
    // url: 'http://qa-aghan.igame.163.com/api/langbase/chat/rag/submitGitTask',
    proxyInfo: {
      url: '/api/langbase/chat/rag/submitGitTask',
      // base: 'http://qa-agama.igame.163.com'
    },
    method: 'POST',
    data,
  });
}

// 查看git导入list
function getTaskList(data) {
  return request({
    proxyInfo: {
      url: '/api/langbase/chat/rag/getTaskList',
      // base: 'http://qa-agama.igame.163.com'
    },
    // url: 'http://qa-aghan.igame.163.com/api/langbase/chat/rag/getTaskList',
    method: 'POST',
    data,
  });
}

// POPO导入
function getPopoCatalog(data) {
  return request({
    // url: 'http://qa-aghan.igame.163.com/api/langbase/chat/rag/getPOPOPreview',
    proxyInfo: {
      url: '/api/langbase/chat/rag/getPOPOPreview',
    },
    method: 'POST',
    data,
  });
}

// 提交POPO导入任务
function submitPopoTask(data) {
  return request({
    // url: 'http://qa-aghan.igame.163.com/api/langbase/chat/rag/submitPOPOTask',
    proxyInfo: {
      url: '/api/langbase/chat/rag/submitPOPOTask',
    },
    method: 'POST',
    data,
  });
}

function deleteTaskKnowledgeItems(data) {
  return request({
    proxyInfo: {
      url: '/api/langbase/chat/rag/deleteTask',
    },
    method: 'POST',
    data,
  });
}

interface ExportKnowledgeParams {
  knowledgeId: string;
  knowledgeItemId: string;
  type: string;
  pageSize?: number;
  pageNumber?: number;
}

function exportKnowledge(params: ExportKnowledgeParams): Promise<{ url: string }> {
  return request({
    method: 'POST',
    proxyInfo: {
      url: `/langbase/chat/rag/knowledgeItem/export`,
    },
    data: {
      id: params.knowledgeId,
      knowledgeItemId: params.knowledgeItemId,
      type: params.type,
      pageNum: params.pageNumber || 1,
      pageSize: params.pageSize || 100,
    },
  });
}

export const NewKnowledge = {
  exportKnowledge,
  submitFile,
  fileSlicePreview,
  addDoc,
  createKnowledge,
  getKnowledgeList,
  updateKnowledge,
  getDocumentList,
  getDocumentDetail,
  updateDocumentInfo,
  getKnowledgeDetail,
  deleteFragment,
  updateFragmentInfo,
  deleteDocument,
  getSheet,
  previewSheet,
  getDeliveryList,
  getCioHeader,
  getKnowledgeItemList,
  getTaskList,
  submitGitTask,
  recall,
  getPopoCatalog,
  submitPopoTask,
  deleteTaskKnowledgeItems,
};
