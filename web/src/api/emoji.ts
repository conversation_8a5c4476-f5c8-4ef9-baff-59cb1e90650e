import { request } from "@/utils/request";


interface IEmoji {
  appId?: string,
  id?:number;
  name:string;
  url:string;
  styles:string[];
  emotions:string[];
  desc:string;
  textContent:string;
}

interface ISearchParams {
  pageNum:number;
  pageSize:number;
  styles?:string[];
  emotions?:string[];
  appId?: string;
}

function queryList(data:ISearchParams) {
  return request({
    method: 'POST',
    proxyInfo: {
      url: '/langbase/virtual/sticker/asset/query'
    },
    data
  }); 
}

function getAllStyle () {
  return request({
    method: 'GET',
    proxyInfo: {
      url: '/langbase/virtual/sticker/asset/all/style/emotion'
    },
  });
};

function recognize(data: {url:string}){
  return request({
    method: 'GET',
    proxyInfo: {
      url: '/langbase/virtual/sticker/asset/recognize'
    },
    params:data,
  });
};

function editEmoji(data:IEmoji) {
  return request({
    method: 'POST',
    proxyInfo: {
      url: '/langbase/virtual/sticker/asset/addOrUpdate'
    },
    data,
  });
};

function deleteEmoji(data: {id:number, appId: string, operator:string}) {
  return request({
    method:'GET',
    proxyInfo: {
      url: '/langbase/virtual/sticker/asset/delete'
    },
    params:data
  });
}

function getAccount(data:{userId:string}) {
  return request({
    // url:'/proxy/account-info',
    proxyInfo: {
      url: '/langbase/virtual/music/account/get'
    },
    params:data
  })
}

function queryByIds(data: {
  appId:string,
  ids: string[]
}) {

  return request({
    // url: `/app/${data.appId}/emotion-query-by-ids`,
    method:'POST',
    proxyInfo: {
      url: '/langbase/virtual/sticker/asset/ids/query'
    },
    data: data
  });
}

export const EmojiApi = {
  getAllStyle,
  editEmoji,
  recognize,
  deleteEmoji,
  queryList,
  getAccount,
  queryByIds
}