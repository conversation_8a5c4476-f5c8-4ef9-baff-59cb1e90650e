import { request } from '@utils/request';

/** 获取循环动画列表 */
function getCycleList(data) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/animation/cycle/query',
    },
    method: 'POST',
    data,
  });
}

/** 获取标签列表 */
function getTagList(params) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/animation/tag/all',
    },
    method: 'GET',
    params,
  });
}

/** 获取首尾帧形象列表 */
function getHeadPortraitList(params) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/animation/head/all',
    },
    method: 'GET',
    params,
  });
}

/** 获取首尾帧形象列表 */
function getAssetInfoList(params) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/animation/name/all',
    },
    method: 'GET',
    params,
  });
}

/** 获取过渡动画列表 */
function getTransitionalList(data) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/animation/transitional/query',
    },
    method: 'POST',
    data,
  });
}

/** 获取首帧形象图列表 */
function getFirstFrameList(data: { pageNum: number; pageSize: number; type: string }) {
  //   /langbase/virtual/portrait/ordinary/query
}

/** 修改动画基本信息 */
function updateAnimationInfo(data) {
  return request({
    url: `/post-common-proxy`,
    proxyInfo: {
      url: '/langbase/virtual/animation/base/update',
    },
    method: 'POST',
    data,
  });
}

/** 生成帧图片 */
function createFrameImage(data) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/portrait/generate',
    },
    method: 'POST',
    data,
  });
}

/** 查看帧图片的生成记录 */
function getFrameHistoryList(data) {
  // /langbase/virtual/asset/task/query
  return request({
    proxyInfo: {
      url: '/langbase/virtual/asset/task/query',
    },
    method: 'POST',
    data,
  });
}

/** 生成动画 */
function createAnimation(data) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/animation/video/generate',
    },
    method: 'POST',
    data,
  });
}

/** 动画生成记录 */
function getCreateFrameRecordList(data) {
  return request({
    url: `/post-common-proxy`,
    proxyInfo: {
      url: '/langbase/virtual/asset/task/page/query',
    },
    method: 'POST',
    data,
  });
}

/** 动画重新生成 */
function recreateAnimation() {
  //   /langbase/virtual/animation/video/regenerate
}

/** 一键生成 */
function autoCreateAnimation(data) {
  //   /langbase/virtual/animation/video/generate/auto
  return request({
    url: `/post-common-proxy`,
    proxyInfo: {
      url: '/langbase/virtual/animation/video/generate/auto',
    },
    // baseURL: '/animation',
    // url: '/langbase/virtual/animation/video/generate/auto',
    method: 'POST',
    data,
  });
}

/** 保存动画 */
function saveAnimation(data) {
  //   /langbase/virtual/animation/saveOrUpdate
  return request({
    url: `/post-common-proxy`,
    proxyInfo: {
      url: '/langbase/virtual/animation/saveOrUpdate',
    },
    // baseURL: '/animation',
    // url: 'https://qa.igame.163.com/langbase/virtual/animation/saveOrUpdate',
    method: 'POST',
    data,
  });
}

// 预览动画
function previewAnimation(data) {
  return request({
    url: `/post-common-proxy`,
    proxyInfo: {
      url: '/langbase/virtual/animation/workflow/preview',
    },
    // baseURL: '/animation',
    // url: '/langbase/virtual/animation/workflow/preview',
    method: 'POST',
    data,
  });
}

// 获取循环动画首帧
function getAllHead(params) {
  return request({
    url: `/app/${params?.appId}/get-common-proxy`,
    proxyInfo: {
      url: '/langbase/virtual/animation/head/all',
    },
    params,
  });
}

function previewPartAnimation(params) {
  return request({
    url: `/app/${params?.appId}/get-common-proxy`,
    proxyInfo: {
      url: `/langbase/virtual/animation/version/preview`,
    },
    params,
  });
}

function getWorkflowDetail(params) {
  return request({
    url: `/get-common-proxy`,
    proxyInfo: {
      url: `/langbase/virtual/animation/workflow/get`
    },
    method: 'GET',
    params
  });
}

// 上传分时动画
function updateAnimationVersion(data) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/animation/version/update',
    },
    method: 'POST',
    data,
  });
}

// 设置默认版本
function updateAnimationMainVersion(data) {
  return request({
    proxyInfo: {
      url: '/langbase/virtual/animation/version/main/update',
    },
    method: 'POST',
    data,
  });
}

export const AnimationApi = {
  getCycleList,
  getTagList,
  getHeadPortraitList,
  getAssetInfoList,
  getTransitionalList,
  getFirstFrameList,
  updateAnimationInfo,
  createFrameImage,
  getFrameHistoryList,
  createAnimation,
  getCreateFrameRecordList,
  recreateAnimation,
  autoCreateAnimation,
  saveAnimation,
  previewAnimation,
  getAllHead,
  previewPartAnimation,
  getWorkflowDetail,
  updateAnimationVersion,
  updateAnimationMainVersion
};
