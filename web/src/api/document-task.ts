import { ResponseItems } from '@/interface/common';

import { CrudAPI, DocumentTaskModel, DocumentTaskUpdateModel } from '../interface';
import { request } from '../utils/request';

function create(
  data: DocumentTaskUpdateModel,
  workspaceId: string,
): Promise<void> {
  return request({
    method: 'POST',
    url: `/workspace/${workspaceId}/document-task?collectionId=${data.collectionId}`,
    data,
  });
}

function listWithPage(
  workspaceId: string,
  knowledgeId: string,
  pageNumber: number = 1,
  pageSize: number = 12,
): Promise<ResponseItems<DocumentTaskModel>> {
  return request({
    url: `/workspace/${workspaceId}/document-task`,
    params: {
      knowledgeId,
      pageNumber,
      pageSize
    },
  });
}

function list(workspaceId: string, knowledgeId?: string, keyword?: string): Promise<DocumentTaskModel[]> {
  return request({
    url: `/workspace/${workspaceId}/document-task`,
    params: {
      knowledgeId,
      keyword,
    }
  }).then((res) => {
    return res.items;
  });
}

function get(documentId: string): Promise<DocumentTaskModel> {
  return request({
    url: `/document-task/${documentId}`,
  });
}

function update(data: DocumentTaskUpdateModel, documentId: string): Promise<void> {
  return request({
    method: 'PUT',
    url: `/document-task/${documentId}`,
    data,
  });
}

function remove(documentId: string, collectionId: number, taskId?: number): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/document-task/${documentId}`,
    params: {
      collectionId,
      taskId,
    }
  });
}

export const DocumentTaskApi = {
  listWithPage,
  list,
  get,
  create,
  update,
  remove,
}
