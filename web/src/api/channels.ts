import { request } from '../utils/request';

function getChannels(data) {
  return request({
    method: 'POST',
    needAuth: false,
    // url: `/post-common-proxy`,
    proxyInfo: {
      url: '/langbase/inner/setting/channel/search'
    },
    // url: 'http://qa-algin.igame.163.com/langbase/inner/publish/channels',
    data,
  });
}

function getChannelSkills(data) {
  return request({
    method: 'POST',
    // url: 'http://qa-algin.igame.163.com/langbase/inner/setting/available/search',
    proxyInfo: {
      url: '/langbase/inner/channel/function/search'
    },
    data,
  });
}

export const ChannelsApi = {
  getChannels,
  getChannelSkills
};
