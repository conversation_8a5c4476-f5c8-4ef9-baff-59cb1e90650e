import { request } from '../utils/request';
import { env } from '@/utils/common';

const baseUrl = env === 'test' ? 'http://qa.igame.163.com' : 'https://music.163.com';

// 内存列表请求参数接口
interface ListMemoryParams {
  appId: string;      // 应用ID
  settingId: string;  // 设置ID
  userId: string;     // 用户ID
  offset: number;     // 分页偏移量
  limit: number;      // 每页限制数量
}

// 清除内存请求参数接口
interface ClearMemoryParams {
  appId?: string;     // 应用ID（可选）
  settingId?: string; // 设置ID（可选）
  userId?: string;    // 用户ID（可选）
  channel?: string;   // 渠道（可选）
}

// 通用post代理请求
// 默认权限，无权限限制，登陆即可，适用通用接口不与应用绑定
function listMemory(data: ListMemoryParams): Promise<any> {
  return request({
    method: 'POST',
    proxyInfo: {
      url: `/langbase/inner/long/term/memory/list`, // 原始地址
    },
    data,
  });
}

// 清除内存接口
// 默认权限，无权限限制，登陆即可，适用通用接口不与应用绑定
function clearMemory(data: ClearMemoryParams): Promise<any> {
  return request({
    method: 'POST',
    proxyInfo: {
      url: `/langbase/inner/long/term/memory/clear`, // 原始地址
    },
    data,
  });
}

export const memoryApi = {
  listMemory,
  clearMemory,
};
