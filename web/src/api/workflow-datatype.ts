import { Crud<PERSON><PERSON> } from '@interface/index';
import { request } from '@utils/request';

interface IUpdateDataType {
  name: string;
  config: Record<string, any>;
  description: string;
}

interface IDataType {
  id: string;
  name: string;
  config: Record<string, any>;
  description: string;
}

/**
 * 获取所有dataType
 * @returns 
 */
function list(pageSize?: number, pageNumber?: number): Promise<IDataType[]> {
  return request({
    url: '/workflow-datatypes',
    data: { pageNumber, pageSize }
  }).then((res) => {
    return res.items;
  });
}

/**
 * 获取 dataType
 * @param workflowDataTypeID 
 * @returns 
 */
function get(workflowDataTypeID: string): Promise<IDataType> {
  return request({
    url: `/workflow-datatypes/${workflowDataTypeID}`
  })
}


/**
 * 创建DateType
 * @param data 
 * @returns 
 */
function create(data: IUpdateDataType): Promise<void> {
  return request({
    url: '/workflow-datatypes',
    method: 'POST',
    data,
  }).then((res) => {
    return res.items;
  });
}

/**
 * 更新 dataType
 * @param workflowDataTypeID 
 * @returns 
 */
function update(data: IUpdateDataType, workflowDataTypeID: string): Promise<void> {
  return request({
    method: 'PUT',
    url: `/workflow-datatypes/${workflowDataTypeID}`,
    data
  })
}

/**
 * 删除dataType
 * @param workflowDataTypeID 
 * @returns 
 */
function remove(workflowDataTypeID: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/workflow-datatypes/${workflowDataTypeID}`
  })
}


export const WorkflowDataTypeApi = {
  list,
  get,
  create,
  update,
  remove
} as CrudAPI<IDataType, IUpdateDataType>;
