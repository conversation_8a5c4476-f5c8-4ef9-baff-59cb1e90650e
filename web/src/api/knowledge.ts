import { ResponseItems } from '@/interface/common';

import { CrudAPI, KnowledgeModel, KnowledgeUpdateModel } from '../interface';
import { request } from '../utils/request';

function create(
  data: { name: string; description: string, groupId: string, workspaceId: string },
  groupId: string,
): Promise<void> {
  return request({
    method: 'POST',
    url: `/group/${groupId}/knowledge`,
    data,
  });
}

function listWithPage(
  workspaceId?: string,
  groupId?: string,
  keyword?: string,
  pageNumber: number = 1,
  pageSize: number = 12,
): Promise<ResponseItems<KnowledgeModel>> {
  return request({
    url: `/group/${groupId}/knowledge`,
    params: {
      pageNumber,
      pageSize,
      keyword,
      workspaceId,
      groupId,
    },
  });
}

function list(workspaceId?: string, groupId?: string, keyword?: string): Promise<KnowledgeModel[]> {
  return request({
    url: `/group/${groupId}/knowledge`,
    params: {
      workspaceId,
      groupId,
      keyword,
      pageNumber: 1,
      pageSize: 100,
    }
  }).then((res) => {
    return res.items;
  });
}

function get(knowledgeId: string): Promise<KnowledgeModel> {
  return request({
    url: `/knowledge/${knowledgeId}`,
  });
}

function update(data: KnowledgeUpdateModel, knowledgeId: string): Promise<void> {
  return request({
    method: 'PUT',
    url: `/knowledge/${knowledgeId}`,
    data,
  });
}

function remove(knowledgeId: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/knowledge/${knowledgeId}`,
  });
}


export const KnowledgeApi = {
  listWithPage,
  list,
  get,
  create,
  update,
  remove,
} as CrudAPI<KnowledgeModel, KnowledgeUpdateModel> & {
  listWithPage: {
    (
      workspaceId: string,
      groupId?: string,
      keyword?: string,
      pageNumber?: number,
      pageSize?: number,
    ): Promise<ResponseItems<KnowledgeModel>>;
  };
};
