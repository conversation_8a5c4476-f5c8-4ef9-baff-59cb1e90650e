import { request } from '../utils/request';

export function getPmsFromCookie(): Promise<{pms_u: string}> {
  return request({
    method: 'GET',
    url: '/pmsinfo',
    params: {},
  });
}

export function pmsInfo(): Promise<{login: boolean}> {
  return request({
    method: 'POST',
    url: '/api/pms/login/status',
    data: {},
    baseURL: '/',
  })
}

export function getPmsLoginUrl(url): Promise<string> {
  return request({
    method: 'GET',
    url: '/api/pms/login',
    params: {
      fromHost: window.location.host,
      redirectUrl: window.location.pathname + window.location.search
    },
    baseURL: '/',
  })
}

export default {};
