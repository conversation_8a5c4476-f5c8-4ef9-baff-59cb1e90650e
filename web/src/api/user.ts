import { groupBy } from 'lodash';

import { ResIdMap, UserModel, UserRoleModel, UserRoles } from '../interface';
import { request } from '../utils/request';

function userInfo(): Promise<UserModel> {
  return request({
    needAuth: false,
    url: '/userinfo',
  });
}

function userRoles(): Promise<UserRoles> {
  return request({
    url: '/userroles',
  }).then((res: UserRoleModel[]) => {
    const addResId = res.map((item: UserRoleModel) => {
      return {
        ...item,
        // 添加资源类型对应的 appId、groupId、workspaceId
        [ResIdMap[item.resourceType]]: item.resourceID,
      };
    });
    const grouped = groupBy(addResId, 'resourceType');
    return {
      ...grouped,
    } as unknown as UserRoles;
  });
}

/**
 * 获取特定资源的权限
 * @param type
 * @param id
 * @returns
 */
function getResourceRole(type: string, id: any): Promise<any> {
  return request({
    url: `/userroles?scopeType=${type}&scopeID=${id}`,
    cache: () => true
  }).then((res: UserRoleModel[]) => {
    return res[0] || { role: 'guest' };
  });
}

function logout(): Promise<void> {
  return request({
    needAuth: false,
    url: '/logout',
  });
}

export const UserApi = {
  userInfo,
  logout,
  getResourceRole,
  userRoles,
};
