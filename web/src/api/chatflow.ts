
import { request } from '@/utils/request';

type WorkflowListResponse = {
  workflowList: any[];
  total: number;
}

type NodeDetailResponse = {
  nodeList: any[];
}

/** 查询对应 chatflow 的节点列表 */
function getWorkflowList(appId: string, page: number, pageSize: number, params: {
  startTime?: string;
  endTime?: string;
  userId?: string;
  chatId?: string;
  requestId?: string;
}): Promise<WorkflowListResponse> {
  return request({
    proxyInfo: {
      url: '/api/langbase/workflow/queryWorkflows',
    },
    method: 'POST',
    data: {
      appId,
      page: page || 1,
      pageSize: pageSize || 20,
      ...params,
    },
  });
}

/** 查询对应节点的节点详情 */
function getNodeDetail(requestId: string): Promise<NodeDetailResponse> {
  return request({
    proxyInfo: {
      url: '/api/langbase/workflow/queryNode',
    },
    method: 'POST',
    data: {
      requestId
    },
  });
}

export const ChatflowAPI = {
  getWorkflowList,
  getNodeDetail,
};
