import { createCmdConfig } from '@antv/xflow';
import { request } from '@/utils/request';

export interface AigwApp {
  id: string;
  app_code: string;
  resource_type: 'Workspace' | 'Group' | 'App';
  resource_id: string;
  quota: string;
  token: string;
  granted_by: string;
  created_at: string;
  updated_at: string;
  extra?: {
    app_id: string;
    app_key: string;
    authorization_header: string;
    credit: {
      quota?: number;
      usage?: number;
      balance?: number;
    };
  };
}

export interface CreateAigwAppParams {
  app_code: string;
  name: string;
}

export interface UpdateAigwAppParams {
  app_code: string;
  name: string;
}

// 根据resourceId和resourceType 创建aigw应用
export const createAigwApp = (
  resourceType: 'Workspace' | 'Group' | 'App',
  resourceId: string,
  params: CreateAigwAppParams
): Promise<AigwApp> => {
  switch (resourceType) {
    case 'Workspace':
      return createWorkspaceAigwApp(resourceId, params);
    case 'Group':
      return createGroupAigwApp(resourceId, params);
    default:
      return Promise.reject(new Error('不支持的资源类型'));
  }
};

// Workspace AIGW APIs
export const createWorkspaceAigwApp = (workspaceId: string, params: CreateAigwAppParams): Promise<AigwApp> => {
  return request({
    url: `/workspace/${workspaceId}/aigw_app`,
    method: 'POST',
    data: params,
  });
};

export const getWorkspaceAigwApp = (workspaceId: string, aigwId: number): Promise<AigwApp> => {
  return request({
    url: `/workspace/${workspaceId}/aigw_app/${aigwId}`,
    method: 'GET',
  });
};

export const listWorkspaceAigwApp = (
  workspaceId: string,
  params?: {
    token?: string;
    limit?: number;
    offset?: number;
  }
): Promise<AigwApp> => {
  return request({
    url: `/workspace/${workspaceId}/aigw_app`,
    method: 'GET',
    params,
  }).then((res) => {
    if (res?.data === null) {
      return undefined;
    }
    return res;
  });
};

export const listWorkspaceAigwApps = (
  workspaceId: string,
  params?: {
    token?: string;
    limit?: number;
    offset?: number;
  }
): Promise<AigwApp[]> => {
  return request({
    url: `/workspace/${workspaceId}/aigw_apps`,
    method: 'GET',
    params,
  }).then((res) => {
    return res.items;
  });
};

export const updateWorkspaceAigwApp = (
  workspaceId: string,
  aigwId: number,
  params: UpdateAigwAppParams
): Promise<AigwApp> => {
  return request({
    url: `/workspace/${workspaceId}/aigw_app/${aigwId}`,
    method: 'PUT',
    data: params,
  });
};


export const deleteWorkspaceAigwApp = (workspaceId: string, aigwId: number): Promise<{ message: string }> => {
  return request({
    url: `/workspace/${workspaceId}/aigw_app/${aigwId}`,
    method: 'DELETE',
  });
};

// Group AIGW APIs
export const createGroupAigwApp = (groupId: string, params: CreateAigwAppParams): Promise<AigwApp> => {
  return request({
    url: `/group/${groupId}/aigw_app`,
    method: 'POST',
    data: params,
  });
};

export const getGroupAigwApp = (groupId: string, aigwId: number): Promise<AigwApp> => {
  return request({
    url: `/group/${groupId}/aigw_app/${aigwId}`,
    method: 'GET',
  });
};

export const listGroupAigwApp = (
  groupId: string,
  params?: {
    token?: string;
    limit?: number;
    offset?: number;
  }
): Promise<AigwApp> => {
  return request({
    url: `/group/${groupId}/aigw_app`,
    method: 'GET',
    params,
  }).then((res) => {
    if (res?.data === null) {
      return undefined;
    }
    return res;
  });
};

export const listGroupAigwApps = (
  groupId: string,
  params?: {
    token?: string;
    limit?: number;
    offset?: number;
  }
): Promise<[AigwApp[], number]> => {
  return request({
    url: `/group/${groupId}/aigw_apps`,
    method: 'GET',
    params,
  });
};

export const updateGroupAigwApp = (
  groupId: string,
  aigwId: number,
  params: UpdateAigwAppParams
): Promise<AigwApp> => {
  return request({
    url: `/group/${groupId}/aigw_app/${aigwId}`,
    method: 'PUT',
    data: params,
  });
};

export const deleteGroupAigwApp = (groupId: string, aigwId: number): Promise<{ message: string }> => {
  return request({
    url: `/group/${groupId}/aigw_app/${aigwId}`,
    method: 'DELETE',
  });
};

export const AigwApi ={
  createWorkspaceAigwApp,
  getWorkspaceAigwApp,
  listWorkspaceAigwApps,
  updateWorkspaceAigwApp,
  deleteWorkspaceAigwApp,
};

// 添加绑定现有账号的接口
export interface BindExistingAccountParams {
  app_code: string;
  token: string;
}

export const bindExistingAccount = (
  resourceType: 'Workspace' | 'Group' | 'App',
  resourceId: string,
  params: BindExistingAccountParams
): Promise<AigwApp> => {
  const url = resourceType === 'Workspace' 
    ? `/workspace/${resourceId}/aigw_bind`
    : `/group/${resourceId}/aigw_bind`;
    
  return request({
    url,
    method: 'POST',
    data: params,
  });
};

// 使用示例:
/*
// 创建工作空间的 AIGW 应用
const createApp = async () => {
  const response = await createWorkspaceAigwApp('workspace-id', {
    app_code: 'test-app',
    token: 'user-token'
  });
  console.log(response.extra?.authorization_header);
};

// 列出分组的 AIGW 应用
const listApps = async () => {
  const [apps, total] = await listGroupAigwApps('group-id', {
    limit: 10,
    offset: 0
  });
  console.log(`Total apps: ${total}`);
  apps.forEach(app => {
    console.log(app.app_code);
  });
};
*/
