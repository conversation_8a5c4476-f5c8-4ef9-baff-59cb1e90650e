import { request } from "@/utils/request";

// Mock数据
const mockVoiceList = [
  {
    voiceId: "minimaxaudiobook_female_1",
    providerKind: "minimax",
    voiceName: "网文小美",
    tag: ["女", "普通话", "青年", "温柔"],
    voiceUrl: "https://m7.music.126.net/20930317002538/f681b22489e99c0495a67375b9bf2546/ymusic/obj/w5zDlMODwrDDiGjCn8Ky/58404408823/9ae9/2ed1/5836/c6571f9e471470229b0122f3d0146db8.mp3?infoId=2612161"
  },
  {
    voiceId: "minimaxaudiobook_female_2",
    providerKind: "minimax",
    voiceName: "语文老师",
    tag: ["女", "普通话", "中年", "稳重"],
    voiceUrl: "https://m7.music.126.net/20930317002538/f681b22489e99c0495a67375b9bf2546/ymusic/obj/w5zDlMODwrDDiGjCn8Ky/58404408823/9ae9/2ed1/5836/c6571f9e471470229b0122f3d0146db8.mp3?infoId=2612161"
  },
  {
    voiceId: "minimaxaudiobook_male_1",
    providerKind: "minimax",
    voiceName: "网文小帅",
    tag: ["男", "普通话", "青年", "阳光"],
    voiceUrl: "https://nos.netease.com/cloud-website-bucket/202403041658045e5c0c9e-f914-4a85-9279-d0c0c9e587b1.mp3"
  },
  {
    voiceId: "minimaxBingjiao_zongcai_platform",
    providerKind: "minimax",
    voiceName: "腹黑男",
    tag: ["男", "普通话", "青年", "稳重"],
    voiceUrl: "https://nos.netease.com/cloud-website-bucket/202403041658045e5c0c9e-f914-4a85-9279-d0c0c9e587b1.mp3"
  },
  {
    "voiceId": "Cuteboy_platform",
    "providerKind": "minimax",
    "voiceName": "可爱萌娃",
    "tag": [
      "男",
      "少年",
      "普通话",
      "可爱"
    ],
    "voiceNos": "jdymusic/0542e75c8b5da882c83cf3bd6c6132281740565030408.mp3",
    "voiceUrl": "https://m7.music.126.net/20930317002538/f681b22489e99c0495a67375b9bf2546/ymusic/obj/w5zDlMODwrDDiGjCn8Ky/58404408823/9ae9/2ed1/5836/c6571f9e471470229b0122f3d0146db8.mp3?infoId=2612161"
  },
  {
    voiceId: "Female_platform",
    providerKind: "minimax",
    voiceName: "温柔女声",
    tag: ["女", "普通话", "青年", "温柔"],
    voiceUrl: "https://nos.netease.com/cloud-website-bucket/202403041658045e5c0c9e-f914-4a85-9279-d0c0c9e587b1.mp3"
  },
  {
    voiceId: "Male_platform",
    providerKind: "minimax",
    voiceName: "标准男声",
    tag: ["男", "普通话", "中年", "稳重"],
    voiceUrl: "https://nos.netease.com/cloud-website-bucket/202403041658045e5c0c9e-f914-4a85-9279-d0c0c9e587b1.mp3"
  }
];

function getVoiceList(data) {
  // 使用mock数据
  // return Promise.resolve(mockVoiceList);

  // 实际API调用（暂时注释掉）
  return request({
    proxyInfo: {
      url: '/langbase/inner/timber/get'
    },
  });
}

export const TtsApi = {
  getVoiceList,
  mockVoiceList
}; 