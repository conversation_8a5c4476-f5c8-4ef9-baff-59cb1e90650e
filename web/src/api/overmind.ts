import { request } from "@/utils/request";

interface ApproveCheckParams {
  appName: string;
  action: string;
  appType: string;
  operator: string;
}

interface RuleDetail {
  id: number;
  ruleType: string;
  action: string;
  scope: string;
  platform: string | null;
  title: string;
  description: string;
  startTime: string | null;
  endTime: string | null;
  appTypes: string[];
  priorities: string[] | null;
  appNames: string[] | null;
  whiteListAppNames: string[] | null;
  whiteListTeamNames: string[] | null;
  teams: string[] | null;
  approvers: string[];
  justStartTime: string;
  justEndTime: string;
}

interface ApproveCheckResult {
  hitWindow: boolean;
  message: string;
  approvers: string[];
  approverNames: string[];
  detail: RuleDetail;
  hitForbidden: boolean;
  forbiddenMessage: string;
  forbiddenDetail: RuleDetail;
}

interface ApproveCheckResponse {
  result: ApproveCheckResult;
}

function approveCheck(data: ApproveCheckParams): Promise<ApproveCheckResponse> {
  return request({
    url: `/overmind/approve_check`,
    method: 'POST',
    data: {
      ...data,
      platform: 'LANGBASE',
    },
  });
}

export const OvermindApi = {
  approveCheck,
};

export type {
  ApproveCheckParams,
  ApproveCheckResponse,
  ApproveCheckResult,
  RuleDetail,
}; 