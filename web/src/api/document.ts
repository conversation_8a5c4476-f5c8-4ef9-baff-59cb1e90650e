import { ResponseItems } from '@/interface/common';

import { DocumentModel, DocumentSegmentModel } from '../interface';
import { request } from '../utils/request';

function create(
  data,
  workspaceId: string,
): Promise<void> {
  return request({
    method: 'POST',
    url: `/workspace/${workspaceId}/document`,
    data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

function listWithPage(
  workspaceId: string,
  knowledgeId: string,
  collectionId: number,
  pageNumber: number = 1,
  pageSize: number = 12,
): Promise<ResponseItems<DocumentModel>> {
  return request({
    url: `/workspace/${workspaceId}/document`,
    params: {
      knowledgeId,
      pageNumber,
      pageSize,
      collectionId,
    },
  });
}

function list(workspaceId: string, knowledgeId?: string, keyword?: string): Promise<DocumentModel[]> {
  return request({
    url: `/workspace/${workspaceId}/document`,
    params: {
      knowledgeId,
      keyword,
    }
  }).then((res) => {
    return res.items;
  });
}

function get(documentId: string): Promise<DocumentModel> {
  return request({
    url: `/document/${documentId}`,
  });
}

function update(data: any, documentId: string): Promise<void> {
  return request({
    method: 'PUT',
    url: `/document/${documentId}`,
    data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

function remove(documentId: string, collectionId: number, docId?: number): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/document/${documentId}`,
    params: {
      collectionId,
      docId,
    }
  });
}

function getSegment(documentId: string, collectionId: number,  docId: number): Promise<DocumentSegmentModel[]> {
  return request({
    url: `/document/${documentId}/segment`,
    params: {
      collectionId,
      docId,
    }
  });
}

export const DocumentApi = {
  listWithPage,
  list,
  get,
  create,
  update,
  remove,
  getSegment,
};
