// @ts-nocheck
import { request } from '../utils/request';

interface AtomicType {
  createTime: string;
  name: string;
  platformDesc: {
    createTime: string;
    name: string;
    updateTime: string;
    id: number;
    operator: string;
    desc: string;
    status: string;
  };
  updateTime: string;
  id: number;
  tag: string;
  platformName: string;
  operator: string;
  desc: string;
  status: string;
}

interface ListType {
  cursor: string;
  hasTotalSize: boolean;
  totalSize: number;
  offset: number;
  totalPage: number;
  limit: number;
  hasNext: boolean;
  list: AtomicType[];
}

interface ParamsType {
  id: number;
  fieldType: 'Number' | 'Boolean' | 'String' | 'Object' | 'Array_Number' | 'Array_Boolean' | 'Array_String' | 'Array_Object';
  name: string;
  desc: string;
  value: string;
  required: boolean;
  ruleType: 'NONE' | 'NotNull' | 'Range' | 'Enum' | 'RegularExpression';
  ruleValue: string;
  view: number;
  subObjectList: ParamsType[];
}

interface AtomicDetailType {
  id: number;
  platformName: string;
  needInput: number;
  name: string;
  desc: string;
  tag: string;
  status: string;
  inputParams: ParamsType[];
  outputParams: ParamsType[];
  specialSubCode: {
    code: string;
    subCode: string;
    remark: string;
  };
  invokeTopic: string;
}

const online = window.location.host === 'langbase.netease.com';
const baseURL = online ? 'https://music.163.com' : 'https://cms.qa-altai.igame.163.com';

function getList(data: { limit: number; offset: number, name?: string, platformName?: string, tag?: string }): Promise<ListType> {
  return request({
    method: 'GET',
    // baseURL,
    // url: `/api/skyeye/platform/atomic/service/list`,
    url: '/proxy/skyeye/atomic',
    params: {
      ...data,
      status: 'ACTIVE'
    }
  }).then(res => {
    return res?.list;
  });
}

function getPlatformList(data: { limit: number; offset: number, name?: string }): Promise<ListType> {
  return request({
    method: 'GET',
    url: '/proxy/skyeye/platform',
    params: data
  }).then(res => {
    return res?.list;
  });
}

function getDetail(data: {atomicServiceId: number}): Promise<AtomicDetailType> {
  return request({
    method: 'GET',
    // baseURL,
    // url: `/api/skyeye/platform/atomic/service/detail`,
    url: '/proxy/skyeye/detail',
    params: data
  }).then(res => {
    return res;
  });
}

export const SkyeyeApi ={
  getList,
  getPlatformList,
  getDetail,
};
