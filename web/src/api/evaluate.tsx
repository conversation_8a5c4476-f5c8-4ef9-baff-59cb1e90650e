import { request } from '@/utils/request';

const addIndicator = (data) => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/indicator/add'
    },
    method: 'POST',
    data
  });
};

const updateIndicator = data => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/indicator/update',
    },
    method: 'POST',
    data
  });
};

const getIndicators = (data) => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/indicator/query'
    },
    method: 'POST',
    data
  });
};

const getIndicatorDetail = (data) => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/indicator/metric/info/get'
    },
    method: 'POST',
    data
  });
};

const getModels = () => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/indicator/model/get'
    },
  });
};

const getTaskList = (data) => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/task/query'
    },
    method: 'POST',
    data
  });
};

const createTask = data => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/task/create'
    },
    method: 'POST',
    data
  });
};

const getTaskReport = data => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/task/report'
    },
    method: 'POST',
    data
  });
};

const createCollection = data => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/collection/add'
    },
    method: 'POST',
    data
  });
};

const addEvaluationDetailItem = data => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/evaluationSetItem/create'
    },
    method: 'POST',
    data
  });
};

const updateEvaluationDetailItem = data => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/evaluationSetItem/update'
    },
    method: 'POST',
    data
  });
};

const deleteEvaluationDetailItem = data => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/evaluationSetItem/delete'
    },
    method: 'POST',
    data
  });
};
const queryEvaluationItems = data => {
  return request({
    proxyInfo: {
      url: '/api/langbase/evaluation/evaluationSetItem/query'
    },
    method: 'POST',
    data
  });
};

const queryEvaluationDetail = data => {

  return request({
    method: 'GET',
    proxyInfo: {
      url: '/api/langbase/evaluation/collection/get' // 原始地址
    },
    params: data,
  });
};

const queryEvaluateTaskDetail = data => {
  return request({
    method: 'POST',
    proxyInfo: {
      url: '/api/langbase/evaluation/evaluationTaskRecord/query'
    },
    data
  });
};

const getTaskDetail = data => {
  return request({
    method: 'GET',
    // url: '/post-common-proxy',
    proxyInfo: {
      url: '/api/langbase/evaluation/task/get'
    },
    params: data
  });
};

const downloadEvaluateTask = data => {
  return request({
    method: 'POST',
    proxyInfo: {
      url: `/api/langbase/evaluation/evaluationTaskRecord/export`,
    },
    data: {
      evaluationTaskId: data?.evaluationTaskId
    }
  });
};

const retryByEvaluationTaskId = data => {
  return request({
    method: 'POST',
    // url:`http://langbase-workflow.yf-onlinetest4.netease.com/api/v1/proxy/api/langbase/evaluation/evaluationTaskRecord/retryByEvaluationTaskId?evaluationTaskId=${data?.evaluationTaskId}`,
    proxyInfo: {
      url: `/api/langbase/evaluation/evaluationTaskRecord/retryByEvaluationTaskId`,
    },
    data: {
      evaluationTaskId: data?.evaluationTaskId
    }
  });
}

const retryByEvaluationSubTaskId = data => {
  return request({
    method: 'POST',
    proxyInfo: {
      url: '/api/langbase/evaluation/evaluationTaskRecord/retryByEvaluationSubTaskId'
    },
    data: {
      evaluationSubTaskId: data?.evaluationSubTaskId,
      evaluationTaskId: data?.evaluationTaskId
    }
  });
};

export const EvaluateApi = {
  addIndicator,
  updateIndicator,
  getIndicators,
  getIndicatorDetail,
  getModels,
  getTaskList,
  createTask,
  getTaskReport,
  createCollection,
  addEvaluationDetailItem,
  updateEvaluationDetailItem,
  deleteEvaluationDetailItem,
  queryEvaluationItems,
  queryEvaluationDetail,
  queryEvaluateTaskDetail,
  getTaskDetail,
  downloadEvaluateTask,
  retryByEvaluationTaskId,
  retryByEvaluationSubTaskId
};