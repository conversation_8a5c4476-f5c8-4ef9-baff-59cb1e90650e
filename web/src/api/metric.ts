import { getWorkspaceId } from '@/utils/state';
import { Metric } from '../interface';
import { request } from '../utils/request';
import dayjs from 'dayjs';

interface MetricParams {
  start?: dayjs.Dayjs;
  end?: dayjs.Dayjs;
}

function getRequestRange(params?: MetricParams) {
  if (!params) return {};
  const { start, end } = params;

  return {
    start: start?.toISOString(),
    end: end?.toISOString(),
  };
}

// 废弃
function getConversationPerUser(appID: string, params?: MetricParams): Promise<Metric[]> {
  return request({
    url: `/app/${appID}/conversation-per-user`,
    params: getRequestRange(params),
  });
}

// 废弃
function getUserUsageCount(appID: string, params?: MetricParams): Promise<Metric[]> {
  return request({
    url: `/app/${appID}/user-usage-count`,
    params: getRequestRange(params),
  });
}

// 废弃
function getTokenPerSeconds(appID: string, params?: MetricParams): Promise<Metric[]> {
  return request({
    url: `/app/${appID}/token-per-seconds`,
    params: getRequestRange(params),
  });
}

// 废弃
function getTokenCount(appID: string, params?: MetricParams): Promise<Metric[]> {
  return request({
    url: `/app/${appID}/token-count`,
    params: getRequestRange(params),
  });
}

// 废弃
function getMessagePerConv(appID: string, params?: MetricParams): Promise<Metric[]> {
  return request({
    url: `/app/${appID}/message-per-conv`,
    params: getRequestRange(params),
  });
}

function getMetric(appID: string, params?: MetricParams): Promise<Metric[]> {
  return request({
    url: `/app/${appID}/metrics`,
    params: getRequestRange(params),
  });
}

function getAppMetrics(groupId: string, params?: MetricParams, sort?: any): Promise<Metric[]> {
  const workspaceId = getWorkspaceId();
  return request({
    url: `/metrics`,
    params: {
      workspaceId,
      groupId,
      ...(sort || {}),
      ...getRequestRange(params)
    }
  });
}


function getModelMetrics(groupId: string, params?: MetricParams, sort?: any): Promise<Metric[]> {
  const workspaceId = getWorkspaceId();
  return request({
    url: `/metrics-by-model`,
    params: {
      workspaceId,
      groupId,
      ...(sort || {}),
      ...getRequestRange(params)
    }
  });
}

export const MetricApi = {
  getAppMetrics,
  getConversationPerUser,
  getUserUsageCount,
  getTokenPerSeconds,
  getTokenCount,
  getMetric,
  getModelMetrics,
  getMessagePerConv,
};
