import dayjs from 'dayjs';
import Qs from 'qs';

import { ResponseItems } from '@/interface/common';
import { getAppId } from '@/utils/state';

import {
  AppCreateModel,
  AppDetailModel,
  AppModel,
  AppTemplate,
  AppUpdateModel,
  IAppType,
  IRunState,
  Pagination,
  Tool,
} from '../interface';
import { request } from '../utils/request';
import { TokenApi } from './token';
import { message } from 'antd';

const useMock = false;

function listAppsByGroup(
  groupId: string,
  params?: IListAppParams,
): Promise<{
  items: AppModel[];
  total: number;
}> {
  return request({
    url: `/group/${groupId}/app`,
    silence: true,
    params,
    paramsSerializer: (params) => {
      return Qs.stringify(params, { arrayFormat: 'repeat' });
    },
  }).then((res) => {
    return res;
  });
}

interface IListAppParams {
  subType?: string;
  appType?: IAppType[];
  name?: string;
  pageSize?: number;
  showAll?: boolean;
  pageNumber?: number;
  userID?: string;
  isTemplate?: boolean;
  // 暂时只有star
  useType?: string;
}

function listAppsByWorkspace(
  workspaceId: string,
  params?: IListAppParams,
): Promise<{
  items: AppModel[];
  total: number;
}> {
  return request({
    url: `/workspace/${workspaceId}/app`,
    params,
    paramsSerializer: (params) => {
      return Qs.stringify(params, { arrayFormat: 'repeat' });
    },
  }).then((res) => {
    return res;
  });
}

function createApp(data: AppCreateModel, groupId: string): Promise<AppDetailModel> {
  return request({
    method: 'POST',
    url: `/group/${groupId}/app`,
    data,
  });
}

function getAppDetail(appId: string): Promise<AppDetailModel> {
  return request({
    url: `/app/${appId}`,
  });
}

function getAppDetailNoAuth(appId: string): Promise<AppDetailModel> {
  return request({
    url: `/app/${appId}`,
    needAuth: false,
  });
}

/**
 * 获取应用发布历史
 * @param appId
 * @returns
 */
function getAppHistory(_appId?: string): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    useMock,
    url: `/app/${appId}/deploy-history`,
  }).then((res: any) => {
    return res.items;
  });
}

/**
 * 获取应用发布历史
 * @param appId
 * @returns
 */
function getAppHistoryWithPage(pageNumber?: number, pageSize?: number, _appId?: string): Promise<any> {
  const appId = _appId || getAppId();
  return request({
    useMock,
    url: `/app/${appId}/deploy-history`,
    params: { pageNumber: pageNumber || 1, pageSize: pageSize || 5 }
  });
}

/**
 * 更新app版本号
 * @param appId
 * @returns
 */
function updateAppVersion(versionId: string, _appId?: string): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    useMock,
    method: 'PUT',
    url: `/app/${appId}/config`,
    data: {
      version_id: versionId
    }
  })
}

/**
 * 获取 workflow 的配置保存记录
 * @param appId
 * @returns
 */
function getAppConfigSnapshot(_appId: string): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    useMock,
    url: `/app/${appId}/config-snapshot`,
  }).then((res: any) => {
    return res.items;
  });
}

function getAppConfigSnapshotWithPage(_appId: string, pageNumber?: number, pageSize?: number): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    useMock,
    url: `/app/${appId}/config-snapshot`,
    params: { pageNumber: pageNumber || 1, pageSize: pageSize || 5 }
  });
}

/**
 * 保存workflow配置
 * @param appId
 * @param config
 * @returns
 */
function saveAppConfigSnapshot(
  _appId: string,
  config: Record<string, any>,
): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    useMock,
    method: 'POST',
    url: `/app/${appId}/config-snapshot`,
    data: { config },
  });
}

/**
 * 复制workflow配置
 * @param appId
 * @param config
 * @returns
 */
function getAppConfig(
  configId: string,
): Promise<AppDetailModel> {
  return request({
    needAuth: false,
    url: `/app-config/${configId}`,
  });
}


/**
 * 保存workflow配置
 * @param appId
 * @param config
 * @returns
 */
function copyAppConfigSnapshot(
  configId: string,
  type: 'rollback' | 'switch',
): Promise<AppDetailModel> {
  return request({
    method: 'PUT',
    url: `/copy-config-snapshot/${configId}`,
    data: { type },
  });
}


/**
 * 部署app
 * @param appId
 * @param config
 * @returns
 */
function deployApp(_appId: string, config: Record<string, any>): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    useMock,
    method: 'POST',
    url: `/app/${appId}/deploy`,
    data: { config },
  });
}

interface ICallback {
  type: 'kafka' | 'nydus';
  info: Record<string, any>;
}

/**
 * 触发app workflow
 * @param appId
 * @param inputs
 * @param callback
 * @param bizContext
 * @returns
 */
async function chatApp(
  _appId: string,
  inputs: Record<string, any>,
): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    baseURL: 'http://10.221.77.8:8000/api/v1',
    needAuth: false,
    method: 'POST',
    url: `/app/${appId}/agentw-chat`,
    data: { appId, inputs, configID: 'fad0fd2e-9007-42cf-a28a-a6f77c6afe93' },
  });
}

/**
 * 触发app workflow
 * @param appId
 * @param inputs
 * @param callback
 * @param bizContext
 * @returns
 */
async function triggerApp(
  _appId: string,
  inputs: Record<string, any>,
  debug?: boolean,
  bizInfo?: Record<string, any>,
  callback?: ICallback,
): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    needAuth: false,
    useMock,
    method: 'POST',
    url: `/app/${appId}/trigger`,
    data: { appId, inputs, callback, bizInfo, debug },
  });
}

/**
 * 触发agent workflow
 * @param appId
 * @param inputs
 * @param callback
 * @param bizContext
 * @returns
 */
async function triggerAgentWorkflow(
  _appId: string,
  inputs: Record<string, any>,
  configID: string,
): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    needAuth: false,
    method: 'POST',
    url: `/app/${appId}/agentw-chat`,
    data: { appId, inputs, configID },
  });
}

/**
 * 触发app workflow
 * @param appId
 * @param inputs
 * @param callback
 * @param bizContext
 * @returns
 */
async function triggerService(
  _token: string,
  _appId: string,
  inputs: Record<string, any>,
  bizInfo?: Record<string, any>,
  callback?: ICallback,
  bizContext?: string,
): Promise<AppDetailModel> {
  const appID = _appId || getAppId();
  let token = _token;
  if (!token) {
    const tokenRes = await TokenApi.createAppTempToken(appID);
    token = tokenRes?.token;
  }
  return request({
    needAuth: false,
    useMock,
    ...(token
      ? {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
      : {}),
    method: 'POST',
    url: `/app/trigger`,
    data: { appID, inputs, callback, bizContext, bizInfo },
  });
}

/**
 * 触发debug部署
 * @param appId
 * @param config
 * @returns
 */
function debugApp(_appId: string, config: Record<string, any>): Promise<AppDetailModel> {
  const appId = _appId || getAppId();
  return request({
    useMock,
    method: 'POST',
    url: `/app/${appId}/debug`,
    data: { config },
  });
}

/**
 * 获取workflow run的状态结果
 * @param appId
 * @returns
 */
function getWorkFlowRunList(
  appId: string,
  debug?: boolean,
  _startTime?: number,
  _endTime?: number,
  pageSize?: number,
  pageNumber?: number,
  token?: string,
): Promise<AppDetailModel> {
  const startTime = _startTime || dayjs().startOf('day').valueOf();
  const endTime = _endTime || dayjs().valueOf();

  return request({
    needAuth: false,
    useMock,
    ...(token
      ? {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
      : {}),
    url: `/app/${appId}/workflow-runs?debug=${typeof debug === 'undefined' ? true : debug
      }&startTime=${startTime}&endTime=${endTime}&pageSize=${pageSize || 10}&pageNumber=${pageNumber || 1
      }`,
  });
}

/**
 * 获取workflow run的状态结果
 * @param appId
 * @returns
 */
function getWorkFlowRun(
  _appId: string,
  runId: string,
  debug?: boolean,
): Promise<IRunState> {
  const appId = _appId || getAppId();
  return request({
    needAuth: false,
    useMock,
    url: `/app/${appId}/workflow-runs/${runId}?debug=${!!debug}`,
  });
}


/**
 * 获取迭代器特点节点的状态结果
 * @param appId
 * @returns
 */
function getWorkFlowNodeStatus(
  _appId: string,
  runId: string,
  nodeId: string,
  indexList: Array<any>,
  debug?: boolean,
): Promise<IRunState> {
  const appId = _appId || getAppId();
  return request({
    needAuth: false,
    silence: true,
    cache: (res) => !!res?.nodeId,
    useMock,
    url: `/app/${appId}/status?runID=${runId}&nodeID=${nodeId}&indexList=${JSON.stringify(indexList)}&debug=${!!debug}`,
  });
}

/**
 * 获取workflow run的状态结果
 * @param appId
 * @returns
 */
async function getWorkFlowRunService(
  _token: string,
  _appId: string,
  runId: string,
): Promise<IRunState> {
  const appId = _appId || getAppId();
  let token = _token;
  if (!token) {
    const tokenRes = await TokenApi.createAppTempToken(appId);
    token = tokenRes?.token;
  }
  return request({
    needAuth: false,
    ...(token
      ? {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
      : {}),
    url: `/app/workflow-runs?appID=${appId}&runID=${runId}`,
  });
}

/**
 * 获取workflow run的日志
 * @param appId
 * @param runId
 * @param nodeId
 * @returns
 */
function getWorkFlowEvents(
  appId: string,
  runId: string,
  nodeId: string,
): Promise<AppDetailModel> {
  return request({
    useMock,
    url: `/app/${appId}/events?runID=${runId}&nodeID=${nodeId}`,
  });
}

function getAppDetailWithoutAuth(appId: string): Promise<AppDetailModel> {
  return request({
    needAuth: false,
    url: `/app/${appId}`,
  });
}

function updateApp(data: AppUpdateModel, appId: string): Promise<void> {
  return request({
    method: 'PUT',
    url: `/app/${appId}`,
    data,
  });
}

function removeApp(id: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/app/${id}`,
  });
}

function listAppTemplate(): Promise<ResponseItems<AppTemplate>> {
  return request({
    method: 'GET',
    url: `/app-template`,
  });
}

interface ListPluginParams extends Pagination {
  toolID?: string[];
  workspaceID?: string;
}

function listTool(params?: ListPluginParams): Promise<ResponseItems<Tool>> {
  return request({
    method: 'GET',
    url: '/plugin',
    params,
    paramsSerializer: (params) => {
      return Qs.stringify(params, { arrayFormat: 'repeat' });
    },
  });
}

function lockApp(_appId?): Promise<ResponseItems<Tool>> {
  const appId = _appId || getAppId();
  return request({
    method: 'POST',
    url: `/app/${appId}/lock`,
  });
}

function saveSettingConfig(data): Promise<any> {
  return request({
    method: 'POST',
    url: `/app/${data.appId}/setting-config`,
    data,
  });
}
function configPublish(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/setting-publish`,
    proxyInfo: {
      url: '/langbase/inner/setting/publish/save'
    },
    data,
  });
}
function publishHistory(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/publish-history`,
    proxyInfo: {
      url: '/langbase/inner/publish/history'
    },
    data,
  });
}
function publishRollback(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/publish-rollback`,
    proxyInfo: {
      url: '/langbase/inner/publish/rollback'
    },
    data,
  });
}
function publishChannels(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/publish-channels`,
    proxyInfo: {
      url: '/langbase/inner/publish/channels'
    },
    data,
  });
}

function configAccount(data): Promise<any> {
  return request({
    method: 'POST',
    proxyInfo: {
      url: '/langbase/inner/property/saveOrUpdate'
    },
    data,
  });
}

function getAccountList(data): Promise<any> {
  return request({
    method: 'POST',
    proxyInfo: {
      url: '/langbase/inner/property/search'
    },
    data,
  });
}
function getPortraitStyle(data): Promise<any> {
  return request({
    method: 'GET',
    proxyInfo: {
      url: '/langbase/virtual/portrait/style/all'
    },
    data,
  });
}
function portraitGenerateResult(data): Promise<any> {
  return request({
    method: 'GET',
    proxyInfo: {
      url: '/langbase/virtual/portrait/generate/result'
    },
    params: data,
  });
}
function portraitPromptPolish(data): Promise<any> {
  return request({
    method: 'POST',
    proxyInfo: {
      url: '/langbase/virtual/portrait/prompt/polish'
    },
    data,
  });
}
function portraitGenerate(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/portrait-generate`,
    proxyInfo: {
      url: '/langbase/virtual/portrait/generate'
    },
    data,
  });
}

function portraitAdd(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/portrait-add`,
    proxyInfo: {
      url: '/langbase/virtual/portrait/add'
    },
    data,
  });
}
function portraitQuery(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/portrait-query`,
    proxyInfo: {
      url: '/langbase/virtual/portrait/query'
    },
    data,
  });
}
function taskQuery(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/task-query`,
    proxyInfo: {
      url: '/langbase/virtual/asset/task/query'
    },
    data,
  });
}
function saveTrigger(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/save-trigger`,
    proxyInfo: {
      url: '/langbase/inner/trigger/saveTrigger'
    },
    data,
  });
}
function queryTrigger(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/query-trigger`,
    proxyInfo: {
      url: '/langbase/inner/trigger/searchTrigger'
    },
    data,
  });
}
function queryTriggerVariables(data): Promise<any> {
  return request({
    method: 'POST',
    // url: `/app/${data?.appId}/trigger-variables`,
    proxyInfo: {
      url: '/langbase/inner/trigger/variables'
    },
    data,
  });
}

function starApp(data) {
  return request({
    method: 'GET',
    url: `/app/${data?.appId}/star`,
    data,
  });
}

function unStarApp(data) {
  return request({
    method: 'GET',
    url: `/app/${data?.appId}/unstar`,
    data,
  });
}





const env = process.env.TEST_ENV;
console.log('env', env);
// } catch(err) {
//   console.log('err env', err);
// }

export const AppApi = {
  lockApp,
  listAppsByGroup,
  listAppsByWorkspace,
  getAppDetail,
  getAppDetailNoAuth,
  getWorkFlowRunService,
  triggerAgentWorkflow,
  getAppConfig,
  getAppHistoryWithPage,
  triggerService,
  createApp,
  updateApp,
  chatApp,
  debugApp,
  removeApp,
  getAppHistory,
  getWorkFlowRunList,
  getAppConfigSnapshotWithPage,
  getWorkFlowNodeStatus,
  copyAppConfigSnapshot,
  deployApp,
  triggerApp,
  getWorkFlowRun,
  getWorkFlowEvents,
  getAppConfigSnapshot,
  saveAppConfigSnapshot,
  updateAppVersion,
  getAppDetailWithoutAuth,
  listAppTemplate,
  listTool,
  configPublish,
  getAccountList,
  configAccount,
  saveSettingConfig,
  publishChannels,
  publishHistory,
  publishRollback,
  getPortraitStyle,
  portraitPromptPolish,
  portraitGenerate,
  portraitGenerateResult,
  portraitAdd,
  portraitQuery,
  taskQuery,
  saveTrigger,
  queryTrigger,
  queryTriggerVariables,
  starApp,
  unStarApp
};
