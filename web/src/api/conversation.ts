import { AgentConfig } from '@/interface/agent';
import {
  CompletionStreamResponse,
  ConversationResponse,
  ConversationStreamResponse,
  ToolCallInMessage,
} from '@/interface/conversation';

import { request } from '../utils/request';
import { streamFetch } from '@/utils/streamFetch';

function completion(params: {
  appID: string;
  parameters: Record<string, string>;
  images?: string[];
  imageUrl?: string;
  audioUrl?: string;
  videoUrl?: string;
  config?: any;
  responseMode?: 'streaming' | 'json';
  onMessage?: (e: CompletionStreamResponse) => void;
  onFinish?: () => void;
}) {
  const {
    appID,
    parameters,
    images,
    imageUrl,
    audioUrl,
    videoUrl,
    config,
    responseMode = 'json',
    onMessage,
    onFinish,
  } = params;
  const body = {
    config,
    parameters,
    responseMode,
    images,
    imageUrl,
    audioUrl,
    videoUrl,
  };
  if (responseMode === 'json') {
    return request({
      needAuth: false,
      url: `/app/${appID}/completion`,
      method: 'POST',
      data: body,
    }).then((res) => {
      if (res.length) {
        onMessage && onMessage(res[0] as CompletionStreamResponse)
        onFinish && onFinish()
      }
    });
  }

  return streamFetch({
    url: `/api/v1/app/${appID}/completion`,
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      'Content-Type': 'application/json',
    },
    onMessage: (ev) => {
      try {
        // const resp: CompletionStreamResponse = JSON.parse(ev.data);
        onMessage && onMessage(ev.data as CompletionStreamResponse);
      } catch (err) {
        console.log(err);
      }
    }
  }).then(res => {
    onFinish && onFinish()
  }).catch(err => {
    onMessage && onMessage({
      error: err.message,
      messageID: '',
    });
    console.log('error...', err);
  })
}

function listConversation(appID: string) {
  return request({
    needAuth: false,
    url: `/app/${appID}/conversation`,
    method: 'GET',
  }).then((res) => res);
}

function listAppMessages(appID: string, params: { pageNumber?: number; pageSize?: number, conversationId?: string, keyword?: string, start?: string, end?: string, userId?: string }): Promise<{
  items: {
    conversationID: string;
    messageID: string;
    query: string;
    toolCalls?: ToolCallInMessage[][];
    response: string;
  }[];
  total: number;
}> {
  return request({
    needAuth: false,
    url: `/app/${appID}/messages`,
    method: 'GET',
    params,
  }).then((res) => res);
}

interface ConversationData {
  message: string;
  image_url?: string;
  parameters: Record<string, string> | null;
  config?: AgentConfig;
}

function startConversationStreaming(
  appID: string,
  data: ConversationData,
  params?: { conversationID?: string, genName?: boolean },
  onMessage?: (e: ConversationStreamResponse) => void,
  onFinish?: () => void,
  signal?: AbortController,
): Promise<ConversationResponse> | void {
  if (Object.keys(params ?? {}).length === 0) {
    params = undefined;
  }
  const body = {
    ...data,
    responseMode: "streaming",
  };

  const url = `/api/v1/app/${appID}/chat?`;
  const searchParams = new URLSearchParams();
  if (params?.conversationID) {
    searchParams.append('conversationID', params.conversationID);
  }
  if (params?.genName !== undefined) {
    searchParams.append('genName', `${params.genName}`);
  }

  streamFetch({
    url: url + searchParams.toString(),
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      'Content-Type': 'application/json',
    },
    abortCtrl: signal,
    onMessage: (ev) => {
      try {
        onMessage && onMessage(ev.data as ConversationStreamResponse);
      } catch (e: any) { console.log(e); window.corona.error(e); }
    }
  }).then(res => {
    onFinish && onFinish()
  })
}

function startConversation(
  appID: string,
  data: ConversationData,
  params?: { conversationID?: string, genName?: boolean },
): Promise<ConversationResponse> {
  if (Object.keys(params ?? {}).length === 0) {
    params = undefined;
  }
  const body = {
    ...data,
    responseMode: "json",
  };
  return request({
    url: `/app/${appID}/chat`,
    method: 'POST',
    data: body,
    params,
  }).then((res) => res);
}

function listMessage(
  conversationID: string,
  params?: { pageNumber?: number; pageSize?: number },
): Promise<{
  items: {
    conversationID: string;
    messageID: string;
    query: string;
    toolCalls?: ToolCallInMessage[][];
    response: string;
  }[];
  total: number;
}> {
  return request({
    needAuth: false,
    url: `/conversation/${conversationID}/message`,
    method: 'GET',
    params,
  }).then((res) => res);
}

function deleteConversation(conversationID: string) {
  return request({
    needAuth: false,
    url: `/conversation/${conversationID}`,
    method: 'DELETE',
  });
}

function getConversation(conversationID: string) {
  return request({
    needAuth: false,
    url: `/conversation/${conversationID}`,
    method: 'GET',
  });
}

function updateConversation(conversationID: string, name: string, config?: AgentConfig) {
  return request({
    needAuth: false,
    url: `/conversation/${conversationID}`,
    method: 'PUT',
    data: {
      name,
      overrideConfig: config,
    },
  });
}

export const OpenAiAPI = {
  completion,
  startConversation,
  startConversationStreaming,
  listConversation,
  listAppMessages,
  listMessage,
  deleteConversation,
  getConversation,
  updateConversation,
};
