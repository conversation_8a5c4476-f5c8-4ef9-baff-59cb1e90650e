import { env } from '@/utils/common';
import { request } from '@utils/request';

interface IPopo {
  id: string;
  name: string;
  description: string;
}

interface IUpdatePopo {
  popoId: string;
  robotUid?: string;
  secret: string;
  type: string;
  id: number;
  config: Record<string, any>;
}

const adoraMap = {
  // 'dev': 'http://ada-popo.qa.ft.igame.163.com',
  'dev': 'http://ada-popo.qa-alloy.ft.igame.163.com',
  // 'dev': 'http://localhost:8080',
  'pre': 'https://ada-popo.ft.netease.com',
  'online': 'https://ada-popo.ft.netease.com',
  'test': 'http://ada-popo.qa.ft.igame.163.com',
}
// const baseURL = online ? 'http://ada-popo.ft.netease.com' : 'http://ada-popo.qa.ft.igame.163.com';
export const baseURL = adoraMap[env];

export const getBaseUrl = () => baseURL;

/**
 * 获取所有分类
 * @returns 
 */
function list(appId: number, isWorkflow: boolean, pageSize?: number, pageNumber?: number): Promise<IPopo[]> {
  return request({
    baseURL,
    url: `/api/ada/popo/config/list`,
    params: { page: JSON.stringify({ from: 1, to: pageNumber, size: pageSize }), langbaseId: appId, isWorkflow }
  }).then((res) => {
    return res.records;
  });
}

/**
 * 创建POPO
 * @param data 
 * @returns 
 */
function create(data: IUpdatePopo): Promise<void> {
  return request({
    baseURL,
    url: `/api/ada/popo/config/create`,
    method: 'POST',
    data,
  });
}

/**
 * 删除POPO
 * @param PopoID 
 * @returns 
 */
function remove(data: IUpdatePopo): Promise<void> {
  return request({
    method: 'POST',
    baseURL,
    data,
    url: `/api/ada/popo/config/delete`,
  })
}

/**
 * 开启POPO
 * @param PopoID 
 * @returns 
 */
function open(popoId: string, langbaseId: string, open: boolean): Promise<void> {
  return request({
    baseURL,
    params: { popoId, langbaseId, open },
    url: `/api/ada/popo/config/open`,
  })
}

/**
 * 更新分类
 * @param data 
 * @param PopoID 
 * @returns 
 */
function update(data: IUpdatePopo): Promise<void> {
  return request({
    method: 'POST',
    baseURL,
    data,
    url: `/api/ada/popo/config/update`,
  })
}

/**
 * 更新当前应用类型
 * @param data 
 * @param PopoID 
 * @returns 
 */
function updateType(isWorkflow: boolean, langbaseId: string): Promise<void> {
  return request({
    method: 'POST',
    baseURL,
    data: { isWorkflow, langbaseId },
    url: `/api/ada/popo/config/updateType`,
  })
}

export const PopoApi = {
  list,
  create,
  updateType,
  update,
  open,
  remove
}
