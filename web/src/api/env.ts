import { request } from '@utils/request';

import { getAppId, getGroupId, getWorkspaceId } from '@/utils/state';

interface IEnv {
  name: string;
  description: string;
  componentID: string;
  value: string;
}

interface IUpdateEnv {
  name: string;
  value: string;
}

/**
 * 获取应用环境变量
 * @returns
 */
function list(
  id?: string,
  type?: 'group' | 'workspace' | 'app' | 'components',
  scopeType?: 'group' | 'workspace' | 'app' | 'components',
  scopeId?: string,
): Promise<IEnv[]> {
  let scopeID = id || getAppId();
  if (!scopeID) {
    if (type === 'workspace') {
      scopeID = getWorkspaceId();
    }
    if (type === 'group') {
      scopeID = getGroupId();
    }
  }
  let url = `/${type}/${id || scopeID}/env-variables`;
  if (scopeType) {
    url += `?scopeType=${scopeType}&scopeID=${scopeId}`;
  }
  return request({
    url,
  }).then((res) => {
    return res.items;
  });
}

/**
 * 更新环境变量
 * @param data
 * @param envVariableID
 * @returns
 */
function create(data: IUpdateEnv, type: string, _id: string): Promise<void> {
  let id = _id || getAppId();
  if (!id) {
    if (type === 'workspace') {
      id = getWorkspaceId();
    }
    if (type === 'group') {
      id = getGroupId();
    }
  }
  return request({
    method: 'POST',
    data,
    url: `/${type}/${id}/env-variables`,
  });
}

/**
 * 删除环境变量
 * @param categoryID
 * @returns
 */
function remove(envVariableID: string): Promise<void> {
  return request({
    method: 'DELETE',
    url: `/env-variables/${envVariableID}`,
  });
}

/**
 * 更新分类
 * @param data
 * @param categoryID
 * @returns
 */
function update(data: IUpdateEnv, envVariableID: string): Promise<void> {
  return request({
    method: 'PUT',
    data,
    url: `/env-variables/${envVariableID}`,
  });
}

/**
 * 获取非用户变量
 * @param appType
 * @param appId
 * @returns
 */
interface INonUserVariable {
  key: string;
  dataType: string;
  title: string;
  default_val: any;
  group: string;
  category: string;
  type: string;
}

function getNonUserVariables(appType: string, appId: string): Promise<{ variables: INonUserVariable[] }> {
  return request({
    method: 'POST',
    // url: `/app/${appId}/nonuser-variables`,
    proxyInfo: {
      url: '/langbase/inner/agent/nonuser/variables'
    },
    data: {
      appType,
      appId
    }
  });
}

export const EnvApi = {
  list,
  update,
  create,
  remove,
  getNonUserVariables,
};
