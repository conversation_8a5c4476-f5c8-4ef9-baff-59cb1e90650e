// @ts-nocheck
import { map, result, zipObject } from 'lodash';

import { arr2Obj, JSONParse } from '@/utils/common';

import { request } from '../utils/request';

function getBusinessResourceTypes(): Promise<any> {
  return request({
    url: `/proxy/cio/resourcetypes`,
  });
}

async function getCIOResource(id, data) {
  const { configs, outputs } = data;
  const options = arr2Obj(configs);
  // const output = map(outputs, 'name');
  // const paths = map(outputs, 'path');
  const codes = map(outputs, 'code');
  return await getDetail({ queryCode: codes, resourceId: id, ...options }).then((res) => {
    return res?.queryResponse;
    // if (res?.resourceContent) {
    //   res.resourceContent = JSONParse(res.resourceContent);
    // }
    // if (res?.extInfo) {
    //   res.extInfo = JSONParse(res.extInfo);
    // }
    // if (res?.extTag) {
    //   res.extTag = JSONParse(res.extTag);
    // }
    // const values = paths.map((p) => result(res, p.replace(/\s/g, '')));
    // return zipObject(output, values);
  });
}

function getPools({
  business,
  resourceType,
}: {
  business: string;
  resourceType: string;
}): Promise<any> {
  return request({
    url: `/proxy/cio/pools`,
    params: { business, resourceType, pageSize: 300 },
  })
    .then((res) => {
      if (res.items) {
        return res.items;
      }
      return res;
    })
    .catch((err) => {
      return [];
    });
}

function getPoolConfig({
  business,
  resourceType,
  poolCode,
}: {
  business: string;
  resourceType: string;
  poolCode: string;
}): Promise<any> {
  return request({
    // baseURL: 'http://cms.qa-flag.igame.163.com/',
    url: `/proxy/cio/content/scene/langbase/field`,
    params: {
      business,
      resourceType,
      poolCode,
      scene: 'LANGBASE_INPUT',
    },
  })
    .then((res) => {
      if (res.items) {
        return res.items;
      }
      return res;
    })
    .catch((err) => {
      return null;
    });
}

function getDetail({
  business,
  resourceType,
  poolCode,
  resourceId,
  queryCode,
}: {
  business: string;
  resourceId: string;
  resourceType: string;
  poolCode: string;
  queryCode: string;
}): Promise<any> {
  return request({
    url: `/proxy/cio/pool/resource/content/detail`,
    method: 'POST',
    data: {
      business,
      resourceType,
      poolCode,
      resourceId,
      queryCode,
    },
  }).catch((err) => {
    window.corona.error('cio fetchError', err);
    return [];
  });
}

export const CioApi = {
  getBusinessResourceTypes,
  getPools,
  getPoolConfig,
  getDetail,
  getCIOResource,
};
