import { request } from "@/utils/request";

const getSettingList = (data: {appId: string} ) => {
  return request({
    method:'GET',
    url: `/app/${data.appId}/setting`,
  });
};

const saveSetting = (data) => {
  return request({
    method:'POST',
    url: `/app/${data.appId}/setting`,
    data
  });
};

const updateSetting = (data) => {
  return request({
    method:'PUT',
    url: `/app/${data.appId}/setting`,
    data
  });
};

const removeSetting = (data) => {
  return request({
    method: 'DELETE',
    url: `/app/${data.appId}/setting`,
    data
  });
};

const getSettingConfig = (data: {settingId: string, appId: string} ) => {
  return request({
    method:'GET',
    url: `/app/${data.appId}/setting-config`,
    params: {
      settingId: data.settingId
    }
  });
};

const getSettingVersionedConfig = (data: {
  settingId: string,
  page?: number,
  pageSize?: number,
  appId: string}) => {
  return request({
    method:'GET',
    url: `/app/${data.appId}/setting-versioned-config`,
    params: {
      settingId: data.settingId,
      page: data?.page || 1,
      pageSize: data?.pageSize || 100
    }
  });
};


export const MultipleConfigApi = {
  getSettingList,
  getSettingConfig,
  getSettingVersionedConfig,
  saveSetting,
  updateSetting,
  removeSetting
};