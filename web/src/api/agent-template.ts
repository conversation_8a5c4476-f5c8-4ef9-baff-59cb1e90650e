import { request } from "@/utils/request";

interface ITemplateAgent {
  workspaceId:string;
  groupId:string;
  name:string;
  avatarUrl:string;
  promptType:string;
  structPrompt: any[];
  prompt:string;
}

interface ITemplateList {
  workspaceId:string;
  groupId:string;
  appType?:string;
}

function saveTemplate (data:ITemplateAgent) {
  return request({
    method: 'POST',
    url:'/prompt-template/save',
    data
  });
};

function getTemplateList(data:ITemplateList) {
  return request({
    method: 'GET',
    url: '/prompt-template/list',
    params: data
  })
};

function struct2Text (data) {
  return request({
    method: 'POST',
    url: '/prompt-tools/convert2text',
    data
  });
};

function text2Struct (data) {
  return request({
    method: 'POST',
    url: '/prompt-tools/convert2json',
    data
  });
};

function beautify2text (data) {
  return request({
    method: 'POST',
    url: '/prompt-tools/beautify2text',
    data
  });
};

function aiBeautify (data: { pre_prompt: string, requirements: string }) {
  return request({
    method: 'POST',
    url: '/prompt-tools/ai-beautify',
    data
  });
}

function textConvert2Struct (data: {prompt: string}) {
  return request({
    method: 'POST',
    url: '/prompt-tools/convert2struct-text',
    data
  });
}

export const AgentTemplateApi = {
  aiBeautify,
  saveTemplate,
  getTemplateList,
  struct2Text,
  text2Struct,
  beautify2text,
  textConvert2Struct
};