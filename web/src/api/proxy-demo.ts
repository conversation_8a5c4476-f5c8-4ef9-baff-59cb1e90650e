import { request } from '../utils/request';

// 通用get代理请求（如果不需要特殊处理权限）
// 默认权限，无权限限制，登陆即可，适用通用接口不与应用绑定
function getProxyDemo(data): Promise<any> {
  return request({
    method: 'GET',
    proxyInfo: {
      url: 'xxx' // 原始地址
    },
    params: data,
  });
}
// 通用post代理请求
// 默认应用权限，租户，开发者，适用与应用绑定的接口
function postAppProxyDemo(data): Promise<any> {
  return request({
    method: 'POST',
    proxyInfo: {
      url: 'xxx' // 原始地址
    },
    data,
  });
}
