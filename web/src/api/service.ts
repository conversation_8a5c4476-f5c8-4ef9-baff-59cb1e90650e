import { AgentConfig } from '@/interface/agent';
import { AppDetailModel } from '@/interface/app';
import { ResponseItems } from '@/interface/common';
import {
  CompletionStreamResponse,
  Conversation,
  ConversationStreamResponse,
} from '@/interface/conversation';
import { getToken } from '@/utils/common';

import { request } from '../utils/request';
import { streamFetch } from '@/utils/streamFetch';

function completion(params: {
  appID: string;
  parameters: Record<string, string>;
  config?: any;
  responseMode?: 'streaming' | 'json';
  onMessage?: (e: CompletionStreamResponse) => void;
  onFinish?: () => void;
}) {
  const {
    appID,
    parameters,
    config,
    responseMode = 'json',
    onMessage,
    onFinish,
  } = params;
  const headers = {
    Authorization: `Bearer ${getToken(appID)}`,
  };
  const body = {
    config,
    parameters,
    responseMode,
  };
  if (responseMode === 'json') {
    return request({
      needAuth: false,
      url: '/completion',
      method: 'POST',
      data: body,
      headers: {
        Authorization: `Bearer ${getToken(appID)}`,
      },
    }).then((res) => {
      if (res.length) {
        onMessage && onMessage(res[0] as CompletionStreamResponse)
        onFinish && onFinish()
      }
    });
  }

  streamFetch({
    url: '/api/v1/completion',
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      ...headers,
      'Content-Type': 'application/json',
    },
    onMessage: (ev) => {
      try {
        onMessage && onMessage(ev.data as CompletionStreamResponse);
      } catch (err) {
        console.log(err);
      }
    }}).then(res => {
      onFinish && onFinish()
    })
}

function listOrGetConversation(
  appID: string,
  conversationID?: string,
  pageNumber?: number,
  pageSize?: number,
): Promise<ResponseItems<Conversation> | Conversation> {
  const token = getToken(appID);
  return request({
    needAuth: false,
    url: `/conversation`,
    method: 'GET',
    params: {
      conversationID,
      pageNumber,
      pageSize,
    },
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

interface ConversationData {
  message: string;
  parameters: Record<string, string> | null;
  config?: AgentConfig;
}

function startConversation(
  appID: string,
  data: ConversationData,
  params?: { conversationID?: string },
  responseMode: 'streaming' | 'json' = 'streaming',
  onMessage?: (e: ConversationStreamResponse) => void,
  onFinish?: () => void,
  signal?: AbortController,
) {
  if (Object.keys(data.config ?? {}).length === 0) {
    data.config = undefined;
  }
  const headers = {
    Authorization: `Bearer ${getToken(appID)}`,
  };
  const body = {
    ...data,
    responseMode,
  };
  if (responseMode === 'json') {
    return request({
      needAuth: false,
      url: `/chat`,
      method: 'POST',
      data: body,
      params,
      headers,
    }).then((res) => res);
  }

  const conversationID = params?.conversationID;
  let url = '/api/v1/chat';
  if (conversationID) {
    url += `?conversationID=${conversationID}`;
  }
  return streamFetch({
    url,
    method: 'POST',
    body: JSON.stringify(body),
    abortCtrl: signal,
    headers: {
      ...headers,
      'Content-Type': 'application/json',
    },
    onMessage: (ev) => {
      try {
        onMessage && onMessage(ev.data as ConversationStreamResponse);
      } catch (err: any) {
        window.corona.error('sse Error', err);
        console.log(err);
      }
    }
  }).then(res => {
    onFinish && onFinish()
  })
}

function listMessage(
  appID: string,
  params?: {
    pageNumber?: number;
    pageSize?: number;
    conversationID: string;
  },
): Promise<{
  items: {
    conversationID: string;
    messageID: string;
    query: string;
    response: string;
  }[];
  total: number;
}> {
  return request({
    needAuth: false,
    url: `/message`,
    method: 'GET',
    params,
    headers: {
      Authorization: `Bearer ${getToken(appID)}`,
    },
  }).then((res) => res);
}

function deleteConversation(appID: string, conversationID: string) {
  return request({
    needAuth: false,
    url: `/conversation`,
    method: 'DELETE',
    params: {
      conversationID,
    },
    headers: {
      Authorization: `Bearer ${getToken(appID)}`,
    },
  });
}

function updateConversation(appID: string, conversationID: string, name: string) {
  return request({
    needAuth: false,
    url: `/conversation`,
    method: 'PUT',
    params: {
      conversationID,
    },
    headers: {
      Authorization: `Bearer ${getToken(appID)}`,
    },
    data: {
      name,
    },
  });
}

function getAppDetail(appID: string, token?: string): Promise<AppDetailModel> {
  return request({
    needAuth: false,
    url: `/app`,
    params: {
      appID,
    },
    headers: {
      Authorization: `Bearer ${token ? token : getToken(appID)}`,
    },
  });
}

export const ServiceAPI = {
  completion,
  startConversation,
  listOrGetConversation,
  listMessage,
  deleteConversation,
  updateConversation,
  getAppDetail,
};
