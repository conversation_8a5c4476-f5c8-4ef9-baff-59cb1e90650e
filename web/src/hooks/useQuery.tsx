import { omit, pick } from 'lodash';
import queryString from 'querystring';
import { useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const beforeQueryChange: {
  key: string;
  callback: (current: any, next: any) => Promise<any>;
}[] = [];

// 改变当前的 query
export function useQuery() {
  const location = useLocation();
  const navigate = useNavigate();
  const parsedQuery = useMemo(() => queryString.parse(location.search.slice(1)), [location]);
  const debug = useMemo(() => parsedQuery.debug, [parsedQuery]);

  function onQueryChange(
    key: string,
    callback: (current: any, next: any) => Promise<any>,
  ) {
    beforeQueryChange.push({
      key,
      callback,
    });
  }

  function offQueryChange(key: string) {
    const index = beforeQueryChange.findIndex((d) => d.key === key);
    beforeQueryChange.splice(index, 1);
  }

  function addQuery(params: any, replace = false) {
    const merged = {
      ...parsedQuery,
      ...params,
    };
    const search = queryString.stringify(merged);
    if (beforeQueryChange.length > 0) {
      Promise.all(beforeQueryChange.map((d) => d.callback(parsedQuery, merged))).then(
        () => {
          navigate(`${location.pathname}?${search}`, { replace });
        },
      );
    } else {
      navigate(`${location.pathname}?${search}`, { replace });
    }
  }

  function updateQuery(key: string, value: string, replace = false) {
    const merged = {
      ...parsedQuery,
      [key]: value,
    };
    const search = queryString.stringify(merged);
    navigate(`${location.pathname}?${search}`, { replace });
  }

  function retainQuery(path: string, retainKeys: string[], replace = false) {
    const newState = pick(parsedQuery, retainKeys);
    const search = queryString.stringify(newState);
    navigate(`${path}?${search}`, { replace });
  }

  function dropQuery(dropKeys: string[], replace = false) {
    const newState = omit(parsedQuery, dropKeys);
    const search = queryString.stringify(newState);
    navigate(`${location.pathname}?${search}`, { replace });
  }

  function addAndRetain(params: any, retainKeys: string[], replace = false) {
    const merged = {
      ...parsedQuery,
      ...params,
    };
    const newState = pick(merged, retainKeys);
    const search = queryString.stringify(newState);
    navigate(`${location.pathname}?${search}`, { replace });
  }

  return {
    location,
    parsedQuery,
    addQuery,
    retainQuery,
    dropQuery,
    addAndRetain,
    updateQuery,
    onQueryChange,
    offQueryChange,
    debug,
  };
}

// 改变路由的同时，对 query 进行操作
export function usePathQuery() {
  const location = useLocation();
  const navigate = useNavigate();
  const parsed = useMemo(
    () => queryString.parse(location.search.substr(1)),
    [location.search],
  );

  const navTo = useCallback(
    (url: string, newPage?: boolean, replace = false) => {
      if (newPage) {
        window.open(url, '_blank');
      } else {
        navigate(url, { replace });
      }
    },
    [history],
  );

  // 改变路由的同时，增加新的 Query
  function pushAddQuery(path: string, params: any, newPage?: boolean, replace = false) {
    const merged = {
      ...parsed,
      ...params,
    };
    const search = queryString.stringify(merged);
    navTo(`${path}?${search}`, newPage, replace);
  }

  // 改变路由的同时，只保留指定的 Query
  function pushRetainQuery(
    path: string,
    retainKeys: string[],
    newPage?: boolean,
    replace = false,
  ) {
    const newState = pick(parsed, retainKeys);
    const search = queryString.stringify(newState);
    console.log("push", path, search, newPage, replace);
    navTo(`${path}?${search}`, newPage, replace);
  }

  // 改变路由的同时，删除指定的 Query
  function pushDropQuery(
    path: string,
    dropKeys: string[],
    newPage?: boolean,
    replace = false,
  ) {
    const newState = omit(parsed, dropKeys);
    const search = queryString.stringify(newState);
    navTo(`${path}?${search}`, newPage, replace);
  }

  return {
    pushAddQuery: useCallback(pushAddQuery, [parsed, navTo]),
    pushRetainQuery: useCallback(pushRetainQuery, [parsed, navTo]),
    pushDropQuery: useCallback(pushDropQuery, [parsed, navTo]),
  };
}

export function getRetainQuery(retainKeys: string[]) {
  const parsedQuery = queryString.parse(window.location.search.slice(1));
  const newState = pick(parsedQuery, retainKeys);
  const search = queryString.stringify(newState);
  return search;
}
