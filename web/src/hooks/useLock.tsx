// src/hooks/useLock.ts
import { useEffect, useRef, useState } from 'react';
import { AppApi } from '@/api/app';
import { getUserState } from '@/utils/state';

export const useLock = (id?: string) => {
  const [lock, setLock] = useState(null);
  const timer = useRef(null);

  const getLockState = async () => {
    let lock = null;
    try {
      lock = await AppApi.lockApp(id);
    } catch (err) {
      window.corona.warn('lock失败', err);
      clearInterval(timer.current);
      timer.current = null;
    }
    const name = getUserState('name');
    if (lock && lock?.username !== name) {
      setLock(lock);
    } else {
      setLock(false);
    }
  };

  useEffect(() => {
    timer.current = setInterval(getLockState, 8000);
    getLockState();

    return () => {
      clearInterval(timer.current);
      timer.current = null;
    };
  }, []);

  return { lock };
};