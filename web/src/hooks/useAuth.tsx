import { RoleType, UserModel, UserRoles } from '../interface';
import { AuthResId, RoleLevel, RoleLevelResMap } from '../interface/auth';
import { useGlobalState } from './useGlobalState';
import { useQuery } from './useQuery';

export function useAuth(minRole: RoleLevel) {
  const { parsedQuery } = useQuery();
  const { globalState } = useGlobalState();
  const { roles, user } = globalState;

  if (!roles || !user) {
    return false;
  }
  const authResKey: AuthResId | null = RoleLevelResMap[minRole];
  const userMaxRoleLevel = getMaxRoleLevel(user, roles, authResKey, parsedQuery);
  return userMaxRoleLevel >= minRole;
}

function getMaxRoleLevel(
  user: UserModel,
  roles: UserRoles,
  authRes: AuthResId | null,
  parsedQuery: any,
) {
  if (user.isAdmin) {
    return RoleLevel.SystemAdmin;
  }

  if (!roles || !authRes) {
    return RoleLevel.UnAuth;
  }

  if (authRes === AuthResId.workspaceId) {
    return getMaxWorkspaceRoleLevel(roles, parsedQuery);
  }
  if (authRes === AuthResId.GroupId) {
    return getMaxGroupRoleLevel(roles, parsedQuery);
  }
  if (authRes === AuthResId.AppId) {
    return getMaxAppRoleLevel(roles, parsedQuery);
  }

  return RoleLevel.UnAuth;
}

// 获取用户对该组织的最大权限
function getMaxWorkspaceRoleLevel(roles: UserRoles, parsedQuery: any) {
  const workspaceId = parsedQuery.workspaceId;
  const targetRole = (roles.workspace || []).find((it) => it.workspaceId === workspaceId);
  if (!targetRole) {
    return RoleLevel.UnAuth;
  }
  if (targetRole.role === RoleType.ADMIN) {
    return RoleLevel.WorkspaceAdmin;
  }
  if (targetRole.role === RoleType.DEVELOPER) {
    return RoleLevel.WorkspaceDeveloper;
  }
  if (targetRole.role === RoleType.VIEWER) {
    return RoleLevel.WorkspaceViewer;
  }
  return RoleLevel.UnAuth;
}

// 获取用户对该业务组的最大权限
function getMaxGroupRoleLevel(roles: UserRoles, parsedQuery: any) {
  const groupId = parsedQuery.groupId;
  const targetRole = (roles.group || []).find((it) => it.groupId === groupId) || { role: 'none' };
  let groupLevel = RoleLevel.UnAuth;
  if (targetRole.role === RoleType.ADMIN) {
    groupLevel = RoleLevel.GroupAdmin;
  }

  if (targetRole.role === RoleType.DEVELOPER) {
    return RoleLevel.GroupDeveloper;
  }

  if (targetRole.role === RoleType.VIEWER) {
    groupLevel = RoleLevel.GroupViewer;
  }

  return Math.max(groupLevel, getMaxWorkspaceRoleLevel(roles, parsedQuery));
}

// 获取用户对该应用的最大权限
function getMaxAppRoleLevel(roles: UserRoles, parsedQuery: any) {
  const appId = parsedQuery.appId;
  const targetRole = (roles.app || []).find((it) => it.appId === appId) || { role: 'none' };
  let appLevel = RoleLevel.UnAuth;

  if (targetRole.role === RoleType.ADMIN) {
    appLevel = RoleLevel.AppAdmin;
  }

  if (targetRole.role === RoleType.DEVELOPER) {
    appLevel = RoleLevel.AppDeveloper;
  }

  if (targetRole.role === RoleType.VIEWER) {
    appLevel = RoleLevel.AppViewer;
  }

  return Math.max(appLevel, getMaxGroupRoleLevel(roles, parsedQuery));
}
