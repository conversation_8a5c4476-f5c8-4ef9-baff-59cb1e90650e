import { getLocalData, localPrefix } from '@/utils/common';
import eventBus, { IEventType } from '@/utils/event-bus';
import React, { useState, useEffect, useRef } from 'react';

export function useLocalStorage(key) {
  const [value, setValue] = useState(() => getLocalData(key));
  const [preConfig, setPreConfig] = useState(null);
  const eventId = useRef(0);

  useEffect(() => {
    const handleStorageChange = (event) => {
      console.log('local storageChange', event, key, localPrefix);
      if (event.key === key) {
        setValue(event.value ? event.value : {});
      }
      if (event.key === 'preConfig') {
        setPreConfig(event.value ? event.value : {});
      }
    };

    eventId.current = eventBus.on(IEventType.STORAGE_CHANGE, handleStorageChange)

    return () => {
      eventBus.cancel(IEventType.STORAGE_CHANGE, eventId.current);
    };
  }, [key]);

  useEffect(() => {
    setValue(() => getLocalData(key));
  }, [key])

  return [value, preConfig];
}
