import { useContext, useEffect } from 'react';
import { useLocation, useMatch } from 'react-router-dom';

import { UserApi } from '@/api';

import { globalContext } from '../store';
import { usePathQuery, useQuery } from './useQuery';
import { LAST_VISITED_WORKSPACE } from '@/utils/constant';
import { DefaultModelApi } from '@/api/model-provider';
import { setDefaultModel } from '@/utils/common';

// 该 hooks 全局只能引用一个实例，否则会多次重复请求。!!!!
// 该 hooks 全局只能引用一个实例，否则会多次重复请求。!!!!
// 该 hooks 全局只能引用一个实例，否则会多次重复请求。!!!!

// 监听 url 上的与全局状态有关的 Query 参数变化，并获取、更新 全局状态
export function useQuery2GlobalState() {
  const { globalState, updateGlobalState, fetchGlobalState } = useContext(globalContext);
  const { parsedQuery, addQuery, updateQuery } = useQuery();
  const { pushRetainQuery } = usePathQuery();
  const { workspaces, groups, workspace, group, app } = globalState;

  const location = useLocation();
  const matchGroupPage = useMatch('/group');

  useEffect(() => {
    console.log("fetch workspaces")
    fetchGlobalState('workspaces');
  }, []);

  // 同步选中的租户
  useEffect(() => {
    if (!workspaces || !workspaces.length) {
      return;
    }
    const workspaceId = parsedQuery.workspaceId;
    const selectWorkspace = workspaces.find((t) => t.id === workspaceId);

    if (!selectWorkspace) {
      // 如果无当前 workspace 的权限，则跳转到 workspace 邀请页面
      if (workspaceId) {
        if (location.pathname === '/invite/workspace') {
          return;
        } else {
          pushRetainQuery('/invite/workspace', ['workspaceId']);
        }
      } else {
        const lastVisitedWorkspace = localStorage.getItem(LAST_VISITED_WORKSPACE)
        if (lastVisitedWorkspace) {
          addQuery({ workspaceId: lastVisitedWorkspace });
        }else {
          addQuery({ workspaceId: workspaces[0].id });
        }
      }
      return;
    }
    if (workspace !== selectWorkspace) {
      UserApi.getResourceRole('workspace', workspaceId).then((role) => {
        updateGlobalState('workspaceRole', role);
      });
      DefaultModelApi.listWorkspaceDefaultModel(workspaceId as string).then((res) => {
        console.log('default model', res);
        const llm = res.find(t => t.modelType === 'llm');
        setDefaultModel(llm?.modelName, llm?.providerKind);
      })
      updateGlobalState({
        workspace: selectWorkspace,
      });
    }
  }, [
    parsedQuery.workspaceId,
    workspaces,
    workspace,
    addQuery,
    updateGlobalState,
    location.pathname,
  ]);

  // 获取当前事业部对应的 业务组列表
  const groupsWorkspaceId = groups && groups[0]?.workspaceId;
  useEffect(() => {
    if (!workspace || !workspace.id) {
      return;
    }
    if (!groups || workspace.id !== groupsWorkspaceId) {
      fetchGlobalState('groups', workspace.id);
    }
  }, [workspace, groupsWorkspaceId, fetchGlobalState]);

  // 同步选中的小组
  useEffect(() => {
    if (!groups || !groups.length) {
      if (group) {
        updateGlobalState('group', null);
      }
      return;
    }
    const groupId = app?.groupID || parsedQuery.groupId;
    if (app?.groupID && parsedQuery.appId && app?.groupID !== parsedQuery.groupId) {
      // 修改链接上的 groupId，以 appId 查询到的 groupId 为准
      updateQuery('groupId', `${app?.groupID}`, true);
    }
    const selectGroup = groups.find((t) => t.id === groupId);

    if (!selectGroup && matchGroupPage) {
      // 等到 groups 更新为当前 tenant 后，在选择默认小组
      if (groups[0].workspaceId === parsedQuery.workspaceId) {
        addQuery({ groupId: groups[0].id }, true);
      }
      return;
    }
    if (selectGroup && group !== selectGroup) {
      UserApi.getResourceRole('group', groupId).then((role) => {
        updateGlobalState('groupRole', role);
      });
      updateGlobalState({
        group: selectGroup,
      });
    }
  }, [parsedQuery, groups, group, addQuery, updateGlobalState, matchGroupPage, app]);

  // 同步选中的应用
  useEffect(() => {
    const matchAppPage = location.pathname.includes('/app');

    if (!matchAppPage) {
      if (app?.id) {
        updateGlobalState('app', null);
      }
      return;
    }

    const appId = parsedQuery.appId;

    if (!appId) return;
    if (app?.id !== appId) {
      fetchGlobalState('app', appId);
    }
  }, [parsedQuery.appId, app]);
}
