import { useGlobalState } from './useGlobalState';

export const useRole = (type) => {
  const { globalState } = useGlobalState();
  if (type === 'system') {
    return globalState.user.isAdmin ? 'admin' : 'guest';
  }

  // const getRole = async () => {
  //   // 查看一下当前用户的角色，看是否已经拥有该角色，如果没有，则去获取workspace权限
  //   const res = globalState[`${type}Role`];
  //   if (!res) {
  //     UserApi.getResourceRole('workspace', getWorkspaceId()).then((r) => {
  //       updateGlobalState('workspaceRole', r);
  //       setRole(r.role);
  //     });
  //   } else {
  //     setRole(res.role);
  //   }
  // };

  // useEffect(() => {
  //   console.log('useRole', globalState[`${type}Role`], type);
  //   getRole();
  // }, [type]);

  // return role;
  return globalState[`${type}Role`]?.role || 'guest';
};

export const useAdmin = (type) => {
  const role = useRole(type);
  if (type === 'group') {
    return role === 'admin' || useRole('workspace') === 'admin';
  }
  if (type === 'app') {
    return role === 'admin' || useRole('workspace') === 'admin' || useRole('group') === 'admin';
  }
  return role === 'admin';
};
