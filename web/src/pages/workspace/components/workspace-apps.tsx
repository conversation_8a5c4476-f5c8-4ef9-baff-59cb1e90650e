import { Col, Pagination, Row, Spin } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';

import { AppApi } from '@/api/app';
import { LayoutContainer } from '@/components/layout-container';
import { AppCard } from '@/components/resource-card';
import { AppModel, IAppType } from '@/interface';
import { CreateAppCard } from '@/pages/home/<USER>/create-app';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppListSearch } from '@/components/AppListSearch';

const PAGE_SIZE = 15;

export function WorkspaceApps() {
  const [appList, setAppList] = useState<AppModel[]>([]);
  const [total, setTotal] = useState(0); // 总数
  const [pageNo, setPageNo] = useState(1);
  const [loading, setLoading] = useState(false);
  const {globalState} = useGlobalState();
  const {workspace, groups} = globalState;
  const [appType, setAppType] = useState<IAppType>();
  const [appSearch, setAppSearch] = useState<string>();

  useEffect(() => {
    if (!workspace?.id) {
      return;
    }
    setLoading(true);
    AppApi.listAppsByWorkspace(workspace.id, {
      appType: appType ? [appType] : undefined,
      name: appSearch,
      pageSize: PAGE_SIZE,
      pageNumber: pageNo,
    })
      .then((res) => {
        const { items, total } = res;
        setAppList(items);
        setTotal(total);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [workspace?.id, pageNo, appType, appSearch]);

  const onSearch = (search?: string, appType?: IAppType ) => {
    setAppSearch(search);
    setAppType(appType);
  };

  return (
    <LayoutContainer>
      <AppListSearch onSearch={onSearch} />
      {
        loading
          ? (
            <Loading>
              <Spin />
            </Loading>
          ) :
          (
            <div>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={8} lg={8} xl={6} xxl={6}>
                  <CreateAppCard workspace={workspace} groups={groups}/>
                </Col>
                {appList.map((app) => (
                  <Col key={app.id} xs={12} sm={24} md={8} lg={8} xl={6} xxl={6}>
                    <AppCard app={app} key={app.id} />
                  </Col>
                ))}
                {/* {!appList.length && <Empty />} */}
              </Row>
              {total > PAGE_SIZE && (
                <Pagination
                  style={{ float: 'right', marginTop: '16px' }}
                  // simple
                  showQuickJumper
                  size="default"
                  current={pageNo}
                  onChange={setPageNo}
                  total={total}
                  pageSize={PAGE_SIZE}
                />
              )}
    </div>

        )
      }
    </LayoutContainer>
  );
}

const FilterHeader = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 20px;
  padding-left: 0px;
  margin-bottom: 20px;

  .filter {
    display: flex;
    flex-direction: row;

    .ant-select {
      margin-right: 10px;
      min-width: 120px;
    }

    .ant-input {
      margin-right: 10px;
    }
  }
`;

const Loading = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
`;
