import { createAigwApp, AigwApp, bindExistingAccount } from '@/api/aigw';
import { Button, Modal, message, Space, Input, Radio } from 'antd';
import React, { useState } from 'react';
import styled from 'styled-components';
import { ModelAccountDetail } from './model-account-detail';

interface CreateModelAccountProps {
  trigger?: React.ReactElement;
  setAccountData?: (aigwApp: AigwApp) => void;
  resourceId: string;
  resourceType: 'Workspace' | 'Group' | 'App';
  name: string;
}

type BindType = 'create' | 'bind';

export function CreateModelAccount({
  setAccountData,
  resourceId,
  resourceType,
  name,
}: CreateModelAccountProps) {
  const [loading, setLoading] = useState(false);
  const [customCode, setCustomCode] = useState('');
  const [token, setToken] = useState('');
  const [bindType, setBindType] = useState<BindType>('create');

  const handleGenerate = async () => {
    if (!customCode.trim()) {
      message.error('请输入账号名称');
      return;
    }

    try {
      setLoading(true);
      const aigwApp = await createAigwApp(resourceType, resourceId, {
        app_code: customCode,
        name,
      });
      setAccountData(aigwApp);
      message.success('账号生成成功');
    } catch (error) {
      console.error('生成失败:', error);
      message.error('生成失败');
    } finally {
      setLoading(false);
    }
  };

  const handleBind = async () => {
    if (!customCode.trim() || !token.trim()) {
      message.error('请输入完整的账号信息');
      return;
    }

    try {
      setLoading(true);
      const aigwApp = await bindExistingAccount(resourceType, resourceId, {
        app_code: customCode,
        token: token,
      });
      setAccountData(aigwApp);
      message.success('账号绑定成功');
    } catch (error) {
      console.error('绑定失败:', error);
      message.error('绑定失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container>
      <RadioGroup>
        <Radio.Group
          value={bindType}
          onChange={(e) => setBindType(e.target.value)}
          style={{ marginBottom: 16 }}
        >
          <Radio.Button value="create">创建新账号</Radio.Button>
          <Radio.Button value="bind">绑定现有账号</Radio.Button>
        </Radio.Group>
      </RadioGroup>

      <ActionContainer>
        {bindType === 'create' ? (
          <Space>
            <Input
              placeholder="请输入账号code(英文)"
              value={customCode}
              style={{ width: 300 }}
              onChange={(e) => setCustomCode(e.target.value)}
            />
            <Button
              type="primary"
              onClick={handleGenerate}
              loading={loading}
            >
              生成账号
            </Button>
          </Space>
        ) : (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Input
              placeholder="请输入账号code"
              value={customCode}
              style={{ width: 300 }}
              onChange={(e) => setCustomCode(e.target.value)}
            />
            <Space>
              <Input
                placeholder="请输入token"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                style={{ width: 300 }}
              />
              <Button
                type="primary"
                onClick={handleBind}
                loading={loading}
              >
                绑定
              </Button>
            </Space>
          </Space>
        )}
      </ActionContainer>
    </Container>
  );
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  padding: 8px 0;
`;

const RadioGroup = styled.div`
  margin-bottom: 16px;
`;

const ActionContainer = styled.div`
  display: flex;
  margin-bottom: 16px;
`;