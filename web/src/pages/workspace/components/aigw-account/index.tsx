import { AigwApp } from '@/api/aigw';
import { Button, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import { ModelAccountDetail } from '@/pages/workspace/components/aigw-account/model-account-detail';
import { CreateModelAccount } from '@/pages/workspace/components/aigw-account/create-model-account';

interface AigwAccountProps {
  trigger?: React.ReactElement;
  resourceId: string;
  aigwApp?: AigwApp;
  resourceType: 'Workspace' | 'Group' | 'App';
  name: string;
  oneKeyBind: (id: string) => void;
}

export function AigwAccount({
  trigger,
  resourceId,
  resourceType,
  aigwApp,
  name,
  oneKeyBind,
}: AigwAccountProps) {
  const [open, setOpen] = useState(false);
  const [accountData, setAccountData] = useState<AigwApp>(aigwApp);

  useEffect(() => {
    setAccountData(aigwApp);
  }, [aigwApp]);

  const isEdit = !!accountData;
  console.log('accountData...', accountData, isEdit);

  const myTrigger = <Button type="link">{isEdit ? '管理模型账号' : '创建模型账号'}</Button>;


  return (
    <>
      {React.cloneElement(trigger ?? myTrigger, {
        onClick: () => setOpen(true),
      })}

      <Modal
        title="创建模型账号"
        open={open}
        destroyOnClose
        onCancel={() => setOpen(false)}
        footer={null}
        width={500}
      >
        {isEdit ? (
          <ModelAccountDetail
            accountData={accountData}
            oneKeyBind={oneKeyBind}
            setAccountData={setAccountData}
          />
        ) : (
          <CreateModelAccount
            setAccountData={setAccountData}
            resourceId={resourceId}
            resourceType={resourceType}
            name={name}
          />
        )}
      </Modal>
    </>
  );
}



// {
//   "code": "_langbase_hk_test3",
//   "name": "_hk_测试3",
//   "type": "project",
//   "app_id": "u5fq0j3py9olemmb",
//   "app_key": "6cwbt5722pbtfxkr663trlyhwjr5yx2j",
//   "authorization_header": "Bearer u5fq0j3py9olemmb.6cwbt5722pbtfxkr663trlyhwjr5yx2j",
//   "cost_code": "",
//   "cost_name": "",
//   "credit": {
//       "quota": 0
//   }
// }