import { AigwApp } from '@/api/aigw';
import { ModelProviderApi } from '@/api/model-provider';
import { copyToClipboard } from '@/utils/common';
import { formatAmount } from '@/utils/format';
import { Button, Space, Input, Typography, message, Popconfirm } from 'antd';
import React, { useState } from 'react';
import styled from 'styled-components';

const { Link } = Typography;

interface ModelAccountDetailProps {
  accountData: AigwApp;
  oneKeyBind: (id: string) => void;
  setAccountData: (data: AigwApp) => void;
}

export function ModelAccountDetail({ accountData, oneKeyBind, setAccountData }: ModelAccountDetailProps) {
  console.log('accountData...', accountData);
  const [loading, setLoading] = useState(false);
  const handleCopy = async (text: string) => {
    try {
      copyToClipboard(text);
      message.success('复制成功');
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败');
    }
  };

  const handleOneKeyBind = async () => {
    if (Number(accountData.quota) <= 0) {
      message.error('额度不足，无法绑定');
      return;
    }
    setLoading(true);
    await oneKeyBind(accountData.id);
    setLoading(false);
  }

  const handleDeleteBind = async () => {
    setLoading(true);
    await ModelProviderApi.deleteAigwAccount(accountData.id);
    message.success('删除成功');
    setLoading(false);
    setAccountData(null);
  }


  return (
    <InfoContainer>
      <InfoItem>
        <Label>app_code: 用于OA申请积分</Label>
        <Space>
          <TokenInput
            value={accountData.app_code}
            disabled
            style={{ width: '300px' }}
          />
          <Button type="link" onClick={() => handleCopy(accountData.app_code)} style={{ marginLeft: -60 }}>
            复制
          </Button>
          <Popconfirm title={<span>删除不会实际删除aigw账号，只是方便绑定新的aigw账号<br /><br></br><b>注意</b>：该操作不会影响已有大模型供应商模型账号绑定关系<br></br>如需更换绑定关系，请绑定新账号后再使用<span style={{ color: 'red' }}>一键绑定</span>功能</span>} onConfirm={handleDeleteBind}>  
            <Button type="link" danger style={{ marginLeft: -20 }} loading={loading}>
              删除/换绑
            </Button>
          </Popconfirm>
        </Space>
      </InfoItem>

      <InfoItem>
        <Label>token: 用于下面大模型绑定apiKey</Label>
        <Space>
          <TokenInput
            value="******"
            disabled
            style={{ width: '300px' }}
          />
          <Button type="link" onClick={() => handleCopy(accountData.token)} style={{ marginLeft: -60 }}>
            复制
          </Button>
          <Button type="link" onClick={handleOneKeyBind} style={{ marginLeft: -20 }} loading={loading}>
            一键绑定
          </Button>
        </Space>
      </InfoItem>

      <InfoItem>
        <Label>额度(RMB) <Link target="_blank" href="https://aigw.doc.nie.netease.com/21_%E5%BC%80%E5%8F%91%E6%8C%87%E5%8D%97/13_%E7%A7%AF%E5%88%86/1_%E7%A7%AF%E5%88%86Credit.html">详情</Link>:（额度决定了每月能请求大模型的量，1元 = 100积分）</Label>
        <Space>
          <TokenInput
            value={formatAmount(accountData.extra?.credit?.quota)}
            disabled
            style={{ width: '300px' }}
          />
          <Link target="_blank" href="https://hz.oa.netease.com/oaWebV4/index.jsp#/engine-flow/119139">
            去OA申请额度
          </Link>
        </Space>
      </InfoItem>
      <InfoItem>
        <Label>已消耗(RMB)</Label>
        <Space>
          <TokenInput
            value={formatAmount(accountData.extra?.credit?.usage)}
            disabled
            style={{ width: '300px' }}
          />
        </Space>
      </InfoItem>
      <InfoItem>
        <Label>余额(RMB)</Label>
        <Space>
          <TokenInput
            value={formatAmount(accountData.extra?.credit?.balance)}
            disabled
            style={{ width: '300px' }}
          />
        </Space>
      </InfoItem>
    </InfoContainer>

  );
}

const InfoContainer = styled.div`
  margin-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const Label = styled.div`
  color: rgba(0, 0, 0, 0.85);
`;

const TokenInput = styled(Input)`
  &[disabled] {
    color: rgba(0, 0, 0, 0.85);
    background-color: #f5f5f5;
    cursor: default;
    
    &:hover {
      border-color: #d9d9d9;
    }
  }
`; 