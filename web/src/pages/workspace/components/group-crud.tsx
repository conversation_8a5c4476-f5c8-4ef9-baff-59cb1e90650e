import { GroupApi } from '@api/index';
import Crud from '@components/crud';
import { GroupModel, GroupUpdateModel } from '@interface/index';
import { globalContext, GlobalStateKey } from '@store/index';
import { Empty } from 'antd';
import React, { useContext } from 'react';

function GroupCrud() {
  const { globalState } = useContext(globalContext);

  if (!globalState.workspace) {
    return <Empty />;
  }

  const config = {
    modelName: '业务组',
    api: GroupApi,
    // globalStateKey: 'groups' as GlobalStateKey,
    parent: {
      key: 'workspaceId', // 创建新的实体时，所属的父级id 字段
      id: globalState.workspace?.id, // 父级实体的 Id
    },
    schema: [
      {
        title: 'ID',
        dataIndex: 'id',
        editable: false,
        required: false,
      },
      {
        title: '业务组名',
        dataIndex: 'name',
        editable: true,
        required: true,
        notNull: true,
      },
      {
        title: '描述',
        dataIndex: 'description',
        editable: true,
        required: true,
        notNull: true,
      },
    ],
  };

  return <Crud<GroupModel, GroupUpdateModel> {...config} />;
}

export default GroupCrud;
