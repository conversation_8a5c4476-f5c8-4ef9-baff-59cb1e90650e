/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRequest } from 'ahooks';
import { Button, Card, Form, message, Tabs } from 'antd';
import styled from 'styled-components';

import { GroupApi, WorkspaceApi } from '@/api';
import { EnvSetting } from '@/components/env-setting';
import { LForm, LFormInput, LFormTextArea } from '@/components/form';
import { MidContainer } from '@/components/layout-container';
import { TokenSetting } from '@/components/TokenSetting';
import { useGlobalState } from '@/hooks/useGlobalState';

export function WorkspaceSettingPage() {
  const tabItems = [
    {
      key: '基础设置',
      label: '基础设置',
      children: <BasicSetting />,
    },
    {
      key: '环境变量',
      label: '环境变量',
      children: <EnvSetting type="workspace" />,
    },
    {
      key: 'API密钥',
      label: 'API密钥',
      children: <WorkspaceToken />,
    },
  ];

  return (
    <MidContainer>
      <Card>
        <Tabs items={tabItems} centered></Tabs>
      </Card>
    </MidContainer>
  );
}

function WorkspaceToken() {
  const { globalState } = useGlobalState();
  const { workspace } = globalState;

  if (!workspace) {
    return null;
  }

  const { data: groups } = useRequest(() => GroupApi.list(workspace.id), {
    refreshDeps: [workspace.id],
  });

  return (
    <TokenSetting
      id={workspace.id}
      type="workspace"
      subResources={groups}
      subType="group"
    ></TokenSetting>
  );
}

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

function BasicSetting() {
  const { globalState, fetchGlobalState } = useGlobalState();
  const { workspace } = globalState;

  if (!workspace) {
    return null;
  }

  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    const { ...others } = values;
    WorkspaceApi.update(
      {
        ...others,
      },
      workspace.id,
    )
      .then(() => {
        message.success('更新成功');
        fetchGlobalState('workspaces', workspace.id);
      })
      .catch((err) => {
        window.corona.warn('setting更新err', err);
        message.error(`更新失败 ${err?.message}`);
      });
  };

  return (
    <StyledBasicSetting>
      <div className="form-wrapper">
        <LForm form={form} onFinish={onFinish} initialValues={workspace} {...layout}>
          <LFormInput
            name="name"
            label="租户名称"
            rules={[{ required: true, message: '请输入租户名称' }]}
          />
          <LFormTextArea
            name="description"
            label="租户描述"
            rules={[{ required: true, message: '请输入租户描述' }]}
          />
          <LFormInput
            // @ts-ignore
            name={['createdBy', 'fullname']}
            disabled
            label="创建者"
            rules={[{ required: true, message: '请输入租户描述' }]}
          />
        </LForm>
      </div>
      <Button onClick={() => form.submit()} type="primary">
        保存
      </Button>
    </StyledBasicSetting>
  );
}

const StyledBasicSetting = styled.div`
  padding-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .form-wrapper {
    width: 500px;
    margin-bottom: 40px;
  }
`;
