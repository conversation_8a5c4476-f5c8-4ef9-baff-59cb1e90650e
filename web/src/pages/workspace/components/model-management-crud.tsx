import { FC, useState } from 'react';
import { LLMModelApi, tagMap } from '@/api/llm-model';
import { LLMModel, LLMModelCreate } from '@/interface/llm-model';
import Crud from '@/components/crud';
import { Input, Select, InputNumber, Switch, Tag } from 'antd';
import { useGlobalState } from '@/hooks/useGlobalState';
import JsonEditor from '@/components/json-editor';


const defaultValues = {
  enable: true,
  context: 128,
  speed: 3,
  providerName: 'openai',
  providerKind: 'openai',
  performance: 3,
  type: 'llm' as const,
  fee: {
    input: 1,
    output: 2,
    avg: 1.5,
  },
  config:{
    "temperature": {
      "range": [
        0,
        2
      ],
      "default": 0.6
    },
    "top_p": {
      "range": [
        0,
        1
      ],
      "default": 0.85
    },
    "presence_penalty": {
      "range": [
        -2,
        2
      ],
      "default": 0.0
    },
    "frequency_penalty": {
      "range": [
        -2,
        2
      ],
      "default": 0.0
    },
    "max_tokens": {
      "range": [
        1,
        4096
      ],
      "default": 1024
    }
  },
  tag: ['fast']  // 默认标签
};

const ModelManagementCrud: FC = () => {
  const [refreshKey, setRefreshKey] = useState(0);
  const { globalState } = useGlobalState();
  const { modelProviderList } = globalState;

  const schema = [
    {
      title: '模型名称',
      dataIndex: 'name',
      editable: true,
      required: true,
      notNull: true,
      width: 150,
    },
    {
      title: '别名',
      dataIndex: 'alias',
      editable: true,
      required: true,
      width: 150,
    },
    {
      title: '模型类型',
      dataIndex: 'type',
      hideInTable: true,
      editable: true,
      required: true,
      notNull: true,
      width: 120,
      valueOptions: [
        { label: 'LLM', value: 'llm' },
      ],
    },
    {
      title: '供应商名称',
      dataIndex: 'providerName',
      editable: true,
      required: true,
      notNull: true,
      width: 150,
      valueOptions: modelProviderList.map(item => ({
        label: item.description,
        value: item.kind
      })),
      filters: modelProviderList.map(item => ({
        text: item.description,
        value: item.kind
      })),
      onFilter: (value: string, record: LLMModel) => record.providerKind === value,
      onEditWatch: {
        watchFieldKeys: ['providerName'],
        callback: (watchFieldKey, watchFieldValue, updateForm) => {
          console.log('watchFieldKey', watchFieldKey, watchFieldValue);
          const provider = modelProviderList.find(item => item.kind === watchFieldValue);
          if (provider) {
            updateForm('providerKind', provider.kind);
          }
        }
      }
    },
    {
      title: '供应商',
      dataIndex: 'providerKind',
      hideInTable: true,
      editable: true,
      required: true,
      notNull: true,
      render: (value: string) => <span>{value}</span>,
      component: null,
      formItemProps: {
        style: { display: 'none' }
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      editable: true,
      required: true,
      width: 200,
      hideInTable: true,
    },
    {
      title: '标签',
      dataIndex: 'tag',
      editable: true,
      required: true,
      width: 150,
      filters: Object.entries(tagMap).map(([value, config]) => ({
        text: config.text,
        value: value
      })),
      onFilter: (value: string, record: LLMModel) => record.tag?.includes(value),
      render: (value: string[]) => {
        return value ? value.map(item => (
          <Tag 
            key={item} 
            color={tagMap[item]?.color}
          >
            {tagMap[item]?.text || item}
          </Tag>
        )) : null;
      },
      component: (
        <Select
          mode="multiple"
          placeholder="请选择标签"
          style={{ width: '100%' }}
          options={Object.entries(tagMap).map(([value, config]) => ({
            label: config.text,
            value
          }))}
        />
      ),
    },
    {
      title: '上下文长度',
      dataIndex: 'context',
      editable: true,
      required: true,
      width: 120,
      component: <InputNumber min={1} max={1000} />,
    },
    {
      title: '速度评分',
      dataIndex: 'speed',
      editable: true,
      required: true,
      width: 120,
      component: <InputNumber min={1} max={5} />,
    },
    {
      title: '性能评分',
      dataIndex: 'performance',
      editable: true,
      required: true,
      width: 120,
      component: <InputNumber min={1} max={5} />,
    },
    {
      title: '计费配置',
      dataIndex: 'fee',
      editable: true,
      required: true,
      width: 200,
      render: (value: any) => {
        return <p>输入: {value.input}<br />输出: {value.output}<br /></p>
      },
      component: <JsonEditor />,
    },
    {
      title: '模型配置',
      dataIndex: 'config',
      hideInTable: true,
      editable: true,
      required: true,
      width: 200,
      component: <JsonEditor />,
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      editable: true,
      required: true,
      width: 100,
      render: (value: boolean) => {
        return <Switch checked={value} disabled={true} />
      },
      component: <Switch />,
    },
  ];

  const config = {
    modelName: '模型管理',
    api: LLMModelApi,
    schema,
    showCopy: true,
    wrapInCard: false,
    scroll: { x: 1500 },
    defaultValues  // 添加默认值配置
  };

  return (
    <Crud<LLMModel, LLMModelCreate>
      {...config}
      clientPagination
      key={refreshKey}
    />
  );
};

export default ModelManagementCrud; 