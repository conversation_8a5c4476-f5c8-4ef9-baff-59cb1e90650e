import { But<PERSON>, Card, message, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

import { DefaultModelApi, ModelProviderApi } from '@/api/model-provider';
import Crud, { JsonType } from '@/components/crud';
import { MidContainer } from '@/components/layout-container';
import { useGlobalState } from '@/hooks/useGlobalState';
import { CrudAPI } from '@/interface';
import {
  CreateModelProviderBindingDto,
  DefaultModel,
  GlobalModel,
  GlobalModelProvider,
  ModelProviderBinding,
  UpdateDefaultModelDto,
} from '@/interface/model-provider';
import { AigwAccount } from './aigw-account';
import { AigwApp, getWorkspaceAigwApp, listGroupAigwApp, listWorkspaceAigwApp, listWorkspaceAigwApps } from '@/api/aigw';
import ModelManagementCrud from './model-management-crud';

export function ModelProvider() {
  const [globalModelProvider, setGlobalModelProvider] = useState<GlobalModelProvider[]>(
    [],
  );
  const [globalModel, setGlobalModel] = useState<GlobalModel[]>([]);

  useEffect(() => {
    ModelProviderApi.listGlobalModelProvider().then((res) => {
      setGlobalModelProvider(res);
    });

    DefaultModelApi.listGlobalModel().then((res) => {
      setGlobalModel(res);
    });
  }, []);
  
  console.log('globalModelProvider', globalModelProvider);

  const tabConfig = [
    {
      key: '模型供应商',
      label: '模型账号绑定',
      children: (
        <WorkspaceProviderBindingCrud globalModelProvider={globalModelProvider} />
      ),
    },
    {
      key: '默认模型',
      label: '默认模型配置',
      children: <DefaultModelCrud globalModel={globalModel} />,
    },
    {
      key: '模型管理',
      label: '模型管理',
      children: <ModelManagementCrud />,
    },
  ];

  return (
    <>
      <MidContainer innerStyle={{ width: '80%' }}>
        <ModelConfigPage>
          <Card title="">
            <Tabs items={tabConfig} centered></Tabs>
          </Card>
        </ModelConfigPage>
      </MidContainer>
    </>
  );
}


export function GroupAppModelProvider({ isApp }: { isApp?: boolean }) {
  const [globalModelProvider, setGlobalModelProvider] = useState<GlobalModelProvider[]>(
    [],
  );

  useEffect(() => {
    ModelProviderApi.listGlobalModelProvider().then((res) => {
      setGlobalModelProvider(res);
    });
  }, []);

  return (
  <MidContainer>
    <Card title="">
    <GroupProviderBindingCrud globalModelProvider={globalModelProvider} isApp={isApp} />
    </Card>
  </MidContainer>)

}

interface IWorkspaceProviderBindingCrudProps {
  globalModelProvider: GlobalModelProvider[];
  isApp?: boolean;
}

const getProviderCrudSchema = (globalModelProvider) => [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 200,
    editable: false,
    required: false,
  },
  {
    title: '模型供应商',
    dataIndex: 'providerKind',
    editable: false,
    required: true,
    notNull: true,
    valueOptions: globalModelProvider.map((it: GlobalModelProvider) => ({
      label: it.description,
      value: it.kind,
    })),
  },
  {
    title: '描述',
    dataIndex: 'description',
    editable: true,
    required: true,
    notNull: true,
    render: (value) => {
      if (value) {
        return value
      }
      return <span style={{ color: '#aaa'}}>请补充</span>
    }
  },
  {
    title: 'apiKey',
    dataIndex: 'apiKey',
    editable: true,
    required: true,
    notNull: true,
    render: (value, record) => {
      if (record.description) {
        return '*******'
      }
      return <span style={{ color: '#aaa'}}>请补充</span>
    }
  },
  {
    title: 'endpoint',
    dataIndex: 'endpoint',
    editable: true,
    required: true,
    notNull: false,
  },
  // {
  //   title: 'config',
  //   dataIndex: 'config',
  //   editable: true,
  //   required: true,
  //   notNull: true,
  // },
]

function WorkspaceProviderBindingCrud(props: IWorkspaceProviderBindingCrudProps) {
  const { globalState } = useGlobalState();
  const [aigwApp, setAigwApp] = useState<AigwApp>();
  const { workspace } = globalState;
  const [refreshKey, setRefreshKey] = useState(0);
  const { globalModelProvider } = props;
  const BindingApi = {
    list: ModelProviderApi.listWorkspaceModelProviderBinding,
    create: ModelProviderApi.createWorkspaceModelProviderBinding,
    update: ModelProviderApi.updateModelProviderBinding,
    remove: ModelProviderApi.deleteModelProviderBinding,
  } as CrudAPI<ModelProviderBinding, CreateModelProviderBindingDto>;

  // 获取当前租户的模型账号展示
  useEffect(() => {
    listWorkspaceAigwApp(workspace?.id as string).then((res) => {
      setAigwApp(res);
    });
  }, [workspace?.id]);

  const oneKeyBind = async (id: string) => {
    await ModelProviderApi.updateWorkspaceBindingWithAigw(id);
    message.success('绑定成功');
    setRefreshKey(refreshKey + 1);
  }

  const config = {
    modelName: '模型账号绑定',
    api: BindingApi,
    parent: {
      key: 'workspaceId',
      id: workspace?.id as string,
    },
    hiddenDelete: true,
    schema: getProviderCrudSchema(globalModelProvider),
    wrapInCard: false,
    hideAdd: true,
    moreAction: (
      <AigwAccount
        resourceId={workspace?.id as string}
        resourceType="Workspace"
        aigwApp={aigwApp}
        oneKeyBind={oneKeyBind}
        name={workspace?.name as string}
      />
    ),
  };

  return <Crud<ModelProviderBinding, CreateModelProviderBindingDto> {...config} key={refreshKey}/>;
}

function GroupProviderBindingCrud(props: IWorkspaceProviderBindingCrudProps) {
  const { globalState } = useGlobalState();
  const { globalModelProvider, isApp } = props;
  const [refreshKey, setRefreshKey] = useState(0);
  const [aigwApp, setAigwApp] = useState<AigwApp>();
  const BindingApi = {
    list: isApp ? ModelProviderApi.listAppModelProviderBinding : ModelProviderApi.listGroupModelProviderBinding,
    create: isApp ? ModelProviderApi.createAppModelProviderBinding : ModelProviderApi.createGroupModelProviderBinding,
    update: ModelProviderApi.updateModelProviderBinding,
    remove: ModelProviderApi.deleteModelProviderBinding,
  } as CrudAPI<ModelProviderBinding, CreateModelProviderBindingDto>;

  // 获取当前租户的模型账号展示
  useEffect(() => {
    listGroupAigwApp(globalState.group?.id as string).then((res) => {
      setAigwApp(res);
    });
  }, [globalState.group?.id]);

  const oneKeyBind = async (id: string) => {
    await ModelProviderApi.updateGroupBindingWithAigw(globalState.group?.id as string, id);
    message.success('绑定成功');
    setRefreshKey(refreshKey + 1);
  }

  const config = {
    modelName: '模型账号绑定',
    api: BindingApi,
    parent: {
      key: 'groupId', // 创建新的实体时，所属的父级id 字段
      id: globalState.group?.id as string, // 父级实体的 Id
    },
    // hiddenDelete: true, // 隐藏删除按钮
    schema: getProviderCrudSchema(globalModelProvider),
    wrapInCard: false,
    moreAction: (
      <AigwAccount
        resourceId={globalState.group?.id as string}
        resourceType="Group"
        aigwApp={aigwApp}
        oneKeyBind={oneKeyBind}
        name={`${globalState.workspace?.name}-${globalState.group?.name}`}
      />
    ),
    // moreAction: <FloatBtn type="primary">
    //   更多
    // </FloatBtn>,
  };

  return <Crud<ModelProviderBinding, CreateModelProviderBindingDto> {...config} />;
}

interface IDefaultModelCrudProps {
  globalModel: GlobalModel[];
}

function DefaultModelCrud(props: IDefaultModelCrudProps) {
  const { globalState } = useGlobalState();
  const { globalModel } = props;
  const [onEditModelType, setOnEditModelType] = useState<string>('');

  const modelTypes = Array.from(new Set(globalModel.map((it) => it.type)));

  const CrudApi = {
    list: DefaultModelApi.listWorkspaceDefaultModel,
    create: DefaultModelApi.updateWorkspaceDefaultModel,
    update: DefaultModelApi.updateWorkspaceDefaultModel,
    remove: () => {
      return Promise.resolve();
    }
  } as CrudAPI<DefaultModel, UpdateDefaultModelDto>;

  const config = {
    modelName: '默认模型',
    api: CrudApi,
    hiddenDelete: true, // 隐藏删除按钮
    parent: {
      key: 'workspaceId', // 创建新的实体时，所属的父级id 字段
      id: globalState.workspace?.id as string, // 父级实体的 Id
    },
    schema: [
      {
        title: '模型类型',
        dataIndex: 'modelType',
        editable: true,
        required: true,
        notNull: true,
        valueOptions: modelTypes.map((it) => ({
          label: it,
          value: it,
        })),
        onEditChange: (value: JsonType) => {
          setOnEditModelType(value as string);
        },
      },
      {
        title: '模型名',
        dataIndex: 'modelName',
        editable: true,
        required: true,
        notNull: true,
        valueOptions: globalModel
          .filter((it) => it.type === onEditModelType)
          .map((it) => ({
            label: `${it.name}`,
            value: it.name,
          })),
        onEditWatch: {
          watchFieldKeys: ['modelType'],
          callback: (_1: string, _2: JsonType, updateFieldFn: any) => {
            updateFieldFn('modelName', undefined);
          },
        },
      },
      {
        title: '模型供应商',
        dataIndex: 'providerKind',
        editable: false,
        required: true,
        notNull: true,
        disableEditInCreate: true,
        onEditWatch: {
          watchFieldKeys: ['modelName'],
          callback: (_1: string, modelName: JsonType, updateFieldFn: any) => {
            const model = globalModel.find((it) => it.name === (modelName as string));
            updateFieldFn('providerKind', model?.providerKind);
          },
        },
      },
    ],
    wrapInCard: false,
  };

  return <Crud<DefaultModel, UpdateDefaultModelDto> {...config} />;
}

const ModelConfigPage = styled.div`
  overflow-y: auto;
  height: 100%;
`;

const FloatBtn = styled(Button)`
  position: absolute;
  right: 0;
  top: 0;
`;