import {
  ApartmentOutlined,
  AppstoreAddOutlined,
  DashboardOutlined,
  SettingOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';

import MemberPage from '@/components/member';
import { MemberOfResource } from '@/interface/member';

import { createMenuPage, MenuItemConfig } from '../../components/pageLayout';
import GroupCrud from './components/group-crud';
import { ModelProvider } from './components/model-provider';
import { WorkspaceSettingPage } from './components/setting';
import { WorkspaceApps } from './components/workspace-apps';
import DataDashboard from '@/components/dashboard';
import WorkspaceMonitor from '../monitor/workspace-monitor';

export const menuConfig = [
  {
    title: '应用大盘',
    path: 'overview',
    Icon: DashboardOutlined,
    element: <DataDashboard />,
  },
  {
    title: '业务组管理',
    path: 'group',
    Icon: ApartmentOutlined,
    element: <GroupCrud />,
  },
  {
    title: '租户成员管理',
    path: 'member',
    Icon: UsergroupAddOutlined,
    element: <MemberPage resType={MemberOfResource.Workspace} />,
  },
  {
    title: '模型绑定设置',
    path: 'model',
    Icon: AppstoreAddOutlined,
    element: <ModelProvider />,
  },
  {
    title: '基础设置',
    path: 'setting',
    Icon: SettingOutlined,
    element: <WorkspaceSettingPage />,
  },
  {
    title: '全部应用',
    path: 'app',
    Icon: AppstoreAddOutlined,
    element: <WorkspaceApps />,
    hide: true,
  },
  {
    title: '监控',
    path: 'monitor',
    Icon: DashboardOutlined,
    element: <WorkspaceMonitor />,
    admin: ['workspace']
  },
] as MenuItemConfig[];

export function createWorkspaceMenuPage() {
  // 在此函数中不能使用 useGlobalState 等 hooks， 否则会导致路由切换异常 (这是一个辅助函数，非 React Component)

  return createMenuPage({
    path: 'workspace',
    menuConfig,
    retainQueries: ['workspaceId'], // 跳转菜单时保留的查询参数项
  });
}
