import { ShadowAppCard } from '@/components/resource-card';
import styled from 'styled-components';
export const AppCard = styled(ShadowAppCard)`
  user-select: none;
  text-align: center;

  .ant-card-body {
    padding: 10px;
    padding-left: 16px;
  }

  box-shadow: 0px 1px 4px 0px #e2e2e296;
    

  &:hover {
    &::before {
      content: '';
      position: absolute;
      display: block;
      height: 100%;
      width: 100%;
      right: 0;
      bottom: 0;
      background-color: rgba(205, 231, 255, 0.2);
      border-radius: 8px;
      z-index: 98;
      box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.08);
    }
  }

  &.selected::before {
    content: '';
    height: 100%;
    width: 100%;
    position: absolute;
    display: block;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    border-radius: 8px;
    z-index: 99;
    box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  }
`;
