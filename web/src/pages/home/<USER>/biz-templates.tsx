import { useRequest } from 'ahooks';
import {
  Tag,
} from 'antd';

import styled from 'styled-components';
import { AppApi } from '@/api/app';
import {
  AppTypeCnMap,
  IAppType,
} from '@/interface';
import { groupIdMap } from '@/constants';
import { AvatarIcon } from '@/pages/app/workflow/react-node/base-node';
import { env } from '@/utils/common';
import { AppTypeAvatarMap, AppTypeTagColorMap } from '@/interface';
import { AppCard } from './app-card';


const TemplateCard = styled(AppCard)`
  margin-bottom: 20px;
  .title {
    height: 45px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    a {
      z-index: 99;
      margin-left: auto;
    }
    .avatar-icon {
      width: 30px;
      flex-shrink: 0;
      img {
        width: 30px;
        height: 30px;
      }
    }
    h3 {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      max-width: 200px;
      font-size: 16px;
      margin: 0;
    }
  }
  .desc {
    text-align: left;
    height: 40px;
    color: #999;
    margin: 10px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
`;

export function BizTemplates({ selected, onClick }: { selected: any, onClick: any }) {
  const groupId = groupIdMap[env];
  const { data: apps = [] } = useRequest(() => AppApi.listAppsByGroup(groupId, {
    pageSize: 300
  }).then(res => {
    if (res?.items?.length) {
      return res.items;
    }
    return []
  }), {
    // refreshDeps: [type],
    // cacheKey: type,
    staleTime: 60000,
  });

  const handleClick = (item) => {
    if (onClick) {
      onClick(item);
    }
  }

  return <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'flex-start', columnGap: '40px', flexWrap: 'wrap' }}>
    {apps.map((item, idx) => (
      <TemplateCard onClick={() => handleClick(item)} key={idx} style={{ textAlign: 'left', height: 'auto', flexBasis: 354, flexGrow: 0 }} className={selected?.id === item.id ? 'selected' : ''}>
        <div className='title'>
          <AvatarIcon icon={item?.extInfo?.avatar || AppTypeAvatarMap[item.type]} style={{ borderRadius: 4 }} ></AvatarIcon>
          <h3>{item.name}</h3>
          <a target='_blank' href={`/preview/${item.type === IAppType.Workflow ? 'workflow-playground' : item.type}?appId=${item.id}`} onClick={() => console.log("123113")}>查看示例</a>
        </div>
        <p className='desc'>{item.description}</p>
        <Tag color={AppTypeTagColorMap[item.type]}>{AppTypeCnMap[item.type]}</Tag>
      </TemplateCard>
    ))}
  </div>
}