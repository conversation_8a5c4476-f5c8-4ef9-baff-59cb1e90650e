import { useRequest } from 'ahooks';
import { useState } from 'react';
import { Empty } from 'antd';

import { WorkspaceApi } from '@/api';
import { useGlobalState } from '@/hooks/useGlobalState';
import { IntelligentChat } from '@/pages/preview/intelligent-chat';
import { getDefaultModel } from '@/utils/common';


interface ExploreProps {
  style?: React.CSSProperties;
}


export const ExploreChat = (props: ExploreProps) => {
  let { style = {} } = props;
  const [basicAppID, setBasicAppID] = useState<string>();
  style = {
    width: '100%',
    border: '1px solid #eee',
    borderRadius: '8px',
    height: '95vh',
    ...style,
  };
  const { globalState } = useGlobalState();
  const { workspace } = globalState;

  useRequest(() => WorkspaceApi.getBasicApp(workspace!.id), {
    ready: !!workspace,
    onSuccess: (res) => {
      setBasicAppID(res.id);
    },
    onError: (err) => {
      console.log("errrrr", err)
      setBasicAppID('');
    },
    refreshDeps: [workspace],
  });

  if (!basicAppID) {
    return <Empty style={{ width: '100%', marginTop: '100px' }} description={<span style={{ color: '#aaa' }}>还没有应用哦~</span>} />;
  }
  const appID = basicAppID

  return <IntelligentChat key={appID} style={{ ...style, marginRight: "24px" }} appId={appID} showModelSelector={appID === basicAppID} defaultModel={getDefaultModel().modelName} />
};
