import { PlusCircleTwoTone } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import {
  Button,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Select,
  Typography
} from 'antd';

import { ChangeEvent, useCallback, useMemo, useState } from 'react';
import styled from 'styled-components';
import { useAdmin } from '@/hooks/useAdmin';
import { AppApi } from '@/api/app';
import { ShadowAppCard } from '@/components/resource-card';
import { useGlobalState } from '@/hooks/useGlobalState';
import { usePathQuery } from '@/hooks/useQuery';
import {
  AppCreateModel,
  AppTemplate,
  IAppType,
} from '@/interface';
import { BizTemplates } from './biz-templates';
import { getDefaultConfig, getDefaultTemplateConfig } from '@/utils/model-helper';
import { templateIdMap } from '@/constants';
import { env } from '@/utils/common';
import DynamicField from '@/components/dynamic-field';
import { AppTypeAvatarMap } from '@/interface';
import Uploader from '@/pages/app/workflow/react-node/imageUploader';
import { AppTypeCards } from './type-cards';
import { ChannelsApi } from '@/api';
import { MultipleConfigApi } from '@/api/multiple-setting';

const CreatingAppCardInnerTitle = styled.div`
  font-weight: 900;
  color: #454545;
`;

interface ICreateAppCardProps {
  defaultGroup?: string;
  workspace?: any;
  groups?: any[];
  render?: any;
}

export const CreateAppCard = (props: ICreateAppCardProps) => {
  const { pushAddQuery } = usePathQuery();
  const { defaultGroup, render, workspace, groups } = props;
  const [createMethod, setCreateMethod] = useState<'type' | 'biz-template' | 'template'>('type');
  const [open, setOpen] = useState(false);
  const [template, setTemplate] = useState<any>();
  const [files, setFiles] = useState<any>();
  const [templateConfigForm] = Form.useForm();
  const isWorkspaceAdmin = useAdmin('workspace');
  const enableSubType = workspace?.description === '音乐事业部';
  const [messageApi, contextHolder] = message.useMessage();
  //@ts-ignore
  const [app, setApp] = useState<AppCreateModel & { groupID: string; template?: AppTemplate }>({
    type: IAppType.AgentCompletion,
    groupID: defaultGroup
  });

  // 发布渠道
  const [channel, setChannel] = useState();


  const enableTemplate = useMemo(() => isWorkspaceAdmin && templateIdMap[env] === app.groupID, [isWorkspaceAdmin, app.groupID]);

  console.log('env...', env, enableTemplate);

  const templateConfigs = [{
    name: '云音乐笔记',
    match: '笔记',
    config: [{
      type: 'Uploader',
      formItemProps: {
        label: '笔记预览图片',
        name: 'previewImages',
        rules: [{
          required: true,
          message: '请上传笔记预览图',
        }]
      },
      componentProps: {
        max: 3,
        desc: '上传图片'
      }
    }]
  }]

  const appList = [IAppType.AgentCompletion, IAppType.AgentConversation, IAppType.VirtualHuman, IAppType.AgentWorkflow, IAppType.Workflow];
  if (enableTemplate) {
    appList.push(IAppType.Evaluator);
  }

  const { run: createApp, loading } = useRequest(
    async () => {
      const type = template?.id ? template.type : app.type;
      // 下面这个defaultConfig不会使用，因为template最后会覆盖，但是defaultConfig有些结构是入参校验需要通过的，所以下面一行保留即可
      const defaultConfig: any = getDefaultConfig(type, app);

      const defaultTemplate = getDefaultTemplateConfig(type);
      if (defaultTemplate || template?.id) {
        app.type = type;
        if (template?.name) {
          app.subType = template.name;
        }
        defaultConfig.templateId = defaultTemplate || template?.id;
      }

      const res = await AppApi.createApp(
        {
          ...app,
          // @ts-ignore
          groupID: defaultGroup || app.groupID,
          isTemplate: enableTemplate,
          config: {
            ...defaultConfig,
            businessConfig: templateConfigForm.getFieldsValue(true),
          },
        },
        defaultGroup || app.groupID
      );

      // 有channel配置的话需要update默认setting的channel
      if (app.type === IAppType.VirtualHuman && channel) {
        const { items: settingList } = await MultipleConfigApi.getSettingList({ appId: res?.id });
        const defaultSetting = settingList?.[0] || {};
        const setting = await MultipleConfigApi.updateSetting({
          appId: res.id,
          ...defaultSetting,
          extInfo: JSON.stringify({
            channel: channel,
          }),
        });
      }

      return res;
    },
    {
      manual: true,
      onSuccess: (data) => {
        setOpen(false);
        messageApi.open({
          type: 'success',
          content: '应用创建成功',
        });
        pushAddQuery('/app/dev', {
          appId: data.id,
          type: data.type,
        });
      },
    },
  );

  const { data: channelList } = useRequest(async () => {
    try {

      const res = await ChannelsApi.getChannels({ appId: '' });

      return res?.channels?.map(channel => {
        return {
          label: channel?.name,
          value: channel?.type

        }
      })
    } catch (error) {
      console.log('errorerror', error);
    }
  }, {
  });

  const onSubmit = useCallback(() => {
    if (!app.name) {
      messageApi.open({
        type: 'error',
        content: '应用名称不能为空',
      });
      return;
    }
    if (!app.type && !app.template?.id) {
      messageApi.open({
        type: 'error',
        content: '请选择应用类型或者模版',
      });
      return;
    }
    if (!defaultGroup && !app.groupID) {
      messageApi.open({
        type: 'error',
        content: '业务组不能为空',
      });
      return;
    }
    if (app.type === IAppType.Workflow) {
      if (!(app as any)?.workflowId) {
        messageApi.open({
          type: 'error',
          content: '工作流英文名不能为空',
        });
        return;
      }
      if (!/^[a-zA-Z][\w\-]*$/.test(app?.workflowId || '')) {
        messageApi.open({
          type: 'error',
          content: '英文名只能为英文、数字、横线和下划线，且必须英文开头',
        });
        return;
      }
    }
    createApp();
  }, [app]);

  const onTypeClick = useCallback(
    (type: IAppType) => {
      !files?.key && setFiles({
        url: AppTypeAvatarMap[type],
      })
      setApp({
        ...app,
        type
      });
    },
    [app],
  );

  const onNameInput = useCallback(
    (e: ChangeEvent) => {
      setApp({
        ...app,
        // @ts-ignore
        name: e.target.value,
      });
    },
    [app, setApp],
  );

  const onDescInput = useCallback(
    (e: ChangeEvent) => {
      setApp({
        ...app,
        // @ts-ignore
        description: e.target.value,
      });
    },
    [app, setApp],
  );

  const onChannelChange = useCallback(val => {
    // setApp({
    //   ...app,
    //   extInfo: {
    //     ...(app?.extInfo || {}),
    //     channel: val
    //   }
    // });
    setChannel(val);
  }, [app, setApp]);

  const onAvatarUpload = useCallback(
    (files) => {
      setFiles(files);
      setApp({
        ...app,
        extInfo: {
          avatar: files?.url
        }
        // @ts-ignore
        // description: e.target.value,
      });
    },
    [app, setApp],
  );


  const options = useMemo(
    () => (groups ?? [])
      .filter(g => g.role !== 'external_user')
      .map((g) => ({ value: g.id, label: g.name })),
    [groups],
  );

  const onGroupSelected = useCallback(
    (groupID: string) => {
      setApp({
        ...app,
        groupID,
      });
    },
    [app, setApp],
  );

  const onWorkflowIdChange = useCallback(
    (ev: any) => {
      setApp({
        ...app,
        workflowId: ev?.target?.value,
      });
    },
    [app, setApp],
  );

  const onCreateMethodChange = (e: any) => {
    setTemplate({});

    setCreateMethod(e.target.value);
  }

  return (
    <>
      {contextHolder}
      {render ? render(() => setOpen(true)) :
        <ShadowAppCard
          style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}
          onClick={() => setOpen(true)}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <PlusCircleTwoTone style={{ fontSize: '40px', color: '#959595' }} />
            <span
              style={{
                fontSize: '16px',
                color: '#959595',
                marginTop: '12px',
                display: 'block',
              }}
            >
              创建应用
            </span>
          </div>
        </ShadowAppCard>
      }

      <Modal
        width={800}
        open={open}
        maskClosable
        footer={null}
        onCancel={() => setOpen(false)}
        afterOpenChange={() => setFiles(null)}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <CreatingAppCardInnerTitle>给您的应用取一个名字：</CreatingAppCardInnerTitle>
            <div style={{ display: 'flex', height: 36, flexDirection: 'row', gap: '10px' }}>
              <Uploader permanent={true} height={36} width={36} desc='上传图标' value={files} onChange={onAvatarUpload} uploadProps={{
                maxCount: 1,
                showUploadList: false
              }} />
              <Input onChange={onNameInput} placeholder='请输入应用名称' />
            </div>
          </div>
          {!defaultGroup && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
              <CreatingAppCardInnerTitle>请选择创建业务组：</CreatingAppCardInnerTitle>
              <Select
                options={options}
                onSelect={onGroupSelected}
                showSearch
                filterOption={(input, option) =>
                  (option?.label?.toString().toLowerCase() || '').includes(input.toLowerCase())
                }
                placeholder="请选择或搜索组织"
              />
            </div>
          )}
          {(createMethod === 'type' && app.type === IAppType.Workflow || createMethod === 'biz-template' && template?.type === IAppType.Workflow) && (
            <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
              <CreatingAppCardInnerTitle>请输入英文名:</CreatingAppCardInnerTitle>
              <Input
                onChange={onWorkflowIdChange}
                placeholder="唯一标识（支持英文、数字、横线和下划线）"
              ></Input>
            </div>
          )}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <CreatingAppCardInnerTitle >应用功能介绍：</CreatingAppCardInnerTitle>
            <Input.TextArea onChange={onDescInput} placeholder="请简短介绍你的功能/使用场景" />
          </div>

          {/* {app.type === IAppType.VirtualHuman && <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <CreatingAppCardInnerTitle >发布渠道：</CreatingAppCardInnerTitle>
            <Select onChange={onChannelChange} allowClear options={[{ label: '暂不选择', value: '' }].concat(channelList || [])} />
            <Typography.Text type="secondary">发布渠道一旦选择不可更改</Typography.Text>
          </div>} */}

          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            <Radio.Group onChange={onCreateMethodChange} value={createMethod}>
              {enableTemplate ? <Radio.Button value="template">模板应用类型</Radio.Button> :
                <>
                  <Radio.Button value="type">基础应用类型</Radio.Button>
                  {enableSubType && <Radio.Button value="biz-template">业务应用类型</Radio.Button>}
                </>
              }
            </Radio.Group>
            {
              (createMethod === 'type' || createMethod === 'template') && <AppTypeCards appList={appList} onTypeClick={onTypeClick} app={app} />
            }
            {
              createMethod === 'biz-template' && <BizTemplates selected={template} onClick={(template) => {
                !files?.key && setFiles({
                  url: AppTypeAvatarMap[template.type],
                })
                setTemplate(template)
              }} />
            }
          </div>
          <Form form={templateConfigForm}>
            {
              templateConfigs.find(config => template?.name?.includes(config.match))?.config &&
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <CreatingAppCardInnerTitle>扩展配置：</CreatingAppCardInnerTitle>
                {
                  templateConfigs.find(config => template?.name?.includes(config.match))?.config.map(item => {
                    return <Form.Item {...item.formItemProps}>
                      <DynamicField type={item.type} onChange={(value) => {
                        templateConfigForm.setFieldValue(item.formItemProps.name, value)
                      }} componentProps={item.componentProps} />
                    </Form.Item>
                  })
                }
              </div>
            }
          </Form>
          <div
            style={{ display: 'flex', flexDirection: 'row', justifyContent: 'flex-end' }}
          >
            <Button style={{ width: '80px' }} loading={loading} onClick={onSubmit} type="primary">
              创建
            </Button>
          </div>
        </div>
      </Modal></>
  );
};

const Hint = styled.div`
  color: #35a1ff;

  &:hover {
    cursor: pointer;
  }
`;
