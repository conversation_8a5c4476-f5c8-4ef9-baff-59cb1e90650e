import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
import classNames from 'classnames';

import { WorkspaceApi } from '@/api';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppDetailModel, AppModel, IAppType } from '@/interface';
import { styled } from 'styled-components'; // fixed import statement
import { AppApi } from '@/api/app';
import { AppAvatar } from '@/components/resource-card';
import { CommentOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Card, Spin, Tooltip, Empty } from 'antd';
import { Conversation } from '@/pages/app/components/agent-dev/conversation';
import { AgentParamsRender } from '@/components/agent-params-render/complete';
import { Completion } from '@/pages/preview/agent-complete';

interface ExploreProps {
  style?: React.CSSProperties;
}

const AppListStyled = styled.div`
  padding-top: 16px;
  padding-bottom: 16px;
  width: 200px;
  display: flex;
  flex-direction: column;
  margin-left: 24px;
  margin-right: 24px;
  color: #333;

  .app-list-box {
    row-gap: 2px;
    flex-direction: column;
    display: flex;
  }

  .app-list-item {
    padding: 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    column-gap: 8px;
    flex-direction: row;
    color: #5c5c5c;

    .app-list-item-name {
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .app-list-item-avatar {
    }

    &:hover {
      background-color: #e3e2e2;
    }
  }

  /* .app-list-item:hover {
    background-color: #ddd;
  } */

  .app-list-chat {
    font-weight: 600;
    font-size: 16px;
    padding-left: 4px;
    padding: 8px;
    border-radius: 8px;
    display: flex;
    column-gap: 4px;
    padding-left: 8px;
    
    &:hover {
      background-color: #ddd;
    }
  }

  .app-list-hr {
    width: 100%;
    margin-top: 16px;
    margin-bottom: 8px;
  }

  .selected {
    /* border: 1px solid #f1f1f1; */
    /* box-shadow: 6px 6px 8px -8px rgba(0, 0, 0, 0.12); */
    background-color: rgba(69, 121, 244, 0.1) !important;
    color: #1654c8 !important;
  }
`

interface AppListProps {
  style?: React.CSSProperties;
  apps: AppModel[];
  onItemClick?: (app?: AppModel) => void;
  selectedAppID?: string;
}

export const AppList = (props: AppListProps) => {
  const { style = {}, onItemClick = () => {}, selectedAppID } = props;
  let { apps } = props;
  // apps = apps.filter((app) => app.type === IAppType.AgentConversation);

  return (
    <AppListStyled>
      <div className="app-list-box">
        {apps.map((app) => {
          return (
            <div onClick={() => onItemClick(app)} className={classNames({
              'selected': selectedAppID === app.id,
              'app-list-item': true,
            })} key={app.id}>
              <AppAvatar className="app-list-item-avatar" app={app} width={24} height={24} />
              <div className="app-list-item-name">
                {app.name}
              </div>
            </div>)
        })}
      </div>
    </AppListStyled>
  )
}

const ReadmeStyled = styled(Card)`
  width: 30%;
  min-width: 200px;

  .ant-card-body {
    display: flex;
    flex-direction: column;
    row-gap: 48px;
    row-gap: 48px; 
  }

  .ant-card-body::before {
    content: none
  }

  .ant-card-body::after {
    content: none
  }

  span.title {
    display: inline-block;
    font-weight: 500;
    font-size: 1.05rem;
  }
`

interface ReadmeProps {
  style?: React.CSSProperties;
  appID: string
}

const Readme = (props: ReadmeProps) => {
  const { style = {}, appID } = props;
  const [app, setApp] = useState<AppDetailModel>();

  useRequest(() => AppApi.getAppDetail(appID), {
    refreshDeps: [appID],
    onSuccess: (res) => {
      setApp(res);
    }
  }) 

  if (!app?.description && !app?.querySample) {
    return null;
  }

  return (
    <ReadmeStyled
    title='README'
    style={style}>
      {/* <TokenCurlCommandCard appID={app.id} appType={app.type} agentConfig={app.config} /> */}
      {
        app?.description && (
          <div>
            <span className="title">描述：</span>
            <div>
              {app?.description}
            </div>
          </div>
        )
      }
      {
        app?.querySample && (
          <div>
            <span className='title'>使用样例：</span>
            <div style={{whiteSpace: 'pre-wrap'}}>
              {app?.querySample}
            </div>
          </div>
        )
      }
    </ReadmeStyled>
  )
}

const ConversationPanelStyled = styled(Card)`
  display: flex;
  flex-direction: column;

  .ant-card-body {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    flex-grow: 1;
  }

`

interface ConversationChatPanelProps {
  style?: React.CSSProperties;
  appID: string;
}

const ConversationChatPanel = (props: ConversationChatPanelProps) => {
  const { style = {}, appID } = props;
  const [inputParams, setInputParams] = useState<any>();
  const [app, setApp] = useState<AppDetailModel>();
  const [random, setRandom] = useState<number>(new Date().getTime());

  useEffect(() => {
    setInputParams(undefined);
  }, [appID])

  useRequest(() => AppApi.getAppDetail(appID), {
    refreshDeps: [appID],
    onSuccess: (res) => {
      setApp(res);
    }
  })

  if (!app) {
    return null;
  }

  const refresh = () => {
    setRandom(new Date().getTime());
    setInputParams(undefined);
  }

  const onStartConversation = (params: any) => {
    setInputParams(params);
  }

  const refreshBtn = (
    <Tooltip title="重置会话">
      <Button
        onClick={refresh}
        disabled={
          !inputParams && (app.config.paramsInPrompt ?? []).length !== 0
        }
      >
        <ReloadOutlined />
      </Button>
    </Tooltip>
  )

  return (
    <ConversationPanelStyled
      style={style}
      extra={refreshBtn}
    >
      {
        inputParams || (app.config.paramsInPrompt ?? []).length === 0
          ? (
            <Conversation key={`${appID}-${random}`} style={{ width: "100%", height: "100%" }} explore appId={appID} inputParams={inputParams} configId={app.app_config_id} />
          )
          : (
            <div style={{ width: "50%", minWidth: '400px', backgroundColor: 'rgba(69, 121, 244, 0.1)', padding: '24px', borderRadius: "8px" }}>
              <AgentParamsRender
                style={{ width: '100%' }}
                paramsInPrompt={app.config.paramsInPrompt ?? []}
                onCommit={onStartConversation}
                labelAlign="left"
                commitBtnName="开始对话"
                commitBtnIcon={<CommentOutlined rev={undefined} />}
              />
            </div>
          )
      }
    </ConversationPanelStyled>
  )

}

// const CompletionPanelStyled = styled(Card)`
//   width: 100%;
// `

interface CompletionPanelProps {
  style?: React.CSSProperties;
  appID: string
}

const CompletionPanel = (props: CompletionPanelProps) => {
  const { style = {}, appID } = props;
  const [app, setApp] = useState<AppDetailModel>();
  useRequest(() => AppApi.getAppDetail(appID), {
    refreshDeps: [appID],
    onSuccess: (res) => {
      setApp(res);
    } 
  })

  if (!app) {
    return <Spin />
  }

  // return <CompletionPanelStyled>
    return <Completion style={style} app={app} internal />
  // </CompletionPanelStyled>
}


const ExploreStyled = styled.div`
  background-color: rgb(248, 250, 252);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: stretch;
  height: 100%;
`

export const Explore = (props: ExploreProps) => {
  let { style = {} } = props;
  const [selectedApp, setSelectedApp] = useState<AppModel>();
  const [basicAppID, setBasicAppID] = useState<string>();
  const [appList, setAppList] = useState<AppModel[]>([]);
  style = {
    width: '100%',
    border: '1px solid #eee',
    borderRadius: '8px',
    ...style,
  };
  const { globalState } = useGlobalState();
  const { workspace } = globalState;

  useRequest(() => WorkspaceApi.getBasicApp(workspace!.id), {
    ready: !!workspace,
    onSuccess: (res) => {
      setBasicAppID(res.id);
    },
  });

  useRequest(() => AppApi.listAppsByWorkspace(workspace!.id, { appType: [IAppType.AgentCompletion, IAppType.AgentConversation] }), {
    ready: !!workspace,
    onSuccess: (res) => {
      const apps = res.items.filter((app) => app.type !== IAppType.Workflow);
      setAppList(apps);
      if (apps.length > 0) {
        setSelectedApp(apps[0]);
      }
    },
    onError: (err) => {
      console.log("errrrr", err)
      setBasicAppID('');
    },
    refreshDeps: [workspace]
  })

  if (!basicAppID) {
    return <Empty style={{ width: '100%', marginTop: '100px'}} description={<span style={{ color: '#aaa'}}>还没有应用哦~</span>} />
  }

  const appID = selectedApp ? selectedApp.id : basicAppID
  
  console.log("appId", appID, basicAppID, selectedApp);

  return (
    <ExploreStyled>
      {!appList?.length && <Empty style={{ width: '100%', marginTop: '100px'}} description={<span style={{ color: '#aaa'}}>还没有应用哦~</span>} />}
      <AppList selectedAppID={selectedApp?.id} style={style} apps={appList} onItemClick={setSelectedApp} />
      {
        appID !== basicAppID &&
        appID === selectedApp?.id &&
        selectedApp.type === IAppType.AgentConversation &&
        <ConversationChatPanel style={{...style, flexGrow: '3'}} appID={appID} />
      }
      {
        appID !== basicAppID &&
        appID === selectedApp?.id &&
        selectedApp.type === IAppType.AgentCompletion &&
        <CompletionPanel style={{...style}} appID={appID} />
      }
      {
        appID && <Readme style={{ marginLeft: "8px", flexGrow: '1'}} appID={appID} />
      }
    </ExploreStyled>
  );
};
