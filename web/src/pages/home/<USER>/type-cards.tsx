

import {
  AppCreate<PERSON><PERSON>l,
  AppTypeAvatarMap,
  AppTypeCardColorMap,
  IAppType,
} from '@/interface';
import styled from 'styled-components';
import { AppCard } from './app-card';

const AppCreatingBox = styled(AppCard)`
  cursor: pointer;
  border: 1px solid #eee;
  border-radius: 8px;
  background: #fff;
 
  // 使用配置的颜色
  ${Object.entries(AppTypeCardColorMap).map(([type, colors]) => `
    &[data-type="${type}"] {
      &:hover {
        border-color: ${colors.primary};
      }
      
      &.selected {
        border-color: ${colors.primary};
        background: ${colors.background};
      }

      &:hover::before {
        background-color: ${colors.background};
      }
    }
  `)}
  .card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 8px;
  }

  .header {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .icon-wrapper {
    flex-shrink: 0;
    line-height: 20px;
    display: flex;
    img {
      border-radius: 6px;
      width: 22px;
      height: 22px;
    }
  }

  .title {
    font-size: 15px;
    color: #333;
    font-weight: bold;
    text-align: left;
    margin: 0;
  }

  .desc {
    text-align: left;
    font-size: 12px;
    color: #666;
    height: auto;
    margin: 0;
    line-height: 1.5;
  }
`;

const AppTypeSection = styled.div`
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  margin-bottom: 14px;

  .section-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .cards-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;
  }
`;

export const AppTypeCard = (props: {
  appType: IAppType;
  onClick?: () => void;
  className?: string;
}) => {
  const { appType, onClick, className } = props;

  const [name, description] = (() => {
    switch (appType) {
      case IAppType.AgentCompletion:
        return ['文本生成', '用于文本生成一次性任务的。例如：文本翻译、舆情打标、文本改写'];
      case IAppType.AgentConversation:
        return ['聊天助手', '简单配置基于大模型的对话机器人。例如：普通问答机器人'];
      case IAppType.VirtualHuman:
        return ['虚拟人', '具备推理与自主工具调用的虚拟人。例如：云音乐虚拟人'];
      case IAppType.AgentWorkflow:
        return ['对话流', '基于对话的复杂工作流编排任务。例如：智能客服'];
      case IAppType.StoryLine:
        return ['剧情型', '可以自定义剧情镜头和结局。适用于活动、游戏、虚拟人等。'];
      case IAppType.Evaluator:
        return ['评估器', '可以自定义评估器。适用于活动、游戏、虚拟人等。'];
      case IAppType.Workflow:
      default:
        return ['工作流', '面向单轮的异步自动自动化编排任务。例如：多分镜合成，AI音视频制作'];
    }
  })();
  
  return (
    <AppCreatingBox className={className} onClick={onClick} data-type={appType}>
      <div className="card-content">
        <div className="header">
          <div className="icon-wrapper">
            <img src={AppTypeAvatarMap[appType]} alt={name} />
          </div>
          <div className="title">{name}</div>
        </div>
        <div className="desc">{description}</div>
      </div>
    </AppCreatingBox>
  );
};

export const AppTypeCards = ({ appList, onTypeClick, app }: { appList: IAppType[], onTypeClick: (type: IAppType) => void, app: AppCreateModel, isTemplate?: boolean }) => {
  return <>
  <AppTypeSection>
    <div className="section-title">一般需求</div>
    <div className="cards-container">
      {appList.filter(type =>
        type !== IAppType.Workflow &&
        type !== IAppType.AgentWorkflow &&
        type !== IAppType.StoryLine
      ).map(type => (
        <AppTypeCard
          key={type}
          onClick={() => onTypeClick(type)}
          className={app.type === type ? 'selected' : ''}
          appType={type}
        />
      ))}
    </div>
  </AppTypeSection>

  <AppTypeSection>
    <div className="section-title">进阶需求</div>
    <div className="cards-container">
      {appList.filter(type =>
        type === IAppType.Workflow ||
        type === IAppType.AgentWorkflow ||
        type === IAppType.StoryLine
      ).map(type => (
        <AppTypeCard
          key={type}
          onClick={() => onTypeClick(type)}
          className={app.type === type ? 'selected' : ''}
          appType={type}
        />
      ))}
    </div>
  </AppTypeSection>
</>
}

