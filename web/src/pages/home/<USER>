import { Spin } from 'antd';
import { useRef } from 'react';
import styled from 'styled-components';

import { LayoutContainer } from '@/components/layout-container';
import { useGlobalState } from '@/hooks/useGlobalState';

import { AppList } from '@/components/app-list';
import { TopBar } from '@/components/top-bar';

export function HomePage({ isAll }: { isAll?: boolean}) {
  const { globalState } = useGlobalState();
  const { groups } = globalState;
  const containerRef = useRef<HTMLDivElement>();

  if (!groups) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', marginTop: '260px' }}>
        <Spin />
      </div>
    );
  }


  return (
    // @ts-ignore
    <LayoutContainer ref={containerRef}>
      <TopBar style={{ paddingLeft: '15%' }} left={<GroupTitle>{isAll ? '所有应用' : '我的应用'}</GroupTitle>} />
      <Main>
        <AppList isMine={!isAll}></AppList>
      </Main>
    </LayoutContainer>
  );
}

const GroupTitle = styled.div`
      user-select: none;  
      display: flex;
      align-items: center;
      min-width: 160px;
      justify-content: start;
      gap: 10px;
      font-size: 18px;
      margin: 0 20px 0 10px;
      `;

const Main = styled.div`
      width: 100%;
      height: calc(100vh - 90px);
      padding: 20px;
      box-sizing: border-box;
      overflow-y: auto;
      `;