import { useRequest } from 'ahooks';
import { Col, Pagination, Row } from 'antd';
import { CSSProperties, useState } from 'react';

import { GroupApi } from '@/api';
import { GroupCard } from '@/components/resource-card';
import { useGlobalState } from '@/hooks/useGlobalState';
import { GroupModel } from '@/interface';

interface IGroupListProps {
  style?: CSSProperties;
}

const PAGE_SIZE = 300;

export function GroupList(props: IGroupListProps) {
  const { style } = props;
  const { globalState } = useGlobalState();
  const { workspace = { id: '' }, groups: _groups } = globalState;
  const [groups, setGroups] = useState<GroupModel[]>([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0); // 总数

  useRequest(() => GroupApi.listWithPage(workspace.id, page), {
    onSuccess: (res) => {
      setGroups(res.items);
      setTotal(res.total);
    },
    refreshDeps: [workspace.id, page],
  });

  // const displayGroup = groups.slice(startIndex, endIndex);

  return (
    <>
      <Row gutter={[16, 16]} style={style}>
        {groups.map((group, i) => (
          <Col key={group.id} xs={24} sm={12} md={8} lg={8} xl={6} xxl={6}>
            <GroupCard group={group} key={i + '@' + page} />
          </Col>
        ))}
      </Row>
      {total > PAGE_SIZE && (
        <Pagination
          style={{ float: 'right', marginTop: '8px' }}
          simple
          size="small"
          current={page}
          onChange={setPage}
          total={total}
          pageSize={PAGE_SIZE}
        />
      )}
    </>
  );
}
