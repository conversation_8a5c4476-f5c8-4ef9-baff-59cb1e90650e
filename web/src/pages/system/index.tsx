import { ClusterOutlined } from '@ant-design/icons';

import { createMenuPage, MenuItemConfig } from '../../components/pageLayout';
import WorkspaceCrud from './components/workspace-crud';

export const menuConfig = [
  {
    title: '租户管理',
    path: 'workspace',
    Icon: ClusterOutlined,
    element: <WorkspaceCrud />,
  },
] as MenuItemConfig[];

export function createSystemMenuPage() {
  // 在此函数中不能使用 useGlobalState 等 hooks， 否则会导致路由切换异常 (这是一个辅助函数，非 React Component)

  return createMenuPage({
    path: 'system',
    menuConfig,
    retainQueries: ['workspaceId'], // 跳转菜单时保留的查询参数项
  });
}
