import { WorkspaceApi } from '@api/index';
import Crud from '@components/crud';
import { WorkspaceModel, WorkspaceUpdateModel } from '@interface/index';
import { GlobalStateKey } from '@store/index';

function WorkspaceCrud() {
  const config = {
    modelName: '租户',
    api: WorkspaceApi,
    balStateKey: 'workspace' as GlobalStateKey,
    hiddenDelete: true, // 隐藏删除按钮
    schema: [
      {
        title: 'ID',
        dataIndex: 'id',
        editable: false,
        required: false,
      },
      {
        title: '租户名',
        dataIndex: 'name',
        editable: true,
        required: true,
        notNull: true,
      },
      {
        title: '描述',
        dataIndex: 'description',
        editable: true,
        required: true,
        notNull: true,
      },
    ],
  };

  return <Crud<WorkspaceModel, WorkspaceUpdateModel> {...config} />;
}

export default WorkspaceCrud;
