import { Input, Modal, Form, Radio, InputNumber, Switch, Alert, Checkbox, Divider} from 'antd';
import { useEffect, useState } from 'react';
import { useQuery } from '@/hooks/useQuery';
import { DocumentTaskApi } from '@/api';

const documentFormLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

export default function AddRemoteFile({ knowledgeDetail, open, onOk, onCancel, initData }) {
  const [remoteForm] = Form.useForm();
  const [submiting, setSubmiting] = useState<boolean>(false);
  const { parsedQuery } = useQuery();

  useEffect(() => {
    if (initData && initData.id) {
      const { agreement, ...formData } = initData.config;
      remoteForm.setFieldsValue(formData);
    }
  }, [initData?.id]);


  const onAddRemoteFileFinish = () => {
    setSubmiting(true);
    remoteForm.validateFields().then((values) => {
      DocumentTaskApi.create({
        collectionId: knowledgeDetail.collectionId,
        knowledgeId: knowledgeDetail.id,
        config: values,
        source: values.remoteConfig.source,
      }, `${parsedQuery.workspaceId}`).then(res => {
        setSubmiting(false);
        onOk();
        remoteForm.resetFields();
      }).catch(() => {
        setSubmiting(false);
      });
    }).catch((error) => {
      setSubmiting(false);
      console.log(error);
    })
  }

  const repoFormat = (value) => {
    const prefix = 'ssh://********************:22222/';
    const prefixHttp = 'https://g.hz.netease.com/';
    const replaceValue = val => (val || '').replace(prefix, '').replace(prefixHttp, '').replace(/\.git$/, '');
    return replaceValue(value);
  }

  const remoteFileType = Form.useWatch(['importConfig', 'type'], remoteForm);

  return (
    <Modal
      title={initData ? '更新导入' : '导入文件'}
      open={open}
      width={800}
      onCancel={() => {
        onCancel();
        remoteForm.resetFields();
      }}
      onOk={onAddRemoteFileFinish}
      okButtonProps={{
        loading: submiting,
      }}
    >
      <Form
        {...documentFormLayout}
        form={remoteForm}
        style={{ padding: '48px 24px' }}
      >
        <Form.Item initialValue='gitlab' name={["remoteConfig", "source"]} label="文档来源">
          <Radio.Group>
            <Radio value='gitlab'>GitLab</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item normalize={repoFormat} name={["remoteConfig", "repo"]} label="GitLab 仓库" rules={[{ required: true }]}>
          <Input placeholder="请输入" maxLength={128} showCount style={{ width: '90%' }}/>
        </Form.Item>
        <Form.Item initialValue='master' name={["remoteConfig", "ref"]} label="分支/tag">
          <Input placeholder="请输入" maxLength={128} showCount style={{ width: '90%' }}/>
        </Form.Item>
        <Form.Item name={["remoteConfig", "sha"]} label="commit hash">
          <Input placeholder="请输入" maxLength={128} showCount style={{ width: '90%' }}/>
        </Form.Item>
        <Form.Item initialValue='/' name={["remoteConfig", "dir"]} label="扫描路径">
          <Input placeholder="请输入" maxLength={128} showCount style={{ width: '90%' }}/>
        </Form.Item>
        <Form.Item initialValue=".md,.mdx" name={["remoteConfig", "ext"]} label="导入文件格式">
          <Input placeholder="请输入" maxLength={128} showCount style={{ width: '90%' }}/>
        </Form.Item>
        <Form.Item initialValue="test/" name={["remoteConfig", "ignore"]} label="排除路径">
          <Input placeholder="请输入" maxLength={128} showCount style={{ width: '90%' }}/>
        </Form.Item>
        <Divider />
        <Form.Item initialValue={true} name={["importConfig", "overwrite"]} label="文档更新策略" tooltip={<>如果文档之前已经被导入过（已有知识库中存在 URL 相同的文档），可以选择覆盖已有文档并重新导入分片，或作为新的文档导入</>}>
          <Radio.Group>
            <Radio value={true}>自动覆盖已有文档</Radio>
            <Radio value={false}>创建新的文档</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item initialValue='text' name={["importConfig", "type"]} label="文档类型">
          <Radio.Group>
            <Radio value='text'>文本文件</Radio>
            <Radio value='markdown'>Markdown 文件</Radio>
            <Radio value='html'>HTML 文件</Radio>
          </Radio.Group>
        </Form.Item>
        {
          remoteFileType === 'html' &&
          <Form.Item wrapperCol={{span: 24}}>
            <Alert
              type="info"
              message="为了避免 html 标签影响文档匹配效果，导入时 html 文档会自动转换为 markdown 格式，因此部分 html 样式会丢失，只保留基础的标题、表格、链接等 markdown 支持的格式" />
          </Form.Item>
        }
        <Form.Item initialValue={1000} name={["importConfig", "size"]} label="限制 token 数" tooltip={<>限制每个片段不超过指定的 token 数，超过值后将会分割为新的片段</>}>
          <InputNumber placeholder="请输入" max={2000} min={50} style={{ width: '90%' }}/>
        </Form.Item>
        {
          remoteFileType === 'text' &&
          <Form.Item name={["importConfig", "spliter"]} label="分隔符" tooltip={<>将按照指定分隔符，对文本进行分段切割</>}>
            <Input placeholder="请输入" maxLength={128} showCount style={{ width: '90%' }}/>
          </Form.Item>
        }
        {
          remoteFileType !== 'text' &&
          <>
            <Form.Item initialValue={2} name={["importConfig", "level"]} label="默认分隔标题等级" tooltip={<>控制按照 markdown 的标题层级进行分割，如果分割后该段文本长度超出限制
              token 数，则会再按照子标题进行分割。例如若设置为 2
              级标题，则默认分段粒度是二级标题，若该段过长，则会按照三级标题、四级标题……继续分割，直到满足
              token 数限制</>}>
              <InputNumber placeholder="请输入" max={7} min={1} style={{ width: '90%' }}/>
            </Form.Item>
            <Form.Item  valuePropName='checked' initialValue={true} name={["importConfig", "includeTitleLevel"]} label="片段包含标题信息" tooltip={<>开启后，会在每个片段后追加当前层级的标题信息，形如{"<!-- 当前文档层级: 一级标题/二级标题/三级标题 -->"}，方便 GPT 理解上下文关系</>}>
              <Switch />
            </Form.Item>
          </>
        }
        <Form.Item
          name='agreement'
          valuePropName='checked'
          wrapperCol={{ offset: 6, span: 18 }}
          rules={[{
            validator: (rule, value, callback) => {
              if (!value) {
                callback("上传文档前，请先同意安全合规要求");
              } else {
                callback();
              }
            }
          }]}
        >
          <Checkbox>
            我已知晓并确认上传的文档将发送至 OpenAI
            的服务器，且文档符合网易集团的信息安全规范
          </Checkbox>
        </Form.Item>
      </Form>
    </Modal>
  );
}
