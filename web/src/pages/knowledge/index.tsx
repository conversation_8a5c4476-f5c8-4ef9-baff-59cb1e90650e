import { Button, Card, Input, Table, Modal, Form, Alert } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import styled from 'styled-components';
import { useQuery } from '@/hooks/useQuery';
import { usePathQuery } from '@/hooks/useQuery';
import type { TableProps, SelectProps } from 'antd';
import { KnowledgeApi, GroupApi } from '@/api';
import './index.less';
import { KnowledgeModel } from '@/interface';

const { Search, TextArea } = Input;

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const Container = styled.div`
  background-color: rgba(248, 250, 252);
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
`;

interface DataType {
  id: string;
  name: string;
  documentCount: number;
  group: string;
  description: string;
}

const PAGE_SIZE = 10;
const groupMap = {};

export default function Knowledge() {
  const { pushAddQuery } = usePathQuery();
  const { parsedQuery } = useQuery();

  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [submiting, setSubmiting] = useState(false);
  const [groups, setGroups] = useState<SelectProps['options']>();
  const [knowledgeList, setKnowledgeList] = useState<KnowledgeModel[]>();
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [keyword, setKeyword] = useState<string>();
  const [tempKeyword, setTempKeyword] = useState<string>();
  const [loading, setLoading] = useState<boolean>(false);

  const getKnowledgeList = (keyword, page) => {
    setLoading(true);
    KnowledgeApi.listWithPage(parsedQuery.workspaceId as string, parsedQuery.groupId as string, keyword, page, PAGE_SIZE).then((res) => {
      setKnowledgeList(res.items);
      setTotal(res.total);
      setLoading(false);
    });
  };

  useEffect(() => {
    if (parsedQuery.workspaceId) {
      getKnowledgeList(keyword, current);
    }
    setTempKeyword(keyword);
  }, [keyword, current, parsedQuery.workspaceId]);

  useEffect(() => {
    if (parsedQuery.workspaceId) {
      GroupApi.list(parsedQuery.workspaceId).then((items) => {
        const options = items.map(item => {
          groupMap[item.id] = item;
          return {
            label: item.name,
            value: item.id,
          };
        });
        setGroups(options);
      });
    }
  }, [parsedQuery.workspaceId]);

  const columns: TableProps<DataType>['columns'] = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, { description }) => {
        return (
          <>
            <div style={{ fontWeight: 700 }}>
              {text}
            </div>
            <div style={{ fontSize: 12 }}>
              {description}
            </div>
          </>
        );
      },
    },
    {
      title: '分组',
      dataIndex: 'groupId',
      key: 'groupId',
      width: 180,
      render: (groupId) => {
        return groupMap[groupId] && groupMap[groupId].name;
      },
    },
    {
      title: '包含文档',
      dataIndex: 'documentCount',
      key: 'documentCount',
      width: 90,
      render: (count = 0) => {
        return count;
      },
    },
    {
      title: '更新时间',
      key: 'updatedAt',
      dataIndex: 'updatedAt',
      width: 180,
      render: (updatedAt) => {
        return dayjs(updatedAt).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button disabled type="link">
          <DeleteOutlined />
        </Button>
      ),
    },
  ];

  const onDelete = (e, record) => {
    e.stopPropagation();
    Modal.confirm({
      title: '删除知识库',
      content: '删除后关联的引用将失效',
      onOk: () => {
        KnowledgeApi.remove(record.id).then(() => {
          getKnowledgeList(undefined, 1);
        });
      },
    });
  };

  const onSearch = (value) => {
    setCurrent(1);
    setKeyword(value);
  };

  const onFinish = () => {
    form.validateFields().then((values) => {
      if (parsedQuery.workspaceId) {
        setSubmiting(true);
        values.groupId = parsedQuery.groupId;
        values.workspaceId = parsedQuery.workspaceId;
        KnowledgeApi.create(values, `${parsedQuery.groupId}`).then(() => {
          setOpen(false);
          setSubmiting(false);
          if (current === 1 && keyword == undefined) {
            getKnowledgeList(undefined, 1);
          } else {
            setCurrent(1);
            setKeyword(undefined);
          }
        });
      }
    }).catch((error) => {
      console.log(error);
    });
  };

  const onTableChange = (pagination) => {
    setCurrent(pagination.current);
  };

  return (
    <Container style={{ height: '100%' }} className="knowledge">
      <Alert
        style={{
          marginTop: '-20px',
          marginBottom: '10px',
        }}
        description="旧版知识库 即将下线，请尽快完成迁移。"
        showIcon
        type="warning"
      />
      <Card
        style={{ width: '100%', margin: '0 auto' }}
        title="知识库"
        extra={<Button type="primary" disabled onClick={() => setOpen(true)}>创建知识库</Button>}
      >
        <Search placeholder="搜索" value={tempKeyword} onChange={(e) => setTempKeyword(e.target.value)}
                onSearch={onSearch} style={{ width: 256 }} />
        <Table
          style={{ marginTop: 24 }}
          columns={columns as any}
          dataSource={knowledgeList}
          rowKey="id"
          onRow={(record) => {
            return {
              onClick: () => {
                pushAddQuery(`/knowledge/${record.id}`, {});
              },
            };
          }}
          onChange={onTableChange}
          pagination={{
            total,
            pageSize: PAGE_SIZE,
            current,
          }}
          loading={loading}
        />
        <Modal
          title="新建知识库"
          open={open}
          width={560}
          onCancel={() => setOpen(false)}
          onOk={onFinish}
          okButtonProps={{
            loading: submiting,
          }}
        >
          <Form
            {...layout}
            form={form}
            name="control-hooks"
            style={{ padding: '48px 24px' }}
          >
            <Form.Item name="name" label="名称" rules={[{ required: true }]}>
              <Input placeholder="请输入" maxLength={128} showCount />
            </Form.Item>
            {/* <Form.Item name="groupId" label="分组" rules={[{ required: true }]}>
              <Select
                placeholder="选择分组"
                allowClear
                options={groups}
              />
            </Form.Item> */}
            <Form.Item name="description" label="描述">
              <TextArea placeholder="请输入" rows={4} maxLength={256} showCount />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </Container>
  );
}
