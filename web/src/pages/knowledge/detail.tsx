import { Button, Card, Input, Table, Modal, Form, Select, Space, Breadcrumb, Tabs, Popover } from 'antd';
import { DeleteOutlined, FormOutlined, EditOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import dayjs from 'dayjs';
import styled from 'styled-components';
import { useQuery } from '@/hooks/useQuery';
import { usePathQuery } from '@/hooks/useQuery';
import { KnowledgeApi, DocumentApi, GroupApi, DocumentTaskApi } from '@/api';
import { KnowledgeModel, DocumentModel, DocumentTaskModel } from '@/interface';
import type { TableProps, SelectProps, TabsProps } from 'antd';
import AddRemoteFile from './remote-file';
import AddLocalFile from './local-file';

const { TextArea } = Input;

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const Container = styled.div`
  background-color: rgba(248, 250, 252);
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
`;

const PAGE_SIZE = 10;
const groupMap = {};
const stateMap = {
  '0': '排队中',
  '1': '抓取中',
  '2': '待导入',
  '3': '导入中',
  '4': '导入完成',
  '19': '创建任务失败', //-1
  '18': '抓取失败', //-2
  '17': '获取结果失败', //-3
  '16': '导入失败', //-4
  '15': '用户取消导入', //-5
};

const DOC_SUCCECC_STATE = 2;
const DOC_END_STATE = 3;

export default function Knowledge() {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [groups, setGroups] = useState<SelectProps['options']>();
  const [localFileOpen, setLocalFileOpen] = useState(false);
  const [remoteFileOpen, setRemoteFileOpen] = useState(false);
  const { pushAddQuery } = usePathQuery();
  const { parsedQuery } = useQuery();
  const params = useParams();
  const [knowledgeDetail, setKnowledgeDetail] = useState<KnowledgeModel>();
  const [submiting, setSubmiting] = useState<boolean>(false);

  const [activeKey, setActiveKey] = useState<string>('document');

  const [documentList, setDocumentList] = useState<DocumentModel[]>();
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [taskList, setTaskList] = useState<DocumentTaskModel[]>();
  const [taskTotal, setTaskTotal] = useState<number>(0);
  const [taskCurrent, setTaskCurrent] = useState<number>(1);

  const [taskData, setTaskData] = useState();
  const [documentData, setDocumentData] = useState();

  const [loading, setLoading] = useState<boolean>(false);
  const [taskLoading, setTaskLoading] = useState<boolean>(false);

  useEffect(() => {
    if (parsedQuery.workspaceId) {
      GroupApi.list(parsedQuery.workspaceId).then((items) => {
        const options = items.map(item => {
          groupMap[item.id] = item;
          return {
            label: item.name,
            value: item.id,
          };
        });
        setGroups(options);
      });
    }
  }, [parsedQuery.workspaceId]);

  const getKnowledegeDetail = () => {
    KnowledgeApi.get && KnowledgeApi.get(`${params.knowledgeId}`).then((res) => {
      setKnowledgeDetail(res);
    });
  };

  const getDocumentList = (page) => {
    setLoading(true);
    if (knowledgeDetail?.collectionId) {
      DocumentApi.listWithPage(`${parsedQuery.workspaceId}`, `${params.knowledgeId}`, knowledgeDetail?.collectionId, page, PAGE_SIZE).then((res) => {
        setDocumentList(res.items);
        setTotal(res.total || 0);
        setLoading(false);
      });
    }
  };
  const getFirstPage = () => {
    if (current === 1) {
      getDocumentList(1);
    } else {
      setCurrent(1);
    }
  };

  useEffect(() => {
    if (activeKey === 'document') {
      getDocumentList(current);
    }
  }, [current, knowledgeDetail?.collectionId, activeKey]);

  useEffect(() => {
    getDocumentTaskList(taskCurrent);
  }, [taskCurrent, activeKey]);

  const getDocumentTaskList = (page) => {
    if (activeKey === 'task') {
      setTaskLoading(true);
      DocumentTaskApi.listWithPage(`${parsedQuery.workspaceId}`, `${params.knowledgeId}`, page, PAGE_SIZE).then((res) => {
        setTaskList(res.items || []);
        setTaskTotal(res.total || 0);
        setTaskLoading(false);
      });
    }
  };
  const getFirstTaskPage = () => {
    if (current === 1) {
      getDocumentTaskList(1);
    } else {
      setTaskCurrent(1);
    }
  };

  useEffect(() => {
    getKnowledegeDetail();
  }, [params.knowledgeId]);

  const onUpdate = (e, record) => {
    e.stopPropagation();
    setDocumentData(record);
    setLocalFileOpen(true);
  };

  const onTaskUpdate = (e, record) => {
    e.stopPropagation();
    setTaskData(record);
    setRemoteFileOpen(true);
  };

  const onDelete = (e, record) => {
    e.stopPropagation();
    Modal.confirm({
      title: '删除文档',
      content: '删除后关联的引用将失效',
      onOk: () => {
        if (knowledgeDetail?.collectionId) {
          DocumentApi.remove(record.id, knowledgeDetail?.collectionId, record.doc_id).then(() => {
            getFirstPage();
          });
        }
      },
    });
  };

  // const onTaskDelete = (e, record) => {
  //   e.stopPropagation();
  //   Modal.confirm({
  //     title: '删除任务',
  //     content: '删除任务后远程文档不会导入',
  //     onOk: () => {
  //       if (knowledgeDetail?.collectionId) {
  //         DocumentTaskApi.remove(record.id, knowledgeDetail?.collectionId, record.task_id).then(() => {
  //           getFirstTaskPage();
  //         });
  //       }
  //     }
  //   })
  // }

  const onUpdateKnowledge = () => {
    setSubmiting(true);
    form.validateFields().then((values) => {
      setSubmiting(false);
      const updateData = {
        name: values.name,
        description: values.description,
        groupId: values.groupId,
        workspaceId: `${parsedQuery.workspaceId}`,
      };
      KnowledgeApi.update(updateData, `${params.knowledgeId}`).then(() => {
        setKnowledgeDetail({
          ...knowledgeDetail!,
          ...updateData,
        });
        setOpen(false);
      });
    }).catch((error) => {
      console.log(error);
    });
  };

  const showEditKnowledge = () => {
    form.setFieldsValue({
      name: knowledgeDetail?.name,
      description: knowledgeDetail?.description,
      groupId: knowledgeDetail?.groupId,
    });
    setOpen(true);
  };

  const onTableChange = (pagination) => {
    setCurrent(pagination.current);
  };

  const onTaskTableChange = (pagination) => {
    setTaskCurrent(pagination.current);
  };

  const columns: TableProps<DocumentModel>['columns'] = [
    {
      title: '名称',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: '来源',
      dataIndex: 'source',
      key: 'source',
      width: 90,
      render: (source) => {
        if (source === 'local') {
          return '文件上传';
        } else {
          return 'Gitlab';
        }
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 150,
      render: (state, { segment }) => {
        if (state === DOC_SUCCECC_STATE) {
          return '导入成功';
        } else if (state === DOC_END_STATE) {
          return <>
            导入失败
            <Popover placement="top" content="文档分片导入失败，具体查看文档详情">
              <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
            </Popover>
          </>;
        } else {
          return `进行中（${segment?.success} / ${segment?.total}）`;
        }
      },
    },
    {
      title: '创建时间',
      key: 'createdAt',
      dataIndex: 'createdAt',
      width: 180,
      render: (createdAt) => {
        return dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '更新时间',
      key: 'updatedAt',
      dataIndex: 'updatedAt',
      width: 180,
      render: (updatedAt) => {
        return dayjs(updatedAt).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space>
          {
            record.source === 'local' && (
              <Button
                disabled
                type="link"
                title="更新"
                style={{ padding: 0 }}
                onClick={(e) => onUpdate(e, record)}
              >
                <EditOutlined />
              </Button>
            )
          }
          <Button
            disabled
            danger
            type="link"
            title="删除"
            style={{ padding: 0 }}
            onClick={(e) => onDelete(e, record)}
          >
            <DeleteOutlined />
          </Button>
        </Space>
      ),
    },
  ];

  const taskColumns: TableProps<DocumentTaskModel>['columns'] = [
    {
      title: 'ID',
      dataIndex: 'task_id',
      key: 'task_id',
    },
    {
      title: '资源地址',
      dataIndex: ['config', 'remoteConfig', 'repo'],
      key: 'repo',
      width: 290,
    },
    {
      title: '状态',
      dataIndex: 'task_state',
      key: 'task_state',
      width: 90,
      render: (state) => {
        return stateMap[`${state}`];
      },
    },
    {
      title: '创建时间',
      key: 'createdAt',
      dataIndex: 'createdAt',
      width: 180,
      render: (createdAt) => {
        return dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '更新时间',
      key: 'updatedAt',
      dataIndex: 'updatedAt',
      width: 180,
      render: (updatedAt) => {
        return dayjs(updatedAt).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Space>
          <Button
            // disabled
            type="link"
            title="更新"
            onClick={(e) => onTaskUpdate(e, record)}
          >
            <EditOutlined />
          </Button>
          {/* <a title='删除' onClick={(e) => onTaskDelete(e, record)}><DeleteOutlined /></a> */}
        </Space>
      ),
    },
  ];

  const items: TabsProps['items'] = [
    {
      key: 'document',
      label: '文档',
      children: (
        <Table
          columns={columns}
          dataSource={documentList}
          rowKey="id"
          loading={loading}
          onRow={(record) => {
            return {
              onClick: () => {
                pushAddQuery(`/knowledge/${params.knowledgeId}/document/${record.id}`, {});
              },
            };
          }}
          onChange={onTableChange}
          pagination={{
            total,
            pageSize: PAGE_SIZE,
            current,
          }}
          scroll={{
            y: 'calc(100vh - 444px)',
          }}
        />
      ),
    },
    {
      key: 'task',
      label: '任务',
      children: (
        <Table
          columns={taskColumns}
          dataSource={taskList}
          rowKey="id"
          loading={taskLoading}
          onChange={onTaskTableChange}
          pagination={{
            total: taskTotal,
            pageSize: PAGE_SIZE,
            current: taskCurrent,
          }}
          scroll={{
            y: 'calc(100vh - 444px)',
          }}
        />
      ),
    },
  ];

  return (
    <Container style={{ height: '100%' }} className="knowledge">
      <div style={{ margin: '20px 20px 0' }}>
        <Breadcrumb
          items={[
            {
              title: <Link
                to={`/group/knowledge?workspaceId=${parsedQuery.workspaceId}&groupId=${parsedQuery.groupId}`}>知识库</Link>,
            },
            {
              title: knowledgeDetail?.name,
            },
          ]}
        />
      </div>
      <Card
        style={{ margin: '20px 20px' }}
        title={<div>
          {knowledgeDetail?.name}
          <Button disabled type="link" style={{ padding: 0, marginLeft: 8 }}>
            <FormOutlined onClick={showEditKnowledge} />
          </Button>
        </div>}
        extra={(<>
          <Button
            type="primary"
            onClick={() => setLocalFileOpen(true)}
            style={{ marginRight: 16 }}
            disabled
          >
            新建本地文档
          </Button>
          <Button
            type="primary"
            onClick={() => setRemoteFileOpen(true)}
            disabled
          >
            导入远程文档
          </Button>
        </>)}
        loading={!knowledgeDetail}
      >
        <div>{knowledgeDetail?.description}</div>
        <Tabs activeKey={activeKey} items={items} onChange={(key) => setActiveKey(key)} />
        <Modal
          title="编辑知识库"
          open={open}
          width={560}
          onCancel={() => setOpen(false)}
          onOk={onUpdateKnowledge}
          okButtonProps={{
            loading: submiting,
          }}
        >
          <Form
            {...layout}
            form={form}
            style={{ padding: '48px 24px' }}
          >
            <Form.Item name="name" label="名称" rules={[{ required: true }]}>
              <Input placeholder="请输入" maxLength={128} showCount />
            </Form.Item>
            <Form.Item name="groupId" label="分组" rules={[{ required: true }]}>
              <Select
                placeholder="选择分组"
                allowClear
                options={groups}
              />
            </Form.Item>
            <Form.Item name="description" label="描述">
              <TextArea placeholder="请输入" rows={4} maxLength={256} showCount />
            </Form.Item>
          </Form>
        </Modal>
        <AddLocalFile
          knowledgeDetail={knowledgeDetail}
          open={localFileOpen}
          onCancel={() => {
            setLocalFileOpen(false);
            setDocumentData(undefined);
          }}
          onOk={() => {
            setLocalFileOpen(false);
            getFirstPage();
            setDocumentData(undefined);
          }}
          initData={documentData}
        />
        <AddRemoteFile
          knowledgeDetail={knowledgeDetail}
          open={remoteFileOpen}
          onCancel={() => {
            setRemoteFileOpen(false);
            setTaskData(undefined);
          }}
          onOk={() => {
            setRemoteFileOpen(false);
            getFirstTaskPage();
            setTaskData(undefined);
          }}
          initData={taskData}
        />
      </Card>
    </Container>
  );
}
