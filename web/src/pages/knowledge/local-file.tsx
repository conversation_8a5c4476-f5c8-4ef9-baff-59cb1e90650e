import { Input, Modal, Form, Radio, InputNumber, Switch, Alert, Checkbox, message } from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@/hooks/useQuery';
import { DocumentApi } from '@/api';

const documentFormLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

export default function AddLocalFile({ knowledgeDetail, open, onOk, onCancel, initData }) {
  const [localForm] = Form.useForm();
  const { parsedQuery } = useQuery();
  const params = useParams();
  const [submiting, setSubmiting] = useState<boolean>(false);
  const [file, setFile] = useState<FileList>();

  useEffect(() => {
    if (initData && initData.id) {
      const { agreement, ...formData } = initData.config;
      localForm.setFieldsValue(formData);
    }
  }, [initData?.id]);

  const onAddLocalFile = () => {
    setSubmiting(true);
    localForm.validateFields().then((values) => {
      if (!file) {
        message.error('请上传文件！');
        return;
      }
      const data = new FormData();
      data.append('knowledgeId', `${params.knowledgeId}`);
      data.append('title', file[0].name);
      data.append('type', values.type);
      data.append('source', 'local');
      data.append('config', JSON.stringify(values));
      data.append('collectionId', `${knowledgeDetail?.collectionId}`);

      [].forEach.call(file || [], item => {
        data.append('file', item);
      });
      if (initData && initData.id) {
        data.append('id', initData.id);
        data.append('doc_id', initData.doc_id);
        DocumentApi.update(data, `${initData.id}`).then(() => {
          setSubmiting(false);
          onOk();
          localForm.resetFields();
        }).catch(() => {
          setSubmiting(false);
        })
      } else {
        DocumentApi.create(data, `${parsedQuery.workspaceId}`).then(() => {
          setSubmiting(false);
          onOk();
          localForm.resetFields();
        }).catch(() => {
          setSubmiting(false);
        })
      }
    }).catch((error) => {
      console.log(error);
      setSubmiting(false);
    })
  }

  const localFileType = Form.useWatch('type', localForm);

  return (
    <Modal
      title={initData ? '更新文件' : '上传文件'}
      open={open}
      width={800}
      onCancel={() => {
        onCancel();
        localForm.resetFields();
      }}
      onOk={onAddLocalFile}
      okButtonProps={{
        loading: submiting,
      }}
    >
      <Form
        {...documentFormLayout}
        form={localForm}
        style={{ padding: '48px 24px' }}
      >
        <Form.Item initialValue='markdown' name="type" label="文档类型">
          <Radio.Group>
            <Radio value='text'>文本文件</Radio>
            <Radio value='markdown'>Markdown 文件</Radio>
            <Radio value='html'>HTML 文件</Radio>
            <Radio value='word'>Word 文件</Radio>
          </Radio.Group>
        </Form.Item>
        {
          localFileType === 'html' &&
          <Form.Item wrapperCol={{span: 24}}>
            <Alert
              type="info"
              message="为了避免 html 标签影响文档匹配效果，导入时 html 文档会自动转换为 markdown 格式，因此部分 html 样式会丢失，只保留基础的标题、表格、链接等 markdown 支持的格式" />
          </Form.Item>
        }
        {
          localFileType === 'word' && 
          <Form.Item wrapperCol={{span: 24}}>
            <Alert
              type="info"
              message="word文档只支持 docx 格式。旧版的 doc 格式，请先通过 Office 或者 WPS 转换成 docx 格式" />
          </Form.Item>
        }
        <Form.Item initialValue={1000} name="size" label="限制 token 数" tooltip={<>限制每个片段不超过指定的 token 数，超过值后将会分割为新的片段</>}>
          <InputNumber placeholder="请输入" max={2000} min={50} style={{ width: '90%' }}/>
        </Form.Item>
        <Form.Item initialValue={50} name="chunkOverlap" label="重叠 token 数" tooltip={<>限制两个片段间重叠 token 数</>}>
          <InputNumber placeholder="请输入" max={500} min={0} style={{ width: '90%' }}/>
        </Form.Item>
        <Form.Item name="spliter" label="分隔符" tooltip={<>将按照指定分隔符，对文本进行分段切割</>}>
          <Input placeholder="请输入" maxLength={128} showCount style={{ width: '90%' }}/>
        </Form.Item>
        {
          localFileType !== 'text' && 
          <Form.Item initialValue={2} name="level" label="默认分隔标题等级" tooltip={<>控制按照 markdown 的标题层级进行分割，如果分割后该段文本长度超出限制
            token 数，则会再按照子标题进行分割。例如若设置为 2
            级标题，则默认分段粒度是二级标题，若该段过长，则会按照三级标题、四级标题……继续分割，直到满足
            token 数限制</>}>
            <InputNumber placeholder="请输入" max={7} min={1} style={{ width: '90%' }}/>
          </Form.Item>
        }
        <Form.Item valuePropName='checked' initialValue={true} name="uploadNos" label="自动同步 NOS" tooltip={<>开启后，会将文本自动同步到NOS一份，保证后续能快速下载原有文件</>}>
          <Switch />
        </Form.Item>
        <Form.Item  valuePropName='checked' initialValue={true} name="includeTitleLevel" label="片段包含标题信息" tooltip={<>开启后，会在每个片段后追加当前层级的标题信息，形如{"<!-- 当前文档层级: 一级标题/二级标题/三级标题 -->"}，方便 GPT 理解上下文关系</>}>
          <Switch />
        </Form.Item>
        <Form.Item label="上传文件">
          <input
            type="file"
            accept=".docx,.txt,.md,.mkd,.mdwn,.mdown,.markdown,.markdn,.mdtxt,.mdtext,.mdx,.htm,.html,.xhtm,.xhtml"
            multiple
            onChange={event => {
              console.log(event.target.files);
              if (event.target.files) {
                setFile(event.target.files);
              }
            }}
          />
        </Form.Item>
        <Form.Item
          name='agreement'
          valuePropName='checked'
          wrapperCol={{ offset: 6, span: 18 }}
          rules={[{
            validator: (rule, value, callback) => {
              if (!value) {
                callback("上传文档前，请先同意安全合规要求");
              } else {
                callback();
              }
            }
          }]}
        >
          <Checkbox>
            我已知晓并确认上传的文档将发送至 OpenAI
            的服务器，且文档符合网易集团的信息安全规范
          </Checkbox>
        </Form.Item>
      </Form>
    </Modal>
  );
}
