import { LockTwoTone, SafetyCertificateTwoTone } from '@ant-design/icons';
import { Spin } from 'antd';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';

import { WorkspaceApi } from '@/api';
import { useGlobalState } from '@/hooks/useGlobalState';
import { usePathQuery, useQuery } from '@/hooks/useQuery';
import { WorkspaceModel } from '@/interface';
import { NoAuthTip } from '@/components/common/NoAuthTip';

export function WorkspaceInvitePage() {
  const { globalState } = useGlobalState();
  const { workspaces } = globalState;
  const { parsedQuery } = useQuery();
  const { pushRetainQuery } = usePathQuery();

  const [loading, setLoading] = useState(true);
  const [targetWorkspace, setTargetWorkspace] = useState<WorkspaceModel | null>(null);
  const [admins, setAdmins] = useState([]);

  useEffect(() => {
    if (!workspaces || !parsedQuery.workspaceId) {
      return;
    }
    async function getAdmins() {
      const admins = await WorkspaceApi.getWorkspaceAdmin(parsedQuery.workspaceId as string);
      setAdmins(admins);
      console.log('admins', admins);
    }
    getAdmins();
    const existWorkspace = workspaces.find((it) => it.id === parsedQuery.workspaceId);
    if (existWorkspace) {
      setTargetWorkspace(existWorkspace);
      setLoading(false);
    } else {
      WorkspaceApi.get(parsedQuery.workspaceId as string).then((res) => {
        setTargetWorkspace(res);
        setLoading(false);
      });
    }
  }, [workspaces, parsedQuery.workspaceId]);

  const hasPermission = workspaces?.find((it) => it.id === targetWorkspace?.id);

  if (loading) {
    return <Spin />;
  }

  function renderHasAuth() {
    return (
      <>
        <SafetyCertificateTwoTone
          style={{ marginTop: '30px' }}
          twoToneColor="#52c41a"
          rev={undefined}
        />
        <div>您已加入工作空间: {targetWorkspace?.name}</div>
        <a
          onClick={() => pushRetainQuery('/', ['workspaceId'])}
          style={{ marginTop: '10px' }}
        >
          进入首页
        </a>
      </>
    );
  }

  function renderNoAuth() {
    const renderContact = () => (
      <NoAuthTip
        type="workspace"
        name={targetWorkspace?.name || ''}
        workspaceId={targetWorkspace?.id}
      />
    );

    const renderAuthWorkspaces = () => (
      <>
        <SafetyCertificateTwoTone
          style={{ marginTop: '30px' }}
          twoToneColor="#52c41a"
          rev={undefined}
        />
        <div style={{ marginTop: '20px', marginBottom: '20px' }}>
          以下是您有权限访问的工作空间:
        </div>
        {workspaces?.map((it) => (
          <div key={it.id} className="auth-workspace">
            <Link to={`/?workspaceId=${it.id}`}>{it.name}</Link>
          </div>
        ))}
      </>
    );

    return (
      <>
        {renderContact()}
        {renderAuthWorkspaces()}
      </>
    );
  }

  return (
    <InviteContainer>{hasPermission ? renderHasAuth() : renderNoAuth()}</InviteContainer>
  );
}

const InviteContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  padding-top: 50px;
  color: #333;

  a {
    color: #1846ff;
    cursor: pointer;
  }

  .contact {
    margin-top: 20px;
  }

  .anticon {
    font-size: 50px;
    margin-bottom: 10px;
  }

  .auth-workspace {
    margin-bottom: 8px;
  }
`;
