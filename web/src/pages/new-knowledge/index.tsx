import React, { useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import TextKnowledge from './components/text-knowledge';
import TableKnowledge from './components/table-knowledge';
import CioKnowledge from './components/cio-knowledge';
import CreateKnowledgeModal from './components/create-knowledge-modal';
import './index.less';
import { knowledgeTypeSchema } from './config';
import { KnowledgeType } from '@/interface';
import { useQuery } from '@hooks/useQuery';
import AllKnowledge from '@/pages/new-knowledge/components/all-knowledge';

interface Props {}

const Container = styled.div`
  width: 100%;
  height: 100%;
  padding: 8px;
  box-sizing: border-box;
  background-color: rgba(248, 250, 252);
`;

const NewKnowledge: React.FC<Props> = () => {
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [knowledgeType, setKnowledgeType] = useState<KnowledgeType>('all');
  const [refreshFlag, setRefreshFlag] = useState(false);

  const { parsedQuery, updateQuery, dropQuery } = useQuery();

  useEffect(() => {
    if (Boolean(parsedQuery.isAllType)) {
      setKnowledgeType('all');
    } else if (parsedQuery.knowledgeType) {
      setKnowledgeType(parsedQuery.knowledgeType as KnowledgeType);
    }
  }, []);

  useEffect(() => {
    updateQuery('knowledgeType', knowledgeType);
  }, [knowledgeType]);

  const Title = useMemo(() => {
    const items = knowledgeTypeSchema.map((item) => ({
      ...item,
      icon: null,
      onClick: () => {
        dropQuery(['isAllType']);
        setKnowledgeType(item.key);
      },
    }));

    return (
      <>
        <span>知识库</span>
        <span> - </span>
        <Dropdown
          menu={{
            items,
            selectable: true,
            defaultSelectedKeys: [knowledgeType],
          }}
        >
          <a onClick={(e) => e.preventDefault()}>
            <span>{(items.find((i) => i.key === knowledgeType) as any)?.label}</span>
            <DownOutlined />
          </a>
        </Dropdown>
      </>
    );
  }, [knowledgeType]);

  const KnowledgeContent = useMemo(() => {
    const renderMap = {
      all: <AllKnowledge refreshFlag={refreshFlag} />,
      text: <TextKnowledge refreshFlag={refreshFlag} />,
      table: <TableKnowledge refreshFlag={refreshFlag} />,
      cio: <CioKnowledge refreshFlag={refreshFlag} />,
    };
    return <div style={{ paddingBottom: 20 }}>{renderMap[knowledgeType]}</div>;
  }, [knowledgeType, refreshFlag]);

  return (
    <Container className="new-knowledge">
      <Alert
        style={{
          marginTop: '-20px',
          marginBottom: '10px',
        }}
        description="目前新知识库已经支持 全部应用形态，欢迎使用，旧版知识库即将下线，请尽快完成迁移。"
        showIcon
        type="warning"
      />
      <Card
        title={Title}
        extra={
          <Button type="primary" onClick={() => setCreateModalOpen(true)}>
            创建知识库
          </Button>
        }
      >
        {KnowledgeContent}
      </Card>

      <CreateKnowledgeModal
        open={createModalOpen}
        setOpen={setCreateModalOpen}
        setRefreshFlag={setRefreshFlag}
      />
    </Container>
  );
};

export default NewKnowledge;
