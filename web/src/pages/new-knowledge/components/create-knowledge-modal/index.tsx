import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Form, Input, message, Modal, Radio } from 'antd';
import { KnowledgeApi, NewKnowledge } from '@/api';
import { usePathQuery, useQuery } from '@hooks/useQuery';
import styled from 'styled-components';
import { knowledgeTypeSchema } from '../../config';

interface CreateKnowledgeModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  setRefreshFlag?: Dispatch<SetStateAction<boolean>>;
  onChange?: () => void;
}

const FlexRadioButton = styled(Radio.Button)`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 25%;
  height: 80px;
`;

const ButtonContent = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const CreateKnowledgeModal: React.FC<CreateKnowledgeModalProps> = ({
  open,
  setOpen,
  setRefreshFlag,
  onChange,
}) => {
  const [isCreating, setIsCreating] = useState(false);

  const [form] = Form.useForm();
  const { parsedQuery } = useQuery();
  const { pushAddQuery } = usePathQuery();

  useEffect(() => {
    form.setFieldValue('type', parsedQuery.knowledgeType);
  }, [parsedQuery.knowledgeType]);

  const createKnowledge = async () => {
    try {
      if (!parsedQuery.workspaceId) return;
      const values = await form.validateFields();
      setIsCreating(true);
      const res = await NewKnowledge.createKnowledge({
        ...values,
        workspaceId: parsedQuery.workspaceId,
        groupId: parsedQuery.groupId,
      });
      pushAddQuery(`/new-knowledge/${res.id}`, { knowledgeType: values.type });
      onChange?.();
      setOpen(false);
      setRefreshFlag?.((prev) => !prev);
    } catch (err: any) {
      if (err?.response?.data?.message?.includes('存在重名知识库')) {
        message.error('创建失败, 存在重名知识库');
      } else {
        message.error(`创建失败, ${err.response.data.message}`);
      }
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Modal
      title="创建知识库"
      open={open}
      width={600}
      onCancel={() => setOpen(false)}
      onOk={createKnowledge}
      okButtonProps={{ loading: isCreating }}
    >
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        form={form}
        name="control-hooks"
        style={{ padding: '24px 12px' }}
      >
        <Form.Item name="type" label="类型" rules={[{ required: true }]}>
          <Radio.Group
            defaultValue={knowledgeTypeSchema[1].key}
            style={{ display: 'flex' }}
          >
            {knowledgeTypeSchema
              .filter((i) => i.key !== 'all')
              .map((item) => (
                <FlexRadioButton
                  key={item.key}
                  value={item.key}
                  disabled={!!item.disable}
                >
                  <ButtonContent>
                    <div style={{ fontSize: 24 }}>{item.icon}</div>
                    {item.label}
                  </ButtonContent>
                </FlexRadioButton>
              ))}
          </Radio.Group>
        </Form.Item>

        <Form.Item name="name" label="名称" rules={[{ required: true }]}>
          <Input placeholder="请输入" maxLength={128} showCount />
        </Form.Item>

        <Form.Item name="description" label="描述">
          <Input.TextArea placeholder="请输入" rows={4} maxLength={256} showCount />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateKnowledgeModal;
