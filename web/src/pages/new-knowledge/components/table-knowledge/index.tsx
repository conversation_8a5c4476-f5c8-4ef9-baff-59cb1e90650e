import React, { ChangeEvent, useEffect, useState } from 'react';
import { Button, Flex, Input, message, Table, type TableProps } from 'antd';
import { knowledgeTypeSchema } from '@/pages/new-knowledge/config';
import dayjs from 'dayjs';
import { usePathQuery, useQuery } from '@hooks/useQuery';
import { GroupApi, NewKnowledge } from '@/api';
import { CloseOutlined, SearchOutlined } from '@ant-design/icons';

const PAGE_SIZE = 10;
const groupMap = {};

interface Props {
  refreshFlag: boolean;
}

const TableKnowledge: React.FC<Props> = ({ refreshFlag }) => {
  const [knowledgeList, setKnowledgeList] = useState([]);
  const [keyword, setKeyword] = useState<string>('');
  const [current, setCurrent] = useState(1);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  const { pushAddQuery } = usePathQuery();
  const { parsedQuery } = useQuery();

  const columns: TableProps<any>['columns'] = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, { description }) => {
        return (
          <Flex align="center">
            <div style={{ fontSize: 18, marginRight: 5 }}>
              {knowledgeTypeSchema.find((item) => item.key === 'table')?.icon}
            </div>
            <div>
              <div style={{ fontWeight: 700 }}>{text}</div>
              <div style={{ fontSize: 12 }}>{description}</div>
            </div>
          </Flex>
        );
      },
    },
    {
      title: '分组',
      dataIndex: 'groupId',
      key: 'groupId',
      width: 180,
      render: (groupId) => groupMap[groupId] && groupMap[groupId].name,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => knowledgeTypeSchema.find((i) => i.key === type)?.label,
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      width: 180,
      render: (updateTime) => dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    // {
    //   title: '操作',
    //   key: 'action',
    //   width: 80,
    //   render: (_, record) => (
    //     <Button disabled type="link" onClick={() => console.log(record)}>
    //       <DeleteOutlined />
    //     </Button>
    //   ),
    // },
  ];

  useEffect(() => {
    if (parsedQuery.workspaceId) {
      GroupApi.list(parsedQuery.workspaceId).then((items) => {
        items.forEach((item) => {
          groupMap[item.id] = item;
        });
      });
    }
  }, [parsedQuery.workspaceId]);

  useEffect(() => {
    setKeyword('');
    setCurrent(1);
    getKnowledgeList('', 1);
  }, [refreshFlag]);

  const getKnowledgeList = async (name: string, page: number) => {
    try {
      setLoading(true);
      const { values, total } = await NewKnowledge.getKnowledgeList({
        type: 'table',
        name,
        workspaceId: parsedQuery.workspaceId as string,
        groupId: parsedQuery.groupId as string,
        page,
        pageSize: PAGE_SIZE,
      });
      setTotal(total);
      setKnowledgeList(values);
    } catch (err: any) {
      message.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const onKeywordClear = () => {
    setCurrent(1);
    setKeyword('');
    getKnowledgeList('', 1);
  };

  const onPressEnter = (e: ChangeEvent<HTMLInputElement>) => {
    setKeyword(e.target.value);
    setCurrent(1);
    getKnowledgeList(e.target.value, 1);
  };

  return (
    <>
      <div style={{ padding: 8 }}>
        <Input
          style={{ width: 250 }}
          value={keyword}
          prefix={<SearchOutlined />}
          suffix={
            <Button
              type="link"
              color="default"
              size="small"
              style={{ padding: 0 }}
              disabled={!keyword.length}
              onClick={onKeywordClear}
            >
              <CloseOutlined />
            </Button>
          }
          onChange={onPressEnter}
          placeholder="输入名称搜索知识库"
        />
      </div>

      <Table
        columns={columns as any}
        dataSource={knowledgeList}
        rowKey="id"
        onRow={(record) => ({
          onClick: () =>
            pushAddQuery(`/new-knowledge/${record.id}`, {
              knowledgeType: record.type,
            }),
        })}
        onChange={(pagination) => {
          setCurrent(pagination.current);
          getKnowledgeList(keyword, pagination.current);
        }}
        pagination={{
          total,
          pageSize: PAGE_SIZE,
          current,
        }}
        loading={loading}
      />
    </>
  );
};

export default TableKnowledge;
