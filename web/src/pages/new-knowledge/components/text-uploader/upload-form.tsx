import FileUpload from "@/pages/app/workflow/react-node/file-uploader";
import { Form, Input } from "antd";
import { forwardRef, useEffect, useImperativeHandle } from "react";

const UploadForm = forwardRef((props: { value?: any, onChange: (value: any) => void }, ref) => {
  const { value, onChange } = props;
  const [form] = Form.useForm();
  const file = Form.useWatch('fileList', form);

  useEffect(() => {
    form.setFieldsValue(value);
  }, [value]);

  useImperativeHandle(ref, () => form);

  return <Form
    form={form}
    onValuesChange={(changedValues, values) => {

      if ('fileList' in changedValues) {
        const name = changedValues?.fileList?.[0]?.name;
        values.name = name;
        form.setFieldValue('name', name);
      }
      onChange?.(values);
    }}>
    <Form.Item name="fileList" rules={[{ required: true }]}>
      <FileUpload
        maxCount={1}
        sizeLimit={100 * 1024}
        accept="application/pdf,text/markdown,.doc,.docx,text/plain,.md"
        description="上传 PDF, TXT, MD, DOC, DOCX 格式的本地文件，每个文件不超过100MB"
      />
    </Form.Item>
    <Form.Item name="name" label="名称" rules={[{ required: true }]}>
      <Input />
    </Form.Item>
    <Form.Item name="description" label="描述">
      <Input />
    </Form.Item>
  </Form>

});

export default UploadForm;
