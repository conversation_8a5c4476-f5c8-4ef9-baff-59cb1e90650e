
import { useRef, useState } from "react";
import { <PERSON><PERSON>, message, Modal, Space, Steps } from "antd";
import SliceForm from "./slice-form";
import { NewKnowledge } from "@/api/new-knowledge";
import DocPreview from "./doc-preview";
import UploadForm from "./upload-form";

const TextUploader = (props) => {
  const { knowledgeId, workspaceId, groupId, onChange, trigger } = props;
  const [open, setOpen] = useState(false);
  const [step, setStep] = useState(0);

  const [file, setFile] = useState<{
    name: string,
    description?: string;
    fileList: any[];
  } | undefined>(
    // {
    // fileList:
    // {
    //   "key": "jd-workflow-tmp/langbase/app/upload/files/709c32ed-339f-4fb8-843b-d76445d5f110.pdf",
    //   "url": "https://jd-workflow-tmp.nos-jd.163yun.com/langbase/app/upload/files/709c32ed-339f-4fb8-843b-d76445d5f110.pdf",
    //   "name": "Attention Is All You Need.pdf",
    //   "status": "done",
    //   "file": {
    //     "uid": "rc-upload-1740647651842-5",
    //     "type": "application/pdf"
    //   }
    // }
    // }
  );
  const [reviews, setReviews] = useState([
    // {
    //   "documentId": null,
    //   "reviewId": "c1d4b2fc-c6cc-4c15-80ee-b4abdc6aa571",
    //   "documentName": null,
    //   "documentType": null
    // }
  ]);
  const [uploadLoading, setUploadLoading] = useState(true);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(true);
  const [chunkStrategy, setChunkStrategy] = useState({
    "chunkType": "normal",
    "params": {}
    // params: {
    //   "separator": "#",
    //   "maxTokens": 10000,
    //   "overlap": 10
    // }
  });

  const uploadForm = useRef(null);
  const sliceForm = useRef(null);
  const totalMapRef = useRef<Record<string, number>>(null);

  const clear = () => {
    totalMapRef.current = null;
    setReviews([]);
    setFile(undefined);
    setStep(0);
  };

  const onOpen = () => {
    setOpen(true);
  };

  const onClose = () => {
    clear();
    setOpen(false);
  };

  const goPre = () => {
    setStep(step - 1);
  };

  const goNext = async () => {
    if (step === 0) {
      const newFile = await uploadForm.current?.validateFields();
      setFile(newFile);
      if (!newFile?.fileList?.[0].key) {
        message.error('请上传文本');
        return false;
      }
    }

    // 分段设置
    if (step === 1) {
      const { fileList, name } = file || {};

      const newChunkStrategy = await sliceForm.current?.validateFields();
      // setChunkStrategy({
      //   chunkType: chunkStrategy.chunkType,
      //   params: newChunkStrategy
      // });

      setChunkStrategy(newChunkStrategy);

      // 请求分段提交接口
      const previewParams = {
        reviews: [
          {
            documentId: fileList?.[0]?.file?.uid,
            nosKey: fileList?.[0]?.key,
            documentName: name,
            documentType: fileList?.[0]?.file?.type
          }
        ],
        chunkStrategy: newChunkStrategy
      };

      setSubmitLoading(true);
      try {
        const res = await NewKnowledge.submitFile(previewParams);
        setReviews(res?.reviews);
        setSubmitLoading(false);

      } catch (error) {
        setSubmitLoading(false);
        return false;
      }
    }
    setStep(step + 1);
  }

  const onSubmit = async () => {
    const { fileList, description } = file || {};

    const fileListParams = reviews?.map(review => {
      const file = fileList?.find(file => file.file.uid === review.documentId);

      return {
        name: review.documentName,
        // 知识库类型
        type: 'text',
        // 本地上传
        importType: 'local',
        config: JSON.stringify({
          chunkConfig: chunkStrategy
        }),
        reviewNums: totalMapRef.current?.[review.reviewId],
        reviewId: review.reviewId,
        metaData: JSON.stringify({
          nosKey: file?.key,
          type: review.documentType
        }),
        description: description
      }
    });
    const res = await NewKnowledge.addDoc({
      knowledgeId,
      workspaceId,
      groupId,
      knowledgeItemDetailRequestList: fileListParams
    });

    if (res) {
      message.success('保存成功');
      onChange?.();
      setOpen(false);
      clear();
    }
  };

  const onFileChange = val => {
    if (val?.fileList?.length > 0) {
      setUploadLoading(false);
    } else {
      setUploadLoading(true);
    }
  };

  const onChunkStrategyChange = val => {
    setChunkStrategy(val);
  };

  return <>
    <span onClick={onOpen}>{trigger ?? <Button >上传</Button>}</span>

    <Modal
      open={open}
      title="文本上传"
      onCancel={onClose}
      width={880}
      footer={<>
        {/* 选择文档 */}
        {step === 0 &&
          <Button type="primary"
            disabled={uploadLoading}
            onClick={() => goNext()}>
            下一步</Button>
        }
        {/* 分段设置 */}
        {step === 1 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={goNext} loading={submitLoading}>下一步</Button>
        </Space>
        }
        {/* 分段预览 */}
        {step === 2 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={onSubmit} disabled={previewLoading}>提交</Button>
        </Space>}

        {step === 3 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={onSubmit}>完成</Button>
        </Space>}
      </>}
    >
      <div style={{ marginBottom: '30px' }}>
        <Steps
          current={step}
          items={[
            { title: '选择文档' },
            { title: '分段设置' },
            { title: '分段预览' },
            // { title: '提交' }
          ]}
        />
      </div>

      <div>
        {step === 0 && <UploadForm
          value={file}
          onChange={onFileChange}
          ref={uploadForm}
        />}

        {step === 1 && <SliceForm
          // value={chunkStrategy}
          onChange={onChunkStrategyChange}
          ref={sliceForm}
        />}

        {/* 预览 */}
        {step === 2 && (
          <DocPreview
            itemKey="chunkId"
            doc={reviews?.[0]}
            key={reviews?.[0]?.reviewId}
            onTotalChange={(total) => {
              if (!totalMapRef.current) {
                totalMapRef.current = {};
              }
              totalMapRef.current[reviews?.[0]?.reviewId] = total;
              setPreviewLoading(false);
            }}
            // action={{
            //   api: NewKnowledge.fileSlicePreview,
            //   params: {reviewId: reviews?.[0]?.reviewId},
            // }}
            payload={{ reviewId: reviews?.[0]?.reviewId }}
            fetchData={async params => {
              const res = await NewKnowledge.fileSlicePreview({
                ...params,
                // reviewId: reviews?.[0]?.reviewId
              });
              return res
            }}
          />
        )}

        {/* 提交 */}
        {/* {step === 3 && <DataProcess docs={file?.fileList} />} */}
      </div>
    </Modal>
  </>
};

export default TextUploader;
