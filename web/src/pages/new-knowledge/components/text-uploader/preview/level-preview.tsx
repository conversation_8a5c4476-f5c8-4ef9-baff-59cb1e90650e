import { itemsEqual } from "@dnd-kit/sortable/dist/utilities";
import ChunkPreview from "../chunk-preview";
import { Typography } from "antd";

const LevelPreview = (props) => {
  const { data } = props;

  const renderLevelData = (node) => {
    return node?.map(item => {
      const { chunkId, content, level, title } = item;
      return <div>
        <Typography.Title level={level}>{title}</Typography.Title>
        {/* {content && <ChunkPreview chunkId={chunkId} value={content} />} */}
      </div>
    })
  }

  return <>
    {data?.map(item => {
      return <>
      </>
    })}
  </>
};

export default LevelPreview;