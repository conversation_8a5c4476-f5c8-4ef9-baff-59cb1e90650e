import { List, message, Flex, Spin, Empty } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import VirtualList from 'rc-virtual-list';
import { CheckCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import ChunkPreview from './chunk-preview';

const pageSize = 10;

type RightType = 'preview' | 'edit' | 'delete';

interface Props {
  doc: Record<string, any>;
  itemKey: string;
  onTotalChange?: (total: number) => void;
  containerHeight?: number;
  onLoadData?: (data: any) => void;
  rights?: RightType[];
  hideMessage?: boolean;
  fetchData?: (params: any) => Promise<any>;
  payload?: Record<string, any>;
}

const DocPreview = ({
  doc,
  itemKey,
  onTotalChange,
  containerHeight = 500,
  rights = ['preview'],
  hideMessage = false,
  fetchData,
  payload,
  onLoadData
}: Props) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const totalChunkRef = useRef(0);
  const currentPage = useRef(0);
  const timer = useRef(null);

  const done =
    currentPage.current && currentPage.current * pageSize >= totalChunkRef.current;

  const isFetching = useRef(false);

  const clear = () => {
    clearTime();
    setData([]);
    setLoading(false);
    totalChunkRef.current = 0;
    currentPage.current = 0;
  };

  const clearTime = () => {
    clearTimeout(timer.current);
    timer.current = null;
  };

  useEffect(() => {
    return () => {
      clear();
    };
  }, []);

  useEffect(() => {
    // reviewId 改变的时候 清楚定时器
    clear();
    appendData();
  }, [doc?.reviewId]);

  const appendData = async () => {
    // 已经溢出了 不用继续请求
    if (
      currentPage.current * pageSize >= totalChunkRef.current &&
      currentPage.current > 0
    )
      return;

    if (isFetching.current) return;

    try {
      setLoading(true);
      isFetching.current = true;
      const res = await fetchData({
        ...payload,
        pageNum: currentPage.current + 1,
        pageSize,
      });
      onLoadData?.(res);

      // 请求结束
      isFetching.current = false;
      // 正在解析，等待
      if (res.status === 'waiting') {
        timer.current = setTimeout(() => {
          appendData();
        }, 2000);
      }

      // 解析失败
      if (res.status === 'fail') {
        message.error('文档解析失败');
        clearTime();
        setLoading(false);
      }

      // 解析成功
      if (res.status === 'success') {

        if (done && !hideMessage) message.success('文档解析完成');
        clearTime();
        currentPage.current++;

        if (currentPage.current === 1) {
          totalChunkRef.current = res.content.total;
          onTotalChange?.(res.content.total);
        }

        setData((prev) => [...prev, ...(res.content.chunks || [])]);
        setLoading(false);
      }
    } catch (err) {
      console.log('err', err);
    } finally {
    }
  };

  const onScroll = (e) => {

    if (timer.current) return; // 已经有请求定时器在跑了

    if (done) return; // 已经完成了

    if (
      Math.abs(
        e.currentTarget.scrollHeight - e.currentTarget.scrollTop - containerHeight,
      ) <= 200
    ) {
      appendData();
    }
  };

  return (
    <>
      <List loading={currentPage.current === 0 && loading}>
        {data.length ? (
          <VirtualList
            height={currentPage.current === 0 ? 100 : containerHeight}
            itemKey={itemKey}
            data={data}
            onScroll={onScroll}
          >
            {({ chunkId, text, order }) => (
              <List.Item key={chunkId}>
                <ChunkPreview
                  chunkId={chunkId}
                  value={text}
                  order={order}
                  editable={rights.includes('edit')}
                  deletable={rights.includes('delete')}
                  onEdit={(editChunkId, editText) =>
                    setData((prev) =>
                      prev.map((i) => ({
                        ...i,
                        text: i.chunkId === editChunkId ? editText : i.text,
                      })),
                    )
                  }
                  onDelete={(deleteChunkId) =>
                    setData((prev) => prev.filter((i) => i.chunkId !== deleteChunkId))
                  }
                />
              </List.Item>
            )}
          </VirtualList>
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </List>

      <Flex style={{ height: '20px', marginTop: '10px', fontSize: '12px' }} justify="center">
        {/* <Spin spinning={loading && currentPage.current > 0} /> */}
        {!!totalChunkRef.current && !done && (
          <Flex style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: '12px' }} gap={4}>
            <Flex style={{ width: '20px' }}>
              <span>
                {loading && currentPage.current > 0 && <LoadingOutlined />}
              </span>
            </Flex>
            共 {totalChunkRef.current} 个分段，已预览 {data?.length} 个
          </Flex>
        )}
        {!!totalChunkRef.current && done && (
          <Flex style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: '12px' }} gap={4}>
            <CheckCircleOutlined />共 {totalChunkRef.current} 个分段，已预览 {data?.length} 个
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default DocPreview;
