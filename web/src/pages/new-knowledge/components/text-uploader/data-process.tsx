import { IconFont } from "@/components/icons";
import { List } from "antd";
import styled from "styled-components";
import { textIconMap } from "../../config";
import { getFileType } from "../../utils";

const StyledDiv = styled.div`
  .ant-list-item-meta-title {
    text-align: left;
  }
`;

const DataProcess = (props) => {
  const { docs } = props;
  console.log('DataProcess', docs);


  return <StyledDiv>
    <List
      dataSource={docs}
      renderItem={(item: any) => {
        const { name, file } = item;
        const fileType = getFileType({ name, type: file?.file?.type });
        const icon = textIconMap[fileType];
        return <List.Item>
          <List.Item.Meta
            avatar={
              <span style={{ fontSize: '26px' }}>
                {icon}
              </span>
            }
            title={name}
          // description=""
          />
        </List.Item>
      }}
    ></List>
  </StyledDiv>
};

export default DataProcess;
