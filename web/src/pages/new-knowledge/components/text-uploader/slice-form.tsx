import { Checkbox, Divider, Flex, Form, Input, InputNumber, Select, Typography } from "antd";
import classNames from "classnames";
import { omit } from "lodash";
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import styled from "styled-components";

const StyledSliceForm = styled.div`
  &.small-size {
    .divider {
      margin: 12px 0;
    }
    .slice-item-form {
      padding: 8px 8px 8px 9px;
    }
    .ant-form-item {
      margin-bottom: 10px;
    }
    .ant-form-item-label {
      padding: 0 0 0 4px;
    }
  }
  &.default-size {
    padding: 16px 16px 16px 18px;
  }
`;

const { Text, Link } = Typography;

const SliceItemForm = styled(Flex)`
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px 16px 16px 18px;
  
  cursor: pointer;
  &.checked {
    border: 1px solid #91caff;
    background: #e6f4ff;
  }
`;

interface IProps {
  value?: any;
  onChange?: (value: any) => void;
  size?: 'default' | 'small';
}

const SliceForm = forwardRef((props: IProps, ref) => {
  const { value, onChange, size = 'default' } = props;
  // const { chunkType, params } = value || {};
  const [chunkType, setChunkType] = useState('normal');

  const customSliceRef = useRef(null);
  const levelSliceRef = useRef(null);


  useEffect(() => {
    let form;
    const chunkConfig = omit(value, ['chunkType'])?.params;

    if (chunkType === 'custom') {
      form = customSliceRef.current
    }
    if (chunkType === 'recursive') {
      form = levelSliceRef.current;
    }
    form?.setFieldsValue(chunkConfig);

  }, [value, chunkType]);

  useImperativeHandle(ref, () => {
    let form;
    if (chunkType === 'custom') {
      form = customSliceRef.current
    }
    if (chunkType === 'recursive') {
      form = levelSliceRef.current;
    }
    return {
      ...form,
      validateFields: async () => {
        if (chunkType === 'normal') {
          return {
            chunkType: 'normal'
          }
        }
        // try {


        const values = await form.validateFields();
        return {
          params: values,
          chunkType,
        }
        // } catch (error) {
        //   throw new Error(error?.message)
        // }
      }
    };
  });

  const onChunkTypeChange = newType => {
    // onChange?.({
    //   chunkType: newType
    // });
    setChunkType(newType);
  }

  const chunkList = [
    { title: '自动分段与清洗', desc: '自动分段与预处理规则', code: 'normal' },
    { title: '自定义', desc: '自定义分段规则、分段长度及预处理规则', code: 'custom', ref: customSliceRef, form: <CustomSlice ref={customSliceRef} /> },
    { title: '按层级分段', desc: '按照文档层级结构分段，将文档转化为有层级信息的结构', code: 'recursive', ref: levelSliceRef, form: <LevelSlice ref={levelSliceRef} /> }
  ];

  const cls = classNames({
    'small-size': size === 'small',
    'default-size': size === 'default'
  });

  return <StyledSliceForm className={cls}>
    <Flex vertical gap={10}>
      {/* <SliceItem
      title="自动分段与清洗"
      desc="自动分段与预处理规则"
      code="normal"
      checked={chunkType === 'normal'}
      onChange={onChunkTypeChange}>
    </SliceItem>
    <SliceItem
      title="自定义"
      desc="自定义分段规则、分段长度及预处理规则"
      code="custom"
      checked={chunkType === 'custom'}
      onChange={onChunkTypeChange}>

    </SliceItem>
    <SliceItem
      title="按层级分段"
      desc="按照文档层级结构分段，将文档转化为有层级信息的树结构"
      code="level" checked={chunkType === 'level'}
      onChange={onChunkTypeChange}>
      <LevelSlice ref={levelSliceRef} />
    </SliceItem> */}
      {chunkList.map(chunk => {
        const { title, desc, code, form } = chunk;
        return <SliceItem
          key={code}
          title={title}
          desc={desc}
          code={code}
          checked={chunkType === code}
          onChange={onChunkTypeChange}
        >{form}</SliceItem>
      })}
    </Flex>
  </StyledSliceForm>
});

const SliceFlag = (props) => {
  const { value, onChange } = props;
  // 没有填写过值
  const noValue = typeof value === undefined;

  const isCustom = value?.[0] !== '\n\n';

  return <Flex vertical>
    <Select
      value={isCustom ? 'custom' : value?.[0]}
      options={[
        { label: '换行', value: '\n\n' },
        { label: '自定义', value: 'custom' },
      ]}
      onChange={val => {
        // 以后seperator会有多个，是数组
        if (val) {
          if (val === 'custom') {
            onChange([]);
          } else {
            onChange([val]);
          }
        } else {
          onChange([]);
        }
      }}
      style={{ marginBottom: '6px' }}
    />
    <div>
      {isCustom && !noValue && <Input
        placeholder="请输入分段标识符"
        value={value}
        onChange={ev => {
          if (ev.target.value) {
            onChange([ev.target.value])
          } else {
            onChange([]);
          }
        }} />}
    </div>
  </Flex>

}

const CustomSlice = forwardRef((props, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => form)

  return <div>
    <Form layout="vertical" form={form}>
      <Form.Item label="分段标识符"
        name="separator"
        rules={[{
          required: true
        }]}>
        <SliceFlag />
      </Form.Item>
      <Form.Item label="分段最大长度"
        name="maxTokens"
        rules={[{
          required: true
        }]}>
        <InputNumber style={{ width: '100%' }} />
      </Form.Item>
      <Form.Item label="分段最小长度"
        name="minTokens"
        rules={[{
          required: true
        }]}
        tooltip="若分段长度小于 分段最小长度，会根据策略合并上下分段。"
      >
        <InputNumber style={{ width: '100%' }} />
      </Form.Item>
      <Form.Item label="分段重叠度%" name="overlap"
        rules={[{
          required: true
        }]}
      >
        <InputNumber style={{ width: '100%' }} />
      </Form.Item>
    </Form>
  </div>
});

const LevelSlice = forwardRef((props, ref) => {
  const { } = props;
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => form);

  return <>
    <Form layout="vertical" form={form}>
      <Form.Item name="maxLevel" label="分段层级">
        <InputNumber style={{ width: '100%' }} />
      </Form.Item>
      <Form.Item name="saveTitle" valuePropName="checked">
        <Checkbox>检索切片保留</Checkbox>
      </Form.Item>
    </Form>
  </>
})

const SliceItem = props => {
  const { title, desc, code, onChange, checked, children } = props;
  const onTypeChange = () => {
    onChange?.(
      code
    )

  };

  const cls = classNames({
    'slice-item-form': true,
    'checked': checked,
  });
  return <SliceItemForm vertical
    className={cls}
    onClick={onTypeChange}>
    <Flex>
      <Flex vertical>
        <Text>{title}</Text>
        <Text type="secondary">{desc}</Text>
      </Flex>

    </Flex>

    {checked && children && <div>
      <Divider className="divider" />
      {children}
    </div>}

  </SliceItemForm>
}


export default SliceForm;