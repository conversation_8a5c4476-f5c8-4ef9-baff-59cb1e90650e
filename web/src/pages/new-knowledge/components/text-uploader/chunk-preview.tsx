import { Button, Flex, Input, message, Modal, Popover } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { useState } from 'react';
import { NewKnowledge } from '@/api';
import { useParams } from 'react-router-dom';
import { useQuery } from '@hooks/useQuery';

interface Props {
  chunkId: string;
  value: string;
  order: number;
  editable: boolean;
  deletable: boolean;
  onEdit?: (chunkId: string, chunk: string) => void;
  onDelete?: (chunkId: string) => void;
}

const Content = styled(Flex)`
  position: relative;
  background-color: #f5f8fc;

  &:hover .tools {
    display: block;
  }

  &:hover {
    background-color: #f2f3f7;
  }
`;

const StyleTextArea = styled(Input.TextArea)`
  width: 100%;
  resize: none;
  cursor: default;
`;

const ToolContainer = styled.div`
  position: absolute;
  right: 10px;
  top: 5px;
  display: none;
  padding: 2px 5px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.1);
`;

const HoverButton = styled(Button)`
  color: #666;

  &:hover {
    background-color: #f2f3f7;
  }
`;

const ChunkPreview = ({
  chunkId,
  value,
  order,
  editable,
  deletable,
  onEdit,
  onDelete,
}: Props) => {
  const [valueBackup, setValueBackup] = useState(value);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [editing, setEditing] = useState(false);

  const params = useParams();
  const { parsedQuery } = useQuery();

  const handleEdit = async () => {
    try {
      setEditing(true);
      await NewKnowledge.updateFragmentInfo({
        knowledgeId: params.knowledgeId,
        knowledgeItemId: parsedQuery.docId as string,
        fragmentId: chunkId,
        serialNum: order,
        content: valueBackup,
      });
      onEdit?.(chunkId, valueBackup);
      message.success('编辑成功');
      setShowEditModal(false);
    } catch (err: any) {
      message.error(`编辑失败, ${err.message}`);
    } finally {
      setEditing(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleting(true);
      await NewKnowledge.deleteFragment({
        knowledgeId: params.knowledgeId,
        knowledgeItemId: parsedQuery.docId as string,
        fragmentId: chunkId,
      });
      onDelete?.(chunkId);
      message.success('删除成功');
      setShowDeleteModal(false);
    } catch (err: any) {
      message.error(`删除失败, ${err.message}`);
    } finally {
      setDeleting(false);
    }
  };

  const toolMenus = [
    {
      type: 'edit',
      label: '编辑',
      icon: <EditOutlined />,
      condition: editable,
      loading: editing,
      onClick: () => setShowEditModal(true),
    },
    {
      type: 'delete',
      label: '删除',
      icon: <DeleteOutlined />,
      condition: deletable,
      loading: deleting,
      onClick: () => setShowDeleteModal(true),
    },
  ];

  return (
    <>
      <Flex style={{ width: '100%' }}>
        {/*<Flex*/}
        {/*  style={{ width: '20px', color: 'rgba(0,0,0,0.45)', fontWeight: 'bold' }}*/}
        {/*  align="center"*/}
        {/*>*/}
        {/*  {order + 1}*/}
        {/*</Flex>*/}

        <Content flex={1}>
          <StyleTextArea readOnly autoSize bordered={false} value={value} />

          {toolMenus.some((menu) => menu.condition) && (
            <ToolContainer className="tools">
              {toolMenus.map((menu) => {
                if (!menu.condition) return null;
                return (
                  <Popover key={menu.type} content={menu.label}>
                    <HoverButton
                      type="link"
                      size="small"
                      icon={menu.icon}
                      loading={!!menu.loading}
                      onClick={menu.onClick}
                    />
                  </Popover>
                );
              })}
            </ToolContainer>
          )}
        </Content>
      </Flex>

      <Modal
        centered
        title={`#${order + 1}`}
        width={800}
        open={showEditModal}
        onOk={handleEdit}
        confirmLoading={editing}
        onCancel={() => {
          setShowEditModal(false);
        }}
      >
        <Input.TextArea
          style={{ width: '100%' }}
          autoSize={{ minRows: 20, maxRows: 20 }}
          value={valueBackup}
          onChange={(e) => setValueBackup(e.target.value)}
        />
      </Modal>

      <Modal
        centered
        title="确认删除"
        width={400}
        open={showDeleteModal}
        onOk={handleDelete}
        okType="danger"
        confirmLoading={deleting}
        onCancel={() => {
          setShowDeleteModal(false);
        }}
      >
        <div>此操作不可撤回</div>
      </Modal>
    </>
  );
};

export default ChunkPreview;
