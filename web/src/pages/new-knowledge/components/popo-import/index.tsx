import { Button, Col, Flex, Form, Input, message, Modal, Radio, Row, Space, Steps } from "antd";
import { useRef, useState } from "react";
import SliceForm from "../text-uploader/slice-form";
import { NewKnowledge } from "@/api";
import CatalogTree from "./catalog-tree";
import { useGlobalState } from "@/hooks/useGlobalState";

const documentFormLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const PopoImport = (props) => {
  const { knowledgeId, groupId, workspaceId, onChange } = props;
  const [open, setOpen] = useState(false);
  const [step, setStep] = useState(0);

  const [popoForm] = Form.useForm();

  const sliceFormRef = useRef(null);

  const [updateForm] = Form.useForm();

  const [importLoading, setImportLoading] = useState();
  const [submitLoading, setSubmitLoading] = useState(false);

  const [popoUrl, setPopoUrl] = useState();

  const [popoDocs, setPopoDocs] = useState(null);

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const reset = () => {
    popoForm.resetFields();
    updateForm.resetFields();
    sliceFormRef.current?.resetFields?.();
    setStep(0);
  };

  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    reset();
    setOpen(false);
  };

  const onDocsChange = val => {
    setPopoDocs(val);
  };

  const goNext = async () => {

    if (step === 0) {
      const val = await popoForm.validateFields();

      setPopoUrl(val?.popo_url);
    }

    if (step === 1) {

    }
    setStep(step + 1);

  };

  const goPre = () => {
    setStep(step - 1);
  };

  const onSubmit = async () => {
    // popourls
    const popoUrls = popoDocs?.map(doc => doc.key);
    // 分段策略
    const sliceValues = await sliceFormRef.current?.validateFields();
    const updateValues = await updateForm.validateFields();

    const params = {
      // popo_urls: popoUrls,
      fileList: popoDocs?.map(doc => {
        return {
          popoUrl: doc?.key,
          name: doc?.title,
          type: doc?.nodeType
        }
      }),
      knowledgeId, groupId, workspaceId,
      chunkStrategy: sliceValues,
      overwrite: updateValues?.overwrite,
      config: JSON.stringify(
        {
          operator: user.name,
          chunkConfig: {
            chunkType: sliceValues?.chunkType,
            chunkStrategy: {
              ...sliceValues,
            },
          }
        }
      )
    };

    try {
      setSubmitLoading(true);
      const res = await NewKnowledge.submitPopoTask(params);
      // setSubmitLoading(false);

      if (res?.debugInfo) {
        message.error(res.debugInfo);
        return;
      }
      onChange?.();

      setTimeout(() => {
        setSubmitLoading(false);
        onCancel();
      }, 1000);
    } catch (error) {
      setSubmitLoading(false);
    }
  };

  return <>
    <Flex onClick={onOpen} align='center'>POPO 文档</Flex>
    <Modal title="POPO 导入"
      width={800}
      styles={{
        body: {
          overflow: 'scroll'
        }
      }}
      open={open}
      onCancel={onCancel}
      footer={<>
        {/* 导入 */}
        {step === 0 &&
          <Button type="primary"
            disabled={importLoading}
            onClick={() => goNext()}>
            下一步</Button>
        }
        {/* 选择文档 */}
        {step === 1 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={goNext} disabled={!popoDocs || (popoDocs?.length < 1)}>下一步</Button>
        </Space>}
        {/* 选择分割策略 */}

        {step === 2 && <Space>
          <Button onClick={goPre} disabled={submitLoading}>上一步</Button>
          <Button type="primary" onClick={onSubmit} loading={submitLoading}>提交</Button>
        </Space>}
      </>}
    >
      <Flex vertical style={{ width: '100%', height: '100%' }}>
        <div style={{ marginBottom: '30px', width: '100%' }}>
          <Steps
            current={step}
            items={[
              { title: 'POPO 导入' },
              { title: '选择文档' },
              { title: '导入策略' },
            ]}
          />
        </div>
        <Flex flex={1} style={{ width: '100%' }}
        >
          {step === 0 && <Flex
            style={{ width: '100%', height: '100%' }}
            vertical
          >
            <Form  {...documentFormLayout} form={popoForm}>
              <Form.Item label="POPO链接"
                name="popo_url"
                rules={[
                  { required: true }
                ]}>
                <Input.TextArea rows={6} />
              </Form.Item>
            </Form>
            {/* <Divider /> */}
          </Flex >}
          {step === 1 && <div style={{ width: '100%', height: '100%' }}>
            <CatalogTree popoUrl={popoUrl} onChange={onDocsChange} />
          </div>}
          {step === 2 && <div style={{ width: '100%', height: '100%' }}>
            <Row style={{ marginBottom: 18 }}>
              <Col span={6} style={{ textAlign: 'right' }}>
                分段策略：
              </Col>
              <Col span={16}>
                <SliceForm
                  ref={sliceFormRef}
                  // value={chunkStrategy}
                  // onChange={onChunkStrategyChange}
                  size="small"
                />
              </Col>
            </Row>
            <Form {...documentFormLayout} form={updateForm}>
              <Form.Item initialValue={true} name={"overwrite"} label="文档更新策略" tooltip={<>如果文档之前已经被导入过（已有知识库中存在 URL 相同的文档），可以选择覆盖已有文档并重新导入分片，或作为新的文档导入</>}>
                <Radio.Group>
                  <Radio value={true}>自动覆盖已有文档</Radio>
                  <Radio value={false}>创建新的文档</Radio>
                </Radio.Group>
              </Form.Item>
            </Form>
          </div>}
        </Flex>
      </Flex>
    </Modal >
  </>
};

export default PopoImport;
