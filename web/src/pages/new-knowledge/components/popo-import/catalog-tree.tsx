import React, { useEffect, useState } from 'react';
import { Empty, Flex, message, Spin, Tree } from 'antd';
import { NewKnowledge } from '@/api';
import { useRequest } from 'ahooks';
import { DeleteOutlined, DoubleRightOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { FileTypeEnum } from '@/interface';

const initTreeData = [
  { title: 'Expand to load', key: '0' },
  { title: 'Expand to load', key: '1' },
  { title: 'Tree Node', key: '2', isLeaf: true },
];

const SelectDoc = styled(Flex)`
  padding: 4px;
  &:hover {
  background-color: #f5f5f5;
  }
`;

// It's just a simple demo. You can use tree map to optimize update perf.
const updateTreeData = (list, key, children) =>
  list.map(node => {
    if (node.key === key) {
      return Object.assign(Object.assign({}, node), { children });
    }
    if (node.children) {
      return Object.assign(Object.assign({}, node), {
        children: updateTreeData(node.children, key, children),
      });
    }
    return node;
  });
const CatalogTree = (props) => {
  const { popoUrl, onChange } = props;
  const [initTreeData, setInitTreeData] = useState(false);
  const [treeData, setTreeData] = useState([]);
  const [initLoading, setInitLoading] = useState(false);
  const [checkedNodes, setCheckedNodes] = useState(null);

  const [defaultExpandedKeys, setDefaultExpandedKeys] = useState([]);

  const { data, run, runAsync } = useRequest(async (key) => {
    const res = await NewKnowledge.getPopoCatalog({
      popoUrl: key
    });
    if (res?.debugInfo) {
      message.error(res.debugInfo);
    }
    return res;
  }, {
    manual: true,
    debounceWait: 100,
  })

  const formatLeaf = (node) => {
    if (!node) return node;
    if (node.type === FileTypeEnum.文件夹) {
      node.isLeaf = false;
      node.checkable = false;
    } else {
      node.isLeaf = true
    }
    node.nodeType = node.type;
    delete node.type

    node?.children?.forEach(child => {
      if (child.type === FileTypeEnum.文件夹) {
        child.isLeaf = false;
        child.checkable = false;

      } else {
        child.isLeaf = true
      }
      child.nodeType = child.type;
      delete child.type
    });

    return node;
  }

  useEffect(() => {
    setInitLoading(true);
    NewKnowledge.getPopoCatalog({
      popoUrl: popoUrl
    }).then(res => {
      if (res?.debugInfo) {
        return message.error(res.debugInfo)
      }
      if (res?.key) {
        setDefaultExpandedKeys([res.key]);
        setTreeData([formatLeaf(res)]);
        setInitTreeData(true);
      } else {
        setInitTreeData(false);
      }
      // setInitLoading(false);

    }).finally(() => {
      setTimeout(() => {
        setInitLoading(false);
      }, 500);
    });
  }, [popoUrl]);

  const onLoadData = async ({ key, children }) => {

    if (children) {
      return treeData;
    }

    const res = await runAsync(key);
    const newVal = formatLeaf(res);

    setTreeData(origin =>
      updateTreeData(origin, key, newVal?.children),
    );
  };

  const onCheck = (checked, e) => {
    setCheckedNodes(e.checkedNodes);
    onChange?.(e.checkedNodes);
  };

  const onDelete = key => {
    const newDocs = checkedNodes?.filter(node => node.key !== key);
    setCheckedNodes(newDocs);
  }

  // if (!initTreeData) {
  //   return <Empty />
  // }
  return <Flex style={{ width: '100%', minHeight: '200px', marginBottom: 20 }} vertical>
    <Spin spinning={initLoading} style={{ width: '100%', height: '100%' }}>
      {initTreeData ? <Flex style={{ width: '100%', minHeight: '200px' }}>
        <Flex style={{ maxHeight: '400px', overflow: 'scroll', width: '45%' }}>
          <Tree.DirectoryTree
            checkable
            // defaultExpandAll
            loadData={onLoadData}
            treeData={treeData}
            onCheck={onCheck}
            defaultExpandedKeys={defaultExpandedKeys}
            checkedKeys={checkedNodes?.map(node => node.key)}
          />
        </Flex>
        <Flex style={{ width: '10%' }} justify='center' align='center'>
          <DoubleRightOutlined />
        </Flex>
        <Flex vertical style={{ maxHeight: '400px', overflow: 'scroll', width: '45%' }}>
          <h4>已选择的文档</h4>
          {checkedNodes?.length ?
            <Flex vertical gap={6}>
              {checkedNodes?.map(node => {
                return <SelectDoc key={node.key} justify='space-between'>
                  {node?.title}
                  <DeleteOutlined onClick={() => onDelete(node.key)} />
                </SelectDoc>
              })}
            </Flex> :
            <Flex style={{ width: '100%', height: '100%' }} justify='center' align='center'>
              <Empty description="请从左侧选择要导入的文档"
              />
            </Flex>}
        </Flex>
      </Flex> : <Empty />}
    </Spin>

  </Flex>;
};
export default CatalogTree;