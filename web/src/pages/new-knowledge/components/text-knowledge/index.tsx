import { Button, Flex, Input, Table, type TableProps } from 'antd';
import React, { ChangeEvent, useEffect, useState } from 'react';
import { CloseOutlined, SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { NewKnowledgeModel } from '@/interface';
import { usePathQuery, useQuery } from '@hooks/useQuery';
import { GroupApi, NewKnowledge } from '@/api';
import { knowledgeTypeSchema } from '../../config';

const PAGE_SIZE = 10;
const groupMap = {};

interface DataType {
  id: string;
  name: string;
  documentCount: number;
  group: string;
  description: string;
}

interface TextKnowledgeProps {
  refreshFlag: boolean;
}

const TextKnowledge: React.FC<TextKnowledgeProps> = ({ refreshFlag }) => {
  const [knowledgeList, setKnowledgeList] = useState<NewKnowledgeModel[]>();
  const [keyword, setKeyword] = useState<string>('');
  const [current, setCurrent] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [total, setTotal] = useState<number>(0);

  const { pushAddQuery } = usePathQuery();
  const { parsedQuery } = useQuery();

  useEffect(() => {
    if (parsedQuery.workspaceId) {
      GroupApi.list(parsedQuery.workspaceId).then((items) => {
        const options = items.map((item) => {
          groupMap[item.id] = item;
          return {
            label: item.name,
            value: item.id,
          };
        });
      });
    }
  }, [parsedQuery.workspaceId]);

  const columns: TableProps<DataType>['columns'] = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, { description }) => {
        return (
          <Flex align="center">
            <div style={{ fontSize: 18, marginRight: 5 }}>
              {knowledgeTypeSchema.find((item) => item.key === 'text')?.icon}
            </div>
            <div>
              <div style={{ fontWeight: 700 }}>{text}</div>
              <div style={{ fontSize: 12 }}>{description}</div>
            </div>
          </Flex>
        );
      },
    },
    {
      title: '分组',
      dataIndex: 'groupId',
      key: 'groupId',
      width: 180,
      render: (groupId) => groupMap[groupId] && groupMap[groupId].name,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => knowledgeTypeSchema.find((i) => i.key === type)?.label,
    },
    {
      title: '包含文档',
      dataIndex: 'documentCount',
      key: 'documentCount',
      width: 90,
      render: (count = 0) => count,
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime',
      width: 180,
      render: (updateTime) => dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss'),
    },
    // {
    //   title: '操作',
    //   key: 'action',
    //   width: 80,
    //   render: (_, record) => (
    //     <Button disabled type="link" onClick={() => console.log(record)}>
    //       <DeleteOutlined />
    //     </Button>
    //   ),
    // },
  ];

  const getKnowledgeList = async (searchKey: string, page: number) => {
    try {
      setLoading(true);
      const { values: list, total } = await NewKnowledge.getKnowledgeList({
        type: 'text',
        name: searchKey,
        workspaceId: parsedQuery.workspaceId as string,
        groupId: parsedQuery.groupId as string,
        page,
        pageSize: PAGE_SIZE,
      });
      setKnowledgeList(list);
      setTotal(total);
    } catch (err) {
      console.log('err', err);
    } finally {
      setLoading(false);
    }
  };

  const onKeywordClear = () => {
    setCurrent(1);
    setKeyword('');
    getKnowledgeList('', 1);
  };

  const onPressEnter = (e: ChangeEvent<HTMLInputElement>) => {
    setKeyword(e.target.value);
    setCurrent(1);
    getKnowledgeList(e.target.value, 1);
  };

  useEffect(() => {
    setKeyword('');
    setCurrent(1);
    getKnowledgeList('', 1);
  }, [refreshFlag]);

  return (
    <>
      <div style={{ padding: 8 }}>
        <Input
          style={{ width: 250 }}
          value={keyword}
          prefix={<SearchOutlined />}
          suffix={
            <Button
              type="link"
              color="default"
              size="small"
              style={{ padding: 0 }}
              disabled={!keyword.length}
              onClick={onKeywordClear}
            >
              <CloseOutlined />
            </Button>
          }
          onChange={onPressEnter}
          placeholder="输入名称搜索知识库"
        />
      </div>

      <Table
        columns={columns as any}
        dataSource={knowledgeList}
        rowKey="id"
        onRow={(record) => ({
          onClick: () =>
            pushAddQuery(`/new-knowledge/${record.id}`, {
              knowledgeType: record.type,
            }),
        })}
        onChange={(pagination) => {
          setCurrent(pagination.current);
          getKnowledgeList(keyword, pagination.current);
        }}
        pagination={{
          total,
          pageSize: PAGE_SIZE,
          current,
        }}
        loading={loading}
      />
    </>
  );
};

export default TextKnowledge;
