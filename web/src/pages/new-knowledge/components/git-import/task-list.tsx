import { NewKnowledge } from "@/api";
import { useRequest } from "ahooks";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lex, FloatButton, message, Popconfirm, Popover, Table, Tooltip } from "antd";
import { useEffect, useState } from "react";
import ReactJson from "react-json-view";

const GitTaskList = (props) => {
  const { workspaceId, groupId, knowledgeId, onChange } = props;
  const [open, setOpen] = useState(false);

  const [pageInfo, setPageInfo] = useState({
    pageSize: 20,
    pageNum: 1
  });

  const { data, loading, run } = useRequest<any, any>(async (params?: any) => {
    if (workspaceId && groupId && knowledgeId) {
      const res = await NewKnowledge.getTaskList({
        workspaceId,
        groupId,
        knowledgeId,
        ...pageInfo,
        ...params,
      });
      return res;
    }
  }, {
    refreshDeps: [workspaceId,
      groupId,
      knowledgeId, open],

  });

  const onOpen = () => {
    setOpen(true);
    setPageInfo({
      pageNum: 1,
      pageSize: 20
    });
    run({
      pageNum: 1,
      pageSize: 20
    });
  };

  const onCancel = () => {
    setOpen(false);
  };

  const onDelete = async taskId => {
    const res = await NewKnowledge.deleteTaskKnowledgeItems({
      taskId,
      knowledgeId
    });

    if(res) {
      message.success('删除成功');
    }
    run();
    onChange?.();
  }

  return <>
    {/* {data?.total && */}
    <Tooltip title="导入任务">
      <FloatButton onClick={onOpen} />
    </Tooltip>
    {/* // } */}

    <Drawer
      width={'100vw'}
      title="导入任务"
      open={open}
      onClose={onCancel}>
      <Table loading={loading}
        dataSource={data?.values as any[]}
        columns={[
          { title: '任务ID', dataIndex: 'id' },
          {
            title: '类型', dataIndex: 'type', width: 104,
            render(value, record, index) {
              const config = JSON.parse(record?.config || '{}');

              if (config?.fileList) {
                return 'POPO 导入'
              }
              if (config?.repo) {
                return 'GIT 导入'
              }

              return '-'

            },
          },
          {
            title: '详情', dataIndex: 'config', render: (value, record, index) => {
              const config = JSON.parse(value || '{}');
              // console.log('config', config);
              if (config?.fileList) {
                return <Flex wrap="wrap">
                  {config?.fileList?.map(file => {
                    return <Flex align="center" key={file.popo_url}>
                      <a
                        target="_blank">{file.name}</a>
                      <Divider type="vertical" />
                    </Flex>
                  })}
                </Flex>
              }
              let label = "详情";
              if (config?.repo) {
                label = config?.repo
              }
              return <Popover content={<ReactJson src={config} />}>
                <a>{label}</a>
              </Popover>
            },
          },
          { title: '状态', dataIndex: 'status' },
          { title: '文件总数', dataIndex: 'total' },
          { title: '成功', dataIndex: 'successNum' },
          { title: '失败', dataIndex: 'failNum' },
          {
            title: '创建人', dataIndex: 'operator', render(value, record, index) {
              const config = JSON.parse(JSON.parse(record?.config || '{}')?.config || '{}');
              return config?.operator
            },
          }, {
            title: '创建时间', dataIndex: 'createTime', render(value, record, index) {
              const config = JSON.parse(record?.config || '{}');
              return config?.createTime
            },
          },
          {
            title: '操作', dataIndex: 'operate', render(value, record, index) {

              return <Popconfirm title="确认删除任务关联的文档？删除后无法恢复" onConfirm={() => onDelete(record?.id)}>
                <Button danger type="text">删除</Button>
              </Popconfirm>
            },
          }
        ]}
        pagination={{
          total: data?.total,
          showTotal(total, range) {
            return `共 ${total} 条任务`
          },
          onChange(page, pageSize) {
            setPageInfo({
              pageNum: page,
              pageSize
            });
            run({
              pageNum: page,
              pageSize
            });
          },
        }}
      />
    </Drawer>
  </>
}

export default GitTaskList;
