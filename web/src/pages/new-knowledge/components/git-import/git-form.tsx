import { Form, Input } from "antd";
import { forwardRef, useImperativeHandle } from "react"

const GitForm = (props, ref) => {
  const { formProps } = props;
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => form);

  const repoFormat = (value) => {
    const prefix = 'ssh://********************:22222/';
    const prefixHttp = 'https://g.hz.netease.com/';
    const replaceValue = val => (val || '').replace(prefix, '').replace(prefixHttp, '').replace(/\.git$/, '');
    return replaceValue(value);
  }

  return <>
    <Form form={form}
      {...formProps}
    >
      {/* <Form.Item initialValue='gitlab' name={["remoteConfig", "source"]} label="文档来源">
        <Radio.Group>
          <Radio value='gitlab'>GitLab</Radio>
        </Radio.Group>
      </Form.Item> */}
      <Form.Item normalize={repoFormat} name={"repo"} label="GitLab 仓库" rules={[{ required: true }]}>
        <Input placeholder="请输入" maxLength={128} showCount />
      </Form.Item>
      <Form.Item initialValue='master' name={"branch"} label="分支">
        <Input placeholder="请输入" maxLength={128} showCount />
      </Form.Item>
      <Form.Item name={"commitId"} label="commit hash">
        <Input placeholder="请输入" maxLength={128} showCount />
      </Form.Item>
      <Form.Item initialValue='/' name={"specifyPath"} label="扫描路径">
        <Input placeholder="请输入" maxLength={128} showCount />
      </Form.Item>
      <Form.Item initialValue=".md,.mdx,.txt,.pdf,.doc,.docx" name={"suffixs"} label="导入文件格式">
        <Input placeholder="请输入" maxLength={128} showCount  disabled />
      </Form.Item>
      <Form.Item initialValue="test/" name="specifyExclusionPath" label="排除路径">
        <Input placeholder="请输入" maxLength={128} showCount />
      </Form.Item>
    </Form>
  </>
}

export default forwardRef(GitForm);
