import { Modal, Form, Radio, Checkbox, Divider, Row, Col, message, Flex } from 'antd';
import { useRef, useState } from 'react';
import { NewKnowledge } from '@/api';
import GitForm from './git-form';
import SliceForm from '../text-uploader/slice-form';
import { useGlobalState } from '@/hooks/useGlobalState';

const documentFormLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

export default function GitImport(props) {
  const { knowledgeId, groupId, workspaceId, onChange } = props;
  const [open, setOpen] = useState(false);
  const [submiting, setSubmiting] = useState<boolean>(false);

  const sliceFormRef = useRef(null);
  const gitFormRef = useRef(null);
  const [updateForm] = Form.useForm();

   const { globalState } = useGlobalState();
    const { user } = globalState;

  const reset = () => {
    updateForm.resetFields();
    gitFormRef.current?.resetFields?.();
    sliceFormRef.current?.resetFields?.();
  };

  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    reset();
    setOpen(false);
  };

  const onOk = async () => {
    const gitValues = await gitFormRef.current?.validateFields();

    const sliceValues = await sliceFormRef.current?.validateFields();

    const updateValues = await updateForm.validateFields();


    const params = {
      workspaceId,
      groupId,
      knowledgeId,
      ...gitValues,
      chunkStrategy: {
        ...sliceValues,
      },
      overwrite: updateValues?.overwrite,
      config: JSON.stringify(
        {
          chunkConfig: {
            chunkType: sliceValues?.chunkType,
            chunkStrategy: {
              ...sliceValues,
            },
          },
          operator: user.name,
        }
      )
    };

    try {

      setSubmiting(true);
      const res = await NewKnowledge.submitGitTask(params);
      setSubmiting(false);

      if (res?.debugInfo) {
        message.error(res.debugInfo);
        return;
      }
      onChange?.();
      onCancel();
    } catch (error) {
      setSubmiting(false);
    }
  };

  return <>
    <Flex onClick={onOpen} align='center'>GIT 文档</Flex>
    <Modal title="GIT 文档"
      width={800}
      styles={{
        body: {
          overflow: 'scroll'
        }
      }}
      open={open}
      onCancel={onCancel}
      onOk={onOk}
      okButtonProps={{
        loading: submiting
      }}
    >
      <GitForm
        formProps={{
          ...documentFormLayout,
        }}
        ref={gitFormRef}
      />
      <Divider />

      <Row style={{ marginBottom: 18 }}>
        <Col span={6} style={{ textAlign: 'right' }}>
          分段策略：
        </Col>
        <Col span={16}>
          <SliceForm
            ref={sliceFormRef}
            // value={chunkStrategy}
            // onChange={onChunkStrategyChange}
            size="small"
          />
        </Col>
      </Row>
      <Form {...documentFormLayout} form={updateForm}>
        <Form.Item initialValue={true} name={"overwrite"} label="文档更新策略" tooltip={<>如果文档之前已经被导入过（已有知识库中存在 URL 相同的文档），可以选择覆盖已有文档并重新导入分片，或作为新的文档导入</>}>
          <Radio.Group>
            <Radio value={true}>自动覆盖已有文档</Radio>
            <Radio value={false}>创建新的文档</Radio>
          </Radio.Group>
        </Form.Item>
        {/* <Form.Item
          name='agreement'
          valuePropName='checked'
          wrapperCol={{ offset: 6, span: 18 }}
          rules={[{
            validator: (rule, value, callback) => {
              if (!value) {
                callback("上传文档前，请先同意安全合规要求");
              } else {
                callback();
              }
            }
          }]}
        >
          <Checkbox>
            我已知晓并确认上传的文档将发送至 OpenAI
            的服务器，且文档符合网易集团的信息安全规范
          </Checkbox>
        </Form.Item> */}
      </Form>
    </Modal>
  </>
}
