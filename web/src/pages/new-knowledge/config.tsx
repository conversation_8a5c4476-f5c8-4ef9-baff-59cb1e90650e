import React from 'react';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  CopyrightOutlined,
  PictureOutlined,
  ProfileOutlined,
  SyncOutlined,
  TableOutlined,
} from '@ant-design/icons';
import { KnowledgeType } from '@/interface/knowledge';
import { CioIcon, ExcelIcon, IconFont, TextIcon, TxtIcon } from '@/components/icons';

interface KnowledgeTypeSchemaItem extends Record<string, any> {
  key: KnowledgeType;
  label: string;
  icon: React.ReactNode;
}

export const knowledgeTypeSchema: KnowledgeTypeSchemaItem[] = [
  {
    key: 'all',
    label: '全部类型',
    icon: '',
  },
  {
    key: 'text',
    label: '文本类型',
    icon: <TxtIcon />,
  },
  {
    key: 'table',
    label: '表格类型',
    icon: <ExcelIcon />,
  },
  {
    key: 'cio',
    label: 'CIO类型',
    // icon: <CopyrightOutlined />,
    icon: <CioIcon />,
  },
  // {
  //   key: 'image',
  //   label: '照片类型',
  //   icon: <PictureOutlined />,
  // },
];

/** 上传类型枚举 */
export const documentImportTypeMap = {
  local: '本地文件',
  custom: '自定义',
  gitlab: 'GitLab',
  popo: 'POPO 文档'
};

/** 切分枚举 */
export const documentChunkStrategyMap = {
  normal: '普通切分',
  recursive: '层级切分',
  custom: '自定义切分',
  // 结构化的层级切分
  structure: '层级切分',
};

export const textIconMap = {
  md: <IconFont type="icon-file-markdown" />,
  pdf: <IconFont type="icon-pdf" />,
  doc: <IconFont type="icon-doc" />,
};

export const statusMap = {
  waiting: {
    label: '上传中',
    color: 'blue',
    icon: <SyncOutlined spin />,
  },
  fail: {
    label: '上传失败',
    color: 'red',
    icon: <CloseCircleOutlined />,
  },
  success: {
    label: '上传成功',
    color: 'green',
    icon: <CheckCircleOutlined />,
  },
};

export const knowledgeIconMap = {
  text: <TxtIcon />,
  table: <ExcelIcon />,
  cio: <CioIcon />
};
