import React from 'react';
import {
  createBrowserRouter,
  createRoutesFromElements,
  Navigate,
  Route,
  RouterProvider,
} from 'react-router-dom';

import { PreviewRoot, Root } from '../components/root';
// import Root from '../components/root/index';
import { createAppMenuPage } from './app';
import ErrorPage from './error-page';
import { createGroupMenuPage } from './group';
import { HomePage } from './home';
import { ExploreChat } from './home/<USER>/explore-chat';
import { Explore } from './home/<USER>/explore-app';
import { WorkspaceInvitePage } from './invite';
import Market from './market';
import Knowledge from './knowledge';
import KnowledgeDetail from './knowledge/detail';
import NewKnowledgeDetail from './knowledge-detail';
import Document from './document';
import { WorkflowPreview } from './preview/workflow';
import { WorkflowPlayground } from './preview/workflow-playground';
import { CompletionPreview } from './preview/agent-complete';
import { AgentConversationPreview1 } from './preview/agent-preview';
import { createSystemMenuPage } from './system';
import { createWorkspaceMenuPage } from './workspace';
import { createGroupSettingMenuPage } from './group-setting';
import { useRequest } from 'ahooks';
import { AppApi } from '@/api/app';
// import { useGlobalState } from '@/hooks/useGlobalState';
import { getAppId } from '@/utils/state';
import ModelsPage from './app/models';
import EvaluateTaskCreate from './evaluate-task/create';
import CollectionItems from './evaluate/collection-items';
import { AppPageLayout } from '@/components/pageLayout';

export function RootRouter() {

  const router = createBrowserRouter(
    createRoutesFromElements(
      <>
        <Route path="/" element={<Root />} errorElement={<ErrorPage />}>
          <Route path="/models" element={<ModelsPage />} />
          <Route errorElement={<ErrorPage />}>
            <Route index element={<ExploreChat />} />
            <Route path="/explore" index element={<Explore />} />
            <Route path="/overview" index element={<HomePage />} />
            <Route path="/all" index element={<HomePage isAll />} />
            {createGroupMenuPage()}
            {createGroupSettingMenuPage()}
            {createAppMenuPage()}
            {createWorkspaceMenuPage()}
            {createSystemMenuPage()}
            <Route path="/logined" element={<Navigate to={localStorage.getItem('login_redirect') || '/overview'} />} />
            <Route path="/invite/workspace" element={<WorkspaceInvitePage />} />
          </Route>
        </Route>
        <Route path="/market" element={<Root />} errorElement={<ErrorPage />}>
          <Route index element={<Market />} />
        </Route>
        <Route path="/knowledge" element={<Root />} errorElement={<ErrorPage />}>
          <Route path="/knowledge/:knowledgeId" element={<KnowledgeDetail />} />
          <Route path="/knowledge/:knowledgeId/document/:documentId" element={<Document />} />
        </Route>
        <Route path="/new-knowledge" element={<Root />} errorElement={<ErrorPage />}>
          <Route path="/new-knowledge/:knowledgeId" element={<NewKnowledgeDetail />} />
          <Route path="/new-knowledge/:knowledgeId/recall" element={<NewKnowledgeDetail type="recall" />} />
        </Route>
        {/* <Route path="/group/evaluate" element={<Root />} errorElement={<ErrorPage />}>
          <Route path="/group/evaluate/collection/:collectionId" element={<CollectionItems />} />
        </Route> */}
        <Route path="/app/evaluate-task" element={<Root />} errorElement={<ErrorPage />}>
          <Route path="/app/evaluate-task/create" element={<EvaluateTaskCreate />}/>
        </Route>
        <Route path="/preview" element={<PreviewRoot />} errorElement={<ErrorPage />}>
          <Route path="/preview/workflow" element={<WorkflowPreview />} />
          <Route path="/preview/workflow-playground" element={<WorkflowPlayground />} />
          <Route path="/preview/agent-completion" element={<CompletionPreview />} />
          <Route path="/preview/agent-conversation" element={<AgentConversationPreview1 />} />
        </Route>
      </>,
    ),
  );
  return <RouterProvider router={router} />;
}
