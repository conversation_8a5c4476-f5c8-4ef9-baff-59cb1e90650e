import { Button, Dropdown, Flex, message, Popconfirm, Space, Table, Tooltip, Typography } from "antd";
import { useState } from "react";
import DetailModal from "./manual-import-modal";
import { EvaluateApi } from "@/api/evaluate";
import { useRequest } from "ahooks";
import { InfoCircleOutlined, LeftOutlined, PlusOutlined } from "@ant-design/icons";
import { useParams } from "react-router-dom";
import { usePathQuery } from "@/hooks/useQuery";
import LocalImportModal from "./local-import-modal";
import { useGlobalState } from "@/hooks/useGlobalState";
import EllipsisText from '@/components/common/EllipsisText';

const PAGE_SIZE = 20;

const CollectionItems = () => {

  const [pageInfo, setPageInfo] = useState({
    pageSize: PAGE_SIZE,
    pageNum: 1
  });

  const params = useParams();
  const { collectionId } = params || {};

  const { pushRetainQuery } = usePathQuery();

  const { globalState } = useGlobalState();
  const { app } = globalState;

  const { data, loading, run } = useRequest(async (val?) => {
    if (!collectionId) return;

    const res = await EvaluateApi.queryEvaluationItems({
      ...pageInfo,
      ...(val || {}),
      evaluationCollectionId: collectionId
    })
    return res;

  }, {
    refreshDeps: [collectionId]
  });

  const { data: detail } = useRequest(async () => {
    if (!collectionId) return;
    const res = await EvaluateApi.queryEvaluationDetail({ id: collectionId });
    return res;

  }, {
    refreshDeps: [collectionId]
  });

  const columns = Object.values(detail?.extInfo?.columnConfig || {})?.reduce((acc: any, cur: any) => {
    cur?.forEach(col => {
      if (col?.name) {
        acc.push({
          dataIndex: col?.name,
          title: <Space>
            {col?.name}
            {col?.description && <Tooltip title={col?.description}>
              <InfoCircleOutlined />
            </Tooltip>}
          </Space>,
        });
      };
    });

    return acc;

  }, []);

  const onDelete = async (rec) => {
    try {
      message.success('删除成功');
      const res = await EvaluateApi.deleteEvaluationDetailItem({ id: rec.id, evaluationCollectionId: collectionId });
      console.log('res', res);
      onEdit();

    } catch (error) {

    }
  };

  const onEdit = async () => {
    run();
  };

  const onRefresh = () => {
    setPageInfo({
      ...pageInfo,
      pageNum: 1
    });
    run({ pageNum: 1 });
  }

  return <div style={{ marginBottom: '40px' }}>
    <Flex justify="space-between">
      <Flex gap={10} align="center">
        <LeftOutlined onClick={() => {
          pushRetainQuery('/group/evaluate', ['workspaceId', 'groupId'])
        }}
        />
        <Typography.Text strong style={{ fontSize: '18px' }}>{detail?.name}</Typography.Text>
        <Typography.Text type="secondary">{detail?.description}</Typography.Text>
      </Flex>
      <Flex>
        <Dropdown menu={{
          items: [
            {
              key: 'local',
              label: <LocalImportModal
                collection={detail}
                columns={columns}
                onChange={onRefresh}
                columnConfig={detail?.extInfo?.columnConfig}
              />
            },
            {
              key: 'manual',
              label: <DetailModal
                trigger={<span>手动添加</span>}
                data={{ collectionId }}
                onChange={onRefresh}
                columnConfig={detail?.extInfo?.columnConfig}
              />
            }
          ]
        }}>
          <Button icon={<PlusOutlined />} type="primary">添加数据</Button>
        </Dropdown>
      </Flex>
    </Flex>
    <Table
      loading={loading}
      dataSource={data?.values?.map(item => {
        return {
          ...item,
          ...(item.itemContent || {})
        }
      })}
      columns={[
        { dataIndex: 'id', title: 'ID', },
        // @ts-ignore
        // ...(columns || []),
        ...(detail?.extInfo?.columnConfig?.inputColumns || [])?.map(col => ({
          textWrap: 'word-break',
          render: (value) => {
            return <EllipsisText style={{ maxWidth: '400px' }}>
              {value}
            </EllipsisText>
          },
          dataIndex: col?.name, title: <Space>
            {col?.name}
            {col?.description && <Tooltip title={col?.description}>
              <InfoCircleOutlined />
            </Tooltip>}
          </Space>
        })),
        ...(detail?.extInfo?.columnConfig?.referenceOutputColumns || [])?.map(col => ({ dataIndex: col?.name, title: col?.description })),
        ...(detail?.extInfo?.columnConfig?.defaultRuleColumns || [])?.map(col => ({ dataIndex: col?.name, title: col?.description })),
        { dataIndex: 'updateTime', title: '更新时间' },
        { dataIndex: 'updater', title: '更新人' },
        { dataIndex: 'createTime', title: '创建时间' },
        { dataIndex: 'creator', title: '创建人' },

        {
          dataIndex: '', title: '操作', render(record) {
            return <Flex>
              <DetailModal
                trigger={<Button type="link">编辑</Button>}
                modalProps={{
                  title: '编辑'
                }}
                value={record.itemContent}
                data={{ collectionId }}
                onChange={() => onEdit()}
                id={record.id}
                columnConfig={detail?.extInfo?.columnConfig}

              />
              <Popconfirm title="确认删除当前数据？" onConfirm={() => onDelete(record)}>
                <Button type="link" danger>删除</Button>
              </Popconfirm>
            </Flex>
          },
        },

      ]}
      pagination={{
        total: data?.total,
        showTotal: total => `共 ${total}`,
        current: data?.pageNum,
        hideOnSinglePage: true,
        pageSize: pageInfo.pageSize,
        onChange(pageNum, pageSize) {
          setPageInfo({
            ...pageInfo,
            pageNum,
            pageSize
          });
          run({
            pageNum,
            pageSize
          });
        },
      }}
    />

  </div>
}

export default CollectionItems;
