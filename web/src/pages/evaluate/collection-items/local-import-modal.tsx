import { Button, Flex, Form, message, Modal, Radio } from "antd";
import { useEffect, useRef, useState } from "react";
import FileUpload from "@/pages/app/workflow/react-node/file-uploader";
import { EvaluateApi } from "@/api/evaluate";
import { utils, writeFile } from "xlsx";
import { useGlobalState } from "@/hooks/useGlobalState";

const ruleOptions = [
  { label: '全等', value: '全等' },
  { label: '包含', value: '包含' },
  { label: '正则', value: '正则' },
]

const LocalImportModal = (props) => {
  const { trigger, onChange, modalProps = {}, columns, collection, columnConfig } = props;
  const { inputColumns, defaultRuleColumns, referenceOutputColumns } = columnConfig || {};

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();
  const templateRef = useRef(null);

  const { globalState } = useGlobalState();
  const { user } = globalState;

  function indexToExcelColumn(index) {
    // 处理无效输入
    if (index < 0 || !Number.isInteger(index)) return "无效输入";

    let result = '';
    while (index >= 0) {
      // 计算当前余数（0-25对应A-Z）
      const remainder = index % 26;
      // 将余数转换为字母（A的ASCII码是65）
      result = String.fromCharCode(65 + remainder) + result;
      // 计算下一轮索引
      index = Math.floor(index / 26) - 1;

      // 当索引小于0时结束循环
      if (index < 0) break;
    }
    return result;
  }

  const downloadExcel = () => {
    // 1. 定义表头
    const headers = []
    inputColumns?.forEach(col => headers.push(col?.name));
    referenceOutputColumns?.forEach(col => headers.push(col?.name));
    defaultRuleColumns?.forEach(col => headers.push(col?.name));

    // const headers = columns?.map(col => col.title);

    // 2. 创建示例行
    const createExampleRow = (inputColumns, referenceOutputColumns, defaultRuleColumns) => {
      const exampleRow = [];
      inputColumns?.forEach((_, index) => {
        exampleRow.push(`示例变量输入${index + 1}`);
      });
      referenceOutputColumns?.forEach(() => {
        exampleRow.push('大模型预期输出');
      });
      defaultRuleColumns?.forEach(() => {
        exampleRow.push('全等|包含|正则 (选其一)');
      });
      return exampleRow;
    };

    // 3. 定义列宽
    const colWidths = [];
    // 输入列宽度设置为40
    inputColumns?.forEach(() => {
      colWidths.push({ wch: 40 }); // wch 表示字符宽度
    });
    // 预期输出列宽度设置为40
    referenceOutputColumns?.forEach(() => {
      colWidths.push({ wch: 40 });
    });
    // 规则列宽度设置为15
    defaultRuleColumns?.forEach(() => {
      colWidths.push({ wch: 25 });
    });

    // 4. 创建工作表
    const ws = utils.aoa_to_sheet([
      headers,
      createExampleRow(inputColumns, referenceOutputColumns, defaultRuleColumns)
    ]);

    // 5. 设置列宽
    ws['!cols'] = colWidths;

    // 6. 设置示例行样式
    const range = utils.decode_range(ws['!ref']);
    for (let C = range.s.c; C <= range.e.c; C++) {
      const cell = utils.encode_cell({ r: 1, c: C });
      if (!ws[cell]) continue;
      ws[cell].s = {
        font: { color: { rgb: "808080" } },
        italic: true
      };
    }

    //   // 3. 设置表头样式（可选）
    // const headerStyle = {
    //   font: { bold: true, color: { rgb: "FFFFFF" } },
    //   fill: { fgColor: { rgb: "4472C4" } }, // 蓝色背景
    //   alignment: { horizontal: "center" }
    // };

    // // 应用样式到表头行
    // for(let col = 0; col < headers.length; col++) {
    //   const cellAddress = utils.encode_cell({r: 0, c: col});
    //   if(!ws[cellAddress]) ws[cellAddress] = {};
    //   ws[cellAddress].s = headerStyle;
    // }

    // 创建工作簿并添加工作表
    const wb = utils.book_new();
    utils.book_append_sheet(wb, ws, `Sheet1`)

    templateRef.current = wb;
  };

  const onDownload = () => {
    writeFile(templateRef.current, `评测集-${collection?.name}-${Date.now()}.xlsx`);
  }


  useEffect(() => {

    downloadExcel();

  }, [
    JSON.stringify(columnConfig),
    collection?.name]);

  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    form.resetFields();
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();

    try {
      setLoading(true);
      const res = await EvaluateApi.addEvaluationDetailItem({
        // ...values,
        evaluationCollectionId: collection?.id,
        nosKey: values?.fileList?.[0]?.key,
        type: 'IMPORT',
        version: '1.0.0',
        operator: user.name,
        importType: values?.importType

      });
      message.success('导入成功');
      setLoading(false);
      onChange?.();
      onCancel();
    } catch (error) {
      setLoading(false);
    }
  }

  return <>
    <div onClick={onOpen}>
      {trigger || <span >本地导入</span>}
    </div>
    <Modal title="本地导入"
      open={open}
      onCancel={onCancel}
      onOk={onOk}
      okButtonProps={{
        loading
      }}>
      <Form layout="vertical" form={form}
      >
        <Form.Item label="上传数据" name="fileList" rules={[{ required: true }]}>
          <FileUpload
            maxCount={1}
            // sizeLimit={100 * 1024}
            // accept="application/pdf,text/markdown,.doc,.docx,text/plain,.md"
            // description="上传 PDF, TXT, MD, DOC, DOCX 格式的本地文件，每个文件不超过100MB"
            // @ts-ignore
            description={<Flex justify="center" align="center" onClick={e => e.stopPropagation()}>
              请使用我们提供的模板：
              <a type="link" onClick={onDownload}>点击下载</a>
            </Flex>}
          />
        </Form.Item>
        <Form.Item label="导入方式" name="importType" rules={[{ required: true }]}
          initialValue={
            'APPEND'
          }>
          <Radio.Group options={[
            { label: '追加数据', value: 'APPEND' },
            { label: '全量覆盖', value: 'OVERRIDE' }
          ]}></Radio.Group>
        </Form.Item>
      </Form>

    </Modal>
  </>
}

export default LocalImportModal;
