import { <PERSON><PERSON>ate<PERSON><PERSON> } from "@/api/evaluate";
import { useGlobalState } from "@/hooks/useGlobalState";
import { Button, Form, Input, message, Modal, Radio, Select } from "antd";
import { useState } from "react";
import styled from "styled-components";

const StyledForm = styled(Form)`
  .ant-form-item-label {
    width: 100px !important;
    margin-right: 8px;
    word-break: break-all;
  }
`;

const ItemModal = (props) => {
  const { trigger, onChange, modalProps = {}, data = {}, value, id, columnConfig } = props;
  const { inputColumns, defaultRuleColumns, referenceOutputColumns } = columnConfig || {};
  const [open, setOpen] = useState(false);

  const [form] = Form.useForm();

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const onOpen = () => {
    if (typeof value !== 'undefined') {
      form.setFieldsValue(value);
    }
    setOpen(true);
  };

  const onOk = async () => {
    const values = await form.validateFields();

    if (id) {
      const params = {
        id,
        evaluationCollectionId: data?.collectionId,
        itemContent: values,
        updater: user.name
      };

      try {
        const res = await EvaluateApi.updateEvaluationDetailItem(params);
        onChange?.();
        onCancel?.();
      } catch (error) {
        // @ts-ignore
        message.error(error.message);
      }
      return;
    }

    const params = {
      type: 'NORMAL',
      importType: 'APPEND',
      evaluationCollectionId: data?.collectionId,
      content: [{
        itemContent: values
      }],
      version: '1.0.0',
      operator: user.name
    };

    try {
      const res = await EvaluateApi.addEvaluationDetailItem(params);
      onChange?.();
      onCancel?.();

    } catch (error) {
      // @ts-ignore
      message.error(error.message);
    }
  };

  const onCancel = () => {
    form.resetFields();
    setOpen(false);
  };

  return <>
    <div onClick={onOpen}>
      {trigger || <Button>添加数据</Button>}
    </div>
    <Modal
      title="添加数据"
      open={open}
      onOk={onOk}
      onCancel={onCancel}
      width={600}
      {...modalProps}
    >
      <StyledForm form={form} labelWrap>
        {/* 输入 */}
        {inputColumns?.map(col => {
          return <Form.Item
            tooltip={col?.description}
            key={col.name}
            label={col.name}
            name={col.name}>
            <Input.TextArea />
          </Form.Item>
        })}
        {/* 预期输出 */}
        {referenceOutputColumns?.map(col => {
          return <Form.Item
            // tooltip={col?.description}
            key={col.name}
            label={col.description}
            name={col.name}>
            <Input.TextArea />
          </Form.Item>
        })}
        {/* 默认规则 */}
        {defaultRuleColumns?.map((col, index) => {

          if (col?.name === 'defaultRule') {
            return <Form.Item
              // tooltip={col?.description}
              key={col.name}
              label={col.description}
              name={col.name}>
              {/* <Input.TextArea /> */}
              {/* <RuleEvaluate /> */}
              <Select options={[
                // { label: '全等', value: 'EQUALS' },
                // { label: '包含', value: 'CONTAINS' },
                // { label: '正则', value: 'REGEX' },
                // { label: '范围', value: 'RANGE' },
                { label: '全等', value: '全等' },
                { label: '包含', value: '包含' },
                { label: '正则', value: '正则' },
              ]}
                style={{ width: '100%' }}
              />
            </Form.Item>
          }

          if (col?.name === 'defaultRuleValue') {
            return <Form.Item
              // tooltip={col?.description}
              key={col.name}
              label={col.description}
              name={col.name}>
              {/* <Input.TextArea /> */}
              {/* <RuleEvaluate /> */}
              <Input />
            </Form.Item>
          }
        })}
        {/* <Form.Item label="输入" name="input">
          <Input.TextArea />
        </Form.Item>
        <Form.Item label="期望输出" name="referenceOutput">
          <Input.TextArea />
        </Form.Item> */}
      </StyledForm>
    </Modal>
  </>
};

export default ItemModal;
