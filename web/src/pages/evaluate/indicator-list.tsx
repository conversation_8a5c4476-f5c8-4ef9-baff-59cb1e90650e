import { Col, Empty, Flex, Input, Pagination, Row, Spin } from "antd";
import EvaluateCard from "./evaluate-card";
import { useEffect, useState } from "react";
import AddIndicatorDrawer from "./indicator-drawer";
import CustomSelect from "@/components/custom-select";
import { EvaluateApi } from "@/api/evaluate";
import { useQuery } from "@/hooks/useQuery";
import { useDebounceFn } from "ahooks";

const PAGE_SIZE = 12;

const IndicatorList = (props) => {
  const [searchValue, setSearchValue] = useState();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);

  const [pageInfo, setPageInfo] = useState({
    page: 1,
    pageSize: PAGE_SIZE
  });

  const { parsedQuery } = useQuery();
  const { workspaceId, groupId } = parsedQuery;

  const { run, flush, cancel } = useDebounceFn(async (val?) => {
    const params = {
      workspaceId,
      groupId,
      ...pageInfo,
      ...(val || {}),
      // 评测指标
      type: 'METRIC'
    };
    setLoading(true);
    const res = await EvaluateApi.getIndicators(params);
    setData(res);
    setLoading(false);
    return res;
  }, {
    wait: 500
  });

  useEffect(() => {
    run();
    flush();

    return () => {
      cancel();
    }
  }, []);

  const onSearch = (val) => {

    const newSearchValue = {
      ...(searchValue || {}),
      ...(val || {}),
      ...pageInfo,
      page: 1
    };

    setPageInfo({
      ...pageInfo,
      page: 1
    });


    setSearchValue({
      ...(searchValue || {}),
      ...(val || {})
    });

    run(newSearchValue);
  };

  const onPageChange = (page, pageSize?) => {
    const newPageInfo = {
      ...pageInfo,
      page
    };

    setPageInfo(newPageInfo);
    run(newPageInfo);
  };

  const onIndicatorChange = (curPage?) => {
    if (curPage) {
      onPageChange(curPage)
      return;
    }
    run();
  }

  return <>
    <Flex justify="space-between" style={{ marginBottom: 10 }}>
      <Flex>
        <CustomSelect
          label="场景"
          popupMatchSelectWidth={false}
          defaultValue="全部"
          options={[
            { label: '全部', value: '全部' },
            { label: '工作流', value: 'workflow' },
            { label: '对话流', value: 'agentWorkflow' },
            { label: '聊天型', value: 'agent' },
            { label: '虚拟人', value: 'virtual-human' }
          ]}
          onChange={val => {
            if (!val || val === '全部') {
              onSearch({ appTypes: undefined });
              return;
            }
            onSearch({ appTypes: val ? [val] : [] })
          }}
        />
        <CustomSelect
          label="可见范围"
          popupMatchSelectWidth={false}
          defaultValue=""
          options={[
            { label: '全部', value: '' },
            { value: 'group', label: '组内访问' },
            { value: 'scoped', label: '租户内访问' },
            { value: 'public', label: '公开(所有租户可见)' },
          ]}
          onChange={val => onSearch({ scope: val })}
        />
        <Input
          placeholder="请输入名称搜索..."
          onChange={ev => onSearch({
            name: ev.target.value
          })}
        />
      </Flex>
      <AddIndicatorDrawer onChange={() => onIndicatorChange(1)} />
    </Flex>
    <Spin spinning={loading}>
      <Flex flex={1}>
        <Row gutter={[16, 16]} style={{ width: '100%' }}>
          {data?.values?.map(item => {
            return <Col key={item.id} xs={12} sm={24} md={8} lg={6} xl={6} xxl={4}>
              <AddIndicatorDrawer
                value={item}
                trigger={<EvaluateCard data={item} />}
                drawerProps={{
                  title: '编辑指标'
                }}
                onChange={() => onIndicatorChange()}
              />
            </Col>
          })}

        </Row>
      </Flex>
      {!data?.values?.length && <Flex justify="center" align="center"
        style={{ minHeight: '400px' }}>
        <Empty />
      </Flex>}
      <Flex justify="end">
        <Pagination
          style={{ float: 'right', marginTop: '8px' }}
          total={data?.total}
          showTotal={(total) => `共 ${total} 条`}
          onChange={onPageChange}
          current={pageInfo.page}
          pageSize={PAGE_SIZE}
        />
      </Flex>
    </Spin>
  </>
}

export default IndicatorList;
