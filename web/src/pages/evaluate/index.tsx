import { Flex, Tabs } from "antd";
import { useState } from "react";
import { useQuery } from "@/hooks/useQuery";
import IndicatorList from "./indicator-list";
import CollectionList from "./collection/collection-list";

const Evaluate = () => {
  const [type, setType] = useState('COLLECTION');

  const { parsedQuery } = useQuery();
  const { workspaceId, groupId } = parsedQuery;

  const onTypeChange = val => {
    setType(val);
  };

  return <Flex vertical style={{ height: '100%' }}>
    <Tabs
      items={[
        { label: '评测集', key: 'COLLECTION', children: <CollectionList /> },
        { label: '评测指标', key: 'METRIC', children: <IndicatorList /> },
      ]}
      onChange={onTypeChange}
      activeKey={type}
    // tabPosition="left"
    >
    </Tabs>
  </Flex>
};

export default Evaluate;
