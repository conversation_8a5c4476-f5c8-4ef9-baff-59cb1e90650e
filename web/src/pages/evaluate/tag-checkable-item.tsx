import { Space, Tag } from 'antd';
import { useState } from 'react';

const { CheckableTag } = Tag;


const TagCheckableItem = (props: any) => {
  const { onChange, options } = props;
  const tagsData = [
    { label: '全部', name: '全部' },
    { label: '工作流', name: 'workflow' },
    { label: '对话流', name: 'agentWorkflow' },
    { label: '聊天型', name: 'agent' },
    { label: '虚拟人', name: 'virtual-human' }
  ];

  const [selectedTags, setSelectedTags] = useState<string[]>(['全部']);

  const handleChange = (tag: string, checked: boolean) => {
    let nextSelectedTags = checked
      ? [...selectedTags, tag]
      : selectedTags.filter((t) => t !== tag);
    if (tag === '全部') {
      nextSelectedTags = ['全部'];
    } else {
      nextSelectedTags = nextSelectedTags.filter((t) => t !== '全部');
    }
    console.log('You are interested in: ', nextSelectedTags);
    if (onChange) {
      onChange(nextSelectedTags);
    }
    setSelectedTags(nextSelectedTags);
  };

  return (
    <div style={{ marginBottom: '10px' }}>
      <span style={{ marginRight: 8, fontSize: 14 }}>场景:</span>
      <Space size={[0, 8]} wrap>
        {options?.map((tag) => (
          <CheckableTag
            key={tag.name}
            checked={selectedTags.includes(tag.name)}
            onChange={(checked) => handleChange(tag.name, checked)}
          >
            {tag.label}
          </CheckableTag>
        ))}
      </Space>
    </div>
  );
};

export default TagCheckableItem;
