import { useGlobalState } from "@/hooks/useGlobalState";
import { AppTypeCardColorMap, AppTypeCnMap, AppTypeTagColorMap, IAppType } from "@/interface";
import { scopeColorMap, scopTypeCnMap } from "@/interface/evaluate";
import { Badge, Card, Flex, Tag } from "antd";
import styled from "styled-components";

const Desc = styled.div`
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin: 0 0 10px 0;
  min-height: 36px;
  font-size: 12px;
`;

const Info = styled.div`
  margin: 5px 0;
  font-size: 12px;
`;

const EvaluateCard = (props) => {
  const { data } = props;
  const { name, description, creator, scope, appTypes } = data || {};

  const { globalState } = useGlobalState();
  const { user } = globalState;

  return <div style={{ width: '100%' }}>
    <Badge.Ribbon text={scopTypeCnMap[scope]} color={scopeColorMap[scope]}>
      <Card hoverable>
        <Card.Meta title={<div>
          <Flex>{name}</Flex>
          {appTypes && <Flex style={{ marginTop: 10 }}>
            {appTypes.map(appType => <Tag key={appType} color={AppTypeTagColorMap[appType]}>{AppTypeCnMap[appType]}</Tag>)}
          </Flex>}
        </div>}
          description={
            <Flex vertical>
              <Desc>{description}</Desc>
              <Info>创建人：{creator}</Info>
            </Flex>}
        ></Card.Meta>

      </Card>
    </Badge.Ribbon>
  </div>
}

export default EvaluateCard;
