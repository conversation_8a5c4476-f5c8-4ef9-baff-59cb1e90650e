import { Evaluate<PERSON><PERSON> } from "@/api/evaluate";
import { usePathQuery, useQuery } from "@/hooks/useQuery";
import { useDebounceFn } from "ahooks";
import { Button, Flex, Input, Space, Table, Tag } from "antd";
import { useEffect, useState } from "react";
import CollectionDrawer from "./collection-drawer";
import { ClockCircleOutlined, UserOutlined } from "@ant-design/icons";
import { AppTypeCnMap, AppTypeColorMap, AppTypeTagColorMap } from "@/interface";
import { scopeColorMap, scopTypeCnMap } from "@/interface/evaluate";

const PAGE_SIZE = 10;

const CollectionList = (props) => {
  const { } = props;

  const [searchValue, setSearchValue] = useState();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);

  const [pageInfo, setPageInfo] = useState({
    page: 1,
    pageSize: PAGE_SIZE
  });

  const { parsedQuery } = useQuery();
  const { workspaceId, groupId } = parsedQuery;

  const { pushAddQuery } = usePathQuery();

  const { run, flush, cancel } = useDebounceFn(async (val?) => {
    const params = {
      workspaceId,
      groupId,
      ...pageInfo,
      ...(val || {}),
      // 评测指标
      // type: 'COLLECTION'
      type: 'INNER_COLLECTION'
    };
    setLoading(true);
    const res = await EvaluateApi.getIndicators(params);
    setData(res);
    setLoading(false);
    return res;
  }, {
    wait: 1000
  });

  useEffect(() => {
    run();
    flush();
    return () => {
      cancel();
    }
  }, []);

  const onSearch = (val) => {

    const newSearchValue = {
      ...(searchValue || {}),
      ...(val || {}),
      ...pageInfo,
      page: 1
    };

    setPageInfo({
      ...pageInfo,
      page: 1
    });

    setSearchValue({
      ...(searchValue || {}),
      ...(val || {})
    });

    run(newSearchValue);
  };

  const onPageChange = (page, pageSize?) => {
    const newPageInfo = {
      ...pageInfo,
      page
    };
    setPageInfo(newPageInfo);
    run(newPageInfo);
    flush();
  };

  const onCollectionAdd = () => {
    onPageChange(1)
  };

  return <>
    <Flex justify="space-between" style={{ marginBottom: 10 }}>
      <Flex gap={8} align="center">
        <Flex style={{ flexShrink: 0 }}>名称：</Flex>
        <Input
          placeholder="请输入名称搜索..."
          onChange={ev => onSearch({
            name: ev.target.value
          })}
        />
      </Flex>
      <Flex>
        <CollectionDrawer onChange={onCollectionAdd} />
      </Flex>
    </Flex>
    <Table
      loading={loading}
      dataSource={data?.values as any[]}
      columns={[
        { title: 'id', dataIndex: 'id', },
        { title: '名称', dataIndex: 'name', },
        {
          title: '数据项', dataIndex: 'evaluationCollectionItemCount', render(value, record, index) {
            return <Button type="link"
              onClick={() => {
                pushAddQuery(`/group/evaluate/collection/${record.id}`, {})
              }}
            >{record?.extInfo?.evaluationCollectionItemCount || 0}</Button>
          },
        },
        {
          title: '使用范围', dataIndex: 'scope', render(value, record, index) {
            if (!value) return '-'
            return <Tag color={scopeColorMap[value]}>{scopTypeCnMap[value]}</Tag>
          },
        },
        {
          title: '使用类型', dataIndex: 'appTypes', render(value, record, index) {
            return <Space wrap={true}>
              {value?.map(val => <Tag color={AppTypeTagColorMap[val]}>{AppTypeCnMap[val]}</Tag>)}
            </Space>
          },
        },

        // { title: '分组', dataIndex: '', },
        // { title: '类型', dataIndex: '', },
        {
          title: '更新', dataIndex: 'updator', render(value, record, index) {
            if (!value || !record?.updatetime) return '-'
            return <Flex vertical>
              <Flex gap={6}>
                <UserOutlined /> {value}
              </Flex>
              <Flex gap={6}>
                <ClockCircleOutlined /> {record?.updatetime}
              </Flex>
            </Flex>
          },
        },
        {
          title: '创建', dataIndex: 'creator',
          render(value, record, index) {
            if (!value || !record?.createTime) return '-'

            return <Flex vertical >
              <Flex gap={6}>
                <UserOutlined /> {value}
              </Flex>
              <Flex gap={6}>
                <ClockCircleOutlined /> {record?.createTime}
              </Flex>
            </Flex>
          },

        },
        // { title: '创建时间', dataIndex: 'createTime', },
        {
          title: '操作', dataIndex: 'operate', render(value, record, index) {
            const { extInfo, ...restValue } = record
            return <Flex>
              <Button type="link"
                onClick={() => {
                  pushAddQuery(`/group/evaluate/collection/${record.id}`, {})
                }}
              >修改</Button>
              <CollectionDrawer
                isPreview
                trigger={<Button type="link">详情</Button>}
                defaultValue={{
                  ...restValue,
                  ...(extInfo?.columnConfig || {})
                }} />
              <Button type="link"
                onClick={() => {
                  pushAddQuery(`/group/evaluate/collection/${record.id}`, {})
                }}
              >查看评测集数据</Button>
            </Flex>
          },
        },
      ]}
      pagination={{
        total: data?.total,
        current: pageInfo.page,
        onChange: onPageChange,
        pageSize: PAGE_SIZE,
        showTotal: (total) => `共 ${total} 条`
      }}
    />
  </>
};

export default CollectionList;
