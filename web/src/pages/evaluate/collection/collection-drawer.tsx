import { But<PERSON>, Checkbox, Drawer, Flex, Form, Input, message, Select, Space, Typography } from "antd";
import { useState } from "react";
import { PlusOutlined } from "@ant-design/icons";
import InputItem from "@/pages/evaluate/collection/input-item";
import styled from "styled-components";
import { AppTypeCnMap, IAppType } from "@/interface";
import { EvaluateApi } from "@/api/evaluate";
import { useQuery } from "@/hooks/useQuery";
import { useGlobalState } from "@/hooks/useGlobalState";

const defaultRule = [
  {
    "name": "defaultRule",
    "description": "默认规则(全等|包含|正则)"
  },
  {
    "name": "defaultRuleValue",
    "description": "默认规则值"
  }
]

const StyledForm = styled(Form)`
  .ant-form-item-label {
    width: 80px;
  }
`;

const CollectionDrawer = (props) => {
  const { onChange, trigger, defaultValue = {}, isPreview } = props;
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const [openDefaultRule, setOpenDefaultRule] = useState(false);

  const { globalState } = useGlobalState();
  const { user } = globalState;
  const { parsedQuery } = useQuery();
  const { workspaceId, groupId } = parsedQuery;

  const befornOpen = () => {
    setOpenDefaultRule(defaultValue?.defaultRuleColumns?.length > 0);
    form.resetFields();
    form.setFieldsValue({
      inputColumns: [{
        name: 'input1',
        description: '变量输入1'
      }],
      referenceOutputColumns: [{
        name: 'referenceOutput',
        description: '预期输出'
      }],
      // defaultRuleColumns: defaultRule,
      ...(defaultValue || {}),
    });
  };

  const onOpen = () => {
    befornOpen();
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();
    const { inputColumns, referenceOutputColumns, defaultRuleColumns, ...rest } = values;
    const params = {
      workspaceId, groupId,
      operator: user.name,
      status: 'ONLINE',
      type: 'INNER_COLLECTION',
      ...rest,
      evaluationCollectionConfig: {
        inputColumns, referenceOutputColumns, defaultRuleColumns
      }
    };
    try {
      const res = await EvaluateApi.createCollection(params);
      message.success('创建成功');
      onChange?.(res);
      setOpen(false);
    } catch (error) {
      // @ts-ignore
      message.error(error?.message);
    }
  };

  const onOpenDefaultRuleChange = (checked) => {
    if (checked) {
      form.setFieldValue('defaultRuleColumns', defaultRule);
    } else {
      form.setFieldValue('defaultRuleColumns', null);
    }
    setOpenDefaultRule(checked);
  };

  return <>
    <span onClick={onOpen}>
      {trigger || <Button type="primary" icon={<PlusOutlined />} >新建评测集</Button>}
    </span>

    <Drawer
      title="新建评测集"
      open={open}
      width={"50%"}
      maskClosable={false}
      onClose={onClose}
      footer={isPreview ? null :
        <Flex justify="end">
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" onClick={onOk}>提交</Button>
          </Space>
        </Flex>
      }>
      <StyledForm
        disabled={isPreview}
        form={form}
        style={{ width: '80%', margin: '0 auto', maxWidth: '800px' }}
      // layout="vertical"
      // size="small"
      >
        <h3>基本信息</h3>
        <Form.Item label="名称" name="name" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item label="描述" name="description" rules={[{ required: true }]}>
          <Input.TextArea />
        </Form.Item>
        <Form.Item label="应用场景" name="appTypes" rules={[{ required: true }]}>
          <Select
            options={Object.keys(AppTypeCnMap)?.filter(appType => appType !== IAppType.Evaluator).map(appType => {
              return {
                value: appType,
                label: AppTypeCnMap[appType]
              }
            })}
            mode="multiple"
          />
        </Form.Item>
        <Form.Item
          name="scope"
          label="可见范围"
          rules={[{ required: true }]}
          initialValue="group"
        // help={
        //   scope === 'public' && '设置成公开后【无法二次编辑】'
        // }
        >
          <Select
            options={[
              { value: 'group', label: '组内访问' },
              { value: 'scoped', label: '租户内访问' },
              { value: 'public', label: '公开(所有租户可见)' },
            ]}
          />
        </Form.Item>
        <h3>输入配置</h3>
        <Form.List name="inputColumns">
          {(fields, { add, remove }) => {
            return (
              <Flex vertical gap={16}>
                {fields.map((field, index) => (
                  <InputItem
                    disabled={isPreview}
                    namePath={[index]}
                    title={`输入 ${index + 1}`}
                    onDelete={() => remove(field.name)} />
                ))}

                <Button type="dashed" onClick={() => add()} block>
                  + 添加输入
                </Button>
              </Flex>
            )
          }}
        </Form.List>
        <h3>输出配置</h3>
        <Form.List name="referenceOutputColumns">
          {(fields, { add, remove }) => {
            return (
              <Flex vertical gap={16}>
                {fields.map((field, index) => (
                  <InputItem
                    namePath={[index]}
                    title="预期输出"
                    disabled
                  />
                ))}
              </Flex>
            )
          }}
        </Form.List>
        <Flex align="center" gap={10}>
          <h3>行内规则</h3>
          <Typography.Text type="secondary" style={{ fontSize: '12px', marginBottom: 0 }}>
            行内规则开启后，可以为每行指定不同的评测规则（未开启或不指定的行将使用评测任务选择的统一规则）
          </Typography.Text>
        </Flex>
        <Form.Item>
          <Checkbox
            defaultChecked={openDefaultRule}
            onChange={ev => {
              onOpenDefaultRuleChange(ev.target.checked)
            }}>开启行内规则</Checkbox>
        </Form.Item>
        <Form.List name="defaultRuleColumns">
          {(fields, { add, remove }) => {
            return (
              <Flex vertical gap={16}>
                {fields.map((field, index) => (
                  <InputItem
                    // description=""
                    // disabled={isPreview}
                    disabled
                    namePath={[index]}
                    title={defaultRule[index]?.name}
                    description={defaultRule[index]?.description}
                  />
                ))}
              </Flex>
            )
          }}
        </Form.List>
        {/* <InputItem namePath={['defaultRuleColumns']}
          title="默认规则"
          description="若默认规则有值，则评测任务执行时直接调用该值进行评测。" /> */}
      </StyledForm>
    </Drawer>
  </>
};

export default CollectionDrawer;
