import { CloseOutlined } from "@ant-design/icons";
import { Card, Flex, Form, Input, Typography } from "antd";

const InputItem = (props) => {
  const { onDelete, title, description, namePath, disabled = false, required = true } = props;

  return <Card
    title={
      <Flex gap={8} align="end">
        <Flex>{title}</Flex>
        <Flex style={{ fontWeight: 'normal', fontSize: '12px', }}
        >
          <Typography.Text type="secondary" style={{ fontSize: '12px', marginBottom: 0 }}>
            {description}
          </Typography.Text>
        </Flex>
      </Flex>
    }
    size="small"
    extra={disabled ? null :
      <CloseOutlined
        onClick={() => {
          onDelete?.();
        }}
      />
    }>
    <Form.Item label="名称" name={[...namePath, "name"]} rules={[{ required: required }]}>
      <Input disabled={disabled} />
    </Form.Item>
    <Form.Item label="描述" name={[...namePath, "description"]} >
      <Input disabled={disabled} />
    </Form.Item>
  </Card>
}

export default InputItem;
