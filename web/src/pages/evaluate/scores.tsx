import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Collapse, Flex, Input } from "antd";
import { useEffect } from "react";
import styled from "styled-components";

const SocreItem = styled(Flex)`
  .score-item-label {
    width: 80px;
    flex-shrink: 0;
    color: #999;
    justify-content: end;
  }
`;

const Scores = (props) => {
  const { value, onChange } = props;

  const onItemChange = (index, val) => {

    const newValue = [...(value || [])];

    newValue.splice(index, 1, {
      ...(value?.[index] || {}),
      ...(val || {})
    });

    onChange?.(newValue);
  };

  return <Collapse
    size="small"
    defaultActiveKey={[5, 4, 3, 2, 1]}
    items={[5, 4, 3, 2, 1].map((score, index) => {
      const { singleStandard, answer } = value?.[index] || {};

      return {
        label: `分值：${score} 分`,
        key: score,
        children: <Flex
          key={score}
          vertical
          gap={8}
          style={{
            // border: '1px solid #d9d9d9',
            borderRadius: '10px',
            // padding: '14px 10px'
          }}>
          {/* <Flex>
            <Flex className="score-item-label">分值：</Flex>
            {score}分
          </Flex> */}
          <SocreItem>
            <Flex className="score-item-label">分数定义：</Flex>
            <Input
              value={singleStandard}
              onChange={ev => onItemChange(index, {
                singleStandard: ev.target.value,
                score
              })} />
          </SocreItem>
          <SocreItem>
            <Flex className="score-item-label">参考答案：</Flex>
            <AnswersItems
              value={answer}
              score={score}
              onChange={val => onItemChange(index, {
                answer: val,
                score
              })} />
          </SocreItem>
        </Flex>
      }
    })}
  />
}

const AnswersItems = (props) => {
  const { value, onChange, score } = props;

  // useEffect(() => {
  //   if (value?.length > 0) return;
    
  //   onChange?.([{
  //     uuid: Date.now(),
  //   }]);

  // }, [value]);

  const onAddAnswer = () => {
    const newValue = [...(value || [])];
    newValue.push({
      uuid: Date.now()
    });
    onChange?.(newValue);
  };

  const onDelete = (index) => {
    const newValue = [...(value || [])];
    newValue.splice(index, 1);
    onChange?.(newValue);
  };

  const onAnswerChange = (index, val) => {
    const newValue = [...(value || [])];
    newValue.splice(index, 1, {
      ...(value?.[index] || {}),
      value: val
    });

    onChange?.(newValue);
  };

  return <div style={{ width: '100%' }}>
    <Flex vertical gap={10}>
      {value?.map((item, index) => {
        return <Flex key={item.uuid} justify="space-between">
          <Flex flex={1} style={{ marginRight: 14 }}>
            <Input.TextArea style={{ width: '100%' }}
              placeholder={`参考答案 ${index + 1}`}
              value={item.value}
              onChange={ev => {
                onAnswerChange(index, ev.target.value)
              }}
            />
          </Flex>
          <DeleteOutlined onClick={() => onDelete(index)} />
        </Flex>
      })}
    </Flex>
    <Button icon={<PlusOutlined />}
      size="small"
      type="link"
      style={{ marginTop: 12 }}
      onClick={onAddAnswer}>添加参考答案</Button>
  </div>
};

export default Scores;
