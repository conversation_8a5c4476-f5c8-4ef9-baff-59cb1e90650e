import { Button, Drawer, Flex, Form, Input, message, Select, Space } from "antd";
import { useState } from "react";
import Scores from "./scores";
import { PlusOutlined } from "@ant-design/icons";
import { EvaluateApi } from "@/api/evaluate";
import { useQuery } from "@/hooks/useQuery";
import { AppTypeCnMap } from "@/interface";
import { useGlobalState } from "@/hooks/useGlobalState";
import styled from "styled-components";

const StyledForm = styled(Form)`
  .ant-form-item-label {
    width: 110px;
  }
`;

const defaultAnswers = [
  {
    "score": 5,
    // "singleStandard": "对话内容高度准确，回答完全符合问题的意图，信息无误，逻辑清晰，能够精准地满足对话者的需求，给对话者带来极佳的交流体验。",
    "answer": [
      ''
      // "用户：你好呀，你叫什么名字？\n虚拟人：我的名字叫小甲，很高兴认识你。（点评：该虚拟人回答准确无误，完全符合问题的意图，语言清晰，逻辑连贯，展现出高度准确的对话能力。）"
    ]
  },
  {
    "score": 4,
    // "singleStandard": "对话内容较为准确，回答基本符合问题的意图，信息基本无误，逻辑较为清晰，能够较好地满足对话者的需求，但可能存在少量细节上的小瑕疵。",
    "answer": [
      ''
      // "用户：你好呀，你叫什么名字？\n虚拟人：大家都叫我小甲，你也可以这么叫我。（点评：该虚拟人回答基本准确，虽稍显简略，但整体符合问题意图，展现出较好的对话准确性。）"
    ]
  },
  {
    "score": 3,
    // "singleStandard": "对话内容有一定准确性，回答能够体现出问题的核心意图，但存在一些明显的错误或信息不完整的情况，需要对话者稍作努力才能理解，连贯性一般。",
    "answer": [
      ''
      // "用户：你好呀，你叫什么名字？\n虚拟人：我叫小甲，不过我有时候也叫小乙。（点评：该虚拟人回答中出现了两种名字，存在一定的矛盾，信息不完整，但整体上仍能体现出问题的核心意图。）"
    ]
  },
  {
    "score": 2,
    // "singleStandard": "对话内容准确性较差，回答与问题的意图存在较大偏差，信息错误较多，逻辑不清晰，难以满足对话者的需求，给对话者带来一定的困惑。",
    "answer": [
      ''
      // "用户：你好呀，你叫什么名字？\n虚拟人：我叫小甲，但你也可以叫我小丙。（点评：该虚拟人回答与问题意图偏差较大，存在明显错误，逻辑不清晰，符合准确性较差的表现。）"
    ]
  },
  {
    "score": 1,
    // "singleStandard": "对话内容极不准确，回答完全偏离问题的意图，信息严重错误，逻辑混乱，几乎无法满足对话者的需求，严重干扰对话的进行。",
    "answer": [
      ''
      // "用户：你好呀，你叫什么名字？\n虚拟人：我今天叫小甲，明天可能就不叫这个名字了。（点评：该虚拟人回答完全偏离问题意图，信息严重错误，逻辑混乱，符合极不准确的表现。）"
    ]
  }
];

const IndicatorDrawer = (props) => {
  const { onChange, value, trigger, drawerProps } = props;

  const [open, setOpen] = useState<boolean>();
  const [saving, setSaving] = useState(false);
  // const [value, setValue] = useState();

  const [form] = Form.useForm();

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const { parsedQuery } = useQuery();
  const { workspaceId, groupId } = parsedQuery;

  const isEditMode = !!value?.indicatorId;


  const beforeOpen = async () => {

    let currentValue = { ...(value || {}) };

    // 已经有indicatorId了的话，需要通过接口去查详情
    if (value?.indicatorId) {
      const res = await EvaluateApi.getIndicatorDetail({ indicatorId: value.indicatorId });

      currentValue = {
        ...(res || {}),
        // apptypes数据需要单独传入
        appTypes: value?.appTypes
      };
    }

    // setValue(currentValue);

    const { answers, ...rest } = currentValue || {};

    const newAnswers = (answers || defaultAnswers)?.map(item => {
      return {
        score: item.score,
        singleStandard: item.singleStandard,
        answer: (item.answer)?.map(val => {
          return {
            uuid: Date.now(),
            value: val
          }
        })
      }
    });

    form.setFieldsValue({
      answers: newAnswers,
      ...rest,
    });
  }

  const onOpen = () => {
    beforeOpen();
    setOpen(true);
  };

  const onClose = () => {
    form.resetFields();
    setOpen(false);
  };

  const onAdd = async () => {
    const { answers, ...values } = await form.validateFields();
    setSaving(true);

    const params = {
      ...values,
      workspaceId,
      groupId,
      answers: answers?.map(item => {
        return {
          score: item.score,
          singleStandard: item.singleStandard,
          answer: item.answer?.map(it => it.value)
        }
      }),
      type: 'METRIC',
      operator: user.name,
      status: 'ONLINE',
    };

    try {
      let service = EvaluateApi.addIndicator;
      if (value?.indicatorId) {
        service = EvaluateApi.updateIndicator;
        params.indicatorId = value.indicatorId;
        params.id = value.id;
      }
      const res = await service(params);

      setSaving(false);
      onChange?.();
      onClose();
    } catch (error) {
      // @ts-ignore
      message.error(error?.message);
      setSaving(false);
    }
  };

  return <div>
    <Flex onClick={onOpen}>
      {trigger || <Button icon={<PlusOutlined />} type="primary"
      >{isEditMode ? '更新' : '创建'}指标</Button>}
    </Flex>

    <Drawer title="创建指标"
      width={'100vw'}
      open={open}
      onClose={onClose}
      footer={<Flex justify="end">
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary"
            loading={saving}
            onClick={onAdd}>{value?.indicatorId ? `更新` : `创建`}</Button>
        </Space>
      </Flex>}
      {...drawerProps}
    >
      <StyledForm form={form}>
        <Form.Item label="名称" name="name" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        {/* <Form.Item label="CODE" name="" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item label="图标地址" name="" >
          <Input />
        </Form.Item> */}
        <Form.Item label="应用场景" name="appTypes" rules={[{ required: true }]}>
          <Select
            options={Object.keys(AppTypeCnMap)?.map(appType => {
              return {
                value: appType,
                label: AppTypeCnMap[appType]
              }
            })}
            mode="multiple"
          />
        </Form.Item>
        <Form.Item label="描述" name="description" rules={[{ required: true }]}>
          <Input.TextArea />
        </Form.Item>
        <Form.Item
          name="scope"
          label="可见范围"
          rules={[{ required: true }]}
          initialValue="group"
        // help={
        //   scope === 'public' && '设置成公开后【无法二次编辑】'
        // }
        >
          <Select
            options={[
              { value: 'group', label: '组内访问' },
              { value: 'scoped', label: '租户内访问' },
              { value: 'public', label: '公开(所有租户可见)' },
            ]}
          />
        </Form.Item>
        <Form.Item label="指标定义" name="definition" rules={[{ required: true }]}>
          <Input.TextArea />
        </Form.Item>
        <Form.Item label="指标配置" name="answers" rules={[{
          required: true, validator(rule, value, callback) {
            // 先校验分值定义
            for (let i = 0; i < value?.length; i++) {
              const current = value?.[i];
              if (!current.singleStandard) {
                callback('分数定义不能为空');
                return;
              }
              if (!current.answer?.length) {
                callback('每个分数至少要有一个参考答案');
                return;
              } else {
                const answerValues = current.answer?.map(it => it.value)?.filter(it => it);
                if (!answerValues?.length) {
                  callback('每个分数至少要有一个参考答案');
                  return;
                }
              }
            }
            callback();
          },
        }]} >
          <Scores />
        </Form.Item>
      </StyledForm>
    </Drawer>
  </div>
};

export default IndicatorDrawer;
