import {
  AppstoreAddOutlined,
  SettingOutlined,
  DashboardOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';

import MemberPage from '@/components/member';
import { MemberOfResource } from '@/interface/member';

import { createMenuPage, MenuItemConfig } from '../../components/pageLayout';
import { GroupAppModelProvider } from '@/pages/workspace/components/model-provider';
import { GroupSettingPage } from './components/setting';
import DataDashboard from '@/components/dashboard';

export const menuConfig = [
  {
    title: '应用大盘',
    path: 'overview',
    Icon: DashboardOutlined,
    element: <DataDashboard isGroup={true} />,
  },
  {
    title: '成员管理',
    path: 'member',
    Icon: UsergroupAddOutlined,
    element: <MemberPage resType={MemberOfResource.Group} />,
  },
  {
    title: '模型绑定设置',
    path: 'model',
    Icon: AppstoreAddOutlined,
    element: <GroupAppModelProvider />,
  },
  {
    title: '基础设置',
    path: 'setting',
    Icon: SettingOutlined,
    element: <GroupSettingPage />,
  },
] as MenuItemConfig[];

export function createGroupSettingMenuPage() {
  // 在此函数中不能使用 useGlobalState 等 hooks， 否则会导致路由切换异常 (这是一个辅助函数，非 React Component)

  return createMenuPage({
    path: 'group-setting',
    menuConfig,
    retainQueries: ['workspaceId', 'groupId'], // 跳转菜单时保留的查询参数项
  });
}
