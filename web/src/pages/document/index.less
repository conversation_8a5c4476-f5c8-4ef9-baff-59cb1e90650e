.document {
  .document_segment {
    position: relative;
    line-height: 24px;
    padding: 8px;
    border: 1px solid white;
    word-break: break-all;
    &:nth-child(odd) {
      span {
        background-color: rgba(255,30,86,.15);
      }
      &:hover {
        background-color: rgba(255,30,86,.15);
        border: 1px solid blue;
        span {
          background-color: initial;
        }
      }
    }
    &:nth-child(even) {
      span {
        background-color: rgba(110,117,237,.18);
      }
      &:hover {
        background-color: rgba(110,117,237,.18);
        border: 1px solid blue;
        span {
          background-color: initial;
        }
      }
    }
    &.failed {
      color: #999;
    }
  }
  .document_segment_fail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.45);
    justify-content: center;
    display: flex;
    align-items: center;
    font-size: 18px;
    color: white;
  }
}
