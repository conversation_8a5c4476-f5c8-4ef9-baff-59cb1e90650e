import { Card, Input, Modal, Form, Breadcrumb, Button } from 'antd';
import { FormOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import styled from 'styled-components';
import { useQuery } from '@/hooks/useQuery';
import { KnowledgeApi, DocumentApi } from '@/api';
import { KnowledgeModel, DocumentModel, DocumentSegmentModel } from '@/interface';
import './index.less';

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

const Container = styled.div`
  background-color: rgba(248, 250, 252);
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
`;

export default function Document() {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const { parsedQuery } = useQuery();
  const params = useParams();
  const [knowledgeDetail, setKnowledgeDetail] = useState<KnowledgeModel>();
  const [documentDetail, setDocumentDetail] = useState<DocumentModel>();
  const [segment, setSegment] = useState<DocumentSegmentModel[]>();

  const getKnowledegeDetail = () => {
    KnowledgeApi.get && KnowledgeApi.get(`${params.knowledgeId}`).then((res) => {
      setKnowledgeDetail(res);
    });
  };

  const getDocumentContent = (collectionId, docId) => {
    DocumentApi.getSegment(`${params.documentId}`, collectionId, docId).then((res) => {
      setSegment(res);
    });
  };

  const getDocumentDetail = () => {
    DocumentApi.get(`${params.documentId}`).then((res) => {
      setDocumentDetail(res);
    });
  };

  useEffect(() => {
    if (knowledgeDetail?.collectionId && documentDetail?.doc_id) {
      getDocumentContent(knowledgeDetail?.collectionId, documentDetail.doc_id);
    }
  }, [knowledgeDetail?.collectionId, documentDetail?.doc_id]);

  useEffect(() => {
    getKnowledegeDetail();
    getDocumentDetail();
  }, [params.documentId]);

  const onModifyKnowledgeFinish = () => {
    form.validateFields().then((values) => {
      DocumentApi.update({
        title: values.title,
        collectionId: knowledgeDetail?.collectionId,
        doc_id: documentDetail?.doc_id,
      }, `${params.documentId}`).then(res => {
        setDocumentDetail({
          ...documentDetail!,
          title: values.title,
        });
        setOpen(false);
      });
    }).catch((error) => {
      console.log(error);
    });
  };

  const showEditKnowledge = () => {
    form.setFieldsValue({
      title: documentDetail?.title,
    });
    setOpen(true);
  };

  return (
    <Container style={{ height: '100%' }}>

      <div style={{ margin: '20px 20px 0' }}>
        <Breadcrumb
          items={[
            {
              title: <Link
                to={`/group/knowledge?workspaceId=${parsedQuery.workspaceId}&groupId=${parsedQuery.groupId}`}>知识库</Link>,
            },
            {
              title: <Link
                to={`/knowledge/${params.knowledgeId}?workspaceId=${parsedQuery.workspaceId}&groupId=${parsedQuery.groupId}`}>{knowledgeDetail?.name}</Link>,
            },
            {
              title: documentDetail?.title,
            },
          ]}
        />
      </div>
      <Card
        style={{ margin: '20px 20px', height: 'calc(100vh - 130px)', overflowY: 'auto' }}
        title={<div>
          {documentDetail?.title}
          <Button disabled type="link" style={{ padding: 0, marginLeft: 8 }}>
            <FormOutlined onClick={showEditKnowledge} />
          </Button>
        </div>}
      >
        <div className="document">
          {
            segment && segment.map(item => {
              return (
                <div className={`document_segment ${item.status < 0 ? 'failed' : ''}`}>
                  {
                    item.status < 0 ? <div className="document_segment_fail">导入失败</div> : null
                  }
                  <span>{item.content}</span>
                </div>
              );
            })
          }
        </div>
        <Modal
          title="编辑文档名称"
          open={open}
          width={560}
          onCancel={() => setOpen(false)}
          onOk={onModifyKnowledgeFinish}
        >
          <Form
            {...layout}
            form={form}
            style={{ padding: '48px 24px' }}
          >
            <Form.Item name="title" label="名称" rules={[{ required: true }]}>
              <Input placeholder="请输入" maxLength={128} showCount />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </Container>
  );
}
