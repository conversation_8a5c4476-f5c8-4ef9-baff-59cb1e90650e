// @ts-nocheck
import {
  CommentOutlined,
  EllipsisOutlined,
  ExclamationCircleFilled,
} from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Form, Input, message, Modal, Popover, Spin, Tooltip } from 'antd';
import classnames from 'classnames';
import jwt_decode from 'jwt-decode';
import VirtualList from 'rc-virtual-list';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import qs from 'querystring';
import { v4 as uuidv4 } from 'uuid';

import { ServiceAPI } from '@/api/service';
import { TokenApi } from '@/api/token';
import { AgentParamsRender } from '@/components/agent-params-render/complete';
import { ModelPredefinedParams, ModelSetting } from '@/components/ModelSetting';
import { useQuery } from '@/hooks/useQuery';
import { AppDetailModel } from '@/interface';
import { getToken, saveToken } from '@/utils/common';
import eventBus, { IEventType } from '@/utils/event-bus';

import { Conversation } from '../app/components/agent-dev/conversation';

const { confirm } = Modal;

interface AgentConversationPreviewProps {
  appId?: string;
  style?: React.CSSProperties;
  showModelSelector?: boolean;
  defaultModel?: string;
}

export function AgentConversationPreview() {
  const parsedQuery = qs.parse(window.location.search.replace('?', ''));
  const { appId } = parsedQuery;
  if (!appId || typeof appId !== 'string') {
    message.error('请检查页面URL: 缺少应用ID');
  }
  return <Container>
      <Conversation appId={appId}></Conversation>
  </Container>
}

export function AgentConversation(props: AgentConversationPreviewProps) {
  let { appId } = props;
  const { style = {}, showModelSelector, defaultModel } = props;
  const [loading, setLoading] = useState(true);
  const [appDetail, setAppDetail] = useState<AppDetailModel | null>(null);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [activeKey, setActiveKey] = useState<string | null>(null);

  const [token, setToken] = useState<string>();

  const { run: createToken } = useRequest(() => TokenApi.createAppTempToken(appId), {
    manual: true,
    onSuccess: (res) => {
      saveToken(appId, res.token);
      setToken(res.token);
    },
  });

  const { parsedQuery } = useQuery();
  if (!appId) {
    const { appId: parsedAppId } = parsedQuery;
    appId = parsedAppId;
  }

  if (!appId || typeof appId !== 'string') {
    message.error('请检查页面URL: 缺少应用ID');
  }

  useEffect(() => {
    const t = getToken(appId);
    let shouldRecreate = false;

    try {
      const decoded_token = jwt_decode(t);
      const expire = new Date(decoded_token.exp * 1000);
      const now = new Date();
      const expectedExpire = new Date(now.getTime() + 1000 * 60 * 60 * 24 * 7);
      if (expire < expectedExpire) {
        shouldRecreate = true;
      }
    } catch (e) {
      window?.corona?.warn('token err', e);
      shouldRecreate = true;
      console.log(e);
    }
    if (shouldRecreate) {
      createToken();
    } else {
      setToken(t);
    }
  }, [appId]);

  useRequest(
    () => {
      return ServiceAPI.getAppDetail(appId, token);
    },
    {
      ready: token !== undefined,
      refreshDeps: [token, appId],
      onSuccess: (res) => {
        setAppDetail(res);
      },
      onFinally: () => {
        setLoading(false);
      },
    },
  );

  const chatPanel = useMemo(() => {
    return (
      <ChatPanel
        defaultModel={defaultModel}
        showExtra={showModelSelector}
        key={activeKey}
        activeConversationId={activeConversationId}
        app={appDetail!}
      />
    );
  }, [appDetail, activeKey, showModelSelector]);

  if (loading) {
    return (
      <div style={{ height: '500px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin tip="加载中，请稍后..." size="large" >
          <div style={{ width: '200px' }} />
        </Spin>
      </div>
    );
  }

  return (
    <Container style={style}>
      <ChatList
        appId={appId as string}
        onActiveConversationChange={(id, key) => {
          setActiveConversationId(id);
          setActiveKey(key);
        }}
      />
      {appDetail && chatPanel}
    </Container>
  );
}

interface IChatPanel {
  app: AppDetailModel;
  activeConversationId: string | null;
  showExtra?: boolean;
  defaultModel?: string;
}

function ChatPanel(props: IChatPanel) {
  const { app, activeConversationId, showExtra = false, defaultModel } = props;
  const [inputParams, setInputParams] = useState<Record<string, any> | null>(null);
  const [modelParams, setModelParams] = useState<Record<string, any>>();
  const [providerKind, setProviderKind] = useState<string>(app.config.providerKind);
  const [modelName, setModelName] = useState<string>(defaultModel ? defaultModel : app.config.modelName);

  if (!app) {
    return null;
  }

  function onStartConversation(inputParams: Record<string, any>) {
    setInputParams(inputParams);
  }

  const config = useMemo(() => {
    let t = modelParams;
    if (!t) {
      t = app.config.modelParams;
    }
    const c = {
      ...app.config,
      modelParams: t,
      providerKind: providerKind,
      modelName: modelName,
    };
    return c;
  }, [app, modelParams, providerKind, modelName]);

  const extra = useMemo(() => {
    return (
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div style={{ flexGrow: '1' }}>
          <h4>基础配置</h4>
          <ModelPredefinedParams onClick={setModelParams} defaultValue="balanced" />
        </div>
        <div style={{ flexGrow: '1' }}>
          <h4>高级配置</h4>
          <ModelSetting
            destroyModal
            disabledList={[/moonshot-v1-128k/, /moonshot-v1-32k/, /gpt-3.5-turbo-16k/, /^gpt-4$/, /^gpt-4-.*/, /gpt-4o-2024-08-06/, /claude-3-5-sonnet-20240620/, ]}
            value={config}
            onChange={(s) => {
              setProviderKind(s.providerKind);
              setModelName(s.modelName);
              setModelParams(s.modelParams);
            }}
          />
        </div>
      </div>
    );
  }, [config]);

  if (activeConversationId === '__new__' && !inputParams) {
    return (
      <StyledChatPanel>
        <div className="chat-init">
          <div className="app-name">{app.name}</div>
          <div className="params-input">
            <AgentParamsRender
              // paramsInPrompt={app.config.paramsInPrompt}
              onCommit={onStartConversation}
              labelAlign="left"
              commitBtnName="开始对话"
              commitBtnIcon={<CommentOutlined rev={undefined} />}
              extra={showExtra && extra}
            />
          </div>
        </div>
      </StyledChatPanel>
    );
  }

  return (
    <StyledChatPanel>
      <Conversation
        style={{
          height: '100%',
          width: '100%',
        }}
        appId={app.id}
        conversationId={
          activeConversationId && activeConversationId !== '__new__'
            ? activeConversationId
            : undefined
        }
        inputParams={inputParams}
        config={config}
      ></Conversation>
    </StyledChatPanel>
  );
}

interface IChatList {
  appId: string;
  onActiveConversationChange?: (conversationId: string, key: string) => void;
}

function ChatList(props: IChatList) {
  const { appId, onActiveConversationChange = () => { } } = props;
  const [conversationList, setConversationList] = useState<any[]>([]);
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [containerHeight, setConntainerHeight] = useState(600);
  const chatListRef = useRef();

  useEffect(() => {
    if (chatListRef.current) {
      setConntainerHeight(chatListRef.current.clientHeight - 80);
    }
  }, [chatListRef.current]);

  const setConversationListWrapped = useCallback((list: any[]) => {
    list.forEach((it) => {
      if (!it.key) {
        it.key = uuidv4();
      }
    });
    const set: Record<string, ''> = {};
    list = list.filter((it) => {
      if (!set[it.id]) {
        set[it.id] = '';
        return true;
      }
      return false;
    });
    setConversationList(list);
  }, []);

  function onNewConversationClick(list?: any[]) {
    let t = conversationList;
    if (Array.isArray(list)) {
      t = list;
    }
    if (t.findIndex((it) => it.id === '__new__') === -1) {
      setConversationListWrapped([
        {
          id: '__new__',
          name: '新的对话',
        },
        ...t,
      ]);
    }
    setActiveId('__new__');
  }

  const { run: loadMore } = useRequest(
    (args: { pageNumber?: number; conversationID?: number }) => {
      const { pageNumber, conversationID } = args;
      return ServiceAPI.listOrGetConversation(appId, conversationID, pageNumber);
    },
    {
      manual: true,
      debounceWait: 200,
      onSuccess: (res, args) => {
        const { pageNumber, conversationID } = args[0];

        if (conversationID) {
          const t = conversationList.map((it) => {
            if (it.id === res.id) {
              it.name = res.name;
            }
            return it;
          });
          setConversationList(t);
          return;
        }

        if (res.items.length === 0) {
          if (currentPage === 0) {
            onNewConversationClick();
          }
          return;
        }
        if (conversationList.length === 0) {
          loadMore({ pageNumber: 2 });
          if (res.items.length > 0) {
            setActiveId(res.items[0].id);
          }
        }
        setCurrentPage(pageNumber);
        const t = [...conversationList, ...res.items];
        t.map((it) => {
          it.pageNumber = pageNumber;
          return it;
        });
        setConversationListWrapped(t);
      },
    },
  );

  useEffect(() => {
    loadMore({ pageNumber: 1 });
  }, []);

  useEffect(() => {
    const newChatEventId = eventBus.on(
      IEventType.NEW_CHAT_CREATE,
      (conversationId: string) => {
        const filters = conversationList.filter((it) => it.id === conversationId);
        // 说明是点击其他的节点
        if (filters.length) {
          return ;
        }
        setConversationList(
          conversationList.map((it) => {
            if (it.id === '__new__') {
              it.name = '新的对话';
              it.id = conversationId;
              loadMore({ conversationID: conversationId });
            }
            return it;
          }),
        );
        setActiveId(conversationId);
      },
    );
    return () => {
      eventBus.cancel(IEventType.NEW_CHAT_CREATE, newChatEventId);
    };
  }, [conversationList]);

  useEffect(() => {
    if (activeId) {
      const key = conversationList.find((it) => it.id === activeId)?.key;
      if (key) {
        onActiveConversationChange(activeId, key);
      }
    }
  }, [activeId, conversationList]);

  const { run: convRename } = useRequest(
    (convID: string, name: string) => ServiceAPI.updateConversation(appId, convID, name),
    {
      manual: true,
      onSuccess: (_, params) => {
        message.success('重命名成功');
        conversationList.map((it) => {
          if (it.id === params[0]) {
            it.name = params[1];
          }
        });
        setConversationListWrapped([...conversationList]);
      },
    },
  );

  const conversationDelete = useCallback(
    (msgId: string) => {
      return ServiceAPI.deleteConversation(appId, msgId).then(() => {
        const newList = conversationList.filter((it) => it.id !== msgId);
        setConversationListWrapped(newList);
        if (newList.length) {
          setActiveId(newList[0] && newList[0].id);
        } else {
          onNewConversationClick(newList);
        }
      });
    },
    [appId, conversationList],
  );

  function onDeleteClick(msgId: string) {
    confirm({
      title: '删除对话',
      icon: <ExclamationCircleFilled rev={undefined} />,
      content: '您确认要删除此对话?',
      onOk: () => {
        return conversationDelete(msgId);
      },
      onCancel() { },
    });
  }

  const onRenameFinish = useCallback(
    (values: { name: string }) => {
      convRename(activeId!, values.name);
    },
    [activeId],
  );

  const onListScroll = useCallback(
    (e: React.UIEvent<HTMLElement, UIEvent>) => {
      if (e.currentTarget.scrollHeight - e.currentTarget.scrollTop >= containerHeight) {
        loadMore({ pageNumber: currentPage + 1 });
      }
    },
    [containerHeight, currentPage],
  );

  return (
    <StyledChatList ref={chatListRef}>
      <Modal
        width={300}
        footer={null}
        open={showRenameModal}
        title={'对话重命名'}
        onCancel={() => setShowRenameModal(false)}
      >
        <Form
          onFinish={(values) => {
            setShowRenameModal(false);
            onRenameFinish(values);
          }}
        >
          <Form.Item name="name">
            <Input />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      <Button onClick={onNewConversationClick}>新对话</Button>
      <VirtualList
        data={conversationList}
        itemKey={(item) => item.id}
        onScroll={onListScroll}
        style={{ width: '200px' }}
        height={containerHeight}
      >
        {(it) => {
          const name: string = it.name || '新对话';
          return (
            <div
              key={it.id}
              className={classnames({
                'chat-item': true,
                active: activeId === it.id,
              })}
              onClick={() => setActiveId(it.id)}
            >
              <CommentOutlined rev={undefined} />
              <Tooltip title={name}>
                <span style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{name}</span>
              </Tooltip>
              {it.id !== '__new__' && (
                <span
                  className="extra-action"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <Popover
                    placement="bottomRight"
                    content={
                      <StyledExtraActions>
                        <div onClick={() => onDeleteClick(it.id)}>删除</div>
                        <div
                          onClick={() => {
                            setActiveId(it.id);
                            setShowRenameModal(true);
                          }}
                        >
                          重命名
                        </div>
                      </StyledExtraActions>
                    }
                    trigger="click"
                  >
                    <EllipsisOutlined rev={undefined} />
                  </Popover>
                </span>
              )}
            </div>
          );
        }}
      </VirtualList>
    </StyledChatList>
  );
}

const Container = styled.div`
  display: flex;
  flex-direction: row;
  height: 100vh;
  box-sizing: border-box;
`;

const StyledChatList = styled.div`
  margin: 0 10px;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  align-items: center;
  padding: 20px;
  padding-left: 5px;
  padding-right: 5px;

  .ant-btn {
    width: 100%;
    margin-bottom: 20px;
  }

  .chat-item {
    display: flex;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    cursor: pointer;
    margin-bottom: 10px;
    position: relative;
    border-radius: 4px;

    &:hover {
      background-color: #ddd;

      .extra-action {
        visibility: visible;
      }
    }

    &.active {
      background-color: rgba(69, 121, 244, 0.1);
      color: #1654c8;
    }

    .anticon {
      font-size: 18px;
      margin-right: 10px;
    }

    .extra-action {
      visibility: hidden;
      position: absolute;
      right: 10px;
      background-color: #fefefe;
      padding: 0 2px;

      & .anticon {
        margin-right: 0px;
      }
    }
  }
`;

const StyledChatPanel = styled.div`
  display: flex;
  flex-direction: column;
  background-color: white;
  flex-grow: 1;
  height: 100%;
  width: min-content;
  min-width: 500px;
  padding: 20px;
  box-sizing: border-box;
  align-items: center;
  border-radius: 8px;
  
  .AdaApp {
    width: 100%;
  }

  .chat-init {
    width: 800px;
    margin-top: 100px;
    background-color: white;
    border: 1px solid #eee;
    border-radius: 20px;

    .app-name {
      font-size: 18px;
      padding: 20px;
      background-color: rgba(87, 133, 240, 0.1);
      border-top-right-radius: 20px;
      border-top-left-radius: 20px;
    }

    .params-input {
      padding: 20px;
    }
  }
`;

const StyledExtraActions = styled.div`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  & > div {
    padding: 2px 10px;
    cursor: pointer;

    &:hover {
      background-color: #eee;
    }
  }
`;
