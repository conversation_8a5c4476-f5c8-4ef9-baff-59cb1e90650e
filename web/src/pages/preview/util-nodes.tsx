/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable react/jsx-no-undef */
/* eslint-disable react/jsx-key */
// @ts-nocheck
import { CaretRightOutlined } from '@ant-design/icons';
import { ConfigProvider, Button, Form, Result, Select, Radio } from 'antd';
import { formatInputValue, joinInputMap } from '@/utils/common';
import { getRealType } from '@/utils/node';
import { TYPE_MAP } from './type-map';

import { FormattedNode } from '../app/workflow/react-node/input-nodes';
import { DecimalStep, IntegerStep } from '../app/workflow/react-node/step';


export const ParamRender = (props) => {
  const { inputs, onSubmit, defaultInputs, hideDefaultInputs } = props;

  // 渲染特定的参数
  const renderParam = (v) => {
    const [dateType] = getRealType(v.type);
    const permanent = v.permanent;
    const value = defaultInputs?.[v.name] ?? v.value;
    if (v?.enum) {
      if (v.enum.length <= 3) {
        return <Radio.Group defaultValue={value} options={v.enum.map((v) => ({ label: v?.label ?? v?.value ?? v, value: v?.value ?? v }))}></Radio.Group>
      } else {
        return <Select defaultValue={value} options={v.enum.map((v) => ({ label: v?.label ?? v?.value ?? v, value: v?.value ?? v }))}></Select>;
      }
    }
    if (v?.range) {
      if (v.type === 'integer') {
        return <IntegerStep min={v.range[0]} max={v.range[1]} defaultValue={value}></IntegerStep>
      } else {
        return <DecimalStep min={v.range[0]} max={v.range[1]} defaultValue={value}></DecimalStep>
      }
    }
    return <FormattedNode type={dateType} defaultValue={value} params={[v]} permanent={permanent}></FormattedNode>;
  };

  const joinData = (values: any) => {
    return inputs.map((v) =>  {
      let value = typeof values[v.name] !== 'undefined' ? values[v.name] : v.value;
      if (defaultInputs && defaultInputs[v.name]) {
        value = defaultInputs[v.name];
      }
      return ({ ...v, value });
    })
  };

  const onFinish = async (values: any) => {
    // debugger
    console.log("onFinish", values, inputs);
    if (onSubmit) {
      const inputs = {};
      const inputsWithData = joinData(values);
      const result = await Promise.all(inputsWithData.map((node) => formatInputValue(node)));
      result.forEach((v) => {
        if (v.value !== undefined && v.value !== '') {
          inputs[v.name] = v.value;
        }
      });
      console.log('result', result);
      onSubmit(inputs);
    }
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            itemMarginBottom: 12
          },
        },
      }}
    >
      <Form onFinish={onFinish} layout={'vertical'}>
        {inputs.map((v) => {
          const value = defaultInputs?.[v.name];
          const hide = hideDefaultInputs ? !!value : false;
          return (
            <Form.Item
              label={`${v.title || v.name}`}
              name={v.name}
              hidden={hide}
              // rules={[{ required: !v.value && v.value !== 0 }]}
              help={v.description}
            >
              {renderParam(v)}
            </Form.Item>
          )
        })}
        <Button
          htmlType="submit"
          type="primary"
          icon={<CaretRightOutlined rev={undefined} />}
        >
          运行
        </Button>
      </Form>
    </ConfigProvider>
  );
};

export function formatInputs(inputs) {
  return inputs.map(v => ({
    ...v,
    value: joinInputMap[v.type] ? joinInputMap[v.type](v.value) : v.value
  }));
}



export function formatRes(outputs, res?) {
  if (res?.status === 'failed') {
    return <Result
      status="error"
      subTitle={res.message}
    >
    </Result>;
  }
  if (!outputs || !Array.isArray(outputs)) {
    return null;
  }
  const text = outputs.map((v) => {
    let value = (v.value || v.value === 0) ? v.value : '';
    if (res?.outputs && res.outputs[v.name]) {
      value = res.outputs[v.name];
    };
    const [dateType, innerType] = getRealType(v.type);
    let content = value;
    if (typeof content === 'object') {
      content = TYPE_MAP.object(content);
    }
    if (dateType === 'array' && Array.isArray(value)) {
      if (TYPE_MAP[innerType]) {
        content = value.map((v) => TYPE_MAP[innerType](v));
      } else {
        content = TYPE_MAP.object(value);
      }
    } else if (TYPE_MAP[dateType]) {
      content = TYPE_MAP[dateType](value);
    }
    return (
      <>
        <h3>
          {v.title || v.name}({v.name})
        </h3>
        {content}
      </>
    );
  });
  return text;
}