/* eslint-disable react/jsx-key */
// @ts-nocheck
import { message, Spin } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

import { AppApi } from '@/api/app';
import { useQuery } from '@/hooks/useQuery';
import { AppDetailModel } from '@/interface';
import Preview from '@/pages/app/workflow/workflow-preview';

export function WorkflowPreview() {
  const [loading, setLoading] = useState(true);
  const [appDetail, setAppDetail] = useState<AppDetailModel | null>(null);

  const { parsedQuery } = useQuery();
  const { appId, configId } = parsedQuery;

  if (!appId && !configId) {
    message.error('请检查页面URL: 缺少应用ID或配置ID');
  }

  useEffect(() => {
    if (!appId && !configId) {
      return;
    }
    if (configId) {
      AppApi.getAppConfig(configId as string)
        .then((res) => {
          setAppDetail(res);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      AppApi.getAppDetailWithoutAuth(appId as string)
        .then((res) => {
          setAppDetail(res);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [appId, configId]);

  if (loading) {
    return (
      <>
        <Spin />
      </>
    );
  }

  return (
    <Container>
      {appDetail.config && <Preview graphData={appDetail.config}></Preview>}
    </Container>
  );
}

const Container = styled.div`
  display: flex;
  flex-direction: row;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
`;
