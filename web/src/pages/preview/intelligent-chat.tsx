// @ts-nocheck
import {
  CommentOutlined,
  EllipsisOutlined,
  ExclamationCircleFilled,
} from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Flex, Form, Input, message, Modal, Popover, Spin, Tooltip } from 'antd';
import classnames from 'classnames';
import jwt_decode from 'jwt-decode';
import VirtualList from 'rc-virtual-list';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import qs from 'querystring';
import { v4 as uuidv4 } from 'uuid';

import { ServiceAPI } from '@/api/service';
import { TokenApi } from '@/api/token';
import { OpenAiAPI } from '@/api/conversation';
import { AgentParamsRender } from '@/components/agent-params-render/complete';
import { ModelPredefinedParams, ModelSetting } from '@/components/ModelSetting';
import { useQuery } from '@/hooks/useQuery';
import { AppDetailModel } from '@/interface';
import { getDefaultModel, getToken, saveToken } from '@/utils/common';
import eventBus, { IEventType } from '@/utils/event-bus';

import { Conversation } from '../app/components/agent-dev/conversation';

const { confirm } = Modal;

interface AgentConversationPreviewProps {
  appId?: string;
  style?: React.CSSProperties;
  showModelSelector?: boolean;
  defaultModel?: string;
}

export function AgentConversationPreview() {
  const parsedQuery = qs.parse(window.location.search.replace('?', ''));
  const { appId } = parsedQuery;
  if (!appId || typeof appId !== 'string') {
    message.error('请检查页面URL: 缺少应用ID');
  }
  return <Container>
    <Conversation appId={appId}></Conversation>
  </Container>
}

// 智聊页面
export function IntelligentChat(props: AgentConversationPreviewProps) {
  let { appId } = props;
  const { style = {}, showModelSelector, defaultModel } = props;
  const [loading, setLoading] = useState(true);
  const [appDetail, setAppDetail] = useState<AppDetailModel | null>(null);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [activeKey, setActiveKey] = useState<string | null>(null);

  const [token, setToken] = useState<string>();

  const { run: createToken } = useRequest(() => TokenApi.createAppTempToken(appId), {
    manual: true,
    onSuccess: (res) => {
      saveToken(appId, res.token);
      setToken(res.token);
    },
  });

  const { parsedQuery } = useQuery();
  if (!appId) {
    const { appId: parsedAppId } = parsedQuery;
    appId = parsedAppId;
  }

  if (!appId || typeof appId !== 'string') {
    message.error('请检查页面URL: 缺少应用ID');
  }

  useEffect(() => {
    const t = getToken(appId);
    let shouldRecreate = false;

    try {
      const decoded_token = jwt_decode(t);
      const expire = new Date(decoded_token.exp * 1000);
      const now = new Date();
      const expectedExpire = new Date(now.getTime() + 1000 * 60 * 60 * 24 * 7);
      if (expire < expectedExpire) {
        shouldRecreate = true;
      }
    } catch (e) {
      window?.corona?.warn('token err', e);
      shouldRecreate = true;
      console.log(e);
    }
    if (shouldRecreate) {
      createToken();
    } else {
      setToken(t);
    }
  }, [appId]);

  useRequest(
    () => {
      return ServiceAPI.getAppDetail(appId, token);
    },
    {
      ready: token !== undefined,
      refreshDeps: [token, appId],
      onSuccess: (res) => {
        setAppDetail(res);
      },
      onFinally: () => {
        setLoading(false);
      },
    },
  );

  const chatPanel = useMemo(() => {
    return (
      <ChatPanel
        defaultModel={defaultModel}
        showExtra={showModelSelector}
        key={activeKey}
        activeConversationId={activeConversationId}
        app={appDetail!}
      />
    );
  }, [appDetail, activeKey, showModelSelector]);

  if (loading) {
    return (
      <div style={{ height: '500px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Spin tip="加载中，请稍后..." size="large" >
          <div style={{ width: '200px' }} />
        </Spin>
      </div>
    );
  }

  return (
    <Container style={style}>
      <ChatList
        appId={appId as string}
        onActiveConversationChange={(id, key) => {
          setActiveConversationId(id);
          setActiveKey(key);
        }}
      />
      {appDetail && chatPanel}
    </Container>
  );
}

interface IChatPanel {
  app: AppDetailModel;
  activeConversationId: string | null;
  showExtra?: boolean;
  defaultModel?: string;
}

const defaultConversation = {
  "name": "新的对话",
  "id": "9a6e405e-5442-4e32-aebc-466527c8ead4",
  "app_config_id": "946f075b-e6ba-4b46-bc84-82fb0fdcdd72",
  "override_config": {
    "prePrompt": null,
    "paramsInPrompt": null,
    "prologue": null,
    "tools": null,
    "knowledge": null,
    "modelName": "deepseek-r1-latest",
    "modelParams": {
      "temperature": 0.5,
      "max_tokens": 4096,
      "top_p": 0.85,
      "frequency_penalty": 0,
      "presence_penalty": 0,
    },
    "providerKind": "deepseek"
  }
}

function ChatPanel(props: IChatPanel) {
  const { app, activeConversationId, showExtra = false, defaultModel } = props;
  const [inputParams, setInputParams] = useState<Record<string, any> | null>({});
  const [modelParams, setModelParams] = useState<Record<string, any>>();
  const [providerKind, setProviderKind] = useState<string>(app.config.providerKind);
  const [modelName, setModelName] = useState<string>(defaultModel ? defaultModel : app.config.modelName);
  const [conversation, setConversation] = useState<any>(null);

  if (!app) {
    return null;
  }

  useEffect(() => {
    if (activeConversationId === '__new__') {
      setConversation({...defaultConversation, override_config: {
        ...defaultConversation.override_config,
        modelName: getDefaultModel().modelName,
        providerKind: getDefaultModel().providerKind,
      }});
      setModelParams(defaultConversation.override_config.modelParams);
      setProviderKind(getDefaultModel().providerKind);
      setModelName(getDefaultModel().modelName);
      return;
    }
    if (activeConversationId) {
      OpenAiAPI.getConversation(activeConversationId).then((res) => {
        console.log('getConversation', res);
        setConversation(res);
      });
    }
  }, [activeConversationId]);

  function onStartConversation(inputParams: Record<string, any>) {
    console.log('onStartConversation', inputParams);
    setInputParams(inputParams);
  }

  const config = useMemo(() => {
    let t = modelParams;
    if (!t) {
      t = app.config.modelParams;
    }
    const c = {
      ...app.config,
      modelParams: t,
      providerKind: providerKind,
      modelName: modelName,
    };
    return c;
  }, [app, modelParams, providerKind, modelName]);

  const extra = useMemo(() => {
    return (
      <div style={{ display: 'flex', flexDirection: 'row' }}>
        <div style={{ flexGrow: '1' }}>
          <span style={{ fontSize: '14px', color: '#000', fontWeight: 'bold' }}>模型配置  </span>
          <ModelSetting
            destroyModal
            disabledList={[/moonshot-v1-128k/, /moonshot-v1-32k/, /gpt-3.5-turbo-16k/, /^gpt-4$/, /^gpt-4-.*/,]}
            value={config}
            onChange={(s) => {
              setProviderKind(s.providerKind);
              setModelName(s.modelName);
              setModelParams(s.modelParams);
            }}
          />
        </div>
      </div>
    );
  }, [config]);

  // if (activeConversationId === '__new__' && !inputParams) {
  //   return (
  //     <StyledChatPanel>
  //       <div className="chat-init">
  //         <div className="app-name">{app.name}</div>
  //         <div className="params-input">
  //           <AgentParamsRender
  //             // paramsInPrompt={app.config.paramsInPrompt}
  //             onCommit={onStartConversation}
  //             labelAlign="left"
  //             commitBtnName="开始对话"
  //             commitBtnIcon={<CommentOutlined rev={undefined} />}
  //             extra={showExtra && extra}
  //           />
  //         </div>
  //       </div>
  //     </StyledChatPanel>
  //   );
  // }

  async function changeModel(config: AgentConfig) {
    console.log(config);
    if (activeConversationId === '__new__') {
      setModelParams(config.modelParams);
      setProviderKind(config.providerKind);
      setModelName(config.modelName);
      console.log('create conversation');
    } else {
      await OpenAiAPI.updateConversation(activeConversationId!, conversation?.name, config);
    }
    setConversation({ ...conversation, override_config: config });
  }

  return (
    <StyledChatPanel>
      <div style={{width: '100%'}}>
        {conversation?.override_config &&
          <ModelSetting
            destroyModal
            isChatExplore={true}
            style={{ marginBottom: '10px' }}
            disabledList={[/moonshot-v1-128k/, /moonshot-v1-32k/, /gpt-3.5-turbo-16k/, /^gpt-4$/, /^gpt-4-.*/]}
            value={conversation?.override_config}
            onConfirm={(s) => {
              console.log(JSON.stringify(s) === JSON.stringify(conversation?.override_config));
              if (JSON.stringify(s) === JSON.stringify(conversation?.override_config)) {
                return;
              }
              changeModel(s);
            }}
          />
        }
      </div>
      <Flex flex={1} style={{ overflow: 'scroll', width: '100%' }}>
        <Conversation
          style={{
            height: '100%',
            width: '100%',
          }}
          explore
          appId={app.id}
          conversationId={
            activeConversationId && activeConversationId !== '__new__'
              ? activeConversationId
              : undefined
          }
          inputParams={conversation?.parameters}
          config={conversation?.override_config ?? config}
        ></Conversation>
      </Flex>

    </StyledChatPanel>
  );
}

interface IChatList {
  appId: string;
  onActiveConversationChange?: (conversationId: string, key: string) => void;
}

function ChatList(props: IChatList) {
  const { appId, onActiveConversationChange = () => { } } = props;
  const [conversationList, setConversationList] = useState<any[]>([]);
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [containerHeight, setConntainerHeight] = useState(600);
  const chatListRef = useRef();

  useEffect(() => {
    if (chatListRef.current) {
      setConntainerHeight(chatListRef.current.clientHeight - 80);
    }
  }, [chatListRef.current]);

  const setConversationListWrapped = useCallback((list: any[]) => {
    list.forEach((it) => {
      if (!it.key) {
        it.key = uuidv4();
      }
    });
    const set: Record<string, ''> = {};
    list = list.filter((it) => {
      if (!set[it.id]) {
        set[it.id] = '';
        return true;
      }
      return false;
    });
    setConversationList(list);
  }, []);

  function onNewConversationClick(list?: any[]) {
    let t = conversationList;
    if (Array.isArray(list)) {
      t = list;
    }
    if (t.findIndex((it) => it.id === '__new__') === -1) {
      setConversationListWrapped([
        {
          id: '__new__',
          name: '新的对话',
        },
        ...t,
      ]);
    } else {
      message.warning('新的对话已存在，可以先尝试跟它对话哦');
    }
    setActiveId('__new__');
  }

  const { run: loadMore } = useRequest(
    (args: { pageNumber?: number; conversationID?: number }) => {
      const { pageNumber, conversationID } = args;
      return ServiceAPI.listOrGetConversation(appId, conversationID, pageNumber);
    },
    {
      manual: true,
      debounceWait: 200,
      onSuccess: (res, args) => {
        const { pageNumber, conversationID } = args[0];

        if (conversationID) {
          const t = conversationList.map((it) => {
            if (it.id === res.id) {
              it.name = res.name;
            }
            return it;
          });
          setConversationList(t);
          return;
        }

        if (res.items.length === 0) {
          if (currentPage === 0) {
            onNewConversationClick();
          }
          return;
        }
        if (conversationList.length === 0) {
          loadMore({ pageNumber: 2 });
          if (res.items.length > 0) {
            setActiveId(res.items[0].id);
          }
        }
        setCurrentPage(pageNumber);
        const t = [...conversationList, ...res.items];
        t.map((it) => {
          it.pageNumber = pageNumber;
          return it;
        });
        setConversationListWrapped(t);
      },
    },
  );

  useEffect(() => {
    loadMore({ pageNumber: 1 });
  }, []);

  useEffect(() => {
    const newChatEventId = eventBus.on(
      IEventType.NEW_CHAT_CREATE,
      (conversationId: string) => {
        const filters = conversationList.filter((it) => it.id === conversationId);
        // 说明是点击其他的节点
        if (filters.length) {
          return;
        }
        setConversationList(
          conversationList.map((it) => {
            if (it.id === '__new__') {
              it.name = '新的对话';
              it.id = conversationId;
              loadMore({ conversationID: conversationId });
            }
            return it;
          }),
        );
        setActiveId(conversationId);
      },
    );
    return () => {
      eventBus.cancel(IEventType.NEW_CHAT_CREATE, newChatEventId);
    };
  }, [conversationList]);

  useEffect(() => {
    if (activeId) {
      const key = conversationList.find((it) => it.id === activeId)?.key;
      if (key) {
        onActiveConversationChange(activeId, key);
      }
    }
  }, [activeId, conversationList]);

  const { run: convRename } = useRequest(
    (convID: string, name: string) => OpenAiAPI.updateConversation(convID, name),
    {
      manual: true,
      onSuccess: (_, params) => {
        message.success('重命名成功');
        conversationList.map((it) => {
          if (it.id === params[0]) {
            it.name = params[1];
          }
        });
        setConversationListWrapped([...conversationList]);
      },
    },
  );

  const conversationDelete = useCallback(
    (msgId: string) => {
      return ServiceAPI.deleteConversation(appId, msgId).then(() => {
        const newList = conversationList.filter((it) => it.id !== msgId);
        setConversationListWrapped(newList);
        if (newList.length) {
          setActiveId(newList[0] && newList[0].id);
        } else {
          onNewConversationClick(newList);
        }
      });
    },
    [appId, conversationList],
  );

  function onDeleteClick(msgId: string) {
    confirm({
      title: '删除对话',
      icon: <ExclamationCircleFilled rev={undefined} />,
      content: '您确认要删除此对话?',
      onOk: () => {
        return conversationDelete(msgId);
      },
      onCancel() { },
    });
  }

  const onRenameFinish = useCallback(
    (values: { name: string }) => {
      convRename(activeId!, values.name);
    },
    [activeId],
  );

  const onListScroll = useCallback(
    (e: React.UIEvent<HTMLElement, UIEvent>) => {
      if (e.currentTarget.scrollHeight - e.currentTarget.scrollTop >= containerHeight) {
        loadMore({ pageNumber: currentPage + 1 });
      }
    },
    [containerHeight, currentPage],
  );

  return (
    <StyledChatList ref={chatListRef}>
      <Modal
        width={300}
        footer={null}
        open={showRenameModal}
        title={'对话重命名'}
        onCancel={() => setShowRenameModal(false)}
      >
        <Form
          onFinish={(values) => {
            setShowRenameModal(false);
            onRenameFinish(values);
          }}
        >
          <Form.Item name="name">
            <Input />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit">
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      <Button onClick={onNewConversationClick}>新对话</Button>
      <VirtualList
        data={conversationList}
        itemKey={(item) => item.id}
        onScroll={onListScroll}
        style={{ width: '200px' }}
        height={containerHeight}
      >
        {(it) => {
          const name: string = it.name || '新对话';
          return (
            <div
              key={it.id}
              className={classnames({
                'chat-item': true,
                active: activeId === it.id,
              })}
              onClick={() => setActiveId(it.id)}
            >
              <CommentOutlined rev={undefined} />
              <Tooltip title={name}>
                <span style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{name}</span>
              </Tooltip>
              {it.id !== '__new__' && (
                <span
                  className="extra-action"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <Popover
                    placement="bottomRight"
                    content={
                      <StyledExtraActions>
                        <div onClick={() => onDeleteClick(it.id)}>删除</div>
                        <div
                          onClick={() => {
                            setActiveId(it.id);
                            setShowRenameModal(true);
                          }}
                        >
                          重命名
                        </div>
                      </StyledExtraActions>
                    }
                    trigger="click"
                  >
                    <EllipsisOutlined rev={undefined} />
                  </Popover>
                </span>
              )}
            </div>
          );
        }}
      </VirtualList>
    </StyledChatList>
  );
}

const Container = styled.div`
  display: flex;
  flex-direction: row;
  height: 100vh;
  box-sizing: border-box;
  .ChatFooter {
    border-top: 0px;
  }
`;

const StyledChatList = styled.div`
  margin: 0 10px;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  align-items: center;
  padding: 20px;
  padding-left: 5px;
  padding-right: 5px;

  .ant-btn {
    width: 100%;
    margin-bottom: 20px;
  }

  .chat-item {
    display: flex;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    cursor: pointer;
    margin-bottom: 10px;
    position: relative;
    border-radius: 4px;

    &:hover {
      background-color: #ddd;

      .extra-action {
        visibility: visible;
      }
    }

    &.active {
      background-color: rgba(69, 121, 244, 0.1);
      color: #1654c8;
    }

    .anticon {
      font-size: 18px;
      margin-right: 10px;
    }

    .extra-action {
      visibility: hidden;
      position: absolute;
      right: 10px;
      background-color: #fefefe;
      padding: 0 2px;

      & .anticon {
        margin-right: 0px;
      }
    }
  }
`;

const StyledChatPanel = styled.div`
  display: flex;
  flex-direction: column;
  background-color: white;
  flex-grow: 1;
  height: 100%;
  width: min-content;
  min-width: 500px;
  padding: 20px;
  box-sizing: border-box;
  align-items: center;
  border-radius: 8px;
  
  .AdaApp {
    width: 100%;
  }

  .chat-init {
    width: 800px;
    margin-top: 100px;
    background-color: white;
    border: 1px solid #eee;
    border-radius: 20px;

    .app-name {
      font-size: 18px;
      padding: 20px;
      background-color: rgba(87, 133, 240, 0.1);
      border-top-right-radius: 20px;
      border-top-left-radius: 20px;
    }

    .params-input {
      padding: 20px;
    }
  }
`;

const StyledExtraActions = styled.div`
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  & > div {
    padding: 2px 10px;
    cursor: pointer;

    &:hover {
      background-color: #eee;
    }
  }
`;
