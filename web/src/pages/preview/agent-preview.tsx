import { useEffect, useState } from 'react';
import qs from 'querystring';
import { message } from 'antd';
import { AppApi } from '@/api/app';
import { Conversation } from '../app/components/agent-dev/conversation';
import styled from 'styled-components';

const Container = styled.div`
background: #999;
height: 100vh;
padding-top: 50px;

.AdaApp {
  width: 700px;
    margin: 0 auto;
    height: calc(100vh - 100px);
}

`;

export function AgentConversationPreview1() {
  document.cookie = "langbase|session=; expires=Thu, 01 Jan 2025 00:00:00 UTC; path=/";
  const [config, setConfig] = useState(null);
  const [welcomeText, setWelcomeText] = useState(null);
  const parsedQuery = qs.parse(window.location.search.replace('?', ''));
  const { appId, type } = parsedQuery;
  if (!appId || typeof appId !== 'string') {
    message.error('请检查页面URL: 缺少应用ID');
  }

  useEffect(() => {
    AppApi.getAppDetailNoAuth(appId as string).then((res) => {
      console.log('res...', res);
      setConfig(res?.config);
      setWelcomeText(res?.config?.welcomeText || null);
    })
  }, []);


  return config ? <Container>
    <Conversation showNavbar appId={appId as string} config={config} welcomeText={welcomeText} type={type === 'agent-workflow' ? 'agent-workflow' : 'agent'}></Conversation>
  </Container> : null;
}