/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable react/jsx-no-undef */
/* eslint-disable react/jsx-key */
// @ts-nocheck
import { PreviewButton } from '@music/ct-langbase';
import { message, Spin } from 'antd';
import { Skeleton, Result, Progress } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useRef } from 'react';
import styled from 'styled-components';
import { formatInputs, ParamRender } from './util-nodes';

import { AppApi } from '@/api/app';
import { TokenApi } from '@/api/token';
import { useQuery } from '@/hooks/useQuery';
import { AppDetailModel } from '@/interface';
import { JSONParse } from '@/utils/common';
import { getRealType } from '@/utils/node';
import { TYPE_MAP } from './type-map';


const isDev = window.location.host.includes('dev');

export function formatRes(outputs, res?) {
  if (res?.status === 'failed') {
    return <Result
      status="error"
      subTitle={res.message}
    >
    </Result>;
  }
  if (!outputs || !Array.isArray(outputs)) {
    return null;
  }
  const text = outputs.map((v) => {
    let value = (v.value || v.value === 0 || v.value === false) ? v.value : '';
    if (res?.outputs && res.outputs[v.name]) {
      value = res.outputs[v.name];
    };
    const [dateType, innerType] = getRealType(v.type);
    let content = value;
    if (typeof content === 'object') {
      content = TYPE_MAP.object(content);
    }
    if (dateType === 'array' && Array.isArray(value)) {
      if (TYPE_MAP[innerType]) {
        content = value.map((v) => TYPE_MAP[innerType](v));
      } else {
        content = TYPE_MAP.object(value);
      }
    } else if (TYPE_MAP[dateType]) {
      content = TYPE_MAP[dateType](value);
    }
    return (
      <>
        <h3>
          {v.title || v.name}({v.name})
        </h3>
        {content}
      </>
    );
  });
  return text;
}

interface WorkflowPlaygroundProps {
  appId: string;
  runId: string;
  showHeader?: boolean;  // 控制头部是否显示，默认显示
  updateQuery?: (key: string, value: string, replace?: boolean) => void;
  defaultInputs?: Record<string, any>;
  hideDefaultInputs?: boolean;
}

export function WorkflowPlaygroundContent({
  appId,
  runId: _runId,
  updateQuery,
  showHeader = true,
  defaultInputs = {},
  hideDefaultInputs,
}: WorkflowPlaygroundProps) {
  const timer = useRef(null);
  const [loading, setLoading] = useState(true);
  const [percent, setPercent] = useState(0);
  const [runId, setRunId] = useState(_runId);
  const [appDetail, setAppDetail] = useState<AppDetailModel | null>(null);
  const [inputData, setInputData] = useState('{}');
  const [completeRes, setCompleteRes] = useState<string>('');
  const [range, setRange] = useState([]);
  const [completeLoading, setCompleteLoading] = useState(false);
  console.log("defaultInputs...", defaultInputs);

  const getToken = async () => {
    let tokenRes = window.localStorage.getItem(`token-${appId}`);
    if (tokenRes) {
      tokenRes = JSONParse(tokenRes);
      // 如果token没过期
      if (
        tokenRes &&
        tokenRes.expiredAt &&
        +new Date(tokenRes.expiredAt) > Date.now() + 60 * 60 * 1000
      )
        return tokenRes.token;
    }
    tokenRes = await TokenApi.createAppTempToken(appId);
    window.localStorage.setItem(`token-${appId}`, JSON.stringify(tokenRes));
    return tokenRes?.token;
  };

  useEffect(() => {
    console.log("run", runId);
    // 有runId则执行
    if (runId && appDetail) {
      setCompleteRes('');
      setCompleteLoading(true);
      getRun(runId, 2000);
      setPercent(0)
    }
  }, [runId, appDetail]);

  if (!appId || typeof appId !== 'string') {
    message.error('请检查页面URL: 缺少应用ID');
  }

  const inputs = useMemo(() => {
    const data = JSONParse(inputData);
    const inputsNodes = appDetail?.config?.inputs || [];
    if (!data) {
      return inputsNodes;
    }
    let result = inputsNodes.map(v => {
      // 必须要有asDefault才能设置默认值
      if (v.asDefault) {
        return ({ ...v, value: data[v.name] ?? v.value });
      }
      return ({ ...v, value: data[v.name] ?? undefined });
    })
    result = formatInputs(result)
    console.log('input data', data, inputsNodes, result);
    return result;
  }, [appDetail?.config?.inputs, inputData])

  useEffect(() => {
    if (!appId) {
      return;
    }
    getToken().then(token => {
      getRecentRuns(appId, token).then(res => setRange(res));
    });
    AppApi.getAppDetailWithoutAuth(appId as string)
      .then((res) => {
        setAppDetail(res);
      })
      .finally(() => {
        setLoading(false);
      });
    // 获取当前
  }, [appId]);

  function formatTime(time) {
    if (!time) {
      return;
    }
    if (time > 3600 * 24) {
      return `${(time / (3600 * 24).toFixed(1))}d`;
    }
    if (time > 3600) {
      return `${(time / 3600).toFixed(1)}h`;
    }
    if (time > 60) {
      return `${(time / 60).toFixed(1)}min`;
    }
    return `${time.toFixed(1)}s`
  }


  function getRun(runID, time) {
    if (timer.current) {
      clearTimeout(timer.current);
      timer.current = null;
    }
    timer.current = setTimeout(() => {
      getToken()
        .then((token) => AppApi.getWorkFlowRunService(token, appId, runID))
        .then((res) => {
          // 设置当前输入的值，用来替换默认值
          setInputData(JSON.stringify(res.inputs));
          if (res.status === 'success' || res.status === 'failed') {
            setPercent(100);
            setTimeout(() => {
              setCompleteLoading(false);
              setCompleteRes(formatRes(appDetail.config.outputs, res));
            }, 500)
          } else {
            // 设置进度
            if (range[1]) {
              let percent = (+new Date() - res.startTime) / range[1] / 10;
              console.log("range time", (+new Date() - res.startTime) / range[1] / 10)
              if (percent > 98) {
                percent = 98;
              }
              setPercent(percent.toFixed(1));
            }
            getRun(runID, time);
          }
        });
    }, time || 1000);
  }

  const onRun = async (inputs: any) => {
    const token = await getToken();
    const res = await AppApi.triggerService(token, appId, inputs, {
      business: 'langbase',
      scene: 'langbase-test',
    });
    // if (runId) {
    //   // 如果是通过 props 传入的 runId，直接调用 getRun
    //   getRun(res.runID, 2000);
    // } else {
    // 否则通过 URL 参数更新
    if (updateQuery) {
      updateQuery('runId', res.runID, true);
    }
    setRunId(res.runID);
    // }
  };

  if (loading) {
    return (
      <>
        <Spin />
      </>
    );
  }

  let env = isDev ? 'dev' : 'online';
  if (window.location.href.includes('onlinetest')) {
    env = 'test'
  }

  return (
    <Container>
      <Left>
        {showHeader ? (
          <>
            <div className="app-name">{appDetail?.name}</div>
            <div className="app-desc">
              {appDetail?.description}
              {appDetail?.config && (
                <PreviewButton appId={appId} env={env} runId={runId}></PreviewButton>
              )}
            </div>
            <div className="app-params">
              <ParamRender inputs={inputs} onSubmit={onRun} key={inputData} />
            </div>
          </>
        ) :
          <ParamRender inputs={inputs} onSubmit={onRun} key={inputData} defaultInputs={defaultInputs} hideDefaultInputs={hideDefaultInputs} />
        }
      </Left>
      <Right>
        <div className="title">结果展示</div>
        <div>
          {completeRes ? (
            <div className="res">
              {completeRes}
              {/* <div dangerouslySetInnerHTML={{ __html: completeRes }}></div> */}
            </div>
          ) : (
            <div className="empty">
              {completeLoading ?
                <div className='loading'>
                  <div style={{ textAlign: 'left', fontSize: '12px' }}>正在处理请稍后...{
                    range.length && range[0] && range[1] ? `大约需要${formatTime(range[0])} ~ ${formatTime(range[1])}` : null
                  }</div>
                  <Progress percent={percent} status="active" strokeColor={{ '100%': '#87d068', '50%': '#ffe58f', '0%': '#ffccc7' }} />
                  <Skeleton loading={completeLoading} active paragraph={{
                    rows: 20
                  }} />
                </div> :
                <div style={{ marginTop: '300px' }}>你生成的结果会在这边展示</div>}
            </div>
          )}
        </div>
      </Right>
    </Container>
  );
}

const Container = styled.div`
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
`;

const Left = styled.div`
  display: flex;
  flex-direction: column;
  background-color: white;
  min-width: 600px;
  max-width: 50%;
  padding: 20px;
  box-sizing: border-box;

  .app-name {
    font-size: 24px;
    font-weight: bold;
  }

  .app-desc {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
  }

  .app-params {
    border-top: 1px solid #eee;
    margin-top: 20px;
    padding-top: 20px;
  }
  .ant-form-item-explain {
    white-space: pre-wrap;
  }
`;

const Right = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ebedf3;
  flex-grow: 1;
  padding: 20px;
  box-sizing: border-box;

  .title {
    font-size: 24px;
    font-weight: bold;
  }

  .res {
    margin-top: 20px;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
  }

  .empty {
    text-align: center;
    color: #666;
  }
  pre {
    white-space: pre-line;
  }
`;


async function getRecentRuns(appId, token) {
  // 获取最近一天的执行情况
  const startTime = dayjs().subtract(1, 'd').startOf('day').valueOf();
  const debugRuns = await AppApi.getWorkFlowRunList(appId, true, startTime, null, 10, 1, token)
  const onlineRuns = await AppApi.getWorkFlowRunList(appId, false, startTime, null, 10, 1, token)
  // 找到最近10次运行成功的结果，并计算他们的最短和最长时间，和平均值
  let runsTimes = [...(onlineRuns?.items || []).filter(v => v.status === "success"), ...(debugRuns?.items || []).filter(v => v.status === "success")].map(v => (v.endTime - v.startTime) / 1000).filter(Number)
  console.log('runtimes', runsTimes);
  runsTimes.sort((a, b) => a - b);
  // 去掉前10%，后10%
  const nums = Math.ceil(runsTimes.length * 0.1);
  if (runsTimes.length > 5) {
    runsTimes = runsTimes.slice(nums, -nums);
  }
  // return [formatTime(runsTimes[0]), formatTime(runsTimes[runsTimes.length - 1])];
  return [runsTimes[0], runsTimes[runsTimes.length - 1]];
}