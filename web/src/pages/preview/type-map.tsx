import { CopyOutlined } from '@ant-design/icons';
import { Flex, Image, Typography } from 'antd';
import dayjs from 'dayjs';
import ReactJson from 'react-json-view';
import { copyText } from '@/utils/common';
import { Markdown } from '@/components/Markdown';

export const CopySpan = (props) => {
  const { children, ...rest } = props;
  const handleCopy = async () => {
    copyText(children);
  };

  return (
    <pre>
      <span {...rest}>{children}</span>
      <CopyOutlined
        onClick={handleCopy}
        style={{ marginLeft: '5px', color: '#1677ff' }}
      />
    </pre>
  );
};

export const nos2url = c => {
  if (!c) {
    return c;
  }
  if (c.includes('http')) {
    return c;
  }
  if (c.includes('yyimgs')) {
    return `https://p6.music.126.net/${c.split('/')[1]}`;
  }
  const prefix = c.split('/')[0];
  let src = '';
  if (prefix.includes('jd')) {
    src = `https://${c.split('/')[0]}.nos-jd.163yun.com/${c.split('/').slice(1).join('/')}`
  } else {
    src = `https://nos.netease.com/${c}`;
  }
  return src;
}

export const TYPE_MAP = {
  string: (c) => <CopySpan className="type-string">{String(c)}</CopySpan>,
  number: (c) => <CopySpan className="type-number">{Number(c)}</CopySpan>,
  float: (c) => <CopySpan className="type-float">{Number(c)}</CopySpan>,
  integer: (c) => <CopySpan className="type-number">{Number(c)}</CopySpan>,
  boolean: (c) => <span className="type-boolean">{String(c)}</span>,
  datetime: (c) => (
    <CopySpan className="type-date">{dayjs(c).format('YYYY-DD-MM HH:mm:ss')}</CopySpan>
  ),
  Image: (c) => <Image src={c} className='playground-img'></Image>,
  ImageNosKey: (c) => (
    <Image src={nos2url(c)} className='playground-img'></Image>
  ),
  Audio: (c) => <audio src={c} controls
    style={{ width: '100%' }}
  ></audio>,
  AudioNosKey: (c) => (
    <audio
      controls
      style={{ width: '100%' }}
      src={nos2url(c)}
    ></audio>
  ),
  AudioNosKeyObject: (c) => {
    console.log('AudioNosKeyObject', c);
    return (
    <div>
      <Flex gap={6} align='center'>
        <Typography.Text strong>{c.name}</Typography.Text><Typography.Text type="secondary">{c.desc}</Typography.Text>
      </Flex>
      <audio
        controls
        style={{ width: '100%' }}
        src={nos2url(c.noskey)}
      ></audio>
    </div>
  )
},
  Video: (c) => <video src={c} controls width="100%" style={{ maxWidth: '300px' }}></video>,
  VideoNosKey: (c) => (
    <video
      controls
      width="100%"
      style={{ maxWidth: '300px' }}
      src={nos2url(c)}
    ></video>
  ),
  Markdown: (c) => {
    return <Markdown content={c} />;
  },
  object: (c) => {
    if (c && typeof c === 'object') {
      return (
        <ReactJson
          theme="summerfruit:inverted"
          name={false}
          displayDataTypes={false}
          iconStyle="square"
          collapsed={2}
          src={c}
        />
      );
    }
    return 'null';
  },
};