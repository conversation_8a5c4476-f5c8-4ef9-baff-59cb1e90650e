// @ts-nocheck
import { useRequest } from 'ahooks';
import { message, Spin } from 'antd';
import jwt_decode from 'jwt-decode';
import { useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';

import { ServiceAPI } from '@/api/service';
import { TokenApi } from '@/api/token';
import { Markdown } from '@/components/Markdown';
import { AgentParamsRender } from '@/components/agent-params-render/complete';
import { useQuery } from '@/hooks/useQuery';
import { AppDetailModel } from '@/interface';
import { CompletionStreamResponse } from '@/interface/conversation';
import { copyText, getFileData, getPopoFileData, getToken, saveToken } from '@/utils/common';
import { OpenAiAPI } from '@/api/conversation';
import { isModelSupportStream } from '@/utils/model-helper';
import { CopyOutlined, RightOutlined } from '@ant-design/icons';

let completeCache = '';
let reasoningCache = '';

interface CompletionProps {
  app: AppDetailModel;
  internal?: boolean;
  style?: React.CSSProperties;
}

export const Completion = (props: CompletionProps) => {
  const { app: appDetail, style = {}, internal = false } = props;
  const [completeRes, setCompleteRes] = useState<string>('');
  const [reasoning, setReasoning] = useState<string>('');
  const [completeLoading, setCompleteLoading] = useState(false);
  const [reasoningExpanded, setReasoningExpanded] = useState(true);
  const supportStream = isModelSupportStream(appDetail.config)

  const onMessage = useCallback(
    (d: CompletionStreamResponse) => {
      if ((d.content || d.reasoning_content) && !d.toolCalls) {
        completeCache = completeCache + d.content;
        reasoningCache = reasoningCache + (d.reasoning_content || '');
        setCompleteRes(completeCache);
        setReasoning(reasoningCache);
      }
    },
    [completeRes],
  );

  async function onRun(params: any) {
    setReasoning('');
    completeCache = '';
    reasoningCache = '';
    setCompleteRes('');
    setCompleteLoading(true);
    const { images, ...rest } = params;
    for (const k in rest) {
      if (typeof rest[k] === 'object' && rest[k].renderType === 'file') {
        rest[k] = await getFileData(rest[k].key);
      }
      if (typeof params[k] === 'object' && params[k].renderType === 'popo-link') {
        rest[k] = await getPopoFileData(rest[k].link);
      }
    };

    if (internal) {
      OpenAiAPI.completion({
        appID: appDetail?.id!,
        parameters: rest,
        images,
        responseMode: supportStream ? 'streaming' : 'json',
        onMessage,
        onFinish: () => {
          setCompleteLoading(false);
        },
      })
    } else {
      ServiceAPI.completion({
        // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
        appID: appDetail?.id!,
        parameters: rest,
        images,
        responseMode: supportStream ? 'streaming' : 'json',
        onMessage,
        onFinish: () => {
          setCompleteLoading(false);
        },
      });
    }

  }

  return (
    <Container style={style}>
      <Left>
        <div className="app-name">{appDetail?.name}</div>
        <div className="app-desc">{appDetail?.description}</div>
        <div className="app-params">
          <AgentParamsRender
            paramsInPrompt={appDetail.config.paramsInPrompt!}
            onCommit={onRun}
          />
        </div>
      </Left>
      <Right>
        <div className="title">生成结果 <CopyOutlined style={{ marginLeft: 5, fontSize: 18, color: '#aaa' }} onClick={() => {
          copyText(completeRes);
        }} /></div>
        <div className="content-container">
          {(reasoning || completeRes) ? (
            <div className="res">
              {reasoning && (
                <>
                  <div
                    className="reasoning-header"
                    onClick={() => setReasoningExpanded(!reasoningExpanded)}
                  >
                    <RightOutlined className={reasoningExpanded ? 'expanded' : ''} />
                    <span>推理过程</span>
                  </div>
                  {reasoningExpanded && (
                    <div className="reasoning-text">
                      {completeLoading && !reasoning ? <Spin /> : <Markdown content={reasoning} />}
                    </div>
                  )}
                </>
              )}
              {completeLoading && !completeRes ? <Spin /> : <Markdown content={completeRes} />}
            </div>
          ) : (
            <div className="empty">
              {completeLoading ? <Spin /> : <span>AI 会在此处响应你</span>}
            </div>
          )}
        </div>
      </Right>
    </Container>
  );
}

export function CompletionPreview() {
  const [loading, setLoading] = useState(true);
  const [appDetail, setAppDetail] = useState<AppDetailModel | null>(null);

  const { parsedQuery } = useQuery();
  const { appId } = parsedQuery;

  const [token, setToken] = useState<string>();

  const { run: createToken } = useRequest(() => TokenApi.createAppTempToken(appId!), {
    manual: true,
    onSuccess: (res) => {
      saveToken(appId!, res.token);
      setToken(res.token);
    },
  });

  if (!appId || typeof appId !== 'string') {
    message.error('请检查页面URL: 缺少应用ID');
  }

  useEffect(() => {
    const t = getToken(appId);
    let shouldRecreate = false;

    try {
      const decoded_token: any = jwt_decode(t ?? '');
      const expire = new Date(decoded_token.exp * 1000);
      const now = new Date();
      const expectedExpire = new Date(now.getTime() + 1000 * 60 * 60 * 24 * 7);
      if (expire < expectedExpire) {
        shouldRecreate = true;
      }
    } catch (e) {
      console.warn(e);
      // window.corona.warn('token err', e);
      shouldRecreate = true;
    }
    if (shouldRecreate) {
      createToken();
    } else {
      setToken(t!);
    }
  }, [appId]);

  useRequest(() => ServiceAPI.getAppDetail(appId!), {
    ready: token !== undefined,
    refreshDeps: [token],
    onSuccess: (res) => {
      setAppDetail(res);
    },
    onFinally: () => {
      setLoading(false);
    },
  });

  if (loading) {
    return (
      <>
        <Spin />
      </>
    );
  }

  return <Completion app={appDetail!} />;
}

export const Container = styled.div`
  display: flex;
  flex-direction: row;
  height: 100vh;
  box-sizing: border-box;
`;

export const Left = styled.div`
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  background-color: white;
  width: 700px;
  padding: 20px;
  height: 100%;
  box-sizing: border-box;

  .app-name {
    font-size: 24px;
    font-weight: bold;
  }

  .app-desc {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
  }

  .app-params {
    border-top: 1px solid #eee;
    margin-top: 20px;
    padding-top: 20px;
  }
`;

export const Right = styled.div`
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  display: flex;
  flex-direction: column;
  background-color: #ebedf3;
  flex-grow: 1;
  min-width: 400px;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
.reasoning-text {
    margin-bottom: 10px;
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    font-size: 12px;
    color: #999;
  }
  .reasoning-header {
    user-select: none;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #aaa;
    font-size: 12px;
    margin-bottom: 10px;
    
    .anticon {
      margin-right: 4px;
      transition: transform 0.3s;
      
      &.expanded {
        transform: rotate(90deg);
      }
    }
  }

  .title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .content-container {
    flex: 1;
    overflow-y: auto;
    height: calc(100% - 60px);
  }

  .res {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
  }

  .empty {
    text-align: center;
    margin-top: 300px;
    color: #666;
  }
`;
