import { Button, Flex, Popconfirm, Space, Table } from "antd";
import AddModal from "./add-modal";

const StoryLine = (props) => {

  const { type, app } = props;

  const onDelete = () => {

  };

  const onAdd = () => {

  };

  return <>
    <Flex justify="end">
      <AddModal onChange={onAdd} />
    </Flex>
    <Table
      columns={[
        { dataIndex: 'no', title: '序号' },
        { dataIndex: 'name', title: '章节名' },
        { dataIndex: 'status', title: '状态' },
        { dataIndex: 'updatedAt', title: '更新时间' },
        {
          dataIndex: 'operate', title: '操作',
          render: (val, rec) => {
            return <>
              <Button type="link" size="small">进入</Button>
              <Button type="link" size="small">编辑</Button>
              <Popconfirm title="确认删除？"
                onConfirm={onDelete}>
                <Button type="link" size="small" danger>删除</Button>
              </Popconfirm>
              <Button type="link" size="small">上线</Button>
              <Button type="link" size="small">复制链接</Button>
            </>
          }
        },
      ]}
      dataSource={[
        { no: 1, name: '第一章：旧梦初醒', status: '待上线', updatedAt: '2025-05-01 14:14:14' }
      ]}
    />
  </>
}

export default StoryLine;
