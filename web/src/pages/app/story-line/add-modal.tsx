import { Button, Form, Input, Modal } from "antd";
import { useState } from "react";

const AddModal = (props) => {
  const { onChange } = props;
  const [open, setOpen] = useState(false);

  const [form] = Form.useForm();

  const onCancel = () => {
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();
    onChange?.();
    setOpen(false);
  };

  const onOpen = () => {
    form.resetFields();
    setOpen(true);
  }

  return <>
    <Button type="primary" onClick={onOpen}>新建章节</Button>
    <Modal title="新建章节" open={open} onCancel={onCancel} onOk={onOk}>
      <Form form={form}>
        <Form.Item label="章节名" name="name" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  </>
};

export default AddModal;
