import {
  AppstoreAddOutlined,
  BookOutlined,
  DashboardOutlined,
  MessageOutlined,
  NodeExpandOutlined,
  SettingOutlined,
  SketchOutlined,
  UsergroupAddOutlined,
} from '@ant-design/icons';

import MemberPage from '@/components/member';
import { IAppType, MemberOfResource } from '@/interface';

import { createMenuPage, MenuItemConfig } from '../../components/pageLayout';
import { DevPage } from './components/agent-dev';
import { OverviewPage } from './components/overview';
import { ResourcePage } from './components/resource/index';
import { AppSettingPage } from './components/setting';
import AppMonitor from '../monitor/app-monitor';
import EvaluateTask from '../evaluate-task';
import CollectionDrawer from '../evaluate-task/create';
import EvaluateTaskDetail from '../evaluate-task/detail';
import queryString from 'querystring';

export const appMarketMenuConfig = [
  {
    title: '开发',
    path: 'dev',
    Icon: NodeExpandOutlined,
    element: <DevPage />,
  },
  {
    title: '日志',
    path: 'overview',
    Icon: DashboardOutlined,
    element: <OverviewPage />,
  },
  {
    title: '成员',
    path: 'member',
    Icon: UsergroupAddOutlined,
    element: <MemberPage resType={MemberOfResource.App} />,
  },
  {
    title: '设置',
    path: 'setting',
    Icon: SettingOutlined,
    element: <AppSettingPage />,
  },
] as MenuItemConfig[];

export const appMenuConfig = [
  {
    title: '开发',
    path: 'dev',
    Icon: NodeExpandOutlined,
    element: <DevPage />,
  },
  {
    title: '资产',
    path: 'resource',
    Icon: SketchOutlined,
    element: <ResourcePage />,
  },
  {
    title: '日志',
    path: 'overview',
    Icon: MessageOutlined,
    element: <OverviewPage />,
  },
  {
    title: '监控',
    path: 'monitor',
    Icon: DashboardOutlined,
    element: <AppMonitor />,
    admin: ['app']
  },
  {
    title: '成员',
    path: 'member',
    Icon: UsergroupAddOutlined,
    element: <MemberPage resType={MemberOfResource.App} />,
  },
  // {
  //   title: '模型绑定',
  //   path: 'model',
  //   Icon: AppstoreAddOutlined,
  //   element: <GroupAppModelProvider />,
  // },
  {
    title: '设置',
    path: 'setting',
    Icon: SettingOutlined,
    element: <AppSettingPage />,
  },
  {
    title: '评测',
    path: 'evaluate-task',
    Icon: BookOutlined,
    // matchPaths: ['evaluate-task/detail/:evaluationTaskId'],
    element: <EvaluateTask />,
    hideMenu: (search) => {
      const searchMap = queryString.parse(search) || {};
      // @ts-ignore
      if ([IAppType.VirtualHuman,IAppType.Workflow].includes(searchMap.type)) {
        return true;
      }

      return search.includes('evaluator');
    }
  },
  {
    title: '评测任务详情',
    path: 'evaluate-task/detail/:evaluationTaskId',
    element: <EvaluateTaskDetail />,
    hideMenu: () => true,
    hide: true
  },
  {
    title: '新建评测',
    path: 'evaluate-task/create',
    Icon: BookOutlined,
    element: <CollectionDrawer />,
    hide: true,
    hideMenu: () => true
  }
] as MenuItemConfig[];

export function createAppMenuPage() {
  // 在此函数中不能使用 useGlobalState 等 hooks， 否则会导致路由切换异常 (这是一个辅助函数，非 React Component)

  return createMenuPage({
    path: 'app',
    menuConfig: appMenuConfig,
    retainQueries: ['workspaceId', 'groupId', 'appId', 'type'],
  });
}
