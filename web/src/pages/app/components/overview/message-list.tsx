import { Table, Input, Space, Typography, Modal, Button, message, Tooltip, Image } from 'antd';
import { useRequest } from 'ahooks';
import { useState } from 'react';
import { OpenAiAPI } from '../../../../api/conversation';
import { CopyOutlined, LeftOutlined, DownloadOutlined } from '@ant-design/icons';
import { copyText } from '@/utils/common';
import dayjs, { Dayjs } from 'dayjs';
import { IAppType } from '@/interface';
import ReactJson from 'react-json-view';
interface MessageListProps {
  appName: string;
  appId: string;
  dateRange: {
    start: Dayjs;
    end: Dayjs;
  };
  setShowMessages: (show: boolean) => void;
  appType?: string;
}

const { Paragraph } = Typography;

interface DetailModalProps {
  visible: boolean;
  title: string;
  content: string;
  onClose: () => void;
}

interface MessageContent {
  type: 'text' | 'image_url' | 'toolCall';
  text?: string;
  image_url?: {
    url: string;
  };
}

const isJsonString = (str: string) => {
  try {
    const result = JSON.parse(str);
    return Array.isArray(result);
  } catch (e) {
    return false;
  }
};

const renderPreviewContent = (content: string, record?: any) => {
  console.log('record...', record);
  if (record?.toolCalls && record.toolCalls.length > 0) {
    // return record.toolCalls.map((toolCall: any) => toolCall.response).join(',');
    return <a>工具调用返回</a>
  }
  if (!isJsonString(content)) return content;

  const items = JSON.parse(content) as MessageContent[];
  return items.map((item, index) => {
    if (item.type === 'text') return item.text;
    if (item.type === 'image_url') return '[图片]';
    return '';
  }).join(' ');
};

const renderDetailContent = (content: string) => {
  if (!isJsonString(content)) {
    return <div>{content}</div>;
  }

  const items = JSON.parse(content) as MessageContent[];
  console.log('content...', items);
  let currentGroup: React.ReactNode[] = [];
  const groups: React.ReactNode[] = [];

  items.forEach((item, index) => {
    if (item.type === 'text') {
      if (currentGroup.length > 0) {
        groups.push(
          <div key={`group-${groups.length}`} style={{ display: 'flex', gap: 8 }}>
            {currentGroup}
          </div>
        );
        currentGroup = [];
      }
      groups.push(<div key={`text-${index}`}>{item.text}</div>);
    } else if (item.type === 'toolCall') {
      currentGroup.push(
        <div key={`toolCall-${index}`}>
          <ReactJson src={JSON.parse(item.text)}   displayDataTypes={false} />
        </div>
      );
    } else if (item.type === 'image_url') {
      currentGroup.push(
        <Image
          key={`image-${index}`}
          src={item.image_url?.url}
          width={100}
          style={{ objectFit: 'cover' }}
        />
      );
    }
  });

  // 处理最后一组未处理的图片
  if (currentGroup.length > 0) {
    groups.push(
      <div key={`group-${groups.length}`} style={{ display: 'flex', gap: 8 }}>
        {currentGroup}
      </div>
    );
  }

  return <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>{groups}</div>;
};

const DetailModal = ({ visible, title, content, onClose }: DetailModalProps) => {
  console.log('content...', content);
  const copy = async () => {
    try {
      copyText(content);
    } catch (err) {
      message.error('复制失败');
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span>{title}</span>
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={copy}
          />
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <div style={{
        maxHeight: '60vh',
        overflowY: 'auto',
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-all'
      }}>
        {renderDetailContent(content)}
      </div>
    </Modal>
  );
};

interface ExportButtonProps {
  total: number;
  onExport: (page?: number, pageSize?: number) => void;
}

const ExportButton = ({ total, onExport }: ExportButtonProps) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPage, setSelectedPage] = useState(1);
  const pageSize = 1000;
  const totalPages = Math.ceil(total / pageSize);

  const handleClick = () => {
    if (total > pageSize) {
      setModalVisible(true);
    } else {
      onExport(1, pageSize);
    }
  };

  return (
    <>
      <Button
        type="primary"
        icon={<DownloadOutlined />}
        onClick={handleClick}
      >
        导出Excel
      </Button>

      <Modal
        title="分批导出数据"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="export"
            type="primary"
            onClick={() => {
              onExport(selectedPage, pageSize);
              setModalVisible(false);
            }}
          >
            确认导出
          </Button>
        ]}
      >
        <div>
          <p>当前数据共 {total} 条，每批次最多导出 {pageSize} 条数据。</p>
          <p>请选择要导出第几批数据：</p>
          <div style={{ marginTop: 16 }}>
            <Input.Group compact>
              <Input
                type="number"
                min={1}
                max={totalPages}
                value={selectedPage}
                onChange={(e) => setSelectedPage(Number(e.target.value))}
                style={{ width: 100 }}
              />
              <Button disabled>/ {totalPages}</Button>
            </Input.Group>
          </div>
        </div>
      </Modal>
    </>
  );
};

export const MessageList = ({ appId, dateRange, setShowMessages, appType, appName }: MessageListProps) => {
  const [pageSize, setPageSize] = useState(10);
  const [current, setCurrent] = useState(1);
  const [conversationId, setConversationId] = useState('');
  const [modalConfig, setModalConfig] = useState<{
    visible: boolean;
    title: string;
    content: string;
  }>({
    visible: false,
    title: '',
    content: '',
  });
  const [searchValue, setSearchValue] = useState('');
  const [userSearchValue, setUserSearchValue] = useState('');
  const [userId, setUserId] = useState('');

  const handleExport = async (page = 1, pageSize = 5000) => {
    try {
      const params = new URLSearchParams();
      if (conversationId) {
        params.append('conversationId', conversationId);
      }
      if (dateRange?.start) {
        params.append('start', dateRange.start.startOf('day').format('YYYY-MM-DD HH:mm:ss'));
      }
      if (dateRange?.end) {
        params.append('end', dateRange.end.endOf('day').format('YYYY-MM-DD HH:mm:ss'));
      }
      if (page) {
        params.append('pageNumber', page.toString());
        params.append('pageSize', pageSize.toString());
      }
      if (userId) {
        params.append('userId', userId);
      }
      params.append('appName', appName);

      const response = await fetch(
        `/api/v1/app/${appId}/messages/export?${params.toString()}`,
        {
          method: 'GET',
        }
      );

      if (!response.ok) {
        throw new Error('导出失败');
      }

      // 获取文件名
      const contentDisposition = response.headers.get('content-disposition');
      let fileName = contentDisposition
        ? contentDisposition.split('filename=')[1].replace(/"/g, '')
        : `messages_${appId.slice(0, 5)}.xlsx`;

      // 下载文件
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = decodeURIComponent(fileName);
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('导出成功');
    } catch (error) {
      console.error('Export error:', error);
      message.error('导出失败');
    }
  };

  const { data, loading } = useRequest(
    () => OpenAiAPI.listAppMessages(appId, {
      pageSize,
      pageNumber: current,
      conversationId,
      userId,
      start: dateRange?.start?.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      end: dateRange?.end?.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
    }),
    {
      refreshDeps: [pageSize, current, conversationId, userId, dateRange],
    }
  );

  console.log('data...', data);

  const columns = [
    {
      title: '问题',
      dataIndex: 'query',
      key: 'query',
      width: '20%',
      render: (text: string, record: any) => (
        <Paragraph
          ellipsis={{
            rows: 2,
            expandable: false,
          }}
          style={{
            marginBottom: 0,
            cursor: 'pointer'
          }}
          onClick={() => setModalConfig({
            visible: true,
            title: '问题详情',
            content: text
          })}
        >
          {renderPreviewContent(text)}
        </Paragraph>
      ),
    },
    {
      title: '回答',
      dataIndex: 'response',
      key: 'response',
      width: '20%',
      render: (text: string, record: any) => (
        <Paragraph
          ellipsis={{
            rows: 2,
            expandable: false,
          }}
          style={{
            marginBottom: 0,
            cursor: 'pointer'
          }}
          onClick={() => setModalConfig({
            visible: true,
            title: '回答详情',
            content: text || JSON.stringify(record?.toolCalls?.map((toolCall: any) => ({
              type: 'toolCall',
              text: toolCall.response
            })))
          })}
        >
          {renderPreviewContent(text, record)}
        </Paragraph>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    appType !== IAppType.VirtualHuman && {
      title: '会话ID',
      dataIndex: 'conversationID',
      key: 'conversationID',
      render: (text: string) => (
        <a onClick={(e) => {
          e.preventDefault();
          setSearchValue(text);
          setConversationId(text);
          setCurrent(1);
        }}>
          {text}
        </a>
      ),
    },
    {
      title: '模型',
      dataIndex: 'modelName',
      key: 'modelName',
    },
    {
      title: '耗时',
      dataIndex: 'time_comsumption_in_ms',
      key: 'time_comsumption_in_ms',
      render: (time: number) => `${(time / 1000).toFixed(2)}s`,
    },
    {
      title: 'Token',
      dataIndex: 'total_tokens',
      key: 'total_tokens',
    },
    {
      title: '费用',
      dataIndex: 'fee',
      key: 'fee',
      render: (fee: number) => `¥${fee.toFixed(4)}`,
    },
    {
      title: '用户',
      dataIndex: 'creator',
      key: 'creator',
      render: (_, record) => (
        <Tooltip title={record.createdBy?.email || record.createdBy}>
          <a onClick={(e) => {
            e.preventDefault();
            setUserSearchValue(record.createdBy?.id || record.createdBy);
            setUserId(record.createdBy?.id || record.createdBy);
            setCurrent(1);
          }}>
            {record.createdBy?.name || record.createdBy}
          </a>
        </Tooltip>
      ),
    },
  ].filter(Boolean);

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column', flex: 1 }}>
      <div style={{
        display: 'flex',
        flex: 1,
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16
      }}>
        {/* <Button
          type="link"
          icon={<LeftOutlined />}
          onClick={() => setShowMessages(false)}
        >
          返回概览
        </Button> */}
        <Space>
          {appType !== IAppType.VirtualHuman && (
            <Input.Search
              placeholder="搜索会话ID"
              value={searchValue}
              onChange={e => setSearchValue(e.target.value)}
              onSearch={value => {
                setConversationId(value);
                if (!value) {
                  setCurrent(1);
                }
              }}
              allowClear
              style={{ width: 200 }}
            />
          )}
          <Input.Search
            placeholder="搜索用户ID"
            value={userSearchValue}
            onChange={e => setUserSearchValue(e.target.value)}
            onSearch={value => {
              setUserId(value);
              if (!value) {
                setCurrent(1);
              }
            }}
            allowClear
            style={{ width: 200 }}
          />
          <ExportButton
            total={data?.total || 0}
            onExport={handleExport}
          />
        </Space>
      </div>
      <Table
        columns={columns}
        dataSource={data?.items}
        loading={loading}
        pagination={{
          total: data?.total,
          pageSize,
          current,
          onChange: (page, pageSize) => {
            setCurrent(page);
            setPageSize(pageSize);
          },
        }}
        style={{ flex: 1 }}
      />
      <DetailModal
        visible={modalConfig.visible}
        title={modalConfig.title}
        content={modalConfig.content}
        onClose={() => setModalConfig(prev => ({ ...prev, visible: false }))}
      />
    </div>
  );
};
