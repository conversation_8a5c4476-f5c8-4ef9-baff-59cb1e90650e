import { Area, AreaConfig } from '@ant-design/charts';
import { useMap, useRequest, useSafeState } from 'ahooks';
import { <PERSON><PERSON>, Card, Popover } from 'antd';
import { styled } from 'styled-components';

import { MetricApi } from '@/api/metric';
import { useQuery } from '@/hooks/useQuery';
import { useGlobalState } from '@/hooks/useGlobalState';
import { ReactNode, useEffect, useMemo } from 'react';
import { DefaultModelApi } from '@/api/model-provider';
import { QuestionCircleOutlined } from '@ant-design/icons';

interface MetricChartProps {
  appID: string;
  start?: Date;
  data?: any;
  end?: Date;
}

interface MessageDetailProps extends MetricChartProps {
  onViewDetail: () => void;
}

interface IAreaChartProps extends AreaConfig {
  title: string | ReactNode;
  description: string;
  color: string;
  data: { date: string; count: number }[];
}

const ChartTitle = styled.div`
  font-size: 15px;
  font-weight: bold;
  color: #434141;
`;

const ChartDescription = styled.div`
  font-size: 11px;
  color: #797575;
`;

const AreaChart = (props: IAreaChartProps) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { title, description, color, data, ...restConfig } = props;

  if (data.length === 0) {
    data.push({
      date: new Date().toISOString().split('T')[0],
      count: 0,
    });
  }

  const config: AreaConfig = {
    renderer: 'svg',
    areaStyle: {
      fill: `l(270) 0:#ffffff 1:${color}`,
    },
    data,
    ...restConfig,
  };

  return (
    <Card>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: '300px',
        }}
      >
        <ChartTitle
          style={{
            margin: '20px',
            marginRight: '0px',
            marginBottom: '0px',
          }}
        >
          {title}
        </ChartTitle>
        <ChartDescription
          style={{
            marginLeft: '20px',
            marginBottom: '12px',
          }}
        >
          {description}
        </ChartDescription>
        <div style={{ width: '100%', height: '100%' }}>
          <Area {...config} />
        </div>
      </div>
    </Card>
  );
};

export const Messages = (props: MessageDetailProps) => {
  const { data, onViewDetail } = props;

  const config: IAreaChartProps = {
    title: <span>每日对话数<Button type="link" style={{
      float: 'right',
      fontWeight: 'normal',
      fontSize: 12,
    }} onClick={onViewDetail}>查看对话详情</Button></span>,
    description: '每日对话数，该指标反映了用户使用频率。',
    data: data ?? [],
    meta: {
      date: {
        range: [0, 1],
      },
    },
    yField: 'messages',
    xField: 'date',
    smooth: true,
    color: '#1890ff',
  };

  return <AreaChart {...config} />;
};

export const UserUsageCount = (props: MetricChartProps) => {
  const { data } = props;

  const config: IAreaChartProps = {
    title: '每日用户数',
    description: '每天使用过该应用的用户数，该指标反映该应用的流行程度。',
    data: data ?? [],
    meta: {
      date: {
        range: [0, 1],
      },
    },
    yField: 'users',
    xField: 'date',
    smooth: true,
    color: '#1890ff',
  };

  return <AreaChart {...config} />;
};

export const TimeConsume = (props: MetricChartProps) => {
  const { data } = props;
  // const newData = (data || []).map(v => ({ ...v, times: (v.total_tokens / v.token_per_second / v.total_messages) }))

  const config: IAreaChartProps = {
    title: '对话平均耗时(ms)',
    description: '每次对话的耗时情况',
    data: data ?? [],
    meta: {
      date: {
        range: [0, 1],
      },
    },
    yField: 'time',
    xField: 'date',
    smooth: true,
    color: '#1890ff',
  };

  return <AreaChart {...config} />;
};

export const TokensPerSeconds = (props: MetricChartProps) => {
  const { data } = props;

  const config: IAreaChartProps = {
    title: '每秒返回Token数',
    description: '每秒从LLM返回的Token数量，该指标反映了LLM的性能。',
    data: data ?? [],
    meta: {
      date: {
        range: [0, 1],
      },
    },
    yField: 'token_per_second',
    xField: 'date',
    smooth: true,
    color: '#1890ff',
  };

  return <AreaChart {...config} />;
};

export const TokenCount = (props: MetricChartProps) => {
  const { data } = props;
  const { globalState } = useGlobalState();
  const { app } = globalState;
  const [modelList, setModelList] = useSafeState([]);

  const newData = (data || []).map(v => ({ ...v, tokens: (v.total_tokens / 1000) }))

  const { parsedQuery } = useQuery();

  useEffect(() => {
    DefaultModelApi.listGlobalModel({
      workspaceId: parsedQuery.workspaceId as string,
      groupId: parsedQuery.groupId as string
    }).then((res) => {
      setModelList(res);
    });
  }, [parsedQuery.workspaceId, parsedQuery.groupId])

  const fee = useMemo(() => {
    if (!data || data?.length === 0) {
      return 0;
    }
    const find = modelList.find(v => v.name === app?.config?.modelName)
    let avgFee = 0;
    if (find) {
      avgFee = find.fee.avg
    }
    const sum = data.reduce((pre, cur) => pre + cur.total_tokens * avgFee, 0);
    return sum / 1000000;
  }, [modelList, app, data])


  console.log('model', modelList, app, fee)

  const config: IAreaChartProps = {
    // title: <span>每日Token用量(K) <span style={{
    //   float: 'right',
    //   fontWeight: 'normal',
    //   fontSize: 12,
    //   color: '#ba2626de'
    // }}>预估费用: ¥{fee.toFixed(2)} <Popover title="目前预估成本是通过当前模型和token用量估算的，后续会基于每次对话，做更精确的成本估算"><QuestionCircleOutlined></QuestionCircleOutlined></Popover></span></span>,
    title: '每日Token用量(K)',
    description: '每天该应用消耗的Token数量，根据此计算成本',
    data: newData,
    meta: {
      date: {
        range: [0, 1],
      },
    },
    yField: 'tokens',
    xField: 'date',
    smooth: true,
    color: '#1890ff',
  };

  return <AreaChart {...config} />;
};

export const TokenPerMessage = (props: MetricChartProps) => {
  const { data } = props;

  const config: IAreaChartProps = {
    title: '平均token',
    description:
      '每个对话的平均token数，主要用来优化成本',
    data: data ?? [],
    meta: {
      date: {
        range: [0, 1],
      },
    },
    yField: 'token_per_message',
    xField: 'date',
    smooth: true,
    color: '#1890ff',
  };

  return <AreaChart {...config} />;
};

export const Conversations = (props: MetricChartProps) => {
  const { data } = props;
  const newData = (data || []).map(v => ({ ...v, messagesPerConv: (v.messages ?? 0) / (v.conversations || 1) }))
  console.log('new', newData);

  const config: IAreaChartProps = {
    title: '平均对话轮数',
    description:
      '主要是用于评估问答效率，仅在对话型中有意义',
    data: newData ?? [],
    meta: {
      date: {
        range: [0, 1],
      },
    },
    yField: 'messagesPerConv',
    xField: 'date',
    smooth: true,
    color: '#1890ff',
  };

  return <AreaChart {...config} />;
};

export const Cost = (props: MetricChartProps) => {
  const { data } = props;
  const newData = (data || []).map(v => ({...v, cost: Number(v.fee ?? v?.extra?.fee ?? 0)}))
  console.log('new', newData);

  const fee = useMemo(() => {
    if (!data || data?.length === 0) {
      return 0;
    }
    const sum = newData.reduce((pre, cur) => pre + cur.cost, 0);
    return sum;
  }, [newData])

  const config: IAreaChartProps = {
    title: <span>每日成本 <span style={{
      float: 'right',
      fontWeight: 'normal',
      fontSize: 12,
      color: '#ba2626de'
    }}>预估费用: ¥{fee.toFixed(2)} <Popover title="成本估算是通过对话模型和对应token用量估算的"><QuestionCircleOutlined></QuestionCircleOutlined></Popover></span></span>,
    description:
      '每日使用的成本统计',
    data: newData ?? [],
    meta: {
      date: {
        range: [0, 1],
      },
    },
    yField: 'cost',
    xField: 'date',
    smooth: true,
    color: '#1890ff',
  };

  return <AreaChart {...config} />;
};