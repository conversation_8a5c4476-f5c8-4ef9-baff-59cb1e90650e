// @ts-nocheck
import { <PERSON>, Col, DatePicker, Flex, Row, Spin } from 'antd';
import { useRef, useState } from 'react';

import { LayoutContainer } from '@/components/layout-container';
import { useGlobalState } from '@/hooks/useGlobalState';

const { RangePicker } = DatePicker;

import { useRequest } from 'ahooks';
import { Switch, Table } from 'antd';
import type { TimeRangePickerProps } from 'antd/es/date-picker';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import ReactJson from 'react-json-view';

dayjs.extend(customParseFormat);

const isDev = window.location.host.includes('dev');

import { AppApi } from '@/api/app';

interface DataType {
  endTime: any;
  startTime: any;
  inputs: object;
  outputs: object;
  message: string;
  status: any;
  key: React.Key;
  name: string;
  age: number;
  address: string;
  description: string;
}

import { useQuery } from '@/hooks/useQuery';
import { PreviewButton } from '@music/ct-langbase';
import { proxyApi } from '@/api/proxy';

export function WorkflowLogPage(props) {
  const { appID, workflowId } = props;
  return <History appID={appID} workflowId={workflowId}></History>;
}

const rangePresets: TimeRangePickerProps['presets'] = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs()] },
  { label: '最近1小时', value: [dayjs().add(-1, 'h'), dayjs()] },
  { label: '最近4小时', value: [dayjs().add(-4, 'h'), dayjs()] },
  { label: '最近两天', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '最近三天', value: [dayjs().add(-2, 'd'), dayjs()] },
];


export function History(props) {
  const { appID, workflowId } = props;
  const [date, setDate] = useState([]);
  const [range, setRange] = useState([]);
  const [debug, setDebug] = useState(false);
  const [current, setCurrent] = useState(1);
  const { debug: isDebug } = useQuery();
  const focusRef = useRef();

  const { data: daysData } = useRequest(() => {
    // 开始时间是今年年初的时间戳，结束时间是今天的时间戳
    return proxyApi.getTaskDays({
      endTime: dayjs().endOf('day').valueOf(),
      limit: 20,
      startTime: dayjs().subtract(3, 'month').valueOf(),
      workflowCode: workflowId + (debug ? '_debug' : ''),
    });
  }, {
    refreshDeps: [workflowId, debug],
  });

  const { data: res, run } = useRequest(
    (page = 1) => {
      setCurrent(page);
      return AppApi.getWorkFlowRunList(
        appID,
        debug,
        range[0] ? range[0].valueOf() : undefined,
        range[1] ? range[1].valueOf() : undefined,
        10,
        page,
      );
    },
    {
      refreshDeps: [appID, range, debug],
    },
  );


  const cellRender: DatePickerProps<Dayjs>['cellRender'] = (current, info) => {
    if (info.type !== 'date') {
      return info.originNode;
    }
    if (typeof current === 'number' || typeof current === 'string') {
      return <div className="ant-picker-cell-inner">{current}</div>;
    }
    const date = current.format('YYYY-MM-DD');
    if (daysData?.days.includes(date)) {
      return <div className="ant-picker-cell-inner red-dot">{current.date()}</div>;
    }
    return <div className="ant-picker-cell-inner">{current.date()}</div>;
  };


  const { data: histories } = useRequest(
    () => {
      return AppApi.getAppHistory(appID);
    },
    {
      refreshDeps: [appID],
    },
  );

  const columns: ColumnsType<DataType> = [
    {
      title: 'runId',
      dataIndex: 'runId',
      key: 'runId',
    },
    {
      title: 'workflowId',
      dataIndex: 'flowId',
      key: 'flowId',
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (v) => dayjs(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      render: (v) => {
        if (v) {
          return dayjs(v).format('YYYY-MM-DD HH:mm:ss');
        }
        return '--';
      },
    },
    {
      title: '耗时',
      dataIndex: 'endTime',
      key: 'endTime',
      render: (_, record) => {
        if (record.endTime) {
          return `${record.endTime - record.startTime}ms`;
        }
        return '--';
      },
    },
    { title: '状态', dataIndex: 'status', key: 'status', width: 20 },
  ];

  const disabledDate = (current: Dayjs) => {

    if (!focusRef.current || !date) return;

    if (focusRef.current === 'start') {
      const tooEarly = date[1] && date[1].diff(current, 'days') >= 2;
      return !!tooEarly;

    }

    if (focusRef.current === 'end') {
      const tooLate = date[0] && current.diff(date[0], 'days') >= 3;
      return !!tooLate;
    }
    return false;
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      setDate([null, null]);
    } else {
      setDate(null);
    }
  };

  let env = isDev ? 'dev' : 'online';
  if (window.location.href.includes('onlinetest')) {
    env = 'test'
  }
  const aioBase = env === 'online' ? 'https://music-cms.hz.netease.com' : 'https://cms.qa.igame.163.com'

  return (
    <Card style={{ userSelect: 'text' }}>
      <div style={{ marginBottom: '20px', width: '100%' }}>
        <h3 style={{ display: 'inline', marginRight: '10px' }}>运行记录</h3>
      </div>
      <Flex justify="space-between" align="center">
        <RangePicker
          showTime
          value={range}
          presets={rangePresets}
          onCalendarChange={(r, dateStrings, info) => {
            if (r) {
              setDate(r);
            }
          }}
          onChange={(r) => {
            if (r) {
              setRange(r);
            }
          }}
          cellRender={cellRender}
          // onOpenChange={onOpenChange}
          disabledDate={disabledDate}
          renderExtraFooter={() => <div>有任务记录的日期会用右上角<span className="red-dot">红点</span>标明</div>}
          onFocus={(e, info) => {
            focusRef.current = info?.range;
          }}
        />
        <Switch
          style={{ marginLeft: '10px' }}
          checkedChildren="正式"
          unCheckedChildren="调试"
          defaultChecked
          onChange={(v) => setDebug(!v)}
        />
      </Flex>
      <Table
        rowKey={'runId'}
        style={{ marginTop: '10px' }}
        columns={columns}
        pagination={{ current, total: res?.total || 1, onChange: run }}
        expandable={{
          expandedRowRender: (record) => (
            <Row>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>输入</h3>
                <ReactJson
                  theme="summerfruit:inverted"
                  name={false}
                  style={{
                    maxWidth: '20vw',
                  }}
                  displayDataTypes={false}
                  onSelect={(v) => console.log('v', v)}
                  iconStyle="square"
                  collapsed={2}
                  src={record.inputs}
                />
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>输出</h3>
                <ReactJson
                  theme="summerfruit:inverted"
                  name={false}
                  style={{
                    maxWidth: '20vw',
                  }}
                  displayDataTypes={false}
                  onSelect={(v) => console.log('v', v)}
                  iconStyle="square"
                  collapsed={2}
                  src={record.outputs}
                />
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>状态</h3>
                <ReactJson
                  theme="summerfruit:inverted"
                  name={false}
                  style={{
                    maxWidth: '20vw',
                  }}
                  displayDataTypes={false}
                  onSelect={(v) => console.log('v', v)}
                  iconStyle="square"
                  collapsed={2}
                  src={{
                    message: record?.message || '',
                    status: record?.status,
                  }}
                />
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>工作流详情</h3>
                <PreviewButton debug={debug} appId={appID} runId={record.runId} env={env}></PreviewButton>
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>调试详情</h3>
                <a target="_blank" href={`${aioBase}/produce-center/workflow/instance/detail?id=${record.runId}`}>AIO详情</a>
              </Col>
            </Row>
          ),
          rowExpandable: (record) => record.name !== 'Not Expandable',
        }}
        dataSource={res?.items || []}
      />
    </Card>
  );
}
