// @ts-nocheck
import { RocketOutlined } from '@ant-design/icons';
import { Button, Card, Col, DatePicker, Row, Select, Space, Spin } from 'antd';
import qs from 'querystring';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { useCallback, useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import { LeftOutlined } from '@ant-design/icons';

import { MidContainer } from '@/components/layout-container';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppDetailModel, AppTypeCnMap, IAppType } from '@/interface';
import { ChatflowLogList } from './chatflow-log';

dayjs.extend(customParseFormat);
const { RangePicker } = DatePicker;

import {
  Conversations,
  Cost,
  Messages,
  TokenCount,
  TokenPerMessage,
  TimeConsume,
  UserUsageCount,
} from './charts';
import { WorkflowLogPage } from './workflow-log';
import { useRequest } from 'ahooks';
import { MetricApi } from '../../../../api/metric';
import { MessageList } from './message-list';

export function OverviewPage() {
  const { globalState } = useGlobalState();
  const { app } = globalState;

  if (!app) {
    return <Spin />;
  }
  if (app.type === 'workflow') {
    return <WorkflowLogPage appID={app.id} workflowId={app?.config?.workflowId}></WorkflowLogPage>
  }
  return (
    <MidContainer innerStyle={{ width: '90%' }}>
      <MessageMetrics appID={app.id} appType={app.type} appName={app.name} />
    </MidContainer>
  );
}

interface IOverviewHeaderProps {
  app: AppDetailModel;
}

export function OverviewHeader(props: IOverviewHeaderProps) {
  const { app } = props;

  function onPreviewClick() {
    window.open(`/preview/${app?.type}?appId=${app!.id}`, '_blank');
  }

  return (
    <Card>
      <StyledHeader>
        <div className="app-info">
          <span className="title">{app.name}</span>
          <span className="app-type">{AppTypeCnMap[app.type]}</span>
        </div>
        <div className="app-desc">{app.description}</div>
        <div className="app-preview">
          <Space>
            <Button onClick={onPreviewClick} icon={<RocketOutlined rev={undefined} />}>
              预览
            </Button>
          </Space>
        </div>
        <div className="create-info">
          <span>
            {app.createdBy.name} 创建于 {app.createdAt}
          </span>
        </div>
      </StyledHeader>
    </Card>
  );
}

interface IMetricsProps {
  appID: string;
  appType: IAppType;
  appName: string;
}

interface DateRange {
  start: Date;
  end: Date;
}

export function MessageMetrics(props: IMetricsProps) {
  const { appID, appType, appName } = props;

  const [dateRange, setDateRange] = useState<DateRange>({
    start: dayjs().add(-3, 'd'),
    end: dayjs()
  });

  const onRangeSelect = useCallback((range) => {
    console.log('range', range);
    setDateRange({
      start: range[0],
      end: range[1],
    });
  }, []);

  const rangePresets: TimeRangePickerProps['presets'] = [
    { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
    { label: '最近七天', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '最近半月', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '最近一月', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '最近一季度', value: [dayjs().add(-90, 'd'), dayjs()] },
  ];

  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    // Can not select days before today and today
    return current && current >= dayjs().endOf('day');
  };

  return (
    <Card>
      <div style={{ marginTop: '20px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span style={{ fontSize: 16, fontWeight: 'bold' }}>日志</span>
          <RangePicker
            defaultValue={[dayjs().add(-3, 'd'), dayjs()]}
            placeholder={['', '今天']}
            allowEmpty={[false, true]}
            disabledDate={disabledDate}
            presets={rangePresets}
            onChange={onRangeSelect}
          />
        </div>
      </div>
      {appType === IAppType.Chatflow ? (
        <ChatflowLogList appId={appID} dateRange={dateRange} appType={appType} appName={appName} />
      ) : (
        <MessageList appId={appID} dateRange={dateRange} appType={appType} appName={appName} />
      )}
    </Card>
  );
}

export function Metrics(props: IMetricsProps) {
  const { appID, appType, appName } = props;
  const query = qs.parse(window.location.search.replace('?', ''));
  const debug = query.debug

  const [dateRange, setDateRange] = useState<DateRange>({
    start: dayjs().add(-3, 'd'),
    end: dayjs()
  });

  const { data } = useRequest(() => MetricApi.getMetric(appID, dateRange), {
    refreshDeps: [dateRange, appID]
  })

  const [showMessages, setShowMessages] = useState(true);

  const onViewDetail = useCallback(() => {
    setShowMessages(true);
  }, []);

  console.log('data', data);

  const metrics = useMemo(
    () =>
      [
        <Messages key="messages" data={data} onViewDetail={onViewDetail} />,
        <UserUsageCount key="userUsage" data={data} />,
        <TokenCount key="tokenCount" data={data} />,
        <Cost key="cost" data={data} />,
        // <TokenPerMessage key="tokenPerMessage" data={data} />,
        <TimeConsume key="tokensPerSeconds" data={data} />,
      ].concat([<Conversations key="conversations" data={data}></Conversations>])
        .map((it) => (
          <Col key={`${it.key}-box`} xs={24} sm={24} md={24} lg={12} xl={12} xxl={12}>
            {it}
          </Col>
        )),
    // .filter((_, idx) => !(appType === IAppType.AgentCompletion && idx == 2)),
    [data],
  );

  const onRangeSelect = useCallback((range) => {
    console.log('range', range);
    setDateRange({
      start: range[0],
      end: range[1],
    });
  }, []);

  const rangePresets: TimeRangePickerProps['presets'] = [
    { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
    { label: '最近七天', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '最近半月', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '最近一月', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '最近一季度', value: [dayjs().add(-90, 'd'), dayjs()] },
  ];

  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    // Can not select days before today and today
    return current && current >= dayjs().endOf('day');
  };

  return (
    <Card>
      <div style={{ marginTop: '20px' }}>
        <span style={{ fontSize: 16, fontWeight: 'bold' }}>数据
          <span style={{ marginLeft: 10, color: '#999', fontSize: 12 }}>数据是离线统计的，最多只能看到前一天数据</span>
        </span>
        <RangePicker style={{ float: 'right' }}
          defaultValue={[dayjs().add(-3, 'd'), dayjs()]}
          placeholder={['', '今天']}
          allowEmpty={[false, true]}
          disabledDate={disabledDate}
          presets={rangePresets}
          onChange={onRangeSelect}
        />
      </div>
      <Row style={{ marginTop: '16px', width: '100%', userSelect: 'none' }} gutter={[16, 16]}>
        {showMessages ? (
          <MessageList appId={appID} dateRange={dateRange} setShowMessages={setShowMessages} appType={appType} appName={appName} />
        ) : (
          metrics
        )}
      </Row>
    </Card>
  );
}

export const OverviewContainer = styled.div`
  padding: 0px;
`;

const StyledHeader = styled.div`
  position: relative;
  background-color: #fff;

  .app-info {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .title {
    font-size: 24px;
    font-weight: bold;
  }

  .app-type {
    display: inline-block;
    margin-left: 10px;
    background-color: #146ce6;
    padding: 2px 10px;
    border-radius: 10px;
    line-height: 20px;
    height: 20px;
    color: white;
  }

  .app-desc {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
  }

  .app-preview {
    margin-top: 40px;
  }

  .create-info {
    position: absolute;
    right: 24px;
    bottom: 0;
    font-size: 14px;
  }
`;
