// @ts-nocheck
import { Table, Input, Space, Typography, Modal, Button, message, Tooltip, Image, Card, Steps, Empty } from 'antd';
import { useRequest } from 'ahooks';
import { useState, useEffect } from 'react';
import { OpenAiAPI } from '../../../../api/conversation';
import { CopyOutlined, LeftOutlined, DownloadOutlined } from '@ant-design/icons';
import { copyText } from '@/utils/common';
import dayjs, { Dayjs } from 'dayjs';
import { IAppType } from '@/interface';
import ReactJson from 'react-json-view';
import { ChatflowAPI } from '@/api/chatflow';
import styled from 'styled-components';

interface MessageListProps {
  appName: string;
  appId: string;
  dateRange: {
    start: Dayjs;
    end: Dayjs;
  };
  setShowMessages: (show: boolean) => void;
  appType?: string;
}

const { Paragraph } = Typography;

interface DetailModalProps {
  visible: boolean;
  title: string;
  content: string;
  onClose: () => void;
}

interface MessageContent {
  type: 'text' | 'image_url' | 'toolCall';
  text?: string;
  image_url?: {
    url: string;
  };
}

interface NodeItem {
  id: number;
  nodeId: string;
  nodeName: string;
  nodeCode: string;
  nodeType: string;
  status: string;
  inputContent: string;
  outputContent: string;
  extInfo: string;
  createTime: string;
}

const isJsonString = (str: string) => {
  try {
    const result = JSON.parse(str);
    return Array.isArray(result);
  } catch (e) {
    return false;
  }
};

const renderPreviewContent = (content: string, record?: any) => {
  console.log('record...', record);
  if (record?.toolCalls && record.toolCalls.length > 0) {
    // return record.toolCalls.map((toolCall: any) => toolCall.response).join(',');
    return <a>工具调用返回</a>
  }
  if (!isJsonString(content)) return content;

  const items = JSON.parse(content) as MessageContent[];
  return items.map((item, index) => {
    if (item.type === 'text') return item.text;
    if (item.type === 'image_url') return '[图片]';
    return '';
  }).join(' ');
};

const renderDetailContent = (content: string) => {
  if (!isJsonString(content)) {
    return <div>{content}</div>;
  }

  const items = JSON.parse(content) as MessageContent[];
  console.log('content...', items);
  let currentGroup: React.ReactNode[] = [];
  const groups: React.ReactNode[] = [];

  items.forEach((item, index) => {
    if (item.type === 'text') {
      if (currentGroup.length > 0) {
        groups.push(
          <div key={`group-${groups.length}`} style={{ display: 'flex', gap: 8 }}>
            {currentGroup}
          </div>
        );
        currentGroup = [];
      }
      groups.push(<div key={`text-${index}`}>{item.text}</div>);
    } else if (item.type === 'toolCall') {
      currentGroup.push(
        <div key={`toolCall-${index}`}>
          <ReactJson src={JSON.parse(item.text)} displayDataTypes={false} />
        </div>
      );
    } else if (item.type === 'image_url') {
      currentGroup.push(
        <Image
          key={`image-${index}`}
          src={item.image_url?.url}
          width={100}
          style={{ objectFit: 'cover' }}
        />
      );
    }
  });

  // 处理最后一组未处理的图片
  if (currentGroup.length > 0) {
    groups.push(
      <div key={`group-${groups.length}`} style={{ display: 'flex', gap: 8 }}>
        {currentGroup}
      </div>
    );
  }

  return <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>{groups}</div>;
};

const DetailModal = ({ visible, title, content, onClose }: DetailModalProps) => {
  console.log('content...', content);
  const copy = async () => {
    try {
      copyText(content);
    } catch (err) {
      message.error('复制失败');
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <span>{title}</span>
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={copy}
          />
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <div style={{
        maxHeight: '60vh',
        overflowY: 'auto',
        whiteSpace: 'pre-wrap',
        wordBreak: 'break-all'
      }}>
        {renderDetailContent(content)}
      </div>
    </Modal>
  );
};

export const NodeFlowModal = ({ visible, onClose, nodeList }: {
  visible: boolean;
  onClose: () => void;
  nodeList: NodeItem[];
}) => {
  const [selectedNode, setSelectedNode] = useState<NodeItem | null>(null);

  const afterOpenChange = (open: boolean) => {
    if (open && nodeList?.length) {
      setSelectedNode(nodeList[0]);
    } else {
      setSelectedNode(null);
    }
  }

  return (
    <Modal
      title="调用链路"
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      bodyStyle={{ padding: 0 }}
      afterOpenChange={afterOpenChange}
    >
      <div style={{ display: 'flex', height: '600px' }}>
        <div style={{
          width: '240px',
          borderRight: '1px solid #f0f0f0',
          overflow: 'auto',
          padding: '16px 0',
          position: 'relative'
        }}>
          {nodeList?.length > 0 ? (
            <>
              <Steps
                direction="vertical"
                size="small"
                current={nodeList.length}
                style={{ width: '100%' }}
                className="node-steps"
                items={nodeList.map((node) => ({
                  title: node.nodeName,
                  status: node.status === 'done' ? 'finish' : 'error',
                  onClick: () => setSelectedNode(node),
                  className: selectedNode?.id === node.id ? 'selected-node' : '',
                  style: {
                    cursor: 'pointer',
                    userSelect: 'none',
                  }
                }))}
              />
              <style>
                {`
                  .node-steps .selected-node .ant-steps-item-title {
                    color: #4782f0 !important;
                    font-weight: bold;
                  }
                `}
              </style>
            </>
          ) : (
            <EmptyContainer>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无链路数据"
              />
            </EmptyContainer>
          )}
        </div>

        {/* 右侧内容展示 */}
        <div style={{
          flex: 1,
          padding: '16px 24px',
          overflow: 'auto',
          background: '#fafafa'
        }}>
          {selectedNode ? (
            <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
              <Card title="输入内容" size="small" bordered={false}>
                <ReactJson
                  src={JSON.parse(selectedNode.inputContent)}
                  displayDataTypes={false}
                  collapsed={2}
                  style={{ fontSize: '13px' }}
                  name={false}
                />
              </Card>
              <Card title="输出内容" size="small" bordered={false}>
                <ReactJson
                  src={JSON.parse(selectedNode.outputContent)}
                  displayDataTypes={false}
                  collapsed={2}
                  style={{ fontSize: '13px' }}
                  name={false}
                />
              </Card>
            </div>
          ) : (
            <EmptyContainer>请选择左侧步骤查看详情</EmptyContainer>
          )}
        </div>
      </div>
    </Modal>
  );
};

export const ChatflowLogList = ({ appId, dateRange, setShowMessages, appType, appName }: MessageListProps) => {
  const [pageSize, setPageSize] = useState(10);
  const [current, setCurrent] = useState(1);
  const [chatId, setChatId] = useState('');
  const [requestId, setRequestId] = useState('');
  const [modalConfig, setModalConfig] = useState<{
    visible: boolean;
    title: string;
    content: string;
  }>({
    visible: false,
    title: '',
    content: '',
  });
  const [userId, setUserId] = useState('');
  const [nodeFlowVisible, setNodeFlowVisible] = useState(false);
  const [currentNodeList, setCurrentNodeList] = useState<NodeItem[]>([]);

  const { data, loading } = useRequest(
    () => ChatflowAPI.getWorkflowList(appId, current, pageSize, {
      startTime: dateRange?.start?.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dateRange?.end?.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      userId,
      chatId,
      requestId,
    }),
    {
      refreshDeps: [pageSize, current, chatId, requestId, userId, dateRange],
    }
  );

  const { run: fetchNodeList } = useRequest(
    (requestId: string) => ChatflowAPI.getNodeDetail(requestId),
    {
      manual: true,
      onSuccess: (data) => {
        setCurrentNodeList(data?.nodeList || []);
        setNodeFlowVisible(true);
      },
    }
  );

  console.log('data...', data);

  const columns = [
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Tooltip title={record.createdBy?.email || record.createdBy}>
          <a onClick={() => fetchNodeList(record.requestId)}>
            调用链路
          </a>
        </Tooltip>
      ),
    },
    {
      title: '问题',
      dataIndex: 'inputContent',
      key: 'inputContent',
      width: '20%',
      render: (text: string, record: any) => (
        <Paragraph
          ellipsis={{
            rows: 2,
            expandable: false,
          }}
          style={{
            marginBottom: 0,
            cursor: 'pointer'
          }}
          onClick={() => setModalConfig({
            visible: true,
            title: '问题详情',
            content: text
          })}
        >
          {renderPreviewContent(text)}
        </Paragraph>
      ),
    },
    {
      title: '回答',
      dataIndex: 'outputContent',
      key: 'outputContent',
      width: '20%',
      render: (text: string, record: any) => (
        <Paragraph
          ellipsis={{
            rows: 2,
            expandable: false,
          }}
          style={{
            marginBottom: 0,
            cursor: 'pointer'
          }}
          onClick={() => setModalConfig({
            visible: true,
            title: '回答详情',
            content: text || JSON.stringify(record?.toolCalls?.map((toolCall: any) => ({
              type: 'toolCall',
              text: toolCall.response
            })))
          })}
        >
          {renderPreviewContent(text, record)}
        </Paragraph>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '请求ID',
      dataIndex: 'requestId',
      key: 'requestId',
      render: (text: string) => (
        <a onClick={(e) => {
          e.preventDefault();
          setRequestId(text);
          setCurrent(1);
        }}>
          {text}
        </a>
      ),
    },
    {
      title: '会话ID',
      dataIndex: 'chatId',
      key: 'chatId',
      render: (text: string) => (
        <a onClick={(e) => {
          e.preventDefault();
          setChatId(text);
          setCurrent(1);
        }}>
          {text}
        </a>
      ),
    },
    {
      title: '用户',
      dataIndex: 'userId',
      key: 'userId',
      render: (_, record) => (
        <Tooltip title={record.createdBy?.email || record.createdBy}>
          <a onClick={(e) => {
            e.preventDefault();
            setUserId(record.userId);
            setCurrent(1);
          }}>
            {record.userId}
          </a>
        </Tooltip>
      ),
    },

  ].filter(Boolean);

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column', flex: 1 }}>
      <div style={{
        display: 'flex',
        flex: 1,
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16
      }}>
        <Space>
          <Input.Search
            placeholder="单次请求ID"
            value={requestId}
            onChange={e => setRequestId(e.target.value)}
            onSearch={value => {
              setRequestId(value);
              if (!value) {
                setCurrent(1);
              }
            }}
            allowClear
            style={{ width: 200 }}
          />
          <Input.Search
            placeholder="会话ID"
            value={chatId}
            onChange={e => setChatId(e.target.value)}
            onSearch={value => {
              setChatId(value);
              if (!value) {
                setCurrent(1);
              }
            }}
            allowClear
            style={{ width: 200 }}
          />
          <Input.Search
            placeholder="搜索用户ID"
            value={userId}
            onChange={e => setUserId(e.target.value)}
            onSearch={value => {
              setUserId(value);
              if (!value) {
                setCurrent(1);
              }
            }}
            allowClear
            style={{ width: 200 }}
          />
        </Space>
      </div>
      <Table
        columns={columns}
        dataSource={data?.workflowList}
        loading={loading}
        pagination={{
          total: data?.total || 0,
          pageSize,
          current,
          onChange: (page, pageSize) => {
            setCurrent(page);
            setPageSize(pageSize);
          },
        }}
        style={{ flex: 1 }}
      />
      <DetailModal
        visible={modalConfig.visible}
        title={modalConfig.title}
        content={modalConfig.content}
        onClose={() => setModalConfig(prev => ({ ...prev, visible: false }))}
      />
      <NodeFlowModal
        visible={nodeFlowVisible}
        onClose={() => setNodeFlowVisible(false)}
        nodeList={currentNodeList}
      />
    </div>
  );
};

const EmptyContainer = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
`