// @ts-ignore
import { RocketOutlined, SettingOutlined, ApiOutlined, BookOutlined } from '@ant-design/icons';
import { Button, Card, Input, message, Modal, Space, Spin, Tabs } from 'antd';
import TabPane from 'antd/es/tabs/TabPane';
import { pick } from 'lodash';
import { useCallback, useState } from 'react';
import styled from 'styled-components';

import { AppApi } from '@/api/app';
import { OpenAiAPI } from '@/api/conversation';
import { AgentParamsRender } from '@/components/agent-params-render/complete';
import { LayoutContainer } from '@/components/layout-container';
import { Markdown } from '@/components/Markdown';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppDetailModel, IAppType } from '@/interface';
import { IAgentParam } from '@/interface/agent';
import { CompletionStreamResponse } from '@/interface/conversation';
import { isModelSupportStream } from '@/utils/model-helper'

import { IModelSettingProps, ModelSetting } from '../../../../components/ModelSetting';
import Workflow from '../../workflow/index';
import { Conversation } from './conversation';
import { TokenOperationCard } from './command/embed';
import { ParamsEditor } from './mods/params-editor';
import { ToolsCard } from './plugins';
import { AppUsage } from './mods/usage';
import { useRequest } from 'ahooks';

const { TextArea } = Input;
const { confirm } = Modal;

export function DevPage() {
  const { globalState } = useGlobalState();
  const { app } = globalState;

  if (!app) {
    return null;
  }

  return <>{app.type === 'workflow' ? <Workflow /> : <AgentDevPage app={app} />}</>;
}

const TabTitle = styled.div`
  margin-left: 4px;
  margin-right: 4px;
`;

interface IAgentDevProps {
  app: AppDetailModel;
}

let completeCache = '';

function AgentDevPage(props: IAgentDevProps) {
  const { app } = props;
  const { prePrompt: pre_prompt, paramsInPrompt: params_in_prompt, tools: selectedTools } = app.config || {};
  const [completeLoading, setCompleteLoading] = useState(false);
  const [res, setRes] = useState('');
  const [conversationKey, setConversationKey] = useState('' + Date.now());

  // 配置相关
  const [prePrompt, setPrePrompt] = useState(pre_prompt);
  // @ts-ignore
  const [paramsInPrompt, setParamsInPrompt] = useState<IAgentParam[]>(params_in_prompt);
  const [tools, setTools] = useState(selectedTools ?? {})
  const [modelConfig, setModelConfig] = useState<IModelSettingProps>(
    // @ts-ignore
    pick(app.config, ['modelName', 'modelParams', 'providerKind']),
  );
  // 调试相关
  const [inputParams, setInputParams] = useState<any>({});

  const { run: updateExample } = useRequest((params: { description?: string, usingExample?: string }) => AppApi.updateApp({
    description: params.description,
    querySample: params.usingExample
  }, app.id), {
    manual: true,
    onSuccess: () => {
      message.success('保存成功');
    },
  })

  // 用于计算对话框的最大高度
  // const rightContainerDomRef = useRef<HTMLDivElement | null>(null);
  // const conversationDomRef = useRef<HTMLDivElement | null>(null);
  // const [conversationMaxHeight, setConversationMaxHeight] = useState(200);

  // useEffect(() => {
  //   // 右面板的高度
  //   const rightContainerHeight =
  //     rightContainerDomRef.current?.getBoundingClientRect().height || 200;
  //   // 右面板的 y 坐标
  //   const rightContainerTop =
  //     rightContainerDomRef.current?.getBoundingClientRect().top || 0;
  //   // 会话框的 y 坐标
  //   const conversationDomTop =
  //     conversationDomRef.current?.getBoundingClientRect().top || 0;
  //   // 会话框的最大剩余高度
  //   const conversationRemainHeight =
  //     rightContainerHeight - (conversationDomTop - rightContainerTop);
  //   setConversationMaxHeight(Math.max(conversationRemainHeight, 200));
  // }, [
  //   conversationDomRef.current?.getBoundingClientRect().top,
  //   (paramsInPrompt ?? []).map((it) => it.key).join('-'), // 当参数变化时，重置会话框的最大高度计算
  // ]);

  const onConversationClear = useCallback(() => {
    setConversationKey('' + Date.now());
  }, []);

  // 重置
  function showResetModal() {
    confirm({
      title: '确认重置？',
      content: '重置会将当前页面的所有修改，恢复至上一次发布',
      onOk() {
        setPrePrompt(pre_prompt);
        // @ts-ignore
        setParamsInPrompt(params_in_prompt);
        // @ts-ignore
        setModelConfig(pick(app.config, ['modelName', 'modelParams', 'providerKind']));
        onConversationClear();
      },
      onCancel() { },
    });
  }

  function onPreviewClick() {
    window.open(`/preview/${app?.type}?appId=${app!.id}`, '_blank');
  }

  const onCompleteRun = useCallback((params: any) => {
    setCompleteLoading(true);
    setRes('');
    completeCache = '';
    const supportStream = isModelSupportStream(modelConfig)
    const { imageUrl, audioUrl, videoUrl, ...rest } = params;

    const config = {
      ...app.config,
      ...modelConfig,
      prePrompt,
      paramsInPrompt,
      tools,
    };

    OpenAiAPI.completion({
      imageUrl,
      audioUrl,
      videoUrl,
      appID: app.id,
      parameters: rest,
      config,
      responseMode: supportStream ? 'streaming' : 'json',
      onMessage: (d: CompletionStreamResponse) => {
        if (d.content && !d.toolCalls) {
          completeCache = completeCache + d.content;
          setRes(completeCache);
        }
      },
      onFinish: () => {
        setCompleteLoading(false);
      },
    });
  }, [prePrompt, paramsInPrompt, modelConfig, app.id, inputParams]);

  // 发布
  function onPublish() {
    const config = {
      prePrompt,
      paramsInPrompt,
      tools,
    };

    AppApi.updateApp(
      {
        config: {
          ...app.config,
          ...modelConfig,
          ...config,
        },
      },
      app.id,
    ).then(() => {
      message.success('发布成功');
    });
  }

  function renderCompleteResult() {
    return (
      <ResultContainer>
        <h4>结果</h4>
        <div className="result-text">
          {completeLoading && !res ? <Spin /> : <Markdown content={res} />}
        </div>
      </ResultContainer>
    );
  }

  function renderConversation(props: { key: string }) {
    const { key } = props;
    return (
      <Conversation
        // forwardRef={conversationDomRef}
        style={{
          height: '64vh',
          flexGrow: "1",
          // maxHeight: conversationMaxHeight + 'px',
        }}
        key={key}
        appId={app.id}
        inputParams={inputParams}
        config={{
          ...app.config,
          ...modelConfig,
          prePrompt,
          paramsInPrompt,
          tools,
        }}
      />
    );
  }

  return (
    <>
      <LayoutContainer innerStyle={{ display: 'flex', width: 'max-content' }}>
        <LeftContainer style={{ width: '850px', height: "100%", marginRight: '12px' }}>
          <Tabs type="card">
            <TabPane
              tab={
                <TabTitle>
                  <SettingOutlined />
                  配置
                </TabTitle>
              }
              key="setting"
            >
              <div style={{ display: 'flex' }}>
                <div style={{ width: '600px', marginRight: '20px'}}>
                  <TextArea
                    style={{ height: '100%'}}
                    value={prePrompt}
                    onChange={(e) => {
                      setPrePrompt(e.target.value);
                    }}
                    rows={8}
                    placeholder="请输入预置提示词"
                  />
                </div>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    flexWrap: 'wrap',
                  }}
                >
                  <ModelSetting value={modelConfig} onChange={setModelConfig} />
                  <Space>
                    <Button onClick={showResetModal}>重置</Button>
                    <Button type="primary" onClick={onPublish}>
                      保存
                    </Button>
                    <Button
                      onClick={onPreviewClick}
                      icon={<RocketOutlined rev={undefined} />}
                    >
                      预览
                    </Button>
                  </Space>
                  <Space direction='vertical' style={{ width: '100%' }}>
                    <ParamsEditor value={paramsInPrompt} onChange={setParamsInPrompt} />
                    <ToolsCard toolIDs={tools} onToolsChanged={setTools} />
                  </Space>
                </div>
              </div>
            </TabPane>
            <TabPane
              key="usage"
              tab={
                <TabTitle>
                  <BookOutlined />
                  Web使用
                </TabTitle>
              } >
              <AppUsage description={app.description} usingExample={app.querySample} onChanged={(d) => { updateExample(d) }} />
            </TabPane>
            <TabPane
              tab={
                <TabTitle>
                  <ApiOutlined />
                  API
                </TabTitle>
              }
              key="embed"
            >
              <div style={{ marginBottom: '10px', fontSize: '12px', color: '#777777' }}>
                Token对接请参考<a href="/docs#/service">Service API</a>文档
              </div>
              <TokenOperationCard
                setTokens={() => {}}
                appID={app.id}
                agentConfig={{
                  ...app.config,
                  ...modelConfig,
                  prePrompt,
                  paramsInPrompt,
                }}
              />
              {/* <h4>访问</h4>
              <TokenCurlCommandCard
                appID={app.id}
                appType={app.type}
                agentConfig={{
                  ...app.config,
                  ...modelConfig,
                  prePrompt,
                  paramsInPrompt,
                }}
              /> */}
            </TabPane>
            <TabPane
              tab={
                <TabTitle>
                  <ApiOutlined />
                  三方接入
                </TabTitle>
              }
              key="third"
            >
              <div style={{ marginBottom: '10px', fontSize: '12px', color: '#777777' }}>
                Token对接请参考<a href="/docs#/service">Service API</a>文档
              </div>
              <TokenOperationCard
                setTokens={() => {}}
                appID={app.id}
                agentConfig={{
                  ...app.config,
                  ...modelConfig,
                  prePrompt,
                  paramsInPrompt,
                }}
              />
              {/* <h4>访问</h4>
              <TokenCurlCommandCard
                appID={app.id}
                appType={app.type}
                agentConfig={{
                  ...app.config,
                  ...modelConfig,
                  prePrompt,
                  paramsInPrompt,
                }}
              /> */}
            </TabPane>
          </Tabs>
        </LeftContainer>
        <RightContainer style={{ width: '750px' }}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <h1>调试与预览</h1>
            {app.type === IAppType.AgentConversation && (
              <Button onClick={onConversationClear}>清除当前对话</Button>
            )}
          </div>
          <AgentParamsRender
            paramsInPrompt={paramsInPrompt}
            onCommit={onCompleteRun}
            onChange={setInputParams}
            labelAlign="left"
            showCommitBtn={app.type === IAppType.AgentCompletion}
          />
          {app.type === IAppType.AgentCompletion && renderCompleteResult()}
          {app.type === IAppType.AgentConversation &&
            renderConversation({ key: conversationKey })}
        </RightContainer>
      </LayoutContainer>
    </>
  );
}

// const Head = styled.div`
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   position: relative;
//   border-bottom: 1px solid #eee;
//   height: 80px; // 需要与 Main 中的 max-height: calc(100% - 80px) 保持一致

//   .split-line {
//     margin: 0 10px;
//     color: #ccc;
//   }
// `;

// const Main = styled.div`
//   display: flex;
//   flex-direction: row;
//   flex-grow: 1;
//   max-height: calc(100% - 80px);
// `;

const LeftContainer = styled.div`
  /* padding-right: 20px; */
  /* border-right: 1px dashed #eee; */
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background-color: #ffffff;
  padding: 20px;
  /* min-height: 80vh; */
  /* overflow-y: auto; */
  box-sizing: border-box;

  /* .ant-card-body {
    height: 100%;
  } */
`;

const RightContainer = styled(Card)`
  display: flex;
  flex-direction: column;
  position: relative;
  width: 50%;
  box-sizing: border-box;
`;

const ResultContainer = styled.div`
  .result-title {
    color: #333;
    margin-bottom: 10px;
  }

  .result-text {
    width: 100%;
    min-height: 340px;
    max-height: 440px;
    overflow-y: auto;
    background-color: white;
    border: 1px solid rgb(217, 217, 217);
    border-radius: 8px;
    padding: 20px;
    box-sizing: border-box;
    white-space: pre-wrap;
  }
`;
