// @ts-nocheck
import {
  BulbOutlined,
  LoadingOutlined,
  SendOutlined,
  SettingOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ChatView, GLOBAL_EVENTS, langEvent } from '@music/lang';
import AutoScroll from '@brianmcallister/react-auto-scroll';
import { useRequest, useThrottle } from 'ahooks';
import { Button, Flex, Input, message, Spin, Tooltip } from 'antd';
import classnames from 'classnames';
import { reverse } from 'lodash';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';

import { OpenAiAPI } from '@/api/conversation';
import { ServiceAPI } from '@/api/service';
import { Markdown } from '@/components/Markdown';
import { AgentConfig, IAgentParam } from '@/interface/agent';
import { ConversationResponse, ConversationStreamResponse } from '@/interface/conversation';
import eventBus, { IEventType } from '@/utils/event-bus';
import { isModelSupportStream, isModelSupportImage, isModelSupportAudio, isModelSupportVideo } from '@/utils/model-helper'
import { TokenApi } from '@/api/token';
import { CodeSnippet } from '@/components/code-snippet';
import InputComposer, { withInfo } from '@/components/input-composer';
import { useGlobalState } from '@/hooks/useGlobalState';
import TopBar from '../chat-bot/top-bar';
import ParamsDebug from '../chat-bot/params-debug';
import { ClearIcon } from '@/components/icons';
import ReferencesDrawer from '../chat-bot/references-drawer';


const { TextArea } = Input;
const loadingIcon = <LoadingOutlined style={{ fontSize: 16 }} spin rev={undefined} />;

interface IProps {
  style?: React.CSSProperties;
  conversationId?: string;
  appId: string;
  inputParams?: Record<string, string> | null;
  config?: AgentConfig;
  forwardRef?: React.RefObject<HTMLDivElement>;
  explore?: boolean;
  showNavbar?: boolean;
  type?: string;
  configId?: string;
  welcomeText?: string;
}

interface IMessage {
  role: 'user' | 'robot' | 'robot-tool-call' | 'tool';
  toolName?: string;
  content: string;
}

const Composer = () => {
  return <div>
    <Input />
    <Button>发送</Button>
  </div>
}



const ToolMessage = (props: IMessage) => {
  const { toolName, content } = props;
  const [showMore, setShowMore] = useState(false);

  if (!showMore) {
    return (
      <ToolMessageStyled>
        <div className="name">{toolName}</div>
        <div className="more" onClick={() => setShowMore(true)}>
          点击查看
        </div>
      </ToolMessageStyled>
    );
  }

  return <Markdown content={content} />;
};

const MessageItem = (props: IMessage) => {
  const { role, content } = props;
  const [showMore, setShowMore] = useState(false);

  const msgContent = useMemo(() => {
    if (role !== 'tool') {
      return content;
    }
    if (showMore) {
      return content;
    }
  }, [role, content]);

  return (
    <div>
      <span
        style={{
          whiteSpace: 'pre-wrap',
          color: '#1f1f1f',
          // lineHeight: '1.75',
        }}
      >
        {role === 'tool' ? <ToolMessage {...props} /> : <Markdown content={msgContent} />}
      </span>
    </div>
  );
};


export function Conversation(props: IProps) {
  const {
    appId,
    conversationId,
    config,
    type,
    configId = '',
    explore,
    showNavbar = false,
    inputParams,
    welcomeText
  } = props;
  const { globalState } = useGlobalState();
  const [media, setMedia] = useState([]);
  const [paramsValue, setParamsValue] = useState({});
  const referencesRef = useRef();
  const paramsInPrompt = useMemo(() => {
    if (!config) {
      return [];
    }
    return config?.paramsInPrompt || [];
  }, [config]);

  const onParamsChange = parameters => {
    setParamsValue(parameters)
  };

  useEffect(() => {
    if (inputParams?._mediaParam) {
      // 新版本
      console.log("inputParams?._mediaParam", inputParams?._mediaParam);
      if (inputParams?._mediaParam?.items) {
        setMedia(inputParams?._mediaParam?.items);
      } else {
        setMedia([inputParams?._mediaParam]);
      }
    }
  }, [inputParams?._mediaParam]);

  const { modelList } = globalState;

  const model = modelList.find(m => m.name === config?.modelName);
  const maxTokens = model?.context || 64;
  const enableView = isModelSupportImage(model);
  const enableAudio = isModelSupportAudio(model);
  const enableVideo = isModelSupportVideo(model);

  const { data } = useRequest(() => TokenApi.createAppTempToken(appId), { refreshDeps: [appId] });
  useEffect(() => {
    langEvent.on(GLOBAL_EVENTS.CHANGE_CONVERSATION, (res) => {
      if (res.conversationId) {
        eventBus.emit(IEventType.NEW_CHAT_CREATE, res.conversationId);
      }
      console.log("conv", res);
    });
  }, [])

  const view = useMemo(() => {
    if (!data?.token) {
      return <Spin></Spin>
    }
    const key = JSON.stringify(media) + maxTokens + enableView;
    return <ChatView
      key={key}
      type={type || 'agent'}
      appId={conversationId ? null : appId}
      token={data.token}
      conversationID={conversationId}
      configId={configId}
      showParams={false}
      config={conversationId ? null : config}
      renderNavbar={navbar => {
        const resetIcon = navbar.rightContent?.find(item => item.label === '删除会话记录')
        if (!showNavbar) {
          return null;
        }
        return <TopBar {...navbar} style={{ fontSize: 14, color: '#aaa' }}
          rightContent={<Flex justify="end" gap={8}>
            <ParamsDebug
              paramsInPrompt={paramsInPrompt}
              onChange={onParamsChange}
              value={paramsValue}
            />
            <Tooltip title={resetIcon.label} >
              <ClearIcon
                style={{ cursor: 'pointer', marginLeft: 10 }}
                onClick={(val) => {
                  resetIcon.onClick(val)
                }}
              />
            </Tooltip>
          </Flex>}
        />
      }}
      greetings={welcomeText}
      components={{
        Code: CodeSnippet
      }}
      // Composer={withInfo(inputParams?._mediaParam?.content, maxTokens)}
      Composer={explore ? (composerProps) => (
        <InputComposer
          {...composerProps}
          enableView={enableView}
          enableAudio={enableAudio}
          enableVideo={enableVideo}
          presetMedia={media} 
          onFileChange={(file) => {
            console.log("file...", file);
          }}
          maxTokens={maxTokens}
        />
      ) : null}
      debug
      avatars={{
        user: 'https://cdn-icons-png.flaticon.com/128/4681/4681809.png',
        bot: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/36161780381/54c7/b091/62ea/a1be41780312f808ece2daf80796c7b7.png',
      }}
      userId='langbase-debug'
      host={window.location.host}
      showReferences
      onReferencesClick={val => {
        console.log('onReferencesClick.references', val);
        referencesRef.current?.show(val);
      }}
    />
  }, [media, maxTokens, data, enableView]);

  return <>
    {view}
    <ReferencesDrawer ref={referencesRef}/>
  </>;
}

const ToolMessageStyled = styled.div`
  display: flex;
  column-gap: 8px;

  .name {
    display: inline-block;
  }
  .more {
    display: inline-block;
    color: #147bdb;
  }
  .more:hover {
    cursor: pointer;
  }
`;

const StyledConversation = styled.div`
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .message-list {
    display: flex;
    flex-direction: column;
    /* height: calc(100% - 60px); */
    width: 100%;
    box-sizing: border-box;
    overflow-y: auto;
    padding: 8px;

    .message-wrapper {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      max-width: 98%;

      & .message-item {
        width: fit-content;
      }

      &.robotToolCall {
        margin-left: 36px;

        & .message-item {
          font-size: 0.9rem;
          padding: 4px;
          padding-left: 8px;
          padding-right: 8px;
        }

        & .avatar {
          height: 28px;
          width: 28px;
          order: 0;
          color: white;
          background-color: #e9b84d;
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 16px;
            height: 16px;
          }
        }
      }

      &.tool {
        align-self: flex-start;
        margin-left: 36px;

        & .message-item {
          font-size: 0.9rem;
          padding: 4px;
          padding-left: 8px;
          padding-right: 8px;
          margin-bottom: 8px;
        }

        & .avatar {
          height: 28px;
          width: 28px;
          order: 0;
          color: white;
          background-color: #48b859;
          display: flex;
          align-items: center;
          justify-content: center;

          svg {
            width: 16px;
            height: 16px;
          }
        }
      }

      &.robot {
        align-self: flex-start;

        & .message-item {
          margin-bottom: 8px;
        }

        & .avatar {
          order: 0;
          color: white;
          background-color: #e9b84d;
        }
      }

      &.user {
        & .message-item {
          background-color: #e8eff8;
        }

        & .avatar {
          order: 0;
          color: white;
          background-color: #1573de;
        }
      }
    }

    .avatar {
      margin-right: 8px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #eee;
      line-height: 32px;
      text-align: center;
      flex-shrink: 0;
    }

    .message-item {
      padding: 8px;
      width: 100%;
      border-radius: 4px;
      margin-bottom: 8px;
      background-color: #eee;
      order: 1;

      & p {
        margin-block-start: 0;
        margin-block-end: 0;
      }
    }

    .no-more,
    .load-more {
      text-align: center;
      color: #999;
      margin-bottom: 10px;
      font-size: 12px;
    }
  }

  .input-box {
    position: absolute;
    width: 90%;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);

    .text-area {
      width: calc(100% - 80px);
      /* padding-right: 50px; */
    }

    .send {
      width: 50px;
      height: 50px;
      right: 24px;
      top: 36px;
      position: absolute;
      z-index: 99;
    }

    .stop {
      height: 40px;
      position: absolute;
      top: -36px;
      left: 50%;
      transform: translateX(calc(-50%));
      z-index: 98;
    }
  }
`;
