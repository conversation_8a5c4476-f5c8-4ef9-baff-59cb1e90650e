import { AggregationStrategyTypeMap } from "@/interface/agent";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { Flex, Form, Input, InputNumber, Select, Slider, Switch, Tooltip } from "antd";
import styled from "styled-components";

const StyledForm = styled(Form)`
  .ant-form-item-label {
     width: 148px;
  }
`;

const defaultValues = {
  maxTime: 1,
  maxCount: 10,
  strategy: 'AGG_ALL'
}


const OutputForm = (props) => {
  const { value, onChange } = props;
  const { enable, maxTime, maxCount, strategy } = value || { enable: true };


  const onValueChange = (key, val) => {
    let newValues = {
      ...(value || {}),
      [key]: val
    }

    // 开启和关闭需要特殊处理
    if (key === 'enable') {
      newValues = val
        ? {
          enable: true,
        }
        : {
          enable: false
        }
    }

    onChange?.(newValues);
  }

  return <>
    <>
      <Flex >
        <Flex style={{ marginRight: 16 }}>
          断句策略
          <Tooltip title="断句策略决定了文本输出的断句方式，开启后会通过 逗号，句号，换行 分割文本">
            <QuestionCircleOutlined style={{ color: "rgba(0,0,0,0.45)", marginLeft: 4 }} />
          </Tooltip>
        </Flex>

        <Switch checked={enable} checkedChildren="开启" unCheckedChildren="关闭"
          onChange={val => onValueChange('enable', val)}
        />
      </Flex>
    </>
  </>
}

export default OutputForm;