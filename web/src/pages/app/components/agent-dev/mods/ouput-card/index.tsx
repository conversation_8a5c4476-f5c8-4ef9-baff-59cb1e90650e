import { Space } from 'antd';
import CardCollapse from '../card-collapse';
import DiffAlert from '../diff-alert';
import OutputForm from './output-form';
import React from 'react';

export const outputSplitStr = (value) => {
  let res = '';

  if (value?.enable) {
    res = `
【分片策略】： 开启
    `;
  } else {
    res = `
【分片策略】：关闭
    `;
  }
  return res;
};

const OutputCard = (props) => {
  const { oldValue, value, onChange, expandLocalKey, disabled } = props;
  return (
    <>
      <CardCollapse
        expandLocalKey={expandLocalKey}
        moduleKey="outputSplit"
        collapsible={disabled ? "disabled" : 'icon'}
        items={[
          {
            key: 'outputSplit',
            label: (
              <Space>
                <span>
                  <span style={{ fontWeight: 'bold' }}>文本输出设置</span>
                  <DiffAlert
                    title="断句策略"
                    type="outputStrategyConfig"
                    oldValue={oldValue}
                    newValue={value}
                    onRedo={() => {
                      onChange(oldValue);
                    }}
                  />
                </span>
              </Space>
            ),
            children: (
              <span style={{ color: '#999' }}>
                输出设置能够让智能体在对话中规范回复内容，例如控制输出格式、设置回复生成速度、调整内容详略程度、应用输出模板等，确保AI回复高质量且符合预期地呈现给用户。
              </span>
            ),
            extra: !disabled && <OutputForm onChange={onChange} value={value} />,
          },
        ]}
        disabled={disabled}
      />
    </>
  );
};

export default OutputCard;
