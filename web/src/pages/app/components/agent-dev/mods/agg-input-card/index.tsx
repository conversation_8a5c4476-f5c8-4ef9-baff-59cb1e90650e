import { Flex, Space, Switch, Tooltip } from 'antd';
import <PERSON><PERSON>ollapse from '../card-collapse';
import DiffAlert from '../diff-alert';
import AggInputForm from './agg-input-form';
import { AggregationStrategyTypeMap } from '@/interface/agent';
import { InfoCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';

export const aggInputStr = (value) => {
  let res = '';
  if (value?.enable) {
    res = `
    【聚合输入】： 开启
    【最大输入等待时长】： ${value.maxTime || ''} 秒
    【最大输入数量】： ${value.maxCount || ''}
    【聚合输入策略】：${AggregationStrategyTypeMap[value.strategy]?.label || ''}
    `;
  } else {
    res = `
    【聚合输入】：关闭
    `;
  }
  return res;
};

const AggInputCard = (props) => {
  const { oldValue, value = {}, onChange, expandLocalKey, disabled } = props;
  return (
    <>
      <CardCollapse
        expandLocalKey={expandLocalKey}
        moduleKey="aggregateInputConfig"
        collapsible={disabled ? "disabled" : 'icon'}
        items={[
          {
            key: 'aggregateInputConfig',
            label: (
              <Space>
                <span>
                  <span style={{ fontWeight: 'bold' }}>输入设置</span>
                  <DiffAlert
                    title="输入设置"
                    type="aggregateInputConfig"
                    oldValue={oldValue}
                    newValue={value}
                    onRedo={() => {
                      onChange(oldValue);
                    }}
                  />
                </span>
                <Tooltip title="聚合输入的主要用途是，当用户输入多个信息的时候，虚拟人可以聚合这些信息，然后根据这些信息生成一个回答。避免在回答过程中，用户需要多次输入，导致回答不连贯。">
                  <InfoCircleOutlined />
                </Tooltip>
              </Space>
            ),
            children: (
              <>
                <AggInputForm onChange={onChange} value={value} />
              </>
            ),
            extra: !disabled && (
              <Space>
                <Flex>
                  聚合用户输入
                </Flex>
                <Switch
                  onClick={(_, e) => e.stopPropagation()}
                  checked={value?.enable}
                  checkedChildren="开启"
                  unCheckedChildren="关闭"
                  onChange={(val) => {
                    const newValues = val
                      ? {
                        enable: true,
                        maxTime: 1,
                        maxCount: 10,
                        strategy: 'AGG_ALL',
                      }
                      : { enable: false };
                    onChange(newValues);
                  }}
                />
              </Space>
            ),
          },
        ]}
      />
    </>
  );
};

export default AggInputCard;
