import { AggregationStrategyTypeMap } from '@/interface/agent';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Flex, Form, Input, InputNumber, Select, Slider, Switch, Tooltip } from 'antd';
import styled from 'styled-components';
import React from 'react';

const StyledForm = styled(Form)`
  .ant-form-item-label {
    width: 148px;
  }
`;

const defaultValues = {
  maxTime: 1,
  maxCount: 10,
  strategy: 'AGG_ALL',
};

const AggInputForm = (props) => {
  const { value, onChange } = props;
  const { enable, maxTime, maxCount, strategy } = value || {};

  const onValueChange = (key, val) => {
    let newValues = {
      ...(value || {}),
      [key]: val,
    };
    // 开启和关闭需要特殊处理
    if (key === 'enable') {
      newValues = val
        ? {
            enable: true,
            ...defaultValues,
          }
        : {
            enable: false,
          };
    }
    onChange?.(newValues);
  };

  return (
    <>
      {enable ? (
        <>
          <Flex align="center" style={{ marginBottom: 10 }}>
            <Flex align="center" style={{ marginRight: 16 }}>
            最大等待时长(秒)
              <Tooltip title="设置聚合的时间窗口长度，以秒为单位。在此时间窗口内收集的用户输入将被聚合为一条消息。">
                <QuestionCircleOutlined
                  style={{ color: 'rgba(0,0,0,0.45)', marginLeft: 4 }}
                />
              </Tooltip>
            </Flex>
            <Slider
              min={1}
              max={5}
              style={{ flex: 1, margin: '0 16px' }}
              value={maxTime}
              onChange={(val) => onValueChange('maxTime', val)}
            />
            <InputNumber
              min={1}
              max={5}
              value={maxTime}
              onChange={(val) => onValueChange('maxTime', val)}
            />
            <span> 秒</span>
          </Flex>

          <Flex align="center" style={{ marginBottom: 10 }}>
            <Flex style={{ marginRight: '4px' }}>
              最大输入数量
              <Tooltip title="当在设定的时间窗口内收集到的输入数量达到此最大值时，将立即触发聚合处理，而不必等待时间窗口结束。">
                <QuestionCircleOutlined
                  style={{ color: 'rgba(0,0,0,0.45)', marginLeft: 4 }}
                />
              </Tooltip>
            </Flex>
            <Slider
              min={1}
              max={20}
              // marks={{
              //   1: 1,
              //   20: 20
              // }}
              style={{ flex: 1, margin: '0 16px' }}
              value={maxCount}
              onChange={(val) => onValueChange('maxCount', val)}
            />
            <InputNumber
              min={1}
              max={20}
              value={maxCount}
              onChange={(val) => onValueChange('maxCount', val)}
            />
          </Flex>

          <Flex align="center">
            <Flex style={{ marginRight: '16px' }}>聚合输入策略</Flex>
            <Select
              style={{ width: '140px' }}
              popupMatchSelectWidth={false}
              value={strategy}
              onChange={(val) => onValueChange('strategy', val)}
              options={Object.keys(AggregationStrategyTypeMap)?.map(
                (key) => AggregationStrategyTypeMap[key],
              )}
              optionRender={(option) => {
                return (
                  <div>
                    <div>{option.data.label}</div>
                    <div
                      style={{
                        fontWeight: 'normal',
                        color: 'rgba(0,0,0,0.5)',
                        fontSize: '12px',
                      }}
                    >
                      {option.data.desc}
                    </div>
                  </div>
                );
              }}
            >
              {/* {Object.keys(AggregationStrategyTypeMap)?.map(key => {
              return <Select.Option value={AggregationStrategyTypeMap[key]?.value}>
                <div>
                  <div>{AggregationStrategyTypeMap[key]?.label}</div>
                  <div>{AggregationStrategyTypeMap[key]?.desc}</div>
                </div>
              </Select.Option>
            })} */}
            </Select>
          </Flex>
        </>
      ) : (
        <span style={{ color: '#999' }}>
          输入设置能够让智能体在对话中规范用户输入，例如限制最大输入等待时长、控制最大输入数量、采用聚合输入策略等，确保对话高效有序地进行。
        </span>
      )}
    </>
  );
};

export default AggInputForm;
