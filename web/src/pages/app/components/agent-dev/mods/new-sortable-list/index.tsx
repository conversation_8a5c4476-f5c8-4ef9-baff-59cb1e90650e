import { HolderOutlined } from "@ant-design/icons";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Flex } from "antd";
import { useEffect, useState } from "react";

const initialData = [
  {
    key: '1',
    name: '<PERSON>',
    age: 32,
    address: 'Long text Long',
  },
  {
    key: '2',
    name: '<PERSON>',
    age: 42,
    address: 'London No. 1 Lake Park',
  },
  {
    key: '3',
    name: '<PERSON>',
    age: 32,
    address: 'Sidney No. 1 Lake Park',
  },
];

const SortableItem = (props) => {
  const { id, data, itemRender, index, itemStyle, dragHandleStyle } = props;
  const {
    setNodeRef,
    setActivatorNodeRef,
    listeners,
    transform,
    transition,
    attributes
  } = useSortable({
    id,
    // transition: {
    //   duration: 150, // milliseconds
    //   easing: 'cubic-bezier(0.25, 1, 0.5, 1)',
    // },
  });


  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    ...itemStyle
  };

  return <Flex ref={setNodeRef} style={style} {...attributes}>
    <Flex
      justify="center"
      ref={setActivatorNodeRef}
      {...listeners}
      style={{
        cursor: 'move',
        // width: '24px',
        ...dragHandleStyle
      }}>
      <HolderOutlined />
    </Flex>
    <Flex flex={1}>
      {itemRender?.(data, index)}
    </Flex>
  </Flex>
};


const NewSortableList = (props) => {
  const { itemKey = 'key', value, onChange, itemRender, itemStyle, dragHandleStyle } = props;

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    }),
  );

  const onDragEnd = ({ active, over }) => {
    if (active.id !== over?.id) {
      const activeIndex = value?.findIndex((record) => record[itemKey] === active?.id);
      const overIndex = value?.findIndex((record) => record[itemKey] === over?.id);
      const newValue = arrayMove(value, activeIndex, overIndex);
      onChange?.(newValue);
    }
  };

  if (!value) {
    return null;
  }

  return <DndContext
    sensors={sensors}
    modifiers={[restrictToVerticalAxis]}
    onDragEnd={onDragEnd}>
    <SortableContext
      items={value?.map((i) => i[itemKey])}
      strategy={verticalListSortingStrategy} >
      {
        value?.map((item, index) => {
          const key = item[itemKey];
          return <SortableItem
            id={key}
            data={item}
            key={key}
            index={index}
            itemRender={itemRender}
            itemStyle={itemStyle}
            dragHandleStyle={dragHandleStyle}
          />
        })
      }
    </SortableContext>
  </DndContext>
};

export default NewSortableList;
