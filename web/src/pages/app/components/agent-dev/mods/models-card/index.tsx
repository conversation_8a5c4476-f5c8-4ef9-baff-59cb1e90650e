import {
  But<PERSON>,
  Flex,
  InputNumber,
  List,
  message,
  Popconfirm,
  Popover,
  Select,
  Slider,
  Space,
  Tooltip,
  Tour,
  TourProps,
  Typography,
} from 'antd';
import CardCollapse from "../card-collapse";
import DiffAlert from "../diff-alert";
import { DeleteOutlined, EditOutlined, PlusOutlined, SettingOutlined, SplitCellsOutlined } from '@ant-design/icons';
import RetryConfig from "@/components/MultipleModelSetting/RetryConfig";
import { useGlobalState } from "@/hooks/useGlobalState";
import styled from "styled-components";
import { IAppType } from "@/interface";
import { useEffect, useRef, useState } from "react";
import { useDebounceFn } from "ahooks";
import ModelSelectModal from "./model-select-modal";
import ModelParamsModal from "./model-params-modal";
import { getLocalData, saveLocalData } from '@/utils/common';

const Desc = styled.pre`
  white-space: pre-line;
  color: #999;
  font-size: 12px;
  margin: 0;

  .fee {
    margin: 0;
    margin-top: 5px;
    color: #da4712;
  }
`;

const StyledModels = styled.div`
  h4 {
    text-align: left;
  }
  .ant-list-item-action {
    margin-inline-start: 20px !important;
  }
`;

const ModelsCard = (props) => {
  // mode === multiple的时候是多选
  const { expandLocalKey, value: parentValue, oldValue, onChange: onParentChange, mode, appType, setMode } = props;
  const ref1 = useRef(null);
  const [open, setOpen] = useState(!getLocalData('modelsTour'));

  const [value, setValue] = useState(parentValue);

  const { globalState } = useGlobalState();
  const { modelList } = globalState;

  const isAdvanced = value?.type === 'advanced';

  const isBasic = !['advanced', 'master_backup'].includes(value?.type);

  const isMultiple = ['advanced', 'master_backup'].includes(value?.type);

  // 是否是生成型
  const isCompletion = appType === IAppType.AgentCompletion;

  const handleClose = () => {
    saveLocalData('modelsTour', 1);
    setOpen(false);
  }

  const { run, flush } = useDebounceFn(val => {
    onParentChange(val)
  }, {
    wait: 500
  });

  useEffect(() => {
    setValue(parentValue);
  }, [parentValue]);

  const onChange = (val, isFlush?) => {
    setValue(val);
    run(val);
    if (isFlush) {
      flush();
    }
  };

  const onRetryConfigChange = (val) => {
    onChange({
      ...(value || {}),
      retryConfig: val
    });
  };

  const onModelParamsChange = (modelName, val) => {
    const newModels = value?.models?.map(item => {
      if (item.modelName === modelName) {
        return {
          ...item,
          modelParams: val
        }
      }
      return item;
    });

    onChange({
      ...(value || {}),
      models: newModels
    });
  };

  const onRatioChange = (modelName, val) => {
    const totalRatio = value?.models?.reduce((acc, cur) => {
      if (cur.modelName === modelName) {
        return acc + val;
      }
      return acc + (cur?.ratio || 0)
    }, 0);

    if (totalRatio > 1) {
      message.error('模型调用概率总和不能超过1');
      return false;
    }
    // if (totalRatio < 1) {
    //   message.error('模型调用概率总和需等于1');
    //   return false;
    // }

    const newModels = value?.models?.map(item => {
      if (item.modelName === modelName) {
        return {
          ...item,
          ratio: val
        }
      }
      return item;
    });
    onChange({
      ...(value || {}),
      models: newModels
    });
  };

  const onModelsChange = (models) => {
    const newModels = value?.models?.concat(models?.map(model => ({ ...model, ratio: 0 })))
    if (isCompletion && !isBasic) {
      // 多选
      onChange({
        ...(value || {}),
        models: newModels
      }, true);
    } else {
      // 单选
      onChange({
        ...(value || {}),
        models: models?.map(model => ({ ...model, ratio: 1 }))
      }, true);
    }
  };

  const onTypeChange = val => {
    let newModels = value?.models;
    // 单选
    if (val === 'basic') {
      newModels = value?.models?.[0] ? [value?.models?.[0]] : []
    }

    // 分流，根据概率
    if (val === 'advanced') {
      newModels = value?.models?.map((item, index) => {
        const ratio = index === 0 ? 1 : 0
        return {
          ...(item || {}),
          ratio
        }
      })
    }

    // 主备，默认第一个是主
    if (val === 'master_backup') {
      newModels = value?.models;
    }

    onChange({
      ...(value || {}),
      models: newModels,
      type: val
    });
  };

  const onDelete = val => {
    const newModels = value?.models?.filter(item => {
      if (item.modelName === val) {
        return false;
      }
      return true;
    });

    onChange({
      ...(value || {}),
      models: newModels
    });
  }

  const onBackUpChange = (val, model, index) => {
    const newModels = value?.models?.filter(item => item?.modelName !== model?.modelName);

    // 当前模型设为第一个
    if (val === 'main') {
      newModels.unshift(model);
    }

    if (val === 'backup') {
      newModels.push(model)
    };
    onChange({
      ...(value || {}),
      models: newModels
    });

  };

  const steps: TourProps['steps'] = [
    {
      title: '模型配置搬家啦🎉',
      description: <span>
        在这里你可以更直观看到高级模型配置，一键切换模型，更便捷的查看模型介绍和费用，
        <a href="https://music-doc.st.netease.com/st/langbase-doc/update#%EF%B8%8F-优化模型配置" target="_blank">点击查看更多细节</a>
      </span>,
      cover: (
        <img
          alt="tour.png"
          src="https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61102900641/18bd/4819/ae33/6722de26f2b96790faeb616ce543aa80.png"
        />
      ),
      target: () => ref1.current,
    },
  ];

  return <>
    <CardCollapse
      ref={ref1}
      expandLocalKey={expandLocalKey}
      moduleKey="modelsConfig"
      defaultActiveKey={['modelsConfig']}
      items={[
        {
          key: 'modelsConfig',
          label: <Space>
            <span>
              <span style={{ fontWeight: 'bold' }}>模型设置</span>
              <DiffAlert
                type="modelsConfig"
                title="模型及参数"
                newValue={value}
                oldValue={oldValue}
                onRedo={() => {
                  // handleChange('modelConfig', oldValue);
                  onChange?.(oldValue);
                }} />
            </span>
          </Space>,
          children: <div>
            <StyledModels>
              <List
                dataSource={isMultiple ? value?.models : [value?.models?.[0]]}
                renderItem={(model: any, index) => {
                  const data: any = modelList?.find(it => it.name == model.modelName) || {};
                  return <List.Item
                    actions={[
                      isBasic && <>
                        <ModelSelectModal
                          value={value}
                          appType={appType}
                          onChange={onModelsChange}
                          trigger={
                            isBasic
                              ? <Tooltip title="修改模型">
                                <EditOutlined style={{ fontSize: 10, color: '#666', cursor: 'pointer' }} />
                              </Tooltip>
                              : <Tooltip title="添加模型">
                                <PlusOutlined style={{ fontSize: 10, color: '#666' }} />
                              </Tooltip>
                          }
                        >
                        </ModelSelectModal>
                      </>,
                      value.type === 'master_backup' && <Select
                        size="small"
                        options={[
                          { label: '主', value: 'main' },
                          { label: '备', value: 'backup' }
                        ]}
                        bordered={false}
                        popupMatchSelectWidth={false}
                        value={index === 0 ? 'main' : 'backup'}
                        onChange={val => onBackUpChange(val, model, index)}
                      />,
                      isAdvanced && <Flex align="center">
                        {isCompletion &&
                          <Flex align="center">
                            {/* <Slider
                            max={1}
                            min={0}
                            step={0.1}
                            style={{ width: 100, marginRight: 16 }}
                            value={model.ratio}
                            onChange={(val) => onRatioChange(model.modelName, val)}

                          /> */}
                            <InputNumber
                              size="small"
                              style={{ width: 60 }}
                              max={1}
                              min={0}
                              step={0.1}
                              value={model.ratio}
                              onChange={(val) => onRatioChange(model.modelName, val)}
                            />
                          </Flex>}
                      </Flex>,
                      <ModelParamsModal
                        modelProps={{
                          title: `模型设置 - ${model?.modelName}`
                        }}
                        modelName={model?.modelName}
                        value={value?.models?.find(it => it.modelName === model?.modelName)?.modelParams}
                        onChange={val => onModelParamsChange(model.modelName, val)}
                        trigger={<Tooltip title="模型设置">
                          <SettingOutlined style={{ cursor: 'pointer' }} />
                        </Tooltip>} />,
                      !isBasic && isCompletion && value?.models?.length > 1
                      && <Popconfirm title="确认删除模型？" onConfirm={() => onDelete(model?.modelName)}>
                        <DeleteOutlined />
                      </Popconfirm>
                    ]?.filter(item => item)}
                  >
                    <List.Item.Meta
                      title={<Popover
                        title={<Desc>
                          <div style={{ fontWeight: 'bold', color: '#333', fontSize: '14px', marginBottom: 8 }}>{model?.modelName}</div>
                          {data?.description}
                          <p className='fee'>费用: 输入{data.fee?.input}元 | 输出{data.fee?.output}元 (/百万Token) <a href={data.url} target='_blank'>模型相关介绍</a></p>
                        </Desc>}>
                        <span style={{ cursor: 'pointer' }}>
                          {model?.modelName}
                        </span>
                      </Popover>}
                    // description={data?.name}
                    />
                  </List.Item>
                }}
              />
            </StyledModels>

            <div style={{ width: '100%' }}>
              <RetryConfig
                value={value?.retryConfig?.retryCount}
                onChange={val => onRetryConfigChange(val)}
                formProps={{
                  labelCol: {},
                  wrapperCol: {}
                }} />
            </div>
          </div>,
          extra: <Flex onClick={(e) => e.stopPropagation()}>
            <Tooltip title="模型对比调试">
              <Button
                type='text'
                size='small'
                icon={<SplitCellsOutlined />}
                onClick={() => setMode('modelDebug')}
              />
            </Tooltip>
            {isCompletion && <Select
              size="small"
              disabled={!isCompletion}
              value={value?.type || 'basic'}
              onChange={onTypeChange}
              bordered={false}
              options={[
                { label: '基础', value: 'basic', description: '只可选择一个模型' },
                { label: '分流', value: 'advanced', description: '可选多个模型，各个模型使用概率总和为1' },
                { label: '主备', value: 'master_backup', description: '可选一个主要模型，多个备选模型' }
              ]}
              popupMatchSelectWidth={false}
              optionRender={op => {
                return <div>
                  <div>
                    <Typography.Text strong>{op.label}</Typography.Text>
                  </div>
                  <Typography.Text type="secondary">{op.data?.description}</Typography.Text>
                </div>
              }}
            />}
            {isMultiple && <ModelSelectModal
              value={value}
              appType={appType}
              onChange={onModelsChange}
              trigger={
                isBasic
                  ? <Tooltip title="修改模型">
                    <EditOutlined style={{ fontSize: 10, color: '#666' }} />
                  </Tooltip>
                  : <Tooltip title="添加模型">
                    <PlusOutlined style={{ fontSize: 10, color: '#666', cursor: 'pointer' }} />
                  </Tooltip>
              }
            >
            </ModelSelectModal>}
          </Flex>
        }
      ]}
    >
    </CardCollapse>
    <Tour open={open} onClose={handleClose} steps={steps} />
  </>
};

export default ModelsCard;