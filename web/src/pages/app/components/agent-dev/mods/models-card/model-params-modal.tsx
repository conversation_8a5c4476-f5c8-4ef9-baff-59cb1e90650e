import ModelParamsForm from "@/components/MultipleModelSetting/ModelParamsForm";
import { useGlobalState } from "@/hooks/useGlobalState";
import { Modal } from "antd";
import { useEffect, useState } from "react";

const ModelParamsModal = (props) => {
  const { trigger, modelName, value: parentValue, modelProps, onChange } = props;

  const [open, setOpen] = useState(false);
  const [value, setValue] = useState();

  const { globalState } = useGlobalState();
  const { modelList, app } = globalState;

  // useEffect(() => {
  //   setValue(parentValue);
  // }, [parentValue]);

  const onOpen = () => {
    setValue(parentValue);
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const handleModelParamsChange = val => {
    setValue(val);
  }

  const onOk = () => {
    onChange?.(value);
    onCancel();
  }


  return <>
    <span onClick={onOpen}>{trigger || '模型设置'}</span>
    <Modal title="模型设置"
      {...modelProps}
      open={open} onCancel={onCancel} onOk={onOk}>
      <ModelParamsForm
        modelName={modelName}
        modelList={modelList}
        app={app}
        value={value}
        onChange={val => handleModelParamsChange(val)}

      />
    </Modal>
  </>
};

export default ModelParamsModal;
