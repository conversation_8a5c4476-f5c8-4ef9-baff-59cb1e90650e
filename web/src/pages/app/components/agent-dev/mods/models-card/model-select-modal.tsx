import ModelModal from "@/components/model-modal";
import { useGlobalState } from "@/hooks/useGlobalState";
import { IAppType } from "@/interface";

const ModelSelectModal = (props) => {
  const { trigger, value, appType, onChange } = props;
  const { globalState } = useGlobalState();
  const { modelList } = globalState;

  const onConfirm = val => {
    const newValue = appType === IAppType.AgentCompletion ? val?.selectedModels : (val?.selectedModels?.[0] ? [val?.selectedModels?.[0]] : []);
    const newModels = newValue?.map((modelItem, index) => {
      const targetModel = modelList.find((it) => it.name === modelItem.name);
      const defaultModelParams = Object.keys(targetModel?.config || {})?.reduce((acc, cur) => {
        acc[cur] = targetModel?.config?.[cur].default
        return acc;
      }, {});
      return {
        modelName: modelItem.name,
        providerKind: targetModel?.providerKind || 'openai',
        model: targetModel,
        modelParams: defaultModelParams,
        ratio: index === 0 ? 1 : 0
      }
    });
    onChange(newModels);
  };

  return <>
    <ModelModal
      title="选择模型"
      modelList={modelList}
      disabledList={value?.models?.map(val => new RegExp(`^${val?.modelName}$`))}
      onConfirm={onConfirm}
      mode="single"
      required
    >
      <span >{trigger || '添加模型'}</span>
    </ModelModal>
  </>
};

export default ModelSelectModal;