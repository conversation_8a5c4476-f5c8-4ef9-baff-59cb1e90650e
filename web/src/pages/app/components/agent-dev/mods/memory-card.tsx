import { Slider, Radio, InputNumber, Space, Tooltip } from 'antd';
import CardCollapse from './card-collapse';
import styled from 'styled-components';
import DiffAlert from './diff-alert';
import { InfoCircleFilled, InfoCircleOutlined } from '@ant-design/icons';

type MemoryConfig = {
  shortMemoryRound: number;
  longMemoryType: MemoryType;
};

type MemoryType = 'NONE' | 'LOCAL' | 'PUBLIC';

type MemoryCardProps = {
  value: MemoryConfig | undefined;
  oldValue: MemoryConfig | undefined;
  onChange: (value: MemoryConfig) => void;
  expandLocalKey: string;
  disabled?: boolean;
};

const MEMORY_TYPES = [
  { value: 'NONE', label: '无长期记忆' },
  { value: 'LOCAL', label: '独立记忆' },
  { value: 'PUBLIC', label: '共享记忆' },
];

const getLongMemoryType = (value) => {
  return MEMORY_TYPES.find((item) => item.value === value)?.label || '无长期记忆';
};

const LabelWrapper = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .label {
    width: 80px;
    flex-shrink: 0;
  }

  .content {
    flex: 1;
    display: flex;
    align-items: center;
  }
`;

/**
 * 记忆diff字符串格式化 (在get-diff-str.ts中使用)
 *
 * @param value MemoryConfig
 * @returns string
 */
export const memoryStr = (value: MemoryConfig | undefined) => {
  let res = `
【短期记忆】: ${value?.shortMemoryRound || 0}轮,
【长期记忆】: ${getLongMemoryType(value?.longMemoryType || 'NONE')}
    `;
  return res;
};

/**
 * 记忆卡片(在single-setting.tsx中使用)
 *
 * @param props MemoryCardProps
 * @returns React.ReactNode
 */
const MemoryCard = (props: MemoryCardProps) => {
  const { value, oldValue, onChange, expandLocalKey, disabled } = props;

  const handleShortMemoryChange = (rounds: number) => {
    onChange({
      ...value,
      shortMemoryRound: rounds,
    });
  };

  const handleLongMemoryChange = (type: MemoryType) => {
    onChange({
      ...value,
      longMemoryType: type,
    });
  };

  return (
    <CardCollapse
      expandLocalKey={expandLocalKey}
      moduleKey="memoryConfig"
      collapsible={disabled ? "disabled" : 'icon'}
      items={[
        {
          key: 'memoryConfig',
          label: (
            <Space>
              <span>
                <span style={{ fontWeight: 'bold' }}>记忆设置</span>
                <DiffAlert
                  title="记忆设置"
                  type="memoryConfig"
                  newValue={value}
                  oldValue={oldValue}
                  onRedo={() => {
                    onChange(oldValue);
                  }}
                />
              </span>
              <Tooltip title="可以长时间记住用户的聊天中的关键信息。当时在平台开启长期记忆后，虚拟人会自动记忆用户的一些信息">
                <InfoCircleOutlined />
              </Tooltip>
            </Space>
          ),
          children: (
            <div style={{ padding: '8px 0' }}>
              <LabelWrapper>
                <Space className="label">短期记忆
                  <Tooltip title="上下文记录的对话轮次数">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
                <div className="content">
                  <Slider
                    style={{ flex: 1, marginRight: 16 }}
                    min={0}
                    max={100}
                    value={value?.shortMemoryRound || 0}
                    onChange={handleShortMemoryChange}
                  />
                  <InputNumber
                    min={0}
                    max={100}
                    value={value?.shortMemoryRound || 0}
                    onChange={handleShortMemoryChange}
                  />
                </div>
              </LabelWrapper>

              <LabelWrapper>
                <Space className="label">长期记忆
                  <Tooltip title={<div>
                    <div>AI 自动总结的有效记忆</div>
                    <div>独立记忆：表示该配置单独的记忆</div>
                    <div>共享记忆：整个虚拟人所有配置共享的记忆</div>
                    <a rel="noopener"
                      target='_blank' href='https://music-doc.st.netease.com/st/langbase-doc/update/2025-03-27#-%E8%99%9A%E6%8B%9F%E4%BA%BA%E6%94%AF%E6%8C%81%E9%95%BF%E6%9C%9F%E8%AE%B0%E5%BF%86%E8%B0%83%E8%AF%95'>
                      查看详情
                    </a>
                  </div>}>
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
                <div className="content">
                  <Radio.Group
                    value={value?.longMemoryType || 'NONE'}
                    onChange={(e) => handleLongMemoryChange(e.target.value)}
                    optionType="button"
                    buttonStyle="solid"
                  >
                    {MEMORY_TYPES.map((type) => (
                      <Radio.Button key={type.value} value={type.value}>
                        {type.label}
                      </Radio.Button>
                    ))}
                  </Radio.Group>
                </div>
              </LabelWrapper>
            </div>
          ),
        },
      ]}
    />
  );
};

export default MemoryCard;
