import React, { useState, useEffect, useCallback } from "react"
import classNames from "classnames"
import { styled } from "styled-components"
import type { TableColumnsType } from 'antd';
import { DeleteOutlined, PlusCircleOutlined } from "@ant-design/icons"
import { Collapse, Table, Input, Button, Card, Col, Drawer, List, Modal, Pagination as PaginationAnt, Row, Space, Tooltip, Typography, message } from "antd"
import { useQuery } from '@/hooks/useQuery';
import { useRequest } from "ahooks"
import { KnowledgeApi, DocumentApi } from '@/api';
import { Pagination } from "@/interface"
import { useGlobalState } from "@/hooks/useGlobalState"
import CardCollapse from "./card-collapse";
import DiffAlert from "./diff-alert";
const { Search } = Input;
const { Paragraph } = Typography

const PAGE_SIZE = 10

interface KnowledgeType {
  name: string;
  description: string;
  collectionId: number;
  id: string;
  documentCount: number;
  docs?: Doc[];
}

interface Doc {
  id: string;
  title: string;
  source: string;
  type: string;
  doc_id?: number;
}

interface KnowledgeToChooseProps {
  selected?: boolean;
  knowledge: KnowledgeType;
  onClick?: () => void;
}

const KnowledgeToChooseCardStyled = styled(Card)`
  height: 100%;
  &.selected {
      position: relative;
      cursor: default;
  }

  &.selected::before {
    content: "";
    border-radius: 8px;
    background-color: rgba(0,0,0,0.1);
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    cursor: default;
  }

  &:hover {
    box-shadow: 6px 6px 16px -8px rgba(0,0,0,0.08);
  }

  .ant-card-head {
    min-height: 40px;
  }
`

const KnowledgeToChoose = (props: KnowledgeToChooseProps) => {
  const { knowledge, onClick, selected = false } = props

  const onItemClick = useCallback(() => {
    onClick?.()
  }, [onClick])

  return <KnowledgeToChooseCardStyled
    style={{ cursor: 'pointer' }}
    className={classNames({ 'selected': selected })}
    title={knowledge.name}
    onClick={onItemClick}
  >
    <Paragraph
      ellipsis={{ rows: 3, tooltip: { title: knowledge.description } }}
      style={{ whiteSpace: "pre-wrap" }}>
      <div>{knowledge.description}</div>
      <div>文档数量：<b>{knowledge.documentCount}</b></div>
    </Paragraph>
  </KnowledgeToChooseCardStyled>
}

interface DocChooseDrawerProps {
  onClose: () => void;
  onChange: (any) => void;
  open: boolean;
  knowledge: KnowledgeType;
}

const columns: TableColumnsType<Doc> = [
  {
    title: '标题',
    dataIndex: 'title',
  },
  {
    title: '来源',
    dataIndex: 'source',
  },
  {
    title: '类型',
    dataIndex: 'type',
  },
];

const DocChooseDrawer = (props: DocChooseDrawerProps) => {
  const { onClose, onChange, open, knowledge } = props

  const [showInnerDoc, setShowInnerDoc] = useState<boolean>(false);
  const [pagination, setPagination] = useState<Pagination>({ pageNumber: 1, pageSize: PAGE_SIZE })
  const [currentKnowledge, setCurrentKnowledge] = useState<KnowledgeType>();

  const [knowledgeList, setKnowledgeList] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [keyword, setKeyword] = useState<string>();
  const [tempKeyword, setTempKeyword] = useState<string>();

  const [docList, setDocList] = useState<Doc[]>();
  const [docTotal, setDocTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>();
  const [selectedRows, setSelectedRows] = useState<Doc[]>();

  const { parsedQuery } = useQuery();
  const { globalState } = useGlobalState()
  const { workspace, group } = globalState;

  useRequest(() => KnowledgeApi.listWithPage(workspace?.id, group?.id, keyword, pagination.pageNumber, PAGE_SIZE),
    {
      refreshDeps: [pagination.pageNumber, keyword],
      ready: !!(workspace?.id && group?.id),
      onSuccess: (res) => {
        setKnowledgeList(res.items);
        setTotal(res.total);
      },
    })

  const getDocList = () => {
    if (!currentKnowledge || !currentKnowledge.id) {
      return;
    }
    setLoading(true);
    DocumentApi.listWithPage(`${parsedQuery.workspaceId}`, currentKnowledge.id, currentKnowledge.collectionId, current, PAGE_SIZE).then((res) => {
      const docs = res.items.map(item => {
        return {
          id: item.id,
          doc_id: item.doc_id,
          title: item.title,
          type: item.type,
          source: item.source,
        }
      });
      setDocList(docs);
      setDocTotal(res.total);
      setLoading(false);
    });
  };

  useEffect(() => {
    if (currentKnowledge?.id) {
      getDocList();
    }
  }, [currentKnowledge?.id, current]);

  const onSearch = (value) => {
    setPagination({
      pageNumber: 1,
      pageSize: PAGE_SIZE
    })
    setKeyword(value);
  }

  const onSelectChange = (newSelectedRowKeys: React.Key[], selectedRows: Doc[]) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys, selectedRows);
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRows(selectedRows);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    preserveSelectedRowKeys: true,
  };

  const onTableChange = (pagination) => {
    setCurrent(pagination.current);
  }

  return <>
    <Drawer
      width='50vw'
      title="知识库查询"
      open={open}
      onClose={onClose}
    >
      <Search placeholder="搜索" value={tempKeyword} onChange={(e) => setTempKeyword(e.target.value)} onSearch={onSearch} style={{ width: 256, marginBottom: 16 }} />
      <Row gutter={[8, 8]}>
        {knowledgeList.map((item) => {
          return (
            <Col xs={24} sm={24} md={24} lg={24} xl={12} xxl={12}>
              <KnowledgeToChoose
                selected={knowledge && item.id === knowledge.id}
                onClick={() => {
                  console.log('knowledge', knowledge);
                  const { createdBy, updatedBy, createdAt, updatedAt, ...others } = item;
                  setCurrentKnowledge(others);
                  setShowInnerDoc(true);
                  if (item.id === knowledge.id) {
                    if (knowledge.docs) {
                      const docIds = knowledge.docs.map(item => item.doc_id!);
                      setSelectedRows(knowledge.docs);
                      setSelectedRowKeys(docIds);
                    }
                  }
                }}
                key={item.id}
                knowledge={item}
              />
            </Col>)
        })}
        <Row style={{ width: "100%", marginTop: "8px", justifyContent: 'flex-end' }}>
          <PaginationAnt total={total} {...pagination} onChange={(page) => setPagination({ ...pagination, pageNumber: page })} />
        </Row>
      </Row>
    </Drawer>
    <Drawer
      width='50vw'
      open={showInnerDoc}
      onClose={() => setShowInnerDoc(false)}
      extra={(
        <Button
          type="primary"
          onClick={() => {
            setShowInnerDoc(false);
            onClose();
            onChange({
              ...currentKnowledge,
              docs: selectedRows
            });
          }}>
          确认添加
        </Button>
      )}
      title={currentKnowledge?.name}
    >
      {
        selectedRowKeys && selectedRowKeys.length ?
          <p style={{ marginTop: 0 }}>已选择 {selectedRowKeys.length} 个文档</p> :
          <p style={{ marginTop: 0 }}>默认使用全部文档</p>
      }
      <Table
        rowKey="doc_id"
        rowSelection={rowSelection}
        columns={columns}
        dataSource={docList}
        onChange={onTableChange}
        pagination={{
          total: docTotal,
          pageSize: PAGE_SIZE,
          current
        }}
        loading={loading}
      />
    </Drawer>
  </>
};

const Ellipsis = styled.p`
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  margin: 0px;
`

export interface KnowledgeProps {
  doc: Doc;
  onDelete: () => void,
}

export const ChoosenDocItem = (props: KnowledgeProps) => {
  const { doc, onDelete } = props
  const [showDeleteModa, setShowDeleteModal] = useState<boolean>(false)

  return <div style={{ columnGap: '8px', padding: '8px', display: 'flex', border: '1px solid #c8dffc', borderRadius: '8px', backgroundColor: '#e7f1fe', width: "100%" }}>
    <Tooltip title={doc.title}>
      <div style={{ width: "0", flex: '2 1 auto' }}>
        <Ellipsis style={{ fontWeight: '600' }}>
          {doc.title}
        </Ellipsis>
      </div>
    </Tooltip>
    <Tooltip title={doc.source}>
      <div style={{ width: "0", flex: "1 1 auto" }}>
        <Ellipsis>
          {doc.source}
        </Ellipsis>
      </div>
    </Tooltip>
    <Tooltip title={doc.type}>
      <div style={{ width: "0", flex: "1 1 auto" }}>
        <Ellipsis>
          {doc.type}
        </Ellipsis>
      </div>
    </Tooltip>
    <div>
      <DeleteOutlined onClick={() => setShowDeleteModal(true)} style={{ cursor: 'pointer' }} />
    </div>
    <Modal
      open={showDeleteModa}
      onCancel={() => setShowDeleteModal(false)}
      onOk={(e) => { e.stopPropagation(); onDelete() }}
    >
      <div>确定不再使用文档 {doc.title} 吗？</div>
    </Modal>
  </div>
}

export interface ToolsCardProps {
  knowledge: KnowledgeType;
  oldValue: KnowledgeType;
  onDocsChange: (KnowledgeType) => void;
  expandLocalKey: string;
}

export const KnowledgeCard = (props: ToolsCardProps) => {
  let { knowledge, onDocsChange, oldValue, expandLocalKey } = props;
  const [open, setOpen] = useState(false);

  const onDocChoose = (knowledges) => {
    if (knowledges) {
      message.info('添加知识库后，注意使用合适的提示词！');
    }
    onDocsChange(knowledges);
  };

  const onDocsRemoved = (doc) => {
    const newDocs = knowledge.docs ? knowledge.docs.filter(item => {
      return item.id !== doc.id;
    }) : [];
    onDocsChange({
      ...knowledge,
      docs: newDocs
    });
  };

  const onKnowledgeRemoved = () => {
    onDocsChange(undefined);
  };

  const genExtra = (docs) => (
    <DeleteOutlined
      onClick={(event) => {
        event.stopPropagation();
        Modal.confirm({
          content: `确定不再使用知识库 ${docs.name} 吗？`,
          onOk: onKnowledgeRemoved
        });
      }}
    />
  );

  return (
    <CardCollapse
      // defaultActiveKey={knowledge && knowledge.id ? ['knowledge'] : []}
      expandLocalKey={expandLocalKey}
      moduleKey="knowledge"
      items={[
        {
          key:'knowledge',
          label: <span>
            <span style={{ fontWeight: 'bold' }}>知识库（旧，已废弃）</span>
            <DiffAlert
              title="知识库"
              type="knowledge"
              newValue={knowledge}
              oldValue={oldValue}
              onRedo={() => {
                onDocsChange(oldValue);
              }}
            />
          </span>,
          children: <div>
            {
              knowledge && knowledge.id &&
              <Collapse
                items={[{
                  key: '1',
                  label: <><b>{knowledge.name}</b> {knowledge.docs?.length ? `选择 ${knowledge.docs?.length} 个文档` : '使用全部文档'}</>,
                  extra: genExtra(knowledge),
                  showArrow: !!(knowledge && knowledge.docs && knowledge.docs && knowledge.docs.length),
                  children: (
                    <List
                      dataSource={knowledge ? knowledge.docs : []}
                      renderItem={(item) => {
                        return (
                          <List.Item key={item.id} style={{ borderBlockEnd: 'none' }}>
                            <ChoosenDocItem
                              doc={item}
                              onDelete={() => {
                                onDocsRemoved(item);
                              }}
                            />
                          </List.Item>
                        )
                      }}
                    />
                  )
                }]}
              />
            }
            <DocChooseDrawer knowledge={knowledge} onChange={onDocChoose} onClose={() => setOpen(false)} open={open} />

          </div>,
          extra:
            <Space style={{ cursor: 'pointer' }} onClick={() => setOpen(true)}>
              <PlusCircleOutlined />
              <div>添加</div>
            </Space>

        }
      ]}>
      <DocCardStyled
        title="知识库"

      >

      </DocCardStyled>
    </CardCollapse>
  )
}

const DocCardStyled = styled(Card)`

  .ant-card-head {
    min-height: 40px;
  }

  // .ant-card-body {
  //   max-height: 169px;
  // }
  
  .ant-list-item {
    padding: 0px;
    padding-top: 4px;
    border-block-end: none;
  }

  .ant-list-pagination {
    margin-block-start: 8px;
  }

  .ant-collapse-content .ant-list {
    max-height: 200px;
    overflow-y: scroll;
    box-sizing: border-box;
  }
`;
