import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Card, Form, Input, Select, message, Checkbox, Modal, Tag, Spin, Flex, Radio } from 'antd';
import { AppApi } from '@/api/app';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';
import ApprovalModal from '@/components/approval-modal';
import { useGlobalState } from '@/hooks/useGlobalState';
import { ChannelsApi } from '@/api';
import { includes } from 'lodash';
import { ConfigCardCnMap } from '@/interface';


const validateKeys = Object.keys(ConfigCardCnMap)

const { TextArea } = Input;
const { Option } = Select;

interface PublishDrawerProps {
  visible: boolean;
  onClose: () => void;
  onOk: (data: any) => void;
  data: any;
}

const ChannelsSelect = (props: any) => {
  const tabList = [{
    key: 'MUSIC',
    tab: '云音乐账号',
  }]

  const { channels, loading, data, value, onChange } = props;
  const [visible, setVisible] = useState(false);
  const [activeKey, setActiveKey] = useState(tabList[0].key);
  const [accountForm] = Form.useForm();




  useRequest(() => {
    if (!visible) {
      return Promise.resolve([])
    }
    return AppApi.getAccountList({ appId: data.appId }).then(res => res?.accountInfoList)
  }, {
    refreshDeps: [data.appId, visible],
    onSuccess: (res) => {
      if (res?.length) {
        accountForm.setFieldsValue(res?.find(v => v.accountType === activeKey))
      }
    }
  });


  const handleSave = () => {
    accountForm.validateFields().then((values) => {
      AppApi.configAccount({
        ...values,
        appId: data.appId,
        accountType: activeKey,
      }).then(() => {
        message.success('保存成功');
        setVisible(false);
      })
    })
  }

  const currentChannel = channels?.find(v => v?.setting?.settingId === props?.data.settingId);

  const isSettedChannel = props?.data.settingId && channels?.map(v => v?.setting?.settingId)?.includes(props?.data.settingId);

  const beforeChange = async (checkedValue) => {
    if (!checkedValue) {
      onChange?.([]);
      return;
    }
    const { config } = data || {};
    const usedSkills = Object.keys(config)?.map(configKey => {
      const item = config[configKey];
      if (!validateKeys.includes(configKey)) return;
      if (!item) return;
      if (typeof item === 'string' && !item) return;
      if (Array.isArray(item) && item?.length) return configKey;
      if (typeof item === 'object') {
        if (Object.keys(item)?.length) {
          return configKey
        }
      }
    })?.filter(key => key);
    // if (checkedValue && usedSkills?.length) {
    //   const res = await ChannelsApi.getChannelSkills({ channel: checkedValue });

    //   const invalidSkills = usedSkills?.reduce((acc, cur) => {
    //     if (res?.functionList?.includes(cur)) return acc;
    //     return acc.concat(ConfigCardCnMap[cur] || cur)
    //   }, []);

    //   if (invalidSkills?.length) {
    //     Modal.confirm({
    //       title: '渠道无效配置确认',
    //       content: <div>当前渠道下，{invalidSkills?.map(item => <Tag key={item} color='warning'>{item}</Tag>)}配置无效，请确认是否要选择该渠道</div>,
    //       onOk: () => {
    //         onChange([checkedValue])
    //       }
    //     });
    //     return;
    //   }
    //   return;

    // }

    onChange([checkedValue]);

  }

  return <Card title="发布平台" extra={<Button type="link" onClick={() => setVisible(true)}>账号配置</Button>}>
    <Spin spinning={loading}>
      <Radio.Group value={value?.[0]} onChange={(e) => {
        // console.log('checkedValues', checkedValues);
        const checkedValue = e.target.checked ? e.target.value : undefined;
        beforeChange(checkedValue);
      }} >
        <div style={{ display: 'flex', flexDirection: 'column', gap: 10 }}>
          {/* 当前setting已经选择过渠道的话不可选择 */}
          {/* TODO: 后续前端逻辑改成单选 */}
          {channels?.map(v => <Radio value={v?.channel?.type}
            disabled={v?.setting?.settingId || isSettedChannel}>
            <div style={{ display: 'flex', height: 28, alignItems: 'center', gap: 6 }}>
              <img width={20} height={20} src={v?.channel?.icon} alt="" />
              {v?.channel?.name}
              {!!v?.setting?.settingId && <Tag color="purple">【{props?.data.settingId === v?.setting?.settingId ? '当前' : v?.setting?.name}】配置已占用</Tag>}
            </div>
          </Radio>)}
        </div>
      </Radio.Group>
    </Spin>
    <Modal
      visible={visible}
      title="绑定账号"
      centered
      style={{
        left: 400
      }}
      footer={null}
      onCancel={() => setVisible(false)}
      getContainer={props.drawerRef.current} >
      <Form form={accountForm} labelCol={{ span: 4 }}>
        <Card style={{ marginTop: 16 }} size="small" type="inner" tabList={tabList} tabProps={{
          activeKey: 'MUSIC',
          onChange: (key) => {
            setActiveKey(key);
          }
        }}>
          <Form.Item label="id" name="id" hidden><Input /></Form.Item>
          <Form.Item label="账号ID" name="accountId" rules={[{
            required: true, message: '请输入账号Id'
          }]}>
            <Input placeholder="请输入账号Id" />

          </Form.Item>
          <Form.Item label="描述" name="description">
            <TextArea placeholder="请输入描述" />

          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={handleSave}>保存</Button>
          </Form.Item>
        </Card>
      </Form>
    </Modal>
  </Card>
}

const PublishDrawer: React.FC<PublishDrawerProps> = ({ visible, onClose, onOk, data }) => {
  const { updateGlobalState, globalState } = useGlobalState();
  const { user, app } = globalState;
  const { extraData } = app?.extInfo?.approve || {};
  const [form] = Form.useForm();
  const [channels, setChannels] = useState([]);
  const drawerRef = React.useRef<any>();

  const afterOpenChange = () => {
    if (extraData?.settingId && data?.settingId === extraData?.settingId) {
      form.setFieldsValue({
        channels: extraData?.deployValue?.channels,
        description: extraData?.deployValue?.description,
        version: extraData?.deployValue?.version,
      })
    } else {
      return form.resetFields()
    }
  }
  // console.log('channels', channels)

  const { loading } = useRequest(() => {
    if (!visible) {
      return Promise.resolve([]);
    }
    return AppApi.publishChannels({
      appId: data.appId,
      settingId: data.settingId
    }).then(res => res?.channels)
  },
    {
      onSuccess: (res) => {
        const publishedChannels = res?.filter(v => v?.setting?.settingId === data?.settingId).map(v => v?.channel?.type)
        setChannels(res)
        if (publishedChannels?.length) {
          form.setFieldValue('channels', publishedChannels)
        }
      },
      refreshDeps: [visible]
    })


  const onFinish = () => {
    form.validateFields().then((values) => {

      const config = data?.config;
      delete config.triggers;

      AppApi.configPublish({
        ...data,
        ...values,
        config: {
          ...data.config,
          message: `${values.version}--${values?.description}`,
        },
        channels: values.channels.map((item) => ({
          name: item,
        })),
      }).then((res) => {
        message.success('发布成功');
        onOk(res);
      }).catch((err) => {
        message.error(err?.message);
      })
    });
  };


  const preCheck = () => {
    return form.validateFields().then((values) => {
      console.log('values', values)
      return {
        channel: channels.filter(v => values.channels.includes(v?.channel?.type)).map(v => v?.channel?.name).join(','),
        reason: values.description,
        deployValue: values,
      }

    })
  }

  return (
    <>
      <Drawer
        title="发布"
        width={1000}
        afterOpenChange={afterOpenChange}
        onClose={onClose}
        headerStyle={{ background: '#F4F4F5' }}
        bodyStyle={{ paddingBottom: 80, background: 'rgba(250, 250, 250, 1)' }}
        extra={
          <ApprovalModal
            preCheck={preCheck}
            updateGlobalState={updateGlobalState}
            app={data?.app}
            setting={data?.setting}
            settingId={data?.settingId}
            appName={data?.app?.name}
            action="DEPLOY" // 或其他操作类型
            // appType="ReactNative" // 应用类型
            appType="vitrual" // 应用类型
            // force={true}
            operator={user?.email}
            onApprove={() => {
              // 处理审批通过后的逻辑
              console.log('审批已创建');
            }}
          >
            <Button onClick={() => onFinish()} type="primary">
              发布
            </Button>
          </ApprovalModal>
        }
        visible={visible}
      // 要比diff drawer高
      >
        <div ref={drawerRef}>
          <Form layout="vertical" form={form}>
            <Form.Item
              name="type"
              label="发布位置"
              initialValue="ONLINE"
              rules={[{ required: true, message: '请选择发布位置' }]}
            >
              <Select placeholder="请选择发布位置">
                <Option value="ONLINE">线上</Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="version"
              label="版本号"
              hidden={true}
              initialValue={dayjs().format('YYYYMMDDHHmmss')}
              rules={[{ required: true, message: '请输入版本号' }]}
            >
              <Input placeholder="请输入版本号" />
            </Form.Item>
            <Form.Item
              name="description"
              label="发布描述"
              rules={[{ required: true, message: '请输入发布描述' }]}
            >
              <TextArea rows={4} placeholder="请输入发布描述" />
            </Form.Item>
            <Form.Item
              initialValue={channels?.filter(v => v?.setting?.settingId === data?.settingId).map(v => v?.channel?.type)}
              name="channels"
              label="发布平台"
              rules={[{
                required: true, message: '请选择发布平台', validator: (rule, value) => {
                  if (value?.length === 0) {
                    return Promise.reject('请选择发布平台');
                  } else {
                    return Promise.resolve();
                  }
                }
              }]}
            >
              <ChannelsSelect drawerRef={drawerRef} data={data} channels={channels} loading={loading} />
            </Form.Item>
          </Form>
        </div>
      </Drawer>
    </>
  );
};

export default PublishDrawer;