import { But<PERSON>, <PERSON>lapse, Flex, Image, List, Popconfirm, Space, Tooltip } from 'antd';
import CardCollapse from '../card-collapse';
import KnowledgeDrawer from './knowledge-drawer';
import {
  CheckCircleFilled,
  DeleteOutlined,
  InfoCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import styled from 'styled-components';
import DiffAlert from '../diff-alert';
import KnowledgeConfig, { defaultKnowledgeConfig, searchTypeMap } from './knowledge-config';
import { knowledgeTypeSchema } from '@/pages/new-knowledge/config';

const StyledList = styled(List)`
  .ant-list-item-meta-title {
    text-align: left;
  }
`;

const knowledgeTypes = knowledgeTypeSchema.reduce((acc, cur) => {
  acc[cur.key] = cur;
  return acc;
}, {});

export const getKnowledgeConfigStr = (value) => {
  if (!value) return '';

  let str = `
知识库配置:
  【搜索模式】: ${searchTypeMap[value?.searchConfig?.searchType || defaultKnowledgeConfig.searchType]}
  【最大召回数量】: ${value?.searchConfig?.filter?.limit || defaultKnowledgeConfig.filter.limit}  
  【最小匹配度】: ${value?.searchConfig?.filter?.similarity || defaultKnowledgeConfig.filter.similarity} 

包含知识库:`;

  // 知识库
  value?.knowledge?.forEach((item) => {
    if (item) {
      str += `
  【知识库】: ${item.knowledgeName}
  【类型】: ${knowledgeTypes[item.type]?.label}
`;
    }
  });

  return str;
};

const KnowledgeCard = (props) => {
  const { value, onChange, appId, oldValue, modelConfig, expandLocalKey, disabled } = props;

  const onConfigChange = val => {
    onChange?.({
      ...value,
      searchConfig: val,
    });
  };

  const onKnowledgeChange = (val) => {
    onChange?.({
      ...value,
      knowledge: val,
    });
  };

  const onDeleteKnowledge = (knowledgeId) => {
    const newData = value?.knowledge?.filter((item) => item?.knowledgeId !== knowledgeId);
    onKnowledgeChange(newData);
  };

  const onDeleteFile = (knowledgeId, fileId) => {
    const newData = value?.knowledge?.map((item) => {
      const newKnowledgeItems = item?.knowledgeItems?.filter((i) => i?.id !== fileId);
      if (item?.knowledgeId !== knowledgeId) return item;
      return {
        ...item,
        knowledgeItems: newKnowledgeItems,
      };
    });
    onKnowledgeChange(newData);
  };

  const onFileChange = (knowledge, files) => {
    const knowledgeId = knowledge?.knowledgeId;
    const val = value?.knowledge;

    if (!knowledgeId) return;
    let isAdd = false;
    let newVal = val?.map((item) => {
      if (item.knowledgeId === knowledgeId) {
        isAdd = true;
        return {
          knowledgeId: item.knowledgeId,
          knowledgeName: item.name,
          useAll: false,
          knowledgeItems: files,
        };
      }
    });
    if (!isAdd) {
      newVal = [
        ...(value || []),
        {
          knowledgeId,
          knowledgeName: knowledge.name,
          useAll: false,
          knowledgeItems: files,
        },
      ];
    }
    onKnowledgeChange(newVal);
  };

  const groupValue = value?.knowledge?.reduce((acc, cur) => {
    const type = cur.type || 'text';

    if (!acc[type]) {
      acc[type] = [];
    }

    acc[type].push(cur);

    return acc;
  }, {});

  return (
    <>
      <CardCollapse
        expandLocalKey={expandLocalKey}
        moduleKey="knowledgeConfig"
        collapsible={disabled ? "disabled" : 'icon'}
        items={[
          {
            key: 'knowledgeConfig',
            label: (
              <Space>
                <span style={{ fontWeight: 'bold' }}>知识库</span>
                <DiffAlert
                  title="知识库"
                  type="knowledgeConfig"
                  newValue={value}
                  oldValue={oldValue}
                  onRedo={() => {
                    onChange(oldValue);
                  }}
                />
                <Tooltip title={<>
                  {/* 知识库能够让智能体存储和调用各种信息、规则和专业知识，例如回答问题、提供建议、辅助决策等，提升智能体的理解能力和任务执行效率。<br /> */}
                  知识库是跟组绑定的，因此，如果需要添加知识库，需要切换到对应的业务组下。<br />
                  <Image
                    src="https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/45589734596/f2fe/6908/0592/7d27a9cf91f1a886573a0541744b75ac.png"
                  />
                  <a
                    rel="noopener"
                    target='_blank' href='https://music-doc.st.netease.com/st/langbase-doc/guide/advance#4--%E5%A6%82%E4%BD%95%E6%8E%A5%E5%85%A5%E7%9F%A5%E8%AF%86%E5%BA%93--rag--%E8%83%BD%E5%8A%9B'>查看详情</a>
                </>}>
                  <InfoCircleOutlined />
                </Tooltip>
              </Space>
            ),
            extra: !disabled && (
              <div onClick={(e) => e.stopPropagation()}>
                <KnowledgeConfig
                  onChange={onConfigChange}
                  value={value?.searchConfig}
                  modelConfig={modelConfig}
                />
                <KnowledgeDrawer
                  trigger={
                    <Tooltip title='添加知识库'>
                      <Button
                        type="text"
                        size="small"
                        icon={<PlusOutlined style={{ fontSize: 10, color: '#666' }} />}
                      />
                    </Tooltip>
                  }
                  onChange={onKnowledgeChange}
                  value={value?.knowledge}
                />
              </div>
            ),
            children: (
              <div onClick={(e) => e.stopPropagation()}>
                {value?.knowledge?.length ? (
                  <>
                    <>
                      {
                        Object.keys(groupValue || {})?.map(type => {
                          return <List
                            size='small'
                            dataSource={groupValue[type]}
                            header={<Flex gap={8}>
                              {knowledgeTypes[type].icon}
                              <span style={{ fontWeight: 'bold' }}>{knowledgeTypes[type].label}</span>
                            </Flex>}
                            renderItem={(knowledge: any) => {
                              return <List.Item style={{ padding: '4px 0px' }}>
                                <Flex justify='space-between' style={{ width: '100%' }}>
                                  <Flex >{knowledge?.knowledgeName}</Flex>
                                  <Popconfirm
                                    title="确认删除该知识库?"
                                    placement="bottom"
                                    onConfirm={() => onDeleteKnowledge(knowledge?.knowledgeId)}
                                  >
                                    <Tooltip title="删除知识库">
                                      <Button
                                        icon={<DeleteOutlined style={{ color: '#999' }} />}
                                        type="text"
                                        size="small"
                                      />
                                    </Tooltip>
                                  </Popconfirm>
                                </Flex>
                              </List.Item>
                            }}
                          />
                        }

                        )
                      }
                    </>

                    {false && <Collapse
                      size="small"
                      bordered={false}
                      items={value?.knowledge?.map((knowledge) => ({
                        showArrow: false,
                        key: knowledge.knowledgeId,
                        label: knowledge.knowledgeName,
                        extra: (
                          <Flex gap={8} style={{ color: 'rgba(0,0,0,0.45)' }}>
                            {/*<FileDrawer*/}
                            {/*  trigger={*/}
                            {/*    <Tooltip title="编辑">*/}
                            {/*      <EditOutlined />*/}
                            {/*    </Tooltip>*/}
                            {/*  }*/}
                            {/*  knowledge={{*/}
                            {/*    id: knowledge?.knowledgeId,*/}
                            {/*    name: knowledge?.knowledgeName,*/}
                            {/*  }}*/}
                            {/*  onChange={(val) => onFileChange(knowledge, val)}*/}
                            {/*  value={knowledge?.knowledgeItems}*/}
                            {/*/>*/}
                            <Popconfirm
                              title="确认删除该知识库?"
                              placement="bottom"
                              onConfirm={() => onDeleteKnowledge(knowledge?.knowledgeId)}
                            >
                              <Tooltip title="删除知识库">
                                <Button
                                  icon={<DeleteOutlined style={{ color: '#999' }} />}
                                  type="text"
                                  size="small"
                                />
                              </Tooltip>
                            </Popconfirm>
                          </Flex>
                        ),
                        children: null,
                        // TODO: 暂时隐藏掉添加文档逻辑
                        childrens: (
                          <div onClick={(e) => e.stopPropagation()}>
                            {knowledge?.useAll ? (
                              <Flex gap={4}>
                                <CheckCircleFilled style={{ color: 'green' }} />
                                已选择全部文档
                              </Flex>
                            ) : (
                              <StyledList
                                dataSource={knowledge?.knowledgeItems}
                                renderItem={(file: any) => {
                                  return (
                                    <List.Item
                                      actions={[
                                        <Tooltip title="删除文档">
                                          <DeleteOutlined
                                            onClick={() =>
                                              onDeleteFile(knowledge?.knowledgeId, file?.id)
                                            }
                                          />
                                        </Tooltip>,
                                      ]}
                                    >
                                      {file?.name}
                                    </List.Item>
                                  );
                                }}
                              />
                            )}
                          </div>
                        ),
                      }))}
                    />}
                  </>
                ) : (
                  <span style={{ color: '#999' }}>
                    知识库能够让智能体存储和调用各种信息、规则和专业知识，例如回答问题、提供建议、辅助决策等，提升智能体的理解能力和任务执行效率。
                  </span>
                )}
              </div>
            ),
          },
        ]}
      />
    </>
  );
};

export default KnowledgeCard;
