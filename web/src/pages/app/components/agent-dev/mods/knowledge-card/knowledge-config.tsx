import { QuestionCircleOutlined, SettingOutlined } from "@ant-design/icons";
import { Button, Flex, Form, Input, InputNumber, Modal, Radio, Slider, Switch, Tabs, Tooltip, Typography } from "antd";
import { useState } from "react";
import { ModelSetting } from '@/components/ModelSetting';
import { getDefaultConfig } from "@/utils/model-helper";
import { IAppType } from "@/interface";

export const defaultKnowledgeConfig = {
  "searchType": "SEMANTIC",
  "filter": {
    "limit": 10,
    "similarity": 0.3
  },
};

export const searchTypeMap = {
  'MIX': '混合检索',
  'FULL_TEXT': '全文检索',
  'SEMANTIC': '语义检索',

};

export const KnowledgeConfigModal = (props) => {
  const { open, onClose, onOk, value, onChange } = props;
  return <Modal title="知识库配置"
    open={open}
    onCancel={onClose}
    onOk={onOk}
  >
    <Tabs
      items={[
        {
          key: 'searchType',
          label: '搜索模式',
          children: <SearchConfig
            value={value?.searchType}
            onChange={val => onChange('searchType', val)} />
        },
        {
          key: 'filter',
          label: '搜索过滤',
          children: <FilterConfig
            value={value?.filter}
            onChange={val => onChange('filter', val)} />
        },
        // {
        //   key: 'queryRewrite',
        //   label: '问题优化',
        //   children: <QuestionModelConfig
        //     value={value?.queryRewrite}
        //     onChange={val => onChange('queryRewrite', val)}
        //     modelConfig={modelConfig}
        //   />
        // }
      ]}></Tabs>

  </Modal>
}

const KnowledgeConfig = (props) => {
  const { value: parentValue = {
    ...defaultKnowledgeConfig
  }, onChange: onParentChange, modelConfig } = props;
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState<any>({});



  const onChange = (key, val) => {
    // onParentChange({
    //   ...(value || {}),
    //   [key]: val
    // });
    setValue({
      ...(value || {}),
      [key]: val
    });
  };

  const onOpen = () => {
    setValue(parentValue);
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onOk = () => {
    onParentChange(value);
    setOpen(false);

  };

  return <>
    <Tooltip title="知识库配置">
      <Button
        size="small"
        type="text"
        icon={<SettingOutlined onClick={onOpen} style={{ fontSize: '10px', color: '#666' }} />}>
      </Button>
      {/* <SettingOutlined onClick={onOpen} style={{ fontSize: 10, color: '#666' }} /> */}
      <KnowledgeConfigModal open={open} onClose={onClose} onOk={onOk} value={value} onChange={onChange} />
    </Tooltip>

  </>
};



const SearchConfig = props => {
  const { onChange, value } = props;

  const data = [
    { label: '语义检索', value: 'SEMANTIC', desc: '基于向量的文本相关性查询，推荐在需要理解语义关联度和跨语言查询的场景使用。' },
    { label: '全文检索', value: 'FULL_TEXT', desc: '依赖于关键字的全文搜索，推荐在搜索具有特定名称、缩写词、短语或ID的场景使用。' },
    { label: '混合检索', value: 'MIX', desc: '结合全文检索和语义检索的优势，并对结果进行综合排序。' },

  ]

  return <Flex vertical gap={10}>
    {data?.map(item => {
      return <Flex key={item.value}
        align="center"
        style={{ border: '1px solid #f0f0f0', borderRadius: '8px', padding: ' 12px', background: '#fafafa', cursor: 'pointer' }}
        onClick={() => {
          if (item.value !== value) {
            onChange(item.value);
          }
        }}
      >
        <Flex vertical flex={1}>
          <Typography.Title style={{ margin: 0 }} level={5}>{item.label}</Typography.Title>
          <Typography.Text type="secondary">{item.desc}</Typography.Text>
        </Flex>
        <Flex style={{ width: '40px' }} justify="center">
          <Radio
            checked={value === item.value}
            onChange={e => {
              if (e.target.checked) {
                onChange(item.value);
              }
            }} />
        </Flex>
      </Flex>
    })}


  </Flex>
}

const FilterConfig = (props) => {
  const { onChange, value } = props;
  return <div>
    <Flex style={{ marginBottom: '10px' }}>
      <Flex align="center" style={{ marginRight: 16 }} gap={6}>
        最大召回数量
        <Tooltip title="从知识库中返回给大模型的最大段落数量，数值越大返回的内容越多">
          <QuestionCircleOutlined />
        </Tooltip>
      </Flex>
      <Flex flex={1} align="center">
        <Slider min={1}
          style={{ flex: 1, margin: '0 16px' }}
          max={10}
          value={value?.limit}
          onChange={(val) => onChange({
            ...value,
            limit: val
          })} />
        <InputNumber
          min={1}
          max={10}
          value={value?.limit}
          onChange={(val) => onChange({
            ...value,
            limit: val
          })} />
      </Flex>
    </Flex>
    <Flex>
      <Flex align="center" style={{ marginRight: 16 }} gap={6}>
        最小匹配度
        <Tooltip title="根据设置的匹配度选取段落返回给大模型，低于设定匹配度的内容不会被找回。">
          <QuestionCircleOutlined />
        </Tooltip>
      </Flex>
      <Flex flex={1} align="center">
        <Slider
          style={{ flex: 1, margin: '0 16px' }}
          min={0}
          max={0.99}
          step={0.01}
          value={value?.similarity}
          onChange={(val) => onChange({
            ...value,
            similarity: val
          })} />
        <InputNumber
          min={0}
          max={0.99}
          step={0.01}
          value={value?.similarity}
          onChange={(val) => onChange({
            ...value,
            similarity: val
          })}
        />
      </Flex>
    </Flex>
  </div>
}

const QuestionModelConfig = props => {
  const { onChange, value, modelConfig } = props;
  // const defaultConfig = getDefaultConfig();
  return <Flex vertical gap={16}>
    <Typography.Text type="secondary">开启问题优化功能，可以提高连续对话时，知识库搜索的精度。开启该功能后，在进行知识库搜索时，会根据对话记录，利用AI补全问题缺失的信息。</Typography.Text>
    <Flex>
      <Flex style={{ marginRight: 16 }}>
        使用问题优化
      </Flex>
      <Flex>
        <Switch
          checked={value?.use}
          onChange={val => onChange({
            ...value,
            use: val
          })} />
      </Flex>
    </Flex>
    {value?.use && <Flex align="center">
      <Flex style={{ marginRight: 16 }}>AI模型</Flex>
      <Flex>
        <ModelSetting
          value={value?.modelConfig || modelConfig}
          onChange={val => onChange({
            ...value,
            modelConfig: val
          })} />
      </Flex>
    </Flex>}
    {value?.use && <Flex vertical gap={8}>
      <div>对话背景描述</div>
      <Input.TextArea
        value={value?.prompt}
        onChange={ev => onChange({
          ...value,
          prompt: ev.target.value
        })}
        rows={5}
      />
    </Flex>}
  </Flex>
}






export default KnowledgeConfig;
