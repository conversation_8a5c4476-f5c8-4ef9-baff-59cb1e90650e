import { NewKnowledge } from "@/api";
import { KnowledgeModel } from "@/interface";
import { knowledgeIconMap, textIconMap } from "@/pages/new-knowledge/config";
import { <PERSON><PERSON>, Card, Drawer, Flex, Input, List, Popover, Table } from "antd";
import { useRef, useState } from "react";
import styled from "styled-components";
import FileDrawer from "./file-drawer";
import { CheckCircleFilled, CiCircleOutlined } from "@ant-design/icons";


const StyledList = styled(List)`
  .ant-list-item-meta-title {
    text-align: left;
  }
`;

const TextKnowledge = (props) => {
  const { data, onSearch: onParentSearch, value, onChange, loading } = props;
  const [knowledge, setKnowledge] = useState<KnowledgeModel>();
  const [open, setOpen] = useState(false);
  const [name, setName] = useState<string>();

  const onSearch = (val) => {
    onParentSearch(val);
  };

  const onAddDoc = val => {
    setKnowledge(val);
    setOpen(true);
  };

  const onDeleteKnowledge = knowledgeId => {
    const newValue = value?.filter(item => item.knowledgeId !== knowledgeId);
    onChange?.(newValue);

  };

  const onSelectAll = curKnowledge => {
    let isAdd = false;
    let newValue = value?.map(item => {
      if (item.knowledgeId === curKnowledge.id) {
        isAdd = true;
        return {
          knowledgeId: item.knowledgeId,
          knowledgeName: item.name,
          useAll: true,
          type: 'text'
        }
      }
    });
    if (!isAdd) {
      newValue = [...(value || []), {
        knowledgeId: curKnowledge.id,
        knowledgeName: curKnowledge.name,
        useAll: true,
        type: 'text'
      }]
    }
    onChange?.(newValue);
  }

  const onFileChange = files => {
    const knowledgeId = knowledge?.id;

    if (!knowledgeId) return;

    // const isAdd = value?.findIndex(item => item.knowledgeId === knowledgeId) > -1;
    let isAdd = false;

    let newValue = value?.map(item => {
      if (item.knowledgeId === knowledgeId) {
        isAdd = true;
        return {
          knowledgeId: item.knowledgeId,
          knowledgeName: item.name,
          useAll: false,
          knowledgeItems: files,
          type: 'text'
        }
      }
    });
    if (!isAdd) {
      newValue = [...(value || []), {
        knowledgeId,
        knowledgeName: knowledge.name,
        useAll: false,
        knowledgeItems: files,
        type: 'text'
      }]
    }

    onChange?.(newValue);
    setOpen(false);
  }

  return <>
    <Input
      placeholder="输入关键字搜索..."
      onChange={ev => {
        setName(ev.target.value);
        onSearch({ name: ev.target.value, type: 'text' });
      }}
      style={{ width: 300 }}
    />
    <StyledList
      // grid={{ gutter: 16, column: 2 }}
      loading={loading}
      dataSource={data}
      renderItem={(item: KnowledgeModel) => {
        const { name, description, type, id, documentCount } = item;
        const selectedValue = value?.find(d => d.knowledgeId === id)
        const knowledgeItems = selectedValue?.knowledgeItems;

        return <List.Item
          actions={[
            // <Button onClick={() => onAddDoc(item)} size="small">选择文档</Button>,
            <ToggleButton data={selectedValue}
              onDeleteKnowledge={() => onDeleteKnowledge(id)}
              onSelectAll={() => onSelectAll(item)}
            />]}
        >
          <List.Item.Meta
            avatar={<span style={{ fontSize: '20px' }}>
              {knowledgeIconMap[type]}
            </span>}
            title={name}
            description={<div>
              <div>{description}</div>
              <div style={{ fontSize: '12px' }}>
                {/* {selectedValue?.useAll && <Flex gap={4} style={{ fontSize: '12px' }}>
                  <CheckCircleFilled style={{ color: 'green' }} />
                  <span>
                    已添加所有文档
                  </span>
                </Flex>} */}
                <div>共 {documentCount} 篇文档</div>
                {knowledgeItems?.length > 0 && !selectedValue.useAll && <div >
                  <Popover content={<div>{
                    knowledgeItems?.map(file => {
                      return <div>{file.name}</div>
                    })
                  }</div>}>
                    <span style={{ cursor: 'pointer' }}>
                      已添加 <a>{knowledgeItems?.length}</a> 个文档
                    </span>
                  </Popover>
                </div>}
              </div>
            </div>} />
        </List.Item>
      }}
    >
    </StyledList>
    <FileDrawer
      knowledge={knowledge}
      open={open}
      onChange={onFileChange}
      onClose={() => { setOpen(false) }}
      value={value?.find(item => item?.knowledgeId === knowledge?.id)?.knowledgeItems}
    />
  </>

};

const ToggleButton = (props) => {
  const { data, onDeleteKnowledge, onSelectAll: onParentSelectAll } = props;
  const { useAll, knowledgeItems } = data || {};

  const [hovering, setHovering] = useState(false);

  const onMouseEnter = () => {
    setHovering(true);
  }

  const onMouseLeave = () => {
    setHovering(false);
  }

  const onRemove = () => {
    if (hovering) {
      onDeleteKnowledge()
    }
  }

  const onSelectAll = () => {
    onParentSelectAll?.()
  }

  const basicButtonProps = {
    style: {
      width: '130px'
    }
  };

  if (useAll) {
    return <div
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {hovering ?
        <Button
          {...basicButtonProps}
          danger
          onClick={onRemove}
          size="small"
        >移除知识库</Button> :
        <Button
          {...basicButtonProps}
          size="small"
          type="link"
        >
          <CheckCircleFilled style={{ color: 'green' }} />
          已添加知识库</Button>}
    </div>
  }

  if (knowledgeItems?.length) {
    return <div
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}>
      {hovering ?
        <Button
          danger
          size="small"
          onClick={onRemove}
          {...basicButtonProps}
        >
          移除知识库
        </Button>
        :
        <Button
          size="small"
          type="link"
          {...basicButtonProps}
        >
          已添加 {knowledgeItems?.length} 个文档
        </Button>
      }

    </div>

  }

  return <Button
    onClick={onSelectAll}
    size="small"
    type="primary"
    {...basicButtonProps}
  >
    添加知识库
  </Button>

}

export default TextKnowledge;
