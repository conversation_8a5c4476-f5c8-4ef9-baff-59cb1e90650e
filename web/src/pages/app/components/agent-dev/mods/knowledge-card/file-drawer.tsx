import { NewKnowledge } from "@/api";
import { useQuery } from "@/hooks/useQuery";
import TextUploader from "@/pages/new-knowledge/components/text-uploader";
import { textIconMap } from "@/pages/new-knowledge/config";
import { getFileType } from "@/pages/new-knowledge/utils";
import { <PERSON><PERSON>, Drawer, Flex, Input, Space, Table } from "antd";
import { ReactNode, useEffect, useRef, useState } from "react";

const PAGE_SIZE = 20;

interface IProps {
  knowledge: {
    id: string;
    name: string
  },
  open?: boolean;
  onChange: (data: any) => void;
  onClose?: () => void;
  trigger?: ReactNode;
  value: {
    id: string;
    name: string;
  }[];
}

const FileDrawer = (props: IProps) => {
  const { knowledge, open: parentOpen, onChange, onClose: onParentClose, trigger, value } = props;

  const [name, setName] = useState<string>();
  const [total, setTotal] = useState<number>();
  const [docs, setDocs] = useState([]);
  const [open, setOpen] = useState(parentOpen);
  const [selectedDocs, setSelectedDocs] = useState<{
    name: string;
    id: string;
  }[]>();
  const { parsedQuery } = useQuery();

  const { workspaceId, groupId } = parsedQuery;


  const docSearchTimer = useRef(null);

  useEffect(() => {
    if (parentOpen) {
      setSelectedDocs(value);
    }
    setOpen(parentOpen);
  }, [parentOpen]);

  useEffect(() => {
    if (!knowledge?.id) return;
    getDocs();
  }, [knowledge?.id]);

  const onTextUpload = () => {
    getDocs();
  }


  const getDocs = async (val?: any) => {
    const { values, total: curTotal } = await NewKnowledge.getDocumentList({
      knowledgeId: knowledge?.id,
      pageSize: PAGE_SIZE,
      page: 1,
      // 搜索key
      name,
      ...(val || {})
    });
    setTotal(curTotal);
    setDocs(values);
  }

  const onDocSearch = val => {
    if (docSearchTimer.current) {
      clearTimeout(docSearchTimer.current);
    }
    docSearchTimer.current = setTimeout(() => {
      getDocs(val);
    }, 1000);
  }

  const onAdd = () => {
    onChange?.(selectedDocs);
    if (trigger) {
      setOpen(false);
    }
  }

  const onClose = () => {
    if (trigger) {
      setOpen(false);
    }
    onParentClose?.()
  }

  const onOpen = () => {
    if (trigger) {
      setSelectedDocs(value);
      setOpen(true);
    }
  }

  return <>
    <>
      <span onClick={onOpen}>
        {trigger}

      </span>
    </>
    <Drawer
      title={knowledge?.name}
      open={open}
      extra={<Space>
        <TextUploader trigger={<Button>上传文档</Button>}
          groupId={groupId}
          workspaceId={workspaceId}
          knowledgeId={knowledge?.id}
          onChange={onTextUpload}
        />
        <Button type="primary" onClick={onAdd}>确认添加</Button>
      </Space>}
      width={800}
      maskClosable={false}
      onClose={onClose}
    >
      <Input
        placeholder="搜索文档..."
        style={{ marginBottom: '10px', width: '200px' }}
        onChange={ev => onDocSearch({ name: ev.target.value })} />
      <Table
        rowKey="knowledgeItemId"
        dataSource={docs}
        pagination={{
          showTotal: total => `共 ${total} 个文档`,
          total,
          onChange(page, pageSize) {
            getDocs({ page })
          },
        }}
        columns={[
          {
            dataIndex: 'name',
            title: '标题',
            render(value, record, index) {
              const iconType = getFileType({ name: value });

              return <Flex>
                <Flex style={{ width: '20px' }}>{textIconMap[iconType] || ' '}</Flex>
                <Flex>{value}</Flex>
              </Flex>
            },
          },
        ]}
        rowSelection={{
          selectedRowKeys: selectedDocs?.map(item => item.id),
          onSelect(record, selected, selectedRows) {
            const idx = selectedDocs?.findIndex(doc => doc.id === record.knowledgeItemId);
            const isSelected = idx > -1;

            if (selected) {
              if (!isSelected) {
                setSelectedDocs(prev => [...(prev || []), {
                  name: record.name, id: record.knowledgeItemId
                }])
              }
            } else {
              if (isSelected) {
                setSelectedDocs(prev => prev?.filter((_, index) => index !== idx))
              }
            }
          }
        }}
      />
    </Drawer>
  </>
};

export default FileDrawer;