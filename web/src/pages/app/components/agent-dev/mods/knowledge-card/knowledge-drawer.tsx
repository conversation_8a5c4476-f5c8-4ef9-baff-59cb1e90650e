import { NewKnowledge } from "@/api";
import { useQuery } from "@/hooks/useQuery";
import { knowledgeTypeSchema } from "@/pages/new-knowledge/config";
import { But<PERSON>, Drawer, Flex, Pagination, Space, Tabs } from "antd";
import { useEffect, useRef, useState } from "react";
import TextKnowledge from "./text-knowlege";
import CreateKnowledgeModal from "@/pages/new-knowledge/components/create-knowledge-modal";
import CioKnowledge from "./cio-knowlege";
import TableKnowledge from "./table-knowledge";
import { useGlobalState } from '@/hooks/useGlobalState';

const PAGE_SIZE = 20;

const KnowledgeDrawer = (props) => {
  const { value, onChange, trigger } = props;
  const { globalState } = useGlobalState();
  const { group, workspace } = globalState;

  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [open, setOpen] = useState(false);

  const [createOpen, setCreateOpen] = useState(false);

  const { parsedQuery } = useQuery();

  const [pageInfo, setPageInfo] = useState({
    page: 1,
    pageSize: PAGE_SIZE
  });

  const [data, setData] = useState([]);

  const [type, setType] = useState('text');

  const searchTimer = useRef(null);

  const onOpen = () => {
    setOpen(true);
  }

  const onTypeChange = newType => {
    setType(newType);
    setPageInfo({
      ...pageInfo,
      page: 1
    });
  }

  useEffect(() => {
    getKnowledgeList({ type, page: 1 });
  }, [type]);

  const getKnowledgeList = async (val: any = {}) => {
    try {
      setLoading(true);
      const { values: list, total } = await NewKnowledge.getKnowledgeList({
        type: 'text',
        workspaceId: workspace.id,
        groupId: group.id,
        ...pageInfo,
        ...(val || {})
      });
      setData(list);
      setTotal(total);
    } catch (err) {
      console.log('err', err);
    } finally {
      setLoading(false);
    }
  };

  const onRefreshKnowledge = () => {
    getKnowledgeList();
  }

  const onSearch = val => {

    if (searchTimer.current) {
      clearTimeout(searchTimer.current);
    }
    searchTimer.current = setTimeout(() => {
      const newParams = {
        ...(val || {}),
        page: 1
      };

      getKnowledgeList(newParams);
    }, 1000);

  }

  const onPageChange = async (page, pageSize) => {
    await getKnowledgeList({
      page,
    });
    setPageInfo({
      ...pageInfo,
      page
    });
  };

  const onKnowledgeChange = knowledges => {
    onChange?.(knowledges);
  }

  const onClose = () => {
    setOpen(false);
  }

  return <>
    <span onClick={onOpen}>
      {trigger || '编辑'}
    </span>
    <Drawer title="知识库"
      open={open}
      width={800}
      maskClosable={false}
      extra={<Space>
        <Button onClick={() => setCreateOpen(true)}>创建知识库</Button>
        {/* <Button type="primary" onClick={() => onClose()}>确定</Button> */}
      </Space>}
      onClose={onClose}
    >

      <Tabs
        activeKey={type}
        onChange={onTypeChange}
        items={knowledgeTypeSchema?.filter(i => i.key !== 'all')?.map(knowledge => {

          return {
            key: knowledge.key,
            label: knowledge.label,
            children: <>
              {knowledge.key === 'text' &&
                <TextKnowledge
                  loading={loading}
                  onSearch={onSearch}
                  data={data}
                  onChange={onKnowledgeChange}
                  value={value}
                />}
              {knowledge.key === 'cio' &&
                <CioKnowledge
                  loading={loading}
                  onSearch={onSearch}
                  data={data}
                  onChange={onKnowledgeChange}
                  value={value}
                />}

              {knowledge.key === 'table' &&
                <TableKnowledge
                  loading={loading}
                  onSearch={onSearch}
                  data={data}
                  onChange={onKnowledgeChange}
                  value={value}
                />}
            </>
          }
        })} />
      <Flex justify="end" style={{ marginTop: '20px' }}>
        <Pagination
          total={total}
          current={pageInfo.page}
          onChange={onPageChange}
          pageSize={pageInfo.pageSize}
        />
      </Flex>

    </Drawer>
    <CreateKnowledgeModal open={createOpen} setOpen={setCreateOpen} onChange={onRefreshKnowledge} />

  </>
};

export default KnowledgeDrawer