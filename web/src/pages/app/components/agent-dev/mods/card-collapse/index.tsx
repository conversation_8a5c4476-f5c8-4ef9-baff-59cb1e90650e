import { Collapse, CollapseProps } from 'antd';
import styled from 'styled-components';
import { RightOutlined } from '@ant-design/icons';
import { getLocalData, saveLocalData } from '@/utils/common';
import { forwardRef, useMemo } from 'react';

const StyledCollapse = styled(Collapse)`
  background-color: #FBFCFF;

  &.ant-collapse {
    border-bottom: 0;

    .ant-collapse-item {
      .ant-collapse-header {
        padding: 10px 12px !important;
        transition: none !important;
        border-radius: 3px;
        border-bottom: 1px solid #eff0f7;

        &:hover {
          background-color: #eff0f7;
        }
      }
    }
  }

  .ant-collapse-item-active .ant-collapse-header {
    border: none;
  }

  .ant-collapse-item-active {
    border-bottom: 1px solid #eff0f7 !important;
  }

  .ant-collapse-content {
    border-top: none;

    .ant-collapse-content-box {
      padding: 12px;
    }
  }
`;
const CardCollapse = forwardRef<any, {
  expandLocalKey?: string;
  moduleKey?: string;
  defaultActiveKey?: string[];
  items?: any[];
  disabled?: boolean;
} & CollapseProps>((props, ref) => {
  const { expandLocalKey, module<PERSON>ey, defaultActiveKey, disabled, ...rest } = props;

  const localData = useMemo(() => {
    return getLocalData(expandLocalKey);
  }, [expandLocalKey]);

  return (
    <StyledCollapse
      ref={ref}
      expandIcon={({ isActive }) => <RightOutlined rotate={isActive ? 90 : 0} />}
      bordered={false}
      defaultActiveKey={disabled ? [] : (localData?.[moduleKey] ? [moduleKey] : undefined) || defaultActiveKey}
      onChange={activeKey => {
        const isExpand = activeKey?.[0] === moduleKey;
        const currentData = getLocalData(expandLocalKey);
        saveLocalData(expandLocalKey, {
          ...currentData,
          [moduleKey]: !!isExpand
        })
      }}
      {...rest}
    />
  );
});

export default CardCollapse;
