import { styled } from "styled-components"
import { QuestionCircleOutlined } from "@ant-design/icons"
import { Popover, Card, Space } from "antd"
import TextArea from 'antd/es/input/TextArea';
import CardCollapse from "./card-collapse";
import DiffAlert from "./diff-alert";

interface KnowledgeType {
  name: string;
  description: string;
  collectionId: number;
  id: string;
  documentCount: number;
  docs?: Doc[];
}

interface Doc {
  id: string;
  title: string;
  source: string;
  type: string;
  doc_id?: number;
}

const Guide = styled.pre`
  width: 400px;
  word-wrap: normal;
  white-space: pre-wrap;
`;

const guide = <Guide>{'每次对话开始前，发送一个初始内容。支持标准 Markdown 语法，可使用的额外标记:\n[快捷按键]: 用户点击后可以直接发送该问题'}</Guide>

export interface KnowledgeProps {
  doc: Doc;
  onDelete: () => void,
}

export interface ToolsCardProps {
  welcomeText: string;
  oldValue: string;
  onChange: (text: string) => void;
  expandLocalKey: string;
}

export const WelcomeCard = (props: ToolsCardProps) => {
  const { expandLocalKey } = props || {};
  return (
    <CardCollapse
      expandLocalKey={expandLocalKey}
      moduleKey="welcomeText"
      items={[{
        key:'welcomeText',
        label: <Space>
          <span>
            <span style={{ fontWeight: 'bold' }}>开场白</span>
            <DiffAlert
              title="开场白"
              type="welcomeText"
              oldValue={props?.oldValue}
              newValue={props?.welcomeText}
              onRedo={() => {
                props.onChange?.(props?.oldValue);
              }}
            />
          </span>

          <Popover content={guide} placement="bottom" title="开场白"><QuestionCircleOutlined style={{ marginLeft: 5, fontSize: 14 }} /></Popover>
        </Space>,
        children: <TextArea
          bordered={false}
          rows={3} placeholder='' onChange={v => props.onChange(v.target.value)} value={props.welcomeText}>
        </TextArea>
      }]} />
    // <WelcomeCardStyled
    //   title={<span>开场白<Popover content={guide} placement="bottom" title="开场白"><QuestionCircleOutlined style={{ marginLeft: 5, fontSize: 14 }} /></Popover></span>}
    // >
    //   <TextArea rows={3} placeholder='' onChange={v=>props.onChange(v.target.value)} value={props.welcomeText}>
    //   </TextArea>
    // </WelcomeCardStyled>
  )
}

const WelcomeCardStyled = styled(Card)`

  .ant-card-head {
    min-height: 40px;
  }

  .ant-card-body {
    padding: 12px;
    padding-top: 0;
  }
  textarea {
    border: none;
  }
`;
