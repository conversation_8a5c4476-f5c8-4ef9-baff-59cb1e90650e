import { Drawer, message, Select, Form, Input, Space, Button } from "antd"
import { useState } from "react";
import getDiffStr from "../../virtual-human/config-diff/get-diff-str";

const PluginsDrawer = (props) => {
  const { trigger, pluginList, onChange, value, ...rest } = props;
  const [open, setOpen] = useState(false);
  const [selectedPlugin, setSelectedPlugin] = useState<any>();
  const [form] = Form.useForm();


  const initValue = () => {
    if (value && !Array.isArray(value)) {

      const plugin = pluginList?.find(item => item.value === value.componentId);
      setSelectedPlugin(plugin?.data);

      const properties = value?.function?.parameters?.properties;
      const initialValue = Object.keys(properties)?.reduce((acc, key) => {
        acc[key] = properties[key]?.value
        return acc;
      }, {});

      form.setFieldValue('componentId', value?.componentId);
      form.setFieldsValue({
        componentId: value?.componentId,
        ...initialValue,
      });
    } else {
      form.resetFields();
      setSelectedPlugin(undefined);
    }
  }

  const onOpen = () => {
    initValue();
    setOpen(true);
  };

  const onPluginChange = (val, obj) => {
    const inputs = obj?.data?.inputs.concat(obj?.data?.vars || []);
    const initialValue = (inputs || [])?.reduce((acc, cur) => {
      acc[`${cur.name}`] = cur.value
      return acc;
    }, {});
    form.setFieldsValue(initialValue);
    setSelectedPlugin(obj?.data);
  };

  const onClose = () => {
    form.resetFields();
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();
    if (!selectedPlugin) return message.error('请选择插件');
    const requiredInput = selectedPlugin.inputs?.map(input => input.name);

    const inputs = selectedPlugin?.inputs.concat(selectedPlugin?.vars || []);
    const inputsProperties = selectedPlugin?.inputs?.reduce((acc, cur) => {
      acc[`${cur.name}`] = {
        type: cur.type,
        description: cur.description,
        value: values[cur.name]
      }
      return acc;
    }, {});

    const varsProperties = selectedPlugin?.vars?.reduce((acc, cur) => {
      acc[`${cur.name}`] = {
        type: cur.type,
        description: cur.description,
        value: values[cur.name]
      }
      return acc;
    }, {});

    const param = {
      componentId: selectedPlugin.id,
      icon: selectedPlugin.icon,
      name: selectedPlugin.name,
      function: {
        name: selectedPlugin.code,
        description: selectedPlugin.description,
        parameters: {
          type: 'object',
          properties: {
            ...inputsProperties,
            ...varsProperties
          },
        },
        required: requiredInput
      },
    };
    if (getDiffStr.pluginsStr([param]) !== getDiffStr.pluginsStr([value])) {
      onChange(param);
    }
    setOpen(false);
  };

  return <div>
    <div onClick={onOpen} style={{ cursor: 'pointer' }}>
      {trigger}
    </div>
    <Drawer
      {...rest}
      width={600}
      open={open}
      onOk={onOk}
      onClose={onClose}
      extra={<Space>
        <Button onClick={onClose}>取消</Button>
        <Button type="primary" onClick={onOk}>提交</Button>
      </Space>}
    >
      <div>
        <Form
          form={form}
          labelCol={{
            span: 5
          }}
          wrapperCol={{
            span: 18
          }}
          labelWrap
        >
          <Form.Item label="插件" name="componentId"
            rules={[{ required: true, }]}
          >
            <Select
              disabled={value?.componentId}
              options={pluginList}
              style={{ width: '100%' }}
              allowClear
              showSearch
              optionFilterProp="label"
              onChange={onPluginChange}
              placeholder="请选择插件..."
            />
          </Form.Item>
          {selectedPlugin?.inputs?.map(item => {
            return <Form.Item
              // rules={[{ required: true, }]}
              key={item.name}
              name={item.name}
              label={item.title || item.name}
              tooltip={item.description}
            >
              <Input.TextArea />
            </Form.Item>
          })}

          {selectedPlugin?.vars?.map(item => {
            return <Form.Item
              key={item.name}
              name={item.name} label={item.title || item.name}
              tooltip={item.description}
            >
              <Input.TextArea />
            </Form.Item>
          })}
        </Form>
      </div>
    </Drawer>
  </div>
}

export default PluginsDrawer;