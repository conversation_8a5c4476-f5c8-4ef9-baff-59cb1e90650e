import { Input, Select, Space, Switch, Table, Tabs } from "antd";
import { useState } from "react";

const PluginParams = (props) => {
  const { paramsInPrompt } = props;

  return <>
    <Tabs items={[
      { label: '输入参数', key: 'inputs', children: <ParamsTable paramsInPrompt={paramsInPrompt} /> },
      { label: '输出参数', key: 'outputs', children: <ParamsTable paramsInPrompt={paramsInPrompt} /> }
    ]} />
  </>
};

const ParamsTable = (props) => {
  const { paramsInPrompt, onChange } = props;
  const [value, setValue] = useState([
    { name: '1', type: 'string', required: false, defaultValue: null, use: false }
  ]);

  const onItemChange = (index, val) => {
    const newValue = [...value];
    newValue.splice(index, 1, {
      ...val
    });
    setValue(newValue);
    onChange?.(newValue);
  };

  return <Table
    size="small"
    dataSource={value}
    columns={[
      { title: '参数名称', dataIndex: 'name' },
      { title: '参数类型', dataIndex: 'type' },
      {
        title: '必填', dataIndex: 'required', render(value, record, index) {
          if (typeof value === 'undefined') {
            return '-'
          }
          return value ? '必填' : '非必填';
        },
      },
      {
        title: '默认值', dataIndex: 'defaultValue', render(value, record, index) {
          const { type } = value || {};
          return <Space.Compact size="small">
            <Select
              style={{ width: 120 }}
              options={[
                { label: '输入', value: 'input' },
                { label: '引用', value: 'quote' },
                { label: 'AI输入', value: 'ai' }
              ]}
              value={type}
              onChange={val => onItemChange(index, {
                ...record,
                defaultValue: {
                  ...(value || {}),
                  type: val
                }
              })}
            />
            {
              type === 'quote'
                ? <Select
                  popupMatchSelectWidth={false}
                  style={{ width: 120 }}
                  options={paramsInPrompt?.map(param => ({ label: param.title, value: param.key }))} />
                : <Input style={{ width: 200 }} />
            }
          </Space.Compact>
        },
      },
      // {
      //   title: '开启', dataIndex: 'use', render(value, record, index) {
      //     return <Switch size="small"
      //       value={value}
      //       onChange={val => onItemChange(index, {
      //         ...record,
      //         use: val
      //       })} />
      //   },
      // }
    ]}
  />
};

export default PluginParams;
