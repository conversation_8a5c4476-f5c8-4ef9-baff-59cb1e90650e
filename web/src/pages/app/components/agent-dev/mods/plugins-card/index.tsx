import { ComponentApi } from '@/api/component';
import {
  CreditCardOutlined,
  DeleteOutlined,
  EditOutlined,
  InfoCircleOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Space, List, Popconfirm, Tooltip, Flex, Typography, Button } from 'antd';
import React, { useEffect, useState } from 'react';
import <PERSON><PERSON>ollapse from '../card-collapse';
import PluginsDrawer from './plugins-drawer';
import { AvatarIcon } from '@/pages/app/workflow/react-node/base-node';
import styled from 'styled-components';
import FilterModal from './filter-modal';
import DiffAlert from '../diff-alert';
import NewSortableList from '../new-sortable-list';
import NewPluginsDrawer from './new-plugins-drawer';
import PluginDataDrawer from './plugin-data-drawer';
import ResponseModal from './plugin-data-drawer/response-modal';

const PluginIcon = styled(AvatarIcon)`
  margin-right: 10px;

  .icon path {
    fill: #2f77ff !important;
  }
`;

const PluginsCard = (props) => {
  const { value, oldValue, onChange, paramsInPrompt, expandLocalKey, disabled } = props;
  const [pluginList, setPluginList] = useState([]);

  const getPlugins = async () => {
    ComponentApi.list('', '', ['virtual-human']).then((res) => {
      // @ts-ignore
      setPluginList(
        res?.map((item) => {
          return {
            label: item.config?.name,
            value: item.config?.id,
            data: item.config,
            // data: item,
          };
        }),
      );
    });
  };

  useEffect(() => {
    getPlugins();
  }, []);

  const onPluginAdd = (newData) => {
    const newValue = [...(value || [])];
    newValue.push(newData);
    onChange(newValue);
    // onChange(newData);
  };

  const onEdit = (newData) => {
    const newValue = value?.map((item) => {
      if (item.componentId === newData.componentId) return newData;
      return item;
    });
    onChange(newValue);
  };

  const onDelete = (componentId) => {
    const newValue = value?.filter((item) => item.componentId !== componentId);
    onChange(newValue);
  };

  const onFilterChange = (val, componentId) => {
    const newValue = value?.map((item) => {
      if (item.componentId === componentId) {
        return {
          ...item,
          filter: val,
        };
      }
      return item;
    });

    onChange(newValue);
  };

  const onSortChange = (newValue) => {
    onChange(newValue);
  };

  return (
    <>
      <CardCollapse
        expandLocalKey={expandLocalKey}
        moduleKey="plugins"
        collapsible={disabled ? "disabled" : 'icon'}
        items={[
          {
            key: 'plugins',
            label: (
              <Space>
                <span>
                  <span style={{ fontWeight: 'bold' }}>插件</span>
                  <DiffAlert
                    title="插件"
                    type="plugins"
                    oldValue={oldValue}
                    newValue={value}
                    onRedo={() => {
                      onChange(oldValue);
                    }}
                  />
                </span>
                <Tooltip title={<>
                  插件只可生效一个；依据配置先后顺序生效第一个命中触发条件的插件
                  <a
                    rel="noopener"
                    target='_blank'
                    href='https://music-doc.st.netease.com/st/langbase-doc/plugin'>
                    查看详情
                  </a>
                </>}>
                  <InfoCircleOutlined />
                </Tooltip>
              </Space>
            ),
            children: (
              <>
                {value?.length ? (
                  <NewSortableList
                    value={value}
                    itemKey="componentId"
                    itemRender={(item) => {
                      return (
                        <Flex
                          justify="space-between"
                          style={{ width: '100%', margin: '6px 0px' }}
                        >
                          <Flex align="center">
                            <div style={{ marginRight: '16px' }}>
                              <PluginIcon icon={item.icon} />
                            </div>
                            <div>
                              <div style={{ textAlign: 'left', fontWeight: 'bold' }}>
                                {item.name}
                              </div>
                              <div style={{ color: 'rgba(0,0,0,0.45)' }}>
                                {item.function.description}
                              </div>
                            </div>
                          </Flex>
                          <Space style={{ color: 'rgb(141, 141, 153)' }}>
                            {/* <ResponseModal trigger={
                              <Tooltip title="回复卡片设置">
                                <CreditCardOutlined style={{ cursor: 'pointer' }} />
                              </Tooltip>
                            } /> */}
                            {/* <PluginDataDrawer
                              trigger={
                                <Tooltip title="设置">
                                  <EditOutlined style={{ cursor: 'pointer' }} />
                                </Tooltip>
                              }
                              value={item} paramsInPrompt={paramsInPrompt} /> */}
                            <FilterModal
                              paramsInPrompt={paramsInPrompt}
                              value={item.filter}
                              onChange={(val) => onFilterChange(val, item.componentId)}
                            />
                            <PluginsDrawer
                              trigger={
                                <Tooltip title="参数编辑">
                                  <EditOutlined />
                                </Tooltip>
                              }
                              pluginList={pluginList}
                              value={item}
                              onChange={onEdit}
                            />
                            <Popconfirm
                              title={`确认删除 ${item.name} 吗？`}
                              onConfirm={() => onDelete(item.componentId)}
                            >
                              <DeleteOutlined style={{ cursor: 'pointer' }} />
                            </Popconfirm>
                          </Space>
                        </Flex>
                      );
                    }}
                    onChange={onSortChange}
                  />
                ) : (
                  <span style={{ color: '#999' }}>
                    插件能够让智能体调用外部
                    API，例如搜索信息、浏览网页、生成图片等，扩展智能体的能力和使用场景。
                  </span>
                )}
              </>
            ),
            extra: !disabled && (
              <div onClick={(e) => e.stopPropagation()}>
                <PluginsDrawer
                  trigger={
                    <Tooltip title='添加插件'>
                      <Button
                        type="text"
                        size="small"
                        icon={<PlusOutlined style={{ fontSize: 10, color: '#666' }} />}
                      />
                    </Tooltip>
                  }
                  value={value}
                  pluginList={pluginList}
                  title="添加插件"
                  onChange={onPluginAdd}
                />
              </div>
            ),
          },
        ]}
      />
    </>
  );
};

export default PluginsCard;
