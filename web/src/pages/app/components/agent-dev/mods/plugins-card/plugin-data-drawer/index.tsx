import { Modal, Tabs } from "antd";
import { useEffect, useState } from "react";
import PluginBasic from "./plugin-basic";
import PluginParams from "./plugin-params";

const PluginDataDrawer = (props) => {
  const { value: parentValue, paramsInPrompt, trigger } = props;
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState(parentValue);

  useEffect(() => {
    setValue(parentValue);
  }, [parentValue]);

  const onOpen = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onOk = () => {
    onClose();
  };

  const onDescChange = desc => {
    const newValue = { ...value };
    newValue.function.description = desc;
    setValue(newValue)
  };

  const onParamsChange = () => {

  };

  return <>
    <div onClick={onOpen}>
      {trigger ?? '编辑基本信息'}
    </div>
    <Modal title="设置"
      width={800}
      open={open} onCancel={onClose}
      onOk={onOk}
      destroyOnClose
    >
      <Tabs items={[
        {
          label: '基础设置', key: 'basic',
          children: <PluginBasic value={value} onChange={onDescChange} />
        },
        {
          label: '参数',
          key: 'params',
          children: <PluginParams value={value} onChange={onParamsChange} paramsInPrompt={paramsInPrompt} />
        }
      ]}
        tabPosition="left"
      />
    </Modal>
  </>
}

export default PluginDataDrawer;
