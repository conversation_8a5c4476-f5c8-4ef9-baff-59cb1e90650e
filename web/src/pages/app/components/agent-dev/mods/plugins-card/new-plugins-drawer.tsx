import { But<PERSON>, <PERSON><PERSON>, Di<PERSON>r, Drawer, Flex, Input, List, Space, Typography } from "antd";
import { useEffect, useState } from "react";
import styled from "styled-components";
import { AvatarIcon } from '@/pages/app/workflow/react-node/base-node';

const PluginIcon = styled(AvatarIcon)`
  margin-right: 10px;
  .icon path {
    fill: #2f77ff !important;
  }
`;

const StyledPlugin = styled.div`
  &:hover {

  }
  h4 {
    text-align: left;
  }
`;

const NewPluginsDrawer = (props) => {
  const { trigger, pluginList, value: parentValue, onChange, ...rest } = props;
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState(parentValue);
  const [list, setList] = useState(pluginList);

  useEffect(() => {
    setList(pluginList);
  }, [pluginList]);

  useEffect(() => {
    setValue(parentValue);
  }, [parentValue, open]);

  const onOpen = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onSearch = (keyword) => {
    const newList = pluginList?.filter(item => {
      return item.label?.toLowerCase().includes(keyword.toLowerCase())
    });
    setList(newList);
  };


  const onCheck = (checked, val) => {
    const newValue = [...(value || [])];
    const index = newValue?.findIndex(item => item.componentId === val.id);

    const requiredInput = val?.config?.inputs?.map(input => input.name);

    const inputsProperties = val?.config?.inputs?.reduce((acc, cur) => {
      acc[`${cur.name}`] = {
        type: cur.type,
        description: cur.description,
        // value: values[cur.name]
      }
      return acc;
    }, {});

    const varsProperties = val?.config?.vars?.reduce((acc, cur) => {
      acc[`${cur.name}`] = {
        type: cur.type,
        description: cur.description,
        // value: values[cur.name]
      }
      return acc;
    }, {});

    const param = {
      componentId: val.id,
      icon: val.config?.icon,
      name: val.name,
      function: {
        name: val.code,
        description: val.description,
        parameters: {
          type: 'object',
          properties: {
            ...inputsProperties,
            ...varsProperties
          },
        },
        required: requiredInput
      },
    };

    if (checked) {
      if (index > -1) return
      newValue.push(param);
    } else {
      if (index > -1) {
        newValue.splice(index, 1)
      }
    }
    setValue(newValue);
  };

  const onSubmit = () => {
    console.log('onSubmit.value', value);
    onChange?.(value);
    onClose();
  };


  return <div>
    <div onClick={onOpen} style={{ cursor: 'pointer' }}>
      {trigger}
    </div>
    <Drawer
      {...rest}
      width={800}
      open={open}
      onClose={onClose}

      footer={<Flex justify="end">
        <Space>
          <Button>取消</Button>
          <Button type="primary" onClick={onSubmit}>提交</Button>
        </Space>
      </Flex>}
    >
      <Input.Search style={{ width: 300 }} onSearch={onSearch} />
      <StyledPlugin>

        <List
          itemLayout="horizontal"
          dataSource={list}
          renderItem={(item: any) => {
            const config = item?.data?.config || {};
            const isChecked = value?.findIndex(it => it.componentId === item.value) > -1;
            return <List.Item actions={[<Checkbox checked={isChecked} onChange={(e) => onCheck(e.target.checked, item.data)} />]}>
              <List.Item.Meta
                avatar={
                  <PluginIcon icon={config.icon} />

                }
                title={config.name}
                description={config.description}
              />
            </List.Item>
          }}
        />
      </StyledPlugin>
    </Drawer>
  </div>
};

export default NewPluginsDrawer;
