import { But<PERSON>, Drawer, Form, Modal, Space, Tooltip } from "antd";
import Condition from "../../../virtual-human/condition";
import { AlertOutlined } from "@ant-design/icons";
import { useState } from "react";
import { toCioModel, toLogicalModel } from "../../../virtual-human/utils";

const FilterModal = (props) => {
  const { paramsInPrompt, onChange, value } = props;
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const keyOptions = paramsInPrompt?.map(item => {
    return {
      ...item,
      label: item.title,
      value: item.key,
    }
  });

  const onOpen = () => {
    const logicalValue = toLogicalModel(value);
    form.setFieldValue('filter', logicalValue);
    setOpen(true);
  };

  const onOk = () => {
    const filter = toCioModel(form.getFieldValue('filter'),keyOptions)
    onChange?.(filter);
    setOpen(false);
  };

  const onCancel = () => {
    setOpen(false);
  };

  return <>
    <Tooltip title="触发条件">
      <AlertOutlined onClick={onOpen} />
    </Tooltip>
    <Drawer title="触发条件"
      open={open}
      width={700}
      onClose={onCancel}
      extra={<Space>
        <Button onClick={onCancel}>取消</Button>
        <Button type="primary" onClick={onOk}>提交</Button>
      </Space>}
    >
      <Form form={form} >
        <Form.Item name="filter">
          <Condition options={keyOptions} />
        </Form.Item>
      </Form>
    </Drawer>
  </>
};

export default FilterModal;
