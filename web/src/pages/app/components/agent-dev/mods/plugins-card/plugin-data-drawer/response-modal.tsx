import { Flex, Form, Image, Modal, Radio, Select } from "antd";
import { useState } from "react";


const ResponseModal = (props) => {
  const { trigger } = props;
  const [open, setOpen] = useState(false);

  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const onOk = () => {
    onCancel();
  };

  return <>
    <div onClick={onOpen}>
      {trigger ?? '回复卡片设置'}
    </div>
    <Modal title="回复卡片设置"
      width={800}
      open={open} onCancel={onCancel} onOk={onOk}>
      <Flex>
        <Flex flex={1} vertical style={{ borderRight: '1px solid #cecece', marginRight: 20 }}>
          <div>
            回复卡片：<Select style={{ width: 200 }} options={[
              { label: '不使用卡片回复', value: 'no' }
            ]} />

          </div>

          <div>
            <h4 style={{ textAlign: 'left' }}>为卡片绑定数据源</h4>
            <Form>
              <Form.Item label="歌曲id" name="">
                <Select />
              </Form.Item>
            </Form>
          </div>

        </Flex>
        <Flex style={{ width: 240 }}>
          <Image src="u" />
        </Flex>
      </Flex>
    </Modal>
  </>
};

export default ResponseModal;