import { Form, Input } from "antd";
import { useEffect, useState } from "react";

const PluginBasic = (props) => {
  const { value, onChange } = props;
  const [desc, setDesc] = useState();

  useEffect(() => {
    setDesc(value?.function?.description);
  }, [value?.function?.description]);

  const onDescChange = (ev) => {
    setDesc(ev.target.value);
    onChange?.(ev.target.value);
  };

  return <Form>
    <Form.Item label="名称" >{value?.name}</Form.Item>
    <Form.Item label="描述" >
      <Input.TextArea rows={10} value={desc} onChange={onDescChange} />
    </Form.Item>
  </Form>
};

export default PluginBasic;
