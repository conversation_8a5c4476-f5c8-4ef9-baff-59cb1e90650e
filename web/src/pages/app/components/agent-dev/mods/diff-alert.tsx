import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import { singleConfigDiff } from "../virtual-human/config-diff/get-diff-str";
import { useMemo, useState } from "react";
import { RedoOutlined } from "@ant-design/icons";

const DiffAlert = (props) => {
  const { title, type, oldValue, newValue, trigger, onRedo: onParentRedo } = props;

  const [open, setOpen] = useState(false);

  const diffItem = singleConfigDiff[type] ?? {
    getDiffStr: val => val || ''
  };


  const hasDiff = useMemo(() => {
    const oldValueStr = diffItem.getDiffStr(oldValue);
    const newValueStr = diffItem.getDiffStr(newValue);
    return oldValueStr !== newValueStr;
  }, [oldValue, newValue]);

  const openDrawer = (e) => {
    e.stopPropagation();
    if (hasDiff) {
      setOpen(true);
    }
  };

  const onClose = (e) => {
    e.stopPropagation();
    setOpen(false);
  };

  const onRedo = (e) => {
    e.stopPropagation();
    onParentRedo?.();
    setOpen(false);
  };

  return <>
    {hasDiff &&
      <div
        style={{
          display: 'inline-block',
        }}
        onClick={openDrawer}>
        {trigger ?? <Tooltip
          title={<span style={{ cursor: 'pointer' }} onClick={openDrawer}>{title}有修改，<span>查看草稿</span></span>}>
          <Badge dot>
            <div style={{
              display: 'inline-block',
              height: '100%',
              width: '1px',
            }}></div>
          </Badge>
        </Tooltip>}
      </div>}
    <Drawer
      title={title}
      open={open}
      onClose={onClose}
      width={1000}
    >
      <ReactDiffViewer
        styles={{
          contentText: {
            wordBreak: 'break-all', // 强制所有字符换行
            whiteSpace: 'pre-wrap'  // 保留空白但允许换行
          }
        }}
        splitView
        oldValue={diffItem.getDiffStr(oldValue)}
        newValue={diffItem.getDiffStr(newValue)}
        leftTitle={<span style={{ fontSize: '12px' }}>
          服务器版本
          {' '}
          <a
            onClick={onRedo}
          ><RedoOutlined /> 使用</a>
        </span>}
        rightTitle={<span style={{ fontSize: '12px' }}>当前草稿</span>}
      />
    </Drawer>
  </>
};

export default DiffAlert