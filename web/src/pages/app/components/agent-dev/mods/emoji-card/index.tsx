import { <PERSON>lider, Flex, InputNumber, Image, Button, Tooltip, Space } from 'antd';
import Card<PERSON>ollapse from '../card-collapse';
import EmojiDrawer from './emoji-drawer';
import { useEffect, useState } from 'react';
import { EmojiApi } from '@/api/emoji';
import styled from 'styled-components';
import DiffAlert from '../diff-alert';
import {
  EllipsisOutlined,
  InfoCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';

const EmojiImg = styled(Image)`
  aspect-ratio: 1;
  width: 100%;
  object-fit: cover !important;
`;

export const emojiStr = (value) => {
  const blank = '            ';
  const res = `
【发送概率】: ${value?.outputFrequency || 0},
【表情包范围】:
${blank}${value?.resourceIds?.map((id) => id)?.join(`\n${blank}`) || ''}
  `;
  return res;
};

const EmojiCard = (props) => {
  const { value, onChange, appId, oldValue, expandLocal<PERSON>ey, disabled } = props;

  const [emojiItems, setEmojiItems] = useState<any[]>();

  useEffect(() => {
    if (value?.resourceIds?.length) {
      EmojiApi.queryByIds({
        ids: value?.resourceIds,
        appId,
      }).then((res) => {
        if (!Array.isArray(res)) {
        } else {
          const items = res?.map((item) => {
            const info = JSON.parse(item.assetInfo || '{}');
            return {
              id: item.id,
              url: info.url,
            };
          });
          setEmojiItems(items);
        }
      });
    } else {
      setEmojiItems([]);
    }
  }, [JSON.stringify(value?.resourceIds)]);

  const onFrequencyChange = (freq: number) => {
    onChange({
      ...value,
      outputFrequency: freq,
    });
  };

  const onEmojiChange = (emojis = {}) => {
    onChange({
      ...value,
      ...emojis,
    });
  };

  return (
    <CardCollapse
      expandLocalKey={expandLocalKey}
      moduleKey="emojiConfig"
      collapsible={disabled ? "disabled" : 'icon'}
      items={[
        {
          key: 'emojiConfig',
          label: (
            <Space>
              <span>
                <span style={{ fontWeight: 'bold' }}>表情包</span>
                <DiffAlert
                  title="表情包"
                  type="emojiConfig"
                  newValue={value}
                  oldValue={oldValue}
                  onRedo={() => {
                    onChange(oldValue);
                  }}
                />
              </span>
              <Tooltip title={<Flex gap={8} vertical>
                <div>配置表情包，和发送概率，有机会在对话中发送表情包。（100%表示每次对话都会发生表情包）</div>
                <Image
                  src="https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/58120913804/c27a/aed6/9d4a/d409011ee075ed1a94a051758bab1c15.png"
                />
                <a
                  rel="noopener"
                  target='_blank'
                  href='https://music-doc.st.netease.com/st/langbase-doc/update#%E8%A1%A8%E6%83%85%E5%8C%85%E7%AE%A1%E7%90%86'
                >查看详情</a>
              </Flex>}>
                <InfoCircleOutlined />
              </Tooltip>
            </Space>
          ),
          children: (
            <>
              <Flex align="center" style={{ marginBottom: 10 }}>
                发送概率
                <Slider
                  min={0}
                  max={100}
                  style={{ flex: 1, margin: '0 16px' }}
                  value={value?.outputFrequency || 0}
                  onChange={onFrequencyChange}
                />
                <InputNumber
                  min={0}
                  max={100}
                  value={value?.outputFrequency || 0}
                  onChange={onFrequencyChange}
                />
              </Flex>
              <Flex>
                <Flex style={{ flexShrink: 0, marginRight: 16, alignSelf: 'center' }}>
                  表情包范围
                </Flex>
                <Flex gap={10}>
                  {emojiItems?.slice(0, 4)?.map((img) => {
                    return <EmojiImg key={img.id} src={img.url} width={60} />;
                  })}
                  <EmojiDrawer
                    appId={appId}
                    trigger={
                      <Flex
                        vertical
                        align="center"
                        justify="center"
                        style={{
                          border: '1px dashed #d9d9d9',
                          width: '60px',
                          height: '60px',
                          cursor: 'pointer',
                        }}
                      >
                        {value?.resourceIds?.length > 4 ? (
                          <>
                            <div>
                              <EllipsisOutlined />
                            </div>
                            <div>更多</div>
                          </>
                        ) : (
                          <>
                            <div>
                              <PlusOutlined />
                            </div>
                            <div>添加</div>
                          </>
                        )}
                      </Flex>
                    }
                    onChange={onEmojiChange}
                    value={value}
                  />
                </Flex>
              </Flex>
            </>
          ),
          extra: !disabled && (
            <div onClick={(e) => e.stopPropagation()}>
              <EmojiDrawer
                appId={appId}
                onChange={onEmojiChange}
                value={value}
                trigger={
                  <Tooltip title='添加表情包'>
                    <Button
                      type="text"
                      size="small"
                      icon={<PlusOutlined style={{ fontSize: 10, color: '#666' }} />}
                    />
                  </Tooltip>
                }
              />
            </div>
          ),
        },
      ]}
    // expandIcon={() => null}
    />
  );
};

export default EmojiCard;
