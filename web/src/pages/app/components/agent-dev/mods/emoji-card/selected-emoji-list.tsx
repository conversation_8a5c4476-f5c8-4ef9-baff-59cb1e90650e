import { List, Image, Flex, Checkbox } from "antd";
import styled from "styled-components";

const EmojiImg = styled(Image)`
  aspect-ratio: 1;
  width: 100%;
  object-fit: cover !important;
`;

const ImageContainer = styled.div`
  width: 100%;
  aspect-ratio: 1;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
`;

const EmojiItem = styled(Flex)`
  width: 100%;
  user-select: none;
  height: auto;
  overflow: hidden;
  .emoji-name {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;

const EmojiContainer = styled.div`
  position: relative;
  height: 100%;
  .select-box {
    position: absolute;
    z-index: 1;
    left: 6px;
    top: 4px;
  }
`;

interface ISelectionProps {
  selectedKeys?: string[];
  selectedItems?: any[];
  onChange?: (selectedKeys: string[], selectedItems: any[]) => void;
  onSelect?: (checked: boolean, item: any) => void;
}


const SelectedEmojiList = (props: {
  appId: string;
  disabled?: boolean;
  selectionProps?: ISelectionProps;
  dataSource?: any[];
}) => {
  const { selectionProps, dataSource } = props;

  const isSelectMode = typeof selectionProps !== "undefined"

  const onEmojiCheck = (checked, item) => {
    if (!isSelectMode) return;
    // const checked = ev.target.checked;

    let newData = [...(selectionProps?.selectedItems || [])];

    const idx = newData.findIndex(d => d.id === item.id);

    if (checked) {
      if (idx < 0) {
        newData.push(item);
      }
    } else {
      if (idx > -1) {
        newData.splice(idx, 1)
      }
    }

    selectionProps?.onChange?.(newData?.map(d => d.id), newData);
    selectionProps?.onSelect?.(item, checked);
  }


  return <div>
    <List
      grid={{
        gutter: 16,
        xs: 1,
        sm: 1,
        md: 1,
        lg: 1,
        xl: 10,
        xxl: 10,
      }}
      dataSource={dataSource}
      renderItem={(item: any) => {
        const checked = selectionProps?.selectedKeys?.includes(item.id);
        return <List.Item style={{ height: '100%' }}>
          <EmojiContainer>
            {isSelectMode && <div className="select-box">
              <Checkbox
                checked={checked}
                onChange={(ev) => onEmojiCheck(ev.target.checked, item)} />
            </div>}
            <EmojiItem vertical justify="space-between">
              <ImageContainer
                onClick={() => onEmojiCheck(!checked, item)}>
                <EmojiImg
                  src={item?.url}
                  height="100%"
                  preview={false}
                />
              </ImageContainer>

            </EmojiItem>
          </EmojiContainer>

        </List.Item>
      }}
    />
  </div>
};

export default SelectedEmojiList;
