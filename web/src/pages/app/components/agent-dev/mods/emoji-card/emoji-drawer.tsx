import { EditOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import { useState } from "react";
import Emoji from "../../../resource/emoji";
import SelectedEmojiList from "./selected-emoji-list";
import { EmojiApi } from "@/api/emoji";

const EmojiDrawer = (props) => {
  const { trigger, appId, onChange, value } = props;
  const [open, setOpen] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState();
  const [selectedItems, setSelectedItems] = useState<any[]>();

  const onOpen = () => {
    if (value?.resourceIds?.length) {
      EmojiApi.queryByIds({
        ids: value?.resourceIds,
        appId
      }).then(res => {
        if (!Array.isArray(res)) {
        } else {
          const items = res?.map(item => {
            const info = JSON.parse(item.assetInfo || '{}')
            return {
              id: item.id,
              url: info.url
            }
          });

          setSelectedItems(items);
        }
      });
    }
    setSelectedKeys(value?.resourceIds);
    setOpen(true);
  }

  const onClose = () => {
    setOpen(false);
  }

  const onEmojiChange = (ids, items) => {
    setSelectedKeys(ids);
    setSelectedItems(items);
  }

  const onSubmit = () => {
    const resourceIds = (selectedItems || [])?.map(item => item.id);
    onChange?.({
      resourceIds,
      // emojiItems: selectedItems
    });
    setOpen(false);
  }

  return <>
    <Space onClick={onOpen} size="small">
      {trigger || <>
        <EditOutlined />编辑
      </>}
    </Space>
    <Drawer title="表情包" open={open}
      width={800}
      onClose={onClose}
      maskClosable={false}
      footer={
        <Flex justify="end">
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" onClick={onSubmit}>确定</Button>
          </Space>
        </Flex>
      }
    >
      <div style={{ maxHeight: '80%', minHeight: '60%', overflow: 'scroll', marginBottom: 24 }}>
        <Emoji
          appId={appId}
          disabled
          selectionProps={{
            selectedKeys,
            selectedItems,
            onChange: onEmojiChange
          }}
        />
      </div>

      <Collapse
        // ghost
        defaultActiveKey={['selected']}
        items={[
          {
            label: '选中的表情包',
            key: 'selected',
            children: <SelectedEmojiList
              appId={appId} disabled
              selectionProps={{
                selectedKeys,
                selectedItems,
                onChange: onEmojiChange
              }}
              dataSource={selectedItems} />
          }
        ]}
      />
    </Drawer>
  </>
};


export default EmojiDrawer;