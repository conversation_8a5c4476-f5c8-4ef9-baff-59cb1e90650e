import { Button, Form, Input } from "antd";

const { Item } = Form;
const { TextArea } = Input;

export interface AppUsageProps {
  description?: string;
  usingExample?: string;
  onChanged: (values: { description?: string; usingExample?: string }) => void;
}

export const AppUsage = (props: AppUsageProps) => {
  const { description, usingExample, onChanged = () => {} } = props;
  return (
    <Form
      layout="vertical"
      onFinish={(d) => {onChanged(d)}}
      initialValues={{
        description, usingExample
      }}
      autoComplete="off"
    >
      <Item name="description" key="description" label="描述">
        <TextArea autoSize={{minRows: 5, maxRows: 5}} />
      </Item>
      <Item name="usingExample" key="usingExample" label="使用样例">
        <TextArea autoSize={{minRows: 20, maxRows: 20}} />
      </Item>
      <Button type="primary" htmlType="submit">保存</Button>
    </Form>
  );
};
