import React, { useEffect, useState } from 'react';
import { Drawer, Button, Switch, Form, Input, Select, message, Checkbox, Modal, Tag, Spin } from 'antd';
import { AppApi } from '@/api/app';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';


interface ChatSettingProps {
  visible: boolean;
  onClose: () => void;
  value: any;
  onChange: (values: any) => void;
}

const ChatSetting: React.FC<ChatSettingProps> = ({ visible, value, onClose, onChange }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    const formValue = form.getFieldsValue(true)
    console.log(999, formValue, value)
    if (JSON.stringify(formValue) !== JSON.stringify(value)) {
      form.setFieldsValue(value);
    }
  }, [value])

  return (
    <>
      <Drawer
        title="调试设置"
        width={600}
        onClose={onClose}
        headerStyle={{background: '#F4F4F5' }}
        bodyStyle={{ paddingBottom: 80, background: 'rgba(250, 250, 250, 1)' }}
        visible={visible}
        // 要比diff drawer高
        zIndex={9999}
      >
        <div>
          <Form form={form} onValuesChange={(values) => onChange(values)}>
            <Form.Item
              name="supportSSE"
              label="开启流式对话"
              valuePropName="checked"
              help={form.getFieldValue('supportSSE') ?
                '流式对话无法使用时，关闭后使用同步对话' :
                '开启流式对话后，支持图片/表情回复'}
              rules={[{ required: true, message: '请选择发布位置' }]}
            >
              <Switch />
            </Form.Item>
          </Form>
        </div>
      </Drawer>
    </>
  );
};

export default ChatSetting;