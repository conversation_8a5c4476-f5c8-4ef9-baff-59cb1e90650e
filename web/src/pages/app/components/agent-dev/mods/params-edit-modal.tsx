import { HolderOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { Input, InputNumber, message, Modal, Select } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';

import { IAgentParam, ParamDataTypeEnum, ParamDataTypeMap, ParamTypeComponentMap, ParamTypeEnum, ParamTypeIconMap } from '@/interface/agent';

export const ParamTypeRender = [
  {
    param_type: ParamTypeEnum.textInput,
    param_type_name: '文本',
    param_type_icon: ParamTypeIconMap[ParamTypeEnum.textInput],
  },
  {
    param_type: ParamTypeEnum.textArea,
    param_type_name: '段落',
    param_type_icon: ParamTypeIconMap[ParamTypeEnum.textArea],
  },
  {
    param_type: ParamTypeEnum.select,
    param_type_name: '下拉筛选',
    param_type_icon: ParamTypeIconMap[ParamTypeEnum.select],
  },
  {
    param_type: ParamTypeEnum.date,
    param_type_name: '日期选择',
    param_type_icon: ParamTypeIconMap[ParamTypeEnum.date],
  },
  {
    param_type: ParamTypeEnum.number,
    param_type_name: '数值',
    param_type_icon: ParamTypeIconMap[ParamTypeEnum.textInput],
  },
  {
    param_type: ParamTypeEnum.boolean,
    param_type_name: '布尔选择',
    param_type_icon: ParamTypeIconMap[ParamTypeEnum.textInput],
  },
  {
    param_type: ParamTypeEnum.file,
    param_type_name: '文件',
    param_type_icon: ParamTypeIconMap[ParamTypeEnum.textInput],
  }
];

interface IProps {
  editParam: IAgentParam | null;
  onSave: (editedParam: IAgentParam) => void;
  onCancel: () => void;
}

export function ParamsEditModal(props: IProps) {
  const { editParam, onSave, onCancel } = props;
  const [tempEditParam, setTempEditParam] = useState(editParam);

  useEffect(() => {
    setTempEditParam(editParam);
  }, [editParam]);

  function onFieldChange(field: string, value: any) {
    setTempEditParam({
      ...tempEditParam!,
      [field]: value,
    });
  }

  function onFieldsChange(obj) {
    if (!obj) return;
    setTempEditParam({
      ...tempEditParam,
      ...obj
    });
  }

  function onCommit() {
    if (tempEditParam?.type === ParamTypeEnum.select) {
      const options = tempEditParam.config?.options;
      if (!options?.length) {
        return message.warning('至少需要一个选项');
      }
      if (options.some((it) => !it.value)) {
        return message.warning('选项不能为空');
      }
      if (new Set(options.map((it) => it.value)).size < options.length) {
        return message.warning('选项不能重复');
      }
    }
    onSave(tempEditParam!);
  }

  return (
    <Modal
      title="参数设置"
      open={!!tempEditParam}
      onOk={onCommit}
      onCancel={onCancel}
      okText="保存"
    >
      {tempEditParam && (
        <>
          <p>
            变量名称：
            {'{{'}
            {tempEditParam.key}
            {'}}'}
          </p>
          <p>
            {tempEditParam.category !== 'INPUT'
              ? <>
                变量类型：{ParamDataTypeMap[tempEditParam.dataType]?.label}
              </>
              : <>
                变量类型：<Select
                  style={{ width: 200 }}
                  options={Object.keys(ParamDataTypeMap).map(key => ({
                    ...ParamDataTypeMap[key]
                  }))}
                  value={tempEditParam.dataType}
                  onChange={v => {
                    const newValue = {
                      config: {},
                      default_val: undefined,
                      dataType: v,
                    };
                    // 时间
                    if (v === ParamDataTypeEnum.time) {
                      onFieldsChange({
                        type: ParamTypeEnum.date,
                        ...newValue
                      });
                    } else if (v === ParamDataTypeEnum.bool) {
                      onFieldsChange({
                        type: ParamTypeEnum.boolean,
                        ...newValue
                      });
                    } else if ([ParamDataTypeEnum.long, ParamDataTypeEnum.integer, ParamDataTypeEnum.double].includes(v as ParamDataTypeEnum)) {
                      onFieldsChange({
                        type: ParamTypeEnum.number,
                        ...newValue
                      });
                    } else {
                      onFieldsChange({
                        type: ParamTypeEnum.textInput,
                        ...newValue
                      });
                    }


                  }}
                ></Select>
              </>}
          </p>
          <p>输入组件：</p>
          <ParamTypeList>
            {ParamTypeRender.filter(item => {
              // 时间类型只能返回时间
              if (tempEditParam.dataType === ParamDataTypeEnum.time) {
                return item.param_type === ParamTypeEnum.date;
              } else if ([ParamDataTypeEnum.integer, ParamDataTypeEnum.long, ParamDataTypeEnum.double].includes(tempEditParam.dataType as ParamDataTypeEnum)) {
                // 整型、长整型
                return item.param_type === ParamTypeEnum.number;
              } else if (ParamDataTypeEnum.bool === tempEditParam.dataType) {
                return item.param_type === ParamTypeEnum.boolean;
              } else {
                return [ParamTypeEnum.select, ParamTypeEnum.textArea, ParamTypeEnum.textInput, ParamTypeEnum.file].includes(item.param_type);
              }

            }).map((it, idx) => {
              return (
                <ParamTypeItem
                  key={`param-type-${idx}`}
                  className={tempEditParam.type === it.param_type ? 'selected' : ''}
                  onClick={() => onFieldChange('type', it.param_type)}
                >
                  {ParamTypeIconMap[it.param_type]}
                  {it.param_type_name}
                </ParamTypeItem>
              );
            })}
          </ParamTypeList>
          {/* {tempEditParam.type === ParamTypeEnum.textInput && (
            <InputConfig tempEditParam={tempEditParam} onFieldChange={onFieldChange} />
          )} */}
          {tempEditParam.type === ParamTypeEnum.select && (
            <OptionConfig tempEditParam={tempEditParam} onFieldChange={onFieldChange} />
          )}
          <DefaultConfig tempEditParam={tempEditParam} onFieldChange={onFieldChange} />
        </>
      )}
    </Modal>
  );
}

interface IConfigProps {
  tempEditParam: IAgentParam;
  onFieldChange: (field: string, value: any) => void;
}

function DefaultConfig({ tempEditParam, onFieldChange }: IConfigProps) {

  const Component: any = ParamTypeComponentMap[tempEditParam.type] || Input;

  let content = <Component
    style={{ width: '100%' }}
    min={1}
    max={256}
    value={tempEditParam.default_val as string}
    onChange={(e) => {
      onFieldChange('default_val', e.target.value);
    }}
  />

  if (tempEditParam.dataType === ParamDataTypeEnum.bool) {
    content = <Component
      checked={tempEditParam.default_val as boolean}
      onChange={(val) => {
        onFieldChange('default_val', val);
      }}
    />
  }

  return (
    <>
      <p>默认值</p>
      {content}
    </>
  );
}

function InputConfig({ tempEditParam, onFieldChange }: IConfigProps) {
  return (
    <>
      <p>最大长度</p>
      <InputNumber
        style={{ width: '100%' }}
        min={1}
        max={256}
        value={tempEditParam.config.length || 48}
        onChange={(e) => {
          onFieldChange('config', {
            ...tempEditParam.config,
            length: e,
          });
        }}
      />
    </>
  );
}

function OptionConfig({ tempEditParam, onFieldChange }: IConfigProps) {
  const [drag, setDrag] = useState('');
  const [drop, setDrop] = useState('');

  function onDrag(uuid: string, ev: any) {
    ev.dataTransfer.setData('text/plain', ev.target.id);
    setDrag(uuid);
  }

  function onDragOver(uuid: string, ev: any) {
    setDrop(uuid);
    ev.preventDefault();
    ev.dataTransfer.dropEffect = 'move';
  }

  function onDrop() {
    setDrag('');
    setDrop('');
  }

  const optionsWithUUid = useMemo(() => {
    return (
      tempEditParam.config?.options?.map((it) => {
        return {
          ...it,
          uuid: it.uuid || `${+Date.now()}-${Math.random()}`,
        };
      }) || []
    );
  }, [tempEditParam.config?.options]);

  useEffect(() => {
    if (tempEditParam && drag && drop) {
      const newList = [...optionsWithUUid!];

      const dragIndex = newList.findIndex((item) => item.uuid === drag);
      const dropIndex = newList.findIndex((item) => item.uuid === drop);

      const dragItem = newList[dragIndex];
      newList.splice(dragIndex, 1);
      newList.splice(dropIndex, 0, dragItem);

      onFieldChange('config', {
        ...tempEditParam.config,
        options: newList,
      });
    }
  }, [drag, drop]);

  return (
    <>
      <p>选项</p>
      <SelectList>
        {optionsWithUUid?.map((it, i) => {
          return (
            <SelectItem
              key={`options-${i}`}
              id={it.uuid}
              draggable={true}
              onDragStart={(e) => onDrag(it.uuid, e)}
              onDragOver={(e) => onDragOver(it.uuid, e)}
              onDrop={onDrop}
            >
              <>
                <HolderOutlined
                  style={{ touchAction: 'none', cursor: 'move' }}
                  rev={undefined}
                />
                <Input
                  bordered={false}
                  value={it.value}
                  onChange={(e) => {
                    const newOptions = [...optionsWithUUid!];
                    newOptions[i] = {
                      ...newOptions[i],
                      value: e.target.value,
                    };
                    onFieldChange('config', {
                      ...tempEditParam.config,
                      options: newOptions,
                    });
                  }}
                />
              </>
              <MinusCircleOutlined
                rev={undefined}
                onClick={() => {
                  const newOptions = [...optionsWithUUid!];
                  newOptions.splice(i, 1);
                  onFieldChange('config', {
                    ...tempEditParam.config,
                    options: newOptions,
                  });
                }}
              />
            </SelectItem>
          );
        })}
        <div
          className="add-item"
          onClick={() => {
            const newOptions = [...optionsWithUUid!];
            newOptions.push({
              uuid: `${+Date.now()}-${Math.random()}`,
              value: '',
              title: '',
            });

            onFieldChange('config', {
              ...tempEditParam.config,
              options: newOptions,
            });
          }}
        >
          + 添加选项
        </div>
      </SelectList>
    </>
  );
}

const ParamTypeList = styled.div`
  display: flex;
`;

const ParamTypeItem = styled.div`
  display: flex;
  flex-direction: column;
  width: 80px;
  padding: 12px 20px;
  margin-right: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  align-items: center;
  cursor: pointer;

  &.selected {
    border-color: #2c37cc;

    background-color: rgba(149, 180, 243, 0.1);

    .anticon {
      color: #2c37cc;
    }
  }
`;

const SelectList = styled.div`
  .add-item {
    cursor: pointer;
    background-color: #f2f2f2;
    padding: 8px 8px;
    border-radius: 8px;
  }
`;

const SelectItem = styled.div`
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #eee;
  padding: 8px 8px;
  border-radius: 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;

  &:hover {
    border-color: #f0a878;
    background-color: rgba(248, 203, 173, 0.1);
  }

  .ant-input {
    width: calc(100% - 20px);
  }
`;
