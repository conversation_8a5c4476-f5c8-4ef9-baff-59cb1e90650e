import React, { useState, useEffect } from 'react';
import { styled } from 'styled-components';
import {
  QuestionCircleOutlined,
  UserOutlined,
  PlusOutlined,
  EditOutlined,
  CloseOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  Popover,
  Card,
  Modal,
  List,
  Avatar,
  Skeleton,
  Empty,
  Pagination,
  Button, Tooltip,
  Space,
  Flex,
} from 'antd';
import CardCollapse from '../card-collapse';
import { useRequest } from 'ahooks';
import { AppApi } from '@/api/app';
import DiffAlert from '../diff-alert';
import HumanDrawer from './human-drawer';

const Guide = styled.pre`
  width: 400px;
  word-wrap: normal;
  white-space: pre-wrap;
`;

const guide = (
  <Guide>
    {
      '每次对话开始前，发送一个初始内容。支持标准 Markdown 语法，可使用的额外标记:\n[快捷按键]: 用户点击后可以直接发送该问题'
    }
  </Guide>
);

const AvatarUploader = styled.div`
  width: 104px;
  height: 104px;
  margin-bottom: 8px;
  text-align: center;
  vertical-align: top;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 2px;
  cursor: pointer;
  transition: border-color 0.3s;

  &:hover {
    border-color: #1890ff;
  }
`;

const AvatarUploaderIcon = styled(PlusOutlined)`
  font-size: 28px;
  color: #999;
  margin-top: 32px;
`;

export interface ToolsCardProps {
  appId: string;
  value: any;
  oldValue: any;
  onChange: (item: any) => void;
  expandLocalKey: string;
  disabled?: boolean;
}

export const HumanModelCard: React.FC<ToolsCardProps> = ({
  appId,
  onChange,
  value,
  oldValue,
  expandLocalKey,
  disabled
}) => {
  const selectedAvatar = value?.[0];

  return (
    <CardCollapse
      expandLocalKey={expandLocalKey}
      moduleKey="portraitConfigs"
      collapsible={disabled ? "disabled" : 'icon'}
      items={[
        {
          key: 'portraitConfigs',
          label: (
            <Space>
              <span>
                <span style={{ fontWeight: 'bold' }}>聊天设置</span>
                <DiffAlert
                  title="聊天设置"
                  type="portraitConfigs"
                  newValue={value}
                  oldValue={oldValue}
                  onRedo={() => {
                    onChange(oldValue);
                  }}
                />
              </span>
              <Tooltip title={<Flex vertical gap={8}>
                <div>给虚拟人配置合适的肖像进行展示。</div>
                <a
                  rel="noopener"
                  target='_blank'
                  href='https://music-doc.st.netease.com/st/langbase-doc/virtual-human#%E8%82%96%E5%83%8F%E7%AE%A1%E7%90%86'
                >查看详情</a>
              </Flex>}>
                <InfoCircleOutlined />
              </Tooltip>
            </Space>
          ),
          children: (
            <Card.Grid style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 20 }}>
                <div>人物肖像</div>
                {selectedAvatar ? (
                  <Avatar shape="square" src={selectedAvatar?.url} size={88} />
                ) : (
                  <HumanDrawer
                    appId={appId}
                    value={selectedAvatar}
                    onChange={onChange}
                    trigger={
                      <AvatarUploader>
                        <AvatarUploaderIcon />
                        <div style={{ marginTop: 8 }}>选择形象</div>
                      </AvatarUploader>
                    }
                  />
                )}
              </div>
            </Card.Grid>
          ),
          extra: !disabled && (
            <>
              <HumanDrawer
                appId={appId}
                value={selectedAvatar}
                onChange={onChange}
                trigger={
                  <Tooltip title='添加人物肖像'>
                    <Button
                      type="text"
                      size="small"
                      icon={<PlusOutlined style={{ fontSize: 10, color: '#666' }} />}
                    />
                  </Tooltip>
                }
              />
            </>
          ),
        },
      ]}
    />
  );
};

export default HumanModelCard;
