import { AppApi } from "@/api/app";
import { EditOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import { Avatar, List, Modal } from "antd";
import { useState } from "react";
import styled from "styled-components";
import ImageGallery from "../../../resource/human-model";

const StyledListItem = styled(List.Item)`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  position: relative;
`;

const StyledAvatar = styled(Avatar)`
  /* width: 100%; */
  height: auto;
  /* max-height: 64px; */
  object-fit: cover;
  cursor: pointer;
`;

const HumanDrawer = (props) => {
  const { appId, value, trigger, onChange } = props;
  const [isModalVisible, setIsModalVisible] = useState(false);

  const [selectedAvatar, setSelectedAvatar] = useState<any>();
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 12;

  const { data: data2D = [], loading: loading2D } = useRequest(() => AppApi.portraitQuery({
    appId,
    type: 'PORTRAIT_2D',
    pageNum: currentPage,
    pageSize: pageSize,
  }).then(res => res), {
    refreshDeps: [isModalVisible, currentPage],
  });

  const handleOk = () => {
    onChange?.(selectedAvatar ? [{
      type: 'PORTRAIT_2D',
      ...(selectedAvatar || {})
    }] : [])
    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const showModal = () => {
    setSelectedAvatar(value);
    setIsModalVisible(true);
  };

  const onSelect = (checked, item) => {
    if (checked && item?.url !== selectedAvatar?.url) {
      setSelectedAvatar(item);
      return;
    }

    if (!checked && item?.url === selectedAvatar?.url) {
      setSelectedAvatar(undefined);
      return;
    }
  }


  return <div onClick={e => e.stopPropagation()}>
    <span onClick={showModal}>
      {trigger || <>
        <EditOutlined /> 编辑
      </>}
    </span>

    <Modal
      title="人物肖像"
      open={isModalVisible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
    >
      {/* {loading2D && <Skeleton active />}
      {!loading2D && !data2D?.[0]?.values?.[0]?.infoList.length && <Empty />}
      {
        !loading2D && data2D?.[0]?.values?.[0]?.infoList.length &&
        <SelectModeList
          data={data2D}
          value={selectedAvatar}
          onSelect={onSelect}
        />
      }
      <Pagination
        current={currentPage}
        total={data2D?.[0]?.total}
        pageSize={pageSize}
        onChange={(page) => setCurrentPage(page)}
        style={{ marginTop: 16, textAlign: 'center' }}
      /> */}
      <ImageGallery
        appId={appId}
        selectionProps={{
          value: selectedAvatar,
          onSelect
        }} />
    </Modal>
  </div>
};

export default HumanDrawer;
