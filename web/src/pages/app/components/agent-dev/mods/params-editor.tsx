import {
  MinusCircleOutlined,
  PlusCircleOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Card, Input, message, Space, Switch } from 'antd';
import { isEqual } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';

import { DragSortTable } from '@/components/drag-sort-table';
import { IAgentParam, ParamTypeEnum, ParamTypeIconMap } from '@/interface/agent';

import { ParamsEditModal } from './params-edit-modal';

// TODO: delete
// const mockData = [
//   {
//     type: ParamTypeEnum.select,
//     key: 'appType',
//     title: '应用类型',
//     required: true,
//     config: {
//       options: [
//         {
//           value: 'ReactNative',
//         },
//         {
//           value: 'Mobile',
//         },
//       ],
//     },
//   },
//   {
//     type: ParamTypeEnum.textArea,
//     key: 'default_input',
//     title: '查询内容',
//     required: true,
//     config: {},
//   },
//   {
//     type: ParamTypeEnum.textInput,
//     key: 'extra_input',
//     title: '其他输入',
//     required: false,
//     config: {
//       length: 48,
//     },
//   },
// ] as IAgentParam[];

interface IParamsEditorProps {
  value: IAgentParam[];
  onChange: (value: IAgentParam[]) => void;
}

export function ParamsEditor(props: IParamsEditorProps) {
  const [paramsList, setParamsList] = useState<IAgentParam[]>(props.value);
  const [editIndex, setEditIndex] = useState(-1);

  useEffect(() => {
    setParamsList(props.value);
  }, [props.value]);

  useEffect(() => {
    if (props.onChange && !isEqual(props.value, paramsList)) {
      props.onChange(paramsList);
    }
  }, [paramsList]);

  // 当前正在编辑的参数实体
  const editParam = useMemo(() => {
    if (editIndex === -1) {
      return null;
    }
    return paramsList[editIndex];
  }, [editIndex, paramsList]);

  const onAddClick = () => {
    if (paramsList.findIndex((it) => it.key === '') !== -1) {
      // 当出现多个 key 为空的行时，在拖动排序时会出现 Bug，此处通过交互限制避免此Bug
      message.warning('请先填写完当前参数');
      return;
    }
    const newParamsList = [
      ...paramsList,
      {
        type: ParamTypeEnum.textInput,
        key: '',
        title: '',
        required: false,
        config: {
          length: 48,
          options: [],
        },
      },
    ];
    setParamsList(newParamsList);
  };

  const onItemDelete = (index: number) => {
    const newParamsList = paramsList.filter((_, i) => i !== index);
    setParamsList(newParamsList);
  };

  const onItemChange = (index: number, key: string, value: any) => {
    const newParamsList = paramsList.map((it, i) => {
      if (i === index) {
        return {
          ...it,
          [key]: value,
        };
      }
      return it;
    });
    setParamsList(newParamsList);
  };

  const onModalSave = (newEditParam: IAgentParam) => {
    const newParamsList = paramsList.map((it, i) => {
      if (i === editIndex) {
        return newEditParam;
      }
      return it;
    });
    setParamsList(newParamsList);
    setEditIndex(-1);
  };

  const columns = [
    {
      title: '变量 KEY',
      dataIndex: 'key',
      key: 'key',
      render: (key: string, record: IAgentParam, index: number) => {
        let newContent = key;
        return (
          <ParamKeyInput>
            <span style={{ fontSize: '12px' }}>{ParamTypeIconMap[record.type]}</span>
            <Input
              placeholder="key"
              bordered={false}
              defaultValue={newContent}
              onChange={(e) => {
                newContent = e.target.value;
              }}
              onBlur={() => {
                onItemChange(index, 'key', newContent);
              }}
            />
          </ParamKeyInput>
        );
      },
    },
    {
      title: '字段名称',
      dataIndex: 'title',
      key: 'title',
      render: (title: string, record: IAgentParam, index: number) => {
        let newContent = title;
        return (
          <Input
            placeholder="title"
            bordered={false}
            defaultValue={newContent}
            onChange={(e) => {
              newContent = e.target.value;
            }}
            onBlur={() => {
              onItemChange(index, 'title', newContent);
            }}
          />
        );
      },
    },
    {
      title: '可选',
      dataIndex: 'required',
      render: (required: boolean, record: IAgentParam, index: number) => {
        return (
          <Switch
            checked={!required}
            onChange={(v) => {
              onItemChange(index, 'required', !v);
            }}
          />
        );
      },
      key: 'required',
    },
    {
      title: '操作',
      key: 'param_operate',
      render: (_: any, record: IAgentParam, index: number) => {
        return (
          <Space>
            <SettingOutlined rev={undefined} onClick={() => setEditIndex(index)} />
            <MinusCircleOutlined rev={undefined} onClick={() => onItemDelete(index)} />
          </Space>
        );
      },
    },
  ];

  return (
    <ParamCard
      title="变量"
      extra={
        <Space size="small" onClick={onAddClick}>
          <PlusCircleOutlined rev={undefined} />
          <span>添加</span>
        </Space>
      }
    >
      <DragSortTable
        dataSource={paramsList}
        columns={columns}
        onSortChange={setParamsList}
      />
      <ParamsEditModal
        editParam={editParam}
        onSave={onModalSave}
        onCancel={() => setEditIndex(-1)}
      />
    </ParamCard>
  );
}

const ParamCard = styled(Card)`
  height: 100%;
  & .ant-card-head {
    min-height: 40px;
  }

  & .ant-card-body {
    padding: 0px;
  }
`


const ParamKeyInput = styled.div`
  display: flex;
  align-items: center;

  .ant-input {
    padding-left: 4px;
  }
`;
