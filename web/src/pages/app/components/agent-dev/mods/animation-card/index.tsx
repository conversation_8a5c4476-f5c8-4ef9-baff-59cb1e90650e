import { Space } from "antd";
import CardCollapse from "../card-collapse";
import DiffAlert from "../diff-alert";
import AnimationForm from "./animation-form";

export const animationStr = value => {
  let res = '';

  if (value) {
    res = `
【默认动画】： ${value.mainDefaultId || ''}
【可选动画】： ${value.resourceIds || ''}
    `;
  } else {
    res = `
【动画配置】：未配置
    `;
  }
  return res;
}

const AnimationCard = (props) => {
  const { oldValue, value, onChange, expandLocalKey, disabled } = props;
  return <>
    <CardCollapse
      expandLocalKey={expandLocalKey}
      moduleKey="animationConfig"
      collapsible={disabled ? "disabled" : 'icon'}
      items={[{
        key: 'animationConfig',
        label: <Space>
          <span>
            <span style={{ fontWeight: 'bold' }}>动画设置</span>
            <DiffAlert
              title="动画设置"
              type="animationConfig"
              oldValue={oldValue}
              newValue={value}
              onRedo={() => {
                onChange(oldValue);
              }}
            />
          </span>
        </Space>,
        children: <>
          <AnimationForm onChange={onChange} value={value} appId={props.appId} />
        </>
      }
      ]}
    />
  </>
};

export default AnimationCard; 