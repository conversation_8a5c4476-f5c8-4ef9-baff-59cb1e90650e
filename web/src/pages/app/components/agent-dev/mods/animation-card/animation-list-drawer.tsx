import { But<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import Cycle from "../../../resource/animation/cycle";
import { useState } from "react";
import VideoPreview from '@/components/video-preview';

const AnimationListDrawer = (props) => {
  const { appId, onChange, value, trigger, type = 'checkbox' } = props;

  const [open, setOpen] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const onOpen = () => {
    setSelectedRowKeys(value);
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onSelect = (record, selected) => {
    if (type === 'radio') {
      setSelectedRowKeys([record.id]);
      return;
    }

    const newData = [...(selectedRowKeys || [])];
    const idx = newData.findIndex(d => d === record.id);

    if (selected) {
      if (idx < 0) {
        newData.push(record.id);
      }
    } else {
      if (idx > -1) {
        newData.splice(idx, 1);
      }
    }

    setSelectedRowKeys(newData);
  };

  const onSubmit = () => {
    onChange?.(selectedRowKeys);
    setOpen(false);
  };

  return <>
    <Flex onClick={onOpen}>
      {trigger || <Button>修改</Button>}
    </Flex>
    <Drawer title="修改"
      width={800}
      open={open}
      maskClosable={false}
      extra={<Space>
        <Button onClick={onClose}>取消</Button>
        <Button type="primary" onClick={onSubmit}>提交</Button>
      </Space>}
      onClose={onClose}
    >
      <Cycle
        queryData={{ type: '' }}
        appId={appId}
        rowSelection={{
          hideSelectAll: true,
          onSelect,
          selectedRowKeys,
          type
        }} />
    </Drawer>
  </>
}

export default AnimationListDrawer;
