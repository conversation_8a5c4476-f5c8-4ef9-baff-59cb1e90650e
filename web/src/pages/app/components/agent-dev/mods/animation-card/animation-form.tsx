import { CloseOutlined, DeleteOutlined, EditOutlined, Ellip<PERSON>Outlined, PlusOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { Flex, Input, Tooltip, Select, Spin } from "antd";
import { useMemo, useState } from "react";
import Cycle from "../../../resource/animation/cycle";
import AnimationListDrawer from "./animation-list-drawer";
import { useRequest } from "ahooks";
import { AnimationApi } from "@/api";
import VideoPreview from "@/components/video-preview";

const defaultAnimationConfig = {
  mainDefaultId: "",
  resourceIds: []
};

const AnimationForm = (props) => {
  const { value, onChange, appId } = props;

  const mainDefaultId = value?.mainDefaultId || "";
  const resourceIds = value?.resourceIds || [];

  const { data: resourceItems, loading, cancel } = useRequest(async () => {
    if (!resourceIds?.length) return null;
    const res = await AnimationApi.getCycleList({
      appId: appId,
      type: 'CYCLE',
      pageNum: 1,
      pageSize: resourceIds?.length,
      ids: resourceIds
    });
    return res?.values;
  }, {
    refreshDeps: [JSON.stringify(resourceIds)],
    debounceWait: 500
  });

  const { data: mainDefaultItem, loading: defaultLoading } = useRequest(async () => {
    if (!mainDefaultId) return null;
    const res = await AnimationApi.getCycleList({
      appId: appId,
      type: 'CYCLE',
      pageNum: 1,
      pageSize: 1,
      ids: [mainDefaultId]
    });

    try {
      if (res?.debugInfo) {
        throw new Error(res?.debugInfo);
      }
      const assetInfo = {
        ...JSON.parse(res?.values?.[0]?.assetInfo || '{}'),
        workflow: JSON.parse(JSON.parse(res?.values?.[0]?.assetInfo)?.workflow || '{}'),
      };

      return assetInfo;
    } catch (error) {
      return null;
    }

  }, {
    refreshDeps: [mainDefaultId],
    debounceWait: 500
  });

  const onValueChange = (key, val) => {
    let newValue = { ...(value || defaultAnimationConfig) };

    if (key === 'mainDefaultId') {
      newValue.mainDefaultId = val;
    } else if (key === 'resourceIds') {
      // 确保所有值都是数字
      const numericValues = val.map(item => {
        const num = parseInt(item, 10);
        return isNaN(num) ? null : num;
      }).filter(item => item !== null);

      newValue.resourceIds = numericValues;
    }

    onChange(newValue);
  };

  const onDelete = (val) => {
    const newValue = {
      ...(value || {})
    }
    const newResourceIds = (value?.resourceIds || [])?.filter(id => id !== val);
    newValue.resourceIds = newResourceIds;
    onChange(newValue);
  }

  const onDeleteMain = () => {
    const newValue = {
      ...(value || {}),
      mainDefaultId: undefined
    }
    // delete newValue.mainDefaultId;
    onChange(newValue);
  }

  return (
    <>
      <Flex style={{ marginBottom: 10 }}>
        <Flex style={{ marginRight: 16, width: 60 }} align="center">
          默认动画
        </Flex>
        <Flex>
          {/* <Input
            value={mainDefaultId}
            onChange={(e) => onValueChange('mainDefaultId', e.target.value)}
            placeholder="请输入主默认ID"
            style={{ width: 300 }}
          /> */}
          <Spin spinning={defaultLoading}>
            {mainDefaultItem && mainDefaultId && <div style={{ position: 'relative', marginRight: 4 }}>
              <Flex
                align="center"
                style={{
                  position: 'absolute', right: 4, top: 4, width: 14, height: 14, background: '#fff', zIndex: 11,
                  cursor: 'pointer'
                }}
                onClick={e => {
                  e.stopPropagation();
                  onDeleteMain();
                }}
              >
                <CloseOutlined />
              </Flex>

              <VideoPreview src={mainDefaultItem?.mainUrl}
                // previewImgUrl={mainDefaultItem?.workflow?.nodes?.[0]?.content?.url}
                previewImgUrl={mainDefaultItem?.headPortraitUrl}
                height={100} />
            </div>}
          </Spin>
          <AnimationListDrawer
            appId={appId}
            type="radio"
            trigger={<Flex
              vertical
              align="center"
              justify="center"
              style={{
                border: '1px dashed #d9d9d9',
                width: '60px',
                height: '100px',
                cursor: 'pointer',
              }}
            >
              {value?.mainDefaultId ? (
                <>
                  <div>
                    <EditOutlined />
                  </div>
                  <div>编辑</div>
                </>
              ) : (
                <>
                  <div>
                    <PlusOutlined />
                  </div>
                  <div>添加</div>
                </>
              )
              }
            </Flex>
            }
            value={value?.mainDefaultId ? [value.mainDefaultId] : undefined}
            onChange={(val) => onValueChange('mainDefaultId', val?.[0])}
          />
        </Flex>
      </Flex>

      <Flex style={{ marginBottom: 10 }}>
        <Flex style={{ marginRight: 16, width: 60 }} align="center">
          可选动画
        </Flex>
        <Flex align="center">
          {/* <Select
            mode="tags"
            value={resourceIds}
            onChange={(val) => onValueChange('resourceIds', val)}
            placeholder="请输入资源ID，按回车添加多个"
            style={{ width: 300 }}
            tokenSeparators={[',']}
            onInputKeyDown={(e) => {
              // 只允许输入数字
              if (!/^\d$/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab', 'Enter', ','].includes(e.key)) {
                e.preventDefault();
              }
            }}
          /> */}
          <Spin spinning={loading}>
            <Flex gap={4} style={{ marginRight: 4 }}>
              {resourceItems?.slice(0, 4)?.map(item => {
                const assetInfo = {
                  ...JSON.parse(item.assetInfo || '{}'),
                  workflow: JSON.parse(JSON.parse(item.assetInfo)?.workflow || '{}'),
                }
                return <div style={{ position: 'relative' }}>
                  <Flex
                    align="center"
                    style={{
                      position: 'absolute', right: 4, top: 4, width: 14, height: 14, background: '#fff', zIndex: 11,
                      cursor: 'pointer'
                    }}
                    onClick={e => {
                      e.stopPropagation();
                      onDelete(item.id)
                    }}
                  >
                    <CloseOutlined />
                  </Flex>

                  <VideoPreview src={assetInfo?.mainUrl}
                    previewImgUrl={assetInfo?.workflow?.nodes?.[0]?.content?.url}
                    height={100} />
                </div>
              })}
            </Flex>
          </Spin>

          <AnimationListDrawer
            type="checkbox"
            appId={appId}
            trigger={
              <Flex
                vertical
                align="center"
                justify="center"
                style={{
                  border: '1px dashed #d9d9d9',
                  width: '60px',
                  height: '100px',
                  cursor: 'pointer',
                }}
              >
                {value?.resourceIds?.length > 4 ? (
                  <>
                    <div>
                      <EllipsisOutlined />
                    </div>
                    <div>更多</div>
                  </>
                ) : (
                  <>
                    <div>
                      <PlusOutlined />
                    </div>
                    <div>添加</div>
                  </>
                )}
              </Flex>
            }
            value={resourceIds}
            onChange={(val) => onValueChange('resourceIds', val)}
          />
        </Flex>
      </Flex>
    </>
  );
};

export default AnimationForm; 