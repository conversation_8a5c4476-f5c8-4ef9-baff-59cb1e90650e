import { Flex, Select, Space, Switch, Tooltip } from "antd";
import CardCollapse from "../card-collapse";
import DiffAlert from "../diff-alert";
import TtsForm from "./tts-form";
import { merge } from 'lodash';
import { InfoCircleOutlined } from "@ant-design/icons";


const defaultTtsConfig = {
  strategicConfig: {
    ttsStrategicType: "NONE",
    params: {
      tssRate: 100
    }
  },
  ttsConfigItems: [
    {
      emotionJudge: false,
      ttsType: "minimax",
      voiceId: "audiobook_female_1",
      voiceName: "网文小美"
    }
  ]
};

export const ttsStr = value => {
  let res = '';

  if (value?.strategicConfig?.ttsStrategicType === 'RANDOM') {
    const emotionJudge = value.ttsConfigItems?.[0]?.emotionJudge;
    res = `
【策略】： ${value.strategicConfig.ttsStrategicType === 'RANDOM' ? '概率发送语音' : value.strategicConfig.ttsStrategicType}
【概率】： ${value.strategicConfig.params?.tssRate || 0}%
【音色选择】： ${value.ttsConfigItems?.map(item => item.voiceName).join('，') || ''}

    `;
  } else {
    res = `
【策略】：不发送语音
    `;
  }
  return res;
}

const TtsCard = (props) => {
  const { oldValue, value, onChange, expandLocalKey, disabled } = props;
  const handleChange = (val) => {
    const newValue = merge({}, defaultTtsConfig, value);
    newValue.strategicConfig = {
      ...newValue.strategicConfig,
      ttsStrategicType: val ? 'RANDOM' : 'NONE'
    };
    onChange(newValue);
  }
  return <>
    <CardCollapse
      expandLocalKey={expandLocalKey}
      moduleKey="ttsConfig"
      collapsible={disabled ? "disabled" : 'icon'}
      items={[{
        key: 'ttsConfig',
        label: <Space>
          <span>
            <span style={{ fontWeight: 'bold' }}>语音输出设置</span>
            <DiffAlert
              title="语音设置"
              type="ttsConfig"
              oldValue={oldValue}
              newValue={value}
              onRedo={() => {
                onChange(oldValue);
              }}
            />
          </span>
          <Tooltip title={<Flex vertical gap={8}>
            <div>
              可以让虚拟人使用某一音色给用户发语音。发送概率模拟真人偶尔发语音的效果。（100%就是每次对话都会通过语音进行回复）
            </div>
            <a
              rel="noopener"
              target="_blank"
              href="https://music-doc.st.netease.com/st/langbase-doc/virtual-human#7-%E8%AF%AD%E9%9F%B3%E8%83%BD%E5%8A%9B"
            >查看详情</a>
          </Flex>}>
            <InfoCircleOutlined />
          </Tooltip>
        </Space>,
        children: <>
          <TtsForm onChange={onChange} value={value} appId={props.appId} />
        </>,
        extra: !disabled && <Switch
          checkedChildren="开启" unCheckedChildren="关闭"
          checked={value?.strategicConfig?.ttsStrategicType === 'RANDOM'}
          onChange={handleChange}
        />
        ,
      }
      ]}
    />
  </>
};

export default TtsCard; 