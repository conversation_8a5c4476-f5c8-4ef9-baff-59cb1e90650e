import { QuestionCircleOutlined, SoundOutlined, PlusOutlined } from "@ant-design/icons";
import { Flex, Form, Select, Slider, Switch, Tooltip, Button } from "antd";
import styled from "styled-components";
import { useState } from "react";
import VoiceSelector from "./voice-selector";
import { merge } from 'lodash';

const StyledForm = styled(Form)`
  .ant-form-item-label {
     width: 148px;
  }
`;


// 音色选项 - 基础选项

const TtsForm = (props) => {
  const { value, onChange, appId } = props;

  // 深度合并 value 和 defaultTtsConfig
  const newValue = merge({}, value);
  console.log("value", value, newValue);
  const enabled = newValue?.strategicConfig?.ttsStrategicType === "RANDOM";
  const strategicType = newValue?.strategicConfig?.ttsStrategicType || "NONE";
  const tssRate = newValue?.strategicConfig?.params?.tssRate || 0;
  const selectedVoice = newValue?.ttsConfigItems?.[0]?.voiceId || "";
  const emotionJudge = newValue?.ttsConfigItems?.[0]?.emotionJudge || false;
  const voiceName = newValue?.ttsConfigItems?.[0]?.voiceName || "网文小美";

  const [voiceSelectorVisible, setVoiceSelectorVisible] = useState(false);

  const onValueChange = (key, val) => {
    if (key === 'strategicType') {
      newValue.strategicConfig = {
        ...newValue.strategicConfig,
        ttsStrategicType: val
      };
    } else if (key === 'tssRate') {
      if (!newValue.strategicConfig.params) {
        newValue.strategicConfig.params = {};
      }
      newValue.strategicConfig.params = {
        ...newValue.strategicConfig.params,
        tssRate: val
      };
    } else if (key === 'voiceConfig') {
      if (!newValue.ttsConfigItems || !newValue.ttsConfigItems.length) {
        newValue.ttsConfigItems = [val];
      } else {
        newValue.ttsConfigItems[0] = { ...newValue.ttsConfigItems[0], ...val };
      }
    } else if (key === 'emotionJudge') {
      if (!newValue.ttsConfigItems || !newValue.ttsConfigItems.length) {
        newValue.ttsConfigItems = [{
          emotionJudge: val
        }];
      } else {
        newValue.ttsConfigItems[0] = {
          ...newValue.ttsConfigItems[0],
          emotionJudge: val
        };
      }
    }
    onChange(newValue);
  };

  // 处理音色选择
  const handleVoiceSelect = (voiceConfig) => {
    console.log("voiceConfig", voiceConfig);
    onValueChange('voiceConfig', voiceConfig);
  };

  return (
    <>
      {enabled ? (
        <>
          <Flex style={{ marginBottom: 10 }}>
            <Flex style={{ marginRight: 16 }}>
              发送概率
            </Flex>
            <Flex align="center" style={{ width: 300 }}>
              <Slider
                min={0}
                max={100}
                value={tssRate}
                onChange={(val) => onValueChange('tssRate', val)}
                style={{ flex: 1, marginRight: 12 }}
              />
              <div style={{ width: 50 }}>{tssRate}%</div>
            </Flex>
          </Flex>

          <Flex style={{ marginBottom: 10 }}>
            <Flex style={{ marginRight: 16 }}>
              音色选择
            </Flex>
            <Flex align="center">
              <Button
                icon={<SoundOutlined />}
                size="small"
                onClick={() => setVoiceSelectorVisible(true)}
                style={{ marginRight: 8, height: 26 }}
              >
                {voiceName || "选择音色"}
              </Button>
              <Tooltip title="选择合成语音的音色">
                <QuestionCircleOutlined style={{ marginLeft: 8 }} />
              </Tooltip>
            </Flex>
          </Flex>

          {/* <Flex style={{ marginBottom: 10 }}>
            <Flex style={{ marginRight: 16 }}>
              高级设置
            </Flex>
            <Flex align="center">
              <Switch
                checked={emotionJudge}
                onChange={(val) => onValueChange('emotionJudge', val)}
              />
              <span style={{ marginLeft: 8 }}>开启情绪判别</span>
              <Tooltip title="根据文本内容自动判断情绪并调整语音效果">
                <QuestionCircleOutlined style={{ marginLeft: 8 }} />
              </Tooltip>
            </Flex>
          </Flex> */}
        </>
      ) : <span style={{ color: '#999' }}>
        开启后，虚拟人会根据设置的概率进行语音回复
      </span>}

      {/* 音色选择弹窗 */}
      <VoiceSelector
        visible={voiceSelectorVisible}
        onClose={() => setVoiceSelectorVisible(false)}
        onSelect={handleVoiceSelect}
        appId={appId}
        currentVoiceId={selectedVoice}
      />
    </>
  );
};

export default TtsForm; 