import { SoundOutlined, PauseOutlined, SearchOutlined, CopyOutlined } from "@ant-design/icons";
import { Modal, Input, List, Tag, Button, Flex, Space, Spin, message, Pagination } from "antd";
import { useEffect, useState, useRef } from "react";
import { TtsApi } from "@/api/tts";
import { copyText } from '@/utils/common';

// 音色标签分类
const tagCategories = {
  // "语言": ["普通话"],
  "性别": ["男", "女"],
  "年龄": ["少年", "青年", "中年", "老年"],
};

// 标签颜色映射
const tagColorMap = {
  "男": "blue",
  "女": "pink",
  "少年": "lime",
  "青年": "green",
  "中年": "cyan",
  "老年": "gold",
  "普通话": "default",
  // 其他标签使用默认颜色
};

// 获取标签颜色
const getTagColor = (tag) => {
  return tagColorMap[tag] || "default";
};

const VoiceSelector = ({ visible, onClose, onSelect, appId, currentVoiceId }) => {
  const [loading, setLoading] = useState(false);
  const [voiceList, setVoiceList] = useState([]);
  const [filteredList, setFilteredList] = useState([]);
  const [selectedTagsByCategory, setSelectedTagsByCategory] = useState({});
  const [searchText, setSearchText] = useState("");
  const [playingId, setPlayingId] = useState(null);
  const audioRef = useRef(null);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  // 获取音色列表
  useEffect(() => {
    if (visible) {
      fetchVoiceList();
      // 重置选择的标签
      setSelectedTagsByCategory({});
      // 停止任何正在播放的音频
      if (playingId && audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    }
  }, [visible]);

  const fetchVoiceList = async () => {
    setLoading(true);
    try {
      const response = await TtsApi.getVoiceList({ appId });
      if (response && Array.isArray(response) && response.length > 0) {
        setVoiceList(response);
        setFilteredList(response);
      } else {
        console.error("获取音色列表失败，返回空数据");
        message.error("获取音色列表失败");
        setVoiceList([]);
        setFilteredList([]);
      }
    } catch (error) {
      console.error("获取音色列表失败:", error);
      message.error("获取音色列表失败");
      setVoiceList([]);
      setFilteredList([]);
    } finally {
      setLoading(false);
    }
  };

  // 播放/停止音频
  const toggleAudio = (voiceId, voiceUrl) => {
    if (playingId === voiceId) {
      // 停止播放
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      setPlayingId(null);
    } else {
      // 如果有其他音频在播放，先停止
      if (playingId && audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }

      // 开始播放新的音频
      if (audioRef.current && voiceUrl) {
        audioRef.current.src = voiceUrl;
        audioRef.current.play().catch(e => {
          console.error("音频播放失败:", e);
          message.error("音频播放失败");
          setPlayingId(null);
        });
        setPlayingId(voiceId);
      }
    }
  };

  // 音频播放结束
  const handleAudioEnded = () => {
    setPlayingId(null);
  };

  // 筛选音色
  useEffect(() => {
    let result = [...voiceList];

    // 按标签筛选 - 只考虑已选择的标签
    const selectedTags = Object.values(selectedTagsByCategory).filter(Boolean);
    if (selectedTags.length > 0) {
      result = result.filter(voice =>
        selectedTags.every(tag => voice.tag && voice.tag.includes(tag))
      );
    }

    // 按名称搜索
    if (searchText) {
      result = result.filter(voice =>
        voice.voiceName.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    setFilteredList(result);
  }, [selectedTagsByCategory, searchText, voiceList]);

  // 处理标签点击 - 修改为单选
  const handleTagClick = (category, tag) => {
    setSelectedTagsByCategory(prev => {
      const newState = { ...prev };

      // 如果已经选择了这个标签，则取消选择
      if (prev[category] === tag) {
        delete newState[category];
      } else {
        // 否则选择这个标签（替换同类别的其他标签）
        newState[category] = tag;
      }

      return newState;
    });
  };

  // 选择音色
  const handleSelect = (voice) => {
    // 停止任何正在播放的音频
    if (playingId && audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }

    onSelect({
      ttsType: voice.providerKind || "minimax",
      voiceId: voice.voiceId,
      voiceName: voice.voiceName,
      emotionJudge: false
    });
    onClose();
  };

  // 计算当前页的数据
  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredList.slice(startIndex, endIndex);
  };

  // 处理页码变化
  const handlePageChange = (page) => {
    setCurrentPage(page);
    // 切换页面时，停止任何正在播放的音频
    if (playingId && audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setPlayingId(null);
    }
  };

  // 当筛选结果变化时，重置页码到第一页
  useEffect(() => {
    setCurrentPage(1);
  }, [filteredList]);

  return (
    <Modal
      title="音色选择"
      open={visible}
      onCancel={() => {
        // 关闭弹窗前停止任何正在播放的音频
        if (playingId && audioRef.current) {
          audioRef.current.pause();
          audioRef.current.currentTime = 0;
        }
        onClose();
      }}
      width={800}
      footer={null}
      bodyStyle={{
        padding: 0,
        display: 'flex',
        flexDirection: 'column',
        userSelect: 'none',
        maxHeight: '70vh'
      }}
    >
      <audio ref={audioRef} onEnded={handleAudioEnded} style={{ display: "none" }} />

      {/* 固定的筛选区域 */}
      <div style={{ padding: '16px 24px', borderBottom: '1px solid #f0f0f0' }}>
        {/* 标签筛选 - 修改为单选 */}
        <div>
          {Object.entries(tagCategories).map(([category, tags]) => (
            <div key={category} style={{ marginBottom: 8 }}>
              <span style={{ marginRight: 8, fontWeight: "bold" }}>{category}:</span>
              <Space size={[0, 8]} wrap>
                {tags.map(tag => (
                  <Tag
                    key={tag}
                    color={selectedTagsByCategory[category] === tag ? getTagColor(tag) : "default"}
                    style={{ cursor: "pointer" }}
                    onClick={() => handleTagClick(category, tag)}
                  >
                    {tag}
                  </Tag>
                ))}
              </Space>
            </div>
          ))}
        </div>
      </div>

      {/* 可滚动的音色列表区域 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        padding: '0 24px 16px',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Spin spinning={loading}>
          <List
            dataSource={getCurrentPageData()}
            renderItem={item => (
              <List.Item
                style={{
                  background: item.voiceId === currentVoiceId ? "rgba(24, 144, 255, 0.1)" : "transparent",
                  padding: "12px",
                  borderRadius: "4px",
                }}
                actions={[
                  <Button
                    type={item.voiceId === currentVoiceId ? "primary" : "default"}
                    onClick={() => handleSelect(item)}
                  >
                    {item.voiceId === currentVoiceId ? "已选" : "选择"}
                  </Button>
                ]}
              >
                <List.Item.Meta
                  title={
                    <Flex align="center" style={{ cursor: "default" }}>
                      <span style={{ marginRight: 8, cursor: "default" }}>{item.voiceName}</span>
                      <Button
                        type={playingId === item.voiceId ? "primary" : "text"}
                        icon={playingId === item.voiceId ? <PauseOutlined /> : <SoundOutlined />}
                        size="small"
                        onClick={() => toggleAudio(item.voiceId, item.voiceUrl)}
                        loading={false}
                        disabled={!item.voiceUrl}
                      />
                    </Flex>
                  }
                  description={
                    <Flex vertical>
                      <span style={{ margin: '-5px 0 5px', fontSize: 12 }}>
                        {item.voiceId} <CopyOutlined onClick={() => copyText(item.voiceId)} />
                      </span>
                      <Space size={[0, 4]} wrap>
                        {item.tag && item.tag.map(tag => (
                          <Tag key={tag} color={getTagColor(tag)}>
                            {tag}
                          </Tag>
                        ))}
                      </Space>
                    </Flex>
                  }
                />
              </List.Item>
            )}
            locale={{ emptyText: "暂无音色数据" }}
          />

          {/* 分页器 */}
          {filteredList.length > 0 && (
            <div style={{
              marginTop: '16px',
              display: 'flex',
              justifyContent: 'flex-end',
              padding: '0 12px'
            }}>
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={filteredList.length}
                onChange={handlePageChange}
                // size="small"
                showSizeChanger={false}
              />
            </div>
          )}
        </Spin>
      </div>
    </Modal>
  );
};

export default VoiceSelector;