import { Space, Table, Form, Typography, Alert, Radio, Input, Switch, message, Popconfirm, Button, Select } from 'antd';
import hljs from 'highlight.js/lib/core';
import javascript from 'highlight.js/lib/languages/javascript';
import { useRequest } from 'ahooks';
import { PopoApi, getBaseUrl } from '@/api/popo';
import ActionModal from '@/components/action-modal';
import TextArea from 'antd/es/input/TextArea';
import { useEffect, useMemo, useRef, useState } from 'react';
import { getAppId } from '@/utils/state';
import { IAppType } from '@/interface';

const { Paragraph, Text } = Typography;

const FormItem = Form.Item;

const getColumns = (run, token, appType) => {
  return [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'popoId',
      dataIndex: 'popoId',
      key: 'popoId',
    },
    {
      title: '机器人账号',
      dataIndex: 'robotUid',
      key: 'robotUid',
    },
    {
      title: '是否开启',
      dataIndex: 'open',
      key: 'open',
      render: v => {
        return !!v ? '✅' : '❌'
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          {record.type === 'robot' ?
            <OpenAction record={record} refresh={run} />
            :
            <SubAction record={record}></SubAction>
          }
          <AddEditAction refresh={run} record={record} token={token} appType={appType}></AddEditAction>
          <DeleteAction record={record} refresh={run} />
        </Space>
      ),
    },
  ];
}

export const PopoCard = ({ token, appID, appType }) => {
  const { data, run } = useRequest(() => PopoApi.list(appID, appType === IAppType.AgentWorkflow));
  console.log('data', data)
  if (!token?.items?.length) {
    return <div>请先点击【新增】，生成API密钥</div>
  }

  return (
    <div style={{ position: 'relative' }} >
      <AddEditAction refresh={run} record={null} token={token} appType={appType}></AddEditAction>
      <Table dataSource={data} columns={getColumns(run, token, appType)} />
    </div >
  )
};

/**
 * 订阅行为
 * @param param0 
 * @returns 
 */
const SubAction = ({ record }) => {
  return <ActionModal title="订阅信息">
    <Form layout='vertical'>
      <FormItem
        label="服务URL"
        help="请将该字段填写到【开发者模式】中的【URL】字段"
        name="serverUrl" >
        <Paragraph underline copyable style={{ marginBottom: 0 }} children={`${getBaseUrl()}/api/ada/popo/cb?popoId=${record.popoId}&appId=${record.appId}`} />
      </FormItem>
      <FormItem
        label="Token"
        children={<Paragraph underline copyable>adaPopo</Paragraph>}
        name="token"
        help="请将该字段填写到【开发者模式】中的【token】字段" />
      {record.type === "robotv2" && (
        <FormItem
          label="aesKey"
          children={<Paragraph underline copyable>{record.config.aesKey}</Paragraph>}
          name="config.aesKey"
          help="请将该字段填写到【开发者模式】中的【aesKey】字段" />
      )}
    </Form>
  </ActionModal>
}

/**
 * 添加或删除行为
 * @param param0 
 * @returns 
 */
const AddEditAction = ({ record, refresh, token, appType }) => {
  const appId = getAppId();
  const [form] = Form.useForm();
  const type = Form.useWatch('type', form);
  const group2sms = Form.useWatch(['config', 'group2sms'], form);
  const manual = Form.useWatch(['config', 'manual'], form);
  const customCard = Form.useWatch(['config', 'customCard'], form);

  useEffect(() => {
    form.setFieldsValue({ ...(record || { type: 'robot' }), secret: '' });
  }, [record])

  const handleOk = async () => {
    const valid = await form.validateFields();
    const data = { ...(record || {}), ...valid };
    console.log("valid", data);
    let action = PopoApi.create;
    if (record) {
      action = PopoApi.update;
    }
    const res: any = await action({
      ...data,
      config: {
        ...data.config,
        isWorkflow: appType === IAppType.AgentWorkflow,
        langbaseId: appId,
        langbaseToken: token?.items[0].token
      }
    });
    console.log('res', res);
    if (res) {
      message.success(`${record ? '更新' : '新增'}成功`);
    }
    // 如果是新增，则需要重置
    if (!record) {
      form.resetFields();
    }
    refresh();
  }

  const cardHelp = useMemo(() => {
    if (customCard === 'custom') {
      return "请确保数据源变量跟真实输出参数对应，其中至少有一个output";
    }
    if (customCard === 'markdown') {
      return "开启后，用户发送的消息会以markdown渲染";
    }
    return "可以开启自定义卡片渲染";
  }, [customCard]);

  return <ActionModal title={record ? "编辑" : "新增"} action={record ? null : <Button type="link" style={{ float: 'right' }}>新增</Button>} onOk={handleOk}>
    <Form labelCol={{ span: 5, offset: 0 }} form={form}>
      <Alert
        message="填写以下内容表明您已授权 LangBase 帮你进行POPO服务运维，LangBase 会保证不会泄露您的信息"
        style={{
          marginBottom: "10px",
          width: "100%",
          margin: "10px auto"
        }}
        showIcon />
      <FormItem
        label="类型"
        children={<Radio.Group buttonStyle="solid">
          <Radio.Button value="robot">机器人</Radio.Button>
          <Radio.Button value="robotv2">机器人2.0</Radio.Button>
          <Radio.Button value="server">服务号</Radio.Button>
        </Radio.Group>}
        help="机器人是邮件申请的，机器人2.0是开发后台创建的，注意区别"
        name="type"
        rules={[{ required: true }]} />
      <FormItem
        children={<Input placeholder={type === "robot" ? "请输入popo机器人的appid" : type === "robotv2" ? "请输入后台生成的AppKey" : "请输入后台生成的AppId"} />}
        name="popoId"
        label={type === "robotv2" ? "appKey" : "appid"}
        rules={[{ required: true }]} />
      <FormItem
        children={<Input placeholder={type === "robot"
          ? "请输入popo机器人的secret"
          : "请输入后台生成的AppSecret"} />}
        name="secret"
        label="secret"
        help="请确保填写正确，该字段保存后将不在可见"
        rules={[{ required: true }]} />
      <FormItem
        label="机器人账号"
        children={<Input></Input>}
        name="robotUid"
        rules={[{ required: true }]}
        help={type === "robot" ? "请输入机器人账号" : "2.0和服务号可输入任意代号，只是用来标识)"} />
      <FormItem
        label="消息卡片"
        help={cardHelp}
        children={<Select defaultValue="default" options={[{ label: '不使用', value: 'default' }, { value: 'markdown', label: 'markdown渲染' }, { value: 'custom', label: '自定义卡片' }]} />}
        hidden={type === 'server'}
        name={['config', 'customCard']}
      />
      <FormItem label="卡片模板Id" children={<Input></Input>}
        name={['config', 'templateId']}
        hidden={customCard !== 'custom'} rules={[{ required: customCard === 'custom' }]} />
      <FormItem
        label="私信默认回复"
        children={<TextArea placeholder="默认为：已收到你的消息，这就帮您处理" />}
        name={['config', 'smsResponse']}
      ></FormItem>
      <FormItem
        label="群聊默认回复"
        children={<TextArea placeholder="默认为：已收到你的消息，这就帮您处理" />}
        hidden={type === 'server'}
        name={['config', 'groupResponse']}
      />
      <FormItem
        label="人工服务"
        children={<Switch />}
        name={['config', 'manual']}
        hidden={type !== 'robot'}
        extra="开启后用户输入找人工客服相关的话语或【rg】关键词会自动把用户和客服拉群" />
      <FormItem
        label="客服邮箱"
        children={<Input />}
        name={['config', 'csMail']}
        hidden={!manual}
        extra="可以输入客服邮箱/http接口(?mail=请求用户邮箱)"
        rules={[{ required: manual }]} />
      <FormItem
        label="回调地址"
        children={<Input />}
        name={['config', 'manualServiceCb']}
        hidden={!manual}
        extra="群号会通过该接口返回（?tid=群号）" />
      <FormItem
        label="群聊转私信"
        children={<Switch />}
        name={['config', 'group2sms']}
        hidden={type !== 'robot'}
        extra="开启后群聊的内容会自动通过私信回复" />
      <FormItem
        label="转私信默认回复"
        children={<TextArea placeholder="不填则跟群聊默认回复一致" />}
        hidden={!group2sms}
        name={['config', 'group2smsResponse']}
      />
      <FormItem
        label="私信开场白"
        children={<TextArea defaultValue="您好，已收到您来自群: {tid} 问题咨询：【{question}】，正在为您解答" />}
        hidden={!group2sms}
        name={['config', 'group2smsGreeting']} />
      <FormItem
        label="关注开场白"
        children={<TextArea />}
        hidden={type !== 'server'}
        name={['config', 'subGreeting']}
        extra="模板变量：tid=群号，question=用户提问" />
      <FormItem
        label="菜单配置"
        children={<TextArea />}
        hidden={type !== 'server'}
        name={['config', 'menus']} />
      <FormItem
        label="生效群号"
        children={<TextArea placeholder="群号逗号分割，不填默认所有群生效" />}
        hidden={!group2sms}
        name={['config', 'groups']} />
    </Form>
  </ActionModal>
}


/**
 * 删除行为
 * @param param0 
 * @returns 
 */
const DeleteAction = ({ record, refresh }) => {
  const handleDelete = async () => {
    const res: any = await PopoApi.remove(record);
    console.log('res', res);
    if (res) {
      message.success('删除成功');
    }
    refresh();
  }

  return <Popconfirm title="是否确认删除？" onConfirm={handleDelete}>
    <a>删除</a>
  </Popconfirm>
}

/**
 * 开启行为
 * @param param0 
 * @returns 
 */
const OpenAction = ({ record, refresh }) => {
  const appId = getAppId();
  const handleDelete = async () => {
    const res: any = await PopoApi.open(record.popoId, appId, !record.open);
    console.log('res', res);
    if (res) {
      message.success('修改成功');
    }
    refresh();
  }

  return <Popconfirm title={`是否确认${record.open ? '关闭' : '开启'}？`} onConfirm={handleDelete}>
    <a>
      {record.open ? '关闭' : '开启'}
    </a>
  </Popconfirm>
}
