import React, { useState, useEffect } from 'react';
import { Drawer, Timeline, Button, message, Space, Spin, Modal } from 'antd';
import { useRequest } from 'ahooks';
import { ClockCircleOutlined, UserOutlined, TagOutlined, RollbackOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { MultipleConfigApi } from '@/api/multiple-setting';

interface ConfigHistoryItem {
  id: string;
  timestamp: string;
  createdBy: any;
  createdAt: string;
  config: any;
  description: string;
  configId: string;
}

interface ConfigHistoryDrawerProps {
  visible: boolean;
  settingId: string;
  appId: string;
  onClose: () => void;
  onConfigSwitch: (config: any) => void;
}

const TimelineWrapper = styled.div`
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding-right: 16px;
`;

const StyledDrawer = styled(Drawer)`
  .ant-drawer-body {
    padding: 24px;
  }
`;

const StyledTimeline = styled(Timeline)`
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  padding-right: 16px;
  padding-left: 20px;
  padding-top: 16px;
`;

const StyledTimelineItem = styled(Timeline.Item)`
  .ant-timeline-item-head {
    left: -6px;
  }
  
  .ant-timeline-item-content {
    margin-left: 18px;
    min-height: 48px;
    top: -6px;
    position: relative;
  }

  &:first-child {
    padding-top: 6px;
  }
`;

const TimelineItemContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const VersionButton = styled(Button)`
  align-self: flex-start;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 8px;
`;

const ConfigHistoryDrawer: React.FC<ConfigHistoryDrawerProps> = ({ visible, onConfigSwitch, appId, settingId, onClose }) => {
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<any | null>(null);

  const { data, loading, run } = useRequest(
    () => MultipleConfigApi.getSettingVersionedConfig({
      page,
      pageSize,
      settingId,
      appId
    }),
    {
      manual: true,
      onSuccess: (result: any, params) => {
        if (result?.list?.length < pageSize) {
          setHasMore(false);
        }
      },
    }
  );

  const [historyItems, setHistoryItems] = useState<ConfigHistoryItem[]>([]);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    if (visible) {
      setPage(1);
      setHistoryItems([]);
      setHasMore(true);
      run();
    }
  }, [visible, run]);

  useEffect(() => {
    if (data?.list) {
      setHistoryItems(prevItems => [...prevItems, ...data?.list]);
    }
  }, [data]);

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, clientHeight, scrollHeight } = event.currentTarget;
    if (scrollHeight - scrollTop === clientHeight && hasMore && !loading) {
      setPage(prevPage => prevPage + 1);
      run();
    }
  };

  const handleVersionSwitch = (version: any) => {
    message.info(`切换到版本 ${version}`);
    // 这里添加切换版本的逻辑
  };

  const showConfirmModal = (version: any) => {
    setSelectedVersion(version);
    setConfirmModalVisible(true);
  };

  const handleUseVersion = () => {
    if (selectedVersion) {
      // 这里添加使用选定版本配置的逻辑
      onConfigSwitch(selectedVersion);
      message.success(`已应用版本 ${selectedVersion?.configId} 的配置`);
      setConfirmModalVisible(false);
      // 可能需要刷新当前配置或关闭抽屉
      // onClose();
    }
  };

  return (
    <>
      <StyledDrawer
        title="配置保存历史"
        placement="right"
        onClose={onClose}
        visible={visible}
        width={600}
      >
        <TimelineWrapper onScroll={handleScroll}>
          <StyledTimeline>
            {historyItems.map((item) => (
              <StyledTimelineItem
                key={item.id}
                dot={<ClockCircleOutlined style={{ fontSize: '16px' }} />}
              >
                <TimelineItemContent>
                  <span>{item?.createdAt}</span>
                  <span>
                    <UserOutlined /> {item?.createdBy?.fullname}
                  </span>
                  <ButtonGroup style={{ alignItems: 'center' }}>
                    {/* <VersionButton
                      type="link"
                      icon={<TagOutlined />}
                      onClick={() => handleVersionSwitch(item)}
                    >
                      {item?.configId}
                    </VersionButton> */}
                    <a>
                      <Space>
                        <TagOutlined />
                        <span>{item?.configId}</span>
                      </Space>
                    </a>
                    <Button
                      type="primary"
                      size="small"
                      icon={<RollbackOutlined />}
                      onClick={() => showConfirmModal(item)}
                    >
                      使用此版本
                    </Button>
                  </ButtonGroup>
                  <p style={{ color: '#999' }}>{item.description || '该用户保存了配置（发布自动保存配置）'}</p>
                </TimelineItemContent>
              </StyledTimelineItem>
            ))}
            {loading && (
              <StyledTimelineItem dot={<Spin size="small" />}>
                加载中...
              </StyledTimelineItem>
            )}
          </StyledTimeline>
        </TimelineWrapper>
      </StyledDrawer>
      <Modal
        title="确认使用此版本配置"
        visible={confirmModalVisible}
        onOk={handleUseVersion}
        onCancel={() => setConfirmModalVisible(false)}
        okText="确认"
        cancelText="取消"
      >
        <p>您确定要使用版本 {selectedVersion?.configId} 的配置吗？这将覆盖当前的配置。</p>
      </Modal>
    </>
  );
};

// const fetchConfigHistory = async (settingId: string, page: number, pageSize: number): Promise<ConfigHistoryItem[]> => {
//   // 这里应该是实际的API调用
//   // 为了演示，返回模拟数据
//   return new Promise((resolve) => {
//     setTimeout(() => {
//       resolve([
//         {
//           id: `${page}-1`,
//           timestamp: '2023-05-10 14:30:00',
//           version: `v1.${page}.1`,
//           description: '更新了某些设置',
//         },
//         {
//           id: `${page}-2`,
//           timestamp: '2023-05-09 11:20:00',
//           operator: '用户B',
//           version: `v1.${page}.0`,
//           description: '初始配置',
//         },
//       ]);
//     }, 1000);
//   });
// };

export default ConfigHistoryDrawer;