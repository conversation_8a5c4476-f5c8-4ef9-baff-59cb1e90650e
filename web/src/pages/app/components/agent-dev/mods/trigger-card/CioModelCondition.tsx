import React from 'react';
import Condition from '../../../virtual-human/condition';
import { toCioModel, toLogicalModel } from '../../../virtual-human/utils';


interface WithConditionTransformProps {
  value?: any;
  onChange?: (value: any) => void;
  options?: any[];
  isPreview?: boolean;
  keyInputProps?: Record<string, any>;
}

export const withConditionTransform = (WrappedComponent: typeof Condition) => {
  return function ConditionWithTransform({
    value,
    onChange,
    options,
    ...rest
  }: WithConditionTransformProps) {
    const handleChange = (newValue: any) => {
      if (onChange) {
        onChange(toCioModel(newValue, options));
      }
    };

    const transformedValue = value ? toLogicalModel(value) : undefined;

    return (
      <WrappedComponent
        value={transformedValue}
        onChange={handleChange}
        options={options}
        {...rest}
      />
    );
  };
};

export default withConditionTransform(Condition);