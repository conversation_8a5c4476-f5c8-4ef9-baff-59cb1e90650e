import React, { useEffect, useState } from 'react';
import { Form, Space, Upload, Radio, Input, message, Button } from 'antd';
import { InboxOutlined, LinkOutlined } from '@ant-design/icons';
import { getAppId } from '@/utils/state';

interface UserSourceInfo {
  source: 'upload' | 'circle';
  uploadFileInfo?: {
    nosKey: string;
    url: string;
    fileName: string;
  };
  circleInfo?: {
    circleId: number;
  };
}

const UserSourceSelector: React.FC<{
  value?: UserSourceInfo;
  onChange?: (value: UserSourceInfo) => void;
}> = ({ value, onChange }) => {
  const [source, setSource] = useState<'upload' | 'circle'>(value?.source || 'upload');
  const [fileList, setFileList] = useState<any[]>([]);

  useEffect(() => {
    if (value?.source) {
      setSource(value.source);
    }

    if (value?.uploadFileInfo?.url) {
      setFileList([{
        uid: '-1',
        name: value.uploadFileInfo.fileName,
        status: 'done',
        url: value.uploadFileInfo.url,
      }]);
    } else {
      setFileList([]);
    }
  }, [value]);

  const handleSourceChange = (e) => {
    const newSource = e.target.value;
    setSource(newSource);
    onChange?.({ ...value, source: newSource });
  };

  const handleFileUpload = (info: any) => {
    const { status } = info.file;
    if (status === 'done') {
      message.success(`${info.file.name} 文件上传成功`);
      const fileInfo = { 
        nosKey: info.file.response.data?.key, 
        url: info.file.response.data?.uri,
        uid: info.file.uid,
        name: info.file.name,
      };
      setFileList([fileInfo]);
      onChange?.({
        ...value,
        source: 'upload',
        uploadFileInfo: { nosKey: fileInfo.nosKey, url: fileInfo.url, fileName: fileInfo.name },
      });
    } else if (status === 'error') {
      message.error(`${info.file.name} 文件上传失败`);
    }
    setFileList(info.fileList);
  };

  const handleRemove = () => {
    setFileList([]);
    onChange?.({
      ...value,
      source: 'upload',
      uploadFileInfo: undefined,
    });
  };

  const handleCircleIdChange = (circleId: number) => {
    onChange?.({
      ...value,
      source: 'circle',
      circleInfo: { circleId },
    });
  };

  const createCircleLink = 'https://opendata.hz.netease.com/st/bi-assets/st/erised-circle/circle?resultTable=mda.langbase_trigger_circle_resource_to_hive';
  const getCircleDetailLink = (circleId: number) => `https://opendata.hz.netease.com/st/bi-assets/st/erised-circle/circle?circleId=${circleId}`;

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Radio.Group value={source} onChange={handleSourceChange}>
        <Radio.Button value="upload">上传文件</Radio.Button>
        <Radio.Button value="circle">添加人群包</Radio.Button>
      </Radio.Group>
      {source === 'upload' && (
        <Form.Item label="上传人群包">
          <Upload.Dragger
            action={`/api/v1/app/${getAppId()}/uploadfile`}
            onChange={handleFileUpload}
            maxCount={1}
            fileList={fileList}
            onRemove={handleRemove}
            accept=".csv,.txt"
            // beforeUpload={(file) => {
            //   const isLt2M = file.size / 1024 / 1024 < 2;
            //   if (!isLt2M) {
            //     message.error('文件必须小于 2MB!');
            //   }
            //   return isLt2M;
            // }}
            progress={{
              strokeColor: {
                '0%': '#108ee9',
                '100%': '#87d068',
              },
              strokeWidth: 3,
              format: (percent) => `${parseFloat(percent.toFixed(2))}%`,
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽上传人群包文件</p>
            <p className="ant-upload-hint">支持 .csv 或 .txt 格式</p>
          </Upload.Dragger>
        </Form.Item>
      )}

      {source === 'circle' && (
        <Form.Item label="人群包 ID">
          <Space>
            <Input
              type="number"
              value={value?.circleInfo?.circleId}
              onChange={(e) => handleCircleIdChange(Number(e.target.value))}
              placeholder="输入人群包 ID"
              style={{ width: '200px' }}
            />
            {value?.circleInfo?.circleId ? (
              <Button
                type="link"
                icon={<LinkOutlined />}
                onClick={() => window.open(getCircleDetailLink(value.circleInfo.circleId), '_blank')}
              >
                查看人群包详情
              </Button>
            ) : (
              <Button
                type="link"
                icon={<LinkOutlined />}
                onClick={() => window.open(createCircleLink, '_blank')}
              >
                创建人群包
              </Button>
            )}
          </Space>
        </Form.Item>
      )}
    </Space>
  );
};

export default UserSourceSelector;