import React from 'react';
import { Card, Button, Input, Checkbox, Space, Tag, Tooltip } from 'antd';
import { PlusOutlined, CloseOutlined, MenuOutlined } from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { v4 as uuidv4 } from 'uuid';
import ImageUploader from '@/pages/app/workflow/react-node/imageUploader';
import { getSize, JSONParse } from '@/utils/common';

const { TextArea } = Input;

interface MessageGroup {
  id: string;
  name: string;
  contentList: MessageContent[];
}

interface MessageContent {
  id: string;
  content: string;
  type: 'TEXT' | 'IMG';
  tag: string[];
}

const SortableItem: React.FC<{
  id: string;
  children: React.ReactNode;
  onDelete: () => void;
  type: 'TEXT' | 'IMG';
}> = ({ id, children, onDelete, type }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <Card
        size="small"
        style={{ marginBottom: 16 }}
        title={type === 'TEXT' ? '文本消息' : '图片消息'}
        headStyle={{ backgroundColor: '#f0f0f0', padding: '8px 16px' }}
        extra={
          <Space>
            <div {...attributes} {...listeners} style={{ cursor: 'move' }}>
              <MenuOutlined />
            </div>
            <CloseOutlined onClick={onDelete} style={{ cursor: 'pointer', color: 'red' }} />
          </Space>
        }
      >
        {children}
      </Card>
    </div>
  );
};

interface CardGroupProps {
  value?: MessageGroup[];
  onChange?: (value: MessageGroup[]) => void;
}

const CardGroup: React.FC<CardGroupProps> = ({ value = [], onChange }) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const addGroup = () => {
    const newGroups = [...value, { id: uuidv4(), name: '新消息包', contentList: [] }];
    onChange?.(newGroups);
  };

  const deleteGroup = (groupId: string) => {
    const newGroups = value.filter(group => group.id !== groupId);
    onChange?.(newGroups);
  };

  const updateGroup = (groupId: string, updates: Partial<MessageGroup>) => {
    const newGroups = value.map(group =>
      group.id === groupId ? { ...group, ...updates } : group
    );
    onChange?.(newGroups);
  };

  const addContent = (groupId: string, type: 'TEXT' | 'IMG') => {
    const newGroups = value.map(group =>
      group.id === groupId
        ? {
          ...group, contentList: [...group.contentList, {
            id: uuidv4(),
            content: '',
            type,
            tag: []
          }]
        }
        : group
    );
    onChange?.(newGroups);
  };

  const updateContent = (groupId: string, contentId: string, updates: Partial<MessageContent>) => {
    const newGroups = value.map(group =>
      group.id === groupId
        ? {
          ...group, contentList: group.contentList.map(content =>
            content.id === contentId ? { ...content, ...updates } : content
          )
        }
        : group
    );
    onChange?.(newGroups);
  };

  const deleteContent = (groupId: string, contentId: string) => {
    const newGroups = value.map(group =>
      group.id === groupId
        ? { ...group, contentList: group.contentList.filter(content => content.id !== contentId) }
        : group
    );
    onChange?.(newGroups);
  };

  const handleDragEnd = (event: any, groupId: string) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const newGroups = value.map(group => {
        if (group.id === groupId) {
          const oldIndex = group.contentList.findIndex(item => item.id === active.id);
          const newIndex = group.contentList.findIndex(item => item.id === over.id);
          return {
            ...group,
            contentList: arrayMove(group.contentList, oldIndex, newIndex),
          };
        }
        return group;
      });
      onChange?.(newGroups);
    }
  };

  const toggleTag = (groupId: string, contentId: string, tag: string) => {
    const newGroups = value.map(group =>
      group.id === groupId
        ? {
          ...group, contentList: group.contentList.map(content =>
            content.id === contentId
              ? {
                ...content, tag: content.tag.includes(tag)
                  ? content.tag.filter(t => t !== tag)
                  : [...content.tag, tag]
              }
              : content
          )
        }
        : group
    );
    onChange?.(newGroups);
  };

  return (
    <div style={{ maxWidth: 800, margin: '0 auto' }}>
      <Button onClick={addGroup} icon={<PlusOutlined />} style={{ marginBottom: 16 }}>添加消息包</Button>
      {value.map((group) => (
        <Card
          key={group.id}
          title={
            <Input
              value={group.name}
              onChange={(e) => updateGroup(group.id, { name: e.target.value })}
              placeholder="请输入消息包名称"
              style={{ width: 200 }}
            />
          }
          extra={
            <CloseOutlined
              onClick={() => deleteGroup(group.id)}
              style={{ color: 'red', cursor: 'pointer' }}
            />
          }
          style={{ marginBottom: 16 }}
        >
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={(event) => handleDragEnd(event, group.id)}
          >
            <SortableContext
              items={group.contentList.map(content => content.id)}
              strategy={verticalListSortingStrategy}
            >
              {group.contentList.map((content) => {
                const contentData = JSONParse(content.content);
                return (
                  <SortableItem
                    key={content.id}
                    id={content.id}
                    onDelete={() => deleteContent(group.id, content.id)}
                    type={content.type}
                  >
                    {content.type === 'TEXT' ? (
                      <TextArea
                        value={content.content}
                        onChange={(e) => updateContent(group.id, content.id, { content: e.target.value })}
                        rows={4}
                        placeholder="请输入文本消息"
                      />
                    ) : (
                      <ImageUploader
                        value={{ url: contentData?.url || content.content }}
                        permanent
                        onChange={async (value) => {
                          console.log('value', value);
                          const size: any = await getSize(value?.file);
                          const newContent = {
                            url: value?.url,
                            width: size?.width,
                            nosKey: value?.key,
                            height: size?.height,
                            fileType: value?.file?.type,
                            name: value?.name,
                          }
                          console.log('content', content);
                          updateContent(group.id, content.id, { content: JSON.stringify(newContent) })
                        }
                        } max={1} type="Image" desc="上传图片" />
                    )}
                    <Space style={{ marginTop: 8 }}>
                      <Tag color={content.tag.includes('NO_MEMORY') ? 'green' : 'default'}>
                        <Checkbox
                          checked={content.tag.includes('NO_MEMORY')}
                          onChange={() => toggleTag(group.id, content.id, 'NO_MEMORY')}
                        >
                          不保存记忆
                        </Checkbox>
                      </Tag>
                      <Tooltip title="仅私信渠道有效">
                        <Tag color={content.tag.includes('PUSH') ? 'blue' : 'default'}>
                          <Checkbox
                            checked={content.tag.includes('PUSH')}
                            onChange={() => toggleTag(group.id, content.id, 'PUSH')}
                          >
                            私信PUSH
                          </Checkbox>
                        </Tag>
                      </Tooltip>
                    </Space>
                  </SortableItem>
                )
              })}
            </SortableContext>
          </DndContext>
          <Space style={{ marginTop: 16 }}>
            <Button onClick={() => addContent(group.id, 'TEXT')} icon={<PlusOutlined />}>添加文本消息</Button>
            <Button onClick={() => addContent(group.id, 'IMG')} icon={<PlusOutlined />}>添加图片消息</Button>
          </Space>
        </Card>
      ))}
    </div>
  );
};

export default CardGroup;