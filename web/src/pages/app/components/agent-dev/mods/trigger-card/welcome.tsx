import {
  DeleteOutlined,
  EditOutlined,
  PlusCircleOutlined,
  MessageOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Space, List, Popconfirm, Button, Tooltip } from 'antd';
import React, { useState } from 'react';
import CardCollapse from '../card-collapse';
import TriggerModal from './trigger-modal';
import DiffAlert from '../diff-alert';

const WelcomeTriggerCard = (props) => {
  const { value, oldValue, onChange, appId, settingId, expandLocalKey, disabled } = props;
  const [visible, setVisible] = useState(false);
  const [currentTrigger, setCurrentTrigger] = useState({});

  const onAdd = (newData) => {
    const newValue = [...(value || [])];
    newValue.push(newData);
    onChange(newValue);
  };

  const onEdit = (newData) => {
    const newValue = value?.map((item) => {
      if (item.triggerId === newData.triggerId) return newData;
      return item;
    });
    onChange(newValue);
  };

  const onDelete = (id) => {
    const newValue = value?.filter((item) => item.triggerId !== id);
    onChange(newValue);
  };

  return (
    <>
      <CardCollapse
        expandLocalKey={expandLocalKey}
        moduleKey="welcome"
        collapsible={disabled ? "disabled" : 'icon'}
        items={[
          {
            key: 'welcome',
            label: (
              <Space size={1}>
                <span style={{ fontWeight: 'bold' }}>开场白</span>
                <DiffAlert
                  title="触发器"
                  type="triggers"
                  oldValue={oldValue?.filter((item) => item.actionType === 'greeting')}
                  newValue={value?.filter((item) => item.actionType === 'greeting')}
                  onRedo={() => {
                    onChange(oldValue?.filter((item) => item.actionType === 'greeting'));
                  }}
                />
              </Space>
            ),
            children: (
              <>
                {value?.length ? (
                  <List
                    size="small"
                    dataSource={value?.filter(
                      (item, index) => item.actionType === 'greeting',
                    )}
                    renderItem={(item: any, index) => {
                      return (
                        <List.Item style={{ paddingLeft: 0, paddingRight: 0 }}>
                          <List.Item.Meta
                            avatar={<MessageOutlined />}
                            title={<div style={{ textAlign: 'left' }}>{item.name}</div>}
                          // description={item?.description}
                          />
                          <Space style={{ color: 'rgb(141, 141, 153)' }}>
                            <EditOutlined
                              onClick={() => {
                                setCurrentTrigger(item);
                                setVisible(true);
                              }}
                            />
                            <Popconfirm
                              title={`确认删除 ${item.name} 吗？`}
                              onConfirm={() => onDelete(item.triggerId)}
                            >
                              <DeleteOutlined style={{ cursor: 'pointer' }} />
                            </Popconfirm>
                          </Space>
                        </List.Item>
                      );
                    }}
                  />
                ) : (
                  <span style={{ color: '#999' }}>
                    开场白能够让智能体在对话开始时主动引导话题，例如问候用户、介绍功能、设定目标等，帮助建立良好的互动氛围并明确对话方向。
                  </span>
                )}
              </>
            ),
            extra: !disabled && (
              <Tooltip title='添加开场白'>
                <Button
                  type="text"
                  size="small"
                  icon={<PlusOutlined style={{ fontSize: 10, color: '#666' }} />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentTrigger({});
                    setVisible(true);
                  }}
                />
              </Tooltip>
            ),
          },
        ]}
      />
      <TriggerModal
        visible={visible}
        onClose={() => setVisible(false)}
        appId={appId}
        settingId={settingId}
        data={currentTrigger}
        onAdd={onAdd}
        onEdit={onEdit}
        template={{
          actionType: 'greeting',
        }}
      />
    </>
  );
};

export default WelcomeTriggerCard;
