import {
  DeleteOutlined,
  EditOutlined,
  ClockCircleOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { Space, List, Popconfirm, Button, Tooltip, Flex } from 'antd';
import React, { useState } from 'react';
import CardCollapse from '../card-collapse';
import TriggerModal from './trigger-modal';
import DiffAlert from '../diff-alert';

const TriggerCard = (props) => {
  const { value, oldValue, onChange, appId, settingId, expandLocalKey, disabled } = props;
  const [visible, setVisible] = useState(false);
  const [currentTrigger, setCurrentTrigger] = useState({});

  const onAdd = (newData) => {
    const newValue = [...(value || [])];
    newValue.push(newData);
    onChange(newValue);
  };

  const onEdit = (newData) => {
    const newValue = value?.map((item) => {
      if (item.triggerId === newData.triggerId) return newData;
      return item;
    });
    onChange(newValue);
  };

  const onDelete = (id) => {
    const newValue = value?.filter((item) => item.triggerId !== id);
    onChange(newValue);
  };

  return (
    <>
      <CardCollapse
        defaultActiveKey={value?.length ? ['triggers'] : []}
        expandLocalKey={expandLocalKey}
        moduleKey="triggers"
        collapsible={disabled ? "disabled" : 'icon'}
        items={[
          {
            key: 'triggers',
            label: (
              <Space>
                <span>
                  <span style={{ fontWeight: 'bold' }}>触发器</span>
                  <DiffAlert
                    title="触发器"
                    type="triggers"
                    oldValue={oldValue?.filter((item) => item.actionType !== 'greeting')}
                    newValue={value?.filter((item) => item.actionType !== 'greeting')}
                    onRedo={() => {
                      onChange(oldValue?.filter((item) => item.actionType !== 'greeting'));
                    }}
                  />
                </span>
                <Tooltip title={<Flex vertical gap={8}>
                  <div>触发器是虚拟人非常重要的能力，一般用于用户召回任务，当触发条件满足时，虚拟人会主动执行对应的任务。</div>
                  <a
                    rel="noopener"
                    target='_blank'
                    href="https://music-doc.st.netease.com/st/langbase-doc/update/2025-02-26#-%E6%94%AF%E6%8C%81%E8%A7%A6%E5%8F%91%E5%99%A8"
                  >查看详情</a>
                </Flex>}>
                  <InfoCircleOutlined />
                </Tooltip>
              </Space>
            ),
            children: (
              <>
                {value?.length ? (
                  <List
                    size="small"
                    dataSource={value.filter(
                      (item, index) => item.actionType !== 'greeting',
                    )}
                    renderItem={(item: any, index) => {
                      return (
                        <List.Item style={{ paddingLeft: 0, paddingRight: 0 }}>
                          <List.Item.Meta
                            avatar={<ClockCircleOutlined />}
                            title={<div style={{ textAlign: 'left' }}>{item.name}</div>}
                          // description={item?.description}
                          />
                          <Space style={{ color: 'rgb(141, 141, 153)' }}>
                            <EditOutlined
                              onClick={() => {
                                setCurrentTrigger(item);
                                setVisible(true);
                              }}
                            />
                            <Popconfirm
                              title={`确认删除 ${item.name} 吗？`}
                              onConfirm={() => onDelete(item.triggerId)}
                            >
                              <DeleteOutlined style={{ cursor: 'pointer' }} />
                            </Popconfirm>
                          </Space>
                        </List.Item>
                      );
                    }}
                  />
                ) : (
                  <span style={{ color: '#999' }}>
                    触发器能够让智能体在特定条件或事件发生时自动执行任务，例如回复消息、发送通知、处理数据等，增强智能体的响应能力和自动化水平。
                  </span>
                )}
              </>
            ),
            extra: !disabled && (
              <Tooltip title='添加触发器'>
                <Button
                  type="text"
                  size="small"
                  icon={<PlusOutlined style={{ fontSize: 10, color: '#666' }} />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentTrigger({});
                    setVisible(true);
                  }}
                />
              </Tooltip>
            ),
          },
        ]}
      />
      <TriggerModal
        visible={visible}
        onClose={() => setVisible(false)}
        appId={appId}
        settingId={settingId}
        data={currentTrigger}
        onAdd={onAdd}
        onEdit={onEdit}
      />
    </>
  );
};

export default TriggerCard;
