import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Button, Radio, Table, Space, message, Slider, Steps, DatePicker, Checkbox, Typography, Upload } from 'antd';
import { useRequest } from 'ahooks';
import { ClockCircleOutlined, ThunderboltOutlined, MessageOutlined, FileTextOutlined, UploadOutlined } from '@ant-design/icons';
import { Cron } from 'react-js-cron'
import MessageGroup from './message-group'
import moment from 'moment';
import 'react-js-cron/dist/styles.css'
import UserSourceSelector from './CrowdPackageSelector';
import Condition from './CioModelCondition';
import dayjs from 'dayjs';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppApi } from '@/api/app';

const chineseLocale = {
  everyText: '每',
  emptyMonths: '月',
  emptyMonthDays: '日',
  emptyMonthDaysShort: '日',
  emptyWeekDays: '星期',
  emptyWeekDaysShort: '星期',
  emptyHours: '小时',
  emptyMinutes: '分钟',
  emptyMinutesForHourPeriod: '分钟',
  yearOption: '年',
  monthOption: '月',
  weekOption: '周',
  dayOption: '天',
  hourOption: '小时',
  minuteOption: '分钟',
  rebootOption: '重启时',
  prefixPeriod: '每',
  prefixMonths: 'on',
  prefixMonthDays: 'on',
  prefixWeekDays: 'on',
  prefixWeekDaysForMonthAndYearPeriod: 'and',
  prefixHours: 'at',
  prefixMinutes: ':',
  prefixMinutesForHourPeriod: '第',
  suffixMinutesForHourPeriod: '分',
  errorInvalidCron: '无效的 Cron 表达式',
  weekDays: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
  months: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
  altWeekDays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
  altMonths: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
};

const { Option } = Select;
const { Step } = Steps;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Title } = Typography;

interface TriggerModalProps {
  visible: boolean;
  data: any;
  onClose: () => void;
  onAdd: (values: any) => void;
  onEdit: (values: any) => void;
  triggerId?: string;
  appId?: string;
  settingId?: string;
  template?: any;
}

interface FormData {
  name: string;
  type: string;
  cronExpression?: string;
  triggerPeriod?: [moment.Moment, moment.Moment];
  mode?: string;
  url?: string;
  token?: string;
  params?: Array<{ variableName: string; variableType: string; description: string }>;
  offlineFilter?: object | null;
  onlineFilter?: string[];
  crowdPackage?: string[];
  task: string;
  contentType?: string;
  content?: string;
  prompt?: string;
  contextWindow?: string;
}

const TriggerModal: React.FC<TriggerModalProps> = ({ visible,
  data,
  onClose,
  onAdd,
  onEdit,
  appId,
  template = {},
  settingId }) => {
  const [form] = Form.useForm();

  const defaultOnlineOptions = [
    { optionType: 'recalledInSameDay', exist: false },
    { optionType: 'chattedInSameDay', exist: false },
    { optionType: 'negativeJudge', exist: false },
  ];
  
  const defaultCronValue = '30 5 * * 1,6';
  const title = template?.actionType === 'greeting' ? '开场白' : '触发器'

  const [params, setParams] = useState<FormData['params']>([]);
  const [cronValue, setCronValue] = useState(defaultCronValue);
  const [currentStep, setCurrentStep] = useState(0);
  const { globalState } = useGlobalState();
  const { user } = globalState;

  const type = Form.useWatch('type', form);
  const isEdit = !!data?.name;
  const actionType = Form.useWatch('actionType', form);
  const contentType = Form.useWatch(['config', 'contentType'], form);
  const userSource = Form.useWatch(['config', 'userSourceInfo'], form);
  const [onlineOptions, setOnlineOptions] = useState(defaultOnlineOptions);

  // 关闭弹窗后所有表单数据重置
  const handleResetForm = () => {
    form.resetFields();
    setOnlineOptions(defaultOnlineOptions)
    setCurrentStep(0);
    setCronValue(defaultCronValue);
    setParams([]);
  }

  useEffect(() => {
    if (visible) {
      if (data) {
        const formData = {
          ...data,
          cron: data.cron ? data.cron.slice(2) : defaultCronValue,
          triggerPeriod: data.startTime && data.endTime ? [dayjs(data.startTime), dayjs(data.endTime)] : undefined,
          config: {
            ...data.config,
            userSourceInfo: {
              ...data.config?.userSourceInfo,
              circleInfo: {
                ...data.config?.userSourceInfo?.circleInfo,
                offlineFilter: data.config?.userSourceInfo?.circleInfo?.offlineFilter,
              }
            }
          }
        };
  
        setCronValue(data.cron ? data.cron.slice(2) : defaultCronValue);
  
        if (data.config?.userSourceInfo?.onlineOptions) {
          setOnlineOptions(data.config.userSourceInfo?.onlineOptions);
        }


        form.setFieldsValue(formData);
      }
    } else {
      handleResetForm()
    }
  }, [data, visible]);

  const handleOnlineOptionChange = (optionType: string, checked: boolean) => {
    setOnlineOptions(prevOptions =>
      prevOptions.map(option =>
        option.optionType === optionType
          ? { ...option, exist: checked }
          : option
      )
    );
  };

  const { run: saveTrigger } = useRequest(
    (values: FormData) => {
    
      return AppApi.saveTrigger(values)
    },
    {
      manual: true,
      onSuccess: (res, params) => {
        console.log('保存触发器:', res);
        const values = {...params?.[0], ...res}

        console.log(666, values);
        
        isEdit ? onEdit(values) : onAdd(values);
        message.success('触发器保存成功');
        onClose();
      },
    }
  );  

  const { data: triggerVariables = [] } = useRequest(
    () => {
      if (!visible) {
        return Promise.resolve([])
      }
    
      return AppApi.queryTriggerVariables({
        appId,
        settingId,
      }).then(res => res?.triggerVariables?.map(v => ({ ...v, label: v.title, value: v?.key })))
    },
    {
      refreshDeps: [visible],
    }
  );

  const getStepForField = (fieldName: any): number => {
    
    const step0Fields = ['name', 'type', 'triggerPeriod'];
    const step1Fields = ['actionType', 'config.contentType', 'config.fixedContent', 'config.aiContent', 'config.aiContent.prompt'];
    const step2Fields = ['config.userSourceInfo', 'config.userSourceInfo.circleInfo.offlineFilter'];
  
    if (step0Fields.some(field => fieldName?.join('.') === field)) {
      return 0;
    } else if (step1Fields.some(field => fieldName?.join('.') === field)) {
      return 1;
    } else if (step2Fields.some(field => fieldName?.join('.') === field)) {
      return 2;
    }
  
    return currentStep; // 如果无法确定，保持在当前步骤
  };

  const handleSave = () => {
    form.validateFields().then((values) => {
      const params: any = {
        ...values,
        cron: `0 ${cronValue}`,
        versionType: 'snapshot',
        settingId: settingId,
        appId: appId,
        operator: user?.id,
        startTime: dayjs(values?.triggerPeriod?.[0]).format('YYYY-MM-DD HH:mm:ss'),
        endTime: dayjs(values?.triggerPeriod?.[1]).format('YYYY-MM-DD HH:mm:ss'),
        config: {
          ...values.config,
          userSourceInfo: {
            source: values.config?.userSourceInfo?.source,
          }
        }
      };
      delete params.triggerPeriod;

      // 编辑需传ID
      if (isEdit && data?.triggerId) {
        params.triggerId = data?.triggerId;
      }
  
      // 优化内容来源字段
      if (params.config.contentType === 'CONFIGURED_MSG') {
        delete params.config.aiContent;
      } else if (params.config.contentType === 'AI_MSG') {
        delete params.config.fixedContent;
      }
  
    params.config.userSourceInfo.onlineOptions = onlineOptions;
    // 优化人群包设置字段
    if (params.config.userSourceInfo.source === 'upload') {
      params.config.userSourceInfo.uploadFileInfo = values.config?.userSourceInfo?.uploadFileInfo;
      // 保留 circleInfo，但只包含 offlineFilter 和 onlineOptions
    } else if (params.config.userSourceInfo.source === 'circle') {
      params.config.userSourceInfo.circleInfo = {
        circleId: values.config?.userSourceInfo?.circleInfo?.circleId,
        offlineFilter: values.config?.userSourceInfo?.circleInfo?.offlineFilter 
          && values.config?.userSourceInfo?.circleInfo?.offlineFilter?.children?.length 
            ? values.config?.userSourceInfo?.circleInfo?.offlineFilter
              : null,
      };
      // 不保存 uploadFileInfo
      delete params.config.userSourceInfo.uploadFileInfo;
    }

      saveTrigger(params);
    }).catch((errorInfo) => {
      // 处理验证错误
      const errorFields = errorInfo.errorFields;
      if (errorFields && errorFields.length > 0) {
        const firstError = errorFields[0];

        console.log(firstError.name)
        
        // 如果错误字段不在当前步骤，跳转到相应的步骤
        const errorStep = getStepForField(firstError.name);
        if (errorStep !== currentStep) {
          setCurrentStep(errorStep);
        }
      }
    });
  };

  const handleAddParam = () => {
    setParams([...params, { variableName: '', variableType: '', description: '' }]);
  };

  const handleParamChange = (index: number, key: string, value: any) => {
    const newParams = [...params];
    newParams[index] = { ...newParams[index], [key]: value };
    setParams(newParams);
  };

  const handleDeleteParam = (index: number) => {
    const newParams = params.filter((_, i) => i !== index);
    setParams(newParams);
  };

  const handleNext = () => {
    setCurrentStep(currentStep + 1);
  };

  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  const renderLabel = (label: string) => (
    <Title level={1} style={{ marginBottom: 4, fontSize: '14px' }}>{label}</Title>
  );

  const renderStepContent = () => {
    return (
      <>
        <div style={{ display: currentStep === 0 ? 'block' : 'none' }}>
          <Form.Item name="name" label={renderLabel("名称")} rules={[{ required: true, message: '请输入名称' }]}>
            <Input placeholder="请输入触发器名称" />
          </Form.Item>
          <Form.Item name="type" label={renderLabel("触发器类型")}>
            <Select placeholder="请选择触发器类型">
              <Option value="schedule"><ClockCircleOutlined /> 定时触发</Option>
            </Select>
          </Form.Item>
          {type === 'schedule' && (
            <>
              <Form.Item label={renderLabel("触发时间")}>
                <Cron value={cronValue} setValue={setCronValue} locale={chineseLocale}/>
              </Form.Item>
              <Form.Item name="triggerPeriod" label={renderLabel("触发周期")}>
                <RangePicker
                  showTime={{ format: 'HH:mm:ss' }}
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder={['开始时间', '结束时间']}
                  disabledDate={(current) => {
                    // 禁用今天之前的日期
                    return current && current.isBefore(dayjs().startOf('day') as any);
                  }}
                  // disabledTime={(current) => {
                  //   const range = (start: number, end: number) => Array.from({ length: end - start }, (_, i) => start + i);

                  //   if (current && current.isSame(dayjs(), 'day')) {
                  //     const currentHour = dayjs().hour();
                  //     const currentMinute = dayjs().minute();
                  //     const currentSecond = dayjs().second();

                  //     return {
                  //       disabledHours: () => range(0, currentHour),
                  //       disabledMinutes: (selectedHour) => {
                  //         if (selectedHour === currentHour) {
                  //           return range(0, currentMinute);
                  //         }
                  //         return [];
                  //       },
                  //       disabledSeconds: (selectedHour, selectedMinute) => {
                  //         if (selectedHour === currentHour && selectedMinute === currentMinute) {
                  //           return range(0, currentSecond);
                  //         }
                  //         return [];
                  //       },
                  //     };
                  //   }
                  //   return {};
                  // }}
                />
              </Form.Item>
            </>
          )}
          {type === 'event' && (
            <>
              <Form.Item name="mode" label={renderLabel("模式")}>
                <Select placeholder="请选择模式">
                  <Option value="webhook"><ThunderboltOutlined /> webhook</Option>
                </Select>
              </Form.Item>
              <Form.Item name="url" label={renderLabel("复制url到你的应用")}>
                <Input placeholder="请输入 URL" addonAfter={<Button onClick={() => navigator.clipboard.writeText(form.getFieldValue('url'))}>复制</Button>} />
              </Form.Item>
              <Form.Item name="token" label={renderLabel("token")}>
                <Input.Password placeholder="请输入 token" />
              </Form.Item>
              <Form.Item label={renderLabel("请求参数")}>
                <Table
                  dataSource={params}
                  columns={[
                    {
                      title: '变量名',
                      dataIndex: 'variableName',
                      render: (text, _, index) => (
                        <Input placeholder="请输入变量名" value={text} onChange={(e) => handleParamChange(index, 'variableName', e.target.value)} />
                      ),
                    },
                    {
                      title: '变量类型',
                      dataIndex: 'variableType',
                      render: (text, _, index) => (
                        <Select placeholder="请选择变量类型" value={text} onChange={(value) => handleParamChange(index, 'variableType', value)}>
                          <Option value="string">字符串</Option>
                          <Option value="number">数字</Option>
                        </Select>
                      ),
                    },
                    {
                      title: '描述',
                      dataIndex: 'description',
                      render: (text, _, index) => (
                        <Input placeholder="请输入描述" value={text} onChange={(e) => handleParamChange(index, 'description', e.target.value)} />
                      ),
                    },
                    {
                      title: '操作',
                      render: (_, __, index) => (
                        <Button onClick={() => handleDeleteParam(index)}>删除</Button>
                      ),
                    },
                  ]}
                  rowKey={(_, index) => index.toString()}
                  pagination={false}
                  footer={() => <Button onClick={handleAddParam}>新增参数</Button>}
                />
              </Form.Item>
            </>
          )}
        </div>

      

        <div style={{ display: currentStep === 1 ? 'block' : 'none' }}>
          <Form.Item hidden={template.actionType === 'greeting'} name="actionType" label={renderLabel("执行任务")} initialValue={'recall'}>
            <Select placeholder="请选择执行任务">
              <Option value="recall"><MessageOutlined /> 召回</Option>
              {
                template.actionType === 'greeting' && 
                  <Option value="greeting"><MessageOutlined /> 开场白</Option>
              }
            </Select>
          </Form.Item>
          
          {(actionType === 'recall' || actionType === 'greeting') && (
            <>
              <Form.Item name={['config', 'contentType']} label={renderLabel("内容来源")}>
                <Radio.Group>
                  <Radio.Button value="CONFIGURED_MSG">预设内容</Radio.Button>
                  <Radio.Button value="AI_MSG">AI文本</Radio.Button>
                </Radio.Group>
              </Form.Item>
              {contentType === 'CONFIGURED_MSG' && (
                <Form.Item name={['config', 'fixedContent', 'groups']} label={renderLabel("内容")}>
                  <MessageGroup />
                </Form.Item>
              )}
              {contentType === 'AI_MSG' && (
                <>
                  <Form.Item name={['config', 'aiContent', 'prompt']} label={renderLabel("提示词")}  rules={[{ required: true, message: '请输入提示词' }]}>
                    <TextArea placeholder="请输入提示词" />
                  </Form.Item>
                  <Form.Item initialValue={30} name={['config', 'aiContent', 'maxChatTurn']} label={renderLabel("上下文窗口")}>
                    <Slider
                      style={{ width: 300, marginTop: 20 }}
                      min={0}
                      max={100}
                      tooltip={{ formatter: (value) => `最近${value}轮` }}
                      marks={{
                        0: '0',
                        100: '100'
                      }}
                    />
                  </Form.Item>
                </>
              )}
            </>
          )}
        </div>

        <div style={{ display: currentStep === 2 ? 'block' : 'none' }}>
          <Form.Item name={['config', 'userSourceInfo']} label={renderLabel("人群包设置")}>
            <UserSourceSelector />
          </Form.Item>
          {/* todo: 先不上离线查询，后端未实现 */}
          {
            userSource?.source === 'circle' && actionType !== 'greeting' &&
              <Form.Item name={['config', 'userSourceInfo', 'circleInfo', 'offlineFilter']} label={renderLabel("人群筛选")}>
                <Condition options={triggerVariables} />
              </Form.Item>
          }
          <Form.Item label={renderLabel("精细化筛选")}>
            <Checkbox.Group
              value={onlineOptions.filter(option => option.exist).map(option => option.optionType)}
              onChange={(checkedValues) => {
                onlineOptions.forEach(option => {
                  handleOnlineOptionChange(option.optionType, checkedValues.includes(option.optionType));
                });
              }}
            >
              <Checkbox value="recalledInSameDay">当日有召回则不发送</Checkbox>
              <Checkbox value="chattedInSameDay">当日有聊天则不发送</Checkbox>
              <Checkbox value="negativeJudge">判定为负向情绪则不发送</Checkbox>
            </Checkbox.Group>
          </Form.Item>
        </div>
      </>
    );
  };

  return (
    <Modal
      title={isEdit ? `编辑${title}` : `新增${title}`}
      visible={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>取消</Button>,
        currentStep > 0 && <Button key="prev" onClick={handlePrev}>上一步</Button>,
        currentStep < 2 ? (
          <Button key="next" type="primary" onClick={handleNext}>下一步</Button>
        ) : (
          <Button key="save" type="primary" onClick={handleSave}>保存</Button>
        ),
      ]}
    >
      <Steps current={currentStep} size="small" style={{ marginBottom: 24 }}>
        <Step title="基本信息" />
        <Step title="任务设置" />
        <Step title="触发条件" />
      </Steps>
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          type: 'schedule',
          actionType: 'recall',
          config: {
            contentType: 'CONFIGURED_MSG',
            userSourceInfo: {
              source: 'upload',
              circleInfo: {
                onlineOptions: [],
              },
            },
          },
          ...template
        }}
        style={{ gap: '8px', display: 'flex', flexDirection: 'column' }}
      >
        {renderStepContent()}
      </Form>
    </Modal>
  );
};

export default TriggerModal;