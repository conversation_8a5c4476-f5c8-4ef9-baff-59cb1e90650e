import { CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Card, message, Modal, Table, Tooltip, Typography } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useEffect, useMemo } from 'react';
import { styled } from 'styled-components';

import { TokenApi } from '@/api/token';
import { TooltipTime } from '@/components/TooltipTime';
import { Token } from '@/interface';
import { AgentConfig, ParamTypeEnum } from '@/interface/agent';
import { copyToClipboard } from '@/utils/common';
import React from 'react';

const { confirm } = Modal;

interface OperationCardProps {
  appID: string;
  style?: any;
  agentConfig?: AgentConfig;
  setTokens: any;
}

const TokenCardBase = styled(Card)`

  .ant-card-actions {
    padding: 0px;

    li {
      margin-top: 0px;
    }
  }

  .ant-card-head {
    min-height: 40px;
  }

  .ant-card-body {
    padding: 0px;
  }
`;

export const TokenOperationCard = (props: OperationCardProps) => {
  const { appID, style, setTokens } = props;
  const [messageApi, contextHolder] = message.useMessage();

  const { data, refresh: refershTokens } = useRequest(() => TokenApi.list('app', appID), {
    refreshDeps: [appID],
  });

  useEffect(() => {
    setTokens(data);
  }, [data])

  const { run: createToken } = useRequest(() => TokenApi.create('app', appID), {
    manual: true,
    onSuccess: () => {
      messageApi.open({
        type: 'success',
        content: '创建成功',
      });
      refershTokens();
    },
  });

  const { run: deleteToken } = useRequest(TokenApi.remove, {
    manual: true,
    onSuccess: () => {
      messageApi.open({
        type: 'success',
        content: '删除成功',
      });
      refershTokens();
    },
  });

  const deleteTokenConfirm = (appID: string) => {
    confirm({
      title: '确认删除',
      content:
        '删除后，当前正在使用该Token的应用将无法正常使用，并且删除无法恢复，确认删除？',
      onOk() {
        deleteToken(appID);
      },
    });
  };

  const copyToken = (token: string) => {
    copyToClipboard(token);
    messageApi.open({
      type: 'success',
      content: '复制成功',
    });
  };

  const columns: ColumnsType<Token> = [
    {
      title: 'Token',
      dataIndex: 'token',
      width: '40%',
      ellipsis: true,
      render(value) {
        return (
          <Tooltip title={value}>
            <p style={{ whiteSpace: 'nowrap', textOverflow: 'ellipsis', overflow: 'hidden', margin: '0px' }}>
              {value}
            </p>
          </Tooltip>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: '20%',
      render: (createdAt) => {
        return <TooltipTime time={createdAt} />;
      },
    },
    {
      title: '最后使用',
      dataIndex: 'lastUsedAt',
      width: '20%',
      render: (lastUsedAt) => {
        if (!lastUsedAt) {
          return '从未';
        }
        return <TooltipTime time={lastUsedAt} />;
      },
    },
    {
      title: '',
      dataIndex: '',
      key: 'action',
      render: (_, record) => (
        <div style={{ marginRight: '40px', display: 'flex' }}>
          <Button type="text" size="small" onClick={() => copyToken(record.token)}>
            <CopyOutlined />
          </Button>
          <Button type="text" size="small" onClick={() => deleteTokenConfirm(record.id)}>
            <DeleteOutlined />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <TokenCardBase
      style={style}
      title="API密钥"
      extra={[
        <a onClick={createToken} type="primary">
          新增
        </a>
      ]}
    >
      {(data?.total ?? 0) > 0 && (
        <Table
          size="small"
          columns={columns}
          dataSource={data!.items}
          pagination={
            data!.total > 5
              ? {
                simple: true,
                total: data!.total,
                pageSize: 5,
              }
              : false
          }
        />
      )}
    </TokenCardBase>
  );
};