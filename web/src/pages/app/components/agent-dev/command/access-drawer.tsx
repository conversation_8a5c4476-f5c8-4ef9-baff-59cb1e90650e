// @ts-ignore
import { ApiOutlined, BookOutlined, RobotOutlined } from '@ant-design/icons';
import { Card, Tabs } from 'antd';
import TabPane from 'antd/es/tabs/TabPane';
import styled from 'styled-components';

import { useGlobalState } from '@/hooks/useGlobalState';
import { AppDetailModel, IAppType } from '@/interface';

import Workflow from '../../../workflow/index';
import { TokenOperationCard } from './embed';
import { AppUsage } from '../mods/usage';
import BasicDrawer from '@/components/basic-drawer';
import { POPOIcon } from '@/components/icons';
import { PopoCard } from '../mods/popo-card';
import { TokenCurlCommandCard } from './command';
import { WebCommandCard } from './web-command';
import { useSafeState } from 'ahooks';



const TabTitle = styled.div`
  margin-left: 4px;
  margin-right: 4px;
  svg {
    margin-right: 5px;
  }
`;

interface IAgentDevProps {
  app: AppDetailModel;
  render?: React.ReactNode;
  agentConfig?: any;
}

export const AccessContent = (props: IAgentDevProps) => {
  const { app, agentConfig } = props;
  const [tokens, setTokens] = useSafeState({})
  console.log("app agent...", app, agentConfig);
  return <>
    <TokenOperationCard
      style={{ marginBottom: 20 }}
      appID={app.id}
      setTokens={setTokens}
    />
    <Tabs type="card">
      <TabPane
        tab={
          <TabTitle>
            <ApiOutlined />
            API接入
          </TabTitle>
        }
        key="embed"
      >
        {/* <div style={{ marginBottom: '10px', fontSize: '12px', color: '#777777' }}>
          Token对接请参考<a href="/docs#/service">Service API</a>文档
        </div> */}
        <TokenCurlCommandCard providerKind={agentConfig.providerKind} token={tokens} appID={app.id} appType={app.type} agentConfig={{
          ...app.config,
          ...agentConfig
        }}></TokenCurlCommandCard>
      </TabPane>
      {app.type !== IAppType.AgentCompletion &&
        <>
          <TabPane
            tab={
              <TabTitle>
                <RobotOutlined />
                WebBot 接入
              </TabTitle>
            }
            key="web"
          >
            <WebCommandCard token={tokens} appID={app.id} appType={app.type}></WebCommandCard>
          </TabPane>
          <TabPane
            tab={
              <TabTitle>
                <POPOIcon />
                POPO 接入
              </TabTitle>
            }
            key="popo"
          >
            <PopoCard token={tokens} appID={app.id} appType={app.type}></PopoCard>
          </TabPane>
        </>
      }
    </Tabs>
  </>
}

export default function Access(props: IAgentDevProps) {
  const { render } = props;

  return (
    <BasicDrawer render={render} title="接入指南">
      <AccessContent {...props}></AccessContent>
    </BasicDrawer>
  );
}