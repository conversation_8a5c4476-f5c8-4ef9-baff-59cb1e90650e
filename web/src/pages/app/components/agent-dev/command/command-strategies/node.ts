import { CodeStrategy } from './types';

export const nodeStrategy: CodeStrategy = {
  getCode: (uri, token, data) => `
const fetch = require('node-fetch');  // 如果是在 Node.js 环境下

const url = '${uri}';

fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': '${token}'
  },
  body: JSON.stringify(${JSON.stringify(data, null, 2)})
}).then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));`
}; 

export const openaiNodeStrategy: CodeStrategy = {
  getCode: (uri, token, data) => `// 请先安装 OpenAI SDK: npm install openai

import OpenAI from "openai";

const baseURL = '${uri}';
const apiKey = '${token}';

const openai = new OpenAI({
        baseURL: baseURL,
        apiKey: apiKey
});

async function main() {
  const completion = await openai.chat.completions.create({
    model: "${data.model}", // 可以替换模型，不过必须是langbase平台的模型，如果没找到，默认会用应用自带模型
    messages: ${JSON.stringify(data.messages)},
    stream: false // 是否流式输出，如果为true，则返回一个生成器，否则返回一个字符串
  });

  console.log(completion.choices[0].message.content);
}

main();`
};  