import { CodeStrategy } from './types';

// 在当前文件中实现 escapeQuotes 函数
const escapeQuotes = (json: string): string => {
  return json.replace(/"/g, '\\"');
};

export const javaStrategy: CodeStrategy = {
  getCode: (uri, token, data) => `import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

public class Main {
    public static void main(String[] args) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost("${uri}");

        String jsonInputString = "${escapeQuotes(JSON.stringify(data))}";
        StringEntity entity = new StringEntity(jsonInputString, "UTF-8");
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Authorization", "${token}");

        CloseableHttpResponse response = httpClient.execute(httpPost);
        try {
            HttpEntity responseEntity = response.getEntity();
            if (responseEntity != null) {
                String result = EntityUtils.toString(responseEntity, "UTF-8");
                System.out.println(result);
            }
        } finally {
            response.close();
        }
    }
}`
}; 