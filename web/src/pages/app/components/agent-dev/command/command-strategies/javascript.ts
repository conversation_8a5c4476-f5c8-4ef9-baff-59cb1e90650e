import { CodeStrategy } from './types';

const replace = {
  'music.163.com': 'langbase.netease.com',
}

export const javascriptStrategy: CodeStrategy = {
  getCode: (uri, token, data) => {
    const url = uri.replace(new RegExp(Object.keys(replace).join('|'), 'g'), (match) => replace[match]);
    return `
const url = '${url}';
fetch(url, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': '${token}'
  },
  body: JSON.stringify(${JSON.stringify(data, null, 2)})
}).then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));`
  }
}; 
