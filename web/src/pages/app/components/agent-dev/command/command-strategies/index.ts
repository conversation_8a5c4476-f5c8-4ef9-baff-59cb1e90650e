import { bashStrategy, openaiBashStrategy } from './bash';
import { pythonStrategy, openaiPythonStrategy } from './python';
import { javaStrategy } from './java';
import { javascriptStrategy } from './javascript';
import { nodeStrategy, openaiNodeStrategy } from './node';
import { CodeStrategy } from './types';

export const strategies: Record<string, { title: string, type: string, strategy: CodeStrategy }> = {
  bash: {
    title: 'curl',
    type: 'bash',
    strategy: bashStrategy,
  },
  python: {
    title: 'python',
    type: 'python',
    strategy: pythonStrategy,
  },
  java: {
    title: 'java',
    type: 'java',
    strategy: javaStrategy,
  },
  javascript: {
    title: 'web',
    type: 'javascript',
    strategy: javascriptStrategy,
  },
  node: {
    title: 'node.js',
    type: 'javascript',
    strategy: nodeStrategy,
  }
};

export const openaiStrategies: Record<string, { title: string, type: string, strategy: CodeStrategy }> = {
  bash: {
    title: 'curl',
    type: 'bash',
    strategy: openaiBashStrategy,
  },
  python: {
    title: 'python',
    type: 'python',
    strategy: openaiPythonStrategy,
  },
  node: {
    title: 'node.js',
    type: 'javascript',
    strategy: openaiNodeStrategy,
  }
};

export type StrategyType = keyof typeof strategies; 