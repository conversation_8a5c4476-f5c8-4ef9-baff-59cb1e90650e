import { CodeStrategy } from './types';

export const pythonStrategy: CodeStrategy = {
  getCode: (uri, token, data) => `import requests

url = "${uri}"
payload = ${JSON.stringify(data, null, '    ')}
headers = {
    'Authorization': '${token}',
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}

response = requests.post(url, json=payload, headers=headers)`
}; 

export const openaiPythonStrategy: CodeStrategy = {
  getCode: (uri, token, data) => `# 请先安装 OpenAI SDK : pip3 install openai
from openai import OpenAI

base_url = "${uri}"
api_key = "${token}"
client = OpenAI(api_key=api_key, base_url=base_url)

response = client.chat.completions.create(
    model="${data.model}", # 可以替换模型，不过必须是langbase平台的模型，如果没找到，默认会用应用自带模型
    messages=${JSON.stringify(data.messages)},
    stream=False  # 是否流式输出，如果为True，则返回一个生成器，否则返回一个字符串
)

print(response.choices[0].message.content)`
}; 