import { CodeStrategy } from './types';

export const bashStrategy: CodeStrategy = {
  getCode: (uri, token, data) => `curl --location --request POST \\
'${uri}' \\
--header 'Authorization: ${token}' \\
--header 'Content-Type: application/json' \\
--header 'Accept: application/json' \\
--data-raw '${JSON.stringify(data, null, '  ')}'`
}; 

export const openaiBashStrategy: CodeStrategy = {
  getCode: (uri, token, data) => `curl --location --request POST \\
'${uri}/chat/completions' \\
--header 'Authorization: Bearer ${token}' \\
--header 'Content-Type: application/json' \\
--data-raw '${JSON.stringify(data, null, '  ')}'`
};