import { CheckCircleTwoTone, CloseCircleTwoTone, CopyOutlined } from '@ant-design/icons';
import { Button, message, Modal, Radio, Space } from 'antd';
import { useEffect, useMemo } from 'react';

import { IAppType } from '@/interface';
import { AgentConfig, ParamTypeEnum } from '@/interface/agent';
import { copyToClipboard, env } from '@/utils/common';
import { useGetState, useSafeState } from 'ahooks';
import { Light as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomOneDark } from 'react-syntax-highlighter/dist/esm/styles/hljs';
// Using ES6 import syntax
import javascript from 'react-syntax-highlighter/dist/esm/languages/hljs/javascript';
import python from 'react-syntax-highlighter/dist/esm/languages/hljs/python';
import bash from 'react-syntax-highlighter/dist/esm/languages/hljs/bash';
import java from 'react-syntax-highlighter/dist/esm/languages/hljs/java';
import json from 'react-syntax-highlighter/dist/esm/languages/hljs/json';
import { openaiStrategies, strategies, StrategyType } from './command-strategies';
import PopoChat from '@music/ct-pc-paopaocaller';
import { ModelProviderApi } from '@/api/model-provider';
import { getGroupId } from '@/utils/state';
import { useGlobalState } from '@/hooks/useGlobalState';
import { isModelSupportAudio, isModelSupportImage, isModelSupportVideo } from '@/utils/model-helper';

// Then register the languages you need
SyntaxHighlighter.registerLanguage('java', java);
SyntaxHighlighter.registerLanguage('javascript', javascript);
SyntaxHighlighter.registerLanguage('python', python);
SyntaxHighlighter.registerLanguage('bash', bash);
SyntaxHighlighter.registerLanguage('json', json);

interface CurlCommandCardProps {
  appID: string;
  appType: IAppType;
  providerKind: string;
  agentConfig: AgentConfig;
  token: any;
}

const getCheckIcon = (check: boolean) => check ? <CheckCircleTwoTone twoToneColor="#52c41a" style={{ marginRight: 4 }} /> : <CloseCircleTwoTone twoToneColor="#eb2f96" style={{ marginRight: 4 }} />;

// 定义提示信息配置
const ENDPOINT_TIPS = {
  b: () => (
    <p style={{
      borderBottom: '1px solid #35333321',
      paddingBottom: 10,
      margin: 0,
    }}>
      1. 如果在机房网访问出现403，请将域名<span style={{ color: 'red', fontWeight: 'bold' }}>https://langbase.netease.com</span> 换成机房网域名 <span style={{ color: 'red', fontWeight: 'bold' }}>http://langbase.yf-online1.service.163.org</span>
    </p>
  ),
  openai: (appID: string) => (
    <p style={{
      borderBottom: '1px solid #35333321',
      paddingBottom: 10,
      margin: 0,
    }}>
      1. 只支持机房网访问，请合理合规使用。
      <br></br>
      2. apiKey直接使用上面生成密钥，baseURL: <span style={{ color: 'red', fontWeight: 'bold' }}>https://langbase.netease.com/api/v1/app/{appID}/openai</span>
      <br></br>
      3. 仅供业务场景使用，<span style={{ color: 'red', fontWeight: 'bold' }}>请勿用于个人工具（特别是高频且大上下文调用场景，例如Claude/Windsurf/Agent等）。</span>
    </p>
  ),
  c: () => (
    <p style={{
      borderBottom: '1px solid #35333321',
      paddingBottom: 10,
      lineHeight: 1.8,
      margin: 0,
    }}>
      1. 如果在机房网访问出现403，请将域名<span style={{ color: 'red', fontWeight: 'bold' }}>https://music.163.com</span> 换成机房网域名 <span style={{ color: 'red', fontWeight: 'bold' }}>http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes</span>
      <br></br>
      2. 如果是 Web 端调用，请将域名<span style={{ color: 'red', fontWeight: 'bold' }}>music.163.com</span> 换成 <span style={{ color: 'red', fontWeight: 'bold' }}>langbase.netease.com</span>，避免跨域问题（该域名是办公网域名，如需 ToC 使用，请服务端代理该接口，不允许对外直接暴露大模型相关的接口)。
      <br></br>
      3. 如果C端流量比较大（接口日调用量大于1w），请在<PopoChat ssid="5274049" chatId="5274049" type={1} name="LangBase群" contentStyle={{ display: 'inline-block', verticalAlign: 'middle', marginLeft: 4 }} textStyle={{ fontSize: 14 }} /> <span style={{ color: '#1677ff' }}>@刘佳林</span> 说明<span style={{ color: 'red', fontWeight: 'bold' }}>业务场景和预估调用量，进行单独保障</span>。<br />
      <br></br>
      {/* 审批通过后，即可在这边查看到C端接口 */}
    </p>
  )
};

// 定义端点URL配置
const getEndpointUrl = (endpoint: 'b' | 'c' | 'openai', appID: string) => {
  if (endpoint === 'openai') {
    return `${window.location.protocol}//${window.location.host}/api/v1/app/${appID}/openai`;
  }
  if (endpoint === 'b') {
    // 使用当前域名和端口
    return `${window.location.protocol}//${window.location.host}`;
  }

  // C端根据环境判断
  return env === 'online' || env === 'pre'
    ? 'https://music.163.com/api/langbase/agent'
    : 'http://qa.igame.163.com/api/langbase/agent';
};

export const TokenCurlCommandCard = (props: CurlCommandCardProps) => {
  const { appID, appType, agentConfig: config, token, providerKind } = props;
  const { globalState } = useGlobalState();
  const { group } = globalState;
  const [type, setType] = useSafeState<StrategyType>('bash');
  const [endpoint, setEndpoint] = useSafeState<'b' | 'c' | 'openai'>('c');
  const [bindCheck, setBindCheck] = useSafeState(false);
  const [verifyCheck, setVerifyCheck] = useSafeState(true);
  const supportImage = isModelSupportImage(config.model);
  const supportAudio = isModelSupportAudio(config.model);
  const supportVideo = isModelSupportVideo(config.model);

  // useEffect，从listGroupModelProviderBinding 获取绑定信息
  useEffect(() => {
    if (group?.id) {
      ModelProviderApi.listGroupModelProviderBinding(group.id).then((res) => {
        setBindCheck(res.some(it => it.providerKind === providerKind));
      });
    }
  }, [group?.id, providerKind]);

  const url = useMemo(() => {
    const urlMap = {
      [IAppType.AgentCompletion]: '/api/v1/completion',
      [IAppType.AgentConversation]: '/api/v1/chat',
      [IAppType.AgentWorkflow]: endpoint === 'c' ? '/workflow' : '/api/v1/agentw-chat'
    };
    return urlMap[appType] || '';
  }, [appType, endpoint]);

  const data: any = useMemo(() => {
    const params: Record<string, any> = {};
    config.paramsInPrompt?.forEach((it) => {
      if (it.type === ParamTypeEnum.select && (it.config.options ?? []).length === 0) {
        return;
      }
      params[it.key] = it.type === ParamTypeEnum.select
        ? (it.config.options ?? [])[0].value
        : it.title;
    });

    if (appType === IAppType.AgentWorkflow) {
      return {
        inputs: { ...params, message: '开始对话' }
      };
    }

    return {
      parameters: params,
      ...(appType === IAppType.AgentConversation ? { message: '开始对话' } : {})
    };
  }, [config, appType]);

  const content = useMemo(() => {
    if (!url) return '';

    const newData = { ...data };
    const token2 = token?.items?.length ? token?.items[0].token : 'token';
    const baseUrl = getEndpointUrl(endpoint, appID);
    if (endpoint === 'openai') {
      return openaiStrategies[type].strategy.getCode(baseUrl, token2, {
        'model': config?.modelsConfig?.models?.[0]?.modelName ?? config?.model?.name,
        'messages': [
          {
            'role': 'user',
            'content': '你好'
          }
        ]
      });
    }
    const bearerToken = `Bearer ${token2}`;
    // 如果c端，要把url的api/v1去掉
    const uri = endpoint === 'c' ? `${baseUrl}${url.replace('/api/v1', '')}` : `${baseUrl}${url}?user=null&appID=${appID}`;
    if (endpoint === 'c') {
      newData.appId = appID;
      newData.userId = '真实用户id字符串，用来日志查询和数据统计';
    }
    if (appType === IAppType.AgentConversation && endpoint === 'c') {
      newData.conversationId = '会话id，用来保持上下文，返回值值中会包含，首次调用时传空';
    }
    if (supportImage) {
      newData.imageUrl = '视觉识别的图片地址(多个用英文逗号分隔)，填空则不使用视觉能力';
    }
    if (supportAudio) {
      newData.audioUrl = '音频识别的音频地址(多个用英文逗号分隔)，填空则不使用音频能力';
    }
    if (supportVideo) {
      newData.videoUrl = '视频识别的音频地址(多个用英文逗号分隔)，填空则不使用视频能力';
    }

    return strategies[type]?.strategy.getCode(uri, bearerToken, newData) || '';
  }, [type, token, url, appID, data, endpoint, supportImage]);

  if (!token?.items?.length) {
    return <div>请先点击【新增】，生成API密钥</div>;
  }

  return (
    <div style={{ position: 'relative' }}>
      <Space direction="vertical" size="small">
        <Radio.Group
          defaultValue="c"
          size="small"
          buttonStyle="solid"
          onChange={e => setEndpoint(e.target.value)}
        >
          <Radio.Button value="c">新版</Radio.Button>
          {appType === IAppType.AgentCompletion && (
            <Radio.Button value="openai">OpenAI格式</Radio.Button>
          )}
          <Radio.Button value="b">旧版(废弃)</Radio.Button>
        </Radio.Group>

        <Radio.Group
          defaultValue="bash"
          size="small"
          buttonStyle="solid"
          onChange={e => setType(e.target.value)}
        >
          {endpoint === 'openai' ? Object.keys(openaiStrategies).map(key => (
            <Radio.Button key={key} value={key}>
              {openaiStrategies[key].title}
            </Radio.Button>
          )) : Object.keys(strategies).map(key => (
            <Radio.Button key={key} value={key}>
              {strategies[key].title}
            </Radio.Button>
          ))}
        </Radio.Group>
      </Space>
      <header>
        <p style={{ fontWeight: 'bold', marginTop: 10 }}>注意</p>
        {ENDPOINT_TIPS[endpoint](appID)}

      </header>
      <div style={{ marginTop: 10, position: 'relative' }}>
        <Button
          type="text"
          size="small"
          onClick={() => {
            copyToClipboard(content);
            message.info({ content: '复制成功' });
          }}
          style={{
            position: 'absolute',
            right: 5,
            top: 40
          }}
        >
          <CopyOutlined style={{ color: '#fff' }} ></CopyOutlined>
        </Button>
        <SyntaxHighlighter
          language={endpoint === 'openai' ? openaiStrategies[type].type : strategies[type].type}
          style={atomOneDark}
          customStyle={{
            padding: 20,
            borderRadius: 10,
          }}
        >
          {content}
        </SyntaxHighlighter>
        <p style={{ margin: 0 }}>返回值结构如下 {appType === IAppType.AgentConversation ? <span>，其中<span style={{ color: 'red', fontWeight: 'bold' }}>{endpoint === 'c' ? 'conversationId' : 'conversationID'}</span>在后续对话中需要传入{endpoint === 'b' ? '（放在query中: &conversationID=xxxx)' : ''}，用来保持上下文</span> : ''}</p>
        <p style={{ margin: 0 }}>{endpoint === 'c' ? <span><span style={{ color: 'red', fontWeight: 'bold' }}>注意</span>：新版接口返回值结构和旧版不一样，请注意区分（旧版不推荐再继续使用，无法应对大流量场景，只适合内部业务少量调用，请尽快切换到新版）</span> : ''}</p>
        <SyntaxHighlighter
          language={'json'}
          style={atomOneDark}
          customStyle={{
            padding: 20,
            borderRadius: 10,
          }}
        >
          {JSON.stringify(getReturnData(appType, endpoint), null, 2)}
        </SyntaxHighlighter>
      </div>
    </div>
  );
};

const getReturnData = (type: IAppType, endpoint: 'c' | 'b' | 'openai') => {
  if (endpoint === 'openai') {
    return { "id": "chatcmpl-Blt0DdefDp5BaSRSereHUZGIRFElp", "object": "chat.completion", "created": 1750752545, "model": "", "choices": [{ "index": 0, "message": { "role": "assistant", "content": "你好！有什么我可以帮助你的？", "name": null }, "finish_reason": "stop" }], "usage": { "prompt_tokens": 8, "completion_tokens": 9, "total_tokens": 17 } }
  }
  let data: any = {
    "code": 200,
    "data": {
      "messageList": [
        {
          "type": null,
          "content": "回复内容",
          "toolCalls": null
        }
      ],
      "totalTokens": 42
    },
    "message": ""
  }
  if (type === IAppType.AgentConversation) {
    data.data.messageList[0].conversationId = "2e1d55a1-42a2-4256-b85d-d5af0ca19933";
    data.data.messageList[0].messageId = "e7b70e60-6afc-4a06-803b-c4dcaa16bcb8";
  }
  if (endpoint === 'b') {
    data = [{
      "type": null,
      "content": "回复内容",
      "toolCalls": null,
      "total_tokens": 42
    }];
    if (type === IAppType.AgentConversation) {
      data[0].conversationID = "2e1d55a1-42a2-4256-b85d-d5af0ca19933";
      data[0].messageID = "e7b70e60-6afc-4a06-803b-c4dcaa16bcb8";
    }
  }
  if (type === IAppType.AgentWorkflow) {
    if (endpoint === 'c') {
      data.data = {
        "chatId": "4aeb791a-47ac-4e08-9fe6-881c1f84b9da",
        "result": { "output": "你好，我是Langbase小助手" }
      }
    } else {
      data = {
        "node_id": "o1a0e9544",
        "inputs": {},
        "outputs": { "output": "你好，我是Langbase小助手" },
        "response": "你好，我是Langbase小助手",
        "running_status": "success",
        "node_name": "结束",
        "chat_id": "0e434490-c0a1-40d7-8638-b126a9df231d"
      }
    }
  }

  return data;
};
