import { CopyOutlined } from '@ant-design/icons';
import { Button, message } from 'antd';

import { IAppType } from '@/interface';
import { copyToClipboard, env } from '@/utils/common';
import hljs from 'highlight.js/lib/core';
import javascript from 'highlight.js/lib/languages/javascript';

hljs.registerLanguage('javascript', javascript);

interface CurlCommandCardProps {
  appID: string;
  appType: IAppType;
  token: any;
}


export const WebCommandCard = (props: CurlCommandCardProps) => {
  const { appID, appType, token } = props;

  if (window.hljs) {
    window.hljs.highlightAll();
  }

  console.log('token', token);

  if (!token?.items?.length) {
    return <div>请先点击【新增】，生成API密钥</div>
  }
  const type = appType === IAppType.AgentConversation ? 'agent-conversation' : 'chat-flow';

  const content = `
  import { useEffect, useRef } from "react";
  import LangBot from "@music/lang";
  
  const Demo = () => {
    const ref = useRef();
    useEffect(() => {
      // 配置
      ref.current = new LangBot({   
        appId: '${appID}',
        token: '${token?.items?.length ? token?.items[0].token : 'token'}',
        type: '${type}',
        env: '${env}',
        userId: '' // 可选，可以填平台用户的id，用来查询日志
      });
      ref.current.run();    // 启动
    }, []);

    return <div></div>;
  };
  `

  const copyToken = (val) => {
    copyToClipboard(val || content);
    message.info({
      content: '复制成功',
    })
  };

  return (
    <div style={{ position: 'relative' }} >
      <h4>使用方法</h4>
      <div style={{ position: 'relative' }} >
        <p style={{
          borderBottom: '1px solid #35333321',
          paddingBottom: 10,
          margin: 0,
        }}>安装依赖</p>
        <pre style={{ overflowX: 'scroll' }}>
          npm install @music/lang
        </pre>
        <Button type="text" size="small" onClick={() => copyToken('npm install @music/lang')} style={{ position: 'absolute', right: 0, top: 0 }}>
          <CopyOutlined />
        </Button>
      </div>
      <div style={{ position: 'relative' }} >
        <Button type="text" size="small" onClick={() => copyToken('')} style={{ position: 'absolute', right: 0, top: 0 }}>
          <CopyOutlined />
        </Button>
        <p style={{
          borderBottom: '1px solid #35333321',
          paddingBottom: 10,
          margin: 0,
        }}>使用demo</p>
        <pre style={{ overflowX: 'scroll' }}
        >
          <code className="language-javascript hljs" dangerouslySetInnerHTML={{
            __html: hljs.highlight(
              content,
              { language: 'javascript' }
            ).value
          }}>
          </code>
        </pre>
      </div>
    </div >
  );
};

