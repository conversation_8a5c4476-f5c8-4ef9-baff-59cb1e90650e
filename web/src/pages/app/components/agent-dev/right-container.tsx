import { AgentParamsRender } from '@/components/agent-params-render/complete';
import { Flex, message } from 'antd';
import styled from 'styled-components';
import { IAppType } from '@/interface';
import React, { useEffect, useRef, useState } from 'react';
import { AppApi } from '@/api/app';
import { useRequest } from 'ahooks';
import { CheckCircleTwoTone } from '@ant-design/icons';
import { getMaxRatioModel } from '../chat-bot/utils';
import { pick } from 'lodash';
import ConversationBot from '../chat-bot/conversation-bot';
import ConversationDebugBot from '@/components/lang-bot-component/conversation-bot/conversation-debug-bot';
import { ICompletionConfig, IConversationConfig } from '@/interface/config';
import CompletionResult from '../chat-bot/completion-result';
import CompletionDebugBots from '@/components/lang-bot-component/completion-debug-bots';
import OnlineNewPrompt from './online-newprompt';
import { ModelDebug } from '@/pages/app/components/chat-bot/model-debug';

interface RightContentProps {
  app: any;
  paramsInPrompt: any[];
  conversationKey: string;
  welcomeText: string;
  modelConfig: any;
  prePrompt: string;
  setPrePrompt: (prePrompt: string) => void;
  tools: any;
  newPrompt: any;
  debugConfig: Record<string, any>;
  mode?: string;
  remoteConfig?: any;
  onModelsChange?: (models: any) => void;
  oldModelsConfig: any;
}

function RightContainer({
                          app,
                          paramsInPrompt,
                          welcomeText,
                          modelConfig,
                          setPrePrompt,
                          conversationKey,
                          newPrompt,
                          debugConfig: parentDebugConfig,
                          mode,
                          remoteConfig,
                          onModelsChange,
                          oldModelsConfig,
                        }: RightContentProps) {
  // 调试相关
  // useEffect(() => {
  //   setChatViewResetVisible(true);
  // }, [conversationKey, newPrompt]);
  // 配置相关
  const [inputParams, setInputParams] = useState<any>({});
  // const [chatViewResetVisible, setChatViewResetVisible] = useState(false);
  const { run: updateExample } = useRequest((params: {
    description?: string,
    usingExample?: string
  }) => AppApi.updateApp({
    description: params.description,
    querySample: params.usingExample,
  }, app.id), {
    manual: true,
    onSuccess: () => {
      message.success('保存成功');
    },
  });
  const compeleteResultRef = useRef<HTMLDivElement>();

  const completionResult = useRef();
  const completionResults = useRef();

  const [debugModel, setDebugModel] = useState<any>({});

  const debugConfig: Record<string, any> = {
    ...(parentDebugConfig || {}),
    ...(pick(debugModel, ['modelName', 'modelParams', 'providerKind'])),
    modelsConfig: ['advanced', 'master_backup'].includes(parentDebugConfig?.modelsConfig?.type) ? parentDebugConfig?.modelsConfig : {
      ...parentDebugConfig?.modelsConfig,
      models: [parentDebugConfig?.modelsConfig?.models?.[0]],
    },
  };

  useEffect(() => {
    // 获取概率最大的模型调试
    const debugModel = getMaxRatioModel(debugConfig?.modelsConfig?.models);

    setDebugModel(debugModel);
  }, [debugConfig?.modelsConfig]);

  function renderCompleteResult() {
    const completionConfig: ICompletionConfig = {
      ...(debugConfig || {}),
      modelsConfig: debugConfig?.modelsConfig,
    };

    if (mode === 'promptDebug') {
      return <CompletionDebugBots
        ref={completionResults}
        modelsConfig={debugConfig?.modelsConfig}
        onModelsChange={onModelsChange}
        app={app}
        newPrompt={newPrompt}
        scrollContainer={compeleteResultRef.current}
        showModel={true}
        configs={[
          {
            title: <div>当前提示词 <CheckCircleTwoTone twoToneColor="#52c41a" /></div>,
            config: completionConfig,
          },
          {
            title: <Flex gap={6}>
              线上提示词
              <OnlineNewPrompt
                value={completionConfig?.prePrompt}
                oldValue={remoteConfig?.prePrompt}
                appType={app.type} />
            </Flex>,
            config: {
              ...completionConfig,
              prePrompt: remoteConfig?.prePrompt,
            },
          },
        ]}
      />;
    }
    return <CompletionResult
      ref={completionResult}
      app={app}
      newPrompt={newPrompt}
      debugConfig={completionConfig}
      scrollContainer={compeleteResultRef.current}
      setPrePrompt={setPrePrompt}
    />;
  }

  function renderConversation(props: { key: string }) {
    const { newPrompt } = app.config;

    const conversationConfig: IConversationConfig = {
      ...(debugConfig || {}),
      modelsConfig: debugConfig?.modelsConfig,
    };

    if (mode === 'promptDebug') {
      return <ConversationDebugBot
        modelsConfig={debugConfig?.modelsConfig}
        onModelsChange={onModelsChange}
        appType={app.type}
        renderDep={JSON.stringify(newPrompt)}
        appId={app.id}
        configs={[
          {
            title: <div>当前提示词 <CheckCircleTwoTone twoToneColor="#52c41a" /></div>,
            config: conversationConfig,
          },
          {
            title: <Flex gap={6}>
              线上提示词
              <OnlineNewPrompt
                value={conversationConfig?.prePrompt}
                oldValue={remoteConfig?.prePrompt}
                appType={app.type} />
            </Flex>,
            config: {
              // 不要占用外面的conversationId
              ...conversationConfig,
              prePrompt: remoteConfig?.prePrompt,
            } as IConversationConfig,
          },
        ]}
      />;
    }

    return <ConversationBot
      appId={app.id}
      userId="langbase-debug"
      debug
      host={window.location.host}
      greetings={welcomeText}
      config={{
        ...debugConfig,
        type: app.type,
      }}
      onModelsChange={onModelsChange}
      oldModelsConfig={oldModelsConfig}
    />;
  }

  if (mode === 'modelDebug') {
    return (
      <Container>
        <ModelDebug
          app={app}
          paramsInPrompt={paramsInPrompt}
          setInputParams={setInputParams}
          modelConfig={modelConfig}
          debugConfig={debugConfig}
          completionResults={completionResults}
          onModelsChange={onModelsChange}
          newPrompt={newPrompt}
          compeleteResultRef={compeleteResultRef}
          remoteConfig={remoteConfig}
        />
      </Container>
    );
  }

  return (
    <Container>
      {/* 生成型 */}
      {(app.type === IAppType.AgentCompletion || app.type === IAppType.Evaluator) &&
        <Flex vertical flex={1}
              style={{
                overflow: 'scroll',
                scrollBehavior: 'smooth',
              }}
              ref={compeleteResultRef}
        >
          <AgentParamsRender
            paramsInPrompt={paramsInPrompt}
            // onCommit={onCompleteRun}
            onCommit={val => {
              if (mode === 'promptDebug') {
                // @ts-ignore
                completionResults.current?.onCompleteRun(val);
                return;
              }
              // @ts-ignore
              completionResult.current?.onCompleteRun(val);
            }}
            onChange={setInputParams}
            modelConfig={modelConfig}
            modelsConfig={debugConfig?.modelsConfig}
            labelAlign="left"
            showCommitBtn={app.type === IAppType.AgentCompletion || app.type === IAppType.Evaluator}
          />
          {/* 生成型 */}
          {renderCompleteResult()}
        </Flex>}
      {/* 聊天型 */}
      {app.type === IAppType.AgentConversation &&
        <div style={{ flex: 1, overflow: 'hidden', marginBottom: '10px' }}>
          {renderConversation({ key: conversationKey })}
        </div>
      }
    </Container>
  );
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  /* background: #fafafa; */
  border-radius: 0;
  /* padding: 0 20px; */
  height: 100%;

  .ant-card-body {
    /* height: calc(100vh - 145px); */
    height: 100%;
    padding: 0 20px;
    background: #fafafa;
    display: flex;
    flex-direction: column;
  }
`;

export default React.memo(RightContainer);