import { CloudOutlined } from "@ant-design/icons";
import { Drawer, Flex, Tooltip } from "antd";
import { useState } from "react";
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import { PromptCard } from "../prompt-card";
import { singleConfigDiff } from "./virtual-human/config-diff/get-diff-str";

const OnlineNewPrompt = (props) => {
  const { value, appType, oldValue } = props;
  const [open, setOpen] = useState(false);

  const onOpen = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  return <>
    <Flex>
      <Tooltip title="查看线上提示词">
        <span onClick={onOpen} style={{ fontWeight: 'normal', color: 'rgba(0,0,0,0.88)', cursor: 'pointer' }}>
          <CloudOutlined />
        </span>
      </Tooltip>
    </Flex>
    <Drawer title="线上提示词"
      open={open}
      onClose={onClose}
      width={'100vw'}>
      {/* <Flex style={{ width: '100%', height: '100%' }}>
        <PromptCard value={value} appType={appType} disabled />
      </Flex> */}
      <ReactDiffViewer
        leftTitle={"线上提示词"}
        rightTitle={"当前提示词"}
        oldValue={singleConfigDiff.prePrompt.getDiffStr(oldValue)}
        newValue={singleConfigDiff.prePrompt.getDiffStr(value)}
      />
    </Drawer>
  </>
};

export default OnlineNewPrompt;
