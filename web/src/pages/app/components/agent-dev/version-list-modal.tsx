import { Modal } from "antd";
import { VersionList } from "./version-list";
import { forwardRef, useImperativeHandle, useState } from "react";

const VersionListModal = (props, ref) => {
  const [open, setOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    show: () => {
      setOpen(true);
    }
  }));

  const onCancel = () => {
    setOpen(false);
  };

  return <>
    <Modal
      title={<span>选择版本<span style={{
        fontSize: 12,
        fontWeight: 'normal',
        color: '#999',
        marginLeft: 5
      }}>回滚后线上调用立刻生效</span></span>}
      open={open}
      onCancel={onCancel}>
      <VersionList {...props} />
    </Modal>
  </>
};

export default forwardRef(VersionListModal);
