import { getDefaultConfig } from '@/utils/model-helper';
import { IAppType } from '@/interface';
import { omit, pick } from 'lodash';

// 没有newPrompt 说明不是新版,这段逻辑兼容旧版本
export const initRemoteAppConfig = (config: any, app: any) => {
  if (!config) {
    config = {};
  }
  const dropKeys = ['modelName', 'modelParams', 'providerKind'];
  if (app.type !== IAppType.AgentConversation) {
    dropKeys.push('welcomeText');
  }

  const defaultConfig: any = getDefaultConfig(IAppType.AgentConversation);

  // 本地用了modelConfig字段
  config.modelConfig = pick(config, ['modelName', 'modelParams', 'providerKind']);

  if (!config?.newPrompt) {
    // 以前的版本
    if (config.prePrompt) {
      config.newPrompt = {
        promptType: 'raw',
        prompt: config.prePrompt,
      };
    } else {
      config.newPrompt = { ...defaultConfig.newPrompt };
    }
  }

  if (!config.modelsConfig) {
    config.modelsConfig = {
      retryConfig: {
        retryCount: 0,
      },
      models: [
        {
          ratio: 1,
          modelName: config.modelName,
          modelParams: config.modelParams,
          providerKind: config.providerKind,
        },
      ],
    };
  }
  // drop废弃的key
  const res = omit(config, dropKeys);
  return res;
};
