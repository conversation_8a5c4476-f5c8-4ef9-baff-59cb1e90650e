// @ts-ignore
import { MoreOutlined, RedoOutlined, ReloadOutlined, RocketOutlined } from '@ant-design/icons';
import { Input, message, Modal, Space, Form, Alert, Flex, Button, Dropdown, Tooltip, Tag, Tabs } from 'antd';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';

import { AppApi } from '@/api/app';
import { LayoutContainer } from '@/components/layout-container';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppDetailModel, IAppType } from '@/interface';

import Workflow from '../../workflow/index';
import VirtualHuman from './virtual-human'
import { ToolsCard } from './plugins';
import { getLocalData, saveLocalData } from '@/utils/common';
import { WelcomeCard } from './mods/welcome';
import { useLocalStorage } from '@/hooks/useStorage';
import { PopoApi } from '@/api/popo';
import { MenuProps } from 'antd/lib';
import { PromptCard } from '../prompt-card';
import { AgentTemplateApi } from '@/api/agent-template';
import './index.less';
import { TopBar } from './top-bar';
import RightContainer from './right-container';
import { useLock } from '@/hooks/useLock';
import ConfigDiff from './virtual-human/config-diff';
import { filterParams } from '../prompt-editor/utils';
import { hasDraft } from './virtual-human/config-diff/utils'
import { initRemoteAppConfig } from './utils';
import ParamsCard from '../params-card';
import NewKnowledgeCard from './mods/knowledge-card';
import VersionListModal from './version-list-modal';
import ApprovalModal from '@/components/approval-modal';
import StoryLine from '../../story-line';
import { getCurrentUseAppKey } from '@/utils/localStorageKey';
import ModelsCard from './mods/models-card';
// import ModelsCard from './mods/models-card';

const { confirm } = Modal;

const TypeMap = {
  [IAppType.AgentConversation]: '聊天助理',
  [IAppType.AgentCompletion]: '生成型应用',
  [IAppType.Evaluator]: '评估器应用',
}

export function DevPage() {
  const { globalState, updateGlobalState } = useGlobalState();
  const { app, user } = globalState;

  // 用来记录最近访问
  const recentView = useRef(false);

  if (!app) {
    return null;
  }
  // 最近访问
  if (!recentView.current) {
    // 记录到最近使用
    const currentUseAppKey = getCurrentUseAppKey({ userId: user.id });
    const currentData = getLocalData(currentUseAppKey) || [];

    let newData = [...currentData];

    // 是否存在
    const index = currentData?.findIndex(item => item.id === app.id);

    if (index > -1) {
      newData.splice(index, 1);
    }

    newData.unshift(app);
    newData.splice(12);

    saveLocalData(currentUseAppKey, newData);
    recentView.current = true;
  }


  if (app.type === IAppType.AgentWorkflow || app.type === IAppType.Workflow) {
    return <Workflow type={app.type}></Workflow>
  }

  if (app.type === IAppType.VirtualHuman) {
    return <VirtualHuman type={app.type} app={app}></VirtualHuman>
  }

  if (app.type === IAppType.StoryLine) {
    return <StoryLine type={app.type} app={app} />
  }

  return <AgentDevPage app={app} updateGlobalState={updateGlobalState} />;
}

const Guide = styled.div`
  width: 200px;
`;

interface IAgentDevProps {
  app: AppDetailModel;
  updateGlobalState: any;
}

let completeCache = '';

const guide = <Guide>{'模型固定的引导词，通过调整该内容，可以引导模型聊天方向。该内容会被固定在上下文的开头。可使用变量，例如 {{language}}，如果想使用知识库问答，可以使用预置的{{docs}}变量'}</Guide>

const items: MenuProps['items'] = [
  {
    label: '回滚',
    icon: <ReloadOutlined></ReloadOutlined>,
    key: 'reload',
  },
];

// 没有newPrompt 说明不是新版,这段逻辑兼容旧版本
// const initRemoteConfig = (config: any) => {
//   if (!config) {
//     config = {};
//   };
//   const defaultConfig: any = getDefaultConfig(IAppType.AgentConversation);

//   // 本地用了modelConfig字段
//   config.modelConfig = pick(config, ['modelName', 'modelParams', 'providerKind']);

//   if (!config?.newPrompt) {
//     // 以前的版本
//     if (config.prePrompt) {
//       config.newPrompt = {
//         promptType: 'raw',
//         prompt: config.prePrompt
//       }
//     } else {
//       config.newPrompt = { ...defaultConfig.newPrompt };
//     }
//   };
//   // drop废弃的key
//   const res = omit(config, ['modelName', 'modelParams', 'providerKind']);
//   return res;
// };

const initLocalConfig = (config: any, app: any) => {
  if (!config) {
    return {}
  }
  if (!config.hasOwnProperty('newPrompt') && config.hasOwnProperty('prePrompt')) {
    config.newPrompt = {
      promptType: 'raw',
      prompt: config.prePrompt
    }
  }

  const dropKeys = [];

  if (app?.type !== IAppType.AgentConversation) {
    dropKeys.push('welcomeText');
  }

  if (!config.hasOwnProperty('modelsConfig') && config.hasOwnProperty('modelConfig')) {
    config.modelsConfig = {
      retryConfig: {
        retryCount: 0,
      },
      models: [
        {
          ratio: 1,
          ...(config.modelConfig || {})
        },
      ],
    };
  }
  const res = omit(config, dropKeys)
  return res;
};


function AgentDevPage(props: IAgentDevProps) {
  const { app, updateGlobalState } = props;
  const { globalState } = useGlobalState();
  const { user } = globalState;
  const { lock } = useLock();
  // 没有newPrompt 说明不是新版,这段逻辑兼容旧版本
  let config: any = initRemoteAppConfig(app.config, app);

  const localStorageKey = `app-${app.id}`;

  // 模块展开本地存储
  const localExpandStorageKey = `app-${app.id}-module-expand`;

  const [_localConfig] = useLocalStorage(localStorageKey);
  const localConfig = initLocalConfig(_localConfig, app);

  const [currentConfig, setCurrentConfig] = useState({
    ...config,
    ...localConfig,
  });
  // 配置相关（新）带有结构化数据 
  // {
  //   struct: [],
  //   text: '',
  //   promptType: 'struct'
  // }
  const { welcomeText, paramsInPrompt, knowledge, modelConfig, prePrompt, newPrompt, tools, knowledgeConfig, modelsConfig } = currentConfig;

  // const useLocal = (localConfig?.time || 0) > (+new Date(app?.updatedAt || Date.now()));
  const { newPrompt: new_prompt, prePrompt: pre_prompt, paramsInPrompt: params_in_prompt, tools: selectedTools, knowledge: pre_knowledge, welcomeText: pre_welcome } = config || {};
  const [conversationKey, setConversationKey] = useState('' + Date.now());
  const pubMessage = useRef('');

  const [useLocal, setUseLocal] = useState();
  const useLocalRef = useRef();
  const versionListModal = useRef(null);

  // 提示词对比调试逻辑
  const [mode, setMode] = useState<'edit' | 'promptDebug' | 'modelDebug'>('edit');

  // 切换功能
  const [activeKey, setActiveKey] = useState<'prompt' | 'skill'>('prompt');

  useEffect(() => {
    if (useLocalRef.current) {
      clearTimeout(useLocalRef.current);
    }
    // @ts-ignore
    useLocalRef.current = setTimeout(() => {
      setUseLocal(hasDraft(config, currentConfig))
    }, 500);

    return () => {
      clearTimeout(useLocalRef.current);
      useLocalRef.current = null;
    }
  }, [
    config, currentConfig
  ]);

  const remoteConfig = {
    ...config,
    // welcomeText: app.type === IAppType.AgentConversation ? pre_welcome : '',
  };

  // 本地的配置
  const modifiedConfig = {
    ...currentConfig,
    // welcomeText: app.type === IAppType.AgentConversation ? welcomeText : '',
  };

  if (app.type === IAppType.AgentConversation) {
    remoteConfig.welcomeText = pre_prompt;
    modifiedConfig.welcomeText = welcomeText;
  }

  // 本地调试的配置
  const debugConfig = {
    ...currentConfig,
    ...(modelConfig || {}),
  };

  useEffect(() => {
    const local = initLocalConfig(getLocalData(localStorageKey), app);
    setCurrentConfig({
      ...config,
      ...(local || {})
    });

  }, [localStorageKey, app.config]);

  const usedUserParams = useMemo(() => {
    return filterParams(prePrompt);
  }, [prePrompt]);

  const onConversationClear = useCallback(() => {
    setConversationKey('' + Date.now());
  }, []);

  // 重置
  function showResetModal() {
    confirm({
      title: '确认重置？',
      content: '重置会放弃当前页面的所有修改，恢复至上一次发布的状态',
      onOk() {
        setCurrentConfig(config);
        onConversationClear();
        saveLocalData(`app-${app.id}`, { time: 0 });
      },
      onCancel() { },
    });
  }

  function showPublishModal() {
    confirm({
      title: <span>确认发布?<span style={{
        fontSize: 12,
        fontWeight: 'normal',
        color: '#999',
        marginLeft: 5
      }}>发布后线上调用立刻生效</span></span>,
      content: function () {
        const id = dayjs().format('MMDDHHmm');
        pubMessage.current = `${id}`;
        return <div style={{ paddingTop: 10 }}>
          {newPrompt?.promptType === 'struct' ?
            <div>
              发布后，当前结构化prompt会强制更新文本prompt
            </div> :
            <div>发布后，当前文本prompt会强制覆盖结构化prompt</div>}
          <Form.Item label="版本号" rules={[{ required: true, message: '请输入版本描述' }]} required><Input disabled defaultValue={id}></Input></Form.Item>
          <Form.Item label="版本描述" rules={[{ required: true, message: '请输入版本描述' }]}><Input onChange={(ev) => pubMessage.current = `${id}-${ev.target.value}`}></Input></Form.Item>
        </div>
      }(),
      onOk() {
        onPublish();
      },
      onCancel() { },
    });
  }

  function showReloadModal() {
    versionListModal.current?.show();
    // confirm({
    //   title: <span>选择版本<span style={{
    //     fontSize: 12,
    //     fontWeight: 'normal',
    //     color: '#999',
    //     marginLeft: 5
    //   }}>回滚后线上调用立刻生效</span></span>,
    //   content: <VersionList
    //     app={app}
    //     updateGlobalState={updateGlobalState}
    //     newValue={modifiedConfig}
    //     onItemChange={handleChange}
    //   ></VersionList>,
    //   onOk() { },
    //   onCancel() { },
    //   styles: {
    //     content: {
    //       width: 800
    //     }
    //   }
    // });
  }

  // 发布
  async function onPublish() {

    // 结构化的时候要转换，同步下prePrompt
    const newConfig: any = await handleChange('newPrompt', newPrompt);

    // 同步需要更新一下POPO那边的类型
    if (app.type === IAppType.AgentConversation) {
      PopoApi.updateType(false, app.id);
    }
    AppApi.updateApp(
      {
        config: {
          ...app.config,
          ...newConfig,
          ...modelConfig,
          message: pubMessage.current
        },
      },
      app.id,
    ).then((res) => {
      // 发布后要更新本地信息
      updateGlobalState('app', res);
      pubMessage.current = ''
      message.success('发布成功');
      saveLocalData(localStorageKey, { time: 0 })
    });
  }

  const onStructPromptChange = async val => {
    try {
      const res = await AgentTemplateApi.struct2Text({ structPrompt: val?.structPrompt });
      return {
        newPrompt: val,
        prePrompt: res.prompt
      }
    } catch (error) {

    }
  };
  const onTextPromptChange = async val => {
    return {
      newPrompt: val,
      prePrompt: val.prompt
    }
  }

  const onTemplateChange = async template => {
    // others paramsInPrompt, modelConfig, knowledge
    const { prompt, promptType, structPrompt, ...others } = template;

    let prePrompt = prompt;

    const newConfig: any = {
      newPrompt: {
        prompt, promptType, structPrompt
      }
    };

    // // 更新知识库
    // if ('knowledge' in others) {
    //   newConfig.knowledge = others.knowledge;
    // }

    // // 更新modelConfig
    // if ('modelConfig' in others) {
    //   newConfig.modelConfig = others.modelConfig;
    // }

    // 更新paramsInPrompt
    if ('paramsInPrompt' in others) {
      newConfig.paramsInPrompt = others.paramsInPrompt;
    }

    // // 更新 welcomeText
    // if ('welcomeText' in others) {
    //   newConfig.welcomeText = others.welcomeText;
    // }

    // 更新prePrompt
    if (promptType === 'raw') {
      newConfig.prePrompt = prePrompt;
    }
    if (promptType === 'struct') {
      const res = await AgentTemplateApi.struct2Text({ structPrompt: structPrompt });
      newConfig.prePrompt = res.prompt;
    }

    setCurrentConfig({
      ...currentConfig,
      ...newConfig,
    });

    // 更新本地存储
    saveLocalData(localStorageKey, {
      ...getLocalData(localStorageKey),
      ...currentConfig,
      ...newConfig,
      time: Date.now(),
    });
  }

  const handleNewPromptChange = async val => {
    let res = {
      newPrompt: val
    };
    if (val?.promptType === 'raw') {
      res = await onTextPromptChange(val);
    }
    if (val?.promptType === 'struct') {
      res = await onStructPromptChange(val)
    }
    return res;
  }

  const handleChange = async (key, value) => {
    // 新的currentConfig
    const newCurrentConfig = {
      ...currentConfig,
      [key]: value
    };

    setCurrentConfig(newCurrentConfig);

    if (key === 'newPrompt') {
      const res: any = await handleNewPromptChange(value);
      newCurrentConfig.prePrompt = res.prePrompt;
      setCurrentConfig(newCurrentConfig);
    }

    if (key === 'modelsConfig') {
      // 聊天助理最多只选择一个模型
      if (app.type === IAppType.AgentConversation) {
        newCurrentConfig.modelsConfig = {
          retryConfig: value?.retryConfig,
          models: [{
            ...(value?.models?.[0] || {}),
            ratio: 1
          }]
        }
      }
    }
    // 更新本地数据
    saveLocalData(localStorageKey, {
      ...newCurrentConfig,
      time: Date.now(),
    });
    return newCurrentConfig;
  }

  const handleMenuClick = item => {
    if (item.key === 'reload') {
      showReloadModal();
    }
  }

  const onDebugPrompt = () => {
    setMode('promptDebug');
  };

  const onActiveKeyChange = val => {
    setActiveKey(val);
  };

  const onCloseDebugPrompt = () => {
    setMode('edit');
  };

  return (
    <div style={{ height: "100%" }}>
      {lock ? (
        <Alert
          style={{ zIndex: 10000, marginTop: -10 }}
          message={
            <span>
              当前应用{' '}
              <a
                href={`http://popo.netease.com/static/html/open_popo.html?ssid=${lock?.username}@corp.netease.com&sstp=0`}
                target="_blank"
                rel="noreferrer"
              >
                {lock?.username}
              </a>{' '}
              正在编辑中，暂时只能查看和调试，如果需要可以联系对方释放资源
            </span>
          }
          banner
        />
      ) : null}
      <LayoutContainer
        innerStyle={{
          display: 'flex',
          width: '100%',
          backgroundColor: '#fff',
          borderRadius: 10,
        }}
      >
        <LeftContainer
          style={{
            width: mode === 'edit' ? '60%' : '30%',
            height: "98%",
            paddingRight: '12px',
            position: 'relative',
            backgroundColor: '#FBFCFF',
          }}
        >
          <Flex justify='space-between' align='center'>
            <TopBar
              type={TypeMap[app.type]}
              oldModelConfig={config?.modelConfig}
              modelConfig={modelConfig}
              onModelChange={val => handleChange('modelsConfig', val)}
              onReset={showResetModal}
              onPublish={showPublishModal}
              onMenuClick={handleMenuClick}
              items={items}
              app={app}
              prePrompt={prePrompt}
              paramsInPrompt={paramsInPrompt}
              modelsConfig={modelsConfig}
              oldModelsConfig={config?.modelsConfig}
              showModelConfig={mode === 'edit'}
            />
            <Flex style={{ fontSize: '12px', color: '#999', userSelect: 'none' }}>
              {useLocal ?
                <Flex align='center'>
                  <ConfigDiff
                    isDiff={useLocal}
                    // oldValue={remoteConfig}
                    newValue={modifiedConfig}
                    onItemChange={handleChange}
                    drawerProps={{
                      footer: null,
                      title: '本地草稿'
                    }}
                    trigger={
                      <Tooltip title="查看草稿">
                        <Tag color="blue" style={{ fontSize: '12px' }}>本地草稿</Tag>
                      </Tooltip>}
                    appType={app.type}
                    appId={app.id}
                  />
                  <span style={{ paddingRight: '6px', fontSize: 12 }}>
                    {/* @ts-ignore */}
                    最后保存于：{dayjs(localConfig?.time).format('YYYY-MM-DD HH:mm:ss')}
                  </span>
                  <ConfigDiff
                    isDiff={useLocal}
                    // oldValue={remoteConfig}
                    newValue={modifiedConfig}
                    onItemChange={handleChange}
                    trigger={
                      <Tooltip title="放弃本地草稿，重置到最新一次保存">
                        <RedoOutlined style={{ color: '#1677ff' }} />
                      </Tooltip>
                    }
                    onOk={() => {
                      showResetModal();
                    }}
                    okText="确认无误，重置到最新一次保存"
                    drawerProps={{
                      title: '重置'
                    }}
                    appType={app.type}
                    appId={app.id}
                  />
                </Flex>
                :
                <span>
                  <Tag color="orange">服务器</Tag>
                  最后保存于：{dayjs(app.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                </span>}
            </Flex>
          </Flex>
          {mode === 'promptDebug' && <Tabs
            items={[
              { label: '提示词', key: 'prompt' },
              { label: '技能', key: 'skill' }
            ]}
            activeKey={activeKey}
            onChange={onActiveKeyChange}
            tabBarStyle={{
              margin: 0
            }}
          ></Tabs>}
          <div style={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
            {(['edit', 'modelDebug'].includes(mode) || (mode === 'promptDebug' && activeKey === 'prompt'))
              && <div
                style={{
                  marginRight: mode === 'edit' ? '10px' : 0,
                  width: mode === 'edit' ? '60%' : '100%',
                  minWidth: 320 }}
              >
                <PromptCard
                  disabled={lock}
                  appType={app.type}
                  oldValue={config?.newPrompt}
                  value={{
                    ...(newPrompt || {}),
                  }}
                  onChange={val => handleChange('newPrompt', val)}
                  // // 文本模式的保存
                  // onTextPromptChange={onTextPromptChange}
                  // // 兼容structprompt的保存
                  // onStructPromptChange={onStructPromptChange}

                  // 文本模式的保存
                  onTextPromptChange={val => handleChange('newPrompt', val)}
                  // 兼容structPrompt的保存
                  onStructPromptChange={val => handleChange('newPrompt', val)}
                  paramsInPrompt={paramsInPrompt}
                  onTemplateChange={onTemplateChange}
                  config={{
                    paramsInPrompt,
                    welcomeText,
                    modelConfig,
                  }}
                  oldPrePrompt={pre_prompt}
                  newPrePrompt={prePrompt}
                  mode={mode}
                  onDebugPrompt={onDebugPrompt}
                  useLocal={useLocal}
                />
              </div>}
            {(mode === 'edit' || (mode === 'promptDebug' && activeKey === 'skill')) && <div
              style={{
                display: 'flex',
                width: mode === 'edit' ? '50%' : '100%',
                justifyContent: 'space-between',
                flexWrap: 'wrap',
                overflow: 'scroll'
              }}
            >
              <Flex vertical style={{ width: '100%', marginBottom: '10px' }}>
                {<ModelsCard
                  appType={app.type}
                  expandLocalKey={localExpandStorageKey}
                  value={modelsConfig}
                  oldValue={config?.modelsConfig}
                  onChange={val => handleChange('modelsConfig', val)}
                  setMode={setMode}
                />}
                {app.type === IAppType.AgentConversation ?
                  <WelcomeCard
                    expandLocalKey={localExpandStorageKey}
                    oldValue={pre_welcome}
                    welcomeText={welcomeText} onChange={(val) => handleChange('welcomeText', val)} />
                  : null}
                {/* <ParamsCard
                  value={paramsInPrompt}
                  oldValue={params_in_prompt}
                  onChange={val => handleChange('paramsInPrompt', val)}
                  appId={app.id}
                  notAllowClears={usedUserParams}
                  appType={app.type}
                /> */}
                <ParamsCard
                  expandLocalKey={localExpandStorageKey}
                  value={paramsInPrompt}
                  oldValue={params_in_prompt}
                  onChange={val => handleChange('paramsInPrompt', val)}
                  appId={app.id}
                  notAllowClears={usedUserParams}
                  appType={app.type} />
                {Object.keys(tools || {}).length ?
                  <ToolsCard toolIDs={tools} onToolsChanged={val => handleChange('tools', val)} />
                  : null
                }
                {/* <KnowledgeCard
                  expandLocalKey={localExpandStorageKey}
                  oldValue={pre_knowledge}
                  knowledge={knowledge} onDocsChange={(val) => handleChange('knowledge', val)} /> */}
                <NewKnowledgeCard
                  appId={app.id}
                  value={knowledgeConfig}
                  oldValue={config?.knowledgeConfig}
                  onChange={val => handleChange('knowledgeConfig', val)}
                  modelConfig={modelConfig}
                />
              </Flex>
            </div>}
          </div>
          {lock ? <Mask onClick={(ev) => {
            // 禁止穿透
            ev.stopPropagation();
            ev.preventDefault();
          }}></Mask> : null}
        </LeftContainer>
        <Flex
          style={{
            width: mode === 'edit' ? '40%' : '70%',
            backgroundColor: '#fff',
            padding: '0 20px'
          }}
          vertical
        >
          <Flex justify="space-between" align='center'>
            {mode === 'edit' && <h3>调试与预览</h3>}
            {mode === 'promptDebug' && <h3>提示词对比调试</h3>}
            {mode === 'modelDebug' && <h3>模型对比调试</h3>}
            <Flex>
              {mode === 'edit' && <Space>
                <ConfigDiff
                  isDiff={useLocal}
                  // oldValue={modifiedConfig}
                  newValue={remoteConfig}
                  onOk={showResetModal}
                  trigger={<Button icon={<RedoOutlined />} disabled={!useLocal}>重置</Button>}
                  okText="确认无误，重置"
                  drawerProps={{
                    title: '重置'
                  }}
                  onItemChange={handleChange}
                  appType={app.type}
                  appId={app.id}
                />
                {/* {useLocal ?  */}
                <ApprovalModal
                  updateGlobalState={updateGlobalState}
                  app={app}
                  setting={currentConfig}
                  settingId={currentConfig?.id}
                  appName={app?.name}
                  action="DEPLOY" // 或其他操作类型
                  appType="aiOther" // 应用类型
                  operator={user?.email}
                  onApprove={() => {
                    // 处理审批通过后的逻辑
                    console.log('审批已创建');
                  }}
                >
                  <Dropdown.Button
                    type='primary'
                    icon={<MoreOutlined />}
                    menu={{ items, onClick: handleMenuClick }}>
                    <ConfigDiff
                      isDiff={useLocal}
                      // oldValue={remoteConfig}
                      newValue={modifiedConfig}
                      onOk={showPublishModal}
                      trigger={
                        <>
                          <RocketOutlined style={{ marginRight: 6 }} />
                          发布
                        </>
                      }
                      okText="确认无误，发布"
                      drawerProps={{
                        title: '发布'
                      }}
                      onItemChange={handleChange}
                      appType={app.type}
                      appId={app.id}
                    />
                  </Dropdown.Button>
                </ApprovalModal>
                {/* : <Button onClick={showReloadModal}> */}
                {/* <RedoOutlined /> */}
                {/* 回滚 */}
                {/* </Button> } */}
                <VersionListModal
                  ref={versionListModal}
                  app={app}
                  updateGlobalState={updateGlobalState}
                  newValue={modifiedConfig}
                  onItemChange={handleChange} />
              </Space>}
              {['promptDebug', 'modelDebug'].includes(mode) && <Button type="primary" onClick={onCloseDebugPrompt}>完成对比</Button>}
            </Flex>
          </Flex>
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <RightContainer
              app={app}
              paramsInPrompt={paramsInPrompt}
              welcomeText={welcomeText}
              modelConfig={modelConfig}
              prePrompt={prePrompt}
              tools={tools}
              newPrompt={newPrompt}
              debugConfig={debugConfig}
              conversationKey={conversationKey}
              setPrePrompt={val => {
                setCurrentConfig({
                  ...currentConfig,
                  prePrompt: val
                });
              }}
              mode={mode}
              remoteConfig={remoteConfig}
              onModelsChange={val => handleChange('modelsConfig', val)}
              oldModelsConfig={config?.modelsConfig}
            />
          </div>
        </Flex>
      </LayoutContainer >
    </div>
  );
}

const Mask = styled.div`
  position: absolute;
  user-select: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: all; 
  background: rgb(255 255 255 / 50%);
`;

const LeftContainer = styled.div`
  display: flex;
  flex-direction: column;
  
  border-radius: 8px;
  box-sizing: border-box;
  padding-left: 20px;
  .ant-card-head {
    background: #fff;
  }
  .ant-card-body {
    background: #fff;
  }
  textarea {
    background: #f5f8fc;
  }
`;
