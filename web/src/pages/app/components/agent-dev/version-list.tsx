import { Table, Tag, Button, message, Space } from 'antd';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { AppApi } from '@/api/app';
import { useState } from 'react';
import ConfigDiff from './virtual-human/config-diff';

interface VersionListProps {
  app: any;
  updateGlobalState: (key: string, value: any) => void;
  newValue: any;
  onItemChange: (key: string, itemValue: any) => void;
}

export const VersionList: React.FC<VersionListProps> = ({ app, updateGlobalState, newValue, onItemChange }) => {
  const [curId, setId] = useState(app.app_config_id);
  const pageSize = 5;
  const [pageNumber, setPage] = useState(1);
  const { data } = useRequest(() => AppApi.getAppHistoryWithPage(pageNumber, pageSize, app.id), {
    refreshDeps: [pageNumber]
  });

  const handleReload = id => {
    AppApi.updateAppVersion(id, app.id).then((res) => {
      updateGlobalState('app', res);
      setId(id);
      message.success('回滚成功')
    })
  }

  const columns = [{
    title: '版本号',
    dataIndex: 'message',
    render: v => v ?? '-'
  }, {
    title: '创建时间',
    dataIndex: 'createdAt',
    render: v => v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '-'
  }, {
    title: '操作',
    dataIndex: 'id',
    render: (id, record) => {
      if (id === curId) {
        return <Tag color="success">当前版本</Tag>;
      }
      return <Space>
        <ConfigDiff
          isDiff
          newValue={newValue}
          // oldValue={record?.config}
          getOldValue={async () => record}
          trigger={
            <Button type="link" size='small'>查看对比</Button>
          }
          appType={app.type}
          appId={app.id}
          drawerProps={{
            title: '查看对比'
          }}
          okText="回滚"
          onOk={() => {
            handleReload(id);
          }}
          onItemChange={onItemChange}
          leftTitle={record.message}
        />
        <Button type="link" size='small' onClick={() => handleReload(id)}>回滚</Button>
      </Space>
    }
  }];

  return <Table
    dataSource={data?.items ?? []}
    columns={columns}
    pagination={{
      pageSize: pageSize,
      total: data?.total || 0,
      current: pageNumber,
      onChange: setPage
    }}
  />;
}; 