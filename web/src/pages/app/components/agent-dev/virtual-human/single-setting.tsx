// @ts-ignore
import {
  RocketOutlined,
  RedoOutlined,
  ReloadOutlined,
  MoreOutlined,
  SplitCellsOutlined,
  CheckCircleTwoTone,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  message,
  Modal,
  Space,
  Alert,
  Flex,
  Tag,
  Tooltip,
  Dropdown,
  Tabs,
  Image,
} from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';

import { AppApi } from '@/api/app';
import { LayoutContainer } from '@/components/layout-container';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppDetailModel, IAppType } from '@/interface';
import { getDefaultConfig } from '@/utils/model-helper';

import { useRequest } from 'ahooks';
import { getLocalData, saveLocalData } from '@/utils/common';
import { useLocalStorage } from '@/hooks/useStorage';
import '../index.less';
import { MenuProps } from 'antd/lib';
import { MultipleConfigApi } from '@/api/multiple-setting';
import PublishDrawer from '../mods/publish-drawer';
import ChatSettingDrawer from '../mods/chat-setting-drawer';
// import PluginsCard from '../plugins-card';
import TriggerCard from '../mods/trigger-card';
import WelcomeTriggerCard from '../mods/trigger-card/welcome';
import PluginsCard from '../mods/plugins-card';
import MemoryCard from '../mods/memory-card';
import ConfigHistoryDrawer from '../mods/config-history-drawer';
import { HumanModelCard } from '../mods/human-model-card';
import { TopBar } from './top-bar';
import { useLock } from '@/hooks/useLock';
import { getWorkspaceId } from '@/utils/state';
import GroupsPrompt from '../../virtual-human/groups-prompt';
import ChatBot from '../../chat-bot';
import ConfigDiff from './config-diff';
import dayjs from 'dayjs';
import { filterParams } from '../../prompt-editor/utils';
import EmojiCard from '../mods/emoji-card';
import DiffAlert from '../mods/diff-alert';
// import EmojiEditModal from '../../emoji/emoji-edit-modal';
import { hasDraft } from './config-diff/utils';
import ParamsCard from '../../params-card';
import { initRemoteSettingValue } from './utils';
import AggInputCard from '../mods/agg-input-card';
import ViewPrompt from '../../virtual-human/groups-prompt/view-prompt';
import AddNewParams from './add-new-params';
import KnowledgeCard from '../mods/knowledge-card';
import OutputCard from '../mods/ouput-card';
import TtsCard from '../mods/tts-card';
import AnimationCard from '../mods/animation-card';
import { EyeIcon } from '@/components/icons';
import VirtualHumanVersionListModal from './virtual-human-version-list-modal';
import ConversationDebugBot from '@/components/lang-bot-component/conversation-bot/conversation-debug-bot';
import { omit } from 'lodash';
import OnlineGroupPrompt from './online-groups-prompt';
import PromptOptimize from '../../virtual-human/prompt-optimize';
import { getCurrentUseAppKey } from '@/utils/localStorageKey';
import { ChannelsApi } from '@/api';
import { inValidSkills } from './channel-skill-check';



const { confirm } = Modal;

const initLocalValue = (config) => {
  if (!config) {
    return {};
  }

  if (!config.hasOwnProperty('groupPrompt')) {
    if (config.hasOwnProperty('newPrompt')) {
      config.groupPrompt = {
        groups: [
          {
            name: '个人资料',
            promptType: config.newPrompt?.promptType === 'struct' ? 'struct' : 'raw',
            prompt: config.newPrompt?.prompt,
            structPrompt:
              config.newPrompt?.promptType === 'struct'
                ? config.newPrompt?.structPrompt?.map((item) => {
                  return {
                    name: item.title,
                    prompt: item.content,
                    filter: {},
                  };
                })
                : [],
          },
        ],
      };
      delete config.newPrompt;
    } else {
      if (config.hasOwnProperty('prePrompt')) {
        config.groupPrompt = {
          groups: [
            {
              name: '个人资料',
              promptType: 'raw',
              prompt: config.prePrompt,
            },
          ],
        };
        delete config.prePrompt;
      }
    }
  }

  return config;
};

export default function SingleConfig(props) {
  const { settingId, info, onloaded, ...rest } = props;
  const { globalState, updateGlobalState } = useGlobalState();
  const [loading, setLoading] = useState(false);
  const { app, user } = globalState;
  const [setting, setSetting] = useState(null);


  const getTriggerList = (triggerList) => {
    if (!triggerList) {
      return Promise.resolve([]);
    }
    const ids = triggerList?.map((item) => item.id);
    return AppApi.queryTrigger({ appId: app.id, ids });
  };

  // 合并触发器配置到config
  const mergeTriggerList = (res) => {
    const data = res;
    data.config = initRemoteSettingValue(res.config || {});
    if (res?.config?.triggerList?.length) {
      setLoading(true);
      getTriggerList(res?.config?.triggerList)
        .then((res) => {
          data.config.triggers = res?.triggerDetailList;
          setSetting(data);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      data.config.triggers = [];
      setSetting(res);
    }
  };

  // 获取最新保存的配置
  const getRemoteConfig = () => {
    setLoading(true);
    MultipleConfigApi.getSettingConfig({
      appId: app.id,
      settingId,
    })
      .then((res) => {
        if (res) {
          mergeTriggerList(res);
        }
      })
      .finally(() => {
        setLoading(false);
        onloaded();
      });
  };

  useEffect(() => {
    if (settingId) {
      getRemoteConfig();
    }
  }, [settingId]);

  const updateSetting = (value) => {
    if (value?.config) {
      mergeTriggerList(value);
    } else {
      getRemoteConfig();
    }
  };

  if (!app || !setting) {
    return null;
  }

  return (
    <AgentDevPage
      app={app}
      user={user}
      loading={loading}
      updateGlobalState={updateGlobalState}
      settingId={settingId}
      setting={{ ...info, ...(setting || {}) }}
      updateSetting={updateSetting}
      {...rest}
    />
  );
}

interface IAgentDevProps {
  app: AppDetailModel;
  updateGlobalState: any;
  settingId: string;
  loading: boolean;
  setting: any;
  onEdit: (settingId, action, data) => void;
  updateSetting: any;
  user: any;
}

function AgentDevPage(props: IAgentDevProps) {
  const { app, setting = {}, settingId, onEdit, updateSetting, user } = props;
  const workspaceId = getWorkspaceId();
  let config: any = setting.config;
  const { lock } = useLock(settingId);

  // 存储key
  const localStorageKey = `app-${app.id}-setting-${settingId}`;

  // 模块展开本地存储
  const localExpandStorageKey = `app-${app.id}-setting-${settingId}-module-expand`;

  // 本地newprompt兼容
  const [_localConfig] = useLocalStorage(localStorageKey);
  const localConfig = initLocalValue(_localConfig);

  // const useLocal = (localConfig?.time || 0) > (+new Date(setting?.updatedAt || Date.now()));

  const [conversationKey, setConversationKey] = useState('' + Date.now());
  const [historyDrawerVisible, setHistoryDrawerVisible] = useState(false);
  const [useLocal, setUseLocal] = useState();
  const [newConfig, setNewConfig] = useState({
    ...config,
    ...localConfig,
  });
  const { modelConfig = {}, welcomeText = '你好，请问有什么我可以帮您的嘛？', paramsInPrompt, plugins, groupPrompt, emojiConfig, memoryConfig, portraitConfigs, triggers, aggregateInputConfig, knowledgeConfig, ttsConfig, modelsConfig } = newConfig || {};

  // 调试相关
  const { run: updateExample } = useRequest(
    (params: { description?: string; usingExample?: string }) =>
      AppApi.updateApp(
        {
          description: params.description,
          querySample: params.usingExample,
        },
        app.id,
      ),
    {
      manual: true,
      onSuccess: () => {
        message.success('保存成功');
      },
    },
  );

  // 线上和预发默认开启SSE对话
  const [publishModalVisible, setPublishModalVisible] = useState(false);
  const [chatSettingDrawerVisible, setChatSettingDrawerVisible] = useState(false);
  const [chatSetting, setChatSetting] = useState({ supportSSE: true });

  const useLocalRef = useRef();
  const virtualListModal = useRef(null);

  // 发布渠道
  const [publishChannel, setPublishChannel] = useState();

  useEffect(() => {
    setPublishChannel(setting?.extInfo?.channel);
  }, [setting?.extInfo?.channel]);

  const { data: skills } = useRequest(async () => {
    // !!!配置中心还未有线上数据,暂时用本地数据
    // if (!publishChannel) {
    return {
      availableSkills: [
        "welcomeText",
        "aggregateInputConfig",
        "ttsConfig",
        "portraitConfigs",
        "paramsInPrompt",
        "plugins",
        "triggers",
        "memoryConfig",
        "emojiConfig",
        "knowledgeConfig",
        "outputStrategyConfig",
        "animationConfig"
      ]
    }
    // }
    const res = await ChannelsApi.getChannelSkills({ channel: publishChannel });

    const availableSkillMap = res?.functionList?.reduce((acc, cur) => {
      acc[cur] = true;
      return acc;
    }, {});
    const disabledSkills = Object.keys(skillCardMap)?.filter(skill => !availableSkillMap[skill]);
    return {
      availableSkills: res?.functionList,
      disabledSkills
    }

  }, {
    refreshDeps: [publishChannel]
  });

  // 获取所有渠道
  const { data: allSkills } = useRequest(async () => {
    if (!app?.id) return;
    const res = await ChannelsApi.getChannels({ appId: app.id });
    return res.channels?.reduce((acc, cur) => {
      acc[cur.type] = cur;
      return acc
    }, {})
  }, {
    refreshDeps: [app?.id]
  });

  const currentChannel: any = publishChannel && allSkills?.[publishChannel];

  useEffect(() => {
    if (useLocalRef.current) {
      clearTimeout(useLocalRef.current);
    }
    // @ts-ignore
    useLocalRef.current = setTimeout(() => {
      setUseLocal(hasDraft(config, newConfig));
    }, 500);
    return () => {
      clearTimeout(useLocalRef.current);
      useLocalRef.current = null;
    };
  }, [newConfig, config]);

  const usedUserParams = useMemo(() => {
    return filterParams(JSON.stringify(groupPrompt));
  }, [groupPrompt]);

  // 本地调试的配置
  const debugConfig = {
    ...newConfig,
    ...(modelConfig || {}),
  };

  useEffect(() => {
    const local = getLocalData(localStorageKey);

    setNewConfig({
      ...config,
      ...(local || {}),
    });
  }, [setting.config, settingId, localStorageKey]);

  const onConversationClear = useCallback(() => {
    setConversationKey('' + Date.now());
  }, []);

  const onReset = () => {
    setNewConfig(config);

    saveLocalData(localStorageKey, { time: 0 });
    onConversationClear();
  };

  // 重置
  function showResetModal() {
    confirm({
      title: '确认重置？',
      content: '重置会放弃当前页面的所有修改，恢复至上一次发布的状态',
      onOk() {
        onReset();
      },
      onCancel() { },
    });
  }

  function showPublishModal() {
    setPublishModalVisible(true);
  }

  const items: MenuProps['items'] = [
    {
      label: '线上回滚',
      icon: <ReloadOutlined></ReloadOutlined>,
      key: 'online-rescroll',
    },
  ];

  const configOptItems: MenuProps['items'] = [
    {
      label: '切换配置',
      icon: <ReloadOutlined></ReloadOutlined>,
      key: 'switch-config',
    },
  ];

  function showReloadModal() {
    virtualListModal.current?.show();
    // confirm({
    //   width: 800,
    //   title: (
    //     <span>
    //       选择版本
    //       <span
    //         style={{
    //           fontSize: 12,
    //           fontWeight: 'normal',
    //           color: '#999',
    //           marginLeft: 5,
    //         }}
    //       >
    //         回滚仅对线上生效, 如需覆盖本地请使用切换配置（保存配置按钮-更多）
    //       </span>
    //     </span>
    //   ),
    //   content: <VersionList app={app} settingId={settingId} newValue={newConfig} onItemChange={handleChange}></VersionList>,
    //   onOk() { },
    //   onCancel() { },
    // });
  }

  const handleMenuClick = (item) => {
    if (item.key === 'online-rescroll') {
      showReloadModal();
    }
  };

  const handleSwitchMenuClick = (item) => {
    if (item.key === 'switch-config') {
      setHistoryDrawerVisible(true);
    }
  };

  // 清空本地缓存
  const clearLocalStorage = () => {
    saveLocalData(localStorageKey, { time: 0 });
  };

  function renderConversation(props: { key: string }) {
    const { globalState } = useGlobalState();
    const { user } = globalState;

    const chatBotConfig = {
      type: IAppType.VirtualHuman,
      settingId,
      userId: user?.id,
      workspaceId,
      conversationId: conversationKey,
      groupId: app.groupID,
      paramsInPrompt,
      appId: app.id,
      config: debugConfig,
      supportSSE: chatSetting?.supportSSE,
      ttsConfig: debugConfig?.ttsConfig
    };

    if (mode === 'promptDebug') {
      return <ConversationDebugBot
        // renderDep 对话组件控制了渲染，config不变的时候不会重新渲染，renderDep用来强制渲染
        renderDep={JSON.stringify(chatBotConfig?.config?.groupPrompt)}
        appId={app.id}
        userId={user?.id}
        settingId={settingId}
        showFeedback={false}
        debug
        useTts={newConfig?.ttsConfig?.strategicConfig?.ttsStrategicType === 'RANDOM'}
        host={window.location.host}
        greetings={welcomeText}
        handlers={{
          SYSTEM_SETTING: () => {
            setChatSettingDrawerVisible(true)
          }
        }}
        modelConfig={modelConfig}
        onModelChange={val => handleChange('modelConfig', val)}
        // onConversationsClear={onConversationClear}
        configs={[
          {
            title: <div>当前提示词 <CheckCircleTwoTone twoToneColor="#52c41a" /></div>,
            config: {
              // 不要占用外面的conversationId
              ...omit(chatBotConfig, ['conversationId']),
            }
          },
          {
            title: <Flex>
              <Flex style={{ marginRight: 6 }}>线上提示词</Flex>
              <OnlineGroupPrompt data={config?.groupPrompt} newValue={chatBotConfig.config?.groupPrompt} />
            </Flex>,
            config: {
              // 不要占用外面的conversationId
              ...omit(chatBotConfig, ['conversationId']),
              config: {
                ...chatBotConfig.config,
                groupPrompt: config?.groupPrompt,
              }
            }
          }
        ]}
      />
    }

    return <Flex style={{ height: '100%', width: '100%' }}>
      <ChatBot
        appId={app.id}
        userId={user?.id}
        settingId={settingId}
        showFeedback={false}
        onReset={onConversationClear}
        debug
        useTts={newConfig?.ttsConfig?.strategicConfig?.ttsStrategicType === 'RANDOM'}
        host={window.location.host}
        greetings={welcomeText}
        handlers={{
          SYSTEM_SETTING: () => {
            setChatSettingDrawerVisible(true)
          }
        }}
        config={chatBotConfig}
      ></ChatBot>
    </Flex>
  }

  const getTriggerList = (triggerList) => {
    if (!triggerList) {
      return Promise.resolve([]);
    }
    const ids = triggerList?.map((item) => item.id);
    return AppApi.queryTrigger({ appId: app.id, ids });
  };

  // 切换配置
  const handleChangeConfig = (_newConfig) => {
    // console.log(66, _newConfig)
    if (_newConfig?.triggerList?.length) {
      getTriggerList(_newConfig?.triggerList).then((res) => {
        setNewConfig({
          ..._newConfig,
          triggers: res?.triggerDetailList,
        });
        const newLocal = {
          ..._newConfig,
          triggers: res?.triggerDetailList,
          time: Date.now(),
        };
        saveLocalData(localStorageKey, newLocal);
      });
    } else {
      setNewConfig(_newConfig);
      const newLocal = {
        ..._newConfig,
        time: Date.now(),
      };
      saveLocalData(localStorageKey, newLocal);
    }

    setHistoryDrawerVisible(false);
  };

  const handleChange = (key, value) => {
    setNewConfig({
      ...newConfig,
      [key]: value,
    });

    // 保存到本地
    const newLocal = {
      ...localConfig,
      ...newConfig,

      time: Date.now(),
      [key]: value,
    };
    saveLocalData(localStorageKey, newLocal);
  };

  function handleSave() {
    const finalyConfig = {
      ...debugConfig,
      triggerList: triggers?.map((v) => ({
        triggerId: v?.triggerId,
        id: v?.id,
      })),
    };

    // triggers 不放到config
    delete finalyConfig.triggers;

    AppApi.saveSettingConfig({
      appId: app.id,
      settingId,
      config: finalyConfig,
    }).then((res) => {
      // 保存后要更新远程数据
      updateSetting(res);
      message.success('保存成功');
      clearLocalStorage();
    });
  }


  // 提示词对比调试逻辑
  const [mode, setMode] = useState<'edit' | 'promptDebug'>('edit');

  // 切换功能
  const [activeKey, setActiveKey] = useState<'groupPrompt' | 'skill'>('groupPrompt');

  const onDebugPrompt = () => {
    setMode('promptDebug');
  };

  const onCloseDebugPrompt = () => {
    setMode('edit');
  };

  const onActiveKeyChange = val => {
    setActiveKey(val);
  };

  const skillCardMap = {
    // 开场白
    welcomeText: props => <WelcomeTriggerCard
      appId={app.id}
      settingId={settingId}
      oldValue={config?.triggers}
      value={triggers}
      onChange={(val) => handleChange('triggers', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    //输入设置
    aggregateInputConfig: props => <AggInputCard
      appId={app.id}
      settingId={settingId}
      oldValue={config?.aggregateInputConfig}
      value={aggregateInputConfig}
      onChange={(val) => handleChange('aggregateInputConfig', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}

    />,
    // 语音输出设置
    ttsConfig: props => <TtsCard
      appId={app.id}
      settingId={settingId}
      oldValue={config?.ttsConfig}
      value={ttsConfig}
      onChange={val => handleChange('ttsConfig', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}

    />,
    // 聊天设置
    portraitConfigs: props => <HumanModelCard
      appId={app.id}
      oldValue={config?.portraitConfigs}
      value={portraitConfigs}
      onChange={(val) => handleChange('portraitConfigs', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    // 变量
    paramsInPrompt: props => <ParamsCard
      appId={app.id}
      appType="virtual-human"
      oldValue={config?.paramsInPrompt}
      value={paramsInPrompt}
      onChange={(val) => handleChange('paramsInPrompt', val)}
      notAllowClears={usedUserParams}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    // 插件
    plugins: props => <PluginsCard
      oldValue={config?.plugins}
      value={plugins}
      paramsInPrompt={paramsInPrompt}
      onChange={(val) => handleChange('plugins', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    // 触发器
    triggers: props => <TriggerCard
      appId={app.id}
      settingId={settingId}
      oldValue={config?.triggers}
      value={triggers}
      onChange={(val) => handleChange('triggers', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    // 记忆设置
    memoryConfig: props => <MemoryCard
      oldValue={config?.memoryConfig}
      value={memoryConfig}
      onChange={(val) => handleChange('memoryConfig', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    // 表情包
    emojiConfig: props => <EmojiCard
      appId={app.id}
      value={emojiConfig}
      oldValue={config?.emojiConfig}
      onChange={val => handleChange('emojiConfig', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    animationConfig: props => <AnimationCard
      appId={app.id}
      settingId={settingId}
      oldValue={config?.animationConfig}
      value={newConfig?.animationConfig}
      onChange={val => handleChange('animationConfig', val)}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    // 知识库
    knowledgeConfig: props => <KnowledgeCard
      appId={app.id}
      value={knowledgeConfig}
      oldValue={config?.knowledgeConfig}
      onChange={(val) => handleChange('knowledgeConfig', val)}
      modelConfig={modelConfig}
      expandLocalKey={localExpandStorageKey}
      {...props}
    />,
    // 文本输出设置
    outputStrategyConfig: props =>
      <OutputCard
        appId={app.id}
        settingId={settingId}
        oldValue={config?.outputStrategyConfig || { enable: true }}
        value={newConfig?.outputStrategyConfig || { enable: true }}
        onChange={(val) => handleChange('outputStrategyConfig', val)}
        expandLocalKey={localExpandStorageKey}
        {...props}
      />
  }

  return (
    // <Spin spinning={props.loading} delay={1000}>
    <div style={{ height: 'calc(100vh - 126px)', marginTop: -16, position: 'relative' }}>
      {lock ? (
        <Alert
          style={{ zIndex: 10 }}
          message={
            <span>
              当前应用{' '}
              <a
                href={`http://popo.netease.com/static/html/open_popo.html?ssid=${lock?.username}@corp.netease.com&sstp=0`}
                target="_blank"
                rel="noreferrer"
              >
                {lock?.username}
              </a>{' '}
              正在编辑中，暂时只能查看和调试，如果需要可以联系对方释放资源
            </span>
          }
          banner
        />
      ) : null}
      <LayoutContainer
        innerStyle={{
          display: 'flex',
          width: '100%',
          backgroundColor: '#FBFCFF',
          borderLeft: '1px solid #f0f0f0',
        }}
      >
        <LeftContainer style={{ width: mode === 'edit' ? '68%' : '32%', height: '100%', marginRight: '12px' }}>
          <Flex justify="space-between" align="center" wrap="wrap">
            <TopBar
              onReset={onReset}
              handleChange={handleChange}
              setting={setting}
              oldModelConfig={config?.modelConfig}
              modelConfig={modelConfig}
              settingId={settingId}
              localConfig={localConfig}
              onEdit={onEdit}
              useLocal={useLocal}
              modelsConfig={modelsConfig}
              showModelConfig={mode === 'edit'}
            ></TopBar>
            <Flex>
              {publishChannel && currentChannel && <Tooltip title={`当前渠道：${currentChannel?.name}`}>
                <Tag style={{ cursor: 'pointer' }}>
                  <Flex align='center' justify='start' gap={2}>
                    <img width={14} src={currentChannel?.icon} />
                    {currentChannel?.name}
                  </Flex>
                </Tag>

              </Tooltip>}
              <Flex style={{ fontSize: '12px', color: '#999', userSelect: 'none' }}>
                {/* <Tag>私信</Tag> */}
                {useLocal ? (
                  <Flex>
                    <ConfigDiff
                      isDiff={useLocal}
                      newValue={newConfig}
                      drawerProps={{
                        footer: null,
                        title: '本地草稿',
                      }}
                      trigger={
                        <Tooltip title="查看草稿">
                          <Tag color="blue">本地草稿</Tag>
                        </Tooltip>
                      }
                      onItemChange={handleChange}
                      appId={app.id}
                      settingId={settingId}
                    />
                    <span style={{ paddingRight: '6px' }}>
                      最后保存于：{dayjs(localConfig.time).format('YYYY-MM-DD HH:mm:ss')}
                    </span>
                    <ConfigDiff
                      isDiff={useLocal}
                      newValue={newConfig}
                      okText="确认无误，重置到最新一次保存"
                      drawerProps={{
                        title: '重置',
                      }}
                      onOk={() => onReset()}
                      trigger={
                        <Tooltip title="放弃本地草稿，重置到最新一次保存">
                          <RedoOutlined style={{ color: '#1677ff' }} />
                        </Tooltip>
                      }
                      onItemChange={handleChange}
                      appId={app.id}
                      settingId={settingId}
                    />
                  </Flex>
                ) : (
                  <span>
                    <Tag color="orange">服务器</Tag>
                    最后保存于：{dayjs(setting.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                  </span>
                )}
              </Flex>
            </Flex>
          </Flex>
          {mode === 'promptDebug' && <Tabs
            items={[
              { label: '提示词', key: 'groupPrompt' },
              { label: '技能', key: 'skill' }
            ]}
            activeKey={activeKey}
            onChange={onActiveKeyChange}
            tabBarStyle={{
              margin: 0
            }}
          ></Tabs>}
          <Flex flex={1} style={{ overflow: 'hidden' }}>
            {(mode === 'edit' || (mode === 'promptDebug' && activeKey === 'groupPrompt')) && <Flex
              vertical
              flex={1}
              style={{
                marginRight: mode === 'edit' ? '10px' : 0,
                paddingRight: mode === 'edit' ? '20px' : 0,
                minWidth: '340px',
                borderRight: mode === 'edit' ? '1px solid #eff0f7' : null,
              }}
            >
              <div style={{ padding: '10px 0' }}>
                <Flex justify="space-between">
                  <Space>

                    <Flex>
                      提示词
                      <DiffAlert
                        title="提示词"
                        type="groupPrompt"
                        newValue={groupPrompt}
                        oldValue={config?.groupPrompt}
                        onRedo={() => {
                          handleChange('groupPrompt', config?.groupPrompt);
                        }}
                      />
                    </Flex>
                    <Tooltip title={<Flex gap={8} vertical>
                      <div>
                        <div>
                          条件提示词是 LangBase 针对虚拟人业务场景独创的，它将条件和提示词关联起来，可以通过不同的条件决定展示不同的提示词。
                        </div>
                        <div>
                          在很多场景都有应用，例如：在特殊节假日需要让虚拟人有不同的表现，在活动期间需要虚拟人配合活动输出一些特殊文案，需要让虚拟人在跟用户不同亲密度的情况下有不同的性格表现……
                        </div>
                      </div>
                      <Image
                        src="https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59437156643/e95b/6890/41ad/27afd5151281b93b3504a2ef4375d658.png"
                      />
                      <a
                        rel="noopener"
                        target='_blank'
                        href='https://music-doc.st.netease.com/st/langbase-doc/update#%E6%9D%A1%E4%BB%B6%E6%8F%90%E7%A4%BA%E8%AF%8D'>查看详情</a>
                    </Flex>}>
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>

                  <Flex>
                    {mode === 'edit' && useLocal &&
                      <Flex>
                        <Tooltip title="提示词对比调试">
                          <Button
                            type='text'
                            size='small'
                            icon={<SplitCellsOutlined />} onClick={onDebugPrompt}></Button>
                        </Tooltip>
                      </Flex>
                    }
                    <ViewPrompt
                      config={newConfig}
                      appId={app.id}
                      userId={user?.id}
                      settingId={settingId}
                      paramsInPrompt={paramsInPrompt}
                      trigger={<Button icon={<EyeIcon />} type='text' size='small'></Button>}
                    />
                    {mode === 'edit' && <Flex>
                      <PromptOptimize
                        workspaceId={workspaceId}
                        groupId={app.groupID}
                        appId={app.id}
                        settingId={settingId}
                        groupPrompt={groupPrompt}
                        onGroupPromptChange={val => handleChange('groupPrompt', val)}
                        app={app}
                        setting={setting}
                      />
                    </Flex>}
                  </Flex>
                </Flex>
              </div>
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <GroupsPrompt
                  paramsInPrompt={paramsInPrompt}
                  value={groupPrompt}
                  onChange={(val) => handleChange('groupPrompt', val)}
                  addNewParams={
                    <AddNewParams
                      appId={app.id}
                      appType="virtual-human"
                      value={paramsInPrompt}
                      onChange={(val) => handleChange('paramsInPrompt', val)}
                      notAllowClears={usedUserParams}
                    />
                  }
                />
              </div>
            </Flex>}
            {(mode === 'edit' || (mode === 'promptDebug' && activeKey === 'skill')) && <Flex
              justify="space-between"
              wrap="wrap"
              style={{
                width: mode === 'edit' ? 460 : '100%',
                overflow: 'scroll',
              }}
            >
              <Flex vertical style={{ width: '100%', marginBottom: '10px' }}>

                {skills?.availableSkills?.map(skill => {
                  const SkillComp = skillCardMap?.[skill];
                  return <SkillComp />
                })}

                {skills?.disabledSkills?.length > 0 && <Alert style={{ marginTop: '10px' }}
                  type="warning" message={
                    <Flex align='center'>以下功能在
                      <Tag>
                        <Flex align='center' justify='start' gap={2}>
                          <img width={14} src={currentChannel?.icon} />
                          {currentChannel?.name}
                        </Flex>
                      </Tag>
                      渠道暂不可用</Flex>
                  } />}
                {skills?.disabledSkills?.map(skill => {
                  const SkillComp = skillCardMap?.[skill];
                  return <SkillComp disabled={true} />
                })}
              </Flex>
            </Flex>}
          </Flex>
        </LeftContainer>

        <RightContainer style={{
          width: mode === 'edit' ? '32%' : '68%'
        }}>
          <Flex
            wrap='wrap'
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <div>
              {mode === 'edit' && <h3>调试与预览</h3>}
              {mode === 'promptDebug' && <h3>提示词对比调试</h3>}
              {/* <ViewPrompt config={newConfig} appId={app.id} /> */}
            </div>
            {mode === 'edit' &&
              <Space>
                <ConfigDiff
                  isDiff={useLocal}
                  newValue={config}
                  okText="确认无误，重置到最新一次保存"
                  drawerProps={{
                    title: '重置',
                  }}
                  trigger={
                    <Button icon={<RedoOutlined />} disabled={!useLocal}>
                      重置
                    </Button>
                  }
                  onOk={showResetModal}
                  onItemChange={handleChange}
                  appId={app.id}
                  settingId={settingId}
                />

                <Dropdown.Button
                  icon={<MoreOutlined />}
                  menu={{ items: configOptItems, onClick: handleSwitchMenuClick }}
                  type="primary"
                >
                  <ConfigDiff
                    isDiff={useLocal}
                    newValue={newConfig}
                    onOk={handleSave}
                    drawerProps={{
                      title: '保存配置',
                    }}
                    trigger={<Flex
                    >保存配置</Flex>}
                    onItemChange={handleChange}
                    appId={app.id}
                    settingId={settingId}
                    beforeOpen={async (e) => {
                      const list = await inValidSkills({
                        channel: publishChannel, config: newConfig, skills: skills?.availableSkills, oldConfig: config
                      });
                      if (list?.length) {
                        return new Promise((resolve, reject) => {
                          Modal.confirm({
                            title: '无效配置确认',
                            content: <div>
                              {currentChannel?.name}渠道下，{list?.map(item => <Tag key={item} color='warning'>{item}</Tag>)}无效，保存后不会生效。</div>,
                            onOk: () => {
                              resolve(true);
                            },
                            onCancel: () => {
                              reject();
                            }
                          });
                        })
                      }
                      return true
                    }}
                  />
                </Dropdown.Button>

                {/* <ConfigDiff
                isDiff={useLocal}
                oldValue={config}
                newValue={newConfig}
                onOk={handleSave}
                drawerProps={{
                  title: '保存配置'
                }}
                trigger={<Button type="primary" disabled={!useLocal}>
                  保存配置
                </Button>}
              /> */}
                {/* {useLocal ?  */}
                <Dropdown.Button
                  icon={<MoreOutlined />}
                  menu={{ items, onClick: handleMenuClick }}
                  type="primary"
                >
                  <ConfigDiff
                    isDiff={useLocal}
                    newValue={newConfig}
                    onOk={() => {
                      showPublishModal();
                    }}
                    drawerProps={{
                      title: '发布',
                    }}
                    okText={'确认无误，发布'}
                    trigger={
                      <Flex>
                        <RocketOutlined style={{ marginRight: 6 }}></RocketOutlined>
                        发布
                      </Flex>
                    }
                    onItemChange={handleChange}
                    appId={app.id}
                    settingId={settingId}
                    beforeOpen={async (e) => {
                      const list = await inValidSkills({
                        channel: publishChannel, config: newConfig, skills: skills?.availableSkills, oldConfig: config
                      });
                      if (list?.length) {
                        return new Promise((resolve, reject) => {
                          Modal.confirm({
                            title: '无效配置确认',
                            content: <div>

                              {currentChannel?.name}渠道下，{list?.map(item => <Tag key={item} color='warning'>{item}</Tag>)}无效，发布后不会生效。</div>,
                            onOk: () => {
                              resolve(true);
                            },
                            onCancel: () => {
                              reject();
                            }
                          });
                        })
                      }
                      return true
                    }}
                  />
                </Dropdown.Button>
                {/* : <Button onClick={showReloadModal}>线上回滚</Button>} */}
                <VirtualHumanVersionListModal ref={virtualListModal} app={app} settingId={settingId} newValue={newConfig} onItemChange={handleChange} />
              </Space>
            }

            {
              mode === 'promptDebug' && <Button type="primary" onClick={onCloseDebugPrompt}>完成对比</Button>
            }
          </Flex>
          <div style={{ flex: 1, overflow: 'hidden', marginBottom: '10px' }}>
            {renderConversation({ key: conversationKey })}
          </div>
        </RightContainer>
        <PublishDrawer
          data={{
            setting,
            settingId,
            appId: app.id,
            app: app,
            config: {
              ...debugConfig,
              triggerList: triggers?.map((v) => ({
                triggerId: v?.triggerId,
                id: v?.id,
              })),
            },
          }}
          visible={publishModalVisible}
          onOk={(res) => {
            // 发布完成后更新setting数据
            updateSetting(res);
            clearLocalStorage();
            setPublishModalVisible(false);
          }}
          onClose={() => {
            setPublishModalVisible(false);
          }}
        />
        <ChatSettingDrawer
          value={chatSetting}
          visible={chatSettingDrawerVisible}
          onChange={(values) => {
            setChatSetting(values);
          }}
          onClose={() => {
            setChatSettingDrawerVisible(false);
          }}
        />
        <ConfigHistoryDrawer
          appId={app.id}
          settingId={settingId}
          visible={historyDrawerVisible}
          onConfigSwitch={(data) => handleChangeConfig(data?.config)}
          onClose={() => setHistoryDrawerVisible(false)}
        />
      </LayoutContainer>
      {
        lock ? (
          <Mask
            onClick={(ev) => {
              // 禁止穿透
              ev.stopPropagation();
              ev.preventDefault();
            }}
          ></Mask>
        ) : null
      }
      {/* <EmojiEditModal /> */}
    </div >
    // </Spin>
  );
}

const Mask = styled.div`
  position: absolute;
  user-select: none;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100% + 35px);
  pointer-events: all;
  background: rgb(255 255 255 / 50%);
`;

const LeftContainer = styled.div`
  display: flex;
  flex-direction: column;

  border-radius: 8px;
  box-sizing: border-box;
  padding-left: 20px;

  .ant-card-head {
    background: #fff;
  }

  .ant-card-body {
    background: #fff;
  }

  textarea {
    background: #f5f8fc !important;
  }
`;

const RightContainer = styled(Card)`
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  /* background: #fafafa; */
  //background-color: rgba(0, 0, 0, 0.02);
  background-color: #fff;
  border-radius: 0;

  .ant-card-body {
    /* height: calc(100vh - 145px); */
    height: 100%;
    padding: 0 20px;
    /* background: #fafafa; */
    //background-color: rgba(0, 0, 0, 0.02);
    background-color: #fff;
    display: flex;
    flex-direction: column;
  }
`;
