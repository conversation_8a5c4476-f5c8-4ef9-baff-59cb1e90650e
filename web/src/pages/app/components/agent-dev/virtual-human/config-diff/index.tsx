import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Flex, Typo<PERSON> } from 'antd';
import ReactDiffViewer from 'react-diff-viewer';
import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { singleConfigDiff } from './get-diff-str';
import { pick } from 'lodash';
import { RedoOutlined } from '@ant-design/icons';
import { MultipleConfigApi } from '@/api/multiple-setting';
import { initRemoteSettingValue } from '../utils';
import { hasDraft } from './utils';
import { AppApi } from '@/api/app';
import { initRemoteAppConfig } from '../../utils';


const styles = {
  gutter: {
    minWidth: '34px'
  },
};

const DiffContainer = styled.div`
  flex:1;
  font-size: 12px;
  height: 100%;
  overflow: scroll;
  scrollbar-width: none;
  .diff-content {
    min-height: 200%;
  }
  pre {
    line-height: 1.4 !important;
  }
`;

const ConfigDiff = (props) => {
  const { onOk: onParentOk, trigger, isDiff,
    okText, newValue, drawerProps, onItemChange,
    appId, settingId, appType = 'virtual-human',
    leftTitle,
    getOldValue, beforeOpen
  } = props;
  const [diffOpen, setDiffOpen] = useState(false);
  const [items, setItems] = useState([]);
  const [oldValue, setOldValue] = useState<any>();
  const diff = useRef<Record<string, { oldValue: string, newValue: string }>>({});

  const getItems = () => {
    if (!oldValue || !newValue) return;
    const items = [];

    Object.keys(singleConfigDiff)?.forEach(key => {
      const item = singleConfigDiff[key];
      const oldValueStr = item.getDiffStr?.(oldValue[key]);
      const newValueStr = item.getDiffStr?.(newValue[key]);

      if (oldValueStr !== newValueStr) {
        items.push({
          title: item.name,
          key,
          href: `#${key}`
        })
      }
    });
    setItems(items);
    return items;
  };

  useEffect(() => {
    if (diffOpen) {
      getItems();
    }
  }, [oldValue, newValue, diffOpen]);

  const onClose = () => {
    setDiffOpen(false);
  };

  const onOk = () => {
    onParentOk?.();
    setDiffOpen(false);
  };

  const getTriggerList = (triggerList) => {
    if (!triggerList) {
      return Promise.resolve([]);
    }
    const ids = triggerList?.map(item => item.id);
    return AppApi.queryTrigger({ appId, ids })
  }

  const getRemoteValue = async () => {
    let res;
    if (getOldValue) {
      res = await getOldValue();
      // setOldValue(val);
      // return val;
    } else {
      if (appType === 'virtual-human') {
        res = await MultipleConfigApi.getSettingConfig({
          appId,
          settingId
        });
      } else {
        res = await AppApi.getAppDetail(appId);
      }
    }

    // 虚拟人配置
    if (appType === 'virtual-human') {
      // const res = await MultipleConfigApi.getSettingConfig({
      //   appId,
      //   settingId
      // });
      const remote = initRemoteSettingValue(res?.config || {});
      if (remote?.triggerList?.length) {
        const triggers = await getTriggerList(remote?.triggerList)
        const triggerDetailList = triggers.triggerDetailList
        remote.triggers = triggerDetailList
        setOldValue(remote);
        return remote;
      } else {
        setOldValue(remote);
        return remote;
      }
    } else {
      // 其他类型是应用
      // if (parentOldValue) {
      //   setOldValue(parentOldValue);
      //   return parentOldValue;
      // }
      // const res = await AppApi.getAppDetail(appId);
      const remote = initRemoteAppConfig(res?.config || {}, res);
      setOldValue(remote);
      return remote;
    }
  };

  const showDiff = async () => {
    const next = beforeOpen && await beforeOpen();
    if (beforeOpen && !next) return false;
    
    // showDiff前强制请求一次远程数据，防止页面上的远程数据是过期数据
    const oldConfig = await getRemoteValue();

    const hasDiff = hasDraft(oldConfig, newValue);

    if (!isDiff && !hasDiff) {
      onOk();
      return;
    }
    getItems();
    setDiffOpen(true);
  };

  const show = key => {
    return items?.findIndex(item => item.key === key) > -1;
  };

  const onRedo = (key, value) => {
    if (key === 'prePrompt') {
      onItemChange?.('newPrompt', oldValue['newPrompt']);
    } else {
      onItemChange?.(key, value);
    }
  };

  return <div>
    <span onClick={showDiff} style={{ cursor: 'pointer' }}>{trigger}</span>
    <Drawer
      title="修改"
      open={diffOpen}
      width={1000}
      onClose={onClose}
      footer={<Flex justify='end' gap={12}>
        <Button onClick={onClose}>取消</Button>
        <Button type="primary" onClick={onOk}>{okText ?? '确认无误，保存配置'}</Button>
      </Flex>}
      {...drawerProps}
    >
      <Flex style={{ height: '100%' }}>
        <DiffContainer id="diff-container">
          <div className="diff-content">
            {Object.keys(singleConfigDiff)?.map(key => {
              const isShow = show(key);
              if (isShow) {
                const item = singleConfigDiff[key];
                const oldValueStr = item.getDiffStr?.(oldValue[key]);
                const newValueStr = item.getDiffStr?.(newValue[key]);
                diff.current[item.name] = {
                  oldValue: oldValueStr,
                  newValue: newValueStr
                }
                return <div id={key} key={key}>
                  <Typography.Title level={5}>{item.name}</Typography.Title>
                  <ReactDiffViewer
                    oldValue={oldValueStr}
                    newValue={newValueStr}
                    styles={styles}
                    leftTitle={<span style={{ fontSize: '12px' }}>
                      {leftTitle || '服务器版本'}{' '}
                      <a onClick={() => onRedo(key, oldValue[key])}>
                        <RedoOutlined />
                        使用
                      </a>
                    </span>}
                    rightTitle={<span style={{ fontSize: '12px' }}>当前草稿</span>}
                  />
                </div>
              }
            })?.filter(item => item)}
          </div>
        </DiffContainer>
        <Anchor
          style={{ marginLeft: '30px' }}
          items={items}
        ></Anchor>
      </Flex>
    </Drawer>
  </div>
};

export default ConfigDiff;
