import { memoryStr } from '../../mods/memory-card';
import { emojiStr } from '../../mods/emoji-card';
import { aggInputStr } from '../../mods/agg-input-card';
import { getKnowledgeConfigStr } from '../../mods/knowledge-card';
import { outputSplitStr } from '../../mods/ouput-card';
import { ttsStr } from '../../mods/tts-card';
import { animationStr } from '../../mods/animation-card';
import { modelsConfigStr } from '@/components/MultipleModelSetting';

// TODO 下面的可以酌情把相关内容放到对应的文件中

const paramsStr = (value) => {
  let res = '';
  value?.forEach((item) => {
    res += `
【变量名称】: ${item.title || ''},
【变量KEY】: ${item.key || ''},
【默认值】: ${item.default_val || ''}

    `;
  });
  return res;
};

const modelConfigStr = (value) => {
  if (!value) return '';
  const res = `
【语言模型】:${value.modelName},
【模型设置】:${JSON.stringify(value.modelParams || {}, null, 2)},
  `;
  return res;
};
const knowledgeStr = (value) => {
  if (!value) return '';
  let res = '';
  res += `
【知识库】: ${value.name},
【文档】: ${value.docs?.map((doc) => doc.title || '').join('，') || ''}

    `;
  return res;
};

const groupPromptStr = (value) => {
  let res = '';
  value?.groups?.forEach((group) => {
    const filters = group.structPrompt
      ? JSON.stringify(
          group.structPrompt?.map((item) => {
            const { id, ...rest } = item;
            return rest;
          }) || [],
          null,
          2,
        )
      : '';

    res += `
【名称】: ${group.name || ''}
【提示词】: ${group.prompt || ''}
【类型】: ${group.promptType || ''}
【条件】: ${filters}

`;
  });
  return res;
};

const pluginsStr = (value) => {
  let res = '';
  value?.forEach((item) => {
    if (!item) return;
    const defaultValue = Object.keys(item.function?.parameters?.properties || {})?.reduce(
      (acc, key) => {
        acc[key] = item.function?.parameters?.properties[key].value || '';
        return acc;
      },
      {},
    );

    const filters = item.filter ? JSON.stringify(item.filter || {}, null, 2) : '';

    res += `
【插件】: ${item.name},
【默认值】: ${JSON.stringify(defaultValue, null, 2)},
【触发条件】: ${filters}


    `;
  });
  return res;
};

const triggersStr = (value) => {
  if (!value || !Array.isArray(value)) return '';

  let res = '';
  value.forEach((trigger, index) => {
    if (!trigger) return;

    res += `
【触发器 ${index + 1}】
名称: ${trigger.name || ''}
类型: ${trigger.type === 'schedule' ? '定时触发' : '事件触发'}
${
  trigger.type === 'schedule'
    ? `
触发时间: ${trigger.cron || ''}
触发周期: ${trigger.startTime ? `${trigger.startTime} 至 ${trigger.endTime}` : '未设置'}
`
    : ''
}
${
  trigger.type === 'event'
    ? `
模式: ${trigger.mode || ''}
URL: ${trigger.url || ''}
Token: ${trigger.token ? '已设置' : '未设置'}
请求参数: ${JSON.stringify(trigger.params || [], null, 2)}
`
    : ''
}
执行任务: ${trigger.actionType === 'recall' ? '召回' : trigger.actionType || ''}
内容来源: ${trigger.config?.contentType === 'preset' ? '预设内容' : 'AI文本'}
${
  trigger.config?.contentType === 'CONFIGURED_MSG'
    ? `
预设内容: ${JSON.stringify(trigger.config.fixedContent?.groups || [], null, 2)}
`
    : ''
}
${
  trigger.config?.contentType === 'AI_MSG'
    ? `
AI提示词: ${trigger.config.aiContent?.prompt || ''}
上下文窗口: ${trigger.config.aiContent?.maxChatTurn || ''}
`
    : ''
}
人群包设置: ${trigger.config?.userSourceInfo?.source === 'upload' ? '文件上传' : '人群包'}
${
  trigger.config?.userSourceInfo?.source === 'circle'
    ? `
人群ID: ${trigger.config?.userSourceInfo?.circleInfo?.circleId || ''}
`
    : ''
}
${
  trigger.config?.userSourceInfo?.source === 'upload'
    ? `
上传文件: ${trigger.config?.userSourceInfo?.uploadFileInfo?.fileName || '未上传'}
`
    : ''
}
人群筛选: ${
      JSON.stringify(trigger.config?.userSourceInfo?.circleInfo?.offlineFilter) || ''
    }
精细化筛选:
${
  trigger.config?.userSourceInfo?.onlineOptions
    ?.map((option) => `- ${option.optionType}: ${option.exist ? '是' : '否'}`)
    .join('\n') || '未设置'
}

`;
  });
  return res;
};

const portraitStr = (value) => {
  let res = '';
  value?.forEach((item) => {
    if (!item) return;
    const defaultValue = Object.keys(item.function?.parameters?.properties || {})?.reduce(
      (acc, key) => {
        acc[key] = item.function?.parameters?.properties[key].value || '';
        return acc;
      },
      {},
    );

    const filters = item.filter ? JSON.stringify(item.filter || {}, null, 2) : '';

    res += `
【人物形象】: ${item.url}


    `;
  });
  return res;
};

const promptStr = (value) => {
  return (value || '') + '\b';
};

const welcomeTextStr = (value) => {
  return (value || '') + '\b';
};

export default {
  paramsStr,
  modelConfigStr,
  pluginsStr,
  groupPromptStr,
  knowledgeStr,
  promptStr,
  memoryStr,
  portraitStr,
  ttsStr,
  emojiStr,
  triggersStr,
  welcomeTextStr,
};

export const singleConfigDiff = {
  modelConfig: {
    name: '模型及参数',
    getDiffStr: modelConfigStr,
  },
  modelsConfig: {
    name: '多模型及参数',
    getDiffStr: modelsConfigStr

  },
  groupPrompt: {
    name: '条件提示词',
    getDiffStr: groupPromptStr,
  },
  prePrompt: {
    name: '提示词',
    getDiffStr: promptStr,
  },
  // newPrompt: {
  //   name: "提示词",
  //   getDiffStr: newPromptStr
  // },
  welcomeText: {
    name: '开场白',
    getDiffStr: welcomeTextStr,
  },
  aggregateInputConfig: {
    name: '输入设置',
    getDiffStr: aggInputStr,
  },
  outputStrategyConfig: {
    name: '文本输出设置',
    getDiffStr: outputSplitStr,
  },
  ttsConfig: {
    name: '语音设置',
    getDiffStr: ttsStr,
  },
  portraitConfigs: {
    name: '人物肖像',
    getDiffStr: portraitStr,
  },
  paramsInPrompt: {
    name: '变量',
    getDiffStr: paramsStr,
  },
  plugins: {
    name: '插件',
    getDiffStr: pluginsStr,
  },
  memoryConfig: {
    name: '记忆',
    getDiffStr: memoryStr,
  },
  emojiConfig: {
    name: '表情包',
    getDiffStr: emojiStr,
  },
  knowledge: {
    name: '知识库',
    getDiffStr: knowledgeStr,
  },
  triggers: {
    name: '触发器',
    getDiffStr: triggersStr,
  },
  knowledgeConfig: {
    name: '知识库',
    getDiffStr: getKnowledgeConfigStr,
  },
  animationConfig: {
    name: '动画设置',
    getDiffStr: animationStr,
  },
};
