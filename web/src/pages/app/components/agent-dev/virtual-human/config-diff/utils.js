import { singleConfigDiff } from './get-diff-str';


export const hasDraft = (remoteConfig, currentConfig) => {
  let hasDraft = false;

  if (!remoteConfig || !currentConfig) return;

  const diffItems = Object.keys(singleConfigDiff);

  for (let i = 0; i < diffItems.length; i++) {
    const key = diffItems[i];
    const item = singleConfigDiff[key];
    const oldValueStr = item.getDiffStr?.(remoteConfig[key]);
    const newValueStr = item.getDiffStr?.(currentConfig[key]);

    if (oldValueStr !== newValueStr) {
      hasDraft = true;
      break;
    }
  }

  return hasDraft;
};
