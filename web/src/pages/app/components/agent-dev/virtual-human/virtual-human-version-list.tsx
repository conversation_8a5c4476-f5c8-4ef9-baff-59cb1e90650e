import { AppApi } from "@/api/app";
import { useRequest } from "ahooks";
import { Button, message, Space, Table, Tag } from "antd";
import dayjs from "dayjs";
import { useState } from "react";
import ConfigDiff from "./config-diff";

const VirtualHumanVersionList = ({ app, settingId, newValue, onItemChange }) => {
  const [curId, setId] = useState(app.app_config_id)
  const pageSize = 5;
  const [pageNumber, setPage] = useState(1);
  const { data, run: getHistory } = useRequest(() => AppApi.publishHistory({ appId: app.id, settingId }), {
    refreshDeps: [pageNumber]
  });

  const handleReload = publishHistoryId => {
    AppApi.publishRollback({ appId: app.id, publishHistoryId, settingId }).then((res) => {
      getHistory()
      message.success('回滚成功')
    })
  }

  const columns = [
    {
      title: '版本号',
      dataIndex: 'publishHistoryId',
      render: v => v ?? '-'
    },
    {
      title: '描述',
      dataIndex: 'description',
      render: v => v ?? '-'
    },
    {
      title: '发布时间',
      dataIndex: 'createTime',
      render: v => {
        return v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '-';
      }
    }, {
      title: '操作',
      dataIndex: 'publishHistoryId',
      render: (publishHistoryId, record, index) => {
        console.log('recordrecordrecord', record);
        if (index === 0) {
          return <Tag color="success">当前版本</Tag>;
        }
        return <Space>
          <ConfigDiff
            isDiff
            newValue={newValue}
            trigger={
              <Button type="link" size='small'>查看对比</Button>
            }
            appType={app.type}
            appId={app.id}
            drawerProps={{
              title: '查看对比'
            }}
            okText="回滚"
            onOk={() => {
              handleReload(publishHistoryId);
            }}
            onItemChange={onItemChange}
            leftTitle={`版本号 ${record.publishHistoryId}`}
            settingId={settingId}
            configId={record.configId}
            getOldValue={async () => {
              const res = await AppApi.getAppConfig(record.configId);
              return res;
            }}
          />
          <Button type="link" onClick={() => handleReload(publishHistoryId)}>回滚</Button>
        </Space>
      }
    }];


  return <Table dataSource={data?.values ?? data?.items ?? []} columns={columns} pagination={{ pageSize: pageSize, total: data?.total || 0, current: pageNumber, onChange: setPage }} />
}


export default VirtualHumanVersionList;