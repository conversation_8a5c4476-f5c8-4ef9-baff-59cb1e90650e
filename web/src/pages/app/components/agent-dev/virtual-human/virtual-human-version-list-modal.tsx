import { Modal } from "antd";
import { forwardRef, useImperativeHandle, useState } from "react";
import VirtualHumanVersionList from "./virtual-human-version-list";

const VersionListModal = (props, ref) => {
  const [open, setOpen] = useState(false);

  useImperativeHandle(ref, () => ({
    show: () => {
      setOpen(true);
    }
  }));

  const onCancel = () => {
    setOpen(false);
  };

  return <>
    <Modal
      title={
        <span>
          选择版本
          <span
            style={{
              fontSize: 12,
              fontWeight: 'normal',
              color: '#999',
              marginLeft: 5,
            }}
          >
            回滚仅对线上生效, 如需覆盖本地请使用切换配置（保存配置按钮-更多）
          </span>
        </span>
      }
      open={open}
      onCancel={onCancel}>
      <VirtualHumanVersionList {...props} />
    </Modal>
  </>
};

export default forwardRef(VersionListModal);
