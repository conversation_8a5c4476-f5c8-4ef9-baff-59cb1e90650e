import { IAppType } from "@/interface";
import { getDefaultConfig } from "@/utils/model-helper";
import { omit, pick } from "lodash";

export const initRemoteSettingValue = (config: any) => {
  const defaultVirtualConfig:any = getDefaultConfig(IAppType.VirtualHuman);

    if (!config) {
      config = {};
    }

    // 触发器获取详情，配置中只保留id

    // if (config.triggerList?.length) {
    //   const ids = config.triggerList?.map(item => item.id);
    //   const triggers = await AppApi.queryTrigger({ ids })
    //   config.triggers = triggers?.triggerDetailList;
    // }

    if (!config.hasOwnProperty('modelName')) {
      config.modelName = defaultVirtualConfig.modelName;
      config.modelParams = defaultVirtualConfig.modelParams;
      config.providerKind = defaultVirtualConfig.providerKind;
    }
    // 本地用了modelConfig字段
    config.modelConfig = pick(config, ['modelName', 'modelParams', 'providerKind']);


    if (!config.hasOwnProperty('groupPrompt')) {
      // 兼容结构化提示词
      if (config.hasOwnProperty('newPrompt')) {
        config.groupPrompt = {
          groups: [{
            name: "个人资料",
            promptType: config.newPrompt?.promptType === 'struct' ? 'struct' : 'raw',
            prompt: config.newPrompt?.prompt,
            structPrompt: config.newPrompt?.promptType === 'struct' ?
              config.newPrompt?.structPrompt?.map(item => {
                return {
                  name: item.title,
                  prompt: item.content,
                  filter: {}
                }
              })
              : []
          }]
        };
      } else {
        // 兼容文本提示词
        if (config.hasOwnProperty('prePrompt')) {
          config.groupPrompt = {
            groups: [{
              name: "个人资料",
              promptType: "raw",
              prompt: config.prePrompt
            }]
          };
        } else {
          config.groupPrompt = { ...defaultVirtualConfig.groupPrompt };
        }
      }
    }
    // if (!config.modelsConfig) {
    //   config.modelsConfig = {
    //     retryConfig: {
    //       retryCount: 0,
    //     },
    //     models: [
    //       {
    //         ratio: 1,
    //         modelName: config.modelName,
    //         modelParams: config.modelParams,
    //         providerKind: config.providerKind,
    //       },
    //     ],
    //   };
    // }
    // drop废弃的key
    const res = omit(config, ['modelName', 'modelParams', 'providerKind', 'newPrompt', 'prePrompt', 'tools']);
    return res;
  };