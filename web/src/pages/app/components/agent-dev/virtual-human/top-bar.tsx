import { Space, Flex } from 'antd';
import { EditFilled } from '@ant-design/icons';
import styled from 'styled-components';
import DiffAlert from '../mods/diff-alert';
import { MultipleModelSetting } from '@/components/MultipleModelSetting';
import { ModelSetting } from '@/components/ModelSetting';

const TopBarContainer = styled.div`
  display: flex;
  align-items: center;
  /* padding-left: 10px; */
  gap: 20px;
  justify-content: space-between;
`;

interface TopBarProps {
  settingId: string;
  setting: any;
  modelConfig: any;
  oldModelConfig: any;
  onEdit: (val: any, type: string, data) => void;
  onReset: () => void;
  handleChange: (key: any, value: any) => void;
  useLocal: boolean;
  localConfig: any;
  modelsConfig?: any;
  showModelConfig?: boolean;
}

export const TopBar: React.FC<TopBarProps> = ({
  settingId,
  modelConfig,
  oldModelConfig,
  setting,
  onReset,
  onEdit,
  handleChange,
  useLocal,
  localConfig,
  // app,
  // prePrompt,
  // paramsInPrompt
  modelsConfig,
  showModelConfig = true
}) => {

  const onModelsChange = models => {
    // val => handleChange('modelsConfig', val)
    handleChange('modelsConfig', models);
  };

  return (
    <TopBarContainer>
      <Space>
        <Space>
          <h3 style={{ marginRight: 10 }}>{`${setting.label || setting.name}`}</h3>
          <EditFilled style={{ fontSize: 14, color: '#1677ff' }} onClick={() => onEdit(settingId, 'edit', {
            ...setting,
          })} />
        </Space>
        {showModelConfig && <Flex>
          <ModelSetting value={modelConfig} onChange={val => handleChange('modelConfig', val)} />
          {/* <MultipleModelSetting value={modelsConfig} onChange={onModelsChange} /> */}
          <DiffAlert type="modelConfig"
            title="模型及参数"
            newValue={modelConfig}
            oldValue={oldModelConfig}
            onRedo={() => {
              handleChange('modelConfig', oldModelConfig);
            }}
          />
        </Flex>}
        {/* <Access
        render={<Button style={{ marginRight: 10 }} type="link" icon={<ApiOutlined></ApiOutlined>}>接入方式</Button>}
        agentConfig={{
          ...modelConfig,
          prePrompt: prePrompt,
          paramsInPrompt,
        }}
        app={app}
      >
      </Access> */}
      </Space>
    </TopBarContainer>
  );
}; 