import { Children, useEffect, useRef, useState } from "react";
import SingleConfig from "./single-setting";
import { Spin, Tabs, Space, Tag, Modal, message, Skeleton } from "antd";
import AddSettingModal from "./add-setting-modal";
import { MultipleConfigApi } from "@/api/multiple-setting";
import { useRequest } from "ahooks";
import styled from 'styled-components';
import { useAdmin } from "@/hooks/useAdmin";
import { getDefaultConfig } from "@/utils/model-helper";
import { IAppType } from "@/interface";

const VirtualBox = styled.div`
  height: 98%;
  margin-top: -12px;
  /* .ant-tabs {
    height: 100%;
  }
  .ant-tabs-content {
    height: 100%;
  }
  .ant-tabs-tabpane-active{
  height: 100%;
  } */
  .ant-tabs-nav-wrap {
    height: 36px;
  }
`

const VirtualHuman = (props) => {
  const { app } = props;

  const isAdmin = useAdmin('workspace');

  const [settingId, setSettingId] = useState();
  const [loading, setLoading] = useState(true);
  const [settingInfo, setSettingInfo] = useState({});
  const [currentSetting, setCurrentSetting] = useState({});
  const [addModalVisible, setAddModalVisible] = useState(false);

  const defaultConfig = getDefaultConfig(IAppType.VirtualHuman);
  // @ts-ignore
  const { groupPrompt, ...otherDefault } = defaultConfig;

  const onEdit = (targetKey, action, data = {}) => {
    setLoading(true);
    if (action === 'add') {
      setCurrentSetting({});
      setAddModalVisible(true);
    };
    if (action === 'edit') {
      const item = (settingList as Array<any>).find(item => item.key === targetKey);
      setCurrentSetting({ ...item, settingId: targetKey, ...data });
      setAddModalVisible(true);
    }
    if (action === 'remove') {
      if ((settingList as Array<any>)?.length <= 1) {
        message.error('至少有一个配置');
        return;
      }
      const preIndex = (settingList as Array<any>).findIndex(item => item.key === targetKey);
      const preSettingId = (settingList as Array<any>)[preIndex - 1]?.key;
      const nextActiveKey = targetKey === settingId ? preSettingId : settingId;
      Modal.confirm({
        title: '确认删除该配置项？',
        onOk: () => {
          deleteSetting({
            appId: app.id,
            targetKey,
            nextActiveKey
          });
        }
      })
    }
  }

  const onloaded = () => {
    setLoading(false)
  }

  const { data: settingList = [] as Array<any>,
    run: getSettingList,
    mutate: setSettingList } = useRequest((params) =>
      MultipleConfigApi.getSettingList({ appId: app.id }).then(res => res?.items?.map(item => {
        const { config, ...others } = item;

        return {
          key: item.settingId,
          label: item.name,
          closable: isAdmin,
          description: item.description,
          children: <SingleConfig
            onloaded={onloaded}
            key={item.settingId}
            settingId={item.settingId}
            info={{
              label: item.name, description: item.description,
              // config: {
              //   ...otherDefault,
              //   ...config,
              // },
              ...others
            }}
            onEdit={onEdit}
          />
        }
      }
      )), {
      onSuccess(data, params) {
        if (params?.[0]?.settingId) {
          const current = (settingList as Array<any>).find(item => item.key === settingId);
          setSettingId(current?.key);
          setSettingInfo(current);
        } else {
          setSettingId(data[0]?.key);
          setSettingInfo(data[0]);
        }
      },
      refreshDeps: [app.id, isAdmin],
    }
    );

  const { run: deleteSetting } = useRequest(({ appId, targetKey, nextActiveKey }) => MultipleConfigApi.removeSetting({
    appId: appId,
    settingId: targetKey
  }), {
    refreshDeps: [],
    manual: true,
    onSuccess: (res, params) => {
      setLoading(false);
      getSettingList({ settingId: params?.[0]?.nextActiveKey })
    }
  }
  )

  const onSettingTabChange = settingId => {
    setLoading(true);
    setSettingId(settingId);
  }

  return <VirtualBox>
    {
      !!(settingList as Array<any>)?.length && <Tabs
        activeKey={settingId}
        type="editable-card"
        tabBarGutter={4}
        destroyInactiveTabPane
        items={settingList as Array<any>}
        onChange={onSettingTabChange}
        onEdit={onEdit}
      ></Tabs>
    }

    <Skeleton active title loading={loading} />

    <AddSettingModal
      visible={addModalVisible}
      data={{
        appId: app.id,
        settingList: (settingList as Array<any>)?.map(
          v => ({
            value: v.key,
            label: <Space>{`使用【${v.label}】配置`}<Tag color="orange">远程</Tag></Space>
          })),
        ...(currentSetting as any)
      }}
      onOk={(newSettingId) => {
        setAddModalVisible(false);
        setLoading(false);
        setSettingId(newSettingId);
        getSettingList({ settingId: newSettingId })
      }}
      onClose={() => {
        setAddModalVisible(false)
        setLoading(false);
      }} />
  </VirtualBox>

};


export default VirtualHuman;
