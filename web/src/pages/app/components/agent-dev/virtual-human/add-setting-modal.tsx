import React, { useEffect, useState } from 'react';
import { Modal, Button, Form, Input, Select } from 'antd';
import { AppApi } from '@/api/app';
import { MultipleConfigApi } from "@/api/multiple-setting";
import { getLocalData, saveLocalData } from '@/utils/common';
import { useRequest } from 'ahooks';
import { ChannelsApi } from '@/api';

const { TextArea } = Input;
const { Option } = Select;
interface SettingsModalProps {
  visible: boolean;
  onClose: () => void;
  onOk: (val) => void;
  data: any;
}
const SettingsModal: React.FC<SettingsModalProps> = ({ visible, onClose, onOk, data }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const { data: channelList } = useRequest(async () => {
    if (!data?.appId) return;
    if (!visible) return;
    const query: any = {
      appId: data?.appId
    }
    if (data.settingId) {
      query.settingId = data.settingId;
    }

    const res = await ChannelsApi.getChannels(query);
    return res?.channels?.map(channel => {
      return {
        label: channel?.name,
        value: channel?.type,
        disabled: !channel?.available
      }
    })
  }, {
    refreshDeps: [data?.appId, data?.settingId, visible]
  });

  useEffect(() => {
    form.resetFields();
    if (data.settingId && visible) {
      form.setFieldsValue({
        name: data.label,
        description: data.description,
        channel: data?.extInfo?.channel
      })
    }
  }, [data, visible]);

  const handleOk = () => {
    // 在这里处理表单提交逻辑
    form.validateFields().then((values) => {
      const params: any = {
        name: values?.name,
        description: values?.description,
        appId: data.appId,
        extInfo: JSON.stringify({
          channel: values?.channel
        }),
      }

      setLoading(true)

      if (data.settingId) {
        params.settingId = data.settingId;
        MultipleConfigApi.updateSetting(params).then((res) => {
          onOk(data.settingId);
        }).finally(() => {
          setLoading(false)
        })
        return;
      }


      MultipleConfigApi.saveSetting(params).then((saveRes) => {
        if (values.copySettingId && values.copySettingId !== 'blank') {
          MultipleConfigApi.getSettingConfig({
            appId: data.appId,
            settingId: values.copySettingId
          }).then(res => {
            AppApi.saveSettingConfig({
              appId: data.appId,
              settingId: saveRes.settingId,
              config: res?.config,
            }).then(() => {
              onOk(saveRes?.settingId);
              return
            }).finally(() => {
              setLoading(false)
            })
          })
        } else {
          onOk(saveRes?.settingId);
        }
      }).finally(() => {
        setLoading(false)
      })
    })

  };

  return (
    <>
      <Modal
        title={data.settingId ? '编辑配置' : '新增配置'}
        open={visible}
        onOk={handleOk}
        onCancel={onClose}
        okText="确认"
        cancelText="取消"
      >
        <Form layout="vertical" form={form}>
          <Form.Item
            name="name"
            label="配置名称"
            rules={[{ required: true, message: '请输入设置名称' }]}
          >
            <Input placeholder="请输入设置名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          // rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={4} placeholder="请输入描述" />
          </Form.Item>
          {
            !data.settingId &&
            <Form.Item
              name="copySettingId"
              label="初始化配置"
            // rules={[{ required: true, message: '请选择拷贝对象（草稿）' }]}
            >
              <Select placeholder="请选择拷贝配置" options={[...data?.settingList, { label: '默认配置', value: 'blank' }]}>

              </Select>
            </Form.Item>
          }
          {/* <Form.Item
            name="channel"
            label="发布渠道"
            help="发布渠道一旦选择不可更改" >
            <Select allowClear options={channelList?.concat([{ label: '暂不选择', value: '' }])} disabled={data?.extInfo?.channel} />
          </Form.Item> */}
        </Form>
      </Modal>
    </>
  );
};

export default SettingsModal;
