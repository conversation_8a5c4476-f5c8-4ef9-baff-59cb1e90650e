import { Drawer, Flex, Space, Tooltip } from "antd";
import GroupsPrompt from "../../virtual-human/groups-prompt";
import { useMemo, useState } from "react";
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import { singleConfigDiff } from "./config-diff/get-diff-str";
import { CloudOutlined } from "@ant-design/icons";


const OnlineGroupPrompt = (props) => {
  const { data, newValue } = props;
  const [open, setOpen] = useState(false);

  const onOpen = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const hasDiff = useMemo(() => {
    const oldValueStr = singleConfigDiff.groupPrompt.getDiffStr(data);
    const newValueStr = singleConfigDiff.groupPrompt.getDiffStr(newValue);
    return oldValueStr !== newValueStr;
  }, [data, newValue]);

  return <>
    <Flex>
      <Tooltip title="查看线上提示词">
        <span onClick={onOpen} style={{ fontWeight: 'normal', color: 'rgba(0,0,0,0.88)', cursor: 'pointer' }}>
          <CloudOutlined />
        </span>
      </Tooltip>
    </Flex>
    <Drawer title="线上提示词"
      open={open}
      onClose={onClose}
      width={'100vw'}
    >
      <div>
        {/* <div style={{ float: 'left', width: hasDiff?'36%':'100%', marginRight: '10px' }}>
          <Flex flex={1} vertical>
            <Flex style={{
              background: "#fafbfc",
              padding: "10px",
              borderBottom: "1px solid #eee",
              lineHeight: '25px'

            }}>线上提示词</Flex>
            <GroupsPrompt value={data} isPreview />
          </Flex>
        </div> */}
        {hasDiff && (
          <div>
            <Flex flex={2}>
              <ReactDiffViewer
                leftTitle={"线上提示词"}
                rightTitle={"当前提示词"}
                oldValue={singleConfigDiff.groupPrompt.getDiffStr(data)}
                newValue={singleConfigDiff.groupPrompt.getDiffStr(newValue)}
              />
            </Flex>
          </div>
        )}
      </div>
    </Drawer>
  </>;
}

export default OnlineGroupPrompt;