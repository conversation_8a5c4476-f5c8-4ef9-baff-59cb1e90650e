import { useState } from "react";
import ParamsEditDrawer from "../../params-card/params-edit-drawer";
import { Button } from "antd";

const AddNewParams = (props) => {
  const { notAllowClears, value, onChange, appId, appType } = props;
  const [open, setOpen] = useState(false);

  const onClose = () => {
    setOpen(false);
  };

  const onOpen = () => {
    setOpen(true);
  }

  return <div>
    <span onClick={onOpen}>
      <Button type="link">没有想要的变量？去添加</Button>
    </span>
    <ParamsEditDrawer
      open={open}
      onClose={onClose}
      notAllowClears={notAllowClears}
      value={value}
      onChange={onChange}
      appId={appId}
      appType={appType}
    />
  </div>
};


export default AddNewParams;
