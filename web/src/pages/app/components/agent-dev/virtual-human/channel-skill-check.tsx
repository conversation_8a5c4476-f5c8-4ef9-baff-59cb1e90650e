import { ChannelsApi } from '@/api';
import { ConfigCardCnMap } from '@/interface';
import { singleConfigDiff } from './config-diff/get-diff-str';

const validateKeys = Object.keys(ConfigCardCnMap);

const getDiffItems = ({ oldValue, newValue }) => {
  if (!oldValue || !newValue) return;
  const items = [];

  Object.keys(singleConfigDiff)?.forEach(key => {
    const item = singleConfigDiff[key];
    const oldValueStr = item.getDiffStr?.(oldValue[key]);
    const newValueStr = item.getDiffStr?.(newValue[key]);

    if (oldValueStr !== newValueStr) {
      // items.push({
      //   title: item.name,
      //   key,
      //   href: `#${key}`
      // })
      items.push(key);
    }
  });
  return items;
}

export const inValidSkills = async ({ channel, skills, config, oldConfig }) => {
  if (!channel) return;

  const diffKeys = getDiffItems({ oldValue: oldConfig, newValue: config });

  if (!diffKeys?.length) return;

  const usedSkills = diffKeys?.map(configKey => {
    const item = config[configKey];
    if (!validateKeys.includes(configKey)) return;
    if (!item) return;
    if (typeof item === 'string' && !item) return;
    if (Array.isArray(item) && item?.length) return configKey;
    if (typeof item === 'object') {
      if (Object.keys(item)?.length) {
        return configKey
      }
    }
  })?.filter(key => key);

  if (channel && usedSkills?.length) {
    let functionList = skills;

    if (!skills?.length) {
      functionList = (await ChannelsApi.getChannelSkills({ channel }))?.functionList;
    }

    const invalidSkills = usedSkills?.reduce((acc, cur) => {
      if (functionList?.includes(cur)) return acc;
      return acc.concat(ConfigCardCnMap[cur] || cur)
    }, []);


    if (invalidSkills?.length) {
      return invalidSkills
    }
  }
}