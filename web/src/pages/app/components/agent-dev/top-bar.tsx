import { Space, Button, Flex } from 'antd';
import { ApiOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import Access from './command/access-drawer';
import DiffAlert from './mods/diff-alert';
import { MultipleModelSetting } from '@/components/MultipleModelSetting';
import { IModelSettingProps } from '@/components/MultipleModelSetting/type';
import { IAppType } from '@/interface';

const TopBarContainer = styled.div`
  /* display: flex; */
  /* align-items: center; */
  /* padding-left: 10px; */
  /* gap: 20px; */
  /* justify-content: space-between; */
`;

interface TopBarProps {
  type: string;
  modelConfig: any;
  oldModelConfig: any;
  onModelChange: (val: any) => void;
  onReset: () => void;
  onPublish: () => void;
  onMenuClick: (item: any) => void;
  items: any[];
  app: any;
  prePrompt: string;
  paramsInPrompt: any[];
  modelsConfig: {
    models: IModelSettingProps[];
    retryConfig?: {
      retryCount?: number;
    };
    type: string;
  };
  oldModelsConfig: {
    models: IModelSettingProps[];
    retryConfig?: {
      retryCount?: number;
    };
  }
  showModelConfig?: boolean;
}

export const TopBar: React.FC<TopBarProps> = ({
  type,
  modelConfig,
  oldModelConfig,
  onModelChange,
  app,
  prePrompt,
  paramsInPrompt,
  modelsConfig,
  oldModelsConfig,
  showModelConfig
}) => {

  const onModelsConfig = val => {

    // 聊天助理最多只选择一个模型
    if (app.type === IAppType.AgentConversation) {
      onModelChange({
        retryConfig: val?.retryConfig,
        models: [{
          ...(val?.models?.[0] || {}),
          ratio: 1
        }]
      });
    } else {
      onModelChange(val);
    }
  };
  return (
    <TopBarContainer>
      <Space>
        <h2 style={{ marginRight: 10 }}>{type}</h2>
        {/* {showModelConfig && app.type !== IAppType.AgentCompletion && <Flex>
          <MultipleModelSetting value={modelsConfig} onChange={onModelsConfig} destroyModal appType={app.type} />
          <DiffAlert type="modelsConfig" title="模型及参数" newValue={modelsConfig} oldValue={oldModelsConfig}
            onRedo={() => {
              onModelChange(oldModelsConfig);
            }}
          />
        </Flex>} */}
        {showModelConfig && <Access
          render={<Button style={{ marginRight: 10 }} type="link" icon={<ApiOutlined />}>接入方式</Button>}
          agentConfig={{
            ...modelConfig,
            ...(modelsConfig?.models?.[0] || {}),
            prePrompt,
            paramsInPrompt,
          }}
          app={app}
        />}
      </Space>
    </TopBarContainer>
  );
}; 