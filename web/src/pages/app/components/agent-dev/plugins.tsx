import { AppApi } from "@/api/app"
import lodash from "lodash"
import { useGlobalState } from "@/hooks/useGlobalState"
import { Pagination, Tool, ToolDetail } from "@/interface"
import { DeleteOutlined, PlusCircleOutlined } from "@ant-design/icons"
import { useRequest } from "ahooks"
import { Button, Card, Col, Descriptions, DescriptionsProps, Drawer, Empty, List, Modal, Pagination as PaginationAnt, Row, Space, Tooltip, Typography, message } from "antd"
import React, { useCallback, useImperativeHandle, useMemo } from "react"
import { useState } from "react"
import { styled } from "styled-components"
import classNames from "classnames"

const { Paragraph } = Typography

const ToolDetailedDescrition = styled(Descriptions)`
  & td.tool-parameters {
    padding: 0px;
  }
`

interface ToolDetailProps {
  tool: Tool
}

const ToolDetailBox = (props: ToolDetailProps) => {
  const { tool } = props

  const items: DescriptionsProps['items'] = [
    {
      key: "1",
      label: "名称",
      children: tool.name,
    },
    {
      key: "2",
      label: "描述",
      children: <text style={{ whiteSpace: 'pre-line' }}>{tool.description_for_llm}</text>,
    },
    {
      key: "3",
      label: "参数",
      className: "tool-parameters",
      children: (
        <div>
          {Object.keys(tool.parameters.properties).map((k) => {
            return (
              <div>
                <div>{tool.parameters.properties[k].title}:</div>
                <div>{tool.parameters.properties[k].description}</div>
              </div>)
          })}
        </div>
      )
    }
  ]

  return <ToolDetailedDescrition title={tool.name} layout="vertical" column={1} bordered items={items} />
}

interface ToolItemToChooseProps {
  selected?: boolean
  tool: Tool
  onClick?: () => void,
}

const ToolItemToChooseCardStyled = styled(Card)`
  &.selected {
      position: relative;
      cursor: default;
  }

  &.selected::before {
    content: "";
    border-radius: 8px;
    background-color: rgba(0,0,0,0.1);
    position: absolute;
    top: 0px;
    bottom: 0px;
    right: 0px;
    left: 0px;
    cursor: default;
  }

  &:hover {
    box-shadow: 6px 6px 16px -8px rgba(0,0,0,0.08);
  }

  .ant-card-head {
    min-height: 40px;
  }
`

const ToolItemToChoose = (props: ToolItemToChooseProps) => {
  const { tool, onClick, selected = false } = props

  const onItemClick = useCallback(() => {
    if (selected) {
      message.warning("已经选择了该Tool")
    }else {
      onClick?.()
    }
  }, [onClick])

  return <ToolItemToChooseCardStyled
    style={{ cursor: 'pointer' }}
    className={classNames({ 'selected': selected })}
    title={tool.name}
    onClick={onItemClick}
  >
    <Paragraph
      ellipsis={{ rows: 3, tooltip: { title: tool.description_for_llm } }}
      style={{ whiteSpace: "pre-wrap" }}>
      {tool.description_for_llm}
    </Paragraph>
  </ToolItemToChooseCardStyled>
}

interface ToolChooseDrawerProps {
  width?: number | string
  onToolChoose?: (tool: Tool) => void
  selectedTooIDs?: string[]
}

interface ToolChooseDrawerRef {
  open: () => void
}

const ToolChooseDrawer = React.forwardRef((props: ToolChooseDrawerProps, ref) => {
  const { width = "50vw", onToolChoose = () => {}, selectedTooIDs = [] } = props
  const [showTools, setShowTools] = useState<boolean>(false)
  const [showInnerTool, setShowInnerTool] = useState<boolean>(false);
  const [pagination, setPagination] = useState<Pagination>({ pageNumber: 1, pageSize: 4 })
  const [tools, setTools] = useState<Tool[]>([])
  const [total, setTotal] = useState<number>(0)
  const [currentTool, setCurrentTool] = useState<Tool>();

  const {globalState} = useGlobalState()

  useRequest(() => AppApi.listTool({ ...pagination, workspaceID:  globalState.workspace?.id}),
    {
      refreshDeps: [pagination.pageNumber, pagination.pageSize],
      ready: !!globalState.workspace?.id,
      onSuccess: (data) => {
        setTools(data.items)
        setTotal(data.total)
      },
    })

  useImperativeHandle(ref, () => ({
    open: () => setShowTools(true)
  }))

  const innerToolContent = useMemo(() => {
    if (currentTool) {
      return <ToolDetailBox tool={currentTool} />
    }
    return <Empty />
  }, [currentTool])

  return <>
    <Drawer
      width={width}
      title="工具查询"
      open={showTools}
      onClose={() => setShowTools(false)}
    >
      <Row gutter={[8, 8]}>
        {tools.map((item) => {
          return (
            <Col xs={24} sm={24} md={24} lg={24} xl={12} xxl={12}>
              <ToolItemToChoose
                selected={selectedTooIDs.includes(item.id)}
                onClick={() => { setCurrentTool(item); setShowInnerTool(true) }}
                key={item.id}
                tool={item}
              />
            </Col>)
        })}
        <Row style={{width: "100%", marginTop: "8px", justifyContent: 'flex-end'}}>
          <PaginationAnt total={total} {...pagination} onChange={(page) => setPagination({ ...pagination, pageNumber: page })} />
        </Row>
      </Row>
    </Drawer>
    <Drawer
      width={width}
      open={showInnerTool}
      onClose={() => setShowInnerTool(false)}
      extra={<Button type="primary" onClick={() => { onToolChoose(currentTool!); setShowInnerTool(false); setShowTools(false) }}>确认添加</Button>}
    >
      {innerToolContent}
    </Drawer>
  </>
});

const Ellipsis = styled.p`
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  margin: 0px;
`

export interface ToolItemProps {
  tool: Tool
  onDelete: () => void,
}

export const ChoosenToolItem = (props: ToolItemProps) => {
  const { tool, onDelete } = props
  const [showDeleteModa, setShowDeleteModal] = useState<boolean>(false)

  return <div style={{ columnGap: '8px', padding: '8px', display: 'flex', border: '1px solid #c8dffc', borderRadius: '8px', backgroundColor: '#e7f1fe', width: "100%" }}>
    <Tooltip title={tool.name}>
      <div style={{ width: "0", flex: '1 1 auto' }}>
        <Ellipsis style={{ fontWeight: '600' }}>
          {tool.name}
        </Ellipsis>
      </div>
    </Tooltip>
    <Tooltip title={tool.description_for_llm}>
      <div style={{ width: "0", flex: "3 1 auto" }}>
        <Ellipsis>
          {tool.description_for_llm}
        </Ellipsis>
      </div>
    </Tooltip>
    <div>
      <DeleteOutlined onClick={() => setShowDeleteModal(true)} style={{ cursor: 'pointer' }} />
    </div>
    <Modal
      open={showDeleteModa}
      onCancel={() => setShowDeleteModal(false)}
      onOk={(e) => { e.stopPropagation(); onDelete() }}
    >
      <div>确定不再使用工具 {tool.name} 吗？</div>
    </Modal>
  </div>
}

export interface ToolsCardProps {
  toolIDs?: Record<string, ToolDetail | null>
  onToolsChanged: (toolIDs: Record<string, ToolDetail | null>) => void
}

export const ToolsCard = (props: ToolsCardProps) => {
  const { toolIDs = {}, onToolsChanged } = props

  const {globalState} = useGlobalState()

  const pageSize = 2
  const [pagination, setPagination] = useState<Pagination>({ pageNumber: 1, pageSize })
  const [tools, setTools] = useState<Tool[]>([])
  const toolDrawerRef = React.useRef<ToolChooseDrawerRef>(null);

  useRequest(() => AppApi.listTool({ ...pagination, toolID: Object.keys(toolIDs), workspaceID: globalState.workspace?.id }),
    {
      refreshDeps: [pagination.pageNumber, pagination.pageSize, toolIDs, globalState.workspace?.id],
      ready: !!globalState.workspace?.id && Object.keys(toolIDs).length > 0,
      onSuccess: (data) => {
        setTools(data.items)
      },
    })

  const openToolsDrawer = () => {
    toolDrawerRef.current?.open()
  };

  const onToolChoose = useCallback((tool: Tool) => {
    if ( tool.id in toolIDs) {
      return
    }

    onToolsChanged({...toolIDs, [tool.id]: null})
  }, [onToolsChanged, toolIDs]);

  const onToolRemoved = useCallback((toolID: string) => {
    if (toolID in toolIDs)
      onToolsChanged(lodash.omit(toolIDs, [toolID]))
  }, [onToolsChanged, toolIDs]);

  return (
    <>
      <ToolCardStyled
        title="工具"
        extra={
          <Space style={{ cursor: 'pointer' }} onClick={openToolsDrawer}>
            <PlusCircleOutlined />
            <div>添加</div>
          </Space>
        }
      >
        {/* <List pagination={{ size: "small", pageSize, total: Object.keys(toolIDs).length, onChange: (page) => setPagination({ ...pagination, pageNumber: page }) }} */}
        <List 
          dataSource={tools}
          renderItem={(item) => <List.Item key={item.id} style={{ borderBlockEnd: 'none' }}><ChoosenToolItem tool={item} onDelete={() => { onToolRemoved(item.id); console.log("delete", item.id) }} /></List.Item>} />
      </ToolCardStyled>
      <ToolChooseDrawer selectedTooIDs={Object.keys(toolIDs)} onToolChoose={onToolChoose} ref={toolDrawerRef} />
    </>
  )
}

export const DocsCard = (props: ToolsCardProps) => {
  const { toolIDs = {}, onToolsChanged } = props

  const {globalState} = useGlobalState()

  const pageSize = 2
  const [pagination, setPagination] = useState<Pagination>({ pageNumber: 1, pageSize })
  const [tools, setTools] = useState<Tool[]>([])
  const toolDrawerRef = React.useRef<ToolChooseDrawerRef>(null);

  useRequest(() => AppApi.listTool({ ...pagination, toolID: Object.keys(toolIDs), workspaceID: globalState.workspace?.id }),
    {
      refreshDeps: [pagination.pageNumber, pagination.pageSize, toolIDs, globalState.workspace?.id],
      ready: !!globalState.workspace?.id && Object.keys(toolIDs).length > 0,
      onSuccess: (data) => {
        setTools(data.items)
      },
    })

  const openToolsDrawer = () => {
    toolDrawerRef.current?.open()
  };

  const onToolChoose = useCallback((tool: Tool) => {
    if ( tool.id in toolIDs) {
      return
    }

    onToolsChanged({...toolIDs, [tool.id]: null})
  }, [onToolsChanged, toolIDs]);

  const onToolRemoved = useCallback((toolID: string) => {
    if (toolID in toolIDs)
      onToolsChanged(lodash.omit(toolIDs, [toolID]))
  }, [onToolsChanged, toolIDs]);

  return (
    <>
      <ToolCardStyled
        title="关联知识库"
        extra={
          <Space style={{ cursor: 'pointer' }} onClick={openToolsDrawer}>
            <PlusCircleOutlined />
            <div>添加</div>
          </Space>
        }
      >
        {/* <List pagination={{ size: "small", pageSize, total: Object.keys(toolIDs).length, onChange: (page) => setPagination({ ...pagination, pageNumber: page }) }} */}
        <List 
          dataSource={tools}
          renderItem={(item) => <List.Item key={item.id} style={{ borderBlockEnd: 'none' }}><ChoosenToolItem tool={item} onDelete={() => { onToolRemoved(item.id); console.log("delete", item.id) }} /></List.Item>} />
      </ToolCardStyled>
      <ToolChooseDrawer selectedTooIDs={Object.keys(toolIDs)} onToolChoose={onToolChoose} ref={toolDrawerRef} />
    </>
  )
}

const ToolCardStyled = styled(Card)`

  .ant-card-head {
    min-height: 40px;
  }

  .ant-card-body {
    max-height: 169px;
  }
  
  .ant-list-item {
    padding: 0px;
    padding-top: 4px;
    border-block-end: none;
  }

  .ant-list-pagination {
    margin-block-start: 8px;
  }
`;
