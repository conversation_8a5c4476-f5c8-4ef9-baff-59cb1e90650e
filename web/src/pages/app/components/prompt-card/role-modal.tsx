import { Form, Input, Modal, <PERSON>lider, But<PERSON>, Flex, message, Radio } from "antd";
import { HighlightOutlined } from '@ant-design/icons';
import { useState } from "react";
import { AgentTemplateApi } from "@/api/agent-template";
import AddTag from './add-tag';
import styled from "styled-components";
import AIBeautify from "./ai-beautify";
import PlaceholderInput from "./placeholder-input";

const StyledForm = styled(Form)`
  .ant-form-item {
    margin-bottom: 12px;
  }
`;

const RoleForm = (props) => {
  const { onChange, loading } = props;
  const [form] = Form.useForm();

  const onSubmit = () => {
    const values = form.getFieldsValue();
    onChange(values);
  };

  const onKeyDown = (e, name, val) => {
    const tabKeyPressed = e.keyCode == 9;
    const currentValue = form.getFieldValue(name);
    if (tabKeyPressed && !currentValue) {
      e.preventDefault();
      form.setFieldValue(
        name, val
      );
    }
  }

  return <Flex vertical style={{ width: '100%' }}>
    <StyledForm
      layout="vertical"
      style={{ width: '100%', flex: 1, overflow: 'scroll' }}
      form={form}
    >
      <Form.Item label="角色信息" name="角色">
        {/* <Input placeholder="你是一个sql专家" onKeyDown={e => onKeyDown(e, '角色', '你是一个sql专家')} /> */}
        <PlaceholderInput placeholder="你是一个sql专家" onKeyDown={e => onKeyDown(e, '角色', '你是一个sql专家')}>
          <Input />
        </PlaceholderInput>
      </Form.Item>
      <Form.Item label="使用场景" name="使用场景">
        {/* <Input placeholder="编程场景" onKeyDown={e => onKeyDown(e, '使用场景', '编程场景')} /> */}
        <PlaceholderInput placeholder="编程场景" onKeyDown={e => onKeyDown(e, '使用场景', '编程场景')}>
          <Input />
        </PlaceholderInput>
      </Form.Item>
      <Form.Item label="工作任务" name="工作任务">
        {/* <Input.TextArea placeholder="帮助用户生成sql" onKeyDown={e => onKeyDown(e, '工作任务', '帮助用户生成sql')} /> */}
        <PlaceholderInput placeholder="帮助用户生成sql" onKeyDown={e => onKeyDown(e, '工作任务', '帮助用户生成sql')}>
          <Input />
        </PlaceholderInput>
      </Form.Item>
      <Form.Item label="人设性格" name="人设性格">
        <AddTag options={['阳光', '开朗', '幽默', '专业', '热情', '认真']} />
      </Form.Item>
      <Form.Item label="人设语气" name="人设语气">
        <AddTag options={['正式', '友善', '友好', '规范', '专业', '搞笑']} />
      </Form.Item>
      <Form.Item label="回答长度" name="回答长度">
        <Radio.Group>
          <Radio value="短">短</Radio>
          <Radio value="适中">适中</Radio>
          <Radio value="长">长</Radio>
        </Radio.Group>
      </Form.Item>
    </StyledForm>
    <Flex justify="end">
      <Button type="primary" onClick={onSubmit} loading={loading}>生成</Button>
    </Flex>
  </Flex>
}

export const RoleModal = (props) => {
  const { onChange: onParentChange, structPrompt } = props;
  const [open, setOpen] = useState(false);
  const [prompt, setPrompt] = useState();
  const [loading, setLoading] = useState(false);

  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
    setPrompt(undefined);
  }

  const onChange = (values) => {

    const subStructPrompt = Object.keys(values || {})?.map(key => {
      let newValue = values[key];

      if (Array.isArray(values[key])) {
        newValue = values[key]?.join(',');
      }
      if (typeof values[key] === 'number') {
        newValue = `${newValue}`;
      }
      if (newValue) {
        return {
          title: key,
          content: newValue
        }
      }
    }).filter(item => !!item);

    if (!subStructPrompt?.length) {
      message.error('角色至少有一项描述');
      return;
    }

    setLoading(true);
    AgentTemplateApi.beautify2text({
      subStructPromptType: 'role',
      subStructPrompt,
      structPrompt
    }).then(res => {
      setPrompt(res.prompt);
      setLoading(false);
    }).catch(err => {
      setLoading(false);
    });
  }

  const onUse = () => {
    onParentChange(prompt);
    setOpen(false);
    setPrompt(undefined);
  }

  return <div>
    <HighlightOutlined onClick={onOpen} />
    <Modal
      title="生成角色提示词"
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <Flex style={{ height: '600px' }}>
        <Flex flex={1} style={{ padding: '0 12px' }}>
          <RoleForm onChange={onChange} loading={loading} />
        </Flex>
        <Flex flex={1} align="center">
          <AIBeautify data={prompt} loading={loading} onUse={onUse} />
        </Flex>
      </Flex>
    </Modal>
  </div>
};
