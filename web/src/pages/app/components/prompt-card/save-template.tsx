import { Button, Select, Checkbox, Flex, Form, Image, Input, Modal, Tooltip } from "antd";
import { SaveOutlined } from '@ant-design/icons';
import { useState } from "react"
import Uploader from "../../workflow/react-node/imageUploader";
import styled from "styled-components";

const Avatars = styled(Flex)`
  .default-avatar {
    padding: 16px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    box-sizing: border-box;
    width: 60px;
    height: 60px;
    position: relative;
    cursor: pointer;
    user-select: none;
    
    &.selected {
      border: 2px solid #1890ff;
    }
  }

  .default-avatar-uploader {
    height: 60px;
  }

  .default-avatar-uploader {
    .ant-upload-wrapper {
      .ant-upload {
        height: 60px !important;
        width: 60px !important;
      }
    }
  }
`;

const defaultAvatarUrl = [
  "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860758994/c4c8/21da/5c7a/dc69dc133974fc34a76f84dabc60c840.png",
  "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860759978/10fa/e0b6/f6ee/47b02777b3d79cae7db718a07e220b3b.png",
  "https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860544305/ceba/1845/78a0/c65f9bc42b3219fe129c60a6bae28f5a.png",
  "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860544316/c1ed/1ef4/e73b/a64ca34dd5520bcdccc84b65d6a9ab49.png",
  "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860757567/634b/2fdd/c59a/437b625423892499a5b6f286a6b97dbe.png",
  "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860545581/5374/28a7/e7ee/09c38e1e662d9d4f0470adcfdc4f6491.png",
  "https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860755942/5239/9dd6/22e0/a04cdddc82e53beaa9d0ac37fc34cbee.png",
  "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860548012/572c/3d42/3fb5/031f33a6dbc72b714b2324c5817b66b9.png",
  "https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860546387/e71f/b64b/18e0/63979f1b0cfdc2676293a6e502650911.png",
  "https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860859006/5a2e/e75d/33f3/9d08dfbed5c9e8c105ce35eb029c0929.png",
  "https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/56860759968/2e07/5066/56e0/215cfe12c80ff6615446a29a020c111e.png"
];

const SaveTemplate = (props) => {
  const { onSave } = props;
  const [open, setOpen] = useState<boolean>();
  const [form] = Form.useForm();

  const onOpen = () => {
    setOpen(true);
    form.setFieldsValue({
      avatarUrl: defaultAvatarUrl[0]
    })
  };

  const onCancel = () => {
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();
    onSave?.(values, () => {
      setOpen(false);
      form.resetFields();
    });
  };

  return <div>
    {/* <Button type="link" onClick={onOpen} style={{ padding: 0 }}>保存模板</Button> */}
    <div>
      <Tooltip title={<Flex vertical gap={8}>
        <Flex>保存为角色模板</Flex>
        <a
          rel="noopener"
          target="_blank"
          href="https://music-doc.st.netease.com/st/langbase-doc/update/2024-12-05#-%E6%94%AF%E6%8C%81%E6%8F%90%E7%A4%BA%E8%AF%8D%E6%A8%A1%E6%9D%BF%E9%A2%84%E8%AE%BE"
        >查看详情</a>
      </Flex>}>
        <SaveOutlined onClick={onOpen} />
      </Tooltip>
    </div>
    <Modal title="保存模板" width={650} onCancel={onCancel} open={open} onOk={onOk} destroyOnClose>
      <Form initialValues={{ scope: 'scoped', avatarUrl: defaultAvatarUrl[0] }} form={form} labelAlign="right" labelCol={{ span: 4 }} wrapperCol={{ span: 16 }}>
        <Form.Item label="头像" name="avatarUrl" required>
          <AvatarSelect />
        </Form.Item>
        <Form.Item label="名称" name="name" required>
          <Input />
        </Form.Item>
        <Form.Item label="描述" name="desc">
          <Input.TextArea />
        </Form.Item>
        <Form.Item label="可访问范围" name="scope">
          <Select options={[
            { label: "业务组", value: 'scoped' },
            { label: "租户", value: 'workspace' },
          ]} />
        </Form.Item>
      </Form>
    </Modal>
  </div >
};

const AvatarSelect = (props) => {
  const { value, onChange } = props;

  const onImgUpload = img => {
    onChange(img?.url);
  };

  const onAvatarChange = (checked, url) => {
    if (checked) {
      onChange(url);
    } else {
      onChange(undefined);
    }
  }

  const isDefaultUrl = defaultAvatarUrl.indexOf(value) > -1;

  return <div>
    <div>
      <Avatars gap={6} wrap="wrap">
        {(isDefaultUrl || !value) && defaultAvatarUrl?.map((url, index) => {
          return <div
            key={index}
            className={`default-avatar ${url === value ? 'selected' : ''}`}
            onClick={() => onAvatarChange(url !== value, url)}>
            <Image
              src={url}
              preview={false} />
          </div>
        })}
        <div className='default-avatar-uploader'>
          <Uploader permanent type={'Image'} onChange={onImgUpload} value={isDefaultUrl ? undefined : value} disabled={false} />
        </div>
      </Avatars>
    </div>
  </div>
};


export default SaveTemplate;
