import { Modal, Spin, Flex, Space, Button, Input } from "antd";
import styled from "styled-components";

const ContentItem = styled.div`
  background-color: #62699914;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  min-height: 400px;
`;

const Struct2PromptModal = (props) => {
  const { open, content, loading, onClose, onOk, onRegen } = props;

  return <div>
    <Modal
      title="使用结构化模式的prompt"
      open={open}
      onCancel={onClose}
      onOk={onOk}
      okText="使用"
      cancelText="重新生成"
      width={800}
      footer={<Flex justify="end">
        <Space>
          <Button onClick={onRegen} disabled={loading}>重新生成</Button>
          <Button type="primary" onClick={onOk} disabled={loading}>使用</Button>
        </Space>
      </Flex>}
    >
      <Spin spinning={loading}>
        <div style={{ minHeight: 400 }}>
          <ContentItem>
            {content}
          </ContentItem>
        </div>
      </Spin>
    </Modal>
  </div>
};


export default Struct2PromptModal;
