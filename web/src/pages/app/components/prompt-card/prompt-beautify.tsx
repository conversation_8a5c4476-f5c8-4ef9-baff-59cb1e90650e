import { Input, Modal, Button, Flex, Empty, Spin, Typography, message, Tooltip } from "antd";
import { HighlightOutlined } from '@ant-design/icons';
import { useState } from "react";
import { AgentTemplateApi } from "@/api/agent-template";
import { PromptContent } from "./style";
import AIBeautify from "./ai-beautify";
import { MagicIcon } from "@/components/icons";
import styled from 'styled-components';

const { Text } = Typography;

const TextInput = (props) => {
  const { onChange, loading, mode } = props;
  const [inputValue, setInputValue] = useState();

  const placeholderText = mode === 'based'
    ? `您希望如何优化提示词？(允许为空，表示直接优化)

例：增加一些用例；我希望输出格式是……`
    : `您希望生成什么样的提示词？
        
例：我想生成一个SQL转化器，能根据我的输入帮我编写可查询的SQL语句...`;

  const onSubmit = () => {
    onChange(inputValue, mode);
  };

  const onTextChange = e => {
    setInputValue(e.target.value);
  }

  return <Flex vertical style={{ width: '100%', height: '100%' }}>
    <Flex
      style={{ width: '100%', flex: 1, overflow: 'scroll', marginBottom: '10px' }}
      flex={1}
      vertical
    >
      <Input.TextArea
        onChange={onTextChange}
        style={{ height: '100%', resize: 'none' }}
        placeholder={placeholderText}
      />
    </Flex>
    <Flex justify="end">
      <Button type="primary" onClick={onSubmit} loading={loading} disabled={!inputValue && mode !== 'based'}>{mode === 'based' ? !inputValue ? '直接优化' : '优化' : '生成'}</Button>
    </Flex>
  </Flex>
}

const PromptBeautify = (props) => {
  const { onChange: onParentChange, prePrompt } = props;
  const [open, setOpen] = useState(false);
  const [prompt, setPrompt] = useState();
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState('based');

  const onOpen = () => {
    const button = document.querySelector('.seesaw-button');
    button?.classList.add('animate');

    // 10秒后移除动画
    setTimeout(() => {
      button?.classList.remove('animate');
    }, 10000);

    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
    setPrompt(undefined);
  }

  const onUse = () => {
    onParentChange(prompt);
    setOpen(false);
    setPrompt(undefined);
  }

  const onChange = (value, mode?: string) => {
    if (mode === 'based') {
      return onBeautify(value);
    }
    if (!value) {
      message.error('请编写提示词');
      return;
    }
    setLoading(true);
    AgentTemplateApi.textConvert2Struct({
      prompt: value
    }).then(res => {
      setPrompt(res.prompt);
      setLoading(false);
    }).catch(() => {
      setLoading(false);
    });
  }

  const onBeautify = (value) => {
    setLoading(true);
    AgentTemplateApi.aiBeautify({
      pre_prompt: prePrompt,
      requirements: value
    }).then(res => {
      setPrompt(res.prompt);
      setLoading(false);
    }).catch(() => {
      setLoading(false);
    });
  }

  return <div>
    {/* <HighlightOutlined onClick={onOpen} /> */}
    <Tooltip title="一键优化提示词">
      <StyledButton
        type="link"
        size="small"
        style={{ padding: 0 }}
        onClick={onOpen}
        className="seesaw-button animate"
      >
        <MagicIcon className="seesaw-icon" />
        <span className="magic-icon seesaw-text">AI优化</span>
      </StyledButton>
    </Tooltip>

    <Modal
      title={`AI生成提示词`}
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <Flex vertical style={{ height: '600px' }}>
        <Flex style={{ marginBottom: '16px' }}>
          <Button.Group>
            <Button
              type={mode === 'new' ? 'primary' : 'default'}
              onClick={() => setMode('new')}
            >
              全新提示词
            </Button>
            <Button
              type={mode === 'based' ? 'primary' : 'default'}
              onClick={() => setMode('based')}
            >
              基于现有提示词
            </Button>
          </Button.Group>
        </Flex>
        <Flex style={{ flex: 1 }}>
          <Flex flex={1} style={{ paddingRight: '12px' }} vertical>
            <TextInput loading={loading} onChange={onChange} mode={mode} />
          </Flex>
          <Flex flex={1} align="center">
            <AIBeautify showDiff={mode === 'based'} data={prompt} onUse={onUse} loading={loading} prePrompt={prePrompt} />
          </Flex>
        </Flex>
      </Flex>
    </Modal>
  </div>
};

const StyledButton = styled(Button)`
  .magic-icon {
    margin-right: 4px;
  }
  position: relative;

  transform-origin: center;

  &.animate {
    animation: seesawLeft 0.8s ease-in-out;
    animation-iteration-count: 3;
  }

  @keyframes seesawLeft {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(-15deg); }
  }

  @keyframes seesawRight {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(15deg); }
  }
`;

export default PromptBeautify;
