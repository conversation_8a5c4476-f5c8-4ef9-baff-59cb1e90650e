import { Tag } from "antd";
import styled from "styled-components";
import React, { useState } from 'react';

const StyledInputWrapper = styled.div`
  position: relative;
`;

const FakePlaceholder = styled.div`
  position: absolute;
  top:  ${props => {
    // @ts-ignore
    if (typeof props.bordered === 'undefined' || props.bordered) {
      return '5px'
    }
    return 0;
  }};
  left: ${props => {
    // @ts-ignore
    if (typeof props.bordered === 'undefined' || props.bordered) {
      return '11px'
    }
    return 0;
  }};
  color: #8d8d9999;
  pointer-events: none;
`;

const TabTag = styled(Tag)`
  color:#8d8d9999;
  border-color: #8d8d9999;
`;

const PlaceholderInput = (props) => {

  const { children, value, onChange, placeholder, bordered, showTab = true, ...rest } = props;

  const newInput = React.cloneElement(children, {
    ...rest,
    value, onChange, bordered,
    placeholder: showTab ? undefined : placeholder,
  })

  return <StyledInputWrapper>
    {newInput}
    {/* @ts-ignore */}
    {(!value && showTab) && <FakePlaceholder bordered={bordered}>
      <pre style={{ margin: 0, whiteSpace: 'pre-line' }}>{placeholder} <TabTag>Tab</TabTag></pre>
    </FakePlaceholder>}
  </StyledInputWrapper>

}

export default PlaceholderInput;
