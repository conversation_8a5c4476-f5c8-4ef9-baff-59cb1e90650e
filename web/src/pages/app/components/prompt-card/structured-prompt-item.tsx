

import { Card, Flex, Input, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { ShrinkOutlined, ArrowsAltOutlined, DeleteOutlined } from '@ant-design/icons';
import { StyldLabel } from './style';
import { RoleModal } from './role-modal';
import TextModal from './text-modal';
import OutputModal from './output-modal';
import PlaceholderInput from './placeholder-input';

const { TextArea } = Input;

const StyledTextArea = styled(TextArea)`
  &.ant-input-disabled {
    color: #333;
  }
`;

const NameInput = (props) => {
  const { value, ...rest } = props;
  const [editing, setEditing] = useState(true);

  useEffect(() => {
    if (value) {
      setEditing(false);
    }
  }, []);

  const onBlur = e => {
    if (value) {
      setEditing(false);
    }
  };

  const onFocus = () => {
    setEditing(true);
  };

  return <div onClick={onFocus}>{editing ?
    <Input value={value}
      placeholder="未命名主题"
      onBlur={onBlur}
      size='small' {...rest} />
    : <StyldLabel>{value}</StyldLabel>}</div>
}

const StructuredPromptItem = (props) => {
  const { value, onChange, onDelete: onParentDelete, structPrompt, isPreview } = props;
  const { type, title, id } = value || {};
  const [textScroll, setTextScroll] = useState(false);

  const onTextScrollChange = () => {
    setTextScroll(!textScroll);
  };

  const onNameChange = (ev) => {
    const newValue = {
      ...value,
      title: ev.target.value
    }
    onChange(newValue);
  };

  const onValueChange = (ev) => {
    const newValue = {
      ...value,
      content: ev.target.value
    }
    onChange(newValue);
  };

  const onDelete = () => {
    onParentDelete(id);
  };

  const onPromptChange = val => {
    const newValue = {
      ...value,
      content: val
    };
    onChange(newValue);
  };

  const onKeyDown = (e, placeholder) => {
    const tabKeyPressed = e.keyCode == 9;
    if (tabKeyPressed && !value.content) {
      e.preventDefault();
      const newValue = {
        ...value,
        content: placeholder
      }
      onChange(newValue);
    }
  }

  const restStructPrompt = structPrompt?.filter(item => item.type !== value.type)

  return <ItemWrapper>
    <div className='item-header'>
      <Flex justify='space-between'>
        <StyldLabel>
          {value.type === 'custom' && !isPreview ? <NameInput value={value.title} onChange={onNameChange} /> : value.title}
        </StyldLabel>
        <Flex gap={6} style={{ color: 'rgb(141 141 153)' }}>
          {!isPreview && <div>
            <Tooltip title="AI生成提示词">
              <>
                {type === 'role' && <RoleModal onChange={onPromptChange} structPrompt={restStructPrompt} />}
              </>
              <>
                {type === 'output_format' && <OutputModal onChange={onPromptChange} title={title} structPrompt={restStructPrompt} />}
              </>
              <>
                {type !== 'role' && type !== 'output_format' && <TextModal onChange={onPromptChange} title={title} structPrompt={restStructPrompt} type={value.type} />}
              </>
            </Tooltip>

          </div>}
          <div>
            {textScroll ? <ShrinkOutlined onClick={onTextScrollChange} /> :
              <ArrowsAltOutlined onClick={onTextScrollChange} />}
          </div>
          <div>
            {type === 'custom' && !isPreview ? <DeleteOutlined onClick={onDelete} /> : null}
          </div>
        </Flex>
      </Flex>
    </div>
    <div>
      {textScroll ?
        <PlaceholderInput style={{ height: 130, padding: 0 }}
          autoSize={{ minRows: 6 }}
          value={value.content}
          placeholder={value?.placeholder || '请输入自定义主题内容'}
          bordered={false}
          onChange={onValueChange}
          onKeyDown={e => onKeyDown(e, value?.placeholder)}
        >
          <StyledTextArea disabled={isPreview} />
        </PlaceholderInput>
        :
        <PlaceholderInput
          rows={3}
          value={value.content}
          bordered={false}
          style={{ padding: 0, background: '#fff', resize: 'none' }}
          placeholder={value?.placeholder || '请输入自定义主题内容'}
          onChange={onValueChange}
          onKeyDown={e => onKeyDown(e, value?.placeholder)}
        >
          <StyledTextArea disabled={isPreview} />
        </PlaceholderInput>
      }
    </div>
  </ItemWrapper>
}

const ItemWrapper = styled(Card)`
  .item-header {
    margin-bottom: 6px;
  }
`;

export default StructuredPromptItem;
