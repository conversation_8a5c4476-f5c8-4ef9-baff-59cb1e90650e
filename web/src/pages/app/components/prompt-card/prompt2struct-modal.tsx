import { Button, Flex, Modal, Space, Spin } from "antd";
import styled from "styled-components";

const ContentItem = styled.div`
  background-color: #62699914;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
`;

const Prompt2StructModal = (props) => {
  const { open, content, loading, onRegen, onOk, onClose } = props;

  return <div>
    <Modal
      title="使用文本模式的prompt"
      open={open}
      onCancel={onClose}
      width={800}
      footer={<Flex justify="end">
        <Space>
          <Button onClick={onRegen} disabled={loading}>重新生成</Button>
          <Button type="primary" onClick={onOk} disabled={loading}>使用</Button>
        </Space>
      </Flex>}
    >
      <Spin spinning={loading} style={{ minHeight: '400px' }} tip="生成中...">
        {content?.map(item => {
          return <ContentItem key={item.title}>
            <div style={{ fontWeight: 500 }}>{item.title}</div>
            <div>{item.content}</div>
          </ContentItem>
        })}
      </Spin>
    </Modal>
  </div>
};


export default Prompt2StructModal;
