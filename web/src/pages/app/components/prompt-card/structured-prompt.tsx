import { <PERSON><PERSON>, <PERSON>, Button } from "antd";
import StructuredPromptItem from "./structured-prompt-item";
import { PlusOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { v4 as uuidv4 } from 'uuid';

export const StructuredPrompt = (props) => {
  const { onBlur, onChange, value, defaultValue, isPreview } = props;
  const [options, setOptions] = useState<any[]>();

  useEffect(() => {
    if ('value' in props) return;
    const newOptions = defaultValue?.map(op => {
      return {
        ...op,
        id: op.id || uuidv4()
      }
    });
    setOptions(newOptions);
  }, []);

  useEffect(() => {
    if ('value' in props) {
      const newOptions = value?.map(op => {
        return {
          ...op,
          id: op.id || uuidv4()
        }
      });
      setOptions(newOptions);
    }
  }, [value]);


  const onAdd = () => {
    const newOptions = (options || []).concat([{
      type: 'custom',
      title: '',
      content: '',
      placeholder: '请输入自定义主题内容',
      id: uuidv4()
    }]);
    setOptions(newOptions);
    onChange?.(newOptions);
  };

  const onItemChange = newValue => {
    const currentIndex = options.findIndex(op => op.id === newValue.id);
    const newOptions = [...options];
    newOptions.splice(currentIndex, 1, newValue);
    setOptions(newOptions);
    // onChange?.(newOptions);
  }

  const onItemDelete = id => {
    const currentIndex = options.findIndex(op => op.id === id);
    const newOptions = [...options];
    newOptions.splice(currentIndex, 1);
    setOptions(newOptions);
    // onChange?.(newOptions);
  }

  const onSave = () => {
    onChange?.(options);
  }

  return <Flex
    // tabIndex={-1} onBlur={onBlur}
    style={{ width: '100%' }}
    vertical >
    <Flex vertical gap={8} style={{ overflow: 'scroll', marginBottom: '14px' }} flex={1}>
      {options?.map(op => {
        return <StructuredPromptItem
          key={op.id}
          value={op}
          onChange={onItemChange}
          onDelete={onItemDelete}
          structPrompt={value}
          isPreview={isPreview || ['输出格式', '输出示例'].includes(op.title)}
        />
      })}
      {!isPreview && <Card onClick={onAdd}>
        <Flex justify="center" style={{ cursor: 'pointer' }}>
          <PlusOutlined />
          添加主题
        </Flex>
      </Card>}
    </Flex>
    {!isPreview && <Flex>
      <Button type="primary" style={{ width: '100%' }} onClick={onSave}>保存</Button>
    </Flex>}
  </Flex>
}