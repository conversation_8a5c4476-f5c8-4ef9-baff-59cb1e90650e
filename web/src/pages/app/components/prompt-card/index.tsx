import { Button, Flex, Image, Input, message, Popconfirm, Segmented, Tooltip } from 'antd';
import { DatabaseOutlined, FileTextOutlined, FileSyncOutlined, SplitCellsOutlined } from '@ant-design/icons';
import styled from "styled-components";
import { StructuredPrompt } from "./structured-prompt";
import { StyldLabel } from './style';
import PreTemplate from "./pre-template";
import SaveTemplate from "./save-template";
import { AgentTemplateApi } from "@/api/agent-template";
import { useGlobalState } from "@/hooks/useGlobalState";
import Prompt2StructModal from './prompt2struct-modal';
import { useState, useRef } from 'react';
import Struct2PromptModal from './struct2prompt-modal';
import PromptEditor from '../prompt-editor';
import PromptBeautify from './prompt-beautify';
import DiffAlert from '../agent-dev/mods/diff-alert';
import { IAppType } from '@/interface';

export const PromptCard = (props) => {
  const { value, oldValue, onChange, onBlur: onParentBlur, onStructPromptChange: onParentStructPromptChange, onTextPromptChange: onParentTextPromptChange, onTemplateChange, disabled, config, appType, oldPrePrompt, newPrePrompt, mode, onDebugPrompt, useLocal } = props;
  const { paramsInPrompt, modelConfig, knowledge, welcomeText } = config || {};
  const { globalState } = useGlobalState();
  const { group, workspace } = globalState;
  const [convertOpen, setConvertOpen] = useState(false);
  const [convertLoading, setConvertLoading] = useState(false);
  const [convertContent, setConvertContent] = useState();
  const [transformOpen, setTransformOpen] = useState(false);

  const [innerPromptType, setPromptType] = useState(value?.promptType);

  const promptType = disabled ? innerPromptType : value?.promptType;

  const preTemplateRef = useRef();
  const timer = useRef(null);

  const onTypeChange = (newType) => {
    const newValue = {
      ...value,
      promptType: newType,
    };
    if (disabled) {
      setPromptType(newType);
      return;
    }
    setTransformOpen(true);

    if (timer?.current) {
      clearTimeout(timer.current);
    }
    timer.current = setTimeout(() => {
      setTransformOpen(false);
    }, 3000);

    // return;
    setConvertContent(undefined);

    onChange(newValue);
  };

  const onPromptTransform = () => {
    setConvertLoading(true);
    setConvertOpen(true);

    if (promptType === 'struct') {
      AgentTemplateApi.text2Struct({
        prompt: value?.prompt
      }).then(res => {
        setConvertContent(res.structPrompt);
        setConvertLoading(false);
      });
    } else {
      // (value.promptType === 'raw')
      AgentTemplateApi.struct2Text({
        structPrompt: value.structPrompt
      }).then(res => {
        setConvertContent(res.prompt);
        setConvertLoading(false);
      })
    }
  };

  const onRestPrompt = () => {
    if (promptType === 'struct') {
      const newValue = {
        ...value,
        structPrompt: convertContent
      };
      onChange(newValue);
    }
    if (promptType === 'raw') {
      const newValue = {
        ...value,
        prompt: convertContent
      }
      onChange(newValue);
    }
    setConvertOpen(false);
  };

  const onConvertClose = () => {
    setConvertOpen(false);
    setConvertContent(undefined);
  };

  const onBlur = (e) => {
    e?.stopPropagation();
    onParentBlur?.()
  };

  const onPromptChange = (key, val) => {
    const newValue = {
      ...value,
      [`${key}`]: val
    };
    onChange(newValue);
  };

  const onTextPromptChange = () => {
    onParentTextPromptChange(value);
  };

  const onStructPromptChange = val => {
    const newValue = {
      ...value,
      structPrompt: val
    };
    onParentStructPromptChange(newValue);
  };

  const onSave = async (basicValue, cb) => {
    const { structPrompt, prompt, ...rest } = value;

    const promptJson: any = {
      structPrompt: structPrompt?.map(item => {
        const { id, ...restItem } = item;
        return {
          ...restItem
        }
      }),
      prompt,
    }

    if (modelConfig && Object.keys(modelConfig || {}).length) {
      promptJson.modelConfig = modelConfig;
    }
    if (paramsInPrompt?.length) {
      promptJson.paramsInPrompt = paramsInPrompt;
    }
    // if (knowledge && Object.keys(knowledge || {})?.length) {
    //   promptJson.knowledge = knowledge;
    // }
    if (welcomeText) {
      promptJson.welcomeText = welcomeText;
    }

    const params = {
      workspaceId: workspace.id,
      appType,
      groupId: group.id,
      prompt: JSON.stringify(promptJson),
      ...rest,
      ...basicValue,
    };

    const res = await AgentTemplateApi.saveTemplate(params);

    if (res.id) {
      cb();
      // @ts-ignore
      preTemplateRef?.current?.getTemplateList?.();
      message.success('保存模板成功！');
    }
  };

  const onPreTemplateChange = (template) => {
    if (!template) return;
    const { prompt: promptConfig, promptType } = template;
    const { prompt, structPrompt, paramsInPrompt: newParamsInPrompt, ...rest } = JSON.parse(promptConfig || '{}');

    onTemplateChange({
      prompt, promptType, structPrompt, paramsInPrompt: newParamsInPrompt,
      ...rest,
    });
  };

  const onPromptBeautify = (newPrompt) => {
    onPromptChange('prompt', newPrompt)
  };

  const onTransformCancel = () => {
    setTransformOpen(false);
  };

  const onTransformConfirm = () => {
    onPromptTransform();
    setTransformOpen(false);
  };

  return <PromptCardWrapper vertical>
    <Flex
      justify="space-between"
      style={{ marginBottom: 4 }}>
      <Flex gap={10} align="center">
        {!disabled && <StyldLabel>
          <Tooltip title={<Flex vertical gap={8}>
            <div>支持文本化提示词和结构化提示词编写，二者可相互转化，同时切换的瞬间也会提示用户是否要进行提示词转换。</div>
            <Image
              src="https://p6.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/56869579157/9784/3f9b/dccb/1a55b672c4bd486ef041ed90f2fd2b5c.png"
            />
            <a
              rel="noopener"
              target='_blank'
              href="https://music-doc.st.netease.com/st/langbase-doc/update/2024-12-05#-%E7%BB%93%E6%9E%84%E5%8C%96%E6%8F%90%E7%A4%BA%E8%AF%8D"
            >查看详情</a>
          </Flex>}>
            <span style={{ cursor: 'pointer' }}>角色设定</span>
          </Tooltip>
          <DiffAlert
            title="提示词"
            type="prePrompt"
            newValue={newPrePrompt}
            oldValue={oldPrePrompt}
            onRedo={() => {
              onChange(oldValue);
            }
            }
          />
        </StyldLabel>}
        {appType === IAppType.Evaluator ? null : (disabled ? <Segmented
          options={[
            {
              value: 'struct', icon:
                <Tooltip title="结构化模式">
                  <DatabaseOutlined />
                </Tooltip>
            },
            {
              value: 'raw', icon: <Tooltip title="文本模式">
                <FileTextOutlined />
              </Tooltip>
            },
          ]}
          value={disabled ? innerPromptType : promptType}
          onChange={onTypeChange}
        /> :
          <Popconfirm
            open={transformOpen}
            title={promptType === 'struct' ? '将文本prompt转成结构化prompt？' : '将结构化prompt转成文本prompt？'}
            onCancel={onTransformCancel}
            onConfirm={onTransformConfirm}
          >
            <Segmented
              options={[
                {
                  value: 'struct', icon:
                    <Tooltip title="结构化模式">
                      <DatabaseOutlined />
                    </Tooltip>
                },
                {
                  value: 'raw', icon: <Tooltip title="文本模式">
                    <FileTextOutlined />
                  </Tooltip>
                },
              ]}
              value={promptType}
              onChange={onTypeChange}
            />
          </Popconfirm>
        )}

      </Flex>
      {!disabled && <Flex align="center" gap={8}>
        {appType !== IAppType.Evaluator && <Tooltip title={promptType === 'struct' ? '将文本模式的prompt，写进结构化' : '将结构化模式的prompt，写进本文'}>
          <FileSyncOutlined onClick={onPromptTransform} />
        </Tooltip>}
        {['edit', 'modelDebug'].includes(mode) &&
          <Flex>
            <Tooltip title="提示词对比调试">
              <Button
                type='text'
                size='small'
                icon={<SplitCellsOutlined />} onClick={onDebugPrompt}></Button>
            </Tooltip>
          </Flex>
        }
        {appType !== IAppType.Evaluator && <SaveTemplate onSave={onSave} />}
        {appType !== IAppType.Evaluator && <PreTemplate onChange={onPreTemplateChange} ref={preTemplateRef} appType={appType} />}
        {(promptType === 'raw' || !promptType) && <PromptBeautify onChange={onPromptBeautify} prePrompt={value?.prompt} />}
      </Flex>}
    </Flex>
    {/* <Flex>
      <a>
        {value.promptType === 'struct' ? '将文本模式的prompt，写进结构化' : '将结构化模式的prompt，写进本文'}
      </a>
    </Flex> */}
    <Flex style={{ height: '100%', overflow: 'hidden' }} flex={1}>
      {promptType === 'struct' &&
        <StructuredPrompt
          value={value.structPrompt}
          // onBlur={onBlur}
          // onChange={val => {
          //   onPromptChange('structPrompt', val);
          // }}
          onChange={onStructPromptChange}
          isPreview={disabled}

        />}
      {(promptType === 'raw' || !promptType) &&
        // <Input.TextArea
        //   value={value.prompt}
        //   onBlur={onBlur}
        //   style={{ height: '100%', resize: 'none' }}
        //   onChange={ev => onPromptChange('prompt', ev.target.value)} />
        <PromptEditor
          disabled={disabled}
          value={value?.prompt}
          onChange={val => onPromptChange('prompt', val)}
          paramsInPrompt={paramsInPrompt}
          onBlur={onTextPromptChange}

        />
      }
    </Flex>
    {promptType === 'struct' && <Prompt2StructModal
      open={convertOpen}
      loading={convertLoading}
      content={convertContent}
      onRegen={onPromptTransform}
      onOk={onRestPrompt}
      onClose={onConvertClose}
    />}
    {promptType === 'raw' && <Struct2PromptModal
      open={convertOpen}
      loading={convertLoading}
      content={convertContent}
      onRegen={onPromptTransform}
      onOk={onRestPrompt}
      onClose={onConvertClose}
    />}
  </PromptCardWrapper>
}

const PromptCardWrapper = styled(Flex)`
  background-color: #f5f8fc;
  padding: 12px;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  .ant-card-body {
    padding: 12px;
  }
`;
