import { Input, Modal, Button, Flex, Empty, Spin, Typography, message } from "antd";
import { HighlightOutlined } from '@ant-design/icons';
import { useState } from "react";
import { AgentTemplateApi } from "@/api/agent-template";
import { PromptContent } from "./style";
import AIBeautify from "./ai-beautify";

const { Text } = Typography;

const TextInput = (props) => {
  const { onChange, loading } = props;
  const [inputValue, setInputValue] = useState();

  const onSubmit = () => {
    if (!inputValue) {
      message.error('业务说明不能为空！');
      return;
    }
    onChange(inputValue);
  };

  const onTextChange = e => {
    setInputValue(e.target.value);
  }

  return <Flex vertical style={{ width: '100%', height: '100%' }}>
    <Flex
      style={{ width: '100%', flex: 1, overflow: 'scroll', marginBottom: '10px' }}
      flex={1}
      vertical
    >
      <Text strong>业务说明：</Text>
      <Input.TextArea onChange={onTextChange} style={{ height: '100%' }} placeholder="请输入业务说明..." />
    </Flex>
    <Flex justify="end">
      <Button type="primary" onClick={onSubmit} loading={loading} disabled={!inputValue}>生成</Button>
    </Flex>
  </Flex>
}

const TextModal = (props) => {
  const { onChange: onParentChange, title, structPrompt, type } = props;
  const [open, setOpen] = useState(false);
  const [prompt, setPrompt] = useState();
  const [loading, setLoading] = useState(false);


  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
    setPrompt(undefined);
  }

  const onUse = () => {
    onParentChange(prompt);
    setOpen(false);
    setPrompt(undefined);
  }

  const onChange = value => {
    setLoading(true);
    AgentTemplateApi.beautify2text({
      subStructPromptType: type,
      subStructPrompt: [{
        title,
        content: value
      }],
      structPrompt
    }).then(res => {
      setPrompt(res.prompt);
      setLoading(false);
    }).catch(err => {
      setLoading(false);
    });

  }

  return <div>
    <HighlightOutlined onClick={onOpen} />
    <Modal
      title={`生成${title}提示词`}
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <Flex style={{ height: '600px' }}>
        <Flex flex={1} style={{ padding: '0 12px' }} vertical>
          <TextInput loading={loading} onChange={onChange} />
        </Flex>
        <Flex flex={1} align="center">
          <AIBeautify data={prompt} onUse={onUse} loading={loading} />
        </Flex>
      </Flex>
    </Modal>
  </div>
};

export default TextModal;
