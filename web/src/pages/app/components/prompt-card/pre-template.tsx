import { AgentTemplateApi } from "@/api/agent-template";
import { Alert, Avatar, Button, Flex, Modal, Popconfirm, Select, Space } from "antd"
import { forwardRef, useEffect, useImperativeHandle, useState } from "react"
import { useGlobalState } from "@/hooks/useGlobalState";

const PreTemplate = (props, ref) => {
  const { onChange, appType } = props;
  const [open, setOpen] = useState<boolean>();
  const [templateList, setTemplateList] = useState();
  const [template, setTemplate] = useState();
  const { globalState } = useGlobalState();
  const { group, workspace } = globalState;

  const getList = async () => {
    if (!workspace || !group) return;
    AgentTemplateApi.getTemplateList({
      workspaceId: workspace.id,
      appType,
      groupId: group.id,
    }).then(res => {
      setTemplateList(res?.items?.map(item => {
        const { id, name, ...rest } = item;
        return {
          value: id,
          label: name,
          ...rest,
        }
      }));
    })
  }

  useImperativeHandle(ref, () => ({
    getTemplateList: getList
  }));

  useEffect(() => {
    getList();
  }, [workspace, group]);

  const onOpen = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onOk = () => {
    onChange(template);
    setOpen(false);
    setTemplate(undefined);
  };

  const onTemplateSelect = (id, data) => {
    setTemplate(data);
  };

  return <div>
    <Button type="link" onClick={onOpen} style={{ padding: 0 }}>预设模板</Button>
    <Modal
      title="预设模板"
      // okText="确认覆盖"
      open={open}
      onCancel={onClose}
      footer={<div>
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Popconfirm
            title="确认后，当前角色设定将会被覆盖"
            onConfirm={onOk}
            onCancel={onClose}>
            {/* @ts-ignore */}
            <Button type="primary" disabled={!template?.value}>确认覆盖</Button>
          </Popconfirm>
        </Space>
      </div>}
    >
      <Flex style={{ marginBottom: 6 }} gap={4}>
        <div>
          选择被覆盖的模板:
        </div>

      </Flex>
      <div>
        <Select
          placeholder="选择被覆盖的模板"
          style={{ width: '100%' }}
          options={templateList}
          optionRender={op => {
            return <Flex key={op.value}>
              <div style={{ marginRight: '12px', marginTop: 5 }}>
                <Avatar shape="square" src={op.data.avatarUrl} />
              </div>
              <div>
                <div>{op.label}</div>
                <div style={{ fontSize: 12, color: '#999'}}>{op.data?.desc}</div>
              </div>
            </Flex>
          }}
          // @ts-ignore
          value={template?.value}
          onChange={onTemplateSelect}
        />
      </div>
      <div style={{ marginTop: 10, }}>
        <Alert
          message="确认后，当前角色设定将会被覆盖"
          type="warning"
          showIcon
          style={{
            padding: 0, background: 'transparent', border: 'none'
          }} />
      </div>
    </Modal>
  </div>
};

export default forwardRef(PreTemplate);
