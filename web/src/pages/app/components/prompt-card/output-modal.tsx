import { Input, Modal, Button, Flex, Radio, Form } from "antd";
import { HighlightOutlined } from '@ant-design/icons';
import { useState } from "react";
import { AgentTemplateApi } from "@/api/agent-template";
import AIBeautify from "./ai-beautify";
import PlaceholderInput from "./placeholder-input";


const example_str = {
  'json': `{
  desc:SQL语句描述,
  sql:完整的sql语句
}
`,
  'table': `ID，用途，sql语句`,
  'text': `1、按照符合人类阅读习惯的文本格式输出；
2、对重要的关键词，可以使用不同的格式突出显示，比如：
**这是加粗的文字**
*这是斜体的文字*
  `,
  'custom': `desc:SQL语句描述,
sql:完整的sql语句
  `
}

const FormatSelect = (props) => {
  const { onChange, loading } = props;
  const [outputType, setOutputType] = useState('text');
  const [desc, setDesc] = useState();

  const onTypeChange = e => {
    setOutputType(e.target.value);
  }

  const onDescChange = e => {
    setDesc(e.target.value);
  }

  const onKeyDown = (e, defaultDesc) => {
    const tabKeyPressed = e.keyCode == 9;
    if (tabKeyPressed && !desc) {
      setDesc(defaultDesc);
    }
  }

  const onSubmit = () => {
    const values = [{ title: '输出格式类型', content: outputType }];
    if (desc) {
      values.push({ title: '字段描述', content: desc })
    }
    onChange(values);
  };

  return <Flex vertical style={{ height: '100%' }}>
    <Flex vertical flex={1}>
      <Form layout="vertical">
        <Form.Item label="输出格式">
          <Radio.Group
            value={outputType}
            onChange={onTypeChange}
            style={{ marginBottom: '10px' }}>
            <Radio value="json">JSON</Radio>
            <Radio value="table">表格</Radio>
            <Radio value="text">文本</Radio>
            <Radio value="custom">自定义</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="字段描述：">
          {/* <Input.TextArea
            value={desc}
            onChange={onDescChange}
            placeholder={example_str[outputType]}
            style={{ minHeight: 400 }}
            onKeyDown={e => onKeyDown(e, example_str[outputType])}
          /> */}
          <PlaceholderInput value={desc}
            onChange={onDescChange}
            placeholder={example_str[outputType]}
            style={{ minHeight: 400 }}
            onKeyDown={e => onKeyDown(e, example_str[outputType])}>
            <Input.TextArea />
          </PlaceholderInput>
        </Form.Item>
      </Form>
    </Flex>
    <Flex justify="end">
      <Button type="primary" onClick={onSubmit} loading={loading}>生成</Button>
    </Flex>
  </Flex>
};

const OutputModal = (props) => {
  const { onChange: onParentChange, title, structPrompt } = props;
  const [open, setOpen] = useState(false);
  const [prompt, setPrompt] = useState();
  const [loading, setLoading] = useState(false);

  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
    setPrompt(undefined);
  }

  const onUse = () => {
    onParentChange(prompt);
    setOpen(false);
    setPrompt(undefined);
  }

  const onChange = value => {
    setLoading(true);
    AgentTemplateApi.beautify2text({
      subStructPromptType: 'output_format',
      subStructPrompt: value,
      structPrompt
    }).then(res => {
      setPrompt(res.prompt);
      setLoading(false);
    }).catch(err => {
      setLoading(false);
    });

  }

  return <div>
    <HighlightOutlined onClick={onOpen} />
    <Modal
      title={`生成${title}提示词`}
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <Flex style={{ height: '600px' }}>
        <Flex flex={1} style={{ padding: '0 12px' }} vertical>
          <FormatSelect loading={loading} onChange={onChange} />
        </Flex>
        <Flex flex={1} align="center">
          <AIBeautify data={prompt} loading={loading} onUse={onUse} />
        </Flex>
      </Flex>
    </Modal>
  </div>
};


export default OutputModal;