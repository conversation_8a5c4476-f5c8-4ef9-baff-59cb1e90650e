import React, { useEffect, useRef, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import { Input, Tag, theme } from 'antd';
import { TweenOneGroup } from 'rc-tween-one';

const App = (props) => {
  const { options, value = [], onChange } = props;
  const { token } = theme.useToken();
  const [tags, setTags] = useState(options);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<InputRef>(null);


  const onSelectedTag = (tag) => {
    const newValue = [...(value || [])]
    const index = value.findIndex(item => item === tag);
    if (index > -1) {
      newValue.splice(index, 1);
      onChange(newValue);
    } else {
      newValue.push(tag);
      onChange(newValue);
    }
  };


  useEffect(() => {
    if (inputVisible) {
      inputRef.current?.focus();
    }
  }, [inputVisible]);

  // const handleClose = (removedTag: string) => {
  //   const newTags = tags.filter((tag) => tag !== removedTag);
  //   console.log(newTags);
  //   setTags(newTags);
  // };

  const showInput = () => {
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    if (inputValue && tags.indexOf(inputValue) === -1) {
      setTags([...tags, inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  const forMap = (tag: string) => {
    const isSelected = value?.indexOf(tag) > -1;

    return <span key={tag} style={{ display: 'inline-block', cursor: 'pointer' }} onClick={() => onSelectedTag(tag)}>
      <Tag
        closable={isSelected}
        onClose={(e) => {
          e.preventDefault();
          onSelectedTag(tag)
          //   handleClose(tag);
        }}
        color={isSelected ? 'blue' : 'default'}
      >
        {tag}
      </Tag>
    </span>
  };

  const tagChild = tags.map(forMap);

  const tagPlusStyle: React.CSSProperties = {
    background: token.colorBgContainer,
    borderStyle: 'dashed',
  };

  return (
    <>
      <div style={{ marginBottom: 16 }}>
        <TweenOneGroup
          appear={false}
          enter={{ scale: 0.8, opacity: 0, type: 'from', duration: 100 }}
          leave={{ opacity: 0, width: 0, scale: 0, duration: 200 }}
          onEnd={(e) => {
            if (e.type === 'appear' || e.type === 'enter') {
              (e.target as any).style = 'display: inline-block';
            }
          }}
        >
          {tagChild}
        </TweenOneGroup>
      </div>
      {inputVisible ? (
        <Input
          ref={inputRef}
          type="text"
          size="small"
          style={{ width: 78 }}
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
        />
      ) : (
        <Tag onClick={showInput} style={tagPlusStyle}>
          <PlusOutlined /> 自定义
        </Tag>
      )}
    </>
  );
};

export default App;