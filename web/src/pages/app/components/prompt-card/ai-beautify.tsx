import { Input, Button, Flex, Empty, Spin, Typography } from "antd";
import { PromptContent } from "./style";
import ReactDiffViewer from 'react-diff-viewer';

const { Text } = Typography;

const AIBeautify = (props) => {
  const { loading, data, onUse, prePrompt, showDiff } = props;
  return <Spin
    wrapperClassName="spinning-loading"
    spinning={loading}>
    {data ?
      <Flex
        vertical
        style={{ height: '100%', width: '100%' }}>
        {/* <Flex>
          <Text strong>生成的提示词：</Text>
        </Flex> */}
        <Flex flex={1} style={{ marginBottom: '10px' }}>
          <PromptContent>
            {showDiff ? (
              <ReactDiffViewer
                oldValue={prePrompt}
                splitView={false}
                hideLineNumbers
                newValue={data}
              styles={{
                diffContainer: {
                  height: '100%',
                }
                }}
              />
            ) : (
              <Input.TextArea
                readOnly
                value={data}
                style={{ height: '100%', background: 'transparent' }}
                bordered={false}
              ></Input.TextArea>
            )}
          </PromptContent>
        </Flex>
        <Flex justify="end">
          <Button type="primary" onClick={onUse} disabled={loading}>使用</Button>
        </Flex>
      </Flex> :
      <Flex align="center" justify="center"
        style={{ height: '100%', width: '100%' }}
      >
        <Empty description="根据左侧指令生成的提示词 将会显示在这里" />
      </Flex>
    }
  </Spin>
};

let text = `
# 角色
你是一名SQL大师，能够根据用户的具体需求生成精确的SQL语句。

# 背景
你的任务是帮助用户将自然语言请求转化为有效的SQL查询，以便从数据库中提取或操作数据。

# 技能
1. 能够识别并理解用户输入中的关键需求。
2. 根据不同数据库的特性，生成优化的SQL语句。
3. 提供安全、有效的查询，避免SQL注入等风险。

# 要求
- 输出的SQL语句必须准确反映用户的意图。
- 确保生成的SQL语句符合标准SQL语法。
- 在必要时，提供参数化查询以增强安全性。

# 用例
1. 用户输入：请生成一个查询所有客户信息的SQL语句。
   输出：SELECT * FROM customers;

2. 用户输入：请生成一个查询特定订单详情的SQL语句。
   输出：SELECT * FROM orders WHERE order_id = ?;

3. 用户输入：请创建一个插入新产品信息的SQL语句。
   输出：INSERT INTO products (product_name, price, quantity) VALUES (?, ?, ?);
`

export default AIBeautify;
