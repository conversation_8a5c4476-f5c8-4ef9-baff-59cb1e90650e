import { Tooltip } from "antd";
import Condition from "./condition";
import { toLogicalModel } from "./utils";
import { PreviewStyle } from "../logical-relations/preview";

const FilterPreview = (props) => {
  const { value, keyOptions } = props;

  const isSimple = value?.children?.length === 1;

  if (isSimple) {
    return <Condition value={toLogicalModel(value)} isPreview
      size="small" />
  }
  return <Tooltip
    color="#fff"
    overlayInnerStyle={{
      color: '#333',
      width: 'max-content',
      padding: '12px'
    }}
    title={<div>
      <Condition
        isPreview
        size="small"
        options={keyOptions} value={toLogicalModel(value)} />
    </div>}>
    <PreviewStyle style={{
      color: '#faad14',
      backgroundColor: '#fffbe6',
      cursor: 'pointer'
    }}>组合条件</PreviewStyle>
  </Tooltip>



};

export default FilterPreview;
