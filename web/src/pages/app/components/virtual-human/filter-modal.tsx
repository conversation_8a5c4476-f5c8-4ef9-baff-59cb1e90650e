import { Button, Form, Input, Modal, Divider } from "antd";
import { useState } from "react";
import Condition from "./condition";
import { toCioModel, toLogicalModel } from "./utils";
import ParamsEditDrawer from "../params-card/params-edit-drawer";

const FilterModal = (props) => {
  const { onChange, trigger, value, keyOptions, addNewParams } = props;
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const onOpen = () => {
    const logicalValue = {
      name: value?.name,
      prompt: value?.prompt,
      condition: toLogicalModel(value?.filter)
    };
    form.setFieldsValue(logicalValue);
    setOpen(true);
  }

  const onCancel = () => {
    setOpen(false);
    form.resetFields();
  }

  const onOk = async () => {
    const values = await form.validateFields();
    const cioValues = {
      name: values.name,
      prompt: values.prompt,
      filter: toCioModel(values.condition, keyOptions)
    };
    onChange(cioValues);
    setOpen(false);
    form.resetFields();
  }


  return <div>
    <div onClick={onOpen} style={{ cursor: 'pointer' }}>
      {trigger || <Button style={{ width: '100%' }} size="small">添加条件</Button>}
    </div>
    <Modal
      title="添加条件"
      open={open}
      width={840}
      onCancel={onCancel}
      onOk={onOk}
      styles={{
        body: {
          maxHeight: '600px',
          overflow: 'scroll',
        }
      }}
    >
      <Form form={form} labelCol={{
        span: 3
      }}>
        <Form.Item label="条件名称" name="name"
          rules={[
            { required: true, message: '请填写条件名称' }
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item label="条件提示词" name="prompt"
          rules={[
            { required: true, message: '请填写条件提示词' }
          ]}
        >
          <Input.TextArea rows={5} />
        </Form.Item>
        <Form.Item label="触发条件" name="condition">
          <Condition
            options={keyOptions}

            keyInputProps={{
              dropdownRender: menu => {
                return <>
                  {menu}
                  <Divider style={{ margin: '8px 0' }} />
                  <div>{addNewParams}</div>
                </>
              }
            }}
          />
        </Form.Item>
      </Form>
    </Modal>
  </div >
};

export default FilterModal;
