import { Flex, Tooltip, Typography } from "antd";
import FilterModal from "./filter-modal";
import styled from "styled-components";
import { DeleteOutlined, EditOutlined } from "@ant-design/icons";
import Condition from "./condition";
import { toLogicalModel } from "./utils";
import { PreviewStyle } from "../logical-relations/preview";

const StyledItem = styled.div`
    width: 100%;
    /* background: #f5f8fc; */
    background: rgb(245, 248, 252);
    /* margin-bottom: 6px; */
    padding: 6px 0px 6px 8px;
    border-radius: 6px;
    border-bottom: 1px solid rgb(239, 240, 247);
`;

const FilterItem = (props) => {
  const { onChange: onParentChange, value, keyOptions, onDelete, addNewParams, isPreview } = props;

  const isEdit = !isPreview;

  const onFilterChange = val => {
    onParentChange(val);
  };

  const isSimple = value?.filter?.children?.length === 1;

  return <StyledItem>
    <div>
      <Flex justify="space-between">
        <Flex>
          <Typography.Text strong>{value.name}</Typography.Text>
        </Flex>
        <Flex gap={6}>
          {value.filter && <>
            {isSimple ?
              <Condition
                isPreview
                size="small"
                options={keyOptions} value={toLogicalModel(value?.filter)} />
              : <Tooltip
                // open
                color="#fff"
                overlayInnerStyle={{
                  color: '#333',
                  width: 'max-content',
                  padding: '12px'
                }}
                title={<div>
                  <Condition
                    isPreview
                    size="small"
                    options={keyOptions} value={toLogicalModel(value?.filter)} />
                </div>}>
                <PreviewStyle style={{
                  color: '#faad14',
                  backgroundColor: '#fffbe6',
                  cursor: 'pointer'
                }}>组合条件</PreviewStyle>
              </Tooltip>}
          </>}
          <FilterModal
            trigger={<EditOutlined style={{ color: 'rgba(0,0,0,0.45)', fontSize: '12px' }} />}
            keyOptions={keyOptions}
            value={value}
            onChange={val => onFilterChange(val)}
            addNewParams={addNewParams}
          />
          {isEdit && <DeleteOutlined style={{ color: 'rgba(0,0,0,0.45)', fontSize: '12px' }} onClick={onDelete} />}
        </Flex>
      </Flex>
    </div>
  </StyledItem>
}

export default FilterItem;