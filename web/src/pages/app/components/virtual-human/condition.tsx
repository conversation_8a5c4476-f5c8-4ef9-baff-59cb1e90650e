import { LogicalRelations } from "../logical-relations";
import { useCallback, useMemo } from "react";
import { operatorOptions } from "./value-component";
import { OperationTypeEnum, ParamDataTypeEnum } from "@/interface/agent";
import { InputPreview, SelectPreview } from "../logical-relations/preview";
import TimeRangeSelector from "./params-filter/time-range";
import { omit } from "lodash";
import { Flex, Tooltip } from "antd";
import { QuestionCircleOutlined } from "@ant-design/icons";

const Condition = (props) => {
  const { value, onChange, options, isPreview, keyInputProps = {} } = props;

  const onLogicalChange = val => {
    onChange(val);
  }

  const keyOptions = useMemo(() => {
    return options?.map(op => {
      if ([ParamDataTypeEnum.double, ParamDataTypeEnum.integer, ParamDataTypeEnum.long].includes(op.dataType)) {
        return {
          ...op,
          operatorOptions: operatorOptions.number
        }
      }

      if ([ParamDataTypeEnum.time].includes(op.dataType)) {
        return {
          ...op,
          operatorOptions: operatorOptions.date
        }
      }

      if ([ParamDataTypeEnum.bool].includes(op.dataType)) {
        return {
          ...op,
          operatorOptions: operatorOptions.bool
        }
      }

      return {
        ...op,
        operatorOptions: operatorOptions.string
      }
    })
  }, [options]);

  const renderItemValue = useCallback(prop => {
    const { record, ...rest } = prop;

    const currentKey = keyOptions?.find(item => record.key === item.value);
    // 时间
    if (currentKey?.dataType === ParamDataTypeEnum.time
      || record.operator === 'cycle'
    ) {
      const { onChange, value: dateValue, ...others } = rest;

      if (record.operator === 'cycle') {
        return <TimeRangeSelector
          value={dateValue}
          onChange={val => {
            onChange(val);
          }}
          {...others} />
      }
      if (['contain', 'notcontain'].includes(record.operator)) {
        return <SelectPreview
          mode="multiple"
          options={[
            { label: '周一', value: 1, type: 'week' },
            { label: '周二', value: 2, type: 'week' },
            { label: '周三', value: 3, type: 'week' },
            { label: '周四', value: 4, type: 'week' },
            { label: '周五', value: 5, type: 'week' },
            { label: '周六', value: 6, type: 'week' },
            { label: '周日', value: 7, type: 'week' },
          ]}
          value={dateValue?.map(item => item.value)}
          onChange={(val, obj) => {
            onChange(obj?.map(item => omit(item, 'label')))
          }}
          {...others}
        // style={{ width: 300 }}
        />
      }
    }

    if ([OperationTypeEnum.包含, OperationTypeEnum.不包含, OperationTypeEnum.模糊包含, OperationTypeEnum.模糊不包含].includes(prop.record?.operator)) {
      // @ts-ignore
      return <SelectPreview mode="tags" {...rest} />
    }

    return <InputPreview
      {...rest}
    />
  }, [options]);

  return <div>
    <LogicalRelations
      isPreview={isPreview}
      options={keyOptions}
      onChange={onLogicalChange}
      showLogicSelect={value?.children?.length > 1}
      value={value}
      operatorOptions={operatorOptions.string}
      defaultOperator="eq"
      renderItemValue={renderItemValue}
      keyInputProps={{
        popupMatchSelectWidth: false,
        ...keyInputProps
      }}
      operatorInputProps={{
        style: { width: 130 },
        popupMatchSelectWidth: false,
        optionRender: (item, obj) => {
          if (['cycle', 'contain', 'notcontain'].includes(item.key as string) && item.data?.type === 'date') {
            return <Flex gap={2}>
              {item.label}
              <Tooltip
                color="#fff"
                overlayInnerStyle={{ width: 640 }}
                title={
                  <img width="620"
                    src="https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/57311264200/34f3/4797/40f2/207c2e278ad2d58d09bf8e35cf8c0025.png" />
                }>
                <QuestionCircleOutlined />
              </Tooltip>
            </Flex>
          }
          return item.label
        }
      }}
      valueInputProps={{
        style: { width: 360 }
      }}
    />
  </div>
};

export default Condition;
