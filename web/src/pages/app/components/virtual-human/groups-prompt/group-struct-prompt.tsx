import styled from "styled-components";
import FilterItem from "../filter-item";
import { useMemo } from "react";
import { uuidv4 } from "@antv/xflow";
import NewSortableList from "../../agent-dev/mods/new-sortable-list";

const CharacterPreview = styled.div`
  /* margin-bottom: 10px; */
`;

const GroupStructuredPrompt = (props) => {
  const { paramsInPrompt, onChange: onParentChange, value: parentValue, keyOptions, addNewParams, isPreview } = props;

  const value = useMemo(() => {
    return parentValue?.map(item => {
      if (item?.id) return item;

      return {
        ...item,
        id: uuidv4()
      }
    })
  }, [parentValue]);

  const onChange = newValue => {
    onParentChange(newValue)
  };

  const onFilterChange = (newFilter, filterIndex) => {
    const newValue = value?.map((item, index) => {
      if (index === filterIndex) {
        return newFilter
      }
      return item;
    });
    onChange(newValue);
  };

  const onDelete = index => {
    const newValue = value?.filter((item, i) => {
      return i !== index;
    });
    onChange(newValue);
  };

  const onSortChange = newValue => {
    onChange(newValue);
  };

  return <div>
    <CharacterPreview>
      <NewSortableList
        itemKey="id"
        value={value}
        onChange={onSortChange}
        itemRender={(item, filterIndex) => {
          return <FilterItem
            value={item}
            onChange={val => onFilterChange(val, filterIndex)}
            onDelete={() => onDelete(filterIndex)}
            paramsInPrompt={paramsInPrompt}
            keyOptions={keyOptions}
            addNewParams={addNewParams}
            isPreview={isPreview}
          />
        }}
      />
    </CharacterPreview>
  </div>
};

export default GroupStructuredPrompt;
