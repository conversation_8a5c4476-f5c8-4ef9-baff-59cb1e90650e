import { But<PERSON>, Flex, message } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import { v4 as uuidv4 } from 'uuid';
import Group from "./group";
import NewSortableList from "../../agent-dev/mods/new-sortable-list";
import { useMemo } from "react";

const GroupsPrompt = (props) => {
  const { value: parentValue, paramsInPrompt, onChange: onParentChange, addNewParams, isPreview, showCondition = true } = props;

  const value = useMemo(() => {
    return parentValue?.groups?.map(item => {
      if (item?.id) return item;
      return {
        ...(item || {}),
        id: uuidv4()
      }
    })
  }, [parentValue?.groups]);

  const keyOptions = paramsInPrompt?.map(item => {
    return {
      ...item,
      label: item.title,
      value: item.key,
    }
  });

  const onChange = newValue => {
    onParentChange({
      groups: newValue
    });
  }

  const validateGroup = () => {
    for (let i = 0; i < value?.length; i++) {
      const group = value[i];
      if (!group.name) {
        message.error('请先填写完当前参数');
        return false;
      }
    }
    return true;
  }

  const onAddGroup = () => {
    const isValid = validateGroup();

    if (!isValid) return false;

    const newGroup = {
      // name: `自定义主题-${value?.length + 1}`,
      name: '',
      promptType: 'raw',
      prompt: '',
      id: uuidv4()
    };
    const newValue = (value || []).concat(newGroup);
    onChange(newValue);
  };

  const onGroupChange = (val, groupIndex) => {
    let newValue;
    if (!val) {
      newValue = value?.filter((item, i) => {
        return i !== groupIndex
      });

    } else {

      newValue = value?.map((item, i) => {
        if (i === groupIndex) {
          return val
        }
        return item;
      })
    }
    onChange(newValue);
  }

  const onSortChange = newValues => {
    onChange(newValues);
  }

  const showDeleteIcon = value?.length > 1;


  const names = groupIndex => {
    return value?.filter((_, index) => index !== groupIndex)?.map(item => item.name);
  }


  return <Flex vertical justify="space-between" style={{ height: '100%', overflow: 'hidden',width: '100%' }}>
    <Flex
      vertical
      flex={1}
      style={{ overflow: 'scroll' }}>
      <NewSortableList
        itemKey="id"
        value={value}
        onChange={onSortChange}
        dragHandleStyle={{
          backgroundColor: 'rgba(0,0,0,0.02)',
          paddingLeft: '6px',
        }}
        itemStyle={{
          marginBottom: '8px'
        }}
        itemRender={(item, groupIndex) => {
          return <div
            style={{
              width: '100%',
              border: '1px solid rgb(239, 240, 247)',
            }}
          // key={`${item.name}-${groupIndex}`}
          >
            <Group
              key={groupIndex}
              value={item}
              keyOptions={keyOptions}
              groupIndex={groupIndex}
              showDeleteIcon={showDeleteIcon}
              onChange={val => onGroupChange(val, groupIndex)}
              names={names(groupIndex)}
              addNewParams={addNewParams}
              length={value?.length}
              isPreview={isPreview}
              showCondition={showCondition}
            />
          </div>
        }}
      />
    </Flex>
    {!isPreview && <Flex>
      <Button type="primary"
        style={{ width: '100%', margin: "8px 0" }} size="small" onClick={onAddGroup}>
        <PlusOutlined />
        添加主题
      </Button>
    </Flex>}
  </Flex>
};

export default GroupsPrompt;
