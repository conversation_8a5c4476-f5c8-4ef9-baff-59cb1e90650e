import { useRef, useState } from "react";
import { PromptApi } from "@/api/prompt";
import { <PERSON><PERSON>, Drawer, Flex, Input, Spin, Tooltip } from "antd";
import { EyeIcon, IconButton } from '@/components/icons';
import ParamsDebugForm from "../../chat-bot/params-debug-form";
interface IProps {
  appId: string;
  config: any;
  parameters?: any;
  trigger?: any
  userId: string;
  settingId: string;
  paramsInPrompt: any[];
};

const ViewPrompt = (props: IProps) => {
  const { config, appId, trigger, userId, settingId, paramsInPrompt } = props;
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>();
  const [parameters, setParameters] = useState(null);

  const formRef = useRef(null);

  const onOpen = () => {
    setOpen(true);
  };

  const onPreviewPrompt = (newParameters) => {
    setLoading(true);
    PromptApi.previewPrompt({
      appId,
      userId,
      settingId,
      config,
      parameters: newParameters,
    }).then(res => {
      setData(res);
    }).finally(() => {
      setLoading(false);
    });
  };

  const onClose = () => {
    setParameters(undefined);
    setOpen(false);
  };

  const onDebug = async () => {
    const res = await formRef.current?.validateFields();
    setParameters(res);
    onPreviewPrompt(res);
  };

  return <>
    {paramsInPrompt?.length > 0 && <Tooltip title="提示词调试">
      <Flex
        onClick={onOpen}
        style={{ cursor: 'pointer' }}>
        {trigger || <IconButton icon={<EyeIcon />} label="提示词" />}
      </Flex>
    </Tooltip>}
    <Drawer
      title="提示词调试"
      width={600}
      open={open}
      onClose={onClose}
    >
      <Flex vertical style={{ height: '100%' }}>
        <div style={{ marginBottom: 10 }}>
          <ParamsDebugForm
            value={parameters}
            paramsInPrompt={paramsInPrompt}
            ref={formRef}
            formProps={{ size: 'small' }}
          />
          <Flex justify="end">
            <Button type="primary" size="small"
              onClick={onDebug}>调试</Button>
          </Flex>
        </div>

        <Spin spinning={loading} style={{ height: '100%', flex: 1 }}>
        </Spin>

        {!loading && <Flex flex={1} style={{ height: '100%' }}>
          <Input.TextArea
            style={{
              height: '100%',
              width: '100%',
              background: '#f5f8fc',
              resize: 'none'
            }}
            value={data?.finalPrompt}
            bordered={false}
            readOnly
          />
        </Flex>}

      </Flex>
    </Drawer>
  </>
};

export default ViewPrompt;
