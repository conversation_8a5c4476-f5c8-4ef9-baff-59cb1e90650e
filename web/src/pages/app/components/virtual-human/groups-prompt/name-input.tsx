import { useEffect, useState } from "react";

import { Input } from "antd";
import { StyldLabel } from "../../prompt-card/style";

const NameInput = (props) => {
  const { value: parentValue, onChange, onBlur: onParentBlur, beforOnChange, ...rest } = props;
  const [editing, setEditing] = useState(false);
  const [value, setValue] = useState();


  useEffect(() => {
    setValue(parentValue);
    if (!parentValue) {
      setEditing(true);
    }
  }, [parentValue]);

  useEffect(() => {
    if (value) {
      setEditing(false);
    }
  }, []);

  const onValueChange = e => {
    setValue(e.target.value);
  }

  const onBlur = e => {
    if ((beforOnChange && beforOnChange(value)) || !beforOnChange) {

      if (value) {
        setEditing(false);
      }
      onChange(value);
    }
  };

  const onFocus = () => {
    setEditing(true);
  };

  return <div onClick={onFocus}>{editing ?
    <Input value={value}
      placeholder="未命名主题"
      onBlur={onBlur}
      size='small'
      onChange={onValueChange}
      {...rest} />
    : <StyldLabel>{value}</StyldLabel>}</div>
}

export default NameInput;