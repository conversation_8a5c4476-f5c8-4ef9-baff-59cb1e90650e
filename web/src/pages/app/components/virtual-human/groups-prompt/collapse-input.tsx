import { ShrinkOutlined, ArrowsAltOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { Tooltip } from 'antd';
import PromptEditor from '../../prompt-editor';
import styled from 'styled-components';

const StyledContainer = styled.div`
  position: relative;
  .placeholder-fake {
    user-select: none;
    position: absolute;
    left: 4;
    z-index: 1;
    color: #bbb;
    font-size: 14px;
  }
`;

export const Toggle = (props) => {
  const { onChange, defaultValue } = props;
  const [textScroll, setTextScroll] = useState(defaultValue);

  const onTextScrollChange = () => {
    setTextScroll(!textScroll);
    onChange(!textScroll);
  };

  return <Tooltip title={textScroll ? '收起' : '展开'}>
    {textScroll ? <ShrinkOutlined onClick={onTextScrollChange} /> :
      <ArrowsAltOutlined onClick={onTextScrollChange} />}
  </Tooltip>
}

const CollapseInput = (props) => {
  const { value, textScroll, onChange, paramsInPrompt, groupIndex, isPreview } = props;

  const onValueChange = (ev) => {
    onChange(ev);
  }

  return <StyledContainer style={{
    height: textScroll ? 420 : 120
  }}>
    {/* <span className='placeholder-fake'>{'输入 { 可选择插入变量'}</span> */}
    <PromptEditor
      id={`group-${groupIndex}`}
      value={value || ''}
      onChange={onValueChange}
      paramsInPrompt={paramsInPrompt}
      disabled={isPreview}
    />
  </StyledContainer>
};

export default CollapseInput;
