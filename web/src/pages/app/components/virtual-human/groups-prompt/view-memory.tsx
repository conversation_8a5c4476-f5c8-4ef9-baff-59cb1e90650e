import { useEffect, useState } from "react";
import { PromptApi } from "@/api/prompt";
import { memoryApi } from "@/api/memory";
import { Drawer, Flex, Input, Spin, Tooltip, Table, Button, message, Popconfirm } from "antd";
import { BrainIcon, IconButton } from '@/components/icons';
import type { TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';

interface IProps {
  appId: string;
  settingId: string;
  userId: string;
  config: any;
  parameters?: any;
  trigger?: any;
}

interface MemoryItem {
  id: number;
  content: string;
  memoryType: string;
  memoryTime: number;
  metaData: Record<string, any>;
}

interface MemoryResponse {
  values: MemoryItem[];
  total: number;
  page: number | null;
  pageSize: number | null;
}

const ViewMemory = (props: IProps) => {
  const { config, appId, settingId, userId, parameters, trigger } = props;
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>();
  const [memoryData, setMemoryData] = useState<MemoryItem[]>([]);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  console.log('settingId', settingId);

  const fetchMemoryList = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const res = await memoryApi.listMemory({
        appId,
        settingId,
        userId,
        offset: (page - 1) * pageSize,
        limit: pageSize,
      });
      console.log('res', res);
      setMemoryData(res.values || []);
      setTotal(res.total || 0);
      setPagination({
        ...pagination,
        current: page,
        pageSize,
        total: res.total || 0,
      });
    } catch (error) {
      console.error('获取记忆列表失败:', error);
      message.error('获取记忆列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleClearMemory = async () => {
    setLoading(true);
    try {
      const res = await memoryApi.clearMemory({
        appId,
        settingId,
        userId,
      });

      message.success('清除记忆成功');
      setTimeout(() => {  
        fetchMemoryList(1, pagination.pageSize);
      }, 1000);
    } catch (error) {
      console.error('清除记忆失败:', error);
      message.error('清除记忆失败');
    } 
  };

  const handleTableChange = (newPagination: TablePaginationConfig) => {
    fetchMemoryList(newPagination.current, newPagination.pageSize);
  };

  const onClose = () => {
    setOpen(false);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'memoryType',
      key: 'memoryType',
      width: 120,
      render: (text: string) => {
        const typeMap: Record<string, string> = {
          'OBSERVATION': '观察',
          'REFLECTION': '反思',
          'PLAN': '计划',
          // 可以根据实际情况添加更多类型
        };
        return typeMap[text] || text;
      }
    },
    {
      title: '时间',
      dataIndex: 'memoryTime',
      key: 'memoryTime',
      width: 180,
      render: (time: number) => dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
  ];

  const handleOpen = () => {
    setLoading(true);
    setOpen(true);
    fetchMemoryList(1, pagination.pageSize);
  }

  return <>
    <Tooltip title="查看长期记忆">
      <span
        onClick={handleOpen}
        style={{ cursor: 'pointer' }}>
        {trigger || <IconButton icon={<BrainIcon />} label="记忆" />}
      </span>
    </Tooltip>
    <Drawer
      title="查看长期记忆历史"
      width={800}
      open={open}
      onClose={onClose}
      extra={
        <Popconfirm
          title="确定要清除所有记忆吗？"
          description="此操作不可恢复"
          onConfirm={handleClearMemory}
          okText="确定"
          cancelText="取消"
        >
          <Button danger>清除记忆</Button>
        </Popconfirm>
      }
    >
      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={memoryData}
          rowKey="id"
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 'max-content' }}
        />
      </Spin>
    </Drawer>
  </>;
};

export default ViewMemory;
