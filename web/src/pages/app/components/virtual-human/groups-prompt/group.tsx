
import { Collapse, Flex, Popconfirm, Select, Space, Tooltip } from "antd";
import FilterModal from "../filter-modal";
import { DeleteOutlined, PlusCircleOutlined } from "@ant-design/icons";
import CollapseInput, { Toggle } from "./collapse-input";
import NameInput from "./name-input";
import GroupStructPrompt from "./group-struct-prompt";
import { useState } from "react";
import { v4 as uuidv4 } from 'uuid';


const Group = props => {
  const { groupIndex, value, keyOptions, onChange, showDeleteIcon, addNewParams, names, length, isPreview, showCondition = true } = props;
  const [textScroll, setTextScroll] = useState(length === 1);
  const [errorMessage, setErrorMessage] = useState(undefined);

  const canEdit = !isPreview;

  const beforeOnNameChange = val => {
    const _val = val?.trim();
    if (!_val) {
      setErrorMessage('名称不能为空，请重新修改');
      return false;
    }

    // 校验名称不能重复，因为名称要作为key
    if (names?.includes(_val)) {
      setErrorMessage(`${val} 已存在，请重新修改`);
      return false;
    }
    setErrorMessage(undefined);
    return true;
  }

  const onGroupNameChange = (val) => {
    const newValue = {
      ...value,
      name: val?.trim()
    };
    onChange(newValue)
  }

  // prompt 相关函数
  const onToggle = val => {
    setTextScroll(val);
  }

  const onPromptChange = val => {
    const newValue = {
      ...value,
      prompt: val
    };
    onChange(newValue)
  }

  const onPromptTypeChange = val => {
    const newValue = {
      ...value,
      promptType: val
    };
    if (val === 'raw') {
      delete newValue.structPrompt;
    }
    onChange(newValue)
  }

  const onAdd = (val) => {
    const newValue = {
      ...value,
      structPrompt: (value.structPrompt || []).concat({
        ...val,
        id: uuidv4()
      }),
    }
    onChange(newValue)
  };

  const onDelete = () => {
    onChange(null);
  }

  const onStructChange = val => {
    const newValue = {
      ...value,
      structPrompt: val
    };
    onChange(newValue);
  }
  return <Collapse
    // ghost
    bordered={false}
    collapsible="icon"
    defaultActiveKey={groupIndex}
    items={[
      {
        key: groupIndex,
        label: <div>
          <Flex gap={6} onClick={e => e.stopPropagation()}>
            <Flex>
              <NameInput value={value.name}
                beforOnChange={val => beforeOnNameChange(val)}
                onChange={val => onGroupNameChange(val)}
                disabled={isPreview} />
            </Flex>
            {showCondition && <Select
              size="small"
              value={value.promptType}
              options={[
                { value: 'struct', label: '条件提示词' },
                {
                  value: 'raw', label: <>
                    {value.promptType === 'struct' ? <div
                      onClick={e => {
                        e.stopPropagation();
                      }}>
                      <Popconfirm
                        title={<div style={{ fontSize: '12px', width: '160px' }}>切换到普通提示词会清空所有条件提示词，确认切换吗？</div>}
                        onConfirm={() => {
                          onPromptTypeChange('raw')
                        }}>
                        普通提示词
                      </Popconfirm>
                    </div> : '普通提示词'}
                  </>
                },
              ]}
              defaultValue="prompt"
              onChange={(val) => onPromptTypeChange(val)}
              style={{ width: 110 }}
              disabled={isPreview}
            />}
          </Flex>
          <div style={{ fontSize: '12px', color: '#ff4d4f' }}>{errorMessage}</div>
        </div>,
        children: <div>
          <>
            <div style={{ marginBottom: value.promptType === 'struct' ? 10 : 0 }}>
              <CollapseInput
                textScroll={textScroll}
                value={value.prompt} onChange={onPromptChange}
                paramsInPrompt={keyOptions}
                groupIndex={groupIndex}
                disabled={isPreview}
              />
            </div>
            {(value.promptType === 'struct') && showCondition &&
              <GroupStructPrompt
                value={value.structPrompt || []}
                onChange={(val) => onStructChange(val)}
                keyOptions={keyOptions}
                addNewParams={addNewParams}
                disabled={isPreview}
              />}
          </>
        </div>,
        extra: <Space onClick={e => e.stopPropagation()}>
          {/* <a>查看更多</a> */}
          {value.promptType === 'struct' && showCondition &&
            <FilterModal
              trigger={<Tooltip title="添加条件"><PlusCircleOutlined /></Tooltip>}
              onChange={val => onAdd(val)}
              keyOptions={keyOptions}
              addNewParams={addNewParams}
            />}
          <Toggle onChange={onToggle} defaultValue={length === 1} />
          {showDeleteIcon && canEdit && <Popconfirm title={`删除 ${value.name} ?`} onConfirm={onDelete}>
            <DeleteOutlined />
          </Popconfirm>}
        </Space>
      }
    ]} />
}

export default Group;