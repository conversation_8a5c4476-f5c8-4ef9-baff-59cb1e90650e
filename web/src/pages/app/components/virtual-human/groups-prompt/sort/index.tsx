import {
  DndContext, PointerSensor, useSensor, useSensors,
  KeyboardSensor,
} from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
  sortableKeyboardCoordinates
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Group from '../group';

const Sort = (props) => {
  const { value, keyOptions, onChange } = props;
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        // https://docs.dndkit.com/api-documentation/sensors/pointer#activation-constraints
        distance: 1,
      },
    }),
  );
  // const sensors = useSensors(
  //   useSensor(PointerSensor),
  //   useSensor(KeyboardSensor, {
  //     coordinateGetter: sortableKeyboardCoordinates,
  //   })
  // );
  const onDragEnd = ({ active, over }) => {
    if (active.id !== over?.id) {
      const activeIndex = active.id;
      const overIndex = over.id;
      const newValue = arrayMove(value, activeIndex, overIndex);
      onChange(newValue)
    }
  }

  const onGroupChange = (val, groupIndex) => {
    let newValue;
    if (!val) {
      newValue = value?.filter((item, i) => {
        return i !== groupIndex
      });

    } else {

      newValue = value?.map((item, i) => {
        if (i === groupIndex) {
          return val
        }
        return item;
      })
    }
    onChange(newValue);
  }

  const showDeleteIcon = value?.length > 1;

  return <DndContext sensors={sensors} modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
    <SortableContext
      items={value?.map((_, i) => i) || []}
      strategy={verticalListSortingStrategy}>
      {
        value?.map((item, groupIndex) => {
          return <Item key={groupIndex} id={groupIndex}>
            <div style={{ marginBottom: '8px' }} key={groupIndex}>
              <Group
                keyOptions={keyOptions}
                groupIndex={groupIndex}
                value={item}
                showDeleteIcon={showDeleteIcon}
                onChange={val => onGroupChange(val, groupIndex)}
              />
            </div>
          </Item>
        })
      }
    </SortableContext>
  </DndContext>
};

const Item = (props) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props.id,
  });
  const style = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: 'move',
    userSelect: 'none',
    ...(isDragging
      ? {
        position: 'relative',
        zIndex: 9999,
        backgroundColor: '#f2f3f5'
      }
      : {}),
  };
  return <div {...props} ref={setNodeRef} style={style} {...attributes} {...listeners}></div>

}

export default Sort;

