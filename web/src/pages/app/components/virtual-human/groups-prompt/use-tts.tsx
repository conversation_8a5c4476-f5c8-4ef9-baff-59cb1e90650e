import { useEffect, useState } from "react";
import { PromptApi } from "@/api/prompt";
import { memoryApi } from "@/api/memory";
import { Drawer, Flex, Input, Spin, Tooltip, Table, Button, message, Popconfirm } from "antd";
import { BrainIcon, IconButton, TtsIcon } from '@/components/icons';
import type { TablePaginationConfig } from 'antd/es/table';
import dayjs from 'dayjs';

interface IProps {
  appId: string;
  settingId: string;
  userId: string;
  config: any;
  parameters?: any;
  trigger?: any;
}

interface MemoryItem {
  id: number;
  content: string;
  memoryType: string;
  memoryTime: number;
  metaData: Record<string, any>;
}

interface MemoryResponse {
  values: MemoryItem[];
  total: number;
  page: number | null;
  pageSize: number | null;
}

const UseTts = () => {
  const [autoPlay, setAutoPlay] = useState(false);
  useEffect(() => {
    setAutoPlay(window.autoPlay);
  }, [window.autoPlay])
  const handleOpen = () => {
    window.autoPlay = !window.autoPlay;
    setAutoPlay(window.autoPlay);
  }

  return <>
    <Tooltip title="配置语音自动播放">
      <span
        onClick={handleOpen}
        style={{ cursor: 'pointer', position: 'relative', color: autoPlay ? '#2c72f0' : '#aaa' }}>
        {<IconButton icon={<TtsIcon />} label="语音" />}
        <span style={{
          position: 'absolute',
          fontSize: '6px',
          color: '#fff',
          top: '12px',
          right: '20px',
          fontWeight: 'bold',
          background: autoPlay ? '#2c72f0' : '#aaa',
          borderRadius: '4px',
          padding: '0 2px',
        }}>{autoPlay ? '自动' : '手动'}</span>
      </span>
    </Tooltip>
  </>;
};

export default UseTts;
