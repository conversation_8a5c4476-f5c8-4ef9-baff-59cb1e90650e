export const toCioModel = (logicalModel, options) => {
  // 没有规则子节点 返回null
  if(!logicalModel?.children?.length)  return null;

  const loop = (item, filter) => {
    if(!item) return null
    if(item.children) {
      filter.node = item.logic;
      filter.type = "logical";
      filter.children = item.children?.map(child=>loop(child, {}))
    } else {
      const op = options?.find(op=>op.value === item.key);
      filter.type = 'dimension';
      filter.param = {
        name: op?.title || item.key,
        code: op?.key || item.key,
        type: op?.dataType || 'STRING',
        defaultValue: op?.defaultValue,
        fieldSource: op?.category || 'INPUT'
      };
      filter.condition = {}
      if(item.operator){
        filter.condition[`${item.operator}`]= item.value
      }
    }
    return filter;
  }

  const res =  loop(logicalModel,{});

  return res
};


export const toLogicalModel = (cioModel) => {

  const loop = (item, filter) => {
    if(!item?.type) return null;

    if(item.type === 'logical' ) {
      filter.logic =item.node;
      filter.children = item.children?.map(child=>loop(child, {}));

    } else {
      // item.type === "dimension"
      // 获取operator 默认只会有一个operator
      if(!item?.condition) return null;
      const operator = Object.keys(item.condition)?.[0];
      filter.key = item.param.code;
      filter.operator = operator;
      filter.value = item.condition?.[operator];
    }
    return filter;
  };

  const res = loop(cioModel, {});
  return res;
}