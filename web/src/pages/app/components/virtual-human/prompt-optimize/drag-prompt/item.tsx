import { useSortable } from '@dnd-kit/react/sortable';
import { Flex } from 'antd';
import Group from '../../groups-prompt/group';
import { HolderOutlined } from '@ant-design/icons';

const Item = ({ id, column, index, data }) => {
  const { ref, handleRef } = useSortable({
    id,
    index,
    group: column,
    type: 'item',
    accept: ['item'],
    data: data
  });

  return <Flex ref={ref} style={{
    background: 'rgba(0,0,0,0.02)'
  }}>
    {/* <a ref={handleRef}>{data?.name}</a> */}
    <Flex ref={handleRef} style={{ padding: '0 6px', cursor: 'grabbing' }}>
      <HolderOutlined />
    </Flex>
    <div
      style={{
        width: '100%',
        border: '1px solid rgb(239, 240, 247)',
      }}>
      <Group value={data} isPreview />
    </div>
  </Flex>
}

export default Item;
