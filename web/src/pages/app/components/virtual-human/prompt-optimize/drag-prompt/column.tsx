import React from 'react';
import { useDroppable } from '@dnd-kit/react';
import { CollisionPriority } from '@dnd-kit/abstract';
import { Flex } from 'antd';

const styles = {
  display: 'flex',
  // flexDirection: 'column',
  // gap: 10,
  // padding: 20,
  minWidth: 200,
  // backgroundColor: 'rgba(0,0,0,0.1)',
  background: 'rgb(251, 252, 255)',
  borderRadius: 10,
  width: '100%',
  height: '100%',
};

const PromptColumn = ({ children, id, column }) => {
  const { ref } = useDroppable({
    id,
    type: 'column',
    accept: ['item'],
    // accept: (info) => {
    //   // 左边优化后的提示词，不能拖到外面去
    //   if (column === '最终提示词') {
    //     if (info.type === 'item') {
    //       return true;
    //     }
    //   }
    //   // 右边待选提示词可以随意拖动
    //   if (column === '待选提示词') {
    //     console.log('accept.type', info, 'info.data.promptType === "struct"', info.data.promptType === "struct");
    //     if (info.data.promptType === "struct" && info.type === 'item') {
    //       return true;
    //     }
    //   }
    //   return false;
    // },
    collisionPriority: CollisionPriority.Low,
  });

  return (
    <Flex vertical gap={10} style={styles} ref={ref}>
      {children}
    </Flex>
  );
}

export default PromptColumn;
