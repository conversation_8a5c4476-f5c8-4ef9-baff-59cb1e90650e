import React, { useEffect, useState } from 'react';
import { DragDropProvider } from '@dnd-kit/react';
import { move } from '@dnd-kit/helpers';

import Column from './column';
import Item from './item';
import { Flex, Tooltip } from 'antd';
import { SwapOutlined } from '@ant-design/icons';


const DragPrompt = ({ targetPrompt, seletablePrompt, onChange }) => {
  const [items, setItems] = useState({
    // A: ['A0', 'A1', 'A2'],
    // B: ['B0', 'B1'],
    最终提示词: [],
    待选提示词: []
  });

  useEffect(() => {
    const items = {
      最终提示词: targetPrompt?.groups?.map(item => {
        return {
          ...item,
        }
      }),
      待选提示词: seletablePrompt?.map(item => {
        return {
          ...item,
        }
      })
    }
    setItems(items);
    onChange?.(items);
  }, [JSON.stringify(seletablePrompt), JSON.stringify(targetPrompt?.groups)]);

  return (
    <DragDropProvider
      onDragOver={(event) => {
        const newItems = move(items, event);
        // setItems((items) => move(items, event));
        setItems(newItems);
        onChange?.(newItems);
      }}

    >
      <Flex style={{ width: '100%' }}>
        <Flex vertical flex={1}>
          <h3>已选提示词</h3>
          <Column key="最终提示词" id="最终提示词" column="最终提示词">
            {items?.最终提示词?.map((item, index) => (
              <Item key={item.id} id={item.id} index={index} data={item} column={"最终提示词"} />
            ))}
          </Column>
        </Flex>
        <Flex style={{ width: '30px', flexShrink: 0 }} align='center' justify='center'>
          <Flex style={{ height: 'fit-content' }}>
            <Tooltip title="请将【待选提示词中】需要的提示词拖入【最终提示词】，合并成最终需要的提示词">
              <a>
                <SwapOutlined style={{ fontSize: 20 }} />
              </a>
            </Tooltip>
          </Flex>
        </Flex>
        <Flex vertical flex={1}>
          <h3>可选条件提示词</h3>
          <Column key="待选提示词" id="待选提示词" column={"待选提示词"}>
            {items?.待选提示词?.map((item, index) => (
              <Item key={item.id}
                id={item.id}
                index={index}
                data={item}
                column={"待选提示词"} />
            ))}
          </Column>
        </Flex>
        {/* {Object.entries(items)?.map(([column, items]) => (
          <Flex vertical flex={1}>
            <Flex>{column}</Flex>
            <Column key={column} id={column}>
              {items?.map((item, index) => (
                <Item key={item.id} id={item.id} index={index} data={item} column={column}/>
              ))}
            </Column>
          </Flex>
        ))} */}
      </Flex>
    </DragDropProvider>
  );
}

export default DragPrompt;
