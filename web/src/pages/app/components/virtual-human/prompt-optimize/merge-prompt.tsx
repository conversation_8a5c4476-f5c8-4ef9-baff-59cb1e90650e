import { v4 as uuidv4 } from 'uuid';
import DragPrompt from "./drag-prompt";

export const splitMarkdownIntoBlocks = (val) => {
  if (!val) return;
  const lines = val.split('\n');
  const blocks = [];
  let currentBlock = null;

  for (const line of lines) {
    const titleMatch = line.match(/^#\s+(.*)/);
    if (titleMatch) {
      // 遇到新标题，将当前块保存（如果有的话）
      if (currentBlock) {
        blocks.push(currentBlock);
      }
      // 创建新块
      currentBlock = {
        title: titleMatch[1],
        content: []
      };
    } else {
      if (!currentBlock) {
        // 创建新块
        currentBlock = {
          title: undefined,
          content: []
        };
      }
      // 添加内容到当前块（如果存在块）
      if (currentBlock) {
        currentBlock.content.push(line);
      }
    }
  }

  // 添加最后一个块
  if (currentBlock) {
    blocks.push(currentBlock);
  }

  // // 将content数组转换为字符串
  // const promptList =  blocks.map(block => ({
  //   title: block.title,
  //   content: block.content.join('\n')
  // }));

  const groupPromptValue = {
    groups: blocks?.map(block => ({
      name: block.title,
      prompt: block.content.join('\n'),
      promptType: 'raw',
      id: uuidv4(),
    }))
  };
  return groupPromptValue;
};

const MergePrompt = (props) => {
  const { prompt, onChange, filterPrompt } = props;

  return <DragPrompt
    targetPrompt={splitMarkdownIntoBlocks(prompt)}
    seletablePrompt={filterPrompt}
    onChange={onChange} />
};

export default MergePrompt;
