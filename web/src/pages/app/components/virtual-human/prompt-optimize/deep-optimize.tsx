import { useRequest } from "ahooks";
import { Button, Flex, Space, Table, Tag, Tooltip } from "antd";
import TaskModal from "./evaluate/task-modal";
import { EvaluateApi } from "@/api/evaluate";
import { useQuery } from "@/hooks/useQuery";
import { useEffect, useRef, useState } from "react";
import { ReloadOutlined } from "@ant-design/icons";
import { evaluateCnMap, evaluateColorMap, IEvaluateType } from "@/interface/evaluate";
import ComparePrompt from "./compare-prompt";

const PAGE_SIZE = 12;

const DeepOptimize = (props) => {
  const { workspaceId, groupId,
    settingId, groupPrompt, onGroupPromptChange, app, setting } = props;

  const [pageInfo, setPageInfo] = useState({
    page: 1,
    pageSize: PAGE_SIZE
  });

  const [taskId, setTaskId] = useState();

  const compareRef = useRef();

  const { parsedQuery } = useQuery();
  const { appId, } = parsedQuery;

  const { data, loading, run } = useRequest(async (params?) => {

    if (!appId || !settingId) return null;

    const res = await EvaluateApi.getTaskList({
      appId,
      settingId,
      ...pageInfo,
      ...(params || {})
    });

    return res;

  }, {
    // refreshDeps: [appId, settingId],
    manual: true
  });

  useEffect(() => {
    run();
  }, []);

  const onChange = () => {
    setPageInfo({
      ...pageInfo,
      page: 1
    });

    run({
      page: 1
    });
  };

  const onRefresh = () => {
    setPageInfo({
      ...pageInfo,
      page: 1
    });

    run({
      page: 1
    });
  };

  const onCompare = (val) => {

    setTaskId(val);
    // @ts-ignore
    compareRef.current?.show();
  };

  return <>
    <Flex justify="end" style={{ marginBottom: 10 }}>
      <Space>
        <Tooltip title="刷新">
          <a onClick={onRefresh}>
            <ReloadOutlined />
          </a>
        </Tooltip>
        <TaskModal
          workspaceId={workspaceId}
          groupId={groupId}
          appId={appId}
          settingId={settingId}
          groupPrompt={groupPrompt}
          onChange={onChange}
          app={app}
          setting={setting}
        />
      </Space>
    </Flex>
    <Table
      size="small"
      loading={loading}
      columns={[
        { title: 'taskId', dataIndex: 'taskId' },
        {
          title: '优化维度', dataIndex: 'metrics', render(value, record, index) {
            const config = JSON.parse(record?.config || '{}');
            return config?.metrics?.map(metric => {
              return <Tag key={metric?.id}>{metric?.name}</Tag>
            });
          },
        },
        {
          title: '评测模型',dataIndex: 'taskEvalModels', render(value, record, index) {
            const config = JSON.parse(record?.config);
            return config?.taskEvalModels?.join(',')
          },
        },
        {
          title: '测试集', dataIndex: 'datasetName',
          render: (value, record, index) => {
            const config = JSON.parse(record?.config);
            return config?.datasetName
          },
        },
        { title: '提交时间', dataIndex: 'createTime' },
        { title: '提交人', dataIndex: 'creator' },
        {
          title: '进度', dataIndex: 'status', render(value, record, index) {
            return <Tag color={evaluateColorMap[record.status]}>{evaluateCnMap[record.status]}</Tag>
          },
        },
        {
          title: '完成时间', dataIndex: 'finishTime', render(value, record, index) {
            const config = JSON.parse(record?.config);
            return config?.finishTime;
          },
        },
        {
          title: '操作', dataIndex: 'operate',
          render: (value, record) => {
            if (+record.status !== IEvaluateType.EVALUATION_COMPLETED) return null;

            return <Button type="link" onClick={() => onCompare(record?.taskId)}>对比及评测</Button>

          }
        },
      ]}
      dataSource={data?.values as any[]}
      pagination={{
        total: data?.total,
        pageSizeOptions: [PAGE_SIZE],
        pageSize: PAGE_SIZE,
        onChange(page, pageSize) {
          setPageInfo({
            ...pageInfo,
            page
          });
          run({
            ...pageInfo,
            page,
          });
        },
      }}
    />
    <ComparePrompt taskId={taskId} ref={compareRef}
      groupPrompt={groupPrompt}
      onChange={onGroupPromptChange}
    />
  </>

};

export default DeepOptimize;
