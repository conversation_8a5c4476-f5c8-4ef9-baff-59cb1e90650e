import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tooltip } from "antd";
import { useEffect, useState } from "react";
import DeepOptimize from "./deep-optimize";
import { MagicIcon } from "@/components/icons";
import styled from "styled-components";

const StyledButton = styled(Button)`
  .magic-icon {
    margin-right: 4px;
  }
  position: relative;

  transform-origin: center;

  &.animate {
    animation: seesawLeft 0.8s ease-in-out;
    animation-iteration-count: 3;
  }

  @keyframes seesawLeft {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(-15deg); }
  }

  @keyframes seesawRight {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(15deg); }
  }
`;

const PromptOptimize = (props) => {
  const { workspaceId, groupId, appId, settingId, groupPrompt, onGroupPromptChange, app, setting } = props;

  const [open, setOpen] = useState(false);

  useEffect(() => {

    return () => {

    };
  }, []);

  const onCancel = () => {
    setOpen(false);
  };

  const onOpen = () => {
    setOpen(true);
  };

  return <>
    <Flex>
      <Tooltip title="提示词优化">
        <StyledButton
          type="link"
          size="small"
          style={{ padding: 0 }}
          onClick={onOpen}
          className="seesaw-button animate">
          <MagicIcon className="seesaw-icon" />
          <span className="magic-icon seesaw-text">优化</span>
        </StyledButton>
      </Tooltip>

    </Flex>
    <Drawer
      title="提示词优化"
      width={'100vw'}
      open={open}
      onClose={onCancel} >
      <Tabs items={[
        // { label: 'AI调优', key: 'ai' },
        {
          label: '深度调优', key: 'deep', children:
            <DeepOptimize
              workspaceId={workspaceId}
              groupId={groupId}
              appId={appId}
              settingId={settingId}
              groupPrompt={groupPrompt}
              onGroupPromptChange={val => {
                onGroupPromptChange({
                  groups: val
                });
                onCancel();
              }}
              app={app}
              setting={setting}
            />
        }
      ]} />
    </Drawer>
  </>
};

export default PromptOptimize;
