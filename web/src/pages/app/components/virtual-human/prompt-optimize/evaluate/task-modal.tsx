import { <PERSON><PERSON>ate<PERSON><PERSON> } from "@/api/evaluate";
import { useGlobalState } from "@/hooks/useGlobalState";
import { PlusOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import { <PERSON><PERSON>, Drawer, Flex, Form, Input, message, Modal, Select, Tooltip } from "antd";
import dayjs from "dayjs";
import { useEffect, useMemo, useState } from "react";
import styled from "styled-components";

const StyledForm = styled(Form)`
  .ant-form-item-label {
    width: 110px;
  }
`;

const TaskModal = (props) => {
  const { onChange, workspaceId, groupId, appId, settingId, groupPrompt, app, setting } = props;


  const [models, setModels] = useState();
  const [open, setOpen] = useState(false);

  const [form] = Form.useForm();

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const datasetType = Form.useWatch('datasetType', form);

  const prompt = useMemo(() => {
    return groupPrompt?.groups?.reduce((acc, cur) => {
      if (cur.promptType === 'struct') {
        return acc;
      }
      return acc + `# ${cur?.name || ''}` + '\n' + cur?.prompt + '\n'
    }, '');

  }, [JSON.stringify(groupPrompt)]);

  const { data: metricList } = useRequest(async () => {
    const params = {
      workspaceId,
      groupId,
      page: 1,
      pageSize: 1000,
      // 评测指标
      type: 'METRIC'
    };
    const res = await EvaluateApi.getIndicators(params);
    return res?.values;
  }, {});

  const { data: collectionList } = useRequest(async (val?) => {
    const params = {
      workspaceId,
      groupId,
      page: 1,
      pageSize: 1000,
      ...(val || {}),
      // 评测集
      type: 'COLLECTION'
    };
    const res = await EvaluateApi.getIndicators(params);
    return res?.values;
  }, {});

  const onOpen = () => {
    form.resetFields();
    if (!prompt) {
      message.warning('当前暂无普通提示词可优化！')
      return;
    }
    form.setFieldValue('prompt', prompt);

    setOpen(true);
  };

  const onOk = async () => {
    const newVal = await form.validateFields();

    const params = {
      workspaceId,
      groupId,
      appId,
      settingId,
      ...(newVal || {}),
      operator: user.name,
      // 默认传1
      type: 1

    };
    const res = await EvaluateApi.createTask(params);

    onChange?.(res?.taskId);
    form.resetFields();
    setOpen(false);
  };

  const onCancel = () => {
    form.resetFields();
    setOpen(false);
  };

  useEffect(() => {

    EvaluateApi.getModels().then(res => {
      setModels(res?.map(item => ({
        label: item.name,
        value: item.modelKey
      })));
    });

  }, []);

  const onDatasetTypeChange = (val) => {
    if (val === 2) {
      form.setFieldValue('datasetName', `${setting?.name}-${dayjs().format('YYYY-MM-DD HH:mm')}`);
    }
  };

  return <>
    <Flex>
      <Tooltip title="新建任务">
        <a onClick={onOpen}>
          <PlusOutlined />
        </a>
      </Tooltip>
    </Flex>
    <Modal
      title="新建任务"
      open={open}
      onOk={onOk}
      onCancel={onCancel}>
      <StyledForm form={form}>
        <Form.Item label="评测模型" name="evalModel" rules={[{ required: true }]}>
          <Select
            mode="multiple"
            options={models} />
        </Form.Item>
        <Form.Item label="评测集来源" name="datasetType" rules={[{ required: true }]}>
          <Select options={[
            // { label: '已有评测集', value: 1 },
            { label: 'AI生成评测集', value: 2 },
          ]}
            onChange={onDatasetTypeChange}
          />
        </Form.Item>
        {datasetType === 2 && <Form.Item label="评测集名称" name="datasetName" rules={[{ required: datasetType === 2 }]}>
          <Input maxLength={36} showCount
          />
        </Form.Item>}
        {datasetType === 1 && <Form.Item label="评测集" name="datasetId" rules={[{ required: datasetType === 1 }]}>
          <Select
            options={collectionList?.map(item => {
              return {
                label: item.name,
                value: item.indicatorId
              }
            })}
          />
        </Form.Item>}
        <Form.Item label="评测指标" name="metricIds" rules={[{ required: true }]}>
          <Select
            mode="multiple"
            options={metricList?.map(item => {
              return {
                label: item.name,
                value: item.indicatorId
              }
            })} />
        </Form.Item>
        <Form.Item label="待优化预览" name="prompt" tooltip="仅支持优化普通提示词部分"
           rules={[{ required: true }]}>
          <Input.TextArea rows={10} readOnly />
        </Form.Item>
      </StyledForm>
    </Modal>
  </>
};

export default TaskModal;