import { <PERSON><PERSON>ate<PERSON><PERSON> } from "@/api/evaluate";
import { CheckCircleOutlined, ExclamationCircleFilled } from "@ant-design/icons";
import { useRequest } from "ahooks";
import { <PERSON><PERSON>, Drawer, Flex, message, Modal, Space, Spin, Steps } from "antd";
import { forwardRef, useImperativeHandle, useMemo, useRef, useState } from "react";
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer';
import MergePrompt, { splitMarkdownIntoBlocks } from "./merge-prompt";
import styled from "styled-components";

const FullHeigthSpinContainer = styled(Flex)`
   height: 100%;
  width: 100%;
    .full-height-spin {
    width: 100%;
  }
`;

const ComparePrompt = (props, ref) => {
  const { taskId, groupPrompt, onChange } = props;
  const [open, setOpen] = useState(false);
  const [step, setStep] = useState(0);
  const [promptType, setPromptType] = useState();

  const mergedPrompt = useRef();
  const selectablePrompt = useRef();

  const { data, loading } = useRequest(async () => {
    if (!taskId) return
    const res = await EvaluateApi.getTaskReport({
      id: taskId
    });
    return res;
  }, {
    refreshDeps: [taskId]
  });


  useImperativeHandle(ref, () => {
    return {
      show: () => {
        setOpen(true);
      },
      close: () => {
        setOpen(false);
      }
    }
  });

  // 条件提示词
  const filterPrompt = useMemo(() => {

    return groupPrompt?.groups?.filter(group => group?.promptType === 'struct');
  }, [
    JSON.stringify(groupPrompt)
  ]);

  const onClose = () => {
    setPromptType(undefined);
    setStep(0);
    setOpen(false);
  };

  const onSelectPrompt = (val) => {
    setPromptType(val);
    const res = splitMarkdownIntoBlocks(data?.[val]);
    if (filterPrompt?.length > 0) {
      // @ts-ignore
      mergedPrompt.current = res?.groups;
      setStep(1);
      return;
    }
    message.success('已采用该提示词');

    // 如果没有条件提示词，直接提交
    onChange?.(res?.groups);
    onClose?.();
  };

  const onStepChange = val => {

    if (val === 0) {
    }

    if (val === 1) {
      if (!promptType) {
        message.error('请选择需要合并的提示词')
        return
      }
    }
    setStep(val);
  };

  const onMergedPromptChange = val => {
    mergedPrompt.current = val?.最终提示词;
    selectablePrompt.current = val?.待选提示词;
  };

  const onSubmit = () => {
    console.log('selectablePrompt', selectablePrompt.current);
    // @ts-ignore
    if (selectablePrompt.current?.length > 0) {

      Modal.confirm({
        title: '未合并的条件提示词',
        content: '存在未合并的条件提示词，确认提交吗？提交后未合并的条件提示词会在现有提示词中删除。',
        icon: <ExclamationCircleFilled />,
        onOk: () => {
          onChange?.(mergedPrompt.current);
          onClose?.();
          setTimeout(() => {
            message.success('合并提示词成功');
          }, 500);

        },
        onCancel: () => { },
        okText: '提交',
        zIndex: 9999
      })
    } else {
      onChange?.(mergedPrompt.current);
      onClose?.();
      setTimeout(() => {
        message.success('合并提示词成功');
      }, 500);
    }
  };

  return <>
    <Drawer title="对比及评测"
      width={'100vw'}
      open={open} onClose={onClose}
      footer={(filterPrompt?.length > 0
        ? <Flex justify="end">
          {step === 0 && <Button type="primary" disabled={!promptType}
            onClick={() => onStepChange(1)}>下一步</Button>
          }
          {step === 1 && <Space>
            <Button onClick={() => onStepChange(0)}>上一步</Button>
            <Button type="primary" onClick={onSubmit}>提交</Button>
          </Space>}
        </Flex>
        : null)}>
      <Flex vertical style={{ height: '100%' }}>
        {filterPrompt?.length > 0 ? <Steps
          current={step}
          onChange={onStepChange}
          items={[
            { title: '选择提示词' },
            filterPrompt?.length > 0 ? { title: '合并提示词' } : null
          ]?.filter(item => item)
          }
          style={{ marginBottom: 20 }}
        /> : null}
        <FullHeigthSpinContainer flex={1}>
          {step === 0 && <Spin wrapperClassName="full-height-spin"
            spinning={loading}
            style={{ height: '100%', width: '100%' }}>
            <ReactDiffViewer
              oldValue={data?.prompt}
              newValue={data?.finalPrompt}
              leftTitle={<Flex justify="space-between">
                优化前提示词
                {promptType === 'prompt' ? <a style={{ cursor: 'default' }}><CheckCircleOutlined />  已选中</a> :
                  <Button type="primary" onClick={() => onSelectPrompt('prompt')}>选这个</Button>}
              </Flex>}
              rightTitle={<Flex justify="space-between">
                优化后提示词
                {promptType === 'finalPrompt' ? <a style={{ cursor: 'default' }}><CheckCircleOutlined />  已选中</a> :
                  <Button type="primary" onClick={() => onSelectPrompt('finalPrompt')}>选这个</Button>}
              </Flex>}
            />
          </Spin>}
          {step === 1 && <MergePrompt
            // @ts-ignore
            prompt={data?.[promptType]}
            filterPrompt={filterPrompt}
            groupPrompt={groupPrompt}
            onChange={onMergedPromptChange}
          />}
        </FullHeigthSpinContainer>
      </Flex>
    </Drawer >
  </>
};

export default forwardRef(ComparePrompt);
