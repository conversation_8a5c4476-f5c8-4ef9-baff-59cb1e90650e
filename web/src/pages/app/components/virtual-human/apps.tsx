import React, { useContext, useMemo } from 'react';
import { HolderOutlined } from '@ant-design/icons';
import { DndContext } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Table } from 'antd';
import CharacterItem from './filter-item';
const RowContext = React.createContext({});
const DragHandle = () => {
  // @ts-ignore
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{
        cursor: 'move',
      }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};
const columns = [
  {
    key: 'sort',
    align: 'center',
    width: 20,
    render: () => <DragHandle />,
  },
  {
    title: 'content',
    dataIndex: 'content',
    render: (val, record) => {
      return record.name
      return <CharacterItem value={record} onChange={() => { }} />
    }
  },
];

const Row = (props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  });

  const style = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging
      ? {
        position: 'relative',
        zIndex: 9999,
      }
      : {}),
  };
  const contextValue = useMemo(
    () => ({
      setActivatorNodeRef,
      listeners,
    }),
    [setActivatorNodeRef, listeners],
  );
  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};
const Apps = (props) => {
  const { value, onChange } = props;

  const onDragEnd = ({ active, over }) => {
    if (active.id !== over?.id) {
      const activeIndex = active.id;
      const overIndex = over.id;
      const newItems = arrayMove([...value], activeIndex, overIndex);
      onChange(newItems);
    }
  };

  return (
    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext items={value.map((item, i) => i)} strategy={verticalListSortingStrategy}>
        <Table
          rowKey={(rec, index) => {
            return index;
          }}
          components={{
            body: {
              row: Row,
            },
          }}
          // @ts-ignore
          columns={columns}
          dataSource={value}
          size='small'
          pagination={false}
        />
      </SortableContext>
    </DndContext>
  );
};
export default Apps;