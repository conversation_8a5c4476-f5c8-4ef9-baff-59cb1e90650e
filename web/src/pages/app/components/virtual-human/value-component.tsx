import { Select } from "antd";

const NUMBER_OPTIONS = [
  { label: '等于', value: 'eq' },
  { label: '大于', value: 'gt' },
  { label: '小于', value: 'lt' },
  { label: '大于等于', value: 'ge' },
  { label: '小于等于', value: 'le' },
  { label: '不等于', value: 'ne' },
  { label: '包含', value: 'contain' },
  { label: '不包含', value: 'notcontain' },
  { label: '范围', value: 'range' },
];

const STRING_OPTIONS = [
  { label: '等于', value: 'eq' },
  { label: '不等于', value: 'ne' },
  { label: '包含(精准匹配)', value: 'contain', desc: '精准匹配' },
  { label: '不包含(精准匹配)', value: 'notcontain', desc: '精准匹配' },
  { label: '包含(模糊匹配)', value: 'contain_fuzzy', desc: '模糊匹配' },
  { label: '不包含(模糊匹配)', value: 'notcontain_fuzzy', desc: '模糊匹配' },
];

const DATE_OPTIONS = [
  // { label: '等于', value: 'eq' },
  // { label: '大于', value: 'gt' },
  // { label: '小于', value: 'lt' },
  // { label: '大于等于', value: 'ge' },
  // { label: '小于等于', value: 'le' },
  // { label: '不等于', value: 'ne' },
  // { label: '范围', value: 'range' },
  // { label: '星期', value: 'range' },
  { label: '范围', value: 'cycle', type: 'date' },
  { label: '包含', value: 'contain', type: 'date' },
  { label: '不包含', value: 'notcontain', type: 'date' },
];

const BOOL_OPERATORS = [
  { label: '等于', value: 'eq' },
  { label: '不等于', value: 'ne' },
];

export const operatorOptions = {
  string: STRING_OPTIONS,
  number: NUMBER_OPTIONS,
  date: DATE_OPTIONS,
  bool: BOOL_OPERATORS
};

const TagsSelect = (props) => {
  return <Select
    mode="tags"
    style={{ width: 160 }} {...props} />
};

export default {
  TagsSelect
};
