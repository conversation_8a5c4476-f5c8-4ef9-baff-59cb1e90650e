import React, { useEffect, useState } from 'react';
import { Select, Space, DatePicker, TimePicker, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { InfoCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';

const { Option } = Select;

const typeLabel = {
  year: '年',
  month: '月',
  day: '日',
  hour: '时',
  minute: '分',
  second: '秒'
};

const format = 'HH:mm:ss';

const TimeRangeSelector = (props) => {
  const { onChange: onParentChange, value, isPreview } = props;
  const [startTime, endTime] = value || [];

  const formatValue = (cycle) => {
    return cycle?.map((date: any = {}) => {
      if (!date) return '/'

      let res = date.year || '-';

      if (date.month) {
        res = res + `/${dayjs().month(date.month - 1).format('MM')}`
      } else {
        res = res + `/-`
      }

      if (date.day) {
        res = res + `/${dayjs().date(date.day).format('DD')}`
      } else {
        res = res + `/-`
      }
      const hasTime = date?.hour || date?.minute || date?.second;
      const timeStr = `${date?.hour || `00`}:${date?.minute || `00`}:${date?.second || `00`}`;

      res = res + ` ${hasTime ? dayjs(timeStr, format).format(format) : ''}`
      return res
    });
  }


  const onChange = (start, end) => {
    const cycle = [start, end].map((time = {}) => ({
      year: time.year || null,
      month: time.month || null,
      day: time.day || null,
      hour: time.hour || null,
      minute: time.minute || null,
      second: time.second || null
    }));
    onParentChange(cycle);
  };

  const setStartTime = start => {
    onChange(start, endTime)
  };

  const setEndTime = end => {
    onChange(startTime, end)
  };

  const updateTime = (type, value, isStart) => {
    const updateFunc = isStart ? setStartTime : setEndTime;
    const prev = isStart ? startTime : endTime;
    updateFunc({ ...prev, [type]: value });
  };

  const onTimeChange = (value, isStart) => {
    const updateFunc = isStart ? setStartTime : setEndTime;
    const prev = isStart ? startTime : endTime;

    const hour = value?.get('hour') || 0;
    const minute = value?.get('minute') || 0;
    const second = value?.get('second') || 0;
    updateFunc({ ...prev, hour, minute, second });
  }

  const generateOptions = (start, end) => {
    return Array.from({ length: end - start + 1 }, (_, i) => i + start);
  };

  const renderSelector = (type, options, isStart) => (
    <Select
      size='small'
      style={{ width: 56 }}
      value={isStart ? startTime?.[type] : endTime?.[type]}
      onChange={(value) => updateTime(type, value, isStart)}
      placeholder={typeLabel[type]}
      allowClear
    >
      {options.map(value => (
        <Option key={value} value={value}>{value}</Option>
      ))}
    </Select>
  );

  const renderTimeGroup = (isStart) => {
    const timeValue = isStart ? startTime : endTime;
    const formattedTime = `${timeValue?.hour || `00`}:${timeValue?.minute || `00`}:${timeValue?.second || `00`}`;

    const hasTime = timeValue?.hour || timeValue?.minute || timeValue?.second

    return (
      <Space.Compact>
        <DatePicker
          size='small'
          picker="year"
          value={timeValue?.year ? dayjs(timeValue.year, 'YYYY') : undefined}
          style={{ width: 68 }}
          onChange={(date, dateString) => updateTime('year', dateString, isStart)}
          placeholder="年"
          allowClear
        />
        {renderSelector('month', generateOptions(1, 12), isStart)}
        {renderSelector('day', generateOptions(1, 31), isStart)}
        <TimePicker
          size="small"
          format={format}
          value={hasTime ? dayjs(formattedTime, format) as any : undefined}
          onChange={val => {
            onTimeChange(val, isStart);
          }}
          style={{ width: 90 }}
        />
        {/* {renderSelector('hour', generateOptions(0, 23), isStart)}
      {renderSelector('minute', generateOptions(0, 59), isStart)}
      {renderSelector('second', generateOptions(0, 59), isStart)} */}
      </Space.Compact>
    )
  };

  if (isPreview) {
    const previewValue = formatValue(value);
    return `${previewValue?.[0]} ~ ${previewValue?.[1]}`
  }


  return (
    <div
      style={{
        background: '#f8f8f8',
        display: 'flex',
        justifyContent: 'center',
        padding: '4px 0',
        ...props.style,
      }}
    >
      <Space direction="vertical">
        <Space>
          <span style={{ fontSize: '12px' }}>
            开始时间 <Tooltip
              color="#fff"
              overlayInnerStyle={{ width: 640 }}
              title={<img width="600"
                src="https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/57311962736/28a9/652d/78d8/664bfb886d19c85c39e7485828d06940.png" />}>
              <QuestionCircleOutlined size={10} />
            </Tooltip>:
          </span>
          {renderTimeGroup(true)}
        </Space>
        <Space>
          <span style={{ fontSize: '12px' }}>
            结束时间 <Tooltip
              color="#fff"
              overlayInnerStyle={{ width: 640 }}
              title={<img width="600"
                src="https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/57311962736/28a9/652d/78d8/664bfb886d19c85c39e7485828d06940.png" />}>
              <QuestionCircleOutlined size={10} />
            </Tooltip>:
          </span>
          {renderTimeGroup(false)}
        </Space>

      </Space>
      {/* <Space>
        <Tooltip title={<img width="600"
          src="https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/57311264200/34f3/4797/40f2/207c2e278ad2d58d09bf8e35cf8c0025.png" />}>
          <InfoCircleOutlined style={{ fontSize: '14px' }} />
        </Tooltip>
      </Space> */}
    </div >
  );
};

export default TimeRangeSelector;