import { Collapse, Typography } from "antd";
import { useState } from "react";

const { Paragraph } = Typography;


const ExpandableParagraph = ({ children, ...rest }) => {
  const [expanded, setExpanded] = useState(false);
  return <Paragraph
    ellipsis={{
      rows: 2,
      // @ts-ignore
      expandable: 'collapsible',
      expanded,
      // @ts-ignore
      onExpand: (_, info) => setExpanded(info.expanded),
    }} {...rest}>
    {children}

  </Paragraph>
}

export default ExpandableParagraph;
