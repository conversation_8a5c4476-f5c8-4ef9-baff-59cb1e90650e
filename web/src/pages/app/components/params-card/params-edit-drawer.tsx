import { But<PERSON>, Drawer, message, Space } from "antd";
import { ParamsEditor } from "./params-editor";
import { useEffect, useState } from "react";
import { ParamDataTypeEnum } from "@/interface/agent";
import { omit } from "lodash";
import { request } from '@/utils/request';
import { EnvApi } from '@/api/env';


const INPUT = 'INPUT';

interface IProps {
  notAllowClears: string[];
  value: any[];
  onChange: (value: any) => void;
  onClose: () => void;
  open: boolean;
  appType: string;
  appId: string;
}


const ParamsEditDrawer = (props: IProps) => {
  const { notAllowClears, value, onChange, onClose, open, appType, appId } = props;
  const [varMap, setVarMap] = useState({});
  const [dataSource, setDataSource] = useState([]);
  const [defaultExpandedRowKeys, setDefaultExpandedRowKeys] = useState(['INPUT']);

  useEffect(() => {
    if (!appType || !appId) {
      return;
    }
    EnvApi.getNonUserVariables(appType, appId).then(res => {
      const resMap = res?.variables?.reduce((acc, cur) => {
        if (!acc[cur.category]) {
          acc[cur.category] = {
            category: cur.category,
            group: cur.group,
            options: []
          }
        }
        acc[cur.category].options.push(cur);
        return acc;
      }, {});

      setVarMap(resMap);
    });
  }, [appType, appId]);

  const formatterValue = () => {

    const varTypes = {};

    const userList = value?.filter(item => {
      // 接口返回的就是非用户输入变量
      if (varMap[item.category]) {

        if (!varTypes[item.category]) {
          varTypes[item.category] = {};
        }
        varTypes[item.category][item.key] = true;
        return false;
      };

      return true;
    });


    const categoryList = Object.keys(varTypes);

    const expandedKeys = ['INPUT'];
    categoryList.forEach(category => {
      if (Object.keys(varTypes[category]).length > 0) {
        expandedKeys.push(category);
      }
    });

    setDefaultExpandedRowKeys(expandedKeys);

    const data = [{
      key: INPUT,
      title: '自定义变量',
      columnType: 'group',
      children: userList?.map(item => ({
        ...item,
        category: INPUT
      })),
    }];

    Object.keys(varMap).forEach(category => {

      const children = varMap?.[category].options?.map(item => {
        item.type = item.dataType === ParamDataTypeEnum.time ? 'date' : 'input';
        item.config = {
          ...(item.config || {})
        }
        if (varTypes?.[category]?.[item.key]) {
          return {
            checked: true,
            ...item,
          }
        }
        return item;
      })

      if (children?.length) {
        data.unshift({
          key: category,
          title: varMap?.[category]?.group,
          columnType: 'group',
          children
        });
      }

    });
    setDataSource(data);
  };

  useEffect(() => {
    if (open) {
      formatterValue();
    }
  }, [open]);

  const onOk = () => {
    const inputValue = dataSource?.find(group => group.key === INPUT)?.children || [];
    if (inputValue?.findIndex((it) => it.key === '') !== -1) {
      // 当出现多个 key 为空的行时，在拖动排序时会出现 Bug，此处通过交互限制避免此Bug
      message.warning('请先填写完当前参数');
      return;
    }

    const newValue = dataSource?.reduce((acc, cur) => {
      if (cur.key !== INPUT) {
        cur?.children?.filter(item => item.checked)?.map(item => {
          acc.push(omit(item, ['checked']))
        })
      } else {
        cur?.children?.forEach(item => acc.push(item))
      }
      return acc;

    }, []);
    onChange?.(newValue);
    onClose();
  };

  const onCancel = () => {
    onClose();
  };

  const onParamsChange = (val) => {
    setDataSource(val);
  };

  return <Drawer
    title="变量"
    maskClosable={false}
    open={open}
    onClose={onCancel}
    width={800}
    extra={<Space>
      <Button onClick={onCancel}>取消</Button>
      <Button type="primary" onClick={onOk}>确定</Button>
    </Space>}
    autoFocus={true}
  >
    <ParamsEditor
      onChange={onParamsChange}
      dataSource={dataSource}
      defaultExpandedRowKeys={defaultExpandedRowKeys}
      notAllowClears={notAllowClears}
    />
  </Drawer>

};

export default ParamsEditDrawer;
