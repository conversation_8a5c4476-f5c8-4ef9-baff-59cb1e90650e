import {
  MinusCircleOutlined,
  PlusOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { Button, Checkbox, Flex, Input, message, Select, Space, Switch, Table, Tooltip } from 'antd';
import { useState } from 'react';
import styled from 'styled-components';
import { IAgentParam, ParamDataTypeEnum, ParamDataTypeMap, ParamTypeEnum, ParamTypeIconMap } from '@/interface/agent';
import { ParamsEditModal, ParamTypeRender } from '../agent-dev/mods/params-edit-modal';

interface IParamsEditorProps {
  onChange: (value: IAgentParam[]) => void;
  dataSource: any[];
  defaultExpandedRowKeys?: string[];
  notAllowClears?: string[];
}
const INPUT = 'INPUT';

export function ParamsEditor(props: IParamsEditorProps) {
  const { dataSource, defaultExpandedRowKeys, notAllowClears } = props;

  const [editIndex, setEditIndex] = useState(-1);
  const [editParam, setEditParam] = useState<IAgentParam>();

  const onEditParam = (index, record) => {
    if (index === -1) {
      setEditIndex(-1);
      setEditParam(null);
      return;
    }
    setEditIndex(index);
    setEditParam(record);
  }

  const onAddClick = () => {
    const inputList = dataSource?.find(item => item.key === INPUT)?.children || [];

    if (inputList.findIndex((it) => it.key === '') !== -1) {
      // 当出现多个 key 为空的行时，在拖动排序时会出现 Bug，此处通过交互限制避免此Bug
      message.warning('请先填写完当前参数');
      return;
    }

    const newData = dataSource.map(group => {
      if (group.key === INPUT) {
        return {
          ...group,
          children: (group.children || []).concat({
            type: ParamTypeEnum.textInput,
            category: INPUT,
            dataType: 'STRING',
            key: '',
            title: '',
            required: false,
            config: {
              length: 48,
              options: [],
            },
          })
        }
      } else {
        return group;
      }
    });
    props.onChange(newData);
  };

  // 用户自定义变量才可以删除
  const onItemDelete = (index: number) => {

    const newData = dataSource.map(group => {
      if (group.key === INPUT) {
        return {
          ...group,
          children: group.children.filter((item, i) => {
            return i !== index
          })
        }
      } else {
        return group;
      }
    });
    props.onChange(newData);
  };

  const onItemChange = (index: number, newValue, record: any) => {
    // 没有categoryd的话默认是用户输入变量
    let category = record.category || INPUT;
    const newData = dataSource.map(group => {
      if (group.key === category) {
        return {
          ...group,
          children: group.children.map((item, i) => {
            if (index === i) {
              return {
                ...item,
                ...newValue
              }
            }
            return item;
          })
        }
      } else {
        return group;
      }
    });
    props.onChange(newData);
  }

  const onModalSave = (newEditParam: IAgentParam) => {
    if (editIndex !== -1) {
      onItemChange(editIndex, newEditParam, editParam);
      onEditParam(-1, null);
      return;
    }
    onEditParam(-1, null);
  };

  const getSystemVariable = record => {
    return record.category && record.category !== INPUT;
  }

  const columns = [
    {
      title: <Flex>
        <span >
          变量 KEY
        </span>
        <Tooltip title="命名规则：系统变量以_开头，且不允许修改;自定义变量不允许以_开头">
          <InfoCircleOutlined style={{ marginLeft: '6px' }} />
        </Tooltip>
      </Flex>,
      dataIndex: 'key',
      key: 'key',
      render: (key: string, record: IAgentParam, index: number) => {
        // const notUserInput = [SYSTEM, TENANT].includes(record.category);
        const notUserInput = getSystemVariable(record);

        if (record.columnType === 'group') return <strong>{record.title}</strong>;

        // 非用户自定义变量需要勾选后才可生效
        let newContent = key;

        return (
          <ParamKeyInput style={{ color: notUserInput && !record.checked ? '#ddd' : 'initial' }}>
            <span style={{ fontSize: '12px' }}>{ParamTypeIconMap[record.type]}</span>
            <Input
              readOnly={notUserInput}
              style={{ color: notUserInput && !record.checked ? '#ddd' : 'initial' }}
              placeholder="key"
              bordered={false}
              defaultValue={newContent}
              onChange={(e) => {
                newContent = e.target.value;
              }}
              onBeforeInput={e => {
                // 非系统变量不能以_开头
                // @ts-ignore
                if (e.target.selectionStart === 0 && e.data === '_') {
                  e.preventDefault();
                }
              }}
              onBlur={() => {
                // if (record.category === SYSTEM) {
                //   onSysItemChange(record.category, record.key, 'key', newContent);
                //   return;
                // }
                // onUserItemChange(index, 'key', newContent);
                onItemChange(index, { 'key': newContent }, record);
              }}
            />
          </ParamKeyInput>
        );
      },
    },
    {
      title: '变量名称',
      dataIndex: 'title',
      key: 'title',
      render: (title: string, record: IAgentParam, index: number) => {
        let newContent = title;
        // const notUserInput = [SYSTEM, TENANT].includes(record.category)

        const notUserInput = getSystemVariable(record);

        if (record.columnType === 'group') return null;
        return (
          <Input
            readOnly={notUserInput}
            style={{ color: notUserInput && !record.checked ? '#ddd' : 'initial' }}
            placeholder="title"
            bordered={false}
            defaultValue={newContent}
            onChange={(e) => {
              newContent = e.target.value;
            }}
            onBlur={() => {
              onItemChange(index, { 'title': newContent }, record);
            }}
          />
        );
      },
    },
    {
      title: '变量类型',
      dataIndex: 'dataType',
      width: 100,
      render: (val: string, record: IAgentParam, index: number) => {
        const notUserInput = getSystemVariable(record);

        // 系统组名不做操作
        if (record.columnType === 'group') return null;

        if (notUserInput) {
          return <span style={{ color: notUserInput && !record.checked ? '#ddd' : 'initial' }}>
            {ParamDataTypeMap[val]?.label || ParamDataTypeMap[ParamDataTypeEnum.string].label}
          </span>
        }

        return <Select
          value={ParamDataTypeMap[val]?.label || ParamDataTypeMap[ParamDataTypeEnum.string].label}
          size='small'
          bordered={false}
          style={{ width: '80px' }}
          options={Object.keys(ParamDataTypeMap).map(key => ({
            ...ParamDataTypeMap[key]
          }))}
          onChange={val => {
            const newValue: any = {
              dataType: val,
            }
            // 时间
            if (val === ParamDataTypeEnum.time) {
              newValue.type = ParamTypeEnum.date
            }
            if (val === ParamDataTypeEnum.bool) {
              newValue.type = ParamTypeEnum.boolean
            }
            if ([ParamDataTypeEnum.long, ParamDataTypeEnum.integer, ParamDataTypeEnum.double].includes(val as ParamDataTypeEnum)) {
              newValue.type = ParamTypeEnum.number
            }

            onItemChange(index, newValue, record)
          }}
        />
      }
    },
    {
      title: '输入组件',
      dataIndex: 'type',
      render: (val, rec, index) => {
        // 系统组名不做操作
        if (rec.columnType === 'group') return null;
        return <Select
          bordered={false}
          size='small'
          style={{ width: '80px' }}
          popupMatchSelectWidth={false}
          value={val}
          onChange={v => onItemChange(index, { type: v }, rec)}
          options={
            ParamTypeRender.filter(item => {
              // 时间类型只能返回时间
              if (rec.dataType === ParamDataTypeEnum.time) {
                return item.param_type === ParamTypeEnum.date;
              } else if ([ParamDataTypeEnum.integer, ParamDataTypeEnum.long, ParamDataTypeEnum.double].includes(rec.dataType as ParamDataTypeEnum)) {
                // 整型、长整型
                return item.param_type === ParamTypeEnum.number;
              } else if (ParamDataTypeEnum.bool === rec.dataType) {
                return item.param_type === ParamTypeEnum.boolean;
              } else {
                return [ParamTypeEnum.select, ParamTypeEnum.textArea, ParamTypeEnum.textInput, ParamTypeEnum.file].includes(item.param_type);
              }
            }).map(item => {
              return {
                label: item.param_type_name,
                value: item.param_type
              }
            })
          }
        >
        </Select>
      }
    },
    // {
    //   title: '可选',
    //   dataIndex: 'required',
    //   render: (required: boolean, record: IAgentParam, index: number) => {
    //     const notUserInput = [SYSTEM, TENANT].includes(record.category)

    //     // 系统组名不做操作
    //     if (record.columnType === 'group') return null;
    //     return (
    //       <Switch
    //         disabled={notUserInput && !record.checked}
    //         checked={!required}
    //         onChange={(v) => {
    //           // if (notUserInput) {
    //           //   onSysItemChange(record.category, record.key, 'key', !v);
    //           //   return;
    //           // }
    //           // onUserItemChange(index, 'required', !v);
    //           onItemChange(index, { 'required': !v }, record);
    //         }}
    //       />
    //     );
    //   },
    //   key: 'required',
    // },
    {
      title: '操作',
      key: 'param_operate',
      render: (_: any, record: IAgentParam, index: number) => {
        // 系统组名不做操作
        if (record.columnType === 'group') return null;
        // const notUserInput = [SYSTEM, TENANT].includes(record.category);

        const notUserInput = getSystemVariable(record);

        const notAllowClear = notAllowClears?.includes(record.key);

        // 非输入变量
        if (notUserInput) {
          return <Space>
            <Tooltip title={record.checked ? "禁用变量" : "启用变量"}>
              <Switch checked={record.checked}
                onChange={(val) => onItemChange(index, { 'checked': val }, record)} />
            </Tooltip>
            {record.checked && <SettingOutlined rev={undefined} onClick={() => onEditParam(index, record)} />}
          </Space>
        }

        return (
          <Space>
            <SettingOutlined rev={undefined} onClick={() => onEditParam(index, record)} />
            <Tooltip title={notAllowClear ? '该变量已经被提示词引用，若要删除，请先解除引用' : ''}>
              <MinusCircleOutlined
                style={{ color: notAllowClear ? '#ddd' : 'initial' }}
                rev={undefined} onClick={() => {
                  if (notAllowClear) return;
                  onItemDelete(index)
                }} />
            </Tooltip>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Table
        size='small'
        dataSource={dataSource}
        columns={columns}
        expandable={{
          defaultExpandedRowKeys
        }}
      />
      <Flex style={{ marginTop: '10px' }}>
        <Button type="default" style={{ width: '100%' }} onClick={onAddClick} icon={<PlusOutlined />}>添加自定义变量</Button>
      </Flex>
      <ParamsEditModal
        editParam={editParam}
        onSave={onModalSave}
        onCancel={() => {
          onEditParam(-1, null);
        }}
      />
    </div>
  );
}



const ParamKeyInput = styled.div`
  display: flex;
  align-items: center;
  .ant-input {
    padding-left: 4px;
  }
`;
