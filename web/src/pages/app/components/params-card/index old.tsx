import { Space, Tag, message, Drawer, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Flex } from "antd";
import { CloseCircleOutlined, CloseOutlined, EditOutlined } from '@ant-design/icons';
import { ParamsEditor } from "./params-editor";
import { useEffect, useState } from "react";
import { request } from '@/utils/request';
import CardCollapse from "../agent-dev/mods/card-collapse";
import { omit } from "lodash";
import { ParamDataTypeEnum } from "@/interface/agent";
import DiffAlert from "../agent-dev/mods/diff-alert";

const online = window.location.host === 'langbase.netease.com';
const TENANT = 'TENANT';
const SYSTEM = 'SYSTEM';
const INPUT = 'INPUT';


const ParamsCard = (props) => {
  const { value, oldValue, onChange, appId, appType, notAllowClears } = props;

  const [open, setOpen] = useState(false);
  const [systemOption, setSysOption] = useState([]);
  const [tenantOption, setTenantOption] = useState([]);

  const [dataSource, setDataSource] = useState([]);
  const [defaultExpandedRowKeys, setDefaultExpandedRowKeys] = useState(['INPUT']);

  const setOptions = (system, tenant) => {
    setSysOption(system);
    setTenantOption(tenant);
  }

  useEffect(() => {
    if (!appType) {
      setOptions([], []);
      return;
    }
    request({
      // baseURL,
      method: 'POST',
      url: `/app/${appId}/agent-variables`,
      data: {
        appType,
        appId
      }
    }).then(res => {
      setOptions(res.system, res.tenant);
    });
  }, []);

  const onParamsChange = (val) => {
    setDataSource(val);
  };

  const onOk = () => {
    const systemValue = dataSource?.find(group => group.key === SYSTEM)?.children?.filter(item => item.checked)?.map(item => omit(item, ['checked'])) || [];
    const tenantValue = dataSource?.find(group => group.key === TENANT)?.children?.filter(item => item.checked)?.map(item => omit(item, ['checked'])) || [];
    const inputValue = dataSource?.find(group => group.key === INPUT)?.children || [];
    if (inputValue?.findIndex((it) => it.key === '') !== -1) {
      // 当出现多个 key 为空的行时，在拖动排序时会出现 Bug，此处通过交互限制避免此Bug
      message.warning('请先填写完当前参数');
      return;
    }
    const newValue = [...systemValue, ...tenantValue, ...inputValue];
    onChange?.(newValue);
    setOpen(false);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const formatterValue = () => {
    // 系统变量
    const systemValue = {};
    // 租户变量
    const tenantValue = {};

    const userList = value?.filter(item => {
      if (item.category === SYSTEM) {
        systemValue[item.key] = item;
        return false;
      }
      if (item.category === TENANT) {
        tenantValue[item.key] = item;
        return false;
      }
      return true;
    });

    const expandedKeys = ['INPUT'];
    if (Object.keys(systemValue)?.length > 0) {
      expandedKeys.push(SYSTEM)
    }
    if (Object.keys(tenantValue)?.length > 0) {
      expandedKeys.push(TENANT)
    }

    setDefaultExpandedRowKeys(expandedKeys);

    // 系统变量
    const systemList = systemOption?.map(item => {
      item.type = item.dataType === ParamDataTypeEnum.time ? 'date' : 'input';
      if (systemValue[item.key]) {
        return {
          config: {},
          ...item,
          checked: true,
        }
      }
      return item;
    });


    // 租户变量
    const tenantList = tenantOption?.map(item => {
      item.type = item.dataType === ParamDataTypeEnum.time ? 'date' : 'input';
      if (tenantValue[item.key]) {
        return {
          config: {},
          ...item,
          checked: true
        }
      }
      return item;
    });

    const data = [];
    if (systemList?.length) {
      data.push({
        key: SYSTEM,
        title: '系统变量',
        columnType: 'group',
        children: systemList
      })
    }
    if (tenantList?.length) {
      data.push({
        key: TENANT,
        title: '租户变量',
        columnType: 'group',
        children: tenantList
      })
    }
    data.push({
      key: INPUT,
      title: '自定义变量',
      columnType: 'group',
      children: userList,
    })
    setDataSource(data);
  };

  const onOpen = () => {
    formatterValue();
    setOpen(true);
  };

  const onDeleteTag = (e, variable) => {
    e.preventDefault();
    const newValue = value?.filter(item => item.key !== variable.key);
    onChange?.(newValue);
  };


  const validValue = value?.filter(v => v.title || v.key);

  // return <ParamsCard
  //   systemOption={systemOption}
  //   tenantOption={tenantOption}
  //   value={paramsList}
  // />
  return <CardCollapse
    items={[{
      label: <span>
        变量
        <DiffAlert
          type="paramsInPrompt"
          title="变量"
          newValue={value}
          oldValue={oldValue}
          onRedo={() => {
            onChange?.(oldValue);
          }}
        />
      </span>,
      extra: <Space size="small" onClick={e => {
        e.stopPropagation();
        onOpen()
      }} style={{ cursor: 'pointer' }}>
        <EditOutlined />
        <span>编辑</span>
      </Space>,
      children: <div>
        {validValue?.map(variable => {
          const closable = !notAllowClears?.includes(variable.key);
          if (!closable) {
            return <Tooltip title={closable ? '' : '该变量已经被提示词引用，若要删除，请先解除引用'}>
              <Tag
                key={variable.key}
                style={{ cursor: 'pointer' }}
                onClick={onOpen}
                closable={closable}
                // onClose={e => onDeleteTag(e, variable)}
                onClose={e => e.preventDefault()}
                closeIcon={closable
                  ? <CloseOutlined />
                  : <Popconfirm title="该变量已经被提示词引用，删除后将失效，是否继续？" onConfirm={(e) => {
                    onDeleteTag(e, variable)
                  }}>
                    <CloseOutlined />
                  </Popconfirm>}
              >{variable?.title ? `${variable.title}(${variable.key})` : variable.key}
              </Tag>

            </Tooltip>
          }
          return <Tag
            key={variable.key}
            style={{ cursor: 'pointer' }}
            onClick={onOpen}
            closable={closable}
            onClose={e => onDeleteTag(e, variable)}
          >{variable?.title ? `${variable.title}(${variable.key})` : variable.key}
          </Tag>
        })}

        <Drawer
          title="变量"
          maskClosable={false}
          open={open}
          onClose={onCancel}
          width={800}
          extra={<Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" onClick={onOk}>确定</Button>
          </Space>}
        >
          <ParamsEditor
            onChange={onParamsChange}
            dataSource={dataSource}
            defaultExpandedRowKeys={defaultExpandedRowKeys}
            notAllowClears={notAllowClears}
          />
        </Drawer>
      </div>
    }]} />
};


export default ParamsCard;
