import React, { useState } from 'react';
import CardCollapse from '../agent-dev/mods/card-collapse';
import { Button, Flex, Image, Popconfirm, Space, Tag, Tooltip } from 'antd';
import { CloseOutlined, InfoCircleOutlined, PlusOutlined } from '@ant-design/icons';
import DiffAlert from '../agent-dev/mods/diff-alert';
import ParamsEditDrawer from './params-edit-drawer';

const NewParamsCard = (props) => {
  const { appType, appId, value, notAllowClears, onChange, oldValue, expandLocalKey, disabled } = props;
  const [open, setOpen] = useState(false);

  const onOpen = () => {
    setOpen(true);
  };

  const onDeleteTag = (e, variable) => {
    e.preventDefault();
    const newValue = value?.filter((item) => item.key !== variable.key);
    onChange?.(newValue);
  };

  const onOk = (newValue) => {
    onChange?.(newValue);
    setOpen(false);
  };

  const validValue = value?.filter((v) => v.title || v.key);

  return (
    <CardCollapse
      expandLocalKey={expandLocalKey}
      moduleKey="paramsInPrompt"
      collapsible={disabled ? "disabled" : 'icon'}
      items={[
        {
          key: 'paramsInPrompt',
          label: (
            <Space>
              <span style={{ fontWeight: 'bold' }}>变量</span>
              <DiffAlert
                type="paramsInPrompt"
                title="变量"
                newValue={value}
                oldValue={oldValue}
                onRedo={() => {
                  onChange?.(oldValue);
                }}
              />
              <Tooltip
                overlayInnerStyle={{ width: '316px' }}
                title={<Flex vertical style={{ width: '100%' }} gap={10}>
                  用于保存用户个人信息，让智能体记住用户的特征，使回复更加个性化。<br />
                  在提示词中可输入 {'`{{`'} 触发变量选择，直接引用变量。
                  <Image width={300}
                    src='https://p5.music.126.net/obj/wonDlsKUwrLClGjCm8Kx/59869209075/0a29/f063/b862/d4218df6766507c2e0551e1c2ff15ae6.png' />
                </Flex>}>
                <InfoCircleOutlined />
              </Tooltip>
            </Space>
          ),
          extra: !disabled && (
            <Tooltip title='添加变量'>
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined style={{ fontSize: 10, color: '#666' }} />}
                onClick={(e) => {
                  e.stopPropagation();
                  onOpen();
                }}
              />
            </Tooltip>
          ),
          children: (
            <div>
              {validValue?.length ? (
                validValue.map((variable) => {
                  const closable = !notAllowClears?.includes(variable.key);
                  if (!closable) {
                    return (
                      <Tooltip
                        title={
                          closable ? '' : '该变量已经被提示词引用，若要删除，请先解除引用'
                        }
                      >
                        <Tag
                          key={variable.key}
                          style={{ cursor: 'pointer' }}
                          onClick={onOpen}
                          closable={closable}
                          onClose={(e) => e.preventDefault()}
                          closeIcon={
                            closable ? (
                              <CloseOutlined />
                            ) : (
                              <Popconfirm
                                title="该变量已经被提示词引用，删除后将失效，是否继续？"
                                onConfirm={(e) => {
                                  onDeleteTag(e, variable);
                                }}
                              >
                                <CloseOutlined />
                              </Popconfirm>
                            )
                          }
                        >
                          {variable?.title
                            ? `${variable.title}(${variable.key})`
                            : variable.key}
                        </Tag>
                      </Tooltip>
                    );
                  }
                  return (
                    <Tag
                      key={variable.key}
                      style={{ cursor: 'pointer' }}
                      onClick={onOpen}
                      closable={closable}
                      onClose={(e) => onDeleteTag(e, variable)}
                    >
                      {variable?.title
                        ? `${variable.title}(${variable.key})`
                        : variable.key}
                    </Tag>
                  );
                })
              ) : (
                <span style={{ color: '#999' }}>
                  用于保存用户个人信息，让智能体记住用户的特征，使回复更加个性化。
                </span>
              )}

              <ParamsEditDrawer
                open={open}
                onClose={() => {
                  setOpen(false);
                }}
                value={value}
                onChange={onOk}
                appType={appType}
                appId={appId}
                notAllowClears={notAllowClears}
              />
            </div>
          ),
        },
      ]}
    />
  );
};

export default NewParamsCard;
