// @ts-nocheck
import { <PERSON>, Col, DatePicker, Flex, Row, Spin } from 'antd';
import { useState } from 'react';

import { LayoutContainer } from '@/components/layout-container';
import { useGlobalState } from '@/hooks/useGlobalState';

const { RangePicker } = DatePicker;

import { useRequest } from 'ahooks';
import { Switch, Table } from 'antd';
import type { TimeRangePickerProps } from 'antd/es/date-picker';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import ReactJson from 'react-json-view';

dayjs.extend(customParseFormat);

const isDev = window.location.host.includes('dev');

import { AppApi } from '@/api/app';

interface DataType {
  endTime: any;
  startTime: any;
  inputs: object;
  outputs: object;
  message: string;
  status: any;
  key: React.Key;
  name: string;
  age: number;
  address: string;
  description: string;
}

import { useQuery } from '@/hooks/useQuery';
import { PreviewButton } from '@music/ct-langbase';

export function WorkflowLogPage() {
  const { globalState } = useGlobalState();
  const { app } = globalState;

  if (!app) {
    return <Spin />;
  }
  return <History appID={app.id}></History>;
}

const rangePresets: TimeRangePickerProps['presets'] = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs()] },
  { label: '最近1小时', value: [dayjs().add(-1, 'h'), dayjs()] },
  { label: '最近4小时', value: [dayjs().add(-4, 'h'), dayjs()] },
  { label: '最近两天', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '最近三天', value: [dayjs().add(-2, 'd'), dayjs()] },
];

export function History(props) {
  const { appID } = props;
  const [date, setDate] = useState([]);
  const [range, setRange] = useState([]);
  const [debug, setDebug] = useState(false);
  const [current, setCurrent] = useState(1);
  const { debug: isDebug } = useQuery();
  const { data: res, run } = useRequest(
    (page = 1) => {
      setCurrent(page);
      return AppApi.getWorkFlowRunList(
        appID,
        debug,
        range[0] ? range[0].valueOf() : undefined,
        range[1] ? range[1].valueOf() : undefined,
        10,
        page,
      );
    },
    {
      refreshDeps: [appID, range, debug],
    },
  );

  const { data: histories } = useRequest(
    () => {
      return AppApi.getAppHistory(appID);
    },
    {
      refreshDeps: [appID],
    },
  );

  console.log('data', range, res, histories);

  const columns: ColumnsType<DataType> = [
    {
      title: 'runId',
      dataIndex: 'runId',
      key: 'runId',
    },
    {
      title: 'workflowId',
      dataIndex: 'flowId',
      key: 'flowId',
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (v) => dayjs(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      render: (v) => {
        if (v) {
          return dayjs(v).format('YYYY-MM-DD HH:mm:ss');
        }
        return '--';
      },
    },
    {
      title: '耗时',
      dataIndex: 'endTime',
      key: 'endTime',
      render: (_, record) => {
        if (record.endTime) {
          return `${record.endTime - record.startTime}ms`;
        }
        return '--';
      },
    },
    { title: '状态', dataIndex: 'status', key: 'status', width: 20 },
  ];

  const disabledDate = (current: Dayjs) => {
    if (!date) {
      return;
    }
    const tooLate = date[0] && current.diff(date[0], 'days') >= 3;
    const tooEarly = date[1] && date[1].diff(current, 'days') >= 2;
    // console.log("cur", current, range);
    return !!tooEarly || !!tooLate;
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      setDate([null, null]);
    } else {
      setDate(null);
    }
  };

  let env = isDev ? 'dev' : 'online';
  if (window.location.href.includes('onlinetest')) {
    env = 'test'
  }
  const aioBase = env === 'online' ? 'https://music-cms.hz.netease.com' : 'https://cms.qa.igame.163.com'

  return (
    <Card style={{ userSelect: 'text' }}>
      <div style={{ marginBottom: '20px', width: '100%' }}>
        <h3 style={{ display: 'inline', marginRight: '10px' }}>运行记录</h3>
      </div>
      <Flex justify="space-between" align="center">
        <RangePicker
          showTime
          value={date || range}
          presets={rangePresets}
          onCalendarChange={(r) => {
            if (r) {
              setDate(r);
            }
          }}
          onChange={(r) => {
            if (r) {
              setRange(r);
            }
          }}
          onOpenChange={onOpenChange}
          disabledDate={disabledDate}
        // disabledDateTime={disabledDateTime}
        />
        <Switch
          style={{ marginLeft: '10px' }}
          checkedChildren="正式"
          unCheckedChildren="调试"
          defaultChecked
          onChange={(v) => setDebug(!v)}
        />
      </Flex>
      <Table
        rowKey={'runId'}
        style={{ marginTop: '10px' }}
        columns={columns}
        pagination={{ current, total: res?.total || 1, onChange: run }}
        expandable={{
          expandedRowRender: (record) => (
            <Row>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>输入</h3>
                <ReactJson
                  theme="summerfruit:inverted"
                  name={false}
                  style={{
                    maxWidth: '20vw',
                  }}
                  displayDataTypes={false}
                  onSelect={(v) => console.log('v', v)}
                  iconStyle="square"
                  collapsed={2}
                  src={record.inputs}
                />
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>输出</h3>
                <ReactJson
                  theme="summerfruit:inverted"
                  name={false}
                  style={{
                    maxWidth: '20vw',
                  }}
                  displayDataTypes={false}
                  onSelect={(v) => console.log('v', v)}
                  iconStyle="square"
                  collapsed={2}
                  src={record.outputs}
                />
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>状态</h3>
                <ReactJson
                  theme="summerfruit:inverted"
                  name={false}
                  style={{
                    maxWidth: '20vw',
                  }}
                  displayDataTypes={false}
                  onSelect={(v) => console.log('v', v)}
                  iconStyle="square"
                  collapsed={2}
                  src={{
                    message: record?.message || '',
                    status: record?.status,
                  }}
                />
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>工作流详情</h3>
                <PreviewButton debug={debug} appId={appID} runId={record.runId} env={env}></PreviewButton>
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>调试详情</h3>
                <a target="_blank" href={`${aioBase}/produce-center/workflow/instance/detail?id=${record.runId}`}>AIO详情</a>
              </Col>
              <Col span={4} style={{ overflow: 'hidden' }}>
                <h3>调试详情</h3>
                <a target="_blank" href={`${aioBase}/produce-center/workflow/instance/detail?id=${record.runId}`}>AIO详情</a>
              </Col>
            </Row>
          ),
          rowExpandable: (record) => record.name !== 'Not Expandable',
        }}
        dataSource={res?.items || []}
      />
    </Card>
  );
}
