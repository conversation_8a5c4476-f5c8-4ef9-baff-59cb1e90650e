import { Button, Flex, Form, Input, message, Popconfirm, Tabs } from "antd";
import { useState } from "react";
import { Emoji<PERSON><PERSON> } from "@/api/emoji";
import { AppApi } from "@/api/app";
import { useRequest } from 'ahooks';



const Account = (props) => {
  const { appId } = props;
  const [form] = Form.useForm();
  const [accountType, setAccountType] = useState<string>('MUSIC');

  useRequest(() => {
    return AppApi.getAccountList({ appId }).then(res => res?.accountInfoList)
  }, {
    refreshDeps: [appId],
    onSuccess: (res) => {
      if (res?.length) {
        form.setFieldsValue(res?.find(v => v.accountType === accountType))
      }
    }
  });

  const id = Form.useWatch('id', form);

  const onSubmit = async (e) => {
    try {
      const values = await form.validateFields();
      console.log('values', values);
      AppApi.configAccount({
        ...values,
        appId,
        accountType,
      }).then(res => {
        message.success('保存成功');
      });
    } catch (error) {
    }
  }

  const tabList = [{
    key: 'MUSIC',
    label: '云音乐账号',
    children: (
      <>
        <Form
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 10 }}
          form={form}>
          <Form.Item name="id" hidden />
          <Form.Item
            label="账号id"
            name="accountId"
            validateDebounce={1000}
            hasFeedback
            rules={[{
              validator: async (rule, value, callback) => {
                if (!value) {
                  throw new Error('请输入账号');
                }
                if (!/^\d+$/.test(value)) {
                  throw new Error('请输入数字');
                }
                const res = await EmojiApi.getAccount({ userId: value });

                if (res?.userId) {
                  callback(res.nickName);
                  return;
                }
                throw new Error('账号不存在');
              },
            }]}

          >
            {id ? form.getFieldValue('accountId') : <Input placeholder="请输入账号" />}
          </Form.Item>
          <Form.Item label="描述" name="description">
            {id ? form.getFieldValue('description') : <Input.TextArea />}
          </Form.Item>

        </Form>
        {!id && <>
          <Flex justify="end">
            <Popconfirm
              title="账号一旦绑定无法更换和删除，确认提交？"
              onConfirm={onSubmit}>

              <Button type="primary" >确认</Button>
            </Popconfirm>
          </Flex>
        </>}
      </>
    )
  }];

  return <div style={{ margin: '0 12px' }}>
    <Tabs
      items={tabList}
      activeKey={accountType}
      onChange={val => setAccountType(val)}
    />

  </div>
};

export default Account;
