import React, { useEffect, useState } from 'react';
import {
  Dropdown,
  Flex,
  Form,
  Image,
  Input,
  message,
  Select,
  Space,
  Table,
  Tag,
} from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { AnimationApi } from '@/api';
import { getAppId } from '@utils/state';
import { useForm } from 'antd/es/form/Form';
import CreateAnimationModal from './components/create-animation-modal';
import PreviewVedio from './components/edit-animation/preview-vedio';
import MetaModal from './components/meta-modal';
import AnimationDrawer from './components/animation-drawer';
import { useGlobalState } from '@hooks/useGlobalState';
import styled from 'styled-components';
import VideoPreview from '@/components/video-preview';
import { useLocalStorage } from '@/hooks/useStorage';
import DraftCycle from './draft-cycle';
import HeadPortraitSelectModal from './components/head-portrait-select-modal';
import { AnimationEnum } from './type';
import UploadVedio from './components/upload-vedio';
import DefaultVedioVersion from './components/default-vedio-version';
import { render } from 'react-dom';

const PAGE_SIZE = 10;

interface Props {
  appId: string;
  rowSelection?: any;
  tableProps?: Record<string, any>;
  queryData?: Record<string, any>;
}

/** 循环动画 */
const Cycle: React.FC<Props> = ({ appId, rowSelection, tableProps, queryData = {} }) => {
  const [animationList, setAnimationList] = useState([]);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [tagList, setTagList] = useState([]);

  const { globalState } = useGlobalState();
  const [form] = useForm();

  const localKey = `app-${appId}-animation-CYCLE`;
  const [storage] = useLocalStorage(localKey);

  const isSelectMode = typeof rowSelection !== "undefined"

  useEffect(() => {
    getAnimationList(current, form.getFieldsValue());
    getTagList();
  }, []);

  const getTagList = async () => {
    try {
      const list = await AnimationApi.getTagList({
        appId: appId || getAppId(),
        type: 'CYCLE',
      });
      setTagList(list || []);
    } catch (err: any) {
      message.error(`获取tags失败, ${err.message}`);
    }
  };

  // 获取动画列表
  const getAnimationList = async (pageNum: number, data: Record<string, any>) => {
    try {
      setLoading(true);
      const { values, total } = await AnimationApi.getCycleList({
        appId: appId || getAppId(),
        type: 'CYCLE',
        ...data,
        pageNum,
        pageSize: PAGE_SIZE,
        ...(queryData || {})
      });
      const formatValues = values.map((item: Record<string, any>) => {
        return {
          ...item,
          assetInfo: {
            ...JSON.parse(item.assetInfo || '{}'),
            workflow: JSON.parse(JSON.parse(item.assetInfo || '{}')?.workflow || '{}'),
          },
        }
      });
      setTotal(total);
      setAnimationList(formatValues);
    } catch (err: any) {
      message.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const onFormChange = async (_, values: Record<string, any>) => {
    setCurrent(1);
    setAnimationList([]);
    setTotal(0);
    await getAnimationList(1, values);
  };

  const onCreateAnimation = () => {
    getAnimationList(1, form.getFieldsValue());
  };

  // 修改动画基本信息
  const handleEditBasicInfo = async (info: Record<string, any>) => {
    try {
      await AnimationApi.updateAnimationInfo({
        appId: appId || getAppId(),
        ...info,
        operator: globalState.user.email?.split('@')?.[0],
      });
      setAnimationList((prev) =>
        prev.map((i) => {
          if (info.id !== i.id) return i;
          return {
            ...i,
            tag: info.tag,
            name: info.name,
            assetInfo: {
              ...i.assetInfo,
              desc: info.desc,
            },
          };
        }),
      );
      message.success('修改成功');
    } catch (err: any) {
      message.error(`修改失败, ${err.message}`);
    }
  };

  const tableColumns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '首尾帧形象',
      dataIndex: 'headPortraitUrl',
      key: 'headPortraitUrl',
      render: (url) => <Image src={url} height={100} />,
    },
    {
      title: '动画',
      dataIndex: 'assetInfo',
      key: 'assetInfo',
      render: (info) => {
        return <VideoPreview
          src={info?.mainUrl}
          previewImgUrl={info?.workflow?.nodes?.[0]?.content?.url}
          height={100} />
      },
    },
    {
      title: '标签',
      key: 'tag',
      dataIndex: 'tag',
      render: (_, { tag }) => (
        <>
          {tag.split(',').map((t) => (
            <Tag key={t}>{t}</Tag>
          ))}
        </>
      ),
    },
    {
      title: '动画描述',
      dataIndex: 'desc',
      key: 'desc',
      render: (_, record) => <div>{record.assetInfo.desc}</div>,
    },
    isSelectMode ? null : {
      title: '是否有本地草稿',
      dataIndex: 'status',
      render: (_, record) => {
        const workflowId = record?.assetInfo?.workflow?.id;
        if (storage?.[workflowId]?.updatedTime) {
          return <Tag color="processing">是</Tag>;
        }
        return <Tag color="success">否</Tag>;
      },
    },
    {
      title: '默认版本',
      dataIndex: 'mainDuration',
      render: (_, record) => { 
        const { assetInfo } = record || {};
        return assetInfo?.mainDuration
      }
    },
    isSelectMode ? null : {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        const { id, name, assetInfo, tag, type } = record || {};
        return (
          <Dropdown
            trigger={['hover']}
            menu={{
              // @ts-ignore
              items: [
                {
                  key: 'previewVideo',
                  style: { padding: 0 },
                  label: (
                    <PreviewVedio
                      animationType={AnimationEnum.循环动画}
                      trigger={
                        <DropdownItemContainer>预览分时动画</DropdownItemContainer>
                      }
                      // value={[assetInfo?.versions]}
                      getVideos={async () => {
                        const res = await AnimationApi.previewPartAnimation({
                          appId,
                          id
                        });
                        return res
                      }}
                    />
                  ),
                },
                {
                  type: 'divider',
                },
                {
                  key: 'uploadVedio',
                  style: { padding: 0 },
                  label: <UploadVedio
                    trigger={<DropdownItemContainer>上传分时动画</DropdownItemContainer>}
                    appId={appId}
                    animationId={record?.id}
                  />
                },
                {
                  type: 'divider',
                },
                {
                  key: 'defaultVersion',
                  style: { padding: 0 },
                  label: <DefaultVedioVersion
                    trigger={<DropdownItemContainer>设置默认版本</DropdownItemContainer>}
                    appId={appId}
                    animationId={record?.id}
                  />
                },
                assetInfo?.workflow?.id ? {
                  type: 'divider',
                } : null,
                assetInfo?.workflow?.id ? {
                  key: 'updateAnimation',
                  style: { padding: 0 },
                  label: (
                    <AnimationDrawer
                      showTrigger
                      trigger={<DropdownItemContainer>编辑动画</DropdownItemContainer>}
                      animationType="CYCLE"
                      appId={appId}
                      value={{
                        // 基本信息
                        id: record?.id,
                        name,
                        tag,
                        desc: assetInfo?.desc,

                        // workflow
                        workflowId: assetInfo?.workflow?.id,
                        // nodes用接口获取
                        // nodes: assetInfo?.workflow?.nodes,
                      }}
                    />
                  ),
                } : null,
                {
                  type: 'divider',
                },
                // {
                //   key: 'viewTransitionAnimation',
                //   style: { padding: 0 },
                //   label: <DropdownItemContainer>查看可过渡动画</DropdownItemContainer>,
                // },
                // {
                //   type: 'divider',
                // },
                {
                  key: 'updateBasicInfo',
                  style: { padding: 0 },
                  label: (
                    <MetaModal
                      editType="edit"
                      trigger={
                        <DropdownItemContainer>修改基本信息</DropdownItemContainer>
                      }
                      value={{
                        id: id,
                        name: name,
                        desc: assetInfo?.desc,
                        tag: tag,
                        type: type,
                      }}
                      onChange={handleEditBasicInfo}
                    />
                  ),
                },
              ]?.filter(item => item),
            }}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ].filter(item => item);

  return (
    <>
      <Flex justify="space-between" align="center">
        <Form layout="inline" form={form} onValuesChange={onFormChange}>
          <Form.Item name="name" label="名称">
            <Input placeholder="请输入设置名称" allowClear />
          </Form.Item>

          <Form.Item name="tag" label="标签">
            <Select
              showSearch
              allowClear
              placeholder="请选择动画标签"
              tagRender={({ label, closable, onClose }) => (
                <Tag closable={closable} onClose={onClose} style={{ marginInlineEnd: 4 }}>
                  {label}
                </Tag>
              )}
              style={{ minWidth: 150 }}
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              options={tagList.map((i) => ({ label: i, value: i }))}
            />
          </Form.Item>

          <Form.Item name="headPortraitId" label="首尾帧形象">
            <HeadPortraitSelectModal
              appId={appId}
              onSelect={(item) => {
                form.setFieldValue('headPortraitId', item.id);
                onFormChange(null, { ...form.getFieldsValue(), headPortraitId: item.id });
              }}
              onCancel={() => {
                form.setFieldValue('headPortraitId', undefined);
                onFormChange(null, {
                  ...form.getFieldsValue(),
                  headPortraitId: undefined,
                });
              }}
            />
          </Form.Item>
        </Form>

        {!isSelectMode && <CreateAnimationModal
          appId={appId}
          animationType={AnimationEnum.循环动画}
          onChange={onCreateAnimation}
        />}
      </Flex>

      <Table
        rowKey="id"
        style={{ marginTop: 20 }}
        columns={tableColumns}
        dataSource={animationList}
        onChange={(pagination) => {
          setCurrent(pagination.current);
          getAnimationList(pagination.current, form.getFieldsValue());
        }}
        pagination={{
          total,
          pageSize: PAGE_SIZE,
          current,
        }}
        loading={loading}
        rowSelection={rowSelection}
        {...tableProps}
      />
      <DraftCycle appId={appId} onChange={onCreateAnimation} />
    </>
  );
};

const DropdownItemContainer = styled.div`
  display: flex;
  align-items: center;
  padding: 4px 12px;
  transition: background 0.2s;
`;

export default Cycle;
