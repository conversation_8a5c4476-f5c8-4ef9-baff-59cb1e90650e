import { useState } from 'react';
import {
  Image,
  Table,
  TableProps,
  Tag,
  FloatButton,
  Drawer,
  Flex,
  Button,
} from 'antd';
import { useLocalStorage } from '@/hooks/useStorage';
import { clearLocalWorkFlow } from './utils';
import CreateAnimationModal from './components/create-animation-modal';
import { AnimationEnum } from './type';


const DraftCycle = (props) => {
  const { appId, onChange } = props;
  const [open, setOpen] = useState(false);

  const localKey = `app-${appId}-animation-CYCLE`;
  const [storage] = useLocalStorage(localKey);

  const localList = Object.keys(storage || {}).filter(workflowId => {
    if (storage[workflowId]?.id) {
      // 已经有id的不用单独展示
      return false
    }
    return true;
  })?.map((workflowId) => {
    return {
      ...(storage[workflowId] || {}),
    };
  });


  const tableColumns: TableProps<Record<string, any>>['columns'] = [
    // {
    //   title: 'id',
    //   dataIndex: 'id',
    //   key: 'id',
    // },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '首尾帧形象',
      dataIndex: 'headPortraitUrl',
      key: 'headPortraitUrl',
      render: (url, record) => <Image src={record?.nodes?.[0]?.content?.url} height={100} />,
    },
    // {
    //   title: '动画',
    //   dataIndex: 'assetInfo',
    //   key: 'assetInfo',
    //   render: (info) => <VideoPreview src={info?.mainUrl} height={100} />,
    // },
    {
      title: '标签',
      key: 'tag',
      dataIndex: 'tag',
      render: (_, { tag }) => (
        <>
          {tag?.split(',').map((t) => (
            <Tag key={t}>{t}</Tag>
          ))}
        </>
      ),
    },
    {
      title: '动画描述',
      dataIndex: 'desc',
      key: 'desc',
      render: (_, record) => <div>{record.desc}</div>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (_) => {
        return <Tag color="processing">草稿</Tag>
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        const { name, tag, desc, workflowId, nodes } = record || {};
        return <Flex vertical>
          <CreateAnimationModal
            trigger={<Button type='link'>编辑</Button>}
            animationType={AnimationEnum.循环动画}
            appId={appId}
            value={{
              // 基本信息
              // id: record?.id,
              name,
              tag,
              desc: desc,

              // workflow
              workflowId,
              nodes: nodes,
            }}
            onChange={onChange}
          />
          <Flex>
            <Button type='text' danger onClick={() => {
              clearLocalWorkFlow(localKey, {
                workflowId: record?.workflowId
              })
            }}>删除</Button>
          </Flex>

        </Flex>
      },
    },
  ];

  const onClose = () => {
    setOpen(false);
  };

  const onOpen = () => {
    setOpen(true);
  };

  return <>
    <Drawer title="循环动画草稿箱" width={800} open={open} onClose={onClose}>
      <Table columns={tableColumns} dataSource={localList} />
    </Drawer>
    {localList?.length > 0 ? <FloatButton tooltip="草稿箱，从未提交到服务器的数据" onClick={onOpen} /> : null}
  </>
};

export default DraftCycle;