export enum AnimationEnum {
  循环动画 = 'CYCLE',
  过渡动画 = 'TRANSITIONAL',
}

enum NodeEnum {
  image = 'IMAGE_FRAME',
  animation = 'ANIMATION',
}
interface IVedioContent {
  nosKey: string;
  url: string;
  width: number;
  height: number;
}

export interface IImageContent {
  nosKey: string;
  url: string;
  width: number;
  height: number;
  id?: number;
}

interface IAnimationNode {
  content: IVedioContent | IImageContent;
  type: NodeEnum;
}

interface IWorkflow {
  id: string;
  node: [];
}

export enum ITaskStatusType {
  排队中 = 0,
  成功 = 1,
  失败 = 2,
  执行中 = 3,
  // QUEUEING (0, "排队中"),
  // SUCCESS(1, "成功"),
  // FAIL(2, "失败"),
  // RUNNING(3, "执行中");
}

export interface IWorkflowRequest {
  name: string;

  // workflowId
  id: string;

  appId: string;
  operator: string;
  type: AnimationEnum;
  tag?: string;
  desc?: string;
  workflow: IWorkflow;
}
