
import { useState } from 'react';
import {
  Image,
  Table,
  TableProps,
  FloatButton,
  Drawer,
  Flex,
  Button,
} from 'antd';
import VideoPreview from '@/components/video-preview';
import { useLocalStorage } from '@/hooks/useStorage';
import { clearLocalWorkFlow } from './utils';
import { AnimationEnum } from './type';
import CreateAnimationModal from './components/create-animation-modal';


const DraftTransitional = (props) => {
  const { appId, onChange } = props;
  const [open, setOpen] = useState(false);

  const localKey = `app-${appId}-animation-TRANSITIONAL`;
  const [storage] = useLocalStorage(localKey);

  const localList = Object.keys(storage || {}).filter(workflowId => {
    if (storage[workflowId]?.id) {
      // 已经有id的不用单独展示
      return false
    }
    return true;
  })?.map((workflowId) => {
    return {
      ...(storage[workflowId] || {}),
    };
  });


  const tableColumns: TableProps<Record<string, any>>['columns'] = [
    {
      title: 'workflowId',
      dataIndex: 'workflowId',
      key: 'workflowId',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'desc',
      key: 'desc',
    },
    {
      title: '动画场景1',
      dataIndex: 'startAssetInfo',
      key: 'startAssetInfo',
      render: (info, record) => {
        return <Image src={record?.nodes?.[0]?.content?.url} height={100} />
      },
    },
    {
      title: '动画场景2',
      dataIndex: 'endAssetInfo',
      key: 'endAssetInfo',
      render: (info, record) => {
        return <Image src={record?.nodes?.[2]?.content?.url} height={100} />
      },
    },
    {
      title: '1=>2',
      dataIndex: 'startToEndAssetInfo',
      key: 'startToEndAssetInfo',
      render: (info, record) => record?.nodes?.[1]?.content?.url && <VideoPreview src={record?.nodes?.[1]?.content?.url} height={100} />,
    },
    {
      title: '2=>1',
      dataIndex: 'endToStartAssetInfo',
      key: 'endToStartAssetInfo',
      render: (info, record) => record?.nodes?.[3]?.content?.url && <VideoPreview src={record?.nodes?.[3]?.content?.url} height={100} />,
    },
    // {
    //   title: '动画描述',
    //   dataIndex: 'desc',
    //   key: 'desc',
    //   render: (_, record) => <div>{record.startAssetInfo.desc}</div>,
    // },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        const { workflowId, nodes } = record || {};
        return <Flex vertical>
          <CreateAnimationModal
            trigger={<Button type='link'>编辑</Button>}
            animationType={AnimationEnum.过渡动画}
            appId={appId}
            value={{
              // name,
              // tag,
              // desc: desc,

              // workflow
              workflowId,
              nodes,
            }}
            onChange={onChange}
          />
          <Button danger type='text'
            onClick={() => {
              clearLocalWorkFlow(localKey, {
                workflowId: record?.workflowId
              })
            }}>删除</Button>
        </Flex>
      },
    },
  ];

  const onClose = () => {
    setOpen(false);
  };

  const onOpen = () => {
    setOpen(true);
  };

  return <>
    <Drawer title="过渡动画草稿箱" width={800} open={open} onClose={onClose}>
      <Table columns={tableColumns} dataSource={localList} />
    </Drawer>
    {localList?.length > 0 ? <FloatButton tooltip="草稿箱，从未提交到服务器的数据" onClick={onOpen} /> : null}
  </>
};

export default DraftTransitional;