import { getLocalData, saveLocalData } from '@/utils/common';
import { v4 as uuidv4 } from 'uuid';

export const generateNodeId = () => {
  const uuids = uuidv4().split('-');
  return `${uuids[0]}-${uuids[1]}`;
};

export const getLocalKey = ({ appId, animationType }) => {
  if (!animationType || !appId) return;
  return `app-${appId}-animation-${animationType}`;
};

export const updateLocalWorkflow = (localKey, workflow) => {
  const _workflowId = workflow?.workflowId;

  const localData = getLocalData(localKey);

  saveLocalData(localKey, {
    ...localData,
    [_workflowId]: {
      ...(localData?.[_workflowId] || {}),
      ...(workflow || {}),

      // 更新时间
      updatedTime: new Date().valueOf(),
    },
  });
};

export const clearLocalWorkFlow = (localKey, workflow?) => {
  const _workflowId = workflow?.workflowId;
  if(!_workflowId) return;

  const localData = getLocalData(localKey);

  delete localData[_workflowId];

  saveLocalData(localKey, localData);
};
