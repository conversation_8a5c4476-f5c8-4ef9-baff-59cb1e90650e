import React, { useEffect, useState } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { AnimationEnum } from './type';
import { Button, Dropdown, Flex, Form, Input, message, Space, Table, TableProps } from 'antd';
import { AnimationApi } from '@/api';
import { useForm } from 'antd/es/form/Form';
import CreateAnimationModal from '@/pages/app/components/resource/animation/components/create-animation-modal';
import styled from 'styled-components';
import VideoPreview from '@components/video-preview';
import DraftTransitional from './draft-transitional';
import HeadPortraitSelectModal from './components/head-portrait-select-modal';
import { useGlobalState } from '@/hooks/useGlobalState';
import AnimationDrawer from '@/pages/app/components/resource/animation/components/animation-drawer';
import { getAppId } from '@utils/state';

const PAGE_SIZE = 5;

interface Props {
  appId: string;
}

/** 循环动画 */
const Transitional: React.FC<Props> = ({ appId }) => {
  const [animationList, setAnimationList] = useState([]);
  const [current, setCurrent] = useState(1);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const [form] = useForm();
  const { globalState } = useGlobalState();

  useEffect(() => {
    getAnimationList(current, form.getFieldsValue());
  }, []);

  const getAnimationList = async (pageNum: number, data: Record<string, any>) => {
    const { values } = await AnimationApi.getTransitionalList({
      appId: appId || getAppId(),
      // appId: 'f9b122a3-493c-4370-adfd-17d41e2fb7bb',
      type: 'TRANSITIONAL',
      ...data,
      pageNum,
      pageSize: 10,
    });
    const formatValues = values.map((item: Record<string, any>) => ({
      ...item,
      endAssetInfo: {
        ...JSON.parse(item.endAssetInfo),
        workflow: JSON.parse(JSON.parse(item.endAssetInfo).workflow),
      },
      endToStartAssetInfo: {
        ...JSON.parse(item.endToStartAssetInfo),
        workflow: JSON.parse(JSON.parse(item.endToStartAssetInfo).workflow),
      },
      startAssetInfo: {
        ...JSON.parse(item.startAssetInfo),
        workflow: JSON.parse(JSON.parse(item.startAssetInfo).workflow),
      },
      startToEndAssetInfo: {
        ...JSON.parse(item.startToEndAssetInfo),
        workflow: JSON.parse(JSON.parse(item.startToEndAssetInfo).workflow),
      },
    }));
    console.log(1111, 'formatValues', formatValues);
    setAnimationList(formatValues);
  };

  const onFormChange = async (_, values: Record<string, any>) => {
    setCurrent(1);
    setAnimationList([]);
    setTotal(0);
    await getAnimationList(1, values);
  };

  // 修改动画基本信息
  const handleEditBasicInfo = async (info: Record<string, any>) => {
    try {
      await AnimationApi.updateAnimationInfo({
        appId: appId || getAppId(),
        // appId: 'f9b122a3-493c-4370-adfd-17d41e2fb7bb',
        ...info,
        operator: globalState.user.email?.split('@')?.[0],
      });
      setAnimationList((prev) =>
        prev.map((i) => {
          if (info.id !== i.id) return i;
          return {
            ...i,
            tag: info.tag,
            name: info.name,
            assetInfo: {
              ...i.assetInfo,
            },
          };
        }),
      );
      message.success('修改成功');
    } catch (err: any) {
      message.error(`修改失败, ${err.message}`);
    }
  };

  const onCreateAnimation = () => {
    getAnimationList(1, form.getFieldsValue());
  };

  const tableColumns: TableProps<Record<string, any>>['columns'] = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '动画场景1',
      dataIndex: 'startAssetInfo',
      key: 'startAssetInfo',
      render: (info) => (
        <VideoPreview
          src={info?.mainUrl}
          previewImgUrl={info?.workflow?.nodes?.[0]?.content?.url}
          height={100}
        />
      ),
    },
    {
      title: '动画场景2',
      dataIndex: 'endAssetInfo',
      key: 'endAssetInfo',
      render: (info) => (
        <VideoPreview
          src={info?.mainUrl}
          previewImgUrl={info?.workflow?.nodes?.[0]?.content?.url}
          height={100}
        />
      ),
    },
    {
      title: '1=>2',
      dataIndex: 'startToEndAssetInfo',
      key: 'startToEndAssetInfo',
      render: (info) => (
        <VideoPreview
          src={info?.mainUrl}
          previewImgUrl={info?.workflow?.nodes?.[0]?.content?.url}
          height={100}
        />
      ),
    },
    {
      title: '2=>1',
      dataIndex: 'endToStartAssetInfo',
      key: 'endToStartAssetInfo',
      render: (info) => {
        console.log('infoinfo', info);
        return (
          <VideoPreview
            src={info?.mainUrl}
            previewImgUrl={info?.workflow?.nodes?.[0]?.content?.url}
            height={100}
          />
        )
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        const { id, name, endAssetInfo, tag, type, startToEndAssetInfo } = record || {};
        return <AnimationDrawer
          showTrigger
          trigger={<Button type="link">编辑动画</Button>}
          animationType={AnimationEnum.过渡动画}
          appId={appId}
          value={{
            // 基本信息
            id: record?.id,
            name,
            tag,

            // workflow
            workflowId: startToEndAssetInfo?.workflow?.id,
            nodes: startToEndAssetInfo?.workflow?.nodes,
          }}
        />
        return (
          <Dropdown
            menu={{
              items: [
                {
                  key: 'editAnimation',
                  style: { padding: 0 },
                  label: (
                    <AnimationDrawer
                      showTrigger
                      trigger={<DropdownItemContainer>编辑动画</DropdownItemContainer>}
                      animationType={AnimationEnum.过渡动画}
                      appId={appId}
                      value={{
                        // 基本信息
                        id: record?.id,
                        name,
                        tag,

                        // workflow
                        workflowId: startToEndAssetInfo?.workflow?.id,
                        nodes: startToEndAssetInfo?.workflow?.nodes,
                      }}
                    />
                  ),
                },
              ],
            }}
            trigger={['click']}
          >
            <a onClick={(e) => e.preventDefault()}>
              <Space>
                操作
                <DownOutlined />
              </Space>
            </a>
          </Dropdown>
        );
      },
    },
  ];

  return (
    <>
      <Flex justify="space-between" align="center">
        <Form layout="inline" form={form} onValuesChange={onFormChange}>
          <Form.Item name="name" label="名称">
            <Input placeholder="请输入设置名称" />
          </Form.Item>

          <Form.Item name="startAnimationId" label="动画场景1">
            <HeadPortraitSelectModal
              title="动画场景1"
              appId={appId}
              onSelect={(item) => {
                if (item.id && item === form.getFieldValue('endAnimationId')) {
                  message.warning('动画场景1和动画场景2不能选择相同的场景');
                  form.setFieldValue('startAnimationId', null);
                  return;
                }
                form.setFieldValue('startAnimationId', item.id);
                onFormChange(null, {
                  ...form.getFieldsValue(),
                  startAnimationId: item.id,
                });
              }}
              onCancel={() => {
                form.setFieldValue('startAnimationId', undefined);
                onFormChange(null, {
                  ...form.getFieldsValue(),
                  startAnimationId: undefined,
                });
              }}
            />
          </Form.Item>

          <Form.Item name="endAnimationId" label="动画场景2">
            <HeadPortraitSelectModal
              title="动画场景2"
              appId={appId}
              onSelect={(item) => {
                if (item.id && item === form.getFieldValue('startAnimationId')) {
                  message.warning('动画场景1和动画场景2不能选择相同的场景');
                  form.setFieldValue('endAnimationId', null);
                  return;
                }
                form.setFieldValue('endAnimationId', item.id);
                onFormChange(null, { ...form.getFieldsValue(), endAnimationId: item.id });
              }}
              onCancel={() => {
                form.setFieldValue('startAnimationId', undefined);
                onFormChange(null, {
                  ...form.getFieldsValue(),
                  endAnimationId: undefined,
                });
              }}
            />
          </Form.Item>
        </Form>

        <CreateAnimationModal
          appId={appId}
          animationType={AnimationEnum.过渡动画}
          onChange={onCreateAnimation}
        />
      </Flex>

      <Table
        style={{ marginTop: 20 }}
        columns={tableColumns}
        dataSource={animationList}
        onChange={(pagination) => {
          setCurrent(pagination.current);
          getAnimationList(pagination.current, form.getFieldsValue());
        }}
        pagination={{
          total,
          pageSize: PAGE_SIZE,
          current,
        }}
        loading={loading}
      />
      <DraftTransitional appId={appId} onChange={onCreateAnimation}/>
    </>
  );
};

const DropdownItemContainer = styled.div`
  display: flex;
  align-items: center;
  padding: 4px 12px;
  transition: background 0.2s;
`;

export default Transitional;
