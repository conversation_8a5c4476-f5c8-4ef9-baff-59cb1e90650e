import React, { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import Cycle from './cycle';
import Transitional from './transitional';
import { useQuery } from '@hooks/useQuery';

interface AnimationProps {
  appId: string;
}

const Animation: React.FC<AnimationProps> = ({ appId }) => {
  const [curTab, setCurTab] = useState('cycle');

  const { parsedQuery, addQuery, updateQuery } = useQuery();

  useEffect(() => {
    setCurTab((parsedQuery.animationType as string) || 'cycle');
  }, []);

  useEffect(() => {
    if (!parsedQuery.animationType) {
      addQuery({ animationType: curTab });
    } else {
      updateQuery('animationType', curTab);
    }
  }, [curTab]);

  return (
    <div>
      <Tabs
        type="line"
        size="small"
        activeKey={curTab}
        onChange={setCurTab}
        items={[
          {
            key: 'cycle',
            label: '循环动画',
            children: <Cycle appId={appId} />,
          },
          {
            key: 'transitional',
            label: '过渡动画',
            children: <Transitional appId={appId} />,
          },
        ]}
      />
    </div>
  );
};

export default Animation;
