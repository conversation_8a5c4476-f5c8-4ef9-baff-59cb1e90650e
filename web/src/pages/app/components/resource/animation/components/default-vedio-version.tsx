import { AnimationApi } from "@/api";
import { Flex, Form, message, Modal, Select } from "antd";
import { useState } from "react";
import { versionTimes } from "../constants";

const DefaultVedioVersion = (props) => {
  const { appId, animationId, trigger } = props;
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  // const [times, setTimes] = useState([]);

  const onOpen = async () => {
    setOpen(true);
    form.resetFields();

    // const res = await AnimationApi.previewPartAnimation({
    //   appId,
    //   id: animationId
    // });
    // console.log('resresr3es', res);
    // const timeList = [];
    // res?.forEach(item => {

    //   const keys = Object.keys(item || {});

    //   timeList.push(...keys)
    // })

    // setTimes(timeList?.map(item => ({
    //   label: item,
    //   value: item
    // })))

  };

  const onCancel = () => {
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();
    const res = await AnimationApi.updateAnimationMainVersion({
      ...values,
      appId, animationId
    });

    if (res) {
      message.success('设置成功');
      setOpen(false);
    }
  };

  return <>
    <Flex onClick={onOpen}>{trigger || '设置默认版本'}</Flex>
    <Modal title="设置主默认版本" open={open} onCancel={onCancel} onOk={onOk}>
      <Form form={form}>
        <Form.Item name="key" label="主默认版本" rules={[{ required: true }]}>
          <Select options={versionTimes}
          />
        </Form.Item>
      </Form>
    </Modal>
  </>
};


export default DefaultVedioVersion;