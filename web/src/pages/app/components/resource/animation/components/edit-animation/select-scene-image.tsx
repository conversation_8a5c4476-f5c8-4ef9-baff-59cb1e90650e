import { useEffect, useState } from "react";
import SelectImage from "./select-image";
import { AnimationApi } from "@/api";
import { AnimationEnum } from "../../type";

const SelectSceneImage = (props) => {
  const { appId, onChange, disabledKeys } = props;
  const [imageList, setImageList] = useState([]);

  useEffect(() => {
    AnimationApi.getAllHead({
      type: AnimationEnum.循环动画,
      appId
    }).then(res => {
      const list = Object.keys(res || {}).filter(id=> res[id]?.url)?.map(id => {
        return {
          content: {
            url: res[id]?.url,
            nosKey:res[id]?.nosKey,
            id
          }
        }
      });
      setImageList(list);
    })
  }, []);

  return <>
    <SelectImage
      options={imageList}
      onChange={onChange}
      disabledKeys={disabledKeys}
    />
  </>
}

export default SelectSceneImage;