import { InfoCircleOutlined } from "@ant-design/icons";
import { Flex, Space, Tooltip } from "antd";
import { AnimationEnum } from "../../type";

const ViewVideos = (props) => {
  const { value: videos, animationType } = props;
  return <>
    <Flex gap={10} style={{ minHeight: '360px' }}>
      <Flex vertical>
        {videos?.map((group, index) => {
          return <Flex key={index}  wrap="wrap" gap={10}>
            {Object.keys(group || {})?.map(key => {
              const videoContent = group?.[key];
              return <Flex vertical style={{ background: '#fff' }}>
                <Flex justify="center" gap={4}>
                  {key}
                  {videoContent?.desc && animationType === AnimationEnum.循环动画 &&<Flex>
                    <Tooltip title={videoContent?.desc}>
                      <InfoCircleOutlined style={{ color: '#bbb' }} />
                    </Tooltip>
                  </Flex>}
                </Flex>
                <Flex>
                  <video muted
                    width="180px"
                    height="100%"
                    controls
                    src={videoContent?.url} />
                </Flex>
              </Flex>
            })}

          </Flex>
        })}
      </Flex>
    </Flex>
  </>

}

export default ViewVideos;