import { Button, message } from "antd";
import { useRef, useState } from "react";
import { getLocalData, saveLocalData } from "@/utils/common";
import { v4 as uuidv4 } from 'uuid';
import MetaModal from "./meta-modal";
import { PlusOutlined } from "@ant-design/icons";
import AnimationDrawer from "./animation-drawer";
import { AnimationApi } from "@/api";
import { useGlobalState } from "@/hooks/useGlobalState";
import { AnimationEnum } from "../type";
import { clearLocalWorkFlow } from "../utils";

interface IProps {
  editType?: 'edit' | 'create';
  animationType: AnimationEnum;
  appId: string;
  value?: any;
  trigger?: any;
  onChange?: (value?: any) => void
};

const CreateAnimationModal = (props: IProps) => {
  const { animationType, appId, onChange, trigger, value } = props;
  const { workflowId: parentWorkflowId, nodes, ...metaValue } = value || {};
  const [workflowId, setWorkflowId] = useState(undefined);
  const localKey = `app-${appId}-animation-${animationType}`;

  const [workflow, setWorkflow] = useState<{
    id: string;
    nodes: any[];
  }>();
  const [saveLoading, setSaveLoading] = useState(false);
  const [previewData, setPreviewData] = useState();

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const metaRef = useRef(null);
  const animationRef = useRef(null);

  const onPreviewDataChange = val => {
    setPreviewData(val);
  }

  const noMetaChange = async (values) => {
    const currentLocalData = getLocalData(localKey)
    setSaveLoading(true);

    try {
      const res = await AnimationApi.saveAnimation({
        ...values,
        appId,
        workflow,
        operator: user?.name,
        type: animationType,
        previewData
      });
      if (res?.debugInfo) {
        throw new Error(res.debugInfo);
      }

      setSaveLoading(false);
      metaRef?.current?.close();
      animationRef?.current?.close();
      onChange?.();
      clearLocalWorkFlow(localKey, {
        workflowId
      })

    } catch (error) {
      setSaveLoading(false);
      // @ts-ignore
      message.error(error?.message);

      saveLocalData(localKey, {
        ...(currentLocalData || {}),
        [workflowId]: {
          workflowId,
          nodes: workflow?.nodes,
          ...(values || {}),
        }
      });
    }
  };

  const onWorkflowChange = val => {
    setWorkflow(val);
    metaRef?.current?.show();
  }
  const onCancel = () => { }

  return <>
    <MetaModal
      showTrigger={false}
      editType="create"
      onChange={noMetaChange}
      animationType={animationType}
      // trigger={<Button icon={<PlusOutlined />} type="primary">创建动画</Button>}
      ref={metaRef}
      modalProps={{
        okButtonProps: {
          loading: saveLoading,

        },
        cancelButtonProps: {
          disabled: saveLoading
        }
      }}
      value={metaValue}
      onCancel={onCancel}
    />
    <AnimationDrawer
      ref={animationRef}
      showTrigger
      animationType={animationType}
      appId={appId}
      trigger={trigger || <Button icon={<PlusOutlined />} type="primary">创建动画</Button>}
      value={{
        ...(value || {}),
        workflowId,
        // // 基本信息
        // id: record?.id,
        // name,
        // tag,
        // desc: assetInfo?.desc,

        // workflow
        // workflowId: assetInfo?.workflow?.id,
      }}
      beforeOpen={() => {
        if (value?.workflowId) {
          setWorkflowId(value?.workflowId);
        } else {
          // 生成一个工作流id，前端用
          setWorkflowId(uuidv4());
        }
      }}
      drawerProps={{
        title: '创建动画'
      }}
      onWorkflowChange={onWorkflowChange}
      onPreviewDataChange={onPreviewDataChange}

    />
  </>
};

export default CreateAnimationModal;
