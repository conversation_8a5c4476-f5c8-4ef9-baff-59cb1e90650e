import ImageUploader from "@/pages/app/workflow/react-node/imageUploader";
import { Button, message, Modal } from "antd";
import { useState } from "react";
import { IImageContent } from "../../type";

const UploadFrame = (props) => {
  const { onChange } = props;
  const [open, setOpen] = useState(false);
  const [img, setImg] = useState<IImageContent>();

  const onOpen = () => {
    setOpen(true);
  };

  const onImgChange = val => {
    console.log('onImgChange', val);
    setImg({
      nosKey: val?.key,
      url: val?.url,
      width: 720,
      height: 1280
    });
  };

  const onOk = () => {
    if (!img) {
      message.error('请先上传图片');
      return;
    }
    onChange(img);
    setOpen(false);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const beforeUpload = async file => {
    return new Promise((resolve, reject) => {
      if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
          const img = new Image();
          // @ts-ignore
          img.src = e.target.result;
          img.onload = function () {
            // @ts-ignore
            const width = this.width;
            // @ts-ignore
            const height = this.height;
            if (width > 720 || height > 1280) {
              message.error(`当前宽为【${width}】，高为【${height}】，不符合720 * 1280要求，请重新上传！`);
              reject(`当前宽为【${width}】，高为【${height}】，不符合720 * 1280要求，请重新上传！`);
              return;
            }
            console.log(`图片宽度: ${width}px`);
            console.log(`图片高度: ${height}px`);
            resolve(true);
          };
          img.onerror = function () {
            console.log('图片加载失败');
            reject(false);
          };
        };
        reader.readAsDataURL(file);
      } else {
        reject(false);
      }
    })
  }

  return <div>
    <Button type="text" style={{ color: '#fff' }}
      onClick={onOpen}>上传</Button>
    <Modal title="上传" open={open} onOk={onOk} onCancel={onCancel}>
      <div>图片大小限制720P，宽720，高1280</div>
      <ImageUploader
        permanent={true}
        type="Image"
        onChange={onImgChange}
        // @ts-ignore
        beforeUpload={beforeUpload}
      />
    </Modal>
  </div>
};

export default UploadFrame;