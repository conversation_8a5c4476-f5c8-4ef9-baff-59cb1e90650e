import { Button, Checkbox, Image, List, Modal } from "antd";
import { useState } from "react";
import styled from "styled-components";
import { IImageContent } from "../../type";

const StyledListItem = styled(List.Item)`
  height: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
`;

const StyledImage = styled(Image)`
  /* width: 100%; */
  height: auto;
  /* max-height: 64px; */
  object-fit: cover;
  cursor: pointer;
`;

const SelectImage = (props) => {
  const { options, value, onChange, imageKey = 'nosKey', disabledKeys } = props;
  const [open, setOpen] = useState(false);
  const [img, setImg] = useState<IImageContent>();

  const onSelect = (checked, content) => {
    if (checked) {
      setImg(content);
    } else {
      setImg(undefined);
    }
  }

  const onOk = () => {
    onChange?.(img);
    setOpen(false);
  };

  const onOpen = () => {
    setOpen(true);
  };

  const onCancel = () => {
    setImg(undefined);
    setOpen(false);
  };


  return <>
    <Button type="text" style={{ color: '#fff' }}
    onClick={onOpen}>选择</Button>
    <Modal
      title="选择帧"
      width={800}
      open={open}
      onOk={onOk}
      onCancel={onCancel}>
      <List
        grid={{
          gutter: 16,
          column: 6
          // xs: 1,
          // sm: 2,
          // md: 4,
          // lg: 4,
          // xl: 6,
          // xxl: 8,
        }}
        dataSource={options}
        renderItem={(node: any) => {
          const content = node.content || {};
          const checked = img?.[imageKey] && content?.[imageKey] === img?.[imageKey]
          if (!content?.[imageKey]) return null;
          const disabled = disabledKeys?.includes(content?.[imageKey]);

          return (
            <StyledListItem>
              <span style={{
                position: 'absolute',
                top: 8,
                left: 10,
                zIndex: 1
              }}>
                <Checkbox
                  onClick={e => e.stopPropagation()}
                  // disabled={disabled}
                  checked={checked}
                  onChange={ev => {
                    onSelect(ev.target.checked, content)
                  }} />
              </span>
              <StyledImage
                src={content?.url}
                style={{
                  border: checked ? '4px solid #1890ff' : 'none',
                }}
                
                onClick={() => {
                  if (disabled) return;
                  // onSelect(!checked, content)
                }}
              />
            </StyledListItem>
          )
        }}
      />
    </Modal>
  </>
};

export default SelectImage;
