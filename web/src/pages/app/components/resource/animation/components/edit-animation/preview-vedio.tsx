import { F<PERSON>, Modal, Spin } from "antd";
import { forwardRef, ReactNode, useEffect, useImperativeHandle, useState } from "react";
import ViewVideos from "./view-vedios";
import { AnimationEnum } from "../../type";

interface IProps {
  getVideos?: () => Promise<any>;
  trigger?: ReactNode;
  value?: any;
  beforeOpen?: () => boolean;
  modalProps?: Record<string, any>;
  onOk?: () => void;
  onCancel?: () => void;
  animationType?: AnimationEnum;
}

const PreviewVedio = (props: IProps, ref) => {
  const { value, trigger, getVideos, beforeOpen, modalProps, onOk, onCancel,animationType } = props;
  const [open, setOpen] = useState(false);
  const [videos, setVideos] = useState();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!getVideos) {
      setVideos(value);
    }
  }, [value, getVideos]);

  useImperativeHandle(ref, () => {
    return {
      show: () => {
        onOpen();
      },
      close: () => {
        setOpen(false);
      }
    }
  });

  const onOpen = async () => {
    if (beforeOpen && !beforeOpen()) return;
    if (getVideos) {
      setLoading(true);
      try {
        const newValue = await getVideos();
        setVideos(newValue);
        setOpen(true);
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }

      return;
    }
    setOpen(true);
  };


  return <>
    <Flex onClick={() => {
      onOpen();
    }}>
      {trigger ?? '预览动画'}
    </Flex>
    <Modal
      title="预览动画"
      width={800}
      open={open}
      onCancel={() => {
        setOpen(false);
        onCancel?.();
      }}
      onOk={() => {
        setOpen(false);
        onOk?.();
      }}
      {...modalProps}
    >
      <Spin spinning={loading} tip="动画生成中...">
        <div style={{ minHeight: '400px' }}>
          <ViewVideos value={videos} animationType={animationType}/>
        </div>
      </Spin>
    </Modal>
  </>
};

export default forwardRef(PreviewVedio);
