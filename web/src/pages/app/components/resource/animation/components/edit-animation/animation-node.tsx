import { OrderedListOutlined, PlayCircleOutlined, PlusOutlined, StopOutlined, VideoCameraAddOutlined } from "@ant-design/icons";
import { Button, Flex, Image, Popconfirm, Space, Tooltip } from "antd";
import GenerateAnimation from "./generate-animation";
import { useEffect, useState } from "react";
import { useRequest } from "ahooks";
import { AnimationApi } from "@/api";
import ViewTaskHistory from "../view-task-history";
import ViewVideos from "./view-vedios";
import { AnimationEnum } from "../../type";

const AnimationNode = props => {
  const { value = {}, appId, workflowId, onChange, startNode, endNode, onAddFrame, animationType, disabled, onTaskLoadingChange } = props;

  const [videos, setVideos] = useState(undefined);
  const [generateLoading, setGenerateLoading] = useState(false);

  const { content } = value;

  const { assetTaskId, nosKey } = content || {};

  const isDebug = window.location.search.includes('debug');

  const { run, cancel, loading } = useRequest(async (params) => {
    if (!params?.assetTaskId) {
      return Promise.resolve([]);
    }
    const res = await AnimationApi.getFrameHistoryList({
      // id: vedioContent?.assetTaskId,
      id: params.assetTaskId
    })
    return res;
  }, {
    // 10s 轮询一次
    pollingInterval: 10000,
    pollingWhenHidden: false,
    pollingErrorRetryCount: 5,
    manual: true,
    onFinally() {
    },

    onSuccess: (data) => {
      const item = data?.[0];

      // status 1成功 0排队 3运行中 
      if (item?.status === 1) {
        cancel();
        onVersionsChange(item);
        setGenerateLoading(false);
      }
    }
  });

  const getMainVedio = (output): {
    nosKey?: string;
    url?: string;
    tail?: any;
  } => {

    const keys = Object.keys(output);
    for (let i = 0; i < keys?.length; i++) {
      const value = output[keys[i]];
      if (value?.main) {

        return value;
      }
    }

    return {};
  }

  const onVersionsChange = (val, newAssetTaskId?) => {
    const output = JSON.parse(val?.output || '{}');
    const params = JSON.parse(val?.params || '{}');

    setVideos(output ? [output] : undefined);
    const maxLengthVedio = getMainVedio(output);

    const newValue = {
      ...value,
      content: {
        assetTaskId: newAssetTaskId || assetTaskId,
        url: maxLengthVedio?.url,
        nosKey: maxLengthVedio?.nosKey,
        versions: output,
      },
      status: maxLengthVedio?.nosKey ? 'SUCCESS' : ''
    };
    // 没有尾帧的话需要补尾帧
    if (!params?.tailImgNosKey && maxLengthVedio?.tail) {
      onChange(newValue, {
        url: maxLengthVedio?.tail?.url,
        nosKey: maxLengthVedio?.tail?.nosKey,
        docId: maxLengthVedio?.tail?.docId
      })
      return;
    }
    onChange(newValue);
  };

  useEffect(() => {
    return () => {
      cancel();
    }
  }, []);

  useEffect(() => {
    if (assetTaskId && !nosKey) {
      setGenerateLoading(true);
      run({ assetTaskId });
      return;
    }

    if (nosKey) {
      setVideos(content.versions ? [content.versions] : undefined);
    }
  }, [assetTaskId, nosKey]);

  useEffect(() => {
    if (!assetTaskId) return;


    if (generateLoading) {
      onTaskLoadingChange?.(assetTaskId, true);

    } else {
      onTaskLoadingChange?.(assetTaskId, false);
    }

  }, [generateLoading, assetTaskId]);

  const onTaskCancel = () => {
    cancel();
    setGenerateLoading(false);
    onTaskLoadingChange?.(assetTaskId, false);
    const newValue = {
      ...value,
      content: {
        // assetTaskId,
        // url: maxLengthVedio?.url,
        // nosKey: maxLengthVedio?.nosKey,
        // versions: output,
      },
      // status: maxLengthVedio?.nosKey ? 'SUCCESS' : ''
    };

    onChange(newValue);
  }

  return <>
    <Space.Compact block direction="vertical" style={{ width: '110px', overflow: 'hidden' }}>
      {/* <Button icon={<PlayCircleOutlined />}  /> */}
      {generateLoading || loading
        ?
        <ToggleButton hoverNode={<Button danger icon={<StopOutlined />} onClick={onTaskCancel}>取消生成</Button>}>
          <Button loading={generateLoading || loading}>动画生成中</Button>
        </ToggleButton>
        : (nosKey ? <>
          <Image src={startNode?.content?.url}
            height={100}
            style={{
              objectFit: 'cover'
            }}
            preview={{
              mask: <div>
                <PlayCircleOutlined style={{ fontSize: '40px' }} />
              </div>,
              imageRender: () => {
                return <Flex>
                  <ViewVideos value={videos} animationType={animationType}/>
                </Flex>
              },
              toolbarRender: () => null,
            }}
          />
        </>
          : <></>)
      }
      {!generateLoading && !loading && <>
        <Tooltip title={nosKey ? '重新生成相邻两帧之间的动画' : ''}>
          <Button icon={<VideoCameraAddOutlined />} disabled={disabled}>
            <GenerateAnimation
              startNode={startNode}
              endNode={endNode}
              value={value}
              appId={appId}
              workflowId={workflowId}
              onChange={onChange}
              trigger={nosKey ? '重置动画' : '生成动画'}
              animationType={animationType}
              imageKey={animationType === AnimationEnum.循环动画 ? 'nosKey' : 'id'}
              disabled={disabled}
            />
          </Button>
        </Tooltip>
      </>}

      <Tooltip>
        <Button icon={<OrderedListOutlined />} disabled={disabled}>
          <ViewTaskHistory
            trigger="生成记录"
            value={assetTaskId}
            onChange={(item, taskId) => {
              onVersionsChange(item, taskId);
            }}
            params={{
              group: `${workflowId}_${value?.id}`,
              appId
            }} />
        </Button>
      </Tooltip>

      {/* 后端未实现超过5个节点的动画 */}
      {isDebug && animationType === AnimationEnum.循环动画 && <Tooltip title="添加一帧">
        {nosKey ?
          <Popconfirm
            title="当前相领两帧已生成动画，添加新的帧节点，会清空动画！确认添加帧节点？"
            onConfirm={onAddFrame}
          >
            <Button icon={<PlusOutlined />} disabled={disabled}
            >添加一帧</Button>
          </Popconfirm> :
          <Button icon={<PlusOutlined />}
            onClick={onAddFrame}
            disabled={disabled}
          >添加一帧</Button>
        }
      </Tooltip>}
    </Space.Compact>
  </>
}

const ToggleButton = (props) => {
  const { children, hoverNode } = props;
  const [hovering, setHovering] = useState(false);

  const onMouseEnter = () => {
    setHovering(true);
  }

  const onMouseLeave = () => {
    setHovering(false);
  }

  return <div
    onMouseEnter={onMouseEnter}
    onMouseLeave={onMouseLeave}
  >
    {hovering ?
      hoverNode :
      children}
  </div>
}

export default AnimationNode;
