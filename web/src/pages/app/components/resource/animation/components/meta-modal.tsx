import StringTags from "@/components/string-tags";
import { Button, Form, Input, Modal, Select } from "antd";
import { forwardRef, useImperativeHandle, useState } from "react";
import { AnimationType } from "../constants";

const MetaModal = (props, ref) => {
  const { value, onChange, animationType, trigger, editType = "create", modalProps, showTrigger = true } = props;
  const [metaOpen, setMetaOpen] = useState(false);
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => {
    return {
      show: () => {
        setMetaOpen(true);
      },
      close: () => {
        setMetaOpen(false);
      }
    }
  });

  const onOk = async () => {
    const values = await form.validateFields();
    if (editType === 'edit') {
      onChange?.({ ...values, id: value.id });
    } else {
      onChange?.(values);
    }
    if (showTrigger) {
      setMetaOpen(false);
    }
  };

  const onCancel = () => {
    setMetaOpen(false);
    props?.onCancel();
  };

  const onMetaOpen = () => {
    if (editType === 'edit') {
      form.setFieldsValue(value);
    }
    setMetaOpen(true);
  };

  return <>
    {showTrigger && <span onClick={onMetaOpen}>{trigger ?? <Button>创建动画</Button>}</span>}
    <Modal title={editType === 'edit' ? '修改基本信息' : '创建动画'}
      open={metaOpen}
      onOk={onOk}
      onCancel={onCancel}
      destroyOnClose
      {...modalProps}
    >
      <Form form={form} labelCol={{ span: 4 }} initialValues={{ type: animationType }}>
        <Form.Item
          label="动画名称"
          name="name"
          rules={[
            { required: true }
          ]}>
          <Input
            disabled={editType === 'edit'}
          />
        </Form.Item>
        <Form.Item label="描述" name="desc">
          <Input.TextArea />
        </Form.Item>
        <Form.Item label="标签" name="tag" rules={[
          { required: true }
        ]}>
          <StringTags />
        </Form.Item>
        <Form.Item label="类型" name="type">
          {/* {AnimationType[animationType] || animationType} */}
          <Select
            disabled
            options={Object.keys(AnimationType)?.map(key => {
              return {
                label: AnimationType[key],
                value: key
              }
            })} />
        </Form.Item>
      </Form>
    </Modal></>
}

export default forwardRef(MetaModal);
