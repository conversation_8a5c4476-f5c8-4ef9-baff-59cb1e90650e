import { AnimationApi } from "@/api";
import { useGlobalState } from "@/hooks/useGlobalState";
import ImageUploader from "@/pages/app/workflow/react-node/imageUploader";
import { useRequest } from "ahooks";
import { Button, Empty, Flex, Form, Image, Input, InputNumber, Modal, Radio, Space, Spin } from "antd";
import { useState } from "react";

const SizeField = (props) => {
  const { value, onChange } = props;
  return <>
    <Flex gap={32}>
      <Flex align="center" gap={10}>
        宽 <InputNumber
          disabled
          value={value?.width}
          onChange={val => onChange?.({
            ...(value || {}),
            width: val
          })} />
      </Flex>
      <Flex align="center" gap={10}>
        高 <InputNumber
          disabled
          value={value?.height}
          onChange={val => onChange?.({
            ...(value || {}),
            height: val
          })} />
      </Flex>
    </Flex>
  </>
};

const defaultValue = {
  size: {
    height: 1280,
    width: 720,
  },
  way: 'image'
};

const GenerateFrame = (props) => {
  const { value = { ...defaultValue }, onChange, appId, workflowId, nodeId } = props;
  const [open, setOpen] = useState(false);
  const [isFirst, setIsFirst] = useState(true);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const groupId = `${workflowId}_${nodeId}`;

  const { data, run, cancel, loading: taskLoading } = useRequest(async (params) => {
    if (!params?.assetTaskId) {
      return Promise.resolve([]);
    }
    const res = await AnimationApi.getFrameHistoryList({
      id: params.assetTaskId
    })
    return res;
  }, {
    pollingInterval: 1000,
    pollingWhenHidden: false,
    pollingErrorRetryCount: 5,
    manual: true,
    onSuccess: (data) => {
      const item = data?.[0];

      // status 1成功 0运行中 
      if (item?.status === 1) {
        cancel();
        const output = JSON.parse(item?.output || '{}')
        console.log('itemitem', item, 'output', output);

      }
    }
  });


  const reset = () => {
    setIsFirst(true);
  };

  const onOpen = () => {
    console.log('valuevalue', value);
    form.setFieldsValue(value);
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();
    const { size, image, ...others } = values || {};

    const params = {
      type: 'KEEP_SUBJECT_GEN_SCENE',
      group: groupId,
      ...(size || {}),
      refImage: image?.key,
      appId,
      ...others,
      operator: user?.name
    };
    setLoading(true);
    try {
      const res = await AnimationApi.createFrameImage(params);
      console.log('resres', res);
      setIsFirst(false);
      // setOpen(false);
      setLoading(false);

    } catch (error) {
      setLoading(false);
    }
  };

  const onSubmit = () => {
    onChange?.({});
   };

  return <>
    <span onClick={onOpen}>生成</span>
    <Modal
      title="参考生成图像"
      open={open}
      onCancel={onCancel}
      // onOk={onOk}
      width={720}
      footer={isFirst ? <Space>
        <Button>取消</Button>
        <Button
          type="primary"
          loading={loading}
          onClick={onOk}>
          生成
        </Button>
        <Button type="primary" onClick={onSubmit}>提交</Button>
      </Space> : <Space>
        <Button>取消</Button>
        <Button onClick={onOk}>重新生成</Button>
        <Button type="primary" onClick={onSubmit}>提交</Button>
      </Space>}
    >
      <Flex>
        <Flex flex={1}>
          <Form form={form}>
            <Form.Item label="主体形象参考图" name="image"
              rules={[{ required: true }]}>
              <ImageUploader />
            </Form.Item>
            <Form.Item label="提示词" name="prompt"
              rules={[{ required: true }]}>
              <Input.TextArea rows={10} />
            </Form.Item>
            <Form.Item label="生成尺寸" name="size"
              rules={[{ required: true }]}>
              <SizeField />
            </Form.Item>
            <Form.Item label="生成格式" name="way"
              rules={[{ required: true }]}>
              <Radio.Group
                options={[
                  { label: '单张图像', value: 'image' },
                  { label: '视频连续帧', value: 'video' }
                ]}
              ></Radio.Group>
            </Form.Item>
          </Form>
        </Flex>
        <Flex vertical flex={1}>
          <div>生成结果</div>
          <div>
            <Spin spinning={loading || taskLoading}>
              <Empty />
            </Spin>
          </div>
          {/* <Image placeholder="111" /> */}
        </Flex>
      </Flex>

    </Modal>
  </>;
}

export default GenerateFrame;
