import { AnimationApi } from "@/api";
import { useRequest } from "ahooks";
import { But<PERSON>, Drawer, <PERSON>lex, Popover, Table, Tag } from "antd";
import { useState } from "react";
import PreviewVedio from "./edit-animation/preview-vedio";
import { TaskStatusMap } from "../constants";
import { ITaskStatusType } from "../type";
import ReactJson from "react-json-view";

const ViewTaskHistory = (props) => {
  const { trigger, params, value, onChange } = props;
  const [open, setOpen] = useState(false);
  const [list, setList] = useState<any[]>([]);

  const { data, run, cancel, loading: taskLoading } = useRequest(async (params) => {
    if (!params) {
      return Promise.resolve([]);
    }
    const res = await AnimationApi.getFrameHistoryList(params)
    return res;
  }, {
    pollingInterval: 2000,
    pollingWhenHidden: false,
    pollingErrorRetryCount: 5,
    manual: true,
    onSuccess: (data) => {
      setList(data);
      const item = data?.[0];
      // status 1成功 0运行中 
      if (item?.status === ITaskStatusType.成功) {
        cancel();
        const output = JSON.parse(item?.output || '{}')
      }
    }
  });

  const onOpen = () => {
    run(params);
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  return <>
    <span onClick={onOpen}>
      {trigger ?? <Button type="text" >查看生成记录</Button>}
    </span>
    <Drawer title="生成记录"
      width={800}
      open={open} onClose={onClose}>
      <Table
        dataSource={list}
        columns={[
          {
            title: 'taskId', dataIndex: 'id', render: val => {
              return <Flex gap={8}>
                <Flex>{val}</Flex>
                {val === value && <Flex><Tag color="success">正在使用</Tag></Flex>}
              </Flex>
            }
          },
          {
            title: '输入详情', dataIndex: 'params', render: (value, record, index) => {
              const params = JSON.parse(value || '{}');

              // return <Input.TextArea value={value}/>
              // return <ReactJson src={params}/>
              return <Popover content={<ReactJson src={params} />}>
                <a>
                  {params?.prompt}
                </a>
              </Popover>

            },
          },
          {
            title: '状态', dataIndex: 'status', render: (value, record, index) => {
              return TaskStatusMap[value] || value
            },
          },
          {
            title: '结果', dataIndex: 'output', render: (value, record, index) => {
              if (record?.type === 'ANIMATION') {

                return <PreviewVedio
                  value={[JSON.parse(value || '{}')]}
                  trigger={<Button type="link">查看动画</Button>}
                />
              }
              return '-'
            },
          },

          { title: '生成时间', dataIndex: 'createTime' },
          { title: '操作人', dataIndex: 'creator' },
          {
            title: '操作', dataIndex: 'operate', render: (val, record) => {

              if (record?.id === value) return '';
              return <Button type="link" onClick={() => {
                onChange(record, record?.id);
                setOpen(false);
              }}>使用</Button>
            },
          }

        ]} />
    </Drawer>

  </>
}

export default ViewTaskHistory;
