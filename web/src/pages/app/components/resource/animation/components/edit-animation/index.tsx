import { But<PERSON>, Flex, message, Tooltip, Typography } from "antd";
import ImageNode from "./image-node";
import { useEffect, useRef, useState } from "react";
import { getLocalData, saveLocalData } from "@/utils/common";
import UploadFrame from "./upload-frame";
import styled from "styled-components";
import PreviewVedio from "./preview-vedio";
import AnimationNode from "./animation-node";
import { AnimationApi } from "@/api";
import { useGlobalState } from "@/hooks/useGlobalState";
import { generateNodeId } from "../../utils";
import { AnimationEnum } from "../../type";
import SelectSceneImage from "./select-scene-image";
import { useRequest } from "ahooks";

const AnimationDiv = styled.div`
  height: 100%;
`;

const NodesDiv = styled.div`
  /* 创建点状背景 */
  background-image: radial-gradient(circle, rgba(0,0,0,0.2) 1px, transparent 1px);
  background-size: 20px 20px;
  /* 可以根据需求调整背景的位置 */
  background-position: 0 0;
  height: 100%;
  padding: 24px;
`;

const NodeDiv = styled.div`
  border: 2px solid transparent;
  &.error-node {
    border: 2px solid #ff4d4f;
  }
`;

const ANIMATION = 'ANIMATION';
const IMAGE_FRAME = 'IMAGE_FRAME';

const EditAnimation = (props: {
  appId: string;
  animationType: AnimationEnum;
  value: {
    // 动画id
    id?: string;
    workflowId: string;
    name?: string;
    tag?: string;
    desc?: string;
    nodes?: any[];
  },
  onChange?: () => void;
  onWorkflowChange?: (value: any) => void;
  onPreviewDataChange?: (value: any) => void;

}) => {
  const { appId, animationType, value, onChange, onWorkflowChange, onPreviewDataChange } = props;
  const { workflowId, nodes: parentNodes, ...restValue } = value || {};

  const [nodes, setNodes] = useState([]);
  // const [workflowId, setWorkflowId] = useState();
  const [errors, setErrors] = useState(null);
  const [generateLoading, setGenerateLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);

  const [saveLoading, setSaveLoading] = useState(false);

  // taskId: false/true
  const taskLoadingMap = useRef({});
  const [taskLoading, setTaskLoading] = useState(false);

  // 预览数据
  const [previewData, setPreviewData] = useState(false);

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const previewRef = useRef(null);

  const { data: headData } = useRequest(async () => {
    if (animationType === AnimationEnum.过渡动画) {
      const res = await AnimationApi.getAllHead({
        type: AnimationEnum.循环动画,
        appId
      });
      return Object.keys(res || {}).filter(id => res[id]?.url)?.map(id => {
        return {
          content: {
            url: res[id]?.url,
            nosKey: res[id]?.nosKey,
            id
          }
        }
      });
    }
  }, {
    refreshDeps: [animationType, appId]
  });

  const localKey = `app-${appId}-animation-${animationType}`;

  const clearLocalWorkFlow = (workflow?) => {
    const _workflowId = workflow?.workflowId || workflowId;

    const localData = getLocalData(localKey);

    delete localData[_workflowId];

    saveLocalData(localKey, localData);
  };

  const updateLocalWorkflow = (workflow) => {
    const _workflowId = workflow?.workflowId || workflowId;

    const localData = getLocalData(localKey);

    saveLocalData(localKey, {
      ...localData,
      [_workflowId]: {
        ...(localData?.[_workflowId] || {}),
        ...(workflow || {}),

        // 基本信息
        ...restValue,

        // 更新时间
        updatedTime: (new Date()).valueOf()
      }
    })
  }

  const updateNodes = (newNodes, _workflowId?) => {
    setNodes(newNodes);

    updateLocalWorkflow({
      workflowId: _workflowId || workflowId,
      nodes: newNodes,
    })
  }

  useEffect(() => {
    if (workflowId) {
      const currentData = getLocalData(localKey)?.[workflowId];
      // 有本地数据
      if (currentData?.nodes) {
        setNodes(currentData?.nodes);
        return;
      }

      // 获取远程数据
      if (props?.value?.id) {

        AnimationApi.getWorkflowDetail({
          animationId: props.value.id
        }).then(res => {
          setNodes(res.nodes);
        })
        return;
      }

      if (typeof parentNodes !== 'undefined') {
        setNodes(parentNodes);
        return;
      }

      onInit();
    }
  }, [workflowId]);

  const onInit = async () => {
    let newNodes;

    if (animationType === AnimationEnum.循环动画) {
      newNodes = [{
        id: generateNodeId(),
        name: '首帧节点',
        type: IMAGE_FRAME
      }, {
        id: generateNodeId(),
        type: ANIMATION
      },
      {
        id: generateNodeId(),
        type: IMAGE_FRAME
      },
      {
        id: generateNodeId(),
        type: ANIMATION
      },
      {
        id: generateNodeId(),
        name: '尾帧节点',
        type: IMAGE_FRAME
      }];
    }

    if (animationType === AnimationEnum.过渡动画) {
      newNodes = [{
        id: generateNodeId(),
        name: '场景1',
        type: IMAGE_FRAME
      }, {
        id: generateNodeId(),
        type: ANIMATION

      },
      {
        id: generateNodeId(),
        type: IMAGE_FRAME,
        name: '场景2',
      },
      {
        id: generateNodeId(),
        type: ANIMATION
      },
      {
        id: generateNodeId(),
        name: '场景1',
        type: IMAGE_FRAME
      }];
    }
    setNodes(newNodes);
  };

  const onAddFrame = (index) => {

    const newNodes = [...nodes];

    newNodes[index] = {
      // 当前重置一个动画
      id: generateNodeId(),
      type: ANIMATION,
    };

    newNodes.splice(index + 1, 0, {
      // 添加一个图片帧
      id: generateNodeId(),
      type: IMAGE_FRAME,
      // name: '',
    }, {
      // 添加一个视频帧
      id: generateNodeId(),
      type: ANIMATION

    });
    updateNodes(newNodes);
    setPreviewData(null);
  }

  useEffect(() => {
    onPreviewDataChange?.(previewData);
  }, [previewData]);

  const onDeleteImageFrame = (val, index) => {

    // 删除图片节点，会把相邻的两个视频一起删除,并且生成一个新的视频节点
    const newNodes = nodes?.map((node, idx) => {
      if (idx === index - 1) {
        const preNode = nodes[index - 1];
        if (preNode.type === ANIMATION) return false;
      }
      if (idx === index) return false;
      if (idx === index + 1) {
        const nextNode = nodes[index - 1];
        if (nextNode.type === ANIMATION) return {
          type: ANIMATION,
          id: generateNodeId(),
        };
      }
      return node;
    })?.filter(node => !!node);

    updateNodes(newNodes);
  };

  const onImageNameChange = (val, index) => {
    const newNodes = nodes?.map((node, idx) => {
      if (index === idx) {
        return val;
      }
      return node;
    });
    updateNodes(newNodes);
  };

  const onImageChange = (val, index) => {
    const newNodes = [...nodes];

    const newNode = {
      ...(newNodes[index] || {}),
      content: val,
      type: 'IMAGE_FRAME',
      status: val?.nosKey ? 'SUCCESS' : ''
    };

    if (index > 1) {
      newNodes[index - 1] = {
        // 清空相邻视频帧
        id: generateNodeId(),
        type: ANIMATION
      }
    }


    if (index < nodes?.length - 2) {
      newNodes[index + 1] = {
        // 清空相邻视频帧
        id: generateNodeId(),
        type: ANIMATION
      }
    }

    // 首帧修改会同步到尾帧
    if (index === 0) {
      newNodes[0] = newNode;
      newNodes[nodes.length - 1].content = newNode.content;
      newNodes[nodes.length - 1].type = newNode.type;
      newNodes[nodes.length - 1].status = newNode.status;
      newNodes[nodes.length - 2] = {
        // 清空相邻视频帧
        id: generateNodeId(),
        type: ANIMATION
      }
    } else if (index === nodes.length - 1) {
      newNodes[0].content = newNode.content;
      newNodes[0].type = newNode.type;
      newNodes[0].status = newNode.status;
      newNodes[nodes.length - 1] = newNode;
      newNodes[1] = {
        // 清空相邻视频帧
        id: generateNodeId(),
        type: ANIMATION
      }
    } else {
      newNodes[index] = newNode
    }
    updateNodes(newNodes);

    // 存在errors的话说明在被校验
    if (errors) {
      validate({ nodes: newNodes });
    }
  }

  const onAnimationChange = (val, index, tailNode) => {
    const newNodes = [...nodes];
    newNodes[index] = val;

    if (tailNode) {
      const tailIndex = index + 1;
      const newTailNode = {
        ...(newNodes[tailIndex] || {}),
        content: tailNode,
        type: 'IMAGE_FRAME',
        status: tailNode?.nosKey ? 'SUCCESS' : ''
      };
      // 清空下一帧视频
      if (tailIndex < nodes?.length - 2) {
        newNodes[tailIndex + 1] = {
          id: generateNodeId(),
          type: ANIMATION
        }
      };
      newNodes[tailIndex] = newTailNode;
    }

    updateNodes(newNodes);

    // 动画修改过后要重新预览
    setPreviewData(null);

    // 存在errors的话说明在被校验
    if (errors) {
      validate({ nodes: newNodes });
    }
  };

  const onTaskLoadingChange = (taskId, loading) => {
    if (!taskLoadingMap.current) {
      taskLoadingMap.current = {};
    }
    taskLoadingMap.current[taskId] = loading;

    const currentTaskLoading = Object.values(taskLoadingMap.current).includes(true);

    if (!!currentTaskLoading !== !!taskLoading) {
      setTaskLoading(currentTaskLoading);
    }
  };

  const validate = (params?: {
    nodes?: any[];
    showMessage?: boolean;
    validateImage?: boolean;
    validateAnimation?: boolean;
  }) => {
    const { nodes: _nodes, showMessage, validateImage = true, validateAnimation = true } = params || {}
    const curNodes = _nodes ?? nodes;

    if (!curNodes) {
      message.error('图片帧不能为空');
      return {
        imageError: true,
        videoError: true
      }
    }
    let imageError = false;
    let videoError = false;

    let newErrors;

    for (let i = 0; i < curNodes?.length; i++) {
      const node = curNodes[i];
      const { nosKey, url } = node.content || {};

      if (node?.type === IMAGE_FRAME) {
        if (!nosKey || !url) {
          // message.error('请上传图片');
          if (validateImage) {
            if (!newErrors) {
              newErrors = {};
            }
            imageError = true;
          }
          newErrors[i] = true;
        }
      }
      if (node?.type === ANIMATION) {

        if (!nosKey || !url) {
          if (validateAnimation) {
            if (!newErrors) {
              newErrors = {}
            }
            newErrors[i] = true;
          }
          videoError = true;
        }
      }
    }

    if (imageError && showMessage && validateImage) {
      message.error('图片帧不能为空，请上传图片。');
    }
    if (videoError && showMessage && validateAnimation) {
      message.error('相邻两帧之间必须生成动画。');
    }

    setErrors(newErrors);
    return {
      imageError,
      videoError
    }
  };

  const onGenerete = async () => {
    const { imageError, videoError } = validate({ showMessage: false, validateAnimation: false });
    if (imageError) return false;

    if (!videoError) {
      message.warning('相邻两帧动画皆已生成完毕。');
      return false;
    }

    try {
      setGenerateLoading(true);
      const res = await AnimationApi.autoCreateAnimation({
        appId,
        workflow: {
          id: workflowId,
          nodes,
        },
        operator: user?.name
      });
      updateNodes(res?.nodes);
      message.success('开始一键生成动画，未生成完请勿关闭页面，会导致数据丢失。');
      setGenerateLoading(false);

    } catch (error) {
      setGenerateLoading(false);
    }
  };

  const onSave = () => {
    setSaveLoading(true);
    const { imageError, videoError } = validate({
      showMessage: true,
    });
    if ((imageError || videoError)) {
      setSaveLoading(false);
      return false;
    }
    // setSaveLoading(false);
    previewRef.current?.show();
  };

  const onSaveData = async () => {

    if (saveLoading) {
      setSaveLoading(false);
    }

    if (onWorkflowChange) {
      onWorkflowChange?.({
        id: workflowId,
        nodes
      });
      return;
    }

    try {

      const res = await AnimationApi.saveAnimation({
        ...restValue,
        appId,
        workflow: {
          id: workflowId,
          nodes,
        },
        operator: user?.name,
        type: animationType,
        previewData: previewData
      });

      setSaveLoading(false);
      // 清空本地的数据
      clearLocalWorkFlow();
      onChange?.();

    } catch (error) {
      console.log('error', error);
      setSaveLoading(false);
    }
  };

  const isOperating = generateLoading || previewLoading || saveLoading || taskLoading;

  return <AnimationDiv>
    <Flex justify="space-between" style={{ margin: '12px' }}>
      <Flex align="center" gap={10}>
        <Typography.Text style={{ fontSize: '16px' }}>{value?.name}</Typography.Text>
        <Typography.Text type="secondary">{value?.desc}</Typography.Text>
      </Flex>
      <Flex gap={8}>
        <Tooltip title="一键生成会补全相领两帧没有生成的动画，生成耗时较长，未结束前禁止任何操作。">
          <Button onClick={onGenerete}
            loading={generateLoading}
            disabled={isOperating}>一键生成</Button>
        </Tooltip>
        <PreviewVedio
          ref={previewRef}
          trigger={<Button loading={previewLoading} disabled={isOperating}>预览</Button>}
          beforeOpen={() => {
            const { imageError, videoError } = validate({ showMessage: true });
            if ((imageError || videoError)) return false;
            return true;
          }}
          getVideos={async () => {
            if (previewData) return previewData;

            try {
              setPreviewLoading(true);
              const res = await AnimationApi.previewAnimation({
                appId,
                workflow: {
                  id: workflowId,
                  nodes,
                },
                type: animationType,
                operator: user?.name
              });
              setPreviewLoading(false);
              if (res?.debugInfo) {
                throw new Error(res.debugInfo);
              }
              setPreviewData(res);
              return res;
            } catch (error) {
              setPreviewLoading(false);
              // @ts-ignore
              throw new Error(error?.message);
            }
          }}
          modalProps={{
            okText: saveLoading ? '确认保存' : '确定'
          }}
          onOk={() => {
            // 在保存流程中
            if (saveLoading) {
              onSaveData();
            }
          }}
          onCancel={() => {
            // 在保存流程中
            if (saveLoading) {
              setSaveLoading(false);
            }
          }}
        />
        <Tooltip title="保存前请确保每一个图片帧和相邻动画不为空。">
          <Button onClick={onSave} loading={saveLoading} disabled={isOperating}>保存</Button>
        </Tooltip>
      </Flex>
    </Flex>
    <NodesDiv>
      <Flex wrap="wrap" >
        {nodes?.map((node, index) => {
          // index为偶数时是图片节点 0,1,2,3,4,5
          const isImageNode = index % 2 === 0;

          // 如果是图片节点的话并且不是最后一个节点 渲染一个图片节点+一个视频节点
          // 最后一个节点单独渲染 index === nodes.length - 1
          if (isImageNode) {
            const nextIndex = index + 1;
            const nextNode = nodes[nextIndex];
            const preNode = nodes[index - 1];

            return <Flex style={{ marginBottom: '20px' }}>
              {node.type === 'IMAGE_FRAME' &&
                <div>
                  <NodeDiv className={errors?.[index] ? 'error-node' : ''}>
                    <ImageNode
                      isStart={index === 0}
                      isEnd={index === nodes?.length - 1}
                      allowClear={![0, nodes?.length - 1].includes(index) && nodes?.length > 6}
                      onDelete={() => onDeleteImageFrame(node, index)}
                      appId={appId}
                      workflowId={workflowId}
                      nodeId={node.id}
                      key={node.id}
                      index={index / 2}
                      value={{
                        name: `动画帧-${index / 2}`,
                        ...node
                      }}
                      onChange={val => onImageNameChange(val, index)}
                      createFrameAction={
                        !isOperating && (<div>
                          {/* 前一个动画，或者后一个动画有生成视频任务时不能修改当前图片节点 */}
                          {((!preNode?.content?.assetTaskId || preNode?.content?.nosKey) &&
                            (!nextNode?.content?.assetTaskId || nextNode?.content?.nosKey))
                            &&
                            <div>
                              {animationType === AnimationEnum.循环动画 && <div>
                                <UploadFrame onChange={val => onImageChange(val, index)} />
                              </div>}
                              {animationType === AnimationEnum.循环动画 && <div>
                                <SelectSceneImage
                                  // options={nodes?.filter(node => {
                                  //   if (node.type === 'IMAGE_FRAME') {
                                  //     const content = node.content || {};
                                  //     if (content.url) return true
                                  //   }
                                  // })}
                                  appId={appId}
                                  onChange={val => onImageChange(val, index)}
                                  disabledKeys={nodes?.map((node, idx) => {
                                    if (node.type === 'IMAGE_FRAME') {
                                      const content = node.content || {};
                                      if (index !== idx) {
                                        return content.nosKey
                                      }
                                    }
                                  })?.filter(item => !!item)}
                                />
                              </div>}
                              {animationType === AnimationEnum.过渡动画 && <div>
                                <SelectSceneImage
                                  appId={appId}
                                  onChange={val => onImageChange(val, index)}
                                  // 已经被选过的，不能重复选
                                  // disabledIds={}
                                  disabledKeys={nodes?.map((node, idx) => {
                                    if (node.type === 'IMAGE_FRAME') {
                                      const content = node.content || {};
                                      if (index !== idx) {
                                        return content.nosKey
                                      }
                                    }
                                  })?.filter(item => !!item)}
                                />
                              </div>}
                              {node.content?.nosKey && <Button type="link" danger onClick={() => onImageChange(null, index)}>清除</Button>}
                            </div>}
                        </div>)}
                    />
                  </NodeDiv>
                  {errors?.[index] && <Flex justify="center" style={{ color: '#ff4d4f' }}>请上传/选择图片</Flex>}
                </div>
              }
              {nextNode?.type === 'ANIMATION' &&
                <Flex vertical justify="center" align="center" style={{ margin: '0 12px' }}>
                  <NodeDiv className={errors?.[nextIndex] ? 'error-node' : ''}>
                    <AnimationNode
                      value={nextNode}
                      onChange={(cur, tailNode) => onAnimationChange(cur, nextIndex, tailNode)}
                      // 如果没有尾帧，生成动画的时候要自动补充尾帧
                      // onTailImageChange={val => onTailImageChange(val, nextIndex + 1)}
                      appId={appId}
                      workflowId={workflowId}
                      index={nextIndex}
                      startNode={nodes[nextIndex - 1]}
                      endNode={nodes[nextIndex + 1]}
                      onAddFrame={() => onAddFrame(nextIndex)}
                      animationType={animationType}
                      disabled={isOperating}
                      onTaskLoadingChange={onTaskLoadingChange}
                    />
                  </NodeDiv>
                  {errors?.[nextIndex] && <Flex justify="center" style={{ color: '#ff4d4f' }}>请生成动画</Flex>}
                </Flex>
              }
            </Flex>

          }
        })}
      </Flex>
    </NodesDiv>

  </AnimationDiv>

};

export default EditAnimation;
