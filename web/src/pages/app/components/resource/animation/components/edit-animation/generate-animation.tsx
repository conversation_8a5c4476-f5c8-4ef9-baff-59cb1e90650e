import { Animation<PERSON><PERSON> } from "@/api";
import { useGlobalState } from "@/hooks/useGlobalState";
import { Button, Flex, Form, Image, Input, message, Modal, Radio, Tooltip } from "antd";
import { useState } from "react";

const defaultValue = {
  movementAmplitude: 'AUTO'
};

const GenerateAnimation = (props) => {
  const { onChange, startNode, endNode, appId, workflowId, value, trigger, imageKey } = props;
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const startContent = startNode?.content || {};
  const endContent = endNode?.content || {};


  const onOpen = () => {
    // 校验相邻两帧是否有图片
    if (!startContent?.[imageKey]) {
      message.error('生成动画前请上传前一帧图片');
      return false;
    }

    // if (!endContent?.[imageKey]) {
    //   message.error('生成动画前请上传尾帧');
    //   return false;
    // }


    form.setFieldsValue(defaultValue);
    setOpen(true);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();

    setLoading(true);
    try {
      const res = await AnimationApi.createAnimation({
        appId,
        type: 'ANIMATION',
        group: `${workflowId}_${value?.id}`,
        headImgNosKey: startContent.nosKey,
        tailImgNosKey: endContent.nosKey,
        operator: user.email?.split('@')?.[0],
        ...values,
      });
      if (res.debugInfo) {
        throw new Error(res.debugInfo);
      }
      onChange?.({
        ...value,
        content: {
          assetTaskId: res
        }
      });

      setOpen(false);
      setLoading(false);
    } catch (error) {
      // @ts-ignore
      message.error(error?.message);
      setLoading(false);
    }
  };

  return <>
    <Tooltip title="生成两帧之间的动画，可自动填补下一帧。">
      <span onClick={onOpen} style={{ cursor: 'pointer' }}>{trigger ?? '生成动画'}</span>
    </Tooltip>

    <Modal title="生成动画"
      // width={720}
      open={open}
      onCancel={onCancel}
      onOk={onOk}
      confirmLoading={loading}
    >
      <Form form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 16 }}>
        <Form.Item label="首尾帧">
          <FrameItem value={{
            startUrl: startContent.url,
            endUrl: endContent.url
          }} />
        </Form.Item>

        <Form.Item label="视频描述" name="prompt" rules={[{ required: startContent.url && endContent.url ? false : true }]}>
          <Input.TextArea rows={5} />
        </Form.Item>
        <Form.Item label="时长">
          {/* <InputNumber /> */}
          4s
        </Form.Item>
        <Form.Item label="动作幅度" name="movementAmplitude">
          <Radio.Group options={
            [
              { label: '自动', value: 'AUTO' },
              { label: '大', value: 'LARGE' },
              { label: '中', value: 'MEDIUM' },
              { label: '小', value: 'SMALL' },
            ]
          } />
        </Form.Item>
      </Form>
    </Modal>
  </>
}

const FrameItem = (props) => {
  const { value } = props || {};
  return <Flex gap={10}>
    <Flex vertical align="center">
      首帧
      <Image src={value.startUrl} width={100} />
    </Flex>
    {value.endUrl && <Flex vertical align="center">
      尾帧<Image src={value.endUrl} width={100} />
    </Flex>}
  </Flex>
}

export default GenerateAnimation;
