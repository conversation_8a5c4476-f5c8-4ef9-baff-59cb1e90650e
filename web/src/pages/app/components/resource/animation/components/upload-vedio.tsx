import { AnimationApi } from "@/api";
import ImageUploader from "@/pages/app/workflow/react-node/imageUploader";
import { Flex, Form, message, Modal, Select } from "antd";
import { useState } from "react";
import { versionTimes } from "../constants";

const UploadVedio = (props) => {
  const { trigger, appId, animationId } = props;
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();

  const onOpen = () => {
    setOpen(true);
    form.resetFields();
  };

  const onCancel = () => {
    setOpen(false);
  };

  const onOk = async () => {
    const values = await form.validateFields();
    const params = {
      animationId,
      key: values?.key,
      nosKey: values?.vedio?.key,
      appId
    };

    const res = await AnimationApi.updateAnimationVersion(params);

    if (res) {
      message.success('上传分时动画成功')
      setOpen(false);
    }
  };

  return <>
    <Flex onClick={() => {
      onOpen();
    }}>
      {trigger ?? '预览动画'}
    </Flex>
    <Modal open={open}
      title="预览分时动画"
      onCancel={onCancel}
      onOk={onOk}
    >
      <Form form={form} labelCol={{ span: 4 }}>
        <Form.Item label="动画" name="vedio" rules={[
          { required: true, message: '请上传动画' }
        ]}>
          <ImageUploader type="Video" permanent />
        </Form.Item>
        <Form.Item label="分时版本" name="key"
          rules={[
            { required: true, message: '请选择分时版本' }
          ]}>
          <Select options={versionTimes} />
        </Form.Item>
      </Form>

    </Modal>
  </>
};

export default UploadVedio;
