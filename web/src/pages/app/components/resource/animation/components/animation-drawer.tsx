import { Drawer, Flex, Modal, Tag, Typography } from "antd";
import { forwardRef, useImperativeHandle, useState } from "react";
import EditAnimation from "./edit-animation";
import { clearLocalWorkFlow, getLocalKey } from "../utils";
import { useLocalStorage } from "@/hooks/useStorage";
import dayjs from "dayjs";
import { ClockCircleOutlined } from "@ant-design/icons";

const AnimationDrawer = (props, ref) => {
  const { onChange, appId, animationType, value, showTrigger = true, trigger, beforeOpen, drawerProps, ...rest } = props;
  const [editOpen, setEditOpen] = useState(false);

  const localKey = getLocalKey({ appId, animationType });

  const [storage] = useLocalStorage(localKey);
  const localData = storage?.[value?.workflowId]

  useImperativeHandle(ref, () => {
    return {
      show: () => {
        setEditOpen(true);
      },
      close: () => {
        setEditOpen(false);
      }
    }
  });

  const onAnimationSave = () => {
    onChange?.();
    setEditOpen(false);
  }

  const onOpen = () => {
    beforeOpen && beforeOpen();
    if (showTrigger) {
      setEditOpen(true);
    }
  };

  const onClose = () => {
    const localKey = getLocalKey({ appId, animationType })
    if (!localKey) {
      setEditOpen(false);

      return;
    }
    if (localData?.updatedTime) {
      Modal.confirm({
        title: '关闭',
        content: '当前动画存在未提交的草稿数据，是否需要保存到本地？',
        okText: '保存到本地',
        cancelText: '清空草稿',
        onOk: () => {
          setEditOpen(false);

        },
        onCancel: () => {
          clearLocalWorkFlow(localKey, {
            workflowId: value?.workflowId
          });
          setEditOpen(false);
        }
      });
      return;
    }
    setEditOpen(false);
    return;
  };

  return <>
    {showTrigger && <Flex onClick={onOpen}> {trigger || '创建动画'}</Flex>}
    <Drawer
      title={<Flex><span>{drawerProps?.title || '编辑动画'}</span>
        {localData?.updatedTime &&
          <Flex align="end" style={{ marginLeft: '10px' }}>
            <Tag style={{ fontWeight: 'normal', fontSize: '12px' }} color="blue">草稿</Tag>
            <Typography.Text
              type="secondary"
              style={{ fontSize: '12px', fontWeight: 'normal' }}
            ><ClockCircleOutlined />{' '}
              {dayjs(localData?.updatedTime)?.format('YYYY-MM-DD HH:mm:ss')}</Typography.Text>
          </Flex>}
      </Flex>}
      open={editOpen}
      style={{ width: '100vw' }}
      width="100%"
      onClose={onClose}
      destroyOnClose
      styles={{
        body: {
          padding: 0,
        }
      }}
    >
      <EditAnimation
        appId={appId}
        animationType={animationType}
        onChange={onAnimationSave}
        value={value}
        {...rest}
      />
    </Drawer>
  </>
}

export default forwardRef(AnimationDrawer);
