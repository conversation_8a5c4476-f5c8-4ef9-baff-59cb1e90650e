import { RocketOutlined, SmileOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { Button, Card, Space, Spin, Menu } from 'antd';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { UserOutlined, SettingOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { MidContainer } from '@/components/layout-container';
import { useGlobalState } from '@/hooks/useGlobalState';
import { AppDetailModel, AppTypeCnMap, IAppType } from '@/interface';

dayjs.extend(customParseFormat);
import HumanModel from './human-model';
import Emoji from './emoji';
import Account from './account';
import Animation from './animation';
import { useQuery } from '@hooks/useQuery';

export function ResourcePage() {
  const { globalState } = useGlobalState();
  const { app } = globalState;

  if (!app) {
    return <Spin />;
  }
  return (
    <MidContainer>
      <Metrics appID={app.id} appType={app.type} />
    </MidContainer>
  );
}

interface IOverviewHeaderProps {
  app: AppDetailModel;
}

export function OverviewHeader(props: IOverviewHeaderProps) {
  const { app } = props;

  function onPreviewClick() {
    window.open(`/preview/${app?.type}?appId=${app!.id}`, '_blank');
  }

  return (
    <Card>
      <StyledHeader>
        <div className="app-info">
          <span className="title">{app.name}</span>
          <span className="app-type">{AppTypeCnMap[app.type]}</span>
        </div>
        <div className="app-desc">{app.description}</div>
        <div className="app-preview">
          <Space>
            <Button onClick={onPreviewClick} icon={<RocketOutlined rev={undefined} />}>
              预览
            </Button>
          </Space>
        </div>
        <div className="create-info">
          <span>
            {app.createdBy.name} 创建于 {app.createdAt}
          </span>
        </div>
      </StyledHeader>
    </Card>
  );
}

interface IMetricsProps {
  appID: string;
  appType: IAppType;
}

type MenuItem = Required<MenuProps>['items'][number];

export function Metrics(props: IMetricsProps) {
  const [selectedMenu, setSelectedMenu] = useState('human-model');

  const { parsedQuery, addQuery, updateQuery } = useQuery();

  const items: MenuItem[] = [
    {
      key: 'human-model',
      label: '肖像管理',
      icon: <UserOutlined />,
    },
    // {
    //   type: 'divider',
    // },
    // {
    //   key: 'Knowledge',
    //   label: '知识库管理',
    //   icon: <BookOutlined />,

    // },

    {
      key: 'account',
      label: '账号管理',
      icon: <SettingOutlined />,
    },

    {
      key: 'emoji',
      label: '表情包管理',
      icon: <SmileOutlined />,
    },

    {
      key: 'animation',
      label: '动画管理',
      icon: <PlayCircleOutlined />,
    },
  ];

  // if (window.location.search.includes('debug')) {
  //   items.push(
  //     {
  //       key: 'animation',
  //       label: '动画管理',
  //       icon: <PlayCircleOutlined />,
  //     }
  //   );
  // }

  useEffect(() => {
    if (parsedQuery.selectedMenu) {
      setSelectedMenu(parsedQuery.selectedMenu as string);
    } else {
      addQuery({ selectedMenu });
    }
  }, []);

  useEffect(() => {
    updateQuery('selectedMenu', selectedMenu);
  }, [selectedMenu]);

  const onClick: MenuProps['onClick'] = (e) => {
    setSelectedMenu(e.key);
  };

  return (
    <div style={{ height: '100%', display: 'flex' }}>
      <div style={{ height: '100%', width: 156, background: '#fff' }}>
        <Menu
          selectedKeys={[selectedMenu]}
          onClick={onClick}
          style={{ width: 156 }}
          defaultSelectedKeys={['1']}
          defaultOpenKeys={['sub1']}
          mode="inline"
          items={items}
        />
      </div>
      <div
        style={{
          height: '100%',
          flex: 1,
          background: '#fff',
          overflow: 'scroll',
          padding: '16px 24px',
        }}
      >
        {selectedMenu === 'human-model' && (
          <HumanModel appId={props.appID} selectionProps={undefined} />
        )}
        {selectedMenu === 'emoji' && <Emoji appId={props.appID} />}
        {selectedMenu === 'account' && <Account appId={props.appID} />}
        {selectedMenu === 'animation' && <Animation appId={props.appID} />}
      </div>
    </div>
  );
}

export const OverviewContainer = styled.div`
  padding: 0px;
`;

const StyledHeader = styled.div`
  position: relative;
  background-color: #fff;

  .app-info {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .title {
    font-size: 24px;
    font-weight: bold;
  }

  .app-type {
    display: inline-block;
    margin-left: 10px;
    background-color: #146ce6;
    padding: 2px 10px;
    border-radius: 10px;
    line-height: 20px;
    height: 20px;
    color: white;
  }

  .app-desc {
    font-size: 14px;
    color: #666;
    margin-top: 10px;
  }

  .app-preview {
    margin-top: 40px;
  }

  .create-info {
    position: absolute;
    right: 24px;
    bottom: 0;
    font-size: 14px;
  }
`;
