import { Emoji<PERSON><PERSON> } from "@/api/emoji";
import { useEffect, useState, useRef } from "react";
import EmojiEditlModal from "./emoji-edit-modal";
import { Card, List, Image, Button, Flex, Popconfirm, Form, Select, Tag, message, Checkbox, Popover, Space } from "antd";
import { DeleteOutlined, EditOutlined, EllipsisOutlined, PlusOutlined } from "@ant-design/icons";
import { useGlobalState } from '@/hooks/useGlobalState';
import styled from "styled-components";
import { difference, differenceBy, unionBy } from "lodash";

const EmojiImg = styled(Image)`
  aspect-ratio: 1;
  width: 100%;
  object-fit: cover !important;
`;

const ImageContainer = styled.div`
  width: 100%;
  aspect-ratio: 1;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
`;

const EmojiContainer = styled.div`
  position: relative;
  height: 100%;
  margin-bottom: 24px;
  .select-box {
    position: absolute;
    z-index: 1;
    left: 6px;
    top: 4px;
  }
`;

const StyledCard = styled(Card)`
  .ant-card-actions>li {
    margin: 6px 0;
  }
`;

interface ISelectionProps {
  selectedKeys?: string[];
  selectedItems?: any[];
  onChange?: (selectedKeys: string[], selectedItems: any[]) => void;
  onSelect?: (checked: boolean, item: any) => void;
}

const Emoji = (props: {
  appId: string;
  disabled?: boolean;
  selectionProps?: ISelectionProps;
  dataSource?: any[];
}) => {
  const { appId, disabled, selectionProps } = props;
  const [filters, setFilters] = useState({ style: [], emotion: [] });
  const [query, setQuery] = useState({
    styles: [], emotions: []
  })
  const [page, setPage] = useState<{
    pageNum: number;
    pageSize: number;
  }>({
    pageNum: 1,
    pageSize: 48
  });

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const [data, setData] = useState<any>({ values: [] });
  const [loading, setLoading] = useState(false);
  const [selectAll, setSelectAll] = useState(false);

  const filterRef = useRef(null);

  const isSelectMode = typeof selectionProps !== "undefined"


  const getList = async (params = {}) => {
    setLoading(true);
    const res = await EmojiApi.queryList({
      appId,
      ...query,
      ...page,
      ...params
    });
    setLoading(false);

    const { values, ...rest } = res || {};

    setData({
      ...rest,
      values: res?.values?.map(item => {
        const emojiInfo = JSON.parse(item?.assetInfo || {});
        emojiInfo.id = item.id;
        emojiInfo.name = item.name;
        emojiInfo.styles = item.styles;
        emojiInfo.emotions = item.emotions;
        return {
          ...item,
          emojiInfo,
        }
      })
    });
  }

  const onFilter = (key, val) => {
    setQuery({
      ...query,
      [key]: val
    });
    if (filterRef.current) {
      clearTimeout(filterRef.current)
    }
    filterRef.current = setTimeout(() => {

      setPage({
        ...page,
        pageNum: 1
      });

      getList({
        [key]: val,
        ...page,
        pageNum: 1
      });
    }, 1000);
  }

  useEffect(() => {
    EmojiApi.getAllStyle().then((res) => {
      const newStyles = {
        style: res?.style?.map(item => ({ label: item, value: item })),
        emotion: res?.emotion?.map(item => ({ label: item, value: item })),
      };
      setFilters(newStyles);
    });
    getList();
  }, []);

  const onDelete = async (imageId) => {
    const res = await EmojiApi.deleteEmoji({
      id: imageId,
      appId,
      operator: user.email?.split('@')?.[0]
    });
    if (res?.debugInfo) {
      return message.error(res.debugInfo)
    } else {
      message.success('删除成功');
      getList();
    }
  }

  const filterTags = (name: 'styles' | 'emotions', tags: string[]) => {
    const selectedTags = query[name] || [];

    const selectedItems = tags.filter(tag => selectedTags.includes(tag));
    const unselectedItems = tags.filter(tag => !selectedTags.includes(tag));

    return [...selectedItems, ...unselectedItems];


    // 合并并限制展示数量为2个
    // return [...selectedItems, ...unselectedItems].slice(0, 1);
  };

  const onEmojiCheck = (checked, item) => {
    if (!isSelectMode) return;

    if (!checked) {
      setSelectAll(false);
    };

    let newData = [...(selectionProps?.selectedItems || [])];

    const idx = newData.findIndex(d => d.id === item.id);

    if (checked) {
      if (idx < 0) {
        newData.push({
          id: item.id,
          url: item.emojiInfo?.url
        });
      }
    } else {
      if (idx > -1) {
        newData.splice(idx, 1)
      }
    }

    selectionProps?.onChange(newData?.map(d => d.id), newData);
    selectionProps?.onSelect?.(item, checked);
  }

  const onSelectAll = ev => {
    const checked = ev.target.checked;
    setSelectAll(checked);
    const newEmojis = data?.values?.map(item => {
      const info = JSON.parse(item.assetInfo || '{}')
      return {
        id: item.id,
        url: info.url
      }
    });
    let result;
    // 选中当前所有结果
    if (checked) {

      result = unionBy(selectionProps?.selectedItems, newEmojis, 'id');
    } else {
      result = differenceBy(selectionProps?.selectedItems, newEmojis, 'id');
      console.log
    }
    selectionProps?.onChange(result?.map(d => d.id), result);
  }


  return <div>
    <Flex justify="space-between" align="center" style={{ marginBottom: '20px', marginLeft: -10 }}>
      <Flex>
        <Form layout="inline" style={{ margin: '10px' }}
          wrapperCol={{ span: 12 }}
        >
          <Form.Item label="风格">
            <Select
              popupMatchSelectWidth={false}
              options={filters?.style}
              mode="multiple"
              onChange={val => onFilter('styles', val)}
              value={query.styles}
              style={{ width: '240px' }}
            />
          </Form.Item>
          <Form.Item label="情绪">
            <Select
              mode="multiple"
              popupMatchSelectWidth={false}
              options={filters?.emotion}
              onChange={val => onFilter('emotions', val)}
              value={query.emotions}
              style={{ width: '240px' }}
            /></Form.Item>
        </Form>
      </Flex>
      {!disabled && <Flex>
        <EmojiEditlModal
          styles={filters}
          appId={appId}
          user={user}
          trigger={<Button type="primary">
            <PlusOutlined />
            上传表情包</Button>}
          onOk={getList}
          modalProps={{ title: '上传表情包' }}
        />
      </Flex>}
      {isSelectMode && <Space>
        <Checkbox
          checked={selectAll}
          onChange={onSelectAll} />
        全选
      </Space>}
    </Flex>
    <List
      loading={loading}
      grid={{
        gutter: 16,
        xs: 1,
        sm: 2,
        md: 4,
        lg: 4,
        xl: 6,
        xxl: 8,
      }}
      dataSource={data?.values}
      renderItem={(item: any) => {
        const checked = selectionProps?.selectedKeys?.includes(item.id)

        return <List.Item style={{ height: '100%' }} key={item.id}>
          <EmojiContainer>
            {isSelectMode && <div className="select-box">
              <Checkbox
                checked={checked}
                onChange={(ev) => onEmojiCheck(ev.target.checked, item)} />
            </div>}
            {isSelectMode ?
              <ImageContainer onClick={() => onEmojiCheck(!checked, item)}>
                <EmojiImg
                  src={item.emojiInfo?.url}
                  preview={false}
                  height="100%"
                />
              </ImageContainer>
              : <Popover
                trigger="click"
                overlayInnerStyle={{ padding: 0 }}
                content={
                  <StyledCard
                    bodyStyle={{
                      padding: '0px',
                      width: '210px'
                    }}
                    size="small"
                    actions={[
                      disabled ? null : <EmojiEditlModal
                        styles={filters}
                        appId={appId}
                        record={item.emojiInfo}
                        user={user}
                        trigger={<EditOutlined />}
                        onOk={getList}
                      />,
                      disabled ? null : <div onClick={e => e.stopPropagation()}>
                        <Popconfirm title="确认删除表情包？"
                          onConfirm={() =>
                            onDelete(item.id)
                          }>
                          <DeleteOutlined />
                        </Popconfirm>
                      </div>,
                      <EmojiEditlModal
                        styles={filters}
                        appId={appId}
                        record={item.emojiInfo}
                        isPreview
                        trigger={<EllipsisOutlined />}
                        modalProps={{ title: '详情' }}
                      />
                    ]?.filter(item => item)}
                  >
                    <EmojiImg src={item.emojiInfo.url} width="100%" height="100%"
                      preview={false} />
                    <Flex vertical gap={4} style={{ padding: '6px 6px' }}>
                      <Flex justify="center" className="emoji-name" >{item.name}</Flex>
                      <Space wrap size={2}
                      >
                        {filterTags('styles', item.styles).map(style => <Tag color="#FAAD14" key={style}>{style}</Tag>)}
                        {filterTags('emotions', item.emotions).map(emotion => <Tag color="#108ee9" key={emotion}>{emotion}</Tag>)}
                      </Space>
                    </Flex>
                  </StyledCard>
                }>
                <ImageContainer>
                  <EmojiImg
                    src={item.emojiInfo?.url}
                    preview={false}
                    height="100%"
                  />
                </ImageContainer>
              </Popover>}
          </EmojiContainer>
        </List.Item>
      }}
      pagination={{
        align: 'end',
        position: 'bottom',
        total: data?.total || 0,
        showTotal(total, range) {
          return `共 ${total} 个`
        },
        pageSize: page.pageSize,
        onChange: (page, pageSize) => {
          setPage({
            pageNum: page,
            pageSize
          });
          getList({
            pageNum: page,
            pageSize
          });
        },
        current: page.pageNum,
      }}
    />
  </div>
};

export default Emoji;
