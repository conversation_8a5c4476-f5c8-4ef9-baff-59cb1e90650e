import { Button, Form, Input, message, Modal, Select, Space, Steps } from "antd";
import { useRef, useState } from "react";
import ImageUploader from "@/pages/app/workflow/react-node/imageUploader";
import { getSize } from "@/utils/common";
import { EmojiApi } from "@/api/emoji";

const EmojiCreateModal = (props) => {
  const { styles, value, appId, trigger, user, record, isPreview, modalProps = {}, onOk } = props;
  const [current, setCurrent] = useState(0);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();

  const imgChange = useRef(false);

  const onChange = step => {
    // 如果尝试直接跳转到第二步，需要先验证第一步表单
    if (step === 1 && current === 0) {
      goNext();
    } else if (step === 0) {
      setCurrent(step);
    }
  };

  const onOpen = () => {
    if (record) {
      form1.setFieldsValue({
        name: record.name,
        id: record.id,
        file: {
          url: record.url,
          key: record.nosKey,
          file: {
            type: record.fileType,
            height: record.height,
            width: record.width
          }
        }
      });
      form2.setFieldsValue({
        desc: record.desc,
        textContent: record.textContent,
        emotions: record.emotions,
        styles: record.styles,
      });
    } else {
      form1.resetFields();
      form2.resetFields();
    }
    setCurrent(0);
    setOpen(true);
  };

  const onImgChange = () => {
    if (imgChange.current) return;
    imgChange.current = true;
  };


  const goNext = async () => {
    const values1 = await form1.validateFields();

    // 如果图片变了，则重新识别
    if (imgChange.current) {
      setLoading(true);

      // 生成推荐风格和情绪
      EmojiApi.recognize({ url: values1.file?.url }).then(res => {
        if (res?.debugInfo) throw new Error(res.debugInfo);
        form2.setFieldsValue(res);
        setLoading(false);
        setCurrent(1);
        imgChange.current = false;
      }).catch(err => {
        message.error(err.message);
        setLoading(false);
      });
    } else {
      setCurrent(1);
    }
  };

  const onSubmit = async () => {
    const values1 = form1.getFieldsValue();
    const values2 = await form2.validateFields();

    if (values1.file?.file?.height && values1.file?.file?.width) {

    } else {
      const size: any = await getSize(values1.file.file);
      values1.file.file.height = size?.height;
      values1.file.file.width = size?.width;
    }

    const params = {
      id: values1.id,
      appId,
      name: values1.name,
      nosKey: values1.file.key,
      url: values1.file.url,
      fileType: values1.file?.file?.type,
      width: values1.file?.file?.width,
      height: values1.file.file.height,
      ...values2,
      operator: user.email?.split('@')?.[0]
    };

    const res = await EmojiApi.editEmoji(params);

    if (res.debugInfo) {
      message.error(res.debugInfo);
    } else {
      onOk?.()
      message.success('成功');
      setOpen(false);

    }
  };

  const goPre = () => {
    setCurrent(0);
  };

  const onCancel = () => {
    setOpen(false);
  };

  const inputIsPreviewProps = {
    // readOnly: isPreview,
    // bordered: !isPreview
    disabled: isPreview
  }

  return <div onClick={e => e.stopPropagation()}>
    <div onClick={onOpen}>
      {trigger || '创建表情'}
    </div>
    <Modal
      title="创建表情"
      open={open}
      maskClosable={false}
      footer={<>
        {isPreview ? <>
          <Button type="primary" onClick={() => {
            setOpen(false);
          }}>关闭</Button>
        </> :
          <>
            {current === 0
              && <Space>
                <Button type="primary" onClick={goNext} loading={loading}>下一步</Button>
              </Space>}
            {current === 1 && <Space>
              <Button onClick={goPre}>上一步</Button>
              <Button type="primary" onClick={onSubmit}>确定</Button>

            </Space>}
          </>}
      </>
      }
      onCancel={
        onCancel
      }
      {...modalProps}
    >
      {!isPreview && <Steps
        current={current}
        onChange={onChange}
        items={[
          { title: "表情上传" },
          { title: "表情配置" },
        ]}
        style={{ marginBottom: '30px' }}
      />}

      <div style={{ height: current === 0 || isPreview ? 'initial' : '0', overflow: 'hidden' }}>
        <Form
          form={form1}
          labelCol={{ span: 4 }}>
          <Form.Item name="id" hidden></Form.Item>
          <Form.Item label="表情名"
            name="name"
            rules={[
              { required: true }
            ]}
          >
            <Input {...inputIsPreviewProps} />
          </Form.Item>
          <Form.Item
            label="表情图片"
            name="file"
            rules={[
              { required: true }
            ]}>
            <ImageUploader
              sizeLimit={1024}
              permanent={true}
              type="Image"
              onChange={onImgChange}
              disabled={isPreview}
            />
          </Form.Item>
        </Form>
      </div>

      <div style={{ height: current === 1 || isPreview ? 'initial' : '0', overflow: 'hidden' }}>
        <Form labelCol={{ span: 4 }} form={form2}>
          <Form.Item label="风格" name="styles" rules={[
            { required: true }
          ]}>
            <Select
              mode="multiple"
              options={
                styles?.style
              }
              disabled={isPreview}
            />
          </Form.Item>
          <Form.Item label="情绪" name="emotions" rules={[
            { required: true }
          ]}>
            <Select
              mode="multiple"
              options={styles?.emotion}
              disabled={isPreview}
            />
          </Form.Item>
          <Form.Item label="表情描述"
            name="desc"
            rules={[
              { required: true }
            ]}>
            <Input.TextArea {...inputIsPreviewProps} />
          </Form.Item>
          <Form.Item label="表情中文本" name="textContent">
            <Input.TextArea {...inputIsPreviewProps} />
          </Form.Item>
        </Form>
      </div>
    </Modal >
  </div >
};

export default EmojiCreateModal;
