import React, { useState, useEffect } from 'react';
import { Drawer, Form, Button, Col, Row, Input, Pagination, Upload, Radio, List, Image, Card, message, InputNumber, Skeleton, Progress } from 'antd';
import { UploadOutlined, ThunderboltOutlined, PlusOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { AppApi } from '@/api/app';
import { useGlobalState } from '@/hooks/useGlobalState';
import ImageUploader from '@/pages/app/workflow/react-node/imageUploader';
import styled from 'styled-components';

const { TextArea } = Input;
const { Meta } = Card;

interface ImageData {
  id: number;
  url: string;
  style: string;
  styleAlias: string;
}

interface ImageGroup {
  id: string;
  images: ImageData[];
  prompt: string;
  createTime: string;
  status: number; // 0: 加载中, 1: 加载完成
}

interface ImageDrawerProps {
  appId: string;
  visible: boolean;
  onClose: () => void;
}

interface ImageStyle {
  name: string;
  alias: string;
  examplePicUrl: string;
}

const StyledImageContainer = styled.div`
  position: relative;
  height: 100%;
  &:hover .add-to-library-button {
    display: block !important;
  }
`;

const ScrollableContainer = styled.div`
  height: calc(100vh - 200px);
  overflow-y: auto;
  padding-right: 16px;
`;

// 定义比例框的样式
const AspectRatioBox = styled.div<{ selected: boolean, width: number, height: number }>`
  width: ${({ width }) => width}px;
  height: ${({ height }) => height}px;
  border: ${({ selected }) => (selected ? '1px solid white !important' : '1px solid #000')};
  border-radius: 2px;
  margin-bottom: 4px;
`;

const AspectRatioContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  margin-top: 10px;
`;

const ImageDrawer: React.FC<ImageDrawerProps> = ({ visible, appId, onClose }) => {
  const [form] = Form.useForm();
  const [useCustomSize, setUseCustomSize] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState<ImageStyle | null>(null);
  const [genLoading, setGenLoading] = useState<boolean>(false);
  const [ratio, setRatio] = useState('9:16');
  const [taskId, setTaskId] = useState<string>();
  const { globalState } = useGlobalState();
  const { user } = globalState;
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 3;
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const defaultWidth = 432;

  const { data: imageGroups = [], mutate: setImageGroups, run: getImageGroups, cancel: stopInterval } = useRequest(() => {
    if (!visible) {
      return Promise.resolve([]);
    }
    return AppApi.taskQuery({
      appId,
      type: 'PORTRAIT_2D'
    }).then(res => res?.map(item => ({
      images: JSON.parse(item.output)?.images || [],
      params: JSON.parse(item.params),
      prompt: JSON.parse(item.params)?.prompt,
      ...item
    })))
  }, {
    pollingInterval: 1000,
    pollingWhenHidden: false,
    refreshDeps: [visible],
    onSuccess: (data) => {
      if (data.every(group => group.status !== 0)) {
        setGenLoading(false);
        stopInterval();
      }
    }
  })

  const { data: imageStyles = [] } = useRequest(() => AppApi.getPortraitStyle({}), {
    refreshDeps: [visible],
    onSuccess: (data) => {
      if (data && data?.length > 0) {
        setSelectedStyle(data[0]);
      }
    }
  });

  const { run: generateDescription, loading: generatingDescription } = useRequest(
    () => {
      const { prompt, customSize, aspectRatio } = form.getFieldsValue(true);
      const [widthRatio, heightRatio] = aspectRatio.split(':').map(Number);
      const calcHeight = defaultWidth / widthRatio * heightRatio;
      return AppApi.portraitPromptPolish({
        appId,
        prompt,
        type: 'PORTRAIT_2D',
        style: selectedStyle?.name,
        styleAlias: selectedStyle?.alias,
        width: customSize?.width || defaultWidth,
        height: customSize?.height || calcHeight,
        operator: user.name
      });
    },
    {
      manual: true,
      onSuccess: (result) => {
        form.setFieldsValue({ prompt: result });
        message.success('AI 生成描述成功');
      },
      onError: (e: any) => {
        console.log(e);
        message.error(e.message || '生成描述失败');
      },
    }
  );

  const { run: genImages } = useRequest(() => {
    setGenLoading(true)
    const { prompt, customSize, aspectRatio, refImage } = form.getFieldsValue(true);
    const [widthRatio, heightRatio] = aspectRatio?.split(':').map(Number);
    const calcHeight = Math.floor(defaultWidth / widthRatio * heightRatio);
    return AppApi.portraitGenerate({
      type: 'PORTRAIT_2D',
      appId,
      prompt,
      refImage: refImage?.url || refImage?.uri,
      style: selectedStyle?.name,
      styleAlias: selectedStyle?.alias,
      width: customSize?.width || defaultWidth,
      height: customSize?.height || calcHeight,
      operator: user.name
    });
  }, {
    manual: true,
    onSuccess: (data) => {

      setTaskId(data);
      getImageGroups();
    }
  });

  // const { data: fetchData, run: fetchImages, cancel: cancelFetchImages, mutate: setFetchData } = useRequest((taskId: string) => AppApi.portraitGenerateResult({
  //   appId,
  //   taskId,
  // }), { 
  //   manual: true, 
  //   pollingInterval: 1000, 
  //   pollingWhenHidden: false,
  //   onSuccess: (data) => {
  //     if (data?.outputs && data.outputs.length > 0) {
  //       const newGroup: ImageGroup = {
  //         id: taskId,
  //         images: data.outputs.map(item => item?.url),
  //         prompt: form.getFieldValue('prompt'),
  //         createTime: new Date().toISOString(),
  //         status: data.status,
  //       };

  //       setImageGroups(prevGroups => prevGroups.map(group => 
  //         taskId === group.id ? newGroup : group
  //       ));

  //       if (data.status === 1 || data.status === 2) {
  //         cancelFetchImages()
  //         setGenLoading(false)
  //       }
  //     }
  //   }
  // });

  const onGenerate = (taskId) => {
    form.validateFields().then(values => {
      const newGroup: ImageGroup = {
        id: taskId,
        images: [],
        prompt: values.prompt,
        createTime: new Date().toISOString(),
        status: 0,
      };
      setImageGroups(prevGroups => [newGroup, ...prevGroups]);
      genImages();
    });
  };

  const handleStyleSelect = (style: ImageStyle) => {
    setSelectedStyle(style);
    form.setFieldsValue({ style: style.name });
  };

  const handleGenerateDescription = () => {
    const currentDescription = form.getFieldValue('prompt');
    generateDescription();
  };

  const handleAspectRatioChange = (e: any) => {
    setUseCustomSize(e.target.value === 'custom');
    setRatio(e.target.value);
  };

  const handleAddToLibrary = (group, item) => {
    const params = JSON.parse(group?.params);
    AppApi.portraitAdd({
      ...params,
      assertTaskId: group?.id,
      url: item
    }).then(res => {
      message.success('图片已添加到库');
    })
  };

  return (
    <Drawer
      title="创建形象"
      width="80%"
      onClose={onClose}
      visible={visible}
      bodyStyle={{ paddingBottom: 80, height: '100%', overflow: 'hidden' }}
    >
      {/* Drawer content */}
      <Row gutter={16} style={{ height: '100%' }}>
        <Col span={10} style={{ height: '100%', overflowY: 'auto' }}>
          <Form form={form} layout="vertical" hideRequiredMark>
            <Form.Item label="选择绘画风格">
              <Row gutter={16}>
                {imageStyles
                  .slice((currentPage - 1) * pageSize, currentPage * pageSize)
                  .map((style, index) => (
                    <Col span={8} key={index}>
                      <Card
                        hoverable
                        onClick={() => handleStyleSelect(style)}
                        cover={
                          <div style={{ position: 'relative' }}>
                            <img alt={style.name} src={`${style?.examplePicUrl}?imageView&thumbnail=256x256`} style={{ width: '100%', height: 'auto' }} />
                            {selectedStyle?.name === style.name && (
                              <div
                                style={{
                                  position: 'absolute',
                                  top: 8,
                                  right: 8,
                                  width: 24,
                                  height: 24,
                                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                  borderRadius: '50%',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                }}
                              >
                                <div style={{ width: 12, height: 12, backgroundColor: '#1890ff', borderRadius: '50%' }} />
                              </div>
                            )}
                          </div>
                        }
                      >
                        <Card.Meta title={style?.alias} />
                      </Card>
                    </Col>
                  ))}
              </Row>
              <Pagination
                current={currentPage}
                total={imageStyles.length}
                pageSize={pageSize}
                onChange={handlePageChange}
                style={{ marginTop: 16, textAlign: 'center' }}
              />
            </Form.Item>
            <Form.Item name="aspectRatio" label="宽高比设置" initialValue={'9:16'}>
              <Radio.Group buttonStyle="solid" style={{ display: 'flex' }} onChange={handleAspectRatioChange} value={form.getFieldValue('aspectRatio')}>
                <Radio.Button value="9:16" style={{ height: 60 }}>
                  <AspectRatioContainer>
                    <AspectRatioBox width={9} height={16} selected={ratio === '9:16'} />
                    <span>9:16</span>
                  </AspectRatioContainer>
                </Radio.Button>
                <Radio.Button value="3:4" style={{ height: 60 }}>
                  <AspectRatioContainer>
                    <AspectRatioBox width={9} height={12} selected={ratio === '3:4'} />
                    <span>3:4</span>
                  </AspectRatioContainer>
                </Radio.Button>
                <Radio.Button value="1:1" style={{ height: 60 }}>
                  <AspectRatioContainer>
                    <AspectRatioBox width={16} height={16} selected={ratio === '1:1'} />
                    <span>1:1</span>
                  </AspectRatioContainer>
                </Radio.Button>
                <Radio.Button value="4:3" style={{ height: 60 }}>
                  <AspectRatioContainer>
                    <AspectRatioBox width={12} height={9} selected={ratio === '4:3'} />
                    <span>4:3</span>
                  </AspectRatioContainer>
                </Radio.Button>
                <Radio.Button value="16:9" style={{ height: 60 }}>
                  <AspectRatioContainer>
                    <AspectRatioBox width={16} height={9} selected={ratio === '16:9'} />
                    <span>16:9</span>
                  </AspectRatioContainer>
                </Radio.Button>
                <Radio.Button value="custom" style={{ height: 60 }}>
                  <AspectRatioContainer>
                    <AspectRatioBox width={24} height={24} selected={ratio === 'custom'} />
                    <span>自定义宽高</span>
                  </AspectRatioContainer>
                </Radio.Button>
              </Radio.Group>
            </Form.Item>

            {useCustomSize && (
              <Form.Item label="自定义宽高">
                <Input.Group compact>
                  <Form.Item
                    name={['customSize', 'width']}
                    noStyle
                  >
                    <InputNumber min={1} placeholder="宽度" style={{ width: '50%' }} />
                  </Form.Item>
                  <Form.Item
                    name={['customSize', 'height']}
                    noStyle
                  >
                    <InputNumber min={1} placeholder="高度" style={{ width: '50%' }} />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            )}

            <Form.Item label="形象描述" required>
              <div style={{ position: 'relative' }}>
                <Form.Item
                  name="prompt"
                  noStyle
                  rules={[{ required: true, message: '请输入形象描述' }]}
                >
                  <TextArea
                    rows={4}
                    placeholder="请输入形象描述"
                    style={{ paddingRight: '80px' }}
                  />
                </Form.Item>
                <Button
                  icon={<ThunderboltOutlined />}
                  onClick={handleGenerateDescription}
                  loading={generatingDescription}
                  style={{
                    position: 'absolute',
                    bottom: 8,
                    right: 8,
                  }}
                >AI生成
                </Button>
              </div>
            </Form.Item>

            <Form.Item name="refImage" label="人像参考图">
              <ImageUploader max={1} type="Image" desc="上传参考图" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" loading={genLoading} onClick={onGenerate}>
                {`${genLoading ? '正在生成中' : '生成图片'}`}
              </Button>
            </Form.Item>
          </Form>
        </Col>
        <Col span={14} style={{ height: '100%' }}>
          {/* {taskQueryLoading && <Skeleton active/>}
          {!taskQueryLoading && */}
          <ScrollableContainer>
            <List
              dataSource={imageGroups}
              renderItem={(group: any) => (
                <List.Item>
                  <Card
                    type="inner"
                    title={`生成时间: ${group?.createTime}`}
                  >
                    {group?.status === 0 && 
                    <>
                      <div style={{ marginBottom: 8 }}>生成4张图片大约需要2分钟时间</div>
                      <Progress 
                        percent={Math.min(
                          Math.floor(
                            ((new Date().getTime() - new Date(group?.createTime).getTime()) / 1000 / 140) * 100
                          ), 
                          99
                        )} 
                        status="active"
                      />
                      <Skeleton active />
                    </>
                    }
                    <Row gutter={[8, 8]}>
                      {group.images.map((item, index) => (
                        <Col span={6} key={index} style={{ width: '200px' }}>
                          <StyledImageContainer>
                            <>
                              <Image
                                width="100%"
                                height="100%"
                                src={item}
                                style={{ objectFit: 'cover' }}
                              />
                              <Button
                                icon={<PlusOutlined />}
                                size="small"
                                className="add-to-library-button"
                                style={{
                                  position: 'absolute',
                                  bottom: 8,
                                  right: 8,
                                  display: 'none',
                                }}
                                onClick={() => handleAddToLibrary(group, item)}
                              >
                                入库
                              </Button>
                            </>
                          </StyledImageContainer>
                        </Col>
                      ))}
                    </Row>
                    <div style={{ marginTop: 16 }}>
                      <strong>Prompt:</strong> {group.prompt}
                    </div>
                  </Card>
                </List.Item>
              )}
            />
          </ScrollableContainer>
          {/* } */}
        </Col>
      </Row>
    </Drawer>
  );
};

export default ImageDrawer;