import { Card, Col, Image, Row } from "antd";

const QueryModeList = (props) => {
  const { data: data2D, loading: loading2D } = props;

  return <Row gutter={16}>
    {data2D?.[0]?.values?.[0]?.infoList.map((item, index) => (
      <Col key={index}>
        <Card
          hoverable
          style={{ width: 200, height: 200, marginBottom: 20 }} // 设置卡片宽度和高度
          bodyStyle={{ padding: 0 }} // 移除卡片内容的内边距
          loading={loading2D}
        >
          <div style={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Image
              alt={item.title}
              src={item.url}
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain',
                aspectRatio: 1
              }}
              preview={{ mask: '预览' }}
            />
          </div>
        </Card>
      </Col>
    ))}
  </Row>

};

export default QueryModeList;
