import React, { useState } from 'react';
import { Modal, message } from 'antd';
import { AppApi } from '@/api/app'; // 假设你的 API 在这个文件中
import ImageUploader from '@/pages/app/workflow/react-node/imageUploader';

interface ImageUploaderModalProps {
  visible: boolean;
  appId: string;
  onClose: () => void;
}

const ImageUploaderModal: React.FC<ImageUploaderModalProps> = ({ visible, onClose, appId }) => {

  const [file, setFile] = useState<any>(null);
  const handleOk = async () => {
    try {
      if (file) {
        await AppApi.portraitAdd({
          appId,
          type: 'PORTRAIT_2D',
          url: file?.url || file?.uri,
          nosKey: file?.key,
          style: 'user-upload',
          styleAlias: '用户上传',
        });
        message.success('上传成功');
        onClose();
      }
    } catch (error) {
      console.error('Error adding portrait:', error);
      message.error('Failed to add portrait');
    }
  };

  return (
    <Modal
      title="上传肖像"
      visible={visible}
      onOk={handleOk}
      onCancel={onClose}
      okText="添加到肖像库"
    >
      <ImageUploader value={file} max={1} onChange={setFile} desc="上传形象" permanent/>
    </Modal>
  );
};

export default ImageUploaderModal;