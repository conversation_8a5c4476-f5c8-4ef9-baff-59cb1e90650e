import { Avatar, List, Radio } from "antd";
import { useState } from "react";
import styled from "styled-components";

const StyledListItem = styled(List.Item)`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  position: relative;
`;

const StyledAvatar = styled(Avatar)`
  /* width: 100%; */
  height: auto;
  /* max-height: 64px; */
  object-fit: cover;
  cursor: pointer;
`;

const SelectModeList = (props) => {
  const { data: data2D, value, onSelect } = props;


  return <>
    <List
      grid={{ gutter: 16 }}
      dataSource={data2D?.[0]?.values?.[0]?.infoList}
      renderItem={(item: any) => {
        const checked = value?.url && item?.url === value?.url
        return (
          <StyledListItem>
            <span style={{
              position: 'absolute',
              top: 8,
              left: 10,
              zIndex: 1
            }}>
              <Radio checked={checked}
                onChange={ev => {
                  console.log('evevev', ev);
                  onSelect(ev.target.checked, item)
                }} />
            </span>
            <StyledAvatar
              size={128}
              shape="square"
              src={item?.url}
              style={{
                border: checked ? '4px solid #1890ff' : 'none',
              }}
              onClick={() => {
                onSelect(!checked, item)
              }}
            />
          </StyledListItem>
        )
      }}
    />
  </>
};

export default SelectModeList;
