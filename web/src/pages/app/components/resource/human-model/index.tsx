import React, { useState } from 'react';
import { Row, Col, Card, Pagination, Button, Space, Empty, Image, Skeleton } from 'antd';
import { useRequest } from 'ahooks';
import CreateDrawer from './components/create-drawer'
import UploadModal from './components/upload-modal';
import { AppApi } from '@/api/app';
import QueryModeList from './components/query-mode-list';
import SelectModeList from './components/select-mode-list';

const { Meta } = Card;

interface ImageGalleryProps {
  appId: string;
  selectionProps: any;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ appId, selectionProps }) => {
  const [currentPage2D, setCurrentPage2D] = useState(1);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [uploadImageVisible, setUploadImageVisible] = useState(false);
  const pageSize = 6; // 每页显示的图片数量，控制为一行

  const isSelectMode = typeof selectionProps !== "undefined";

  const { data: data2D = [], loading: loading2D, run: getImages } = useRequest(() => AppApi.portraitQuery({
    appId,
    type: 'PORTRAIT_2D',
    pageNum: currentPage2D,
    pageSize: pageSize,
  }).then(res => res), {
    refreshDeps: [currentPage2D],
  });

  const handleCreatePortrait = (type: string) => {
    setDrawerVisible(true);
    // 在这里处理创建肖像的逻辑
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3>2D肖像</h3>
        <Space>
          <Button onClick={() => handleCreatePortrait('2D')}>
            创建2D肖像
          </Button>
          <Button type="primary" onClick={() => setUploadImageVisible(true)}>
            上传2D肖像图
          </Button>
        </Space>
      </div>
      {loading2D && <Skeleton active />}
      {!loading2D && !data2D?.[0]?.values?.[0]?.infoList.length && <Empty />}
      {
        !loading2D && data2D?.[0]?.values?.[0]?.infoList.length && <>

          {isSelectMode ?
            <SelectModeList data={data2D} {...selectionProps} />
            : <QueryModeList data={data2D} loading={loading2D} />}
        </>
      }
      <Pagination
        current={currentPage2D}
        pageSize={pageSize}
        total={data2D?.[0]?.total || 0}
        onChange={setCurrentPage2D}
        style={{ marginTop: '20px', textAlign: 'center' }}
      />
      <UploadModal visible={uploadImageVisible} appId={appId} onClose={() => {
        setUploadImageVisible(false)
        getImages()
      }} />
      <CreateDrawer visible={drawerVisible} appId={appId} onClose={() => {
        setDrawerVisible(false)
        getImages()
      }} />
    </div>
  );
};

export default ImageGallery;