import { Mentions } from "antd";
import { useEffect, useRef } from "react";
import styled from "styled-components";

const Editor = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
`;

const TextPreview = styled.div`
  position:absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  /* textarea会多加一行 造成滚动不一致，加个padding能滚到和texterea一样的scrolltop */
  padding: 4px 11px 33px 11px;
  word-break: break-all;
  white-space: pre-wrap;
  box-sizing: border-box;
  line-height: 22px;
  font-size: 14px;
  overflow-y: auto;
  background: #f5f8fc;
`;

const TextInput = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 999;
  .ant-mentions {
    height: 100%;
    border: none;
    background-color: transparent;
    textarea {
      background-color: transparent !important;
    }
  }
  ::-webkit-scrollbar {
    display: none;
  }
 textarea {
    z-index: 9999;
    background: transparent;
    word-break: break-all;
    height: 100%;
    caret-color: #333;
    line-height: 22px;
    font-size: 14px;
    color: transparent; 
  }
`;

const PromptEditor = (props) => {
  const { value, onChange: onParentChange, paramsInPrompt, onBlur, disabled, id = '' } = props;
  const previewInput = useRef(null);
  const editorRef = useRef(null);

  const options = paramsInPrompt?.map(item => {
    return {
      value: `${item.key}}}`,
      label: `{{${item.key}}}`
    }
  })

  // 多个组件高亮时，所有组件的 CSS.highlights.set 方法的参数是同一个字符串，那么后面在 set 的时候就会覆盖前面已 set 的内容,为了不覆盖，动态插入样式
  useEffect(() => {
    const style = document.createElement('style');
    style.setAttribute('type', 'text/css');
    style.innerHTML =
      `.prompt-editor {
            ::highlight(user-variable-${id}) {
              color: red;
            }
            ::highlight(system-variable-${id}) {
              color: #1677ff;
            }
        }`
    editorRef.current.appendChild(style);
  }, [])


  const onChange = (newValue) => {
    onParentChange?.(newValue);
  };

  const getHighlightRanges = ({ regex, text, parentNode }) => {
    let match;
    const ranges = []
    while ((match = regex.exec(text)) !== null) {
      console.log(`找到内容 '${match[0]}'，位置从 ${match.index} 到 ${regex.lastIndex - 1}`);
      const range = new Range();
      range.setStart(parentNode, match.index);
      range.setEnd(parentNode, regex.lastIndex);
      ranges.push(range);
    }
    return ranges;
  };

  useEffect(() => {
    // previewInput.current.innerHTML = value;

    try {
      const parentNode = document.createTextNode(value);
      while (previewInput.current.firstChild) {
        previewInput.current.removeChild(previewInput.current.firstChild);
      }
      previewInput.current.appendChild(parentNode);

      // @ts-ignore
      if (!value || !CSS.highlights) return;

      const systemRegex = /{{_(.*?)}}/g;
      const userRegex = /{{(?!_).*?}}/g;

      const userRanges = getHighlightRanges({ regex: userRegex, text: value, parentNode });
      const systemRanges = getHighlightRanges({ regex: systemRegex, text: value, parentNode });
      // @ts-ignore
      const userHighlights = new Highlight(...userRanges);
      // @ts-ignore
      const systemHighlights = new Highlight(...systemRanges);

      // @ts-ignore
      CSS.highlights.set(`user-variable-${id}`, userHighlights);
      // @ts-ignore
      CSS.highlights.set(`system-variable-${id}`, systemHighlights);

    } catch (error) {
      console.log('===error===', error);
    }
  }, [value, id]);

  const onScroll = e => {
    previewInput.current.scrollTop = e.target.scrollTop;
  };

  return <Editor ref={editorRef} className="prompt-editor">
    <TextPreview ref={previewInput}>
    </TextPreview>
    <TextInput>
      <Mentions
        disabled={disabled}
        options={options}
        value={value}
        onChange={onChange}
        onScroll={onScroll}
        prefix="{{"
        onBlur={onBlur}
        placeholder={options?.length > 0 ? '输入 {{ 可插入变量' : ''}
      />
    </TextInput>
  </Editor>
}

export default PromptEditor;
