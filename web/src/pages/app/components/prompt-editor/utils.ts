// export const userParamsRegex = /{{(?!_)(.*?)}}/g;
// export const systemParamsRegex = /{{_(.*?)}}/g;
export const userParamsRegex = /{{(.*?)}}/g;
    
export const includesUserParams = (text) => {
  const res = [];
  let match;
  while((match = userParamsRegex.exec(text))!==null) {
    res.push(match[1]);
  }
  return res;
};

// export const filterParamsRegex = /code: '(\w+)'/g;

// export const filterParams = (text) => {
//   const res = [];
//   let match;
//   while((match = filterParamsRegex.exec(text))!==null) {
//     res.push(match[1]);
//   }
//   return res;
// };

export const filterParams = text => {
  let regex = /"code":"(\w+)"|{{(\w+)}}/g;
  let matches = [];
  let match;
  while ((match = regex.exec(text)) !== null) {
    matches.push(match[1] || match[2]);
  }
  return matches;
}