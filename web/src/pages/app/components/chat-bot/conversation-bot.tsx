import { <PERSON>tView } from "@music/lang";
import ParamsDebug from "./params-debug";
import { useEffect, useState, memo, useRef } from "react";
import { Flex, Tooltip } from "antd";
import TopBar from "./top-bar";
import { ClearIcon, SettingIcon } from '@/components/icons';
import { CodeSnippet } from '@/components/code-snippet';
import { getMaxRatioModel } from "./utils";
import ReferencesDrawer from "./references-drawer";
import ModelsSelect from "./models-select";
import { MultipleModelSetting } from "@/components/MultipleModelSetting";
import { IAppType } from "@/interface";
import DiffAlert from "../agent-dev/mods/diff-alert";

// 聊天助理
const ConversationBot = memo((props: any) => {
  const { onReset, hiddenConfig = [], config, onModelsChange, oldModelsConfig, ...rest } = props;
  const [paramsValue, setParamsValue] = useState();
  const [updateConfig, setUpdateConfig] = useState({});

  const references = useRef(null);

  useEffect(() => {
    // paramsInPrompt改变的时候需要刷新默认值

    const initialValue = config?.paramsInPrompt?.reduce((acc, cur) => {
      // @ts-ignore
      acc[cur.key] = paramsValue && paramsValue.hasOwnProperty(cur.key) ? paramsValue[cur.key] : cur.default_val
      return acc;
    }, {});
    setParamsValue(initialValue);
  }, [config?.paramsInPrompt]);

  useEffect(() => {
    const model = getMaxRatioModel(config?.modelsConfig?.models);

    setUpdateConfig({
      ...updateConfig,
      modelsConfig: {
        retryConfig: config?.modelsConfig?.retryConfig,
        models: [{
          ratio: 1,
          modelName: model.modelName,
          providerKind: model.providerKind,
          modelParams: model.modelParams,
          model: model?.model
        }]
      },

      // 虚拟人用的是如下几个参数
      modelName: model.modelName,
      providerKind: model.providerKind,
      modelParams: model.modelParams,
    });
  }, [config?.modelsConfig?.models]);

  const onParamsChange = parameters => {
    setParamsValue(parameters)
  };

  const debugConfig = {
    ...config,
    ...updateConfig,
  };

  const onModelsConfig = (val) => {

    // 聊天助理最多只选择一个模型
    if (config?.type === IAppType.AgentConversation) {
      onModelsChange({
        retryConfig: val?.retryConfig,
        models: [{
          ...(val?.models?.[0] || {}),
          ratio: 1
        }]
      });
    } else {
      onModelsChange(val);
    }
  }

  return <div style={{ height: '100%' }}>
    <ChatView
      {...rest}
      config={debugConfig}
      showParams={false}
      components={{
        Code: CodeSnippet
      }}
      paramsValue={paramsValue}
      renderNavbar={navbar => {
        const resetIcon = navbar.rightContent?.find(item => item.label === '删除会话记录')
        return <TopBar {...navbar} title=""
          leftContent={<Flex align="center">
            <Flex>
              <MultipleModelSetting
                destroyModal
                appType={config?.type}
                value={config?.modelsConfig}
                onChange={onModelsConfig}
              />
              <DiffAlert type="modelsConfig" title="模型及参数" newValue={config?.modelsConfig} oldValue={oldModelsConfig}
                onRedo={() => {
                  onModelsChange(oldModelsConfig);
                }}
              />
            </Flex>
          </Flex>}
          style={{ fontSize: 14, color: '#aaa' }}
          rightContent={<Flex justify="end" gap={8}>
            {config?.paramsInPrompt?.length > 0 && <ParamsDebug
              paramsInPrompt={config?.paramsInPrompt}
              onChange={onParamsChange}
              value={paramsValue}
            />}
            <Tooltip title={resetIcon.label} >
              <ClearIcon
                style={{ cursor: 'pointer', marginLeft: 10 }}
                onClick={(val) => {
                  resetIcon.onClick(val)
                  onReset()
                }}
              />
            </Tooltip>
            {
              props?.handlers?.SYSTEM_SETTING && <SettingIcon
                style={{ cursor: 'pointer' }}
                onClick={props?.handlers?.SYSTEM_SETTING}
              />
            }
          </Flex>}
        />
      }}
      showReferences
      onReferencesClick={val => {
        references.current?.show(val);
      }}
    />
    <ReferencesDrawer ref={references} />
  </div>

}, (prevProps, nextProps) => {
  // 控制重渲染，配置不变情况不要重渲染
  if (prevProps?.useTts !== nextProps?.useTts) {
    return false
  }
  if (JSON.stringify(prevProps?.config) === JSON.stringify(nextProps?.config)
    && prevProps?.greetings === nextProps?.greetings) {
    return true
  }

  return false
})

export default ConversationBot;
