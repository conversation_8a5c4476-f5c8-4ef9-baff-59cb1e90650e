import { Flex, Spin, Typography } from 'antd';
import styled from 'styled-components';
import { IAppType } from '@/interface';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { AgentTemplateApi } from '@/api/agent-template';
import { OpenAiAPI } from '@/api/conversation';
import { CompletionStreamResponse } from '@/interface/conversation';
import { Markdown } from '@/components/Markdown';
import { CopyOutlined, RightOutlined, RobotOutlined } from '@ant-design/icons';
import { copyText, getFileData, getPopoFileData } from '@/utils/common';
import ModelsSelect from '../chat-bot/models-select';
import { getMaxRatioModel } from '../chat-bot/utils';
import { omit, pick } from 'lodash';



const { Title } = Typography;

const ResultContainer = styled.div`
  flex:1;
  display: flex;
  flex-direction: column;
  width: 100%;

  .result-title {
    color: #333;
    margin-bottom: 10px;
  }
  .reasoning-text {
    margin-bottom: 10px;
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    font-size: 12px;
    color: #999;
  }

  .reasoning-header {
    user-select: none;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #aaa;
    font-size: 12px;
    margin-bottom: 10px;
    
    .anticon {
      margin-right: 4px;
      transition: transform 0.3s;
      
      &.expanded {
        transform: rotate(90deg);
      }
    }
  }

  .result-text {
    width: 100%;
    flex:1;
    background-color: white;
    border: 1px solid rgb(217, 217, 217);
    border-radius: 8px;
    padding: 20px;
    box-sizing: border-box;
    blockquote {
      color: #999;
      border-left: 3px solid #9993;
      margin-left: 0;
      padding-left: 20px;
    }
  }
`;

interface RightContentProps {
  app: any;
  modelConfig?: any;
  prePrompt?: string;
  setPrePrompt?: (prePrompt: string) => void;
  tools?: any;
  newPrompt: any;
  debugConfig: Record<string, any>;
  mode?: string;
  remoteConfig?: any;
  onModelsChange?: (models: any) => void;
  scrollContainer?: any;
  renderNavBar?: () => any;
}

function CompletionResult({
  app,
  setPrePrompt,
  newPrompt,
  debugConfig: parentDebugConfig,
  scrollContainer,
  renderNavBar,
}: RightContentProps, ref) {
  const [completeLoading, setCompleteLoading] = useState(false);
  const [res, setRes] = useState('');
  const [reasoning, setReasoning] = useState('');

  const [reasoningExpanded, setReasoningExpanded] = useState(true);
  const [debugModel, setDebugModel] = useState<any>({});

  let completeCache = useRef('');
  let reasoningCache = useRef('');

  const debugConfig: Record<string, any> = {
    ...(omit(parentDebugConfig || {}, ['modelsConfig'])),
    modelsConfig: {
      retryConfig: parentDebugConfig?.modelsConfig?.retryConfig,
      models: [debugModel]
    }
    // ...(pick(debugModel, ['modelName', 'modelParams', 'providerKind']))
  };

  useEffect(() => {
    // 获取概率最大的模型调试
    const debugModel = getMaxRatioModel(parentDebugConfig?.modelsConfig?.models);

    setDebugModel(debugModel);
  }, [parentDebugConfig?.modelsConfig]);

  const scrollBottom = () => {

    jsScroll.current = true;
    // clearTimeout(scrollTimer.current);
    // scrollTimer.current = null;
    // const resEle = compeleteResultRef.current;
    if (scrollContainer) {
      // const scrollHeight = scrollContainer.scrollHeight;
      // const scrollTop = scrollHeight - scrollContainer.clientHeight;
      // if (scrollTop < 0) return;

      // scrollContainer.scrollTop = scrollTop + 20;
      scrollContainer.scrollTo({
        top: scrollContainer.scrollHeight,
        behavior: 'smooth'
      });
    }
    scrollTimer.current = setTimeout(() => {
      jsScroll.current = false;
      clearTimeout(scrollTimer.current);
      scrollTimer.current = null;
    }, 200);
  }

  const jsScroll = useRef(true);
  const userScroll = useRef(false);
  const scrollTimer = useRef(null);

  const scrollEvent = () => {
    if (jsScroll.current) {
      // 是JS的滚动，不用处理
    } else {
      userScroll.current = true;
    }
  };

  const resetScroll = () => {
    jsScroll.current = true;
    userScroll.current = false;
    clearTimeout(scrollTimer.current);
    scrollTimer.current = null;
  }

  useEffect(() => {
    if (!scrollContainer) return;
    scrollContainer?.addEventListener('scroll', scrollEvent);
    return () => {
      resetScroll();
      scrollContainer?.removeEventListener('scroll', scrollEvent);
    }
  }, [scrollContainer]);

  const onRun = (prePrompt: string, params: any) => {
    resetScroll();
    setCompleteLoading(true);
    setRes('');
    completeCache.current = '';
    reasoningCache.current = '';
    const { images, imageUrl, audioUrl, videoUrl, ...rest } = params;

    OpenAiAPI.completion({
      appID: app.id,
      parameters: rest,
      images,
      imageUrl,
      audioUrl,
      videoUrl,
      config: debugConfig,
      responseMode: (imageUrl || audioUrl || videoUrl) ? 'json' : 'streaming',
      onMessage: (d: CompletionStreamResponse) => {
        if (d.error) {
          setRes(d.error);
          return;
        }
        if ((d.content || d.reasoning_content) && !d.toolCalls) {
          completeCache.current = completeCache.current + (d.content || '');
          reasoningCache.current = reasoningCache.current + (d.reasoning_content || '');
          setRes(completeCache.current);
          setReasoning(reasoningCache.current);

          if (!scrollTimer.current && !userScroll.current) {
            scrollBottom();
          }
        }
      },
      onFinish: () => {
        setCompleteLoading(false);
      },
    });
  }

  const onDebugModelChange = (val) => {
    const model = parentDebugConfig?.modelsConfig?.models?.find(it => it.modelName === val);
    setDebugModel(model);
  }

  const genPrePrompt = async () => {
    if (newPrompt?.promptType === 'raw') {
      setPrePrompt?.(newPrompt.prompt);
      // setChatViewResetVisible(false);
      return newPrompt.prompt;

    } else if (newPrompt.promptType === 'struct') {
      const res = await AgentTemplateApi.struct2Text({ structPrompt: newPrompt.structPrompt });
      setPrePrompt?.(res.prompt);
      // setChatViewResetVisible(false);
      return res.prompt;
    }
  }

  const onCompleteRun = async (params: any) => {
    for (const k in params) {
      if (typeof params[k] === 'object' && params[k].renderType === 'file') {
        params[k] = await getFileData(params[k].key);
      }
      if (typeof params[k] === 'object' && params[k].renderType === 'popo-link') {
        params[k] = await getPopoFileData(params[k].link);
      }

    };
    const _prePrompt = await genPrePrompt();
    onRun(_prePrompt, params);
  }

  useImperativeHandle(ref, () => {

    return {
      onCompleteRun
    };
  });

  return (
    <ResultContainer>
      {renderNavBar ? renderNavBar() : <Flex justify='space-between' align='center'>
        <Flex gap={10} align='center'>
          <Title level={5}>调试结果
            <span style={{ paddingLeft: 10 }}>
              <ModelsSelect
                options={parentDebugConfig?.modelsConfig?.models}
                value={debugModel?.modelName}
                onChange={onDebugModelChange}
                trigger={<span
                  style={{ fontSize: 14, color: '#aaa', fontWeight: 'normal', cursor: 'pointer' }} >
                  {debugModel?.modelName} <RobotOutlined />
                  {/* <IconButton icon={<RobotOutlined />} label="模型"></IconButton> */}
                </span>} />
            </span>
          </Title>
        </Flex>
        <Flex>
          <Title level={5}>
            <CopyOutlined style={{ marginLeft: 5, fontSize: 14, color: '#aaa' }} onClick={() => {
              copyText(res);
            }} />

          </Title>
        </Flex>
      </Flex>}

      <div className="result-text">
        {reasoning && (
          <>
            <div
              className="reasoning-header"
              onClick={() => setReasoningExpanded(!reasoningExpanded)}
            >
              <RightOutlined className={reasoningExpanded ? 'expanded' : ''} />
              <span>推理过程</span>
            </div>
            {reasoningExpanded && (
              <div className="reasoning-text">
                {completeLoading && !reasoning ? <Spin /> : <Markdown content={reasoning} />}
              </div>
            )}
          </>
        )}
        {completeLoading && !res ? <Spin /> : <Markdown content={res} />}
      </div>
    </ResultContainer>
  );
}


export default React.forwardRef(CompletionResult);