.TopBar {

    position: relative;
    z-index: 10;
    display: flex;
    align-items: center;
    margin: 0 12px;
    padding-top: 0;
    border-bottom: 1px solid #ededed;
    background: white;
    cursor: default;

    &.draggable {
        cursor: move;
    }

    &-left {
        flex: 1;
        display: flex;
        gap: 8px;
        align-items: center;
        min-height: 44px;
    }

    &-left,
    &-right {
        min-width: 58px;
    }

    &-right {
        display: flex;
        align-items: center;
        white-space: nowrap;
        justify-content: flex-end;
    }

    &-title {
        margin: 0;
        color: rgb(48, 48, 48);
        font-size: 14px;
        font-weight: 500;
        text-align: center;
    }

    &-logo {
        width: auto;
        height: 20px;
    }

    .IconBtn {
        color: #111;
        cursor: pointer;
    }

    .IconBtn+.IconBtn {
        margin-left: 9px;
    }
}