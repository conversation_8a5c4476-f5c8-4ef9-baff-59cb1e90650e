import { ParamTypeComponentMap } from "@/interface/agent";
import { Form, Input } from "antd";
import { useEffect, useImperativeHandle, forwardRef } from "react";

interface IProps {
  paramsInPrompt: any[];
  value: Record<string, any>;
  formProps?: any;
}

const ParamsDebugForm = forwardRef((props: IProps, ref) => {
  const { paramsInPrompt, value, formProps = {} } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(value);
  }, [value]);

  useImperativeHandle(ref, () => form);

  return <div>

    <Form
      form={form}
      labelCol={{ span: 6 }}
      labelWrap
      wrapperCol={{ span: 16 }}
      {...formProps}
    >
      {paramsInPrompt?.map(v => {
        const Component = ParamTypeComponentMap[v.type] || Input;
        let content = <Component
          placeholder={`请输入${v.title || v.key}`}
        />

        if (v.type === 'list') {
          content = (
            <Component
              placeholder="请选择"
            >
              {v.config.options.map((opt: any) => (
                <option value={opt.value}>{opt.title || opt.value}</option>
              ))}
            </Component>
          );
        }
        return (
          <Form.Item
            label={v.title || v.key}
            name={v.key}
          // rules={[{
          //   required: v.required,
          // }]}
          // invalid={!!errors[v.key]}
          // help={errors[v.key]}
          >
            {content}
          </Form.Item>
        );

      })}
    </Form>

  </div>

});

export default ParamsDebugForm;
