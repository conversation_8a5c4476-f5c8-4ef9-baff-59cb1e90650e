import { <PERSON><PERSON>, Drawer, Flex, List } from "antd";
import { forwardRef, useImperativeHandle, useState } from "react";
import styled from "styled-components";

const StyledList = styled(List)`
  h4 {
    text-align: left;
  }
`;

const ReferencesDrawer = (props, ref) => {
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<any[]>();

  useImperativeHandle(ref, () => ({
    show: (data) => {
      setData(data);
      setOpen(true);
    },
    close: () => {
      setData(undefined);
      setOpen(false);
    }
  }));

  const onClose = () => {
    setData(undefined);
    setOpen(false);
  };

  return <>
    <Drawer
      title={`引用资料（${data?.length}）`}
      width={800}
      open={open}
      onClose={onClose}>
      <StyledList
        itemLayout="vertical"
        dataSource={data}
        renderItem={(item: any, index) => (
          <List.Item
            key={index}
            actions={[
              <span>
                <a href={item?.url} target="_blank">{item?.site_name}</a>
              </span>,
              <span>{item?.publish_time}</span>
            ]}>
            <List.Item.Meta
              avatar={<Avatar src={item?.logo_url} />}
              title={<a href="https://ant.design">{item?.title}</a>}
              description={item?.summary}
            />
          </List.Item>
        )}

      >

      </StyledList>
    </Drawer>
  </>
}

export default forwardRef(ReferencesDrawer);