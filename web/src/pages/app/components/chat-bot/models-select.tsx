import { IconButton } from "@/components/icons";
import { RobotOutlined } from "@ant-design/icons";
import { Popover, Radio } from "antd";
import { useState } from "react";

const ModelsSelect = (props) => {
  const { options: modelList, onChange: onParentChange, value, trigger, multiple = false } = props;
  const [open, setOpen] = useState(false);

  const onChange = (ev) => {
    onParentChange?.(ev.target.value);
    setOpen(false);
  };

  const options = (modelList?.map(item => {
    return {
      label: item.modelName,
      value: item.modelName,
      data: item
    }
  }) || []).concat(multiple ? [{
    label: '随机调用',
    value: '_random'
  }] : null).filter(op => !!op);


  return <Popover
    placement="bottomRight"
    trigger="click"
    open={open}
    onOpenChange={newOpen => {
      setOpen(newOpen);
    }}
    content={
      <Radio.Group
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: 8
        }}
        options={options}
        value={value}
        onChange={onChange}
      >

      </Radio.Group>
    }
  >
    {trigger ?? <IconButton icon={<RobotOutlined />} label={"模型"}></IconButton>}
  </Popover>
};

export default ModelsSelect;