import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { IAppType } from '@/interface';
import { AgentParamsRender } from '@components/agent-params-render/complete';
import CompletionDebugBots from '@components/lang-bot-component/completion-debug-bots';
import { Flex } from 'antd';
import { ICompletionConfig } from '@interface/config';
import ModelModal from '@components/model-modal';
import { RobotOutlined } from '@ant-design/icons';
import { useGlobalState } from '@hooks/useGlobalState';
import { useLocalStorage } from '@hooks/useStorage';
import { saveLocalData } from '@utils/common';

export const ModelDebug = (props: Record<string, any>) => {
  return (
    <div>
      {props.app.type === IAppType.AgentCompletion && <Completion {...props} />}
      {props.app.type === IAppType.AgentConversation && <Conversation />}
    </div>
  );
};

// 生成型
const Completion = (props: Record<string, any>) => {
  const {
    app,
    paramsInPrompt,
    setInputParams,
    modelConfig,
    debugConfig,
    completionResults,
    onModelsChange,
    newPrompt,
    compeleteResultRef,
    remoteConfig,
  } = props;
  const [editModelName, setEditModelName] = useState(['', '']);
  const [modelConfigs, setModelConfigs] = useState<any[]>([]);

  const { globalState } = useGlobalState();
  const { modelList } = globalState;

  const [localConfig] = useLocalStorage(`app-${app?.id}`);

  const completionConfig: ICompletionConfig = {
    ...(debugConfig || {}),
    modelsConfig: debugConfig?.modelsConfig,
  };

  // 初始化两个模型选择器
  useEffect(() => {
    if (editModelName.length === 0) {
      setEditModelName(['', '']); // 确保有两个模型选择器
    }
  }, [editModelName.length]);

  const handleModelChange = (value: Record<string, any>, index: number) => {
    console.log('模型选择变化:', value);
    const selectedModel = value.selectedModels[0];
    const modelName = selectedModel.name;

    // 更新模型名称
    setEditModelName((prev) => prev.map((i, idx) => (idx === index ? modelName : i)));

    // 保存到本地存储
    saveLocalData(`app-${app?.id}`, {
      ...(localConfig || {}),
      modelSelectSettings: value,
    });

    // 找到目标模型
    const targetModel = modelList.find((it) => it.name === modelName);
    console.log('选择的模型:', targetModel, modelName);

    // 更新模型配置
    const newModelConfigs = [...modelConfigs];
    newModelConfigs[index] = {
      ...completionConfig,
      modelName: modelName,
      modelParams: targetModel?.modelParams || {},
      providerKind: targetModel?.providerKind || '',
    };
    setModelConfigs(newModelConfigs);
  };

  const getDefaultValue = useCallback(
    (modelName: string) => {
      return {
        selectedModels: modelName
          ? [
              {
                key: modelName,
                name: modelName,
                // 其他必要的字段可以从 modelList 中获取
                ...modelList.find((m) => m.name === modelName),
              },
            ]
          : [],
        multiple: {
          cost: 1,
          speed: 1,
          context: 1,
          performance: 1,
        },
      };
    },
    [modelList],
  );

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
      overflow: 'hidden'
    }}>
      {/* 参数输入区域 - 固定高度 */}
      {app.type === IAppType.AgentCompletion && (
        <div style={{ flexShrink: 0 }}>
          <AgentParamsRender
            paramsInPrompt={paramsInPrompt}
            onCommit={(val) => {
              console.log('开始运行模型对比，参数:', val);
              // 使用 completionResults 来运行两个模型
              // @ts-ignore
              completionResults.current?.onCompleteRun(val);
            }}
            onChange={setInputParams}
            modelConfig={modelConfig}
            modelsConfig={debugConfig?.modelsConfig}
            labelAlign="left"
            showCommitBtn={true}
          />
        </div>
      )}

      {/* 模型对比结果区域 - 可滚动 */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        minHeight: 0 // 重要：确保 flex 子元素可以收缩
      }}>
        <CompletionDebugBots
          ref={completionResults}
          modelsConfig={debugConfig?.modelsConfig}
          onModelsChange={onModelsChange}
          app={app}
          newPrompt={newPrompt}
          scrollContainer={compeleteResultRef.current}
          showModels={false}
          configs={editModelName.map((modelName, index) => {
            const targetModel = modelList.find((m) => m.name === modelName);
            return {
              title: (
                <ModelModal
                  title={`选择模型 ${index + 1}`}
                  modelList={modelList}
                  onConfirm={(data) => handleModelChange(data, index)}
                  mode="single"
                  defaultValue={getDefaultValue(modelName) as any}
                >
                  <div
                    style={{
                      display: 'inline-block',
                      minWidth: '120px',
                      padding: '4px 11px',
                      fontSize: '14px',
                      fontWeight: 'normal',
                      lineHeight: '22px',
                      cursor: 'pointer',
                      borderRadius: '6px',
                      border: '1px solid #d9d9d9',
                      color: modelName ? '#000' : '#999',
                    }}
                  >
                    {modelName || `请选择模型 ${index + 1}`} <RobotOutlined />
                  </div>
                </ModelModal>
              ),
              config: {
                ...completionConfig,
                modelName: modelName,
                modelParams: targetModel?.modelParams || {},
                providerKind: targetModel?.providerKind || '',
                prePrompt: newPrompt?.prePrompt || completionConfig?.prePrompt,
              },
            };
          })}
        />
      </div>
    </div>
  );
};

// 聊天型
const Conversation = () => {
  return <div>聊天型</div>;
};
