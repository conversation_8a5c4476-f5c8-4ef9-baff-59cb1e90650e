import React, { useCallback, useMemo, useState } from 'react';
import { IAppType } from '@/interface';
import { AgentParamsRender } from '@components/agent-params-render/complete';
import CompletionDebugBots from '@components/lang-bot-component/completion-debug-bots';
import { Flex } from 'antd';
import { ICompletionConfig } from '@interface/config';
import ModelModal from '@components/model-modal';
import { RobotOutlined } from '@ant-design/icons';
import { useGlobalState } from '@hooks/useGlobalState';
import { useLocalStorage } from '@hooks/useStorage';
import { saveLocalData } from '@utils/common';

export const ModelDebug = (props: Record<string, any>) => {
  return (
    <div>
      {props.app.type === IAppType.AgentCompletion && <Completion {...props} />}
      {props.app.type === IAppType.AgentConversation && <Conversation />}
    </div>
  );
};

// 生成型
const Completion = (props: Record<string, any>) => {
  const {
    app,
    paramsInPrompt,
    setInputParams,
    modelConfig,
    debugConfig,
    completionResults,
    onModelsChange,
    newPrompt,
    compeleteResultRef,
    remoteConfig,
  } = props;
  const [editModelName, setEditModelName] = useState(['', '']);

  const { globalState } = useGlobalState();
  const { modelList } = globalState;

  const [localConfig] = useLocalStorage(`app-${app?.id}`);

  const completionConfig: ICompletionConfig = {
    ...(debugConfig || {}),
    modelsConfig: debugConfig?.modelsConfig,
  };

  const handleModelChange = (value: Record<string, any>, index: number) => {
    console.log(111111, 'val', value);
    let modelName = editModelName[index];
    const selectedModel = value.selectedModels[0];
    setEditModelName((prev) =>
      prev.map((i, idx) => (idx === index ? selectedModel.name : i)),
    );
    modelName = selectedModel.name;
    saveLocalData(`app-${app?.id}`, {
      ...(localConfig || {}),
      modelSelectSettings: value,
    });
    const targetModel = modelList.find((it) => it.name === modelName);
    console.log(22222, targetModel, modelName);
  };

  const getDefaultValue = useCallback(
    (modelName: string) => {
      return {
        selectedModels: modelName
          ? [
              {
                key: modelName,
                name: modelName,
                // 其他必要的字段可以从 modelList 中获取
                ...modelList.find((m) => m.name === modelName),
              },
            ]
          : [],
        multiple: {
          cost: 1,
          speed: 1,
          context: 1,
          performance: 1,
        },
      };
    },
    [modelList],
  );

  return (
    <>
      {app.type === IAppType.AgentCompletion && (
        <AgentParamsRender
          paramsInPrompt={paramsInPrompt}
          onCommit={(val) => {
            // @ts-ignore
            completionResult.current?.onCompleteRun(val);
          }}
          onChange={setInputParams}
          modelConfig={modelConfig}
          modelsConfig={debugConfig?.modelsConfig}
          labelAlign="left"
        />
      )}
      <CompletionDebugBots
        ref={completionResults}
        modelsConfig={debugConfig?.modelsConfig}
        onModelsChange={onModelsChange}
        app={app}
        newPrompt={newPrompt}
        scrollContainer={compeleteResultRef.current}
        showModels={false}
        configs={editModelName.map((modelName, index) => {
          return {
            title: (
              <ModelModal
                title="选择模型"
                modelList={modelList}
                onConfirm={(data) => handleModelChange(data, index)}
                mode="single"
                defaultValue={getDefaultValue(modelName) as any}
              >
                <div
                  style={{
                    display: 'inline-block',
                    minWidth: '120px',
                    padding: '4px 11px',
                    fontSize: '14px',
                    fontWeight: 'normal',
                    lineHeight: '22px',
                    cursor: 'pointer',
                    borderRadius: '6px',
                    border: '1px solid #d9d9d9',
                    color: modelName ? '#000' : '#999',
                  }}
                >
                  {modelName || '请选择模型'} <RobotOutlined />
                </div>
              </ModelModal>
            ),
            config: {
              ...completionConfig,
              prePrompt: remoteConfig?.prePrompt,
            },
          };
        })}
      />
    </>
  );
};

// 聊天型
const Conversation = () => {
  return <div>聊天型</div>;
};
