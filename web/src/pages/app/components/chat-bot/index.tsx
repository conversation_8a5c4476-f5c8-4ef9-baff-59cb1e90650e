import { <PERSON>t<PERSON>iew } from "@music/lang";
import ParamsDebug from "./params-debug";
import { useEffect, useState, memo } from "react";
import { Flex, Tooltip } from "antd";
import TopBar from "./top-bar";
import ViewPrompt from "../virtual-human/groups-prompt/view-prompt";
import ViewMemory from "../virtual-human/groups-prompt/view-memory";
import UseTts from "../virtual-human/groups-prompt/use-tts";
import { EyeOutlined } from "@ant-design/icons";
import { ClearIcon, SettingIcon } from '@/components/icons';
import { CodeSnippet } from '@/components/code-snippet';
import ModelsSelect from "./models-select";
import { getMaxRatioModel } from "./utils";
import { IAppType } from "@/interface";

const ChatBot = memo((props: any) => {
  const { onReset, hiddenConfig = [], config: parentConfig, ...rest } = props;
  const { config, ...otherConfig } = parentConfig || {};
  const [paramsValue, setParamsValue] = useState();
  const [updateConfig, setUpdateConfig] = useState({});

  const [currentModel, setCurrentModel] = useState();


  useEffect(() => {
    // paramsInPrompt改变的时候需要刷新默认值
    const initialValue = config?.paramsInPrompt?.reduce((acc, cur) => {
      // @ts-ignore
      acc[cur.key] = paramsValue && paramsValue.hasOwnProperty(cur.key) ? paramsValue[cur.key] : cur.default_val
      return acc;
    }, {});
    setParamsValue(initialValue);
  }, [config?.paramsInPrompt]);

  // useEffect(() => {
  //   const model = getMaxRatioModel(config?.modelsConfig?.models);

  //   setUpdateConfig({
  //     ...updateConfig,
  //     modelsConfig: {
  //       models: [{
  //         ratio: 1,
  //         modelName: model.modelName,
  //         providerKind: model.providerKind,
  //         modelParams: model.modelParams,
  //         model: model?.model
  //       }]
  //     },

  //     // 虚拟人用的是如下几个参数
  //     modelName: model.modelName,
  //     providerKind: model.providerKind,
  //     modelParams: model.modelParams,
  //   });

  //   setCurrentModel(model.modelName);
  // }, [config?.modelsConfig?.models]);

  const onParamsChange = parameters => {
    setParamsValue(parameters)
  };

  const onModelChange = (val) => {
    if (val === '_random') {
      setUpdateConfig({
        ...updateConfig,
        modelsConfig: {
          ...config?.modelsConfig
        },
      });
      setCurrentModel(val);

      return;
    };
    const model = config?.modelsConfig?.models?.find(it => it.modelName === val);
    if (model) {
      setUpdateConfig({
        ...updateConfig,
        modelsConfig: {
          retryConfig: config?.modelsConfig?.retryConfig,
          models: [{
            ratio: 1,
            modelName: model.modelName,
            providerKind: model.providerKind,
            modelParams: model.modelParams,
            model: model?.model
          }]
        },

        // 虚拟人用的是如下几个参数
        modelName: model.modelName,
        providerKind: model.providerKind,
        modelParams: model.modelParams,
      });
    }
    setCurrentModel(val);
  };

  const debugConfig = {
    ...config,
    ...updateConfig,
  }


  return <div style={{ height: '100%', width: '100%' }}>
    <ChatView
      type="virtual-human"
      {...rest}
      config={{
        ...otherConfig,
        config: debugConfig
      }}
      showParams={false}
      components={{
        Code: CodeSnippet
      }}
      paramsValue={paramsValue}
      renderNavbar={navbar => {
        const resetIcon = navbar.rightContent?.find(item => item.label === '删除会话记录')
        return <TopBar {...navbar} style={{ fontSize: 14, color: '#aaa' }}
          rightContent={<Flex justify="end" gap={8}>
            {/* <ModelsSelect
              options={config?.modelsConfig?.models}
              onChange={onModelChange}
              value={currentModel}
              // multiple={parentConfig.type === IAppType.VirtualHuman}
              
            /> */}
            {rest?.useTts ? <UseTts></UseTts> : null}
            {!hiddenConfig?.includes('memoryConfig') && config?.memoryConfig?.longMemoryType !== 'NONE' && <ViewMemory
              appId={props.appId}
              config={props.config?.config}
              settingId={props.settingId}
              userId={props.userId}
            />}
            {/* {!hiddenConfig?.includes('viewPrompt') && <ViewPrompt
              appId={props.appId}
              config={props.config?.config}
              parameters={paramsValue}
              userId={props.userId}
              settingId={parentConfig?.settingId}
              paramsInPrompt={parentConfig?.paramsInPrompt}
            // trigger={<EyeOutlined />}
            />} */}
            {parentConfig?.paramsInPrompt?.length > 0 && <ParamsDebug
              paramsInPrompt={parentConfig?.paramsInPrompt}
              onChange={onParamsChange}
              value={paramsValue}
            />}
            <Tooltip title={resetIcon.label} >
              <ClearIcon
                style={{ cursor: 'pointer', marginLeft: 10 }}
                onClick={(val) => {
                  resetIcon.onClick(val)
                  onReset()
                }}
              />
            </Tooltip>
            {
              props?.handlers?.SYSTEM_SETTING && <SettingIcon
                style={{ cursor: 'pointer' }}
                onClick={props?.handlers?.SYSTEM_SETTING}
              />
            }
          </Flex>}
        />
      }}
    />
  </div>

}, (prevProps, nextProps) => {
  // 控制重渲染，配置不变情况不要重渲染
  if (prevProps?.useTts !== nextProps?.useTts) {
    return false
  }
  if (JSON.stringify(prevProps?.config) === JSON.stringify(nextProps?.config)
    && prevProps?.greetings === nextProps?.greetings) {
    return true
  }

  return false
})

export default ChatBot;
