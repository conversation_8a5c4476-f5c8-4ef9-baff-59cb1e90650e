import { IconButton, VariableIcon } from '@/components/icons';
import { Flex, Modal, Tooltip } from "antd";
import { useState, useRef } from "react";
import ParamsDebugForm from './params-debug-form';

const ParamsDebug = (props) => {
  const { paramsInPrompt, value, onChange } = props;
  // const [form] = Form.useForm();
  const [open, setOpen] = useState(false);

  const formRef = useRef(null);

  const onOpen = () => {
    formRef.current?.setFieldsValue(value);
    setOpen(true);
  };

  const onOk = async () => {
    const newValue = await formRef.current?.validateFields();
    onChange?.(newValue);
    setOpen(false);
  };

  const onCancel = () => {
    setOpen(false);
  }

  return <div>
    {paramsInPrompt?.length > 0 && <Tooltip title="修改变量">
      <Flex onClick={onOpen} >
        <IconButton icon={<VariableIcon />} label="变量" />
      </Flex>
    </Tooltip>}

    <Modal title="变量"
      open={open}
      width={600}
      okText="保存变量，开始对话"
      onOk={onOk}
      onCancel={onCancel}
      centered
    >
      <ParamsDebugForm
        paramsInPrompt={paramsInPrompt}
        value={value} ref={formRef} />
    </Modal>
  </div>

};

export default ParamsDebug;
