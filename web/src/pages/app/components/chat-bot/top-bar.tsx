import classNames from "classnames";

import './index.less';
import { Flex, Select } from "antd";
const TopBar = (props) => {
  const { className, title, logo, rightContent = [], leftContent, ...reset } = props;

  return <header
    className={classNames('TopBar ada-nav-bar', className, reset.draggable ? 'draggable' : '')}
    {...reset}
  >
    {/* <div className="TopBar-left">{leftContent && <IconButton size="md" {...leftContent} />}</div> */}
    <Flex className="TopBar-left">
      {logo && <img className="TopBar-logo" src={logo} alt={title} />}
      {title && <span className="TopBar-title">{title}</span>}
      {leftContent && <Flex>{leftContent}</Flex>}
    </Flex>

    <div className="TopBar-right">
      {/* {rightContent.map((item, idx) => (
        <IconButton size="md" {...item} key={idx} />
      ))} */}
      {rightContent}
    </div>
  </header>
}

export default TopBar;
