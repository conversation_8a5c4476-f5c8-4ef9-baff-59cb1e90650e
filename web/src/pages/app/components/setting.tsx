import { useRequest } from 'ahooks';
import { <PERSON><PERSON>, But<PERSON>, Card, Form, message, Modal, Result, Space, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useCallback } from 'react';
import styled from 'styled-components';

import { AppApi } from '@/api/app';
import { EnvApi } from '@/api/env';
import EnvComp from '@/components/env-setting';
import {
  LForm,
  LFormAppType,
  LFormRadioGroup,
  LFormInput,
  LFormSelect,
  LFormTextArea,
} from '@/components/form';
import { MidContainer } from '@/components/layout-container';
import { TokenSetting } from '@/components/TokenSetting';
import { useAdmin, useRole } from '@/hooks/useAdmin';
import { useGlobalState } from '@/hooks/useGlobalState';
import { usePathQuery } from '@/hooks/useQuery';
import LFormImageUploader from '@/components/form/form-items/form-imageuploader';

export function AppSettingPage() {
  const tabItems = [
    {
      key: '基础设置',
      label: '基础设置',
      children: <BasicSetting />,
    },
    {
      key: '环境变量',
      label: '环境变量',
      children: <EnvSetting />,
    },
    {
      key: 'API密钥',
      label: 'API密钥',
      children: <AppToken />,
    },
  ];

  return (
    <MidContainer>
      <Card>
        <Tabs items={tabItems} centered></Tabs>
      </Card>
    </MidContainer>
  );
}

function AppToken() {
  const { globalState } = useGlobalState();
  const { app } = globalState;

  if (!app) {
    return null;
  }

  return <TokenSetting id={app.id} type="app"></TokenSetting>;
}

const layout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};

function EnvSetting() {
  const [list, setList] = useState([]);
  const role = useRole('app');
  console.log('role', role);
  const hasRight = role === 'admin' || role === 'developer';
  async function getList() {
    const res = await EnvApi.list('', 'app');
    setList(res as any);
  }
  useEffect(() => {
    if (hasRight) {
      getList();
    }
  }, [hasRight]);

  if (!hasRight) {
    return <Result status="403" title="无权限" subTitle="对不起，您暂时无权限查看." />;
  }

  return (
    <div>
      <EnvComp envs={list} getList={getList} role={role}></EnvComp>
    </div>
  );
}

function BasicSetting() {
  const { globalState, fetchGlobalState } = useGlobalState();
  const { app, groups = [], workspace = { id: undefined } } = globalState;
  const isAdmin = useAdmin('workspace');

  if (!app) {
    return null;
  }

  const { pushAddQuery } = usePathQuery();
  const [form] = Form.useForm();

  const { run: deleteAppAPI } = useRequest(() => AppApi.removeApp(app.id), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      pushAddQuery(`/overview`, {
        workspaceId: workspace.id,
      });
    },
    onError: (err) => {
      message.error(`删除失败 ${err?.message}`);
    },
  });

  const deleteApp = useCallback(() => {
    Modal.confirm({
      maskClosable: true,
      content: '删除应用后可能导致已经部署的应用无法正常使用，确定删除吗？',
      onOk: deleteAppAPI,
    });
  }, [deleteAppAPI]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onFinish = (values: any) => {
    let querySample:any = '';
    if (values.scene && values.business) {
      querySample = `${values.business}@${values.scene}`
    }
    let extInfo = app.extInfo || {};
    if (values.avatar) {
      extInfo.avatar = values.avatar.url;
    }
    AppApi.updateApp(
      {
        ...values,
        querySample,
        extInfo,
      },
      app.id,
    )
      .then(() => {
        message.success('更新成功');
        fetchGlobalState('app', app.id);
      })
      .catch((err) => {
        window.corona.warn('settings 更新失败', err);
        message.error(`更新失败 ${err?.message}`);
      });
  };

  const groupOptions = groups.map((g) => ({
    id: g.id,
    name: g.name,
  }));

  const [business, scene] = app.querySample ? app.querySample.split('@'): ['', ''];

  return (
    <StyledBasicSetting>
      <div className="form-wrapper">
        <LForm
          form={form}
          onFinish={onFinish}
          // onValuesChange={(changedValues, allValues) => {
          //   console.log('changedValues', changedValues);
          //   console.log('allValues', allValues);
          // }}
          initialValues={{ ...app, ...(app?.config || {}), ...(app?.extInfo || {}), business, scene }}
          {...layout}
        >
          <LFormInput
            name="id"
            label="应用Id"
            disabled
          />
          <LFormInput
            name="name"
            label="应用名称"
            rules={[{ required: true, message: '请输入应用名称' }]}
          />
          {app.type === 'workflow' && (
            <LFormInput name="workflowId" label="workflowId" disabled />
          )}
          <LFormTextArea
            name="description"
            label="应用描述"
            rules={[{ required: true, message: '请输入应用描述' }]}
          />
          <LFormSelect name="groupID" label="业务组" options={groupOptions} disabled={!isAdmin} />
          <LFormRadioGroup name="isTemplate" label="是否是模板" options={[{label: '是', value: true}, {label: '否', value: false}]} disabled={!isAdmin} />
          <LFormImageUploader name="avatar" label="应用图标" disabled={!isAdmin} permanent={true} />
          <LFormAppType />
          {app.type === 'workflow' && (
            <>
              <Alert message="以下内容会修改调试的business和scene" style={{ marginBottom: 10 }} />
              <LFormInput name="business" label="business" />
              <LFormInput name="scene" label="scene" />
            </>
          )}
        </LForm>
      </div>
      <Space size={100}>
        <Button onClick={() => form.submit()} type="primary">
          保存
        </Button>
        <Button danger onClick={deleteApp}>
          删除应用
        </Button>
      </Space>
    </StyledBasicSetting>
  );
}

// const StyledAppSettingPage = styled.div`
//   overflow-y: auto;
//   height: 100%;
// `;

const StyledBasicSetting = styled.div`
  padding-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .form-wrapper {
    width: 500px;
    margin-bottom: 40px;
  }
`;
