import { DatePicker, Input, Select } from "antd";
import dayjs from "dayjs";
import styled from "styled-components";

export const PreviewStyle = styled.span`
  padding: 0px 4px;
  font-size: 12px;
  ${prop => {
    // @ts-ignore
    if (prop.nodeType === 'key-node') {
      return {
        color: ' #1677ff',
        backgroundColor: '#e6f4ff'
      }
    }
  }}
  ${prop => {
    // @ts-ignore
    if (prop.nodeType === 'logical-node') {
      return {
        color: '#ff4d4f',
        backgroundColor: '#fff2f0'
      }
    }
  }}
`;

export const SelectPreview = (props) => {
  const { isPreview, nodeType, ...rest } = props;

  if (!isPreview) return <Select  {...rest} />

  const { options, value, mode, className } = rest;

  let label = value;

  if (mode === 'multiple') {
    const labels = value?.reduce((acc, cur) => {
      const data = rest.options?.find(item => item.value === cur);
      if (data) {
        acc.push(data.label)
      }
      return acc;
    }, [])
    return <PreviewStyle>
      {labels?.join(',')}
    </PreviewStyle>
  } else if (mode === 'tags') {
    return <PreviewStyle> {value?.map(value => value.value || value || '').join(', ')}</PreviewStyle>
  } else {
    label = options?.find(item => item.value === value)?.label || value
  }

  // @ts-ignore
  return <PreviewStyle className={className} nodeType={nodeType}>{label}</PreviewStyle>
}

export const InputPreview = (props) => {
  const { isPreview, ...others } = props;
  if (isPreview) return <PreviewStyle>{props.value}</PreviewStyle>
  return <Input {...others} />
}

export const DatePickerPreview = props => {
  const { isPreview, ...others } = props;

  if (isPreview) {
    return <PreviewStyle>{props.value ? dayjs(props.value).format('YYYY-MM-DD HH:mm:ss') : ''}</PreviewStyle>
  }
  return <DatePicker {...others} />
}

export const RangePickerPreview = props => {
  const { isPreview, ...others } = props;
  if (isPreview) {
    const start = props.value?.[0];
    const end = props.value?.[1];

    return <PreviewStyle>{start ? dayjs(start).format('YYYY-MM-DD HH:mm:ss') : ''} ~ {
      end ? dayjs(end).format('YYYY-MM-DD HH:mm:ss') : ''
    }</PreviewStyle>
  }
  return <DatePicker.RangePicker {...others} />
}



