import React, { useContext, useMemo, useState } from 'react';
import { useEffect } from 'react';
import { ConfigContext } from 'antd/es/config-provider';
import { Flex, Select, Button } from 'antd';
import LogicalRelationsItem from './item';
import cls from 'classnames';

import { LOGIC_OPTIONS } from './consts';
import { ILogicalRelationsGroupProps, ILogicalRelationsValue, ILogicalRelationsValueGroup, ILogicalRelationsValueItem } from './types';
import { setFromEvent, omit } from './utils';
import { MinusCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { SelectPreview } from './preview';

const LogicColumn = styled.div`
  flex: none;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: center;
  justify-content: center;

  &::before,
  &::after {
    content: '';
    display: block;
    width: calc(50% + 1px);
    height: 100%;
    border-left: 2px solid rgba(0,0,0,0.06);
    min-height: 8px;
  }

  &::before {
    border-top: 2px solid rgba(0,0,0,0.06);
  }

  &::after {
    border-bottom: 2px solid rgba(0,0,0,0.06);
  }
`;

const baseClassName = 'logical-relations-group';

const LogicalRelationsGroup = <T,>(
  props: ILogicalRelationsGroupProps<T>
) => {
  const {
    className,
    logicOptions = LOGIC_OPTIONS,
    defaultLogic = logicOptions?.[0]?.value,
    defaultValue,
    value: valueProp,
    onChange,
    logicInputProps,
    isPreview,
    renderItem,
    isRoot = true,
    showAddButton = true,
    showRemoveButton = true,
    showLogicSelect = true,
    ...rest
  } = props;
  const wrapValue = (value: ILogicalRelationsValue) => {
    // 支持 value 只传入数组的情况，作为 children 解析
    if (Array.isArray(value)) {
      return {
        logic: defaultLogic,
        children: value,
      };
    }
    return value;
  };

  const [val, setVal] = useState(wrapValue(defaultValue));
  const { logic = defaultLogic, children = [{}] } = val || {};
  const { getPrefixCls } = useContext(ConfigContext);

  const availableAddButtons = useMemo(
    () => (Array.isArray(showAddButton) ? showAddButton : showAddButton ? ['item', 'group'] : []),
    [showAddButton],
  );

  const handleChange = (name: string, event: any) => {
    const values = { ...val, logic, children };
    const result = setFromEvent(values, name, event) as ILogicalRelationsValueGroup;

    setVal(result);
    // 如果组件传入的 children 只有数组，则只传出数组部分，与传入的结构保持一致
    if (Array.isArray(valueProp || defaultValue)) {
      onChange?.(result?.children);
      return;
    }
    onChange?.(result as any);
  };

  const handleChildrenChange = (type: 'add' | 'edit' | 'delete', index: number, value: any) => {
    const i = index ?? children.length - 1;
    const next = type === 'add' ? i : i + 1;

    handleChange('children', [
      ...children.slice(0, i),
      ...(type === 'delete' ? [] : [value]),
      ...children.slice(next),
    ]);
  };

  const renderChildren = () =>
    children?.map((elem, index) => {
      if (elem.logic && elem.children) {
        return (
          <div key={index}>
            <LogicalRelationsGroup
              {...omit(['defaultValue'], props)}
              isRoot={false}
              value={elem}
              onChange={(value: ILogicalRelationsValueGroup) => {
                const type = value.children?.length ? 'edit' : 'delete';

                handleChildrenChange(type, index, value);
              }}
              showAddButton={['item']}
              isPreview={isPreview}
            />
          </div>
        );
      }

      const showDelete = showRemoveButton && !isPreview && (!isRoot || children.length > 0);
      const content = renderItem?.({
        value: elem,
        index,
        onChange: (val) => handleChildrenChange('edit', index, val),
      }) ?? (
          <LogicalRelationsItem
            {...omit(['className', 'defaultValue', 'logicInputProps', 'renderItem'], props)}
            value={elem}
            onChange={(val) => handleChildrenChange('edit', index, val)}
            isPreview={isPreview}
          />
        );

      return (
        <Flex
          key={index}
          className={cls(getPrefixCls(getPrefixCls('expression', baseClassName)))}
          flex={8}
        >
          {content}
          {showDelete && (
            <Flex align="center" style={{ marginLeft: '8px', lineHeight: 1 }}>
              <a onClick={() => handleChildrenChange('delete', index, elem)}>
                <MinusCircleOutlined style={{ fontSize: '18px' }} />
              </a>
            </Flex>
          )}
        </Flex>
      );
    });

  useEffect(() => {
    if (typeof valueProp === 'undefined') {
      return;
    }
    setVal(wrapValue(valueProp));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [valueProp]);

  return (
    <Flex
      gap={8}
      className={cls(getPrefixCls(baseClassName), className)}
      {...rest}
    >
      {showLogicSelect && (
        <LogicColumn>
          <SelectPreview
            nodeType="logical-node"
            options={logicOptions}
            value={logic}
            onChange={(val) => handleChange('logic', val)}
            isPreview={isPreview}
            showArrow={false}
            {...logicInputProps}
          />
        </LogicColumn>
      )}
      <Flex
        justify="center"
        vertical
        gap={12}
      >
        {renderChildren()}
        {!isPreview && availableAddButtons.length > 0 && (
          <div>
            <Button.Group>
              {availableAddButtons.includes('item') && (
                <Button onClick={() => handleChildrenChange('add', children?.length, {})}>
                  添加条件
                </Button>
              )}
              {showLogicSelect && availableAddButtons.includes('group') && (
                <Button
                  onClick={() =>
                    handleChildrenChange('add', children?.length, {
                      logic: defaultLogic,
                      children: [{}],
                    })
                  }
                >
                  添加条件组
                </Button>
              )}
            </Button.Group>
          </div>
        )}
      </Flex>
    </Flex>
  );
};

export default LogicalRelationsGroup;
