import LogicalRelationsItem from './item';
import LogicalRelationsGroup from './group';
import * as consts from './consts';
import type {
  ILogicalRelationsItemProps,
  ILogicalRelationsGroupProps,
  ILogicalRelationsAvailableOptions,
} from './types';
import { omit } from './utils';

const options = omit(
  (key: string) => key.endsWith('OPTIONS'),
  consts,
) as ILogicalRelationsAvailableOptions;

const LogicalRelations = LogicalRelationsGroup as typeof LogicalRelationsGroup & {
  Item: typeof LogicalRelationsItem;
  OPTIONS: ILogicalRelationsAvailableOptions;
};

LogicalRelations.Item = LogicalRelationsItem;
LogicalRelations.OPTIONS = options;

export { LogicalRelations, LogicalRelationsItem };

export interface LogicalRelationsItemProps extends Omit<ILogicalRelationsItemProps, 'prefixCls'> { }
export interface LogicalRelationsProps
  extends Omit<ILogicalRelationsGroupProps, 'isRoot' | 'prefixCls'> { }
