import { ReactElement, ReactNode } from 'react';
import { InputProps, SelectProps } from 'antd';
import type { DefaultOptionType, SelectProps as AntSelectProps } from 'antd/lib/select';

export type ILogicalRelationsValueItem = Record<string, any> & {
  /**
   * 字段名
   */
  key?: string;
  /**
   * 条件操作符
   */
  operator?: any;
  /**
   * 字段值
   */
  value?: any;
};

export type ILogicalRelationsValueGroup = Record<string, any> & {
  /**
   * 条件组逻辑关系
   */
  logic?: string;
  /**
   * 条件列表
   */
  children?: Array<ILogicalRelationsValueGroup | ILogicalRelationsValueItem>;
};

export type ILogicalRelationsRenderItemValueProps = InputProps & {
  record: ILogicalRelationsValueItem;
  onChange: (value: any) => void;
};

export type ILogicalRelationsValueRender = ((props: ILogicalRelationsRenderItemValueProps) => ReactNode) | ReactElement<ILogicalRelationsRenderItemValueProps> | false;

export type ILogicalRelationsBaseOptions = AntSelectProps['options'];

type ILogicalRelationsOperatorOption = DefaultOptionType & {
  /**
   * 自定义渲染条件值输入框
   */
  renderValue?: ILogicalRelationsValueRender,
};

export type ILogicalRelationsOperatorOptions = ILogicalRelationsOperatorOption[] | ILogicalRelationsBaseOptions;

export type ILogicalRelationsLogicOptions = ILogicalRelationsBaseOptions;

type ILogicalRelationsOption = DefaultOptionType & {
  /**
   * 自定义条件操作符的选项列表
   */
  operatorOptions?: ILogicalRelationsOperatorOptions;
};

export type ILogicalRelationsOptions = ILogicalRelationsOption[] | ILogicalRelationsBaseOptions;

export type ILogicalRelationsValue =
  | ILogicalRelationsValueGroup
  | ILogicalRelationsValueGroup['children'];

interface ILogicalRelationsBaseProps extends Record<string, any> {
  /**
   * 容器 class
   */
  className?: string;
  /**
   * 字段的选项列表
   */
  options?: ILogicalRelationsOptions;
  /**
   * 自定义条件操作符的选项列表
   *
   * @default LogicalRelations.OPTIONS.NUMBER_OPERATOR_SYMBOL_OPTIONS
   */
  operatorOptions?: ILogicalRelationsOperatorOptions;
  /**
   * 默认的条件操作符
   *
   * @default operatorOptions?.[0]?.value
   */
  defaultOperator?: string;
  /**
   * 自定义条件字段输入框的 props
   */
  keyInputProps?: SelectProps;
  /**
   * 自定义条件操作符输入框的 props
   */
  operatorInputProps?: SelectProps;
  /**
   * 自定义条件值输入框的 props
   */
  valueInputProps?: InputProps;
  /**
   * 自定义渲染条件值输入框
   */
  renderItemValue?: ILogicalRelationsValueRender,
  /**
   * 预览态
   */
  isPreview?: boolean;
}

export interface ILogicalRelationsItemProps extends ILogicalRelationsBaseProps {
  /**
   * 默认值
   */
  defaultValue?: ILogicalRelationsValueItem;
  /**
   * 当前值，受控
   */
  value?: ILogicalRelationsValueItem;
  /**
   * 当值发生变化时触发
   */
  onChange?: (value: ILogicalRelationsValueItem) => void;
}

export interface ILogicalRelationsGroupProps<T = ILogicalRelationsValue> extends ILogicalRelationsBaseProps {
  /**
   * 自定义条件组逻辑关系的选项列表
   *
   * @default LogicalRelations.OPTIONS.LOGIC_OPTIONS
   */
  logicOptions?: ILogicalRelationsLogicOptions;
  /**
   * 默认的条件组逻辑关系
   *
   * @default logicOptions?.[0]?.value
   */
  defaultLogic?: string;
  /**
   * 自定义条件组逻辑关系输入框的 props
   */
  logicInputProps?: SelectProps;
  isRoot?: boolean;
  /**
   * 是否显示添加按钮
   *
   * @default true
   */
  showAddButton?: boolean | ('item' | 'group')[];
  /**
   * 是否显示删除条件按钮
   *
   * @default true
   */
  showRemoveButton?: boolean;
  /**
   * 是否显示条件组逻辑关系输入框
   *
   * @default true
   */
  showLogicSelect?: boolean;
  /**
   * 条件的自定义渲染
   */
  renderItem?: (option: {
    value: ILogicalRelationsValueItem;
    index: number;
    onChange: (value: ILogicalRelationsValueItem) => void;
  }) => ReactNode;
  /**
   * 默认值
   */
  defaultValue?: T;
  /**
   * 当前值，受控
   */
  value?: T;
  /**
   * 当值发生变化时触发，若传入的 value 为数组，则只返回 children
   */
  onChange?: (value: T extends any[] ? ILogicalRelationsValueGroup['children'] : ILogicalRelationsValueGroup) => void;
}

export type ILogicalRelationsAvailableOptionKeys =
  | 'LOGIC_OPTIONS'
  | 'NUMBER_OPERATOR_SYMBOL_OPTIONS'
  | 'NUMBER_OPERATOR_OPTIONS'
  | 'STRING_OPERATOR_OPTIONS';

export type ILogicalRelationsAvailableOptions = Record<
  ILogicalRelationsAvailableOptionKeys,
  ILogicalRelationsOperatorOptions | ILogicalRelationsLogicOptions
>;
