import React, { useContext, useMemo, useState, useEffect, useCallback } from 'react';
import { ConfigContext } from 'antd/es/config-provider';
import cls from 'classnames';
import { Select, Input, Flex } from 'antd';

import { NUMBER_OPERATOR_SYMBOL_OPTIONS } from './consts';
import { ILogicalRelationsItemProps, ILogicalRelationsOperatorOptions } from './types';
import { setFromEvent } from './utils';

const baseClassName = 'logical-relations-item';

const LogicalRelationsItem = (props: ILogicalRelationsItemProps) => {
  const {
    className,
    options,
    operatorOptions = NUMBER_OPERATOR_SYMBOL_OPTIONS,
    defaultOperator,
    defaultValue,
    value: item,
    onChange,
    keyInputProps,
    operatorInputProps,
    valueInputProps,
    isPreview,
    renderItemValue,
    ...rest
  } = props;
  const [val, setVal] = useState(defaultValue);

  const record = val || {};
  console.log('record', record);

  const { getPrefixCls } = useContext(ConfigContext);

  const getOperatorOptions = useCallback((key: string): ILogicalRelationsOperatorOptions => {
    const getOptions = (ops: any[]) =>
      (ops || []).find((e) => e?.value === key)?.operatorOptions || operatorOptions;

    if (!options || options instanceof Array) return getOptions((options as any[]) || []);

    return operatorOptions;
  }, [options, operatorOptions]);

  const getDefaultOperator = (ops: ILogicalRelationsOperatorOptions) => {
    // 默认条件优先取传入的 defaultOperator，但前提是其在传入的条件选项里
    // 若不存在，则再从条件选项列表里取第一个作为默认值
    const getOperator = (list: ILogicalRelationsOperatorOptions) => {
      if (list.some(e => e.value === defaultOperator)) {
        return defaultOperator;
      }
      return list?.[0]?.value;
    };
    if (ops?.length) {
      return getOperator(ops);
    }
    if (operatorOptions?.length) {
      return getOperator(operatorOptions);
    }
    return defaultOperator;
  };

  const { key, value, param = {} } = record;

  // 获取当前所选 key 的实际条件选项列表
  const logicalOps = useMemo(() => getOperatorOptions(key), [getOperatorOptions, key]);

  const { operator = getDefaultOperator(logicalOps) } = record;

  // 选中的条件选项
  const activeLogicalOp = useMemo(() => (
    logicalOps.find((e: any) => e.value === operator)
  ), [logicalOps, operator]);

  // 从选中的条件选项上取 value 的自定义渲染方法
  const renderValue = activeLogicalOp?.renderValue ?? renderItemValue;

  const onKeyChange = (newKey, obj) => {
    console.log('obj', obj);

    const param = {
      name: obj.data?.title,
      code: obj.data?.key,
      type: obj.data?.type,
      defaultValue: obj.data?.defaultValue,
      fieldSource: obj.data?.fieldSource
    };
    
    const result = {
      ...val,
      type: 'dimension',
      param,
    };
    console.log('result', result, 'val', val);

    setVal(result);
    onChange?.(result);
  }

  const handleChange = (name: string, event: any) => {
    console.log('name===', name, 'event==', event);

    const values = { ...val, key, operator, value };

    const result = setFromEvent(values, name, event);

    // 当选择了 key 或 operator 时，自动删除后面的配置值，避免数据源不一致导致冲突
    if (name !== 'value') {
      delete result.value;
    }

    // 当选择了 key 时，自动选择 operator
    if (name === 'key') {
      const ops = getOperatorOptions(result.key);
      if (ops.every((op) => op.value !== result.operator)) {
        result.operator = getDefaultOperator(ops);
      }
    }

    setVal(result);
    onChange?.(result);
  };

  const ValueComponent = renderValue || Input;

  const inputs = [
    <Select
      key="key"
      options={options}
      // value={key}
      value={param.code}
      // onChange={handleChange.bind(this, 'key')}
      onChange={onKeyChange}
      showSearch
      optionFilterProp="label"
      // isPreview={isPreview}
      placeholder="字段"
      style={{ width: 120 }}
      {...keyInputProps}
    />,
    <Select
      key="operator"
      options={logicalOps}
      value={operator}
      onChange={handleChange.bind(this, 'operator')}
      // isPreview={isPreview}
      placeholder="条件"
      style={{ width: 120 }}
      {...operatorInputProps}
    />,
    renderValue !== false && (
      <ValueComponent
        key="value"
        record={record}
        value={value}
        onChange={handleChange.bind(this, 'value')}
        isPreview={isPreview}
        placeholder="值"
        style={{ width: 160 }}
        {...valueInputProps}
      />
    ),
  ];

  useEffect(() => {
    item && setVal(item);
  }, [item]);

  return (
    <div className={cls(getPrefixCls(baseClassName), className)} {...rest}>
      {isPreview ? (
        <Flex >
          {inputs}
        </Flex>
      ) : (
        <Input.Group compact>{inputs}</Input.Group>
      )}
    </div>
  );
};

export default LogicalRelationsItem;
