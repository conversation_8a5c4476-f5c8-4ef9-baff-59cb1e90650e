export const getEventValue = (event: any) => {
  return event?.target?.value ?? event?.target?.checked ?? event;
};

export const setFromEvent = <T extends Record<string, any> | any[]>(data: T, key: string, event: any): T => {
  let result;

  if (data instanceof Array) {
    result = [ ...data ];
  } else {
    result = { ...data };
  }

  result[key] = getEventValue(event);
  return result as T;
};

// @private 判断key是否在数组或对象中
const _isInObj = (key: string, obj: { [x: string]: any } | Array<string>, isArray?: boolean) =>
  obj instanceof Array ? obj.indexOf(key) > -1 : key in obj;

/**
 * 获取对象的类型
 * @param  {*} obj
 * @return {String}
 *
 * @example
 * typeOf([]) === 'Array'
 * typeOf() === 'Undefined'
 * typeOf(1) === 'Number'
 */
export function typeOf(obj: any): string {
  return Object.prototype.toString.call(obj).replace(/\[object\s|]/g, '');
}


/**
 * 过滤出属性
 * @param  {Object|Array|Function} omitProps 排除的属性
 * @param  {Object} props     被过滤的对象
 * @return {Object}           除过滤对象以外的其他属性
 *
 * @example
 * object.omit(FooComponent.propTypes, this.props);
 * object.omit(['className', 'onChange'], this.props);
 */
export function omit(omitProps: Array<any> | ((key: string) => boolean) | {}, props: {}) {
  const others = {};
  const isArray = typeOf(omitProps) === 'Array';

  for (const key in props) {
    if (typeof omitProps === 'function' ? omitProps(key) : !_isInObj(key, omitProps, isArray)) {
      others[key] = props[key];
    }
  }

  return others;
}