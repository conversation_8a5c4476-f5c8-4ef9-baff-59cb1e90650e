/* eslint-disable @typescript-eslint/no-namespace */
// @ts-nocheck
import type { IArgsBase, ICommandHandler } from '@antv/xflow';
import { ICommandContextProvider, ManaSyringe, XFlowGroupCommands } from '@antv/xflow';
import type { HookHub } from '@antv/xflow-hook';

import { addNode2Group } from '../config/cmds/x6-events';
import * as CustomCommands from './constants';

type ICommand = ICommandHandler<NsTestCmd.IArgs, NsTestCmd.IResult, NsTestCmd.ICmdHooks>;

const { inject, injectable } = ManaSyringe;
export namespace NsAddGroupCmd {
  /** Command: 用于注册named factory */
  export const command = CustomCommands.ADD_GROUPS;
  /** hook name */
  export const hookKey = 'addGroups';
  /** hook 参数类型 */
  export interface IArgs extends IArgsBase {
    groups: any;
  }
  /** hook handler 返回类型 */
  export interface IResult {
    cells: any;
  }
  /** hooks 类型 */
  export interface ICmdHooks extends IHooks {
    addGroups: HookHub<IArgs, IResult>;
  }
}

@injectable()
/** 创建节点命令 */
export class AddGroupsCommand implements ICommand {
  /** api */
  @inject(ICommandContextProvider) contextProvider: ICommand['contextProvider'];

  /** 执行Cmd */
  execute = async () => {
    const ctx = this.contextProvider();
    const hooks = ctx.getHooks();
    const { args, hooks: runtimeHook } = ctx.getArgs();
    const graph = await ctx.getX6Graph();
    const result = await hooks.addGroups.call(
      args,
      async (handlerArgs) => {
        const { groups } = handlerArgs;
        const cells = await Promise.all(
          groups.map((group) => {
            const children = group.groupChildren;
            const currentParent = graph.getCellById(group.id);
            const childCells = children.map((c) => graph.getCellById(c)).filter((c) => c);
            childCells.forEach((cell) =>
              addNode2Group({ currentParent, node: cell, isInit: true }),
            );
            return currentParent;
          }),
        );
        console.log('cells', cells);
        return { cells };
      },
      runtimeHook,
    );

    ctx.setResult(result);
    return this;
  };

  /** undo cmd */
  undo = async () => {
    if (this.isUndoable()) {
      const ctx = this.contextProvider();
      ctx.undo();
    }
    return this;
  };

  /** redo cmd */
  redo = async () => {
    if (!this.isUndoable()) {
      await this.execute();
    }
    return this;
  };

  isUndoable(): boolean {
    const ctx = this.contextProvider();
    return ctx.isUndoable();
  }
}
