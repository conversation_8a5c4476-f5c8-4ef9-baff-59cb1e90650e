import type { HookHub, ICmdHooks as IHooks, IModelService, NsGraph } from '@antv/xflow';
import type { IArgsBase, ICommandHandler } from '@antv/xflow';
import { Deferred, ManaSyringe } from '@antv/xflow';
import { ICommandContextProvider } from '@antv/xflow';
import { ConfigProvider, Form, Input, message, Modal, Select } from 'antd';
import * as CustomCommands from './constants';
import { ComponentApi } from '@/api/component';
import { getGroupId, getWorkspaceId } from '@/utils/state';
import eventBus, { IEventType } from '@/utils/event-bus';

type ICommand = ICommandHandler<
  NsExportPluginCmd.IArgs,
  NsExportPluginCmd.IResult,
  NsExportPluginCmd.ICmdHooks
>;

export namespace NsExportPluginCmd {
  export const command = CustomCommands.SHOW_EXPORT_MODAL;
  export const hookKey = 'exportPlugin';

  export interface IArgs extends IArgsBase {
    nodeConfig: NsGraph.INodeConfig;
  }

  export interface IResult {
    err: string | null;
    exportConfig?: any;
  }

  export interface ICmdHooks extends IHooks {
    exportPlugin: HookHub<IArgs, IResult>;
  }
}

@ManaSyringe.injectable()
export class ExportPluginCommand implements ICommand {
  @ManaSyringe.inject(ICommandContextProvider)
  contextProvider: ICommand['contextProvider'];

  execute = async () => {
    const ctx = this.contextProvider();
    const { args } = ctx.getArgs();
    const { nodeConfig } = args;
    const config = await showModal(nodeConfig);
    const newConfig = { ...config.nodeConfig };
    delete newConfig.id;
    delete newConfig.x;
    delete newConfig.y;
    delete newConfig.height;
    delete newConfig.width;

    await ComponentApi.create({
      name: config.name,
      alias: config.name,
      code: config.code,
      description: config.description,
      type: config.nodeConfig.type,
      categoryID: 'bcff7409-1cbc-42f8-8b56-6d81cd41440a',
      appTypes: ['workflow'],
      url: '1',
      category: '自定义',
      workspaceID: getWorkspaceId(),
      groupID: getGroupId(),
      scope: config.scope,
      deprecated: false,
      config: newConfig,
    });
    message.success('导出成功');
    eventBus.emit(IEventType.REFRESH_COMPONENTS);
    window.location.reload();
    console.log('config', config);
    return this;
  };

  undo = async () => {
    return this;
  };

  redo = async () => {
    return this;
  };

  isUndoable(): boolean {
    return false;
  }
}

interface IFormProps {
  name: string;
  description: string;
}

function showModal(node: NsGraph.INodeConfig) {
  const defer = new Deferred<any>();

  class ModalCache {
    static modal: ReturnType<typeof Modal.confirm>;
    static form: any;
  }

  const onOk = async () => {
    const { form, modal } = ModalCache;
    try {
      modal.update({ okButtonProps: { loading: true } });
      await form.validateFields();
      const values = await form.getFieldsValue();
      defer.resolve({
        ...values,
        nodeConfig: node
      });
      onHide();
    } catch (error) {
      console.error(error);
      modal.update({ okButtonProps: { loading: false } });
    }
  };

  const onHide = () => {
    modal.destroy();
    ModalCache.form = null;
    ModalCache.modal = null;
    container.destroy();
  };

  const ModalContent = () => {
    const [form] = Form.useForm<IFormProps>();
    ModalCache.form = form;

    return (
      <ConfigProvider>
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={{
            name: node.name,
            scope: 'group'
          }}
        >
          <Form.Item
            name="name"
            label="插件名称"
            rules={[{ required: true, message: '请输入插件名称' }]}
          >
            <Input placeholder="请输入插件名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="插件描述"
            rules={[{ required: true, message: '请输入插件描述' }]}
          >
            <Input.TextArea placeholder="请输入插件描述" />
          </Form.Item>
          <Form.Item
            name="code"
            label="插件CODE"
            rules={[{ required: true, message: '请输入插件CODE' }]}
          >
            <Input placeholder="请输入插件CODE" />
          </Form.Item>
          <Form.Item
            name="scope"
            label="插件范围"
            help="插件范围决定了插件的可见性和使用范围"
            rules={[{ required: true, message: '请选择插件范围' }]}
          >
            <Select placeholder="请选择插件范围">
              {/* <Select.Option value="public">公共</Select.Option>
              <Select.Option value="scoped">租户内</Select.Option> */}
              <Select.Option value="group">组内</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </ConfigProvider>
    );
  };

  const container = createContainer();

  const modal = Modal.confirm({
    title: '导出插件配置',
    width: 500,
    content: <ModalContent />,
    getContainer: () => container.element,
    okButtonProps: {
      onClick: (e) => {
        e.stopPropagation();
        onOk();
      },
    },
    onCancel: onHide,
    afterClose: onHide,
  });

  ModalCache.modal = modal;

  return defer.promise;
}

const createContainer = () => {
  const div = document.createElement('div');
  div.classList.add('xflow-modal-container');
  window.document.body.append(div);
  return {
    element: div,
    destroy: () => {
      window.document.body.removeChild(div);
    },
  };
}; 