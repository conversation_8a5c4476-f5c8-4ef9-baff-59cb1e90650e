// @ts-nocheck
import type { ICommandContributionConfig } from '@antv/xflow';

import { AddGroupsCommand, NsAddGroupCmd } from './cmd-addGroups';
import { NsTestCmd, TestAsyncCommand } from './cmd-async-test';
import { DeployDagCommand, NsDeployDagCmd } from './cmd-deploy';
import { NsPreviewDagCmd, PreviewCommand } from './cmd-preview';
import { NsRenameNodeCmd, RenameNodeCommand } from './cmd-rename-node-modal';
import { NsExportPluginCmd, ExportPluginCommand } from './cmd-export-plugin-modal';
/** 注册成为可以执行的命令 */

export const commandContributions: ICommandContributionConfig[] = [
  {
    ...NsTestCmd,
    CommandHandler: TestAsyncCommand,
  },
  {
    ...NsDeployDagCmd,
    CommandHandler: DeployDagCommand,
  },
  {
    ...NsRenameNodeCmd,
    CommandHandler: RenameNodeCommand,
  },
  {
    ...NsPreviewDagCmd,
    CommandHandler: PreviewCommand,
  },
  {
    ...NsAddGroupCmd,
    CommandHandler: AddGroupsCommand,
  },
  {
    ...NsExportPluginCmd,
    CommandHandler: ExportPluginCommand,
  },
];
