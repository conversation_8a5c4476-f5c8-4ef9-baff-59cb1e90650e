/* eslint-disable @typescript-eslint/no-namespace */
// @ts-nocheck
/* eslint-disable @typescript-eslint/no-dupe-class-members */
/* eslint-disable react-hooks/rules-of-hooks */
import type {
  HookHub,
  IArgsBase,
  ICmdHooks as IHooks,
  ICommandHandler,
  IModelService,
  NsGraphCmd,
} from '@antv/xflow';
import { ManaSyringe, XFlowGraphCommands } from '@antv/xflow';
import { ICommandContextProvider } from '@antv/xflow';

import { getCustomModal } from '../config/config-model-service';
import * as CustomCommands from './constants';

type ICommand = ICommandHandler<
  NsPreviewDagCmd.IArgs,
  NsPreviewDagCmd.IResult,
  NsPreviewDagCmd.ICmdHooks
>;

export namespace NsPreviewDagCmd {
  /** Command: 用于注册named factory */
  export const command = CustomCommands.PREVIEW_CMD;
  /** hook name */
  export const hookKey = 'preview_cmd';
  /** hook 参数类型 */
  export interface IArgs extends IArgsBase {
    intoPreview: boolean;
  }
  /** hook handler 返回类型 */
  export interface IResult {
    success: boolean;
  }
  /** hooks 类型 */
  export interface ICmdHooks extends IHooks {
    preview: HookHub<IArgs, IResult>;
  }
}

@ManaSyringe.injectable()
/** 部署画布数据 */
export class PreviewCommand implements ICommand {
  /** 更新节点数据 */
  updateNodeData = (id, data: NsGraphStatusCommand.INodeStatus) => {
    const cell = this.x6Graph.getCellById(id) as X6Node;
    if (!cell) {
      return;
    }
    cell.setData({
      ...cell.getData(),
      ...cell.getSize(),
      ...cell.getPosition(),
      ...data,
    });
  };
  /** X6Graph */
  x6Graph: X6Graph;
  /** api */
  @ManaSyringe.inject(ICommandContextProvider)
  contextProvider: ICommand['contextProvider'];

  /** 更新节点数据 */
  updateCellData = (preview) => {
    // 把所有节点增加preview数据
    const cells = this.x6Graph.getNodes();
    if (!cells.length) {
      return;
    }
    cells.forEach((cell: any) => {
      cell.setData({
        ...cell.getData(),
        ...cell.getSize(),
        ...cell.getPosition(),
        preview,
      });
    });
  };

  /** 执行Cmd */
  execute = async () => {
    const ctx = this.contextProvider();
    const { args } = ctx.getArgs();
    this.x6Graph = await ctx.getX6Graph();
    const result = (async () => {
      const { commandService, intoPreview, modelService } = args;
      /** 执行Command */
      await commandService.executeCommand<NsGraphCmd.SaveGraphData.IArgs>(
        XFlowGraphCommands.SAVE_GRAPH_DATA.id,
        {
          saveGraphDataService: async (meta, graph) => {
            const ctx = await getCustomModal(modelService as IModelService);
            const values = await ctx.getValidValue();
            ctx.setValue({
              ...values,
              preview: intoPreview,
            });
            console.log('preview====', values, intoPreview);
            (window as any).preview = intoPreview;
          },
        },
      );
      this.updateCellData(intoPreview);
      return { success: true, preview: intoPreview };
    })();

    ctx.setResult(result);
    return this;
  };

  /** undo cmd */
  undo = async () => {
    if (this.isUndoable()) {
      const ctx = this.contextProvider();
      ctx.undo();
    }
    return this;
  };

  /** redo cmd */
  redo = async () => {
    if (!this.isUndoable()) {
      await this.execute();
    }
    return this;
  };

  isUndoable(): boolean {
    const ctx = this.contextProvider();
    return ctx.isUndoable();
  }
}
