/* eslint-disable react-hooks/rules-of-hooks */
// @ts-nocheck
import '@antv/xflow/dist/index.css';
import './index.less';
import { Modal } from 'antd'
// import { Transform } from '@antv/x6-plugin-transform';
import type { IApplication, IAppLoad } from '@antv/xflow';
/** app 核心组件 */
import {
  CanvasNodePortTooltip,
  IModelService,
  KeyBindings,
  XFlow,
  XFlowCanvas,
} from '@antv/xflow';
/** 交互组件 */
import {
  CanvasContextMenu,
  /** 触发Command的交互组件 */
  CanvasScaleToolbar,
  CanvasToolbar,
  DagGraphExtension,
  // NodeCollapsePanel,
} from '@antv/xflow';
import { JsonSchemaForm } from '@/components/canvas-json-schema-form';
import { NodeCollapsePanel } from '@components/canvas-collapse-panel'
import React, { useEffect } from 'react';

import { AppApi } from '@/api/app';
import { setInstance } from '@/utils/flow';
import { getAppId, $, mergeConfig } from '@/utils/state';

/** 配置自定义面板 */
import ResponsePanel from './components/response-panel';
import { diffGraph } from './config/cmds/hooks-services';
/** 配置Command */
import { initGraphCmds, initGraphCmdsAgent, useCmdConfig } from './config/config-cmd';
/** 配置Dnd组件面板 */
import * as dndPanelConfig from './config/config-dnd-panel';
/** 配置画布 */
import { useGraphHookConfig } from './config/config-graph';
/** 配置Canvas */
import { useGraphConfig } from './config/config-graph';
/** 配置JsonConfigForm */
import {
  controlMapService,
  formSchemaService,
  formValueUpdateService,
} from './config/config-json';
/** 配置快捷键 */
import { useKeybindingConfig } from './config/config-keybinding';
/** 配置Menu */
import { useMenuConfig } from './config/config-menu';
/** 配置Model */
import {
  getCustomModal,
  getCustomState,
  useModelServiceConfig,
} from './config/config-model-service';
/** 配置自定义面板 */
import DebugCard from './components/debug-card';
import { IAppType } from '@/interface';
import { TemplateSelect } from '@/components/template-select';

const { confirm } = Modal;

export interface IProps {
  meta: { flowId: string };
}

const leaveWarning = '离开当前页后，所有未保存的数据将会丢失，是否继续？';
const listener = (e): void => {
  diffGraph().then((diff) => {
    if (diff) {
      e.preventDefault();
      e.returnValue = leaveWarning;
    }
  });
};

export const AgentWorkflowEdit: React.FC<any> = (props) => {
  useEffect(() => {
    window.addEventListener('beforeunload', listener);
    return (): void => {
      window.removeEventListener('beforeunload', listener);
    };
  }, []);

  const [showSchema, setShowSchema] = React.useState(false);
  const [preview, setPreview] = React.useState(false);
  const { meta } = props;
  const graphHooksConfig = useGraphHookConfig(props);
  const menuConfig = useMenuConfig();
  const cmdConfig = useCmdConfig();
  const modelServiceConfig = useModelServiceConfig(false)();
  const keybindingConfig = useKeybindingConfig();

  const cache = React.useMemo<{ app: IApplication }>(
    () => ({
      app: null as any,
    }),
    [],
  );
  /**
   * @param app 当前XFlow工作空间
   * @param extensionRegistry 当前XFlow配置项
   */

  const onLoad: IAppLoad = async (app) => {
    cache.app = app;
    setInstance(app);
    const appId = getAppId();
    const histories = await AppApi.getAppHistory(appId);
    console.log("his", histories)
    const ctx = await getCustomModal(app.modelService as IModelService);
    const state = await getCustomState(app.modelService as IModelService);
    ctx.watch((val) => {
      setShowSchema(val.showSchema);
      setPreview(val.preview);
    });
    ctx.setValue({ ...state, canIntoPlayground: histories?.length });
    initGraphCmdsAgent(cache.app);
    // const x6graph = await app.getGraphInstance();
  };

  /** 父组件meta属性更新时,执行initGraphCmds */
  const config = useGraphConfig(false);
  const nodeDataService = dndPanelConfig.nodeDataService('agentWorkflow');

  const handleSelect = async (template: Template) => {
    console.log('选择的模板：', template);
    const app = await AppApi.getAppDetail(template.id);
    const curConfig = await $.getConfig();
    console.log('app...', app, curConfig);
    if (app) {
      const res = await AppApi.saveAppConfigSnapshot('', mergeConfig(app.config, curConfig));
      if (res) {
        window.location.reload();
      }
    }

    // 处理模板选择逻辑
  };

  return (
    <XFlow
      className="dag-user-custom-clz"
      hookConfig={graphHooksConfig}
      modelServiceConfig={modelServiceConfig}
      commandConfig={cmdConfig}
      onLoad={onLoad}
      meta={meta}
    >
      <DagGraphExtension layout="LR" />
      <XFlowCanvas
        position={{ top: 0, left: 0, right: 0, bottom: 0 }}
        config={config()}
      >
        <CanvasScaleToolbar layout='horizontal' style={{ height: 40, borderRadius: 10, overflow: 'hidden', left: 'auto' }} position={{ bottom: 12, left: 'auto', right: 20 }} />
        <CanvasContextMenu config={menuConfig} />
        {/* <CanvasSnapline color="#faad14" /> */}
        <CanvasNodePortTooltip></CanvasNodePortTooltip>
      </XFlowCanvas>
      <div style={{ position: 'relative' }}>
        <NodeCollapsePanel
          className="xflow-node-panel"
          style={{ transform: preview ? 'translateX(-400px)' : '' }}
          collapsible
          searchService={dndPanelConfig.searchService}
          nodeDataService={nodeDataService}
          onNodeDrop={dndPanelConfig.onNodeDrop}
          position={{ width: 320, top: 60, bottom: 0, left: 10 }}
          footerPosition={{ height: 0 }}
          bodyPosition={{ top: 0, bottom: 0, left: 0 }}
        />
        <TemplateSelect
          appType={IAppType.AgentWorkflow}
          buttonStyle={{ position: 'absolute', top: 19, left: 70, padding: '4px 12px' }}
          onSelect={handleSelect}
        />
      </div>
      <DebugCard type="agentWorkflow" />
      <JsonSchemaForm
        controlMapService={controlMapService}
        formSchemaService={formSchemaService}
        style={{ transform: preview ? 'translateX(400px)' : '' }}
        targetType={['group', 'node', 'canvas']}
        formValueUpdateService={formValueUpdateService}
        bodyPosition={{ top: 0, bottom: 0, right: 0 }}
        position={{ width: showSchema && !preview ? 320 : 0, top: 60, bottom: 0, right: 20 }}
        footerPosition={{ height: 0 }}
      />
      <KeyBindings config={keybindingConfig} />
    </XFlow>
  );
};

export default AgentWorkflowEdit;

AgentWorkflowEdit.defaultProps = {
  meta: { flowId: 'test-meta-flow-id' },
};
