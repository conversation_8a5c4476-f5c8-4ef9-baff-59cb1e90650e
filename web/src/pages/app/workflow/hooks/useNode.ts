import { NsNodeCmd, useXFlowApp, XFlowNodeCommands } from '@antv/xflow';
import { useEffect, useRef } from 'react';
import { updateNode as update } from '@/utils/node';

export const useNode = () => {
  const app = useXFlowApp();
  const graph = useRef();

  useEffect(() => {
    if (app) {
      app.getGraphInstance().then((res: any) => {
        graph.current = res;
      });
    }
  }, [app]);

  const deleteNode = (nodeConfig: any) => {
    return app.executeCommand(XFlowNodeCommands.DEL_NODE.id, {
      nodeConfig,
    } as NsNodeCmd.UpdateNode.IArgs);
  };

  const updateNode = (nodeId: string, config: any) => {
    let newConfig = config;
    if (graph.current) {
      const cell = (graph.current as any).getCellById(nodeId);
      newConfig = { ...cell.data, ...config };
    }
    return update({
      id: nodeId,
      ...newConfig,
    },
      app
    );
  };

  return { updateNode, deleteNode };
};
