@body-bg: #fafafa;
@primaryColor: #3056e3;
@light-border: 1px solid #d9d9d9;
@input_bkg: rgb(245, 248, 252);

.dag-solution {
  .__dumi-default-previewer-actions {
    border: 0;
  }
}

.horizontal {
  .dag-user-custom-clz {
    .xflow-toolbar {
      width: 150px;
    }
  }
}

.dag-user-custom-clz {
  position: absolute;
  height: calc(100vh - 60px);
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  left: 0;
  top: 60px;
  overflow: hidden;
  border-radius: 6px;
  border: @light-border;

  .ant-btn {
    border-radius: 8px;
  }



  .xflow-collapse-panel {
    position: absolute;
    width: 320px;
    top: 10px;
    bottom: 0px;
    left: 20px;
    transition: left 0.5s ease 0s;
    height: calc(100vh - 180px);
    border-radius: 10px;
    overflow: hidden;
    border: none;
    box-shadow: 0px 1px 9px 3px #dddddd;
  }

  .xflow-collapse-panel-header-search {
    width: 100%;
    padding: 0 12px;
    display: flex;
    gap: 10px;
  }

  .xflow-x6-canvas {
    background: @body-bg;
  }
  .x6-edge {
    &:hover {
      path:nth-child(2) {
        // stroke: @primaryColor;
        stroke-width: 6px;
        opacity: 0.7;
      }
    }
    &.x6-edge-selected {
      path:nth-child(2) {
        // stroke: @primaryColor;
        stroke-width: 6px;
        opacity: 0.7;
      }
    }
  }

  .xflow-node-dnd-panel-node-wrapper {
    .xflow-algo-node {
      width: 180px;
    }
  }
  .xflow-canvas-dnd-node-tree {
    border-right: @light-border;
  }

  .xflow-workspace-toolbar-top {
    background-image: ~'linear-gradient(180deg, #ffffff 0%, #fafafa 100%)';
    border-bottom: @light-border;
  }

  .xflow-workspace-toolbar-bottom {
    text-align: center;
    background: #fff;
    border-top: @light-border;
  }

  .xflow-modal-container {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
  }

  .xflow-collapse-panel {
    box-sizing: border-box;
    border-top: 1px solid #d9d9d9;
    z-index: 100;
    .xflow-collapse-panel-header {
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      background: #fff;
      .ant-input-affix-wrapper {
        background: @input_bkg;
        padding: 2px 11px;
      }
      .ant-input {
        background: @input_bkg;
      }
    }
    .xflow-collapse-panel-body {
      background: #fff;
      .xflow-collapse-header {
        padding: 12px 8px;
      }
    }
    .xflow-node-dnd-panel-footer {
      display: none;
    }
  }

  .xflow-json-form .tabs .ant-tabs-nav {
    box-shadow: unset;
  }
  .xflow-json-schema-form {
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #1677ff;
      border-bottom: 1px solid #1677ff;
    }
    .singleTab {
      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #555;
        border-bottom: none;
      }
    }
    .xflow-json-schema-form-footer {
      display: none;
    }
    .xflow-json-form .tabs.xTab .ant-tabs-nav .ant-tabs-nav-list,
    .xflow-json-form .tabs.xTab .ant-tabs-nav .ant-tabs-nav-list .ant-tabs-tab {
      background: #fff;
      font-size: 16px;
      font-weight: bold;
      border: none;
    }
    .xflow-json-schema-form-body {
      overflow-y: auto;
      border-radius: 15px;
      position: relative;
      width: 100%;
      height: calc(100vh - 180px);
      background: #fff;
      box-shadow: 0 1px 11px 0 rgb(206 201 201 / 50%);
      .ant-input-number, .ant-select-selector, .ant-input, .ant-input-textarea-allow-clear {
        border: none;
        background-color: rgb(245, 248, 252);
        &.ant-input-disabled {
          background-color: #fafafa;
        }
        &.ant-input-number-disabled {
          background-color: #fafafa;
        }
      }
      input {
        border: none;
        background-color: rgb(245, 248, 252);
      }
    }
  }
}

.xflow-collapse-panel-body {
  inset: 40px 0px 0px !important;
}

.ant-tabs-nav-operations {
  padding-left: 0 !important;
}
.xflow-collapse-content {
  display: grid;
  grid-template-columns: 49% 49%;
  grid-column-gap: 2%;
  padding: 0 10px;
}

.xflow-dnd-item {
  border-radius: 8px;
  // background-color: #fff;
  position: relative;
  box-shadow:
    -1px -1px 4px 0 rgba(223, 223, 223, 0.5),
    -2px 2px 4px 0 rgba(244, 244, 244, 0.5),
    2px 3px 8px 2px rgba(151, 151, 151, 0.05);
  // transition: all ease-in-out 0.15s;
  padding: 12px 0;
  padding-left: 48px;
  display: flex;
  justify-content: flex-start;
  .avatar-icon {
    position: absolute;
    left: 12px;
    top: 8px;
    border-radius: 6px;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 #0000, 0 0 #0000, 0px 2px 4px -2px rgba(16, 24, 40, .06), 0px 4px 8px -2px rgba(16, 24, 40, .1);;
    span {
      font-size: 14px;
    }
  }
  .label {
    font-size: 12px;
    line-height: 12px;
  }
}

.xflow-collapse-panel-node-wrapper > div {
  width: 100%;
}

.dag-extension-container .x6-port .xflow-port-group.connecting .xflow-port {
  stroke: null;
  r: 8;
  stroke-width: 8px;
  opacity: 0.8;
}

.xflow-algo-node {
  .label {
    font-size: 16px;
    font-weight: bold;
  }
}

.code-edit {
  background-color: #fff !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 5px;
  textarea:focus-visible {
    background-color: #3056e3;
    outline-color: red !important;
  }
}

.param-form .ant-form-item-row {
  flex-direction: row !important;
}

.icon {
  width: 15px;
  max-height: 15px;
  img {
    width: 100%;
  }
  path {
    fill:#fff !important;
  }
}

.x6-port-group1 {
  &.connecting {
    .xflow-port {
      r: 8;
      stroke-width: 8px;
      opacity: 0.7;
    }
  }
}

.xflow-port {
  &.connecting {
    r: 8;
    stroke-width: 8px;
    opacity: 0.7;
  }
}

.xflow-toolbar-root {
  .x6-toolbar {
    .ant-btn-icon {
      font-size: 14px;
      margin-inline-end: 4px !important;
    }
    button {
      font-size: 12px;
      // background-color: #f5f5f5;
    }
  }
}

.full {
  .cm-theme-light {
    height: 100%;
  }
  .ant-modal-body {
    height: 90%;
  }
  .ant-modal-content {
      top: 0px;
      position: fixed;
      width: 100vw;
      left: 0;
      height: 100vh;
  }
}

.dag-extension-container {
  .x-1 path:nth-child(2) {
    stroke-width: 8px !important;
    opacity: 0.6;
  }

  .x-1-2 path:nth-child(2) {
    stroke-width: 10px !important;
    opacity: 0.6;
  }

  .x-1-6 path:nth-child(2) {
    stroke-width: 16px !important;
    opacity: 0.6;
  }

  .x-1-3 path:nth-child(2) {
    stroke-width: 14px !important;
    opacity: 0.6;
  }
}

.type-string {
  word-wrap: break-word;
  color: rgb(221, 105, 0);
  &::before {
    content: '"';
  }
  &::after {
    content: '"';
  }
}

.type-number {
  color: #ddbb4e;
  font-weight: bold;
  font-family: monospace;
}

.type-date {
  color: #de54e5;
  font-family: monospace;
}

.type-boolean {
  color: #1677ff;
  font-family: monospace;
}

.type-float {
  color: #82c844;
  font-weight: bold;
  font-family: monospace;
}

.mini {
  .one-line {
    display: inline-block;
    max-width: 80px;
  }
}

.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dag-extension-container .x6-edge.processing path:nth-child(2) {
  stroke-width: 8px !important;
}

.xflow-collapse-panel .xflow-collapse-search-list {
  padding: 10px 12px !important;
}

.mask {
  background: #fff;
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 64px;
  z-index: 10000;
}

.ant-form-item-explain {
  margin-bottom: 12px;
}

.ant-tabs-nav-operations {
  display: none !important;
}

