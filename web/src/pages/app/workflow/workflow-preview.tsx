// @ts-nocheck
/* eslint-disable react-hooks/rules-of-hooks */
import '@antv/xflow/dist/index.css';
import './index.less';

import qs from 'querystring';
import type { IApplication, IAppLoad } from '@antv/xflow';
/** app 核心组件 */
import { IModelService, NodeCollapsePanel, XFlow, XFlowCanvas, XFlowDagCommands } from '@antv/xflow';
/** 交互组件 */
import {
  /** 触发Command的交互组件 */
  CanvasScaleToolbar,
  /** Graph的扩展交互组件 */
  CanvasSnapline,
  DagGraphExtension,
  JsonSchemaForm,
} from '@antv/xflow';
import React from 'react';

import { useGlobalState } from '@/hooks/useGlobalState';
import { setInstance } from '@/utils/flow';

/** 配置Command */
import { initGraphCmdsWithData } from './config/config-cmd';
/** app 组件配置  */
/** 配置画布 */
import { useGraphConfig, useGraphHookConfig } from './config/config-graph';
/** 配置JsonConfigForm */
import {
  controlMapService,
  formSchemaService,
  formValueUpdateService,
} from './config/config-json';
// import { useCmdConfig } from './config/config-hooks';
/** 配置Model */
import {
  getCustomModal,
  getCustomState,
  useModelServiceConfig,
} from './config/config-model-service';
import CustomPanel from './components/debug-card';
import CallbackPanel from './components/response-panel';
import { graphStatusService } from './config/cmds/hooks-services';

export interface IProps {
  meta: { flowId: string };
}

export const Demo: React.FC<any> = (props) => {
  const { globalState } = useGlobalState();
  const { meta, graphData } = props;
  const { app } = globalState;
  const query = qs.parse(window.location.search.replace('?', ''));
  console.log('app', app, graphData);

  const [showSchema, setShowSchema] = React.useState(false);
  const graphHooksConfig = useGraphHookConfig({ ...props, preview: true });
  // const cmdConfig = useCmdConfig();
  const modelServiceConfig = useModelServiceConfig(true)();

  const cache = React.useMemo<{ app: IApplication }>(
    () => ({
      app: null as any,
    }),
    [],
  );
  /**
   * @param app 当前XFlow工作空间
   * @param extensionRegistry 当前XFlow配置项
   */

  const onLoad: IAppLoad = async (app) => {
    cache.app = app;
    setInstance(app);
    const ctx = await getCustomModal(app.modelService as IModelService);
    const preVal = await getCustomState(app.modelService as IModelService);
    ctx.setValue({ ...preVal, preview: true });
    ctx.watch((val) => {
      setShowSchema(val.showSchema);
    });
    if (query.runId) {
      console.log("query..1", query.runId);
      app.executeCommand(XFlowDagCommands.QUERY_GRAPH_STATUS.id, {
        graphStatusService,
        loopInterval: 3000,
        runID: query.runId,
        noDebug: !query.debug,
      })
    }
    initGraphCmdsWithData(graphData)(cache.app);
  };

  /** 父组件meta属性更新时,执行initGraphCmds */
  const config = useGraphConfig(true);

  return (
    <XFlow
      className="dag-user-custom-clz"
      hookConfig={graphHooksConfig}
      modelServiceConfig={modelServiceConfig}
      onLoad={onLoad}
      meta={meta}
    >
      <DagGraphExtension layout="LR" />

      <XFlowCanvas position={{ top: 0, left: 0, right: 0, bottom: 0 }} config={config()}>
        <CanvasScaleToolbar position={{ top: 12, right: 12 + (showSchema ? 290 : 0) }} />
        <CanvasSnapline color="#faad14" />
        <CallbackPanel
          defaultLevel={query.runId ? 1 : 0}
          style={{ width: `calc(100% - ${showSchema ? '290px' : '0px'})` }}
        ></CallbackPanel>
      </XFlowCanvas>
    </XFlow>
  );
};

export default Demo;

Demo.defaultProps = {
  meta: { flowId: 'test-meta-flow-id' },
};
