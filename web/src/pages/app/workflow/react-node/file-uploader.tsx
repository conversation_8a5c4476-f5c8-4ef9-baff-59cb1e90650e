import { InboxOutlined, UploadOutlined } from "@ant-design/icons";
import { message, Upload } from "antd";
import { getAppId } from '@/utils/state';
import { useEffect, useState } from "react";
import type { UploadFile, UploadProps } from 'antd';

interface IUploadProps extends UploadProps {
  sizeLimit?: number;
  value?: UploadFile[];
  description?:string;
}


const { Dragger } = Upload;

const FileUpload = (props: IUploadProps) => {
  const { onChange, value, maxCount = 1, sizeLimit, accept, description } = props;
  const [fileList, setFileList] = useState<UploadFile[]>();

  useEffect(() => {
    setFileList(value);
  }, [value]);

  const formatFile = (file) => {
    let obj: any = file;
    if (file?.response?.data) {
      obj = {
        key: file?.response?.data?.key,
        url: file?.response?.data?.uri,
        name: obj.name,
        status: obj.status,
        file: file.originFileObj
      };
    }
    return obj;
  };

  const beforeUpload = file => {
    if (!sizeLimit) {
      return true;
    }
    const isLimited = file.size < sizeLimit * 1024;
    if (!isLimited) {
      const unit = sizeLimit >= 1024 ? 'MB' : 'KB';
      const size = sizeLimit >= 1024
        ? (sizeLimit / 1024).toFixed(0)
        : (sizeLimit).toFixed(0);

      const fileSize = file.size / 1024; // 转换为 KB
      const fileSizeStr = fileSize >= 1024
        ? `${(fileSize / 1024).toFixed(2)}MB`
        : `${fileSize.toFixed(2)}KB`;

      message.error(`文件大小超出限制：当前大小 ${fileSizeStr}，限制大小 ${size}${unit}!`);
    }
    return isLimited;

  };

  const uploadProps = {
    // multiple: false,
    action: `/api/v1/app/${getAppId()}/uploadfile`,
    fileList,
    maxCount,
    beforeUpload,
    accept,
    onChange: info => {
      const { fileList: newFileList, file } = info;

      // 有上传行为
      if (file.status) {
        setFileList(newFileList);
      }
      // 说明是删除
      if (!newFileList.length) {
        onChange(null);
        return;
      }
      if (file.status === 'done') {
        onChange(newFileList.map(formatFile));
      }

    }
  };

  return <Dragger {...uploadProps}>
    <p className="ant-upload-drag-icon">
      <UploadOutlined />
    </p>
    <p className="ant-upload-text">
      点击上传或拖拽文档到这里
    </p>
    <p className="ant-upload-hint">
      {description}
    </p>
  </Dragger>
};

export default FileUpload;