import { getAppId } from '@/utils/state';
import { InboxOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { message, Upload } from 'antd';

const { Dragger } = Upload;

const props: UploadProps = {
  name: 'file',
  multiple: false,
  accept: 'png|jpg|gif',
  action: `/api/v1/app/${getAppId()}/uploadfile`,
  onChange(info) {
    const { status } = info.file;
    if (status !== 'uploading') {
      console.log(info.file, info.fileList);
    }
    if (status === 'done') {
      message.success(`${info.file.name} file uploaded successfully.`);
    } else if (status === 'error') {
      message.error(`${info.file.name} file upload failed.`);
    }
  },
  onDrop(e) {
    console.log('Dropped files', e.dataTransfer.files);
  },
};

const Uploader: React.FC = () => (
  <Dragger {...props}>
    <p className="ant-upload-drag-icon">
      <InboxOutlined rev={undefined} />
    </p>
    <p className="ant-upload-text">点击或拖拽图片到这里</p>
    <p className="ant-upload-hint">只能上传单个文件，支持（png,jpg,jepg,gif）</p>
  </Dragger>
);

export default Uploader;
