import './group.less';

import type { NsGraph } from '@antv/xflow';
import { useXFlowApp, XFlowGroupCommands } from '@antv/xflow';

import { PARAM_GAP, PARAM_HEIGHT } from '@/utils/constant';

import { BaseLayout } from './base-node';
import React from 'react';

const mouseDown = false;

export const GroupNode: NsGraph.INodeRender = (props) => {
  const { cell } = props;
  const app = useXFlowApp();
  const isCollapsed = props.data.isCollapsed || false;
  const onExpand = () => {
    app.executeCommand(XFlowGroupCommands.COLLAPSE_GROUP.id, {
      nodeId: cell.id,
      isCollapsed: false,
      collapsedSize: { width: 200, height: 80 },
    });
  };
  const onCollapse = () => {
    app.executeCommand(XFlowGroupCommands.COLLAPSE_GROUP.id, {
      nodeId: cell.id,
      isCollapsed: true,
      collapsedSize: { width: 200, height: 80 },
      gap: 3,
    });
  };

  const height = 3 * (PARAM_GAP + PARAM_HEIGHT) + 20;

  const rightHeight = 2 * (PARAM_GAP + PARAM_HEIGHT) + 20;

  return (
    <BaseLayout {...props} className="xflow-group-node">
      {/* <div className="xflow-group-node">
      <div className="xflow-group-header">
        <div className="header-left">{props.data.name}</div>
        <div className="header-right">
          {isCollapsed && <PlusSquareOutlined onClick={onExpand} rev={undefined} />}
          {!isCollapsed && <MinusSquareOutlined onClick={onCollapse} rev={undefined} />}
        </div>
      </div>
    </div> */}
      <div className="port-rect" style={{ height }}></div>
      <div className="port-rect right" style={{ height: rightHeight }}></div>
    </BaseLayout>
  );
};
