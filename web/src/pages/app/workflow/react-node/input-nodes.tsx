// @ts-nocheck
import './input.less';

import { QuestionCircleOutlined, UploadOutlined } from '@ant-design/icons';
import { NsGraph, useXFlowApp } from '@antv/xflow';
import { Button, DatePicker, Input, InputNumber, Tooltip, Select, Slider, Radio } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import FileReaderInput from 'react-file-reader-input';

import { joinInputMap, JSONParse } from '@/utils/common';
import { NODE_CODE } from '@/utils/constant';

import { getCustomState } from '../config/config-model-service';
import { useNode } from '../hooks/useNode';
import Base from './base-node';
import Uploader from './imageUploader';
import { nos2url } from '@/pages/preview/type-map';

export const type2Node = {
  string: NODE_CODE.INPUT_TEXT,
  number: NODE_CODE.INPUT_NUMBER,
  integer: NODE_CODE.INPUT_NUMBER,
  float: NODE_CODE.INPUT_FLOAT,
  Image: NODE_CODE.INPUT_IMAGE,
  ImageNosKey: NODE_CODE.INPUT_IMAGE,
  Audio: NODE_CODE.INPUT_AUDIO,
  AudioNosKey: NODE_CODE.INPUT_AUDIO,
  Video: NODE_CODE.INPUT_VIDEO,
  VideoNosKey: NODE_CODE.INPUT_VIDEO,
  Url: NODE_CODE.INPUT_UPLOAD,
  NosKey: NODE_CODE.INPUT_UPLOAD,
  array: NODE_CODE.INPUT_ARRAY,
  datetime: NODE_CODE.INPUT_DATE,
  boolean: NODE_CODE.INPUT_BOOL,
};

export const FormattedNode = (props) => {
  const { value, code: inCode, onChange, type, defaultValue, params, permanent: inPermanent } = props;
  const permanent = inPermanent ?? props.data?.outputs?.[0]?.permanent;
  const sizeLimit = permanent ? 1024 * 50 : undefined;
  console.log("props...", props, permanent);
  let code = inCode || NODE_CODE.INPUT_TEXT;
  const range = (params?.length && params[0].range) ? params[0].range : null;
  const [val, setVal] = useState(() => {
    let curVal = defaultValue === undefined ? value : defaultValue;
    if (type && joinInputMap[type]) {
      curVal = joinInputMap[type](curVal);
    }
    return curVal;
  });
  let node = null;

  if (type && type2Node[type]) {
    code = type2Node[type];
  }

  const handleChange = (_val?) => {
    if (onChange) {
      onChange(_val ?? val);
      if (typeof _val !== 'undefined') {
        setVal(_val);
      }
    }
  };

  const uploadFile = (data) => {
    const rawData = data[0][0].target.result;
    let result: any = JSONParse(rawData);
    if (Array.isArray(result)) {
      result = result
        .map((v) => (typeof v === 'object' ? JSON.stringify(v) : v))
        .join('\n');
    } else {
      result = rawData;
    }
    handleChange(result);
  };

  if (code === NODE_CODE.INPUT_BOOL) {
    node = (
      <Radio.Group onChange={ev => handleChange(ev.target.value)} value={val}>
        <Radio value={true}>是</Radio>
        <Radio value={false}>否</Radio>
      </Radio.Group>
    );
  }

  if (code === NODE_CODE.INPUT_TEXT) {
    node = (
      <Input.TextArea
        rows={4}
        allowClear
        style={{ wordWrap: 'normal' }}
        onChange={(ev: any) => setVal(ev.target.value)}
        onBlur={() => handleChange()}
        value={val}
      ></Input.TextArea>
    );
  }
  if (code === NODE_CODE.INPUT_OBJECT) {
    node = (
      <Input.TextArea
        rows={8}
        allowClear
        style={{ wordWrap: 'normal' }}
        placeholder="请输入一个JSON对象"
        onChange={(ev: any) => setVal(ev.target.value)}
        onBlur={() => handleChange()}
        value={val}
      ></Input.TextArea>
    );
  }
  if (code === NODE_CODE.INPUT_ARRAY) {
    const newVal = Array.isArray(val) ? val.join('\n') : val;
    node = (
      <>
        <FileReaderInput
          as="text"
          id="my-file-input"
          onChange={(e, val) => uploadFile(val)}
        >
          <Button icon={<UploadOutlined />} style={{ marginBottom: 10 }}>
            上传文本
            <Tooltip
              placement="topLeft"
              title={'可以上传一个JSON数组，或者按行分割的字符串'}
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </Button>
        </FileReaderInput>
        <Input.TextArea
          rows={8}
          allowClear
          style={{ wordWrap: 'normal' }}
          placeholder="每行表示一个数组一个元素。注意：如果是对象，也需要一行一个JSONObject，复杂的对象建议采用文件上传"
          onChange={(ev: any) => setVal(ev.target.value)}
          onBlur={() => handleChange()}
          value={newVal}
        ></Input.TextArea>
      </>
    );
  }
  if (code === NODE_CODE.INPUT_UPLOAD) {
    node = <Uploader onChange={handleChange} value={val} type={'Url'} permanent={permanent} sizeLimit={sizeLimit} />;
  }
  if (code === NODE_CODE.INPUT_AUDIO) {
    node = <Uploader onChange={handleChange} value={val} type={'Audio'} permanent={permanent} sizeLimit={sizeLimit} />;
  }
  if (code === NODE_CODE.INPUT_ENUM) {
    const options = (code === NODE_CODE.INPUT_ENUM && params?.length && params[0].enum) ? params[0].enum : [];
    node = <Select onChange={handleChange} options={options.map(v => ({ label: v?.label ?? v?.value ?? v, value: v?.value ?? v }))} value={val} style={{ width: '100%' }} placeholder="请选择一个值" />;
  }
  if (code === NODE_CODE.INPUT_VIDEO) {
    node = <Uploader onChange={handleChange} value={val} type={'Video'} permanent={permanent} sizeLimit={sizeLimit} />;
  }
  if (code === NODE_CODE.INPUT_IMAGE) {
    console.log('uploader...', val, inCode, type);
    node = <Uploader onChange={handleChange} value={val} type={'Image'} permanent={permanent} sizeLimit={sizeLimit} />;
  }
  if (code === NODE_CODE.INPUT_DATE) {
    node = (
      <DatePicker
        showTime
        allowClear
        style={{ width: '100%' }}
        onChange={(val: any) => setVal(val.valueOf())}
        onBlur={() => handleChange()}
        value={val ? dayjs(val) : undefined}
      />
    );
  }
  if (code === NODE_CODE.INPUT_NUMBER) {
    node = (
      <InputNumber
        allowClear
        style={{ width: '100%' }}
        onChange={(val) => {
          setVal(val);
        }}
        min={range ? range[0] : undefined}
        max={range ? range[1] : undefined}
        precision={0}
        onBlur={() => handleChange()}
        value={val}
      />
    );
  }
  if (code === NODE_CODE.INPUT_FLOAT) {
    node = <InputNumber
      allowClear
      min={range ? range[0] : undefined}
      max={range ? range[1] : undefined}
      style={{ width: '100%' }}
      onChange={(val) => {
        setVal(val);
      }}
      onBlur={() => handleChange()}
      value={val}
    />
  }
  if (code === NODE_CODE.INPUT_CIO) {
    node = (
      <Input
        allowClear
        onChange={(ev: any) => setVal(ev.target.value)}
        placeholder="请输入资源ID"
        onBlur={async () => {
          // TODO 通过接口拿CIO信息
          handleChange();
        }}
        value={val}
      />
    );
  }
  return node;
};

const ExtraComponent = (props: any) => {
  const app = useXFlowApp();
  const [preview, setPreview] = useState(false);
  const { data, code = NODE_CODE.INPUT_TEXT } = props;
  const { updateNode } = useNode();

  useEffect(() => {
    async function getModel() {
      const val = await getCustomState(app.modelService);
      setPreview(val.preview);
    }
    getModel();
  }, []);

  const handleChange = (_val) => {
    updateNode(data.id, {
      originValue: _val
    });
  };

  if (preview) {
    return null;
  }

  return (
    <div className="extra-content">
      <FormattedNode
        onChange={handleChange}
        code={code}
        data={data}
        params={data.outputs}
        value={data.originValue ?? data.value ?? ''}
      ></FormattedNode>
    </div>
  );
};

export const InputNode: NsGraph.INodeRender = (props) => {
  return (
    <Base {...props}>
      <ExtraComponent
        code={props.data.code}
        {...props}
        label={props.data.renderKey}
      ></ExtraComponent>
    </Base>
  );
};
