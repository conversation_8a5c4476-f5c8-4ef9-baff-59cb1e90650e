import React, { useState } from 'react';
import type { InputNumberProps } from 'antd';
import { Col, InputNumber, Row, Slider } from 'antd';

type StepProps = {
  min: number;
  max: number;
  value: number;
  onChange: (any) => void;
  defaultValue: number;
};

export const IntegerStep: React.FC<StepProps> = ({ min, max, value, onChange, defaultValue } : StepProps) => {
  const [inputValue, setInputValue] = useState(value || defaultValue);

  const handleChange: InputNumberProps['onChange'] = (newValue) => {
    setInputValue(newValue as number);
    onChange(newValue as number)
  };

  return (
    <Row>
      <Col span={12}>
        <Slider
          min={min}
          max={max}
          defaultValue={defaultValue}
          onChange={handleChange}
          value={typeof inputValue === 'number' ? inputValue : 0}
        />
      </Col>
      <Col span={4}>
        <InputNumber
          min={min}
          max={max}
          defaultValue={defaultValue}
          style={{ margin: '0 16px' }}
          value={inputValue}
          onChange={handleChange}
        />
      </Col>
    </Row>
  );
};

export const DecimalStep: React.FC<StepProps> = ({ min, max, value, onChange, defaultValue }: StepProps) => {
  const [inputValue, setInputValue] = useState(value || defaultValue);

  const handleChange: InputNumberProps['onChange'] = (value) => {
    if (isNaN(value as number)) {
      return;
    }
    setInputValue(value as number);
    onChange(value);
  };

  return (
    <Row>
      <Col span={12}>
        <Slider
          min={min}
          max={max}
          defaultValue={defaultValue}
          onChange={handleChange}
          value={typeof inputValue === 'number' ? inputValue : 0}
          step={0.01}
        />
      </Col>
      <Col span={4}>
        <InputNumber
          min={min}
          max={max}
          defaultValue={defaultValue}
          style={{ margin: '0 16px' }}
          step={0.01}
          value={inputValue}
          onChange={handleChange}
        />
      </Col>
    </Row>
  );
};
