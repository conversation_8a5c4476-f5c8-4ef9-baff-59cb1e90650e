@light-border: 1px solid #d9d9d9;
@primaryColor: #c2c8d5;

.xflow-algo-container {
  display: flex;
  width: 180px;
  min-height: 40px;
  background-color: #fff;
  border-radius: 10px;
  flex-direction: column;
  &:hover {
    background-color: #fff;
    border: 1px solid #3057e3;
    // border: 1px solid @primaryColor;
    box-shadow: 0 0 3px 3px rgba(48, 86, 227, 0.15);
    cursor: move;
  }
  box-shadow: ~'-1px -1px 4px 0 rgba(223,223,223,0.50), -2px 2px 4px 0 rgba(244,244,244,0.50), 2px 3px 8px 2px rgba(151,151,151,0.05)';
  // transition: all ease-in-out 0.15s;
  border: 1px solid @primaryColor;
  padding: 10px;
}

.xflow-algo-node {
  display: flex;
  line-height: 20px;
  text-align: center;
  border-radius: 2px;
  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
  }
  .label {
    width: 108px;
    overflow: hidden;
    font-size: 14px;
    text-overflow: ellipsis;
  }
  .status {
    width: 36px;
  }
  &.panel-node {
    border: 0;
  }
}

.x6-node-selected {
  .xflow-algo-container {
    background-color: rgba(48, 86, 227, 0.05);
    border: 1px solid #3057e3;
    box-shadow: 0 0 3px 3px rgba(48, 86, 227, 0.15);
    &:hover {
      background-color: #fff;
      box-shadow: 0 0 5px 5px rgba(48, 86, 227, 0.15);
    }
  }
}

.xflow-alog-content {
  margin-top: 10px;

}