/* eslint-disable react/no-unknown-property */
/* eslint-disable jsx-a11y/alt-text */
// @ts-nocheck
import './base.less';

import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DatabaseOutlined,
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  RedoOutlined,
} from '@ant-design/icons';
import { createFromIconfontCN } from '@ant-design/icons';
import { IconStore, useAppContext } from '@antv/xflow';
import { NsGraphStatusCommand, useXFlowApp } from '@antv/xflow';
import { Popconfirm, Tooltip, Modal, Tag, message, Table, Select } from 'antd';
import React, { useEffect, useMemo, useState, useRef } from 'react';

import { useGlobalState } from '@/hooks/useGlobalState';
import { NODE_CODE, NODE_HEIGHT_MAP, NODE_TYPE } from '@/utils/constant';

import { SHOW_RENAME_MODAL } from '../cmd-extensions/constants';
import { renameNode } from '../config/cmds/hooks-services';
import { getCustomState } from '../config/config-model-service';
import { useNode } from '../hooks/useNode';
import { pick } from 'lodash';
import styled from 'styled-components';
import { IconFont } from '@/components/icons';

const ForSelect = styled(Select)`
  top: 44px;
  width: 140px;
  position: absolute;
`;

const fontStyle = { fontSize: '16px' };
interface IProps {
  status: NsGraphStatusCommand.StatusEnum;
  hide: boolean;
  onDelete: () => {};
  preview: boolean;
}

const TYPE_COLOR = {
  'update_rule': {
    text: '更新规则',
    style: {
      color: 'geekblue',
    }
  },
  'update_value': {
    text: '更新值',
    style: {
      color: 'blue',
    }
  },
  'update_enum': {
    text: '更新选项',
    style: {
      color: 'gold',
    }
  },
  'update_type': {
    text: '更新选项',
    style: {
      color: 'blue',
    }
  },
  'add': {
    text: '新增',
    style: {
      color: '#1f8437',
    }
  },
  'delete': {
    text: '删除',
    style: {
      color: '#fe6c6c',
      textDecoration: 'line-through'
    }
  }
}


const columns = [
  {
    title: '变更',
    dataIndex: 'type',
    render: item => {
      const color = TYPE_COLOR[item].style?.color || '';
      return <Tag color={color}>{TYPE_COLOR[item].text}</Tag>
    }
  },
  {
    title: '参数名',
    dataIndex: 'name',
  },
  {
    title: '变更前',
    dataIndex: 'value',
    render: item => item && item[0]
  },
  {
    title: '变更后',
    dataIndex: 'value',
    render: item => item && item[1]
  },
];

const Changes: React.FC = ({ data }) => {
  const dataSource = useMemo(() => {
    return [
      ...data.inputs.map(v => ({ ...v, scope: 'inputs' })),
      ...data.vars.map(v => ({ ...v, scope: 'vars' })),
      ...data.outputs.map(v => ({ ...v, scope: 'outputs' }))]
  }, [data])

  return (
    <Table columns={columns} dataSource={dataSource} />
  );
};

export const AlgoIcon: React.FC<IProps> = (props) => {
  const { needAlert, changes, onUpdate } = props;
  const modal = useRef(null)

  const handleAlert = () => {
    console.log('changes', changes);
    modal.current = Modal.confirm({
      title: '更新的内容',
      width: 1000,
      okText: '立即更新',
      cancelText: '暂不更新',
      maskClosable: true,
      onOk: onUpdate,
      content: <Changes data={changes} onUpdate={onUpdate}></Changes>,
    })
  }

  if (props.hide) {
    return null;
  }
  switch (props.status) {
    case NsGraphStatusCommand.StatusEnum.PROCESSING:
      return (
        <RedoOutlined
          spin
          style={{ color: '#c1cdf7', fontSize: '16px' }}
          rev={undefined}
        />
      );
    case NsGraphStatusCommand.StatusEnum.ERROR:
      return (
        <CloseCircleOutlined
          style={{ color: '#ff4d4f', fontSize: '16px' }}
          rev={undefined}
        />
      );
    case NsGraphStatusCommand.StatusEnum.SUCCESS:
      return (
        <CheckCircleOutlined
          style={{ color: '#39ca74cc', fontSize: '16px' }}
          rev={undefined}
        />
      );
    case NsGraphStatusCommand.StatusEnum.WARNING:
      return (
        <ExclamationCircleOutlined
          style={{ color: '#faad14', fontSize: '16px' }}
          rev={undefined}
        />
      );
    default:
      if (needAlert) {
        return <Tooltip title="有更新，点击更新">
          <InfoCircleOutlined
            style={{ color: 'red', fontSize: '16px', cursor: 'pointer' }}
            onClick={handleAlert}
            rev={undefined}
          />
        </Tooltip>
      }
      return (
        <InfoCircleOutlined
          style={{ color: '#d9d9d9', fontSize: '16px' }}
          rev={undefined}
        />
      );
  }
};

const index = 0;

const SvgIcon = ({ icon, type, ...rest }) => (
  <span dangerouslySetInnerHTML={{ __html: icon }} {...rest} />
);

export const AvatarIcon = (props: any) => {
  // 控制图标展示
  let Icon = DatabaseOutlined;
  let iconImage = '';
  if (props.icon) {
    if (props.icon.startsWith('<svg')) {
      Icon = SvgIcon;
    } else if (props.icon.includes('icon-')) {
      Icon = IconFont;
    } else if (props.icon.includes('http')) {
      iconImage = props.icon;
    } else {
      Icon = IconStore.get(props.icon) || DatabaseOutlined;
    }
  }
  return (
    <span className="avatar-icon">
      {iconImage ? (
        <img src={iconImage} width={40} height={40} {...props} />
      ) : (
        <Icon
          {...props}
          style={{ ...fontStyle, ...(props.style || {}) }}
          type={props.icon}
          rev={undefined}
        />
      )}
    </span>
  );
};

const category_map = {
  '输入': 'INPUT',
  '逻辑': 'LOGIC',
  '请求': 'REQUEST',
  '脚本': 'SCRIPT',
  '大模型': 'LLM',
}

const BaseNode = (props: any) => {
  const app = useXFlowApp();
  const { data, mini } = props;
  const [preview, setPreview] = useState(false);

  useEffect(() => {
    async function getModel() {
      const val = await getCustomState(app.modelService);
      setPreview(val.preview);
      // console.log('index', val);
    }
    getModel();
  }, []);


  // 根据输入输出情况动态变更当前节点的展示
  const paramsBar = useMemo(() => {
    const params: any[] = [...(data.inputs || []), ...(data.outputs || [])];
    return params.map((v) => {
      return <div className="param-bar"></div>;
    });
  }, [data.inputs, data.outputs]);
  // console.log("props", paramsBar, props);

  const children = props.children
    ? React.cloneElement(props.children, { preview })
    : null;

  return (
    <BaseLayout {...props}>
      {!mini && (
        <div className="xflow-base-body">
          {paramsBar}
          {children}
        </div>
      )}
    </BaseLayout>
  );
};

export const BaseLayout = (props: any) => {
  const { data, mini, cell } = props;
  const { globalState } = useGlobalState();
  const { deleteNode, updateNode } = useNode();
  const app = useXFlowApp();

  // 控制样式展示
  const style = {};
  if (data.height) {
    style.height = data.height;
  }
  // 如果是 input 必须满足input 高度
  if (
    data.type === NODE_TYPE.INPUT &&
    NODE_HEIGHT_MAP[data.code] &&
    data.height < NODE_HEIGHT_MAP[data.code] &&
    cell
  ) {
    style.height = NODE_HEIGHT_MAP[data.code];
    cell.setData(() => ({
      ...data,
      height: NODE_HEIGHT_MAP[data.code],
    }));
  }

  // 需要将移动变更的xy重新设置到data上，否则配置面板更新值，会导致节点重新渲染回原始位置
  if (!mini) {
    props.data.x = props.position.x;
    props.data.y = props.position.y;
  }

  const handleDelete = () => {
    console.log('delete');
    deleteNode(props.data);
  };

  const edit = async () => {
    app.executeCommand(SHOW_RENAME_MODAL.id, {
      nodeConfig: props.data,
      updateNodeNameService: renameNode,
    });
  };

  // 更新组件的配置
  const handleUpdate = async () => {
    message.success('更新成功');
    await updateNode(props.data.id, { ...pick(props.data.updateData, ['inputs', 'vars', 'outputs']), needAlert: false });
  }

  // 更新迭代器的选择
  const handleSelect = async (index) => {
    console.log('select', index);
    await updateNode(props.data.id, { index, ...props.data.groupChildrenSize });
  };



  return (
    <div
      style={style}
      className={`${!mini ? 'xflow-base-container' : 'mini'} ${props.className} ${props.data.class
        } ${props.data.type} ${props.data.category} ${category_map[props.data.category]} ${props?.data?.status}`}
    >
      <div className={`xflow-base-title ${props.isNodeTreePanel ? 'panel-node' : ''} `}>
        <AvatarIcon icon={props.data.icon}></AvatarIcon>
        <span className="label">
          <span className="one-line">{props.data.name}</span>{' '}
          {!mini && !(props.data.name === '开始' || props.data.name === '结束') && <EditOutlined className="edit" onClick={edit} />}
        </span>
        {props.data.code === NODE_CODE.FOREACH && props?.data?.count ?
          <ForSelect
            onChange={handleSelect}
            value={props?.data?.index}
            options={
              Array.from(new Array(props?.data?.count)).map((_, idx) => ({
                label: `第${idx}次循环`, value: idx
              }))} defaultValue={0}></ForSelect>
          : null}
        {!mini && (
          <>
            <span className="status">
              <AlgoIcon
                onUpdate={handleUpdate}
                needAlert={props.data && props.data.needAlert}
                changes={props.data && props.data.changes}
                preview={globalState.running}
                onDelete={handleDelete}
                status={props.data && props.data.status}
                hide={props.isNodeTreePanel}
              />
            </span>
            {props.data.code !== NODE_CODE.OUTPUT && !props.data.fixed && (
              <span className="trash">
                <Popconfirm
                  title="删除该节点"
                  description="确认删除该节点吗？删除后无法恢复（对应的边也会删除）"
                  onConfirm={handleDelete}
                  okText="Yes"
                  cancelText="No"
                >
                  <DeleteOutlined
                    className="delete-icon"
                    rev={undefined}
                  ></DeleteOutlined>
                </Popconfirm>
              </span>
            )}
          </>
        )}
      </div>
      {props.children}
    </div>
  );
};

/**
 * 渲染拖拽的节点
 * @param props
 * @returns
 */
export const renderDndComponent = (props: any) => {
  return <BaseNode {...props} mini className="xflow-dnd-item" />;
};

export default BaseNode;
