import { useState } from 'react'
import { useNode } from '../hooks/useNode';
import { Input } from 'antd';
import { NsGraph } from '@antv/xflow'
import './algo-node.less'
import Base from './base-node';

// const ExtraComponent = (props: any) => {
//   const { data, setValue } = props;
//   const { updateNode } = useNode();
//   const [val, setVal] = useState(data?.value || '');

//   console.log("data", data);

//   const handleChange = () => {
//     console.log("val", val);
//     updateNode(data.id, {
//       value: val
//     });
//   };
//   return <Input.TextArea onChange={(ev: any) => setVal(ev.target.value)} onBlur={handleChange} value={val}></Input.TextArea>
// }

export const Atomic: NsGraph.INodeRender = props => {
  return (
    <Base {...props}>
    </Base>
  )
}
