import React, { useState } from 'react'
import {
  DatabaseOutlined,
  RedoOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons'
import { Input, Image, Tag, Divider } from 'antd';
import { IconStore, NsGraph } from '@antv/xflow'
import { NsGraphStatusCommand } from '@antv/xflow'
import { useNode } from '../hooks/useNode';
import './algo-node.less'
import { IMAGE_RENDER_ID, TEXT_RENDER_ID } from '@utils/constant';

const fontStyle = { fontSize: '16px', color: '#3057e3' }
interface IProps {
  status: NsGraphStatusCommand.StatusEnum
  hide: boolean
}
export const AlgoIcon: React.FC<IProps> = props => {
  if (props.hide) {
    return null
  }
  switch (props.status) {
    case NsGraphStatusCommand.StatusEnum.PROCESSING:
      return <RedoOutlined spin style={{ color: '#c1cdf7', fontSize: '16px' }} rev={undefined} />
    case NsGraphStatusCommand.StatusEnum.ERROR:
      return <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} rev={undefined} />
    case NsGraphStatusCommand.StatusEnum.SUCCESS:
      return <CheckCircleOutlined style={{ color: '#39ca74cc', fontSize: '16px' }} rev={undefined} />
    case NsGraphStatusCommand.StatusEnum.WARNING:
      return <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: '16px' }} rev={undefined} />
    case NsGraphStatusCommand.StatusEnum.DEFAULT:
      return <InfoCircleOutlined style={{ color: '#d9d9d9', fontSize: '16px' }} rev={undefined} />
    default:
      return null
  }
}
const ExtraComponent = (props: any) => {
  const { label, data } = props;
  const { updateNode } = useNode();
  const [val, setVal] = useState(data?.value || '');

  console.log("data", data);

  const handleChange = () => {
    console.log("val", val);
    updateNode(data.id, {
      value: val
    });
  };

  if (label === TEXT_RENDER_ID) {
    return <Input.TextArea onChange={(ev:any) => setVal(ev.target.value)} onBlur={handleChange} value={val}></Input.TextArea>
  }
  if (label === IMAGE_RENDER_ID) {
    return <Image src="https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/29066095317/b52e/cafe/1852/234ade5b936959d4601bdaea34dc9090.png"></Image>
  }
  return null;
}

export const AlgoNode: NsGraph.INodeRender = props => {
  let Icon = DatabaseOutlined;
  if (props.data.icon) {
    Icon = IconStore.get(props.data.icon)
  }
  const style: any = {};
  console.log('height', props);
  if (props.data.height) {
    style.height = props.data.height;
  }
  const ports = (props.data?.ports?.items || []).map(p => (<div className="xflow-node-param"></div>))
  return (
    <div className={`xflow-algo-container`} style={style}>
      <div className={`xflow-algo-node ${props.isNodeTreePanel ? 'panel-node' : ''}`}>
        <span className="icon">
          <Icon style={fontStyle} rev={undefined} />
        </span>
        <span className="label">{props.data.label}</span>
        <span className="status">
          <AlgoIcon status={props.data && props.data.status} hide={props.isNodeTreePanel} />
        </span>
      </div>
      <div>
      {ports}
      </div>
      {props?.data?.preview ?
        <div className='xflow-alog-content'><ExtraComponent {...props} label={props.data.renderKey}></ExtraComponent></div>
        : null
      }
    </div>
  )
}
