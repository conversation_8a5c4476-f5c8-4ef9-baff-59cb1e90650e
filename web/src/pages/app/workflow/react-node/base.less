@light-border: 1px solid #d9d9d9;
@primaryColor: #c2c8d5;

.xflow-base-container {
  display: flex;
  width: @node-width;
  min-height: 40px;
  background-color: #fff;
  border-radius: 14px;
  flex-direction: column;
  border: 2px solid transparent;
  box-sizing: border-box;
  &.error {
    // border: 3px solid red;
    background-color: #fecece;
  }
  &.success {
    // box-shadow: 0px 0px 14px 8px rgba(75, 192, 102, 0.51);
    background-color: rgb(253 255 253);
    border: 2px solid rgba(75, 192, 102, 0.51);
  }
  &.processing {
    animation: breathing 2s infinite;
  }
  &:hover {
    // background-color: #fff;
    // border: 2px solid #3057e3;
    // border: 1px solid @primaryColor;
    box-shadow: 0 0 2px 3px rgb(192 189 189 / 54%);
    box-shadow: 0 0 #0000, 0 0 #0000, 0px 4px 6px -2px rgba(16, 24, 40, .03), 0px 12px 16px -4px rgba(16, 24, 40, .08);
    cursor: move;
  }
  box-shadow: ~'-1px -1px 4px 0 rgba(223,223,223,0.50), -2px 2px 4px 0 rgba(244,244,244,0.50), 2px 3px 8px 2px rgba(151,151,151,0.05)';
  // transition: all ease-in-out 0.15s;
  padding-bottom: 10px;
}
.x6-node-selected {
  .xflow-base-container {
    border: 2px solid #1890ff;
    // box-shadow: 0px 0px 14px 8px #1890ff;
  }
}

.param-bar {
  height: @param-height;
  background: #e0e0e04f;
  margin-bottom: @param-gap;
  // border-top: 1px solid #eaeaea;
  box-sizing: border-box;
  border-radius: 4px;
  // border-bottom: 1px solid #eaeaea;
}

.xflow-base-title{
  .avatar-icon {
    color: #fff;
  }
}

.xflow-base-container {
  .xflow-base-title {
    padding: 10px;
    height: @title-height;
    align-items: center;
    display: flex;
    line-height: 20px;
    text-align: center;
    border-radius: 2px;
    justify-content: space-evenly;

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
    }
    .avatar-icon {
      margin-right: 10px;
      margin-left: 5px;
      padding: 5px;
      border-radius: 8px;
      width: 20px;
      height: 20px;
      box-shadow: 0 0 #0000, 0 0 #0000, 0px 2px 4px -2px rgba(16, 24, 40, .06), 0px 4px 8px -2px rgba(16, 24, 40, .1);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .label {
      // width: 140px;
      flex: 1;
      font-weight: bold;
      overflow: hidden;
      font-size: 16px;
      text-overflow: ellipsis;
    }
    .status {
      margin-right: 5px;
    }
    &.panel-node {
      border: 0;
    }
  }

}

.INPUT, .输入 {
  background-color: #fff;
  .avatar-icon {
    background-color: #2650dc;
  }
}


.OUTPUT {
  background-color: #fff;
  .avatar-icon {
    background-color: #2650dc;
  }
}

.LOGIC {
  background-color: #fff;
  .avatar-icon {
    background-color: #e4eaff;
  }
}

.logic, .LOGIC, .逻辑, .SWITCH, .SELECT, .JOIN {
  background-color: #fff;
  .avatar-icon {
    background-color: #f1a950;
  }
}

.HTTP, .LLM {
  background-color: #fff;
  .avatar-icon {
    background-color: #ff6464;
  }
}
.SCRIPT, .脚本 {
  background-color: #fff;
  .avatar-icon {
    background-color: #28af07;
  }
}
.REQUEST,.请求 {
  background-color: #fff;
  .avatar-icon {
    background-color: #92cbcb;
  }
}
.自定义 {
  background-color: #fff;
  .avatar-icon {
    background-color: #70bf37;
  }
}

 .音视频 {
  background-color: #fff;
  .avatar-icon {
    background-color: #c67ee9;
  }
}

.图片 {
  background-color: #fff;
  .avatar-icon {
    background-color: #ff80c3;
  }
}

.x6-node-selected {
  .xflow-algo-container {
    background-color: rgba(48, 86, 227, 0.05);
    border: 1px solid #3057e3;
    box-shadow: 0 0 3px 3px rgba(48, 86, 227, 0.15);
    &:hover {
      // background-color: #fff;
      box-shadow: 0 0 5px 5px rgba(48, 86, 227, 0.15);
    }
  }
}

.xflow-alog-content {
  margin-top: 10px;
}

.delete-icon {
  cursor: pointer;
  color: #3057e3;
}

.desc {
  height: @desc-height;
}

.xflow-base-body {
  margin-top: -5px;
    margin: 0px 10px;
    border-radius: 10px;
}

@keyframes breathing {
  0% {
    box-shadow: 0px 0px 2px 4px rgb(240 225 41 / 51%);
  }
  50% {
    box-shadow: 0px 0px 4px 10px rgb(240 225 41 / 51%);
  }
  100% {
    box-shadow: 0px 0px 2px 4px rgb(240 225 41 / 51%);
  }
}

.edit {
  cursor: pointer;
  color:#1890ff;
}

body {
  --sb-track-color: transparent;
  --sb-thumb-color: #a7d2f626;
  --sb-size: 2px;

  scrollbar-color: var(--sb-thumb-color) 
                   var(--sb-track-color);
}

::-webkit-scrollbar {
  width: 20px;
}

.post-markdown {
  p {
    margin: 0;
  }
  ol {
    margin: 0;
    padding-inline-start: 40px;
    margin-block-start: 0;
    margin-block-end: 0;
    line-height: 1;
  }
}

// ::-webkit-scrollbar-track {
//   background: var(--sb-track-color);
//   width: 2px;
//   border-radius: 10px;
// }

// ::-webkit-scrollbar-thumb {
//   background: var(--sb-thumb-color);
//   border-radius: 10px;
// }
// /* 显示滚动条当hover或active */
// ::-webkit-scrollbar:hover {
//   width: 10px;
//   height: 10px;
// }

// body::-webkit-scrollbar:active {
//   width: 10px;
//   height: 10px;
// }

.workspace-select{
  &.ant-select-disabled{
    .ant-select-selector {
      color: rgba(0, 0, 0, 0.88) !important;
      background: rgba(0, 0, 0, 0.04);
      cursor: pointer !important;
    }
  }
}