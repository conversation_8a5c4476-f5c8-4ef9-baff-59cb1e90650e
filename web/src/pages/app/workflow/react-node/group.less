@light-border: 1px solid #d9d9d9;
@primaryColor: #c1cdf7;

.xflow-group-node {
  z-index: 9;
  width: 100%;
  height: 100%;
  background-color: #e4eaff57;
  border: 1px solid rgba(255, 255, 255, 0.25);
  border-radius: 4px;
  box-shadow: ~'rgb(17 49 96 / 12%) 0px 1px 3px 0px, rgb(17 49 96 / 4%) 0px 0px 0px 1px';
  cursor: grab;
  &:hover {
    background-color: rgba(227, 244, 255, 0.45);
    border: 1px solid @primaryColor;
    box-shadow: 0 0 3px 3px rgba(64, 169, 255, 0.2);
    cursor: move;
  }
  .xflow-group-header {
    display: flex;
    justify-content: space-between;
    padding: 0 12px;
    font-size: 14px;
    line-height: 38px;
    .header-left {
      width: 80%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .header-right {
      display: inline-flex;
      align-items: center;
      span.anticon {
        margin-left: 8px;
      }
    }
  }
}

.x6-node-selected {
  .xflow-group-node {
    background-color: rgba(243, 249, 255, 0.92);
    border: 1px solid @primaryColor;
    box-shadow: 0 0 3px 3px rgb(64 169 255 / 20%);
    &:hover {
      background-color: rgba(243, 249, 255, 0.6);
    }
  }
}

.embedding {
  .xflow-group-node {
    border: 8px solid rgb(243, 113, 15);
  }
}


.port-rect {
  position: absolute;
  left: -10px;
  width: 20px;
  background-color: #fff;
  top: 40px;
  border: 1px solid #759bffad;
  border-radius: 4px;
}

.right {
  right: -10px;
  left: auto;
}


.resize-ctl-container {
  span.anticon{
    margin: 0 !important;
  }
  position: absolute;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 20px;
  background: #fff;
  display: none;
  align-items: center;
  justify-content: center;
  box-shadow: 1px 1px 5px 1px #00000038;
}

.resize-ctl {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  cursor: crosshair;
}

.left-bottom {
  left: -20px;
  bottom: -20px;
}

.right-bottom {
  right: -20px;
  bottom: -20px;
}

.xflow-group-node {
  &:hover {
    .resize-ctl-container {
      display: flex;
    }
  }
}