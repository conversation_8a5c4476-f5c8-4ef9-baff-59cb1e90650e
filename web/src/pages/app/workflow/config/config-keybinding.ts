// @ts-nocheck
import type { NsEdgeCmd, NsGraphCmd, NsNodeCmd } from '@antv/xflow';
import {
  createKeybindingConfig,
  MODELS,
  XFlowEdgeCommands,
  XFlowGraphCommands,
  XFlowNodeCommands,
} from '@antv/xflow';

import { NODE_CODE, NODE_TYPE } from '@/utils/constant';

import { getCustomState } from './config-model-service';

export const useKeybindingConfig = createKeybindingConfig((config) => {
  config.setKeybindingFunc((regsitry) => {
    return regsitry.registerKeybinding([
      {
        id: 'delete node or edge',
        keybinding: 'backspace',
        callback: async function (item, modelService, cmd, e) {
          const cells = await MODELS.SELECTED_CELLS.useValue(modelService);
          // 禁止删除输出节点
          if (cells.length === 1 && cells[0].data.code === NODE_CODE.OUTPUT) {
            return;
          }
          console.log(cells);
          if (cells.length === 1 && cells[0].data.fixed) {
            return;
          }
          e.preventDefault();
          // 先删除edges
          await Promise.all(
            cells.map((cell) => {
              if (cell.isEdge()) {
                return cmd.executeCommand<NsEdgeCmd.DelEdge.IArgs>(
                  XFlowEdgeCommands.DEL_EDGE.id,
                  {
                    edgeConfig: { ...cell.getData(), id: cell.id },
                  },
                );
              }
            }),
          );
          // 先删除nodes
          await Promise.all(
            cells.map((cell) => {
              if (cell.isNode()) {
                if (cell.data.type !== NODE_TYPE.OUTPUT) {
                  return cmd.executeCommand<NsNodeCmd.DelNode.IArgs>(
                    XFlowNodeCommands.DEL_NODE.id,
                    {
                      nodeConfig: {
                        ...cell.getData(),
                        id: cell.id,
                      },
                    },
                  );
                }
                return Promise.resolve();
              }
            }),
          );
        },
      },
      {
        id: 'group',
        keybinding: ['alt+space'],
        callback: async function (item, modelService, cmd, e) {
          cmd.executeCommand<NsGraphCmd.GraphToggleMultiSelect.IArgs>(
            XFlowGraphCommands.GRAPH_TOGGLE_MULTI_SELECT.id,
            {},
          );
        },
      },
      {
        id: 'copy',
        keybinding: ['command+c', 'ctrl+c'],
        callback: async function (item, modelService, cmd, e) {
          const cells = await MODELS.SELECTED_CELLS.useValue(modelService);
          if (
            cells.length === 1 &&
            (cells[0].data.type === NODE_TYPE.INPUT ||
              cells[0].data.type === NODE_TYPE.OUTPUT)
          ) {
            return;
          }
          e.preventDefault();
          cmd.executeCommand<NsGraphCmd.GraphCopySelection.IArgs>(
            XFlowGraphCommands.GRAPH_COPY.id,
            {},
          );
        },
      },
      {
        id: 'paste',
        keybinding: ['command+v', 'ctrl+v'],
        callback: async function (item, modelService, cmd, e) {
          const cells = await MODELS.SELECTED_CELLS.useValue(modelService);
          if (
            cells.length === 1 &&
            (cells[0].data.type === NODE_TYPE.INPUT ||
              cells[0].data.type === NODE_TYPE.OUTPUT)
          ) {
            return;
          }
          const state = await getCustomState(modelService);
          if (state.preview) {
            return;
          }
          e.preventDefault();
          cmd.executeCommand<NsGraphCmd.GraphPasteSelection.IArgs>(
            XFlowGraphCommands.GRAPH_PASTE.id,
            {},
          );
        },
      },
    ]);
  });
});
