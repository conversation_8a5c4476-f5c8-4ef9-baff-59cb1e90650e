import {
  ApartmentOutlined,
  AudioOutlined,
  CalendarOutlined,
  CloudSyncOutlined,
  CodeOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  FieldNumberOutlined,
  FileImageOutlined,
  CloudUploadOutlined,
  FontSizeOutlined,
  ForkOutlined,
  FunctionOutlined,
  GatewayOutlined,
  GroupOutlined,
  HighlightOutlined,
  MessageOutlined,
  PlaySquareOutlined,
  RedoOutlined,
  ReloadOutlined,
  SaveOutlined,
  SplitCellsOutlined,
  StopOutlined,
  UndoOutlined,
  ExportOutlined,
  UngroupOutlined,
} from '@ant-design/icons';
import { IconStore } from '@antv/xflow';

IconStore.set('ExportOutlined', ExportOutlined)
IconStore.set('DeleteOutlined', DeleteOutlined);
IconStore.set('CopyOutlined', CopyOutlined);
IconStore.set('FieldNumberOutlined', FieldNumberOutlined);
IconStore.set('EditOutlined', EditOutlined);
IconStore.set('StopOutlined', StopOutlined);
IconStore.set('EyeInvisibleOutlined', EyeInvisibleOutlined);
IconStore.set('EyeOutlined', EyeOutlined);
IconStore.set('FunctionOutlined', FunctionOutlined);
IconStore.set('CodeOutlined', CodeOutlined);
IconStore.set('ApartmentOutlined', ApartmentOutlined);
IconStore.set('ForkOutlined', ForkOutlined);
IconStore.set('FontSizeOutlined', FontSizeOutlined);
IconStore.set('FileImageOutlined', FileImageOutlined);
IconStore.set('ReloadOutlined', ReloadOutlined);
IconStore.set('SplitCellsOutlined', SplitCellsOutlined);
IconStore.set('MessageOutlined', MessageOutlined);
IconStore.set('AudioOutlined', AudioOutlined);
IconStore.set('HighlightOutlined', HighlightOutlined);
IconStore.set('CloudUploadOutlined', CloudUploadOutlined);

/** 注册icon 类型 */
IconStore.set('SaveOutlined', SaveOutlined);
IconStore.set('CloudSyncOutlined', CloudSyncOutlined);
IconStore.set('GatewayOutlined', GatewayOutlined);
IconStore.set('GroupOutlined', GroupOutlined);
IconStore.set('UngroupOutlined', UngroupOutlined);
IconStore.set('PlaySquareOutlined', PlaySquareOutlined);
IconStore.set('StopOutlined', StopOutlined);
IconStore.set('RedoOutlined', RedoOutlined);
IconStore.set('UndoOutlined', UndoOutlined);
IconStore.set('CalendarOutlined', CalendarOutlined);
