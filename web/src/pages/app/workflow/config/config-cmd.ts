// @ts-nocheck
import type { IGraphPipelineCommand, NsGraphCmd, NsGroupCmd } from '@antv/xflow';
import type { IApplication } from '@antv/xflow';
import { createCmdConfig, XFlowGraphCommands, XFlowGroupCommands } from '@antv/xflow';

import { getGraphData } from '@/utils/flow';
import { $ } from '@/utils/state';

import * as CustomCommands from '../cmd-extensions/constants';
import { commandContributions } from '../cmd-extensions/index';
import * as HooksApi from './cmds/hooks-services';
import { IAppType } from '@/interface';

export const useCmdConfig = createCmdConfig((config) => {
  // 注册全局Command扩展
  config.setCommandContributions(() => commandContributions);
});

export const initGraphCmdsAgent = (app: IApplication) => {
  app.executeCommandPipeline([
    /** 1. 从服务端获取数据 */
    {
      commandId: XFlowGraphCommands.LOAD_DATA.id,
      getCommandOption: async () => {
        HooksApi.setGraphDataReady(false);
        return {
          args: {
            loadDataService: HooksApi.loadGraphData(IAppType.AgentWorkflow),
          },
        };
      },
    } as IGraphPipelineCommand<NsGraphCmd.GraphLoadData.IArgs>,
     /** 2. 渲染画布 */
    {
      commandId: XFlowGraphCommands.GRAPH_RENDER.id,
      getCommandOption: async (ctx) => {
        const { graphData } = ctx.getResult();
        const nodes = graphData.nodes;
        return {
          originData: graphData,
          args: {
            graphData: {
              ...graphData,
              nodes,
            },
          },
        };
      },
    } as IGraphPipelineCommand<NsGraphCmd.GraphRender.IArgs>,
    /** 4. 缩放画布 */
    {
      commandId: XFlowGraphCommands.GRAPH_ZOOM.id,
      getCommandOption: async () => {
        HooksApi.setGraphDataReady();
        return {
          args: {
            factor: 'real',
            zoomOptions: {
              maxScale: 1.5,
              minScale: 0.05,
            },
          },
        };
      },
    } as IGraphPipelineCommand<NsGraphCmd.GraphZoom.IArgs>,
  ]);
}

/** 没有默认数据的初始化流程 */
export const initGraphCmds = (app: IApplication) =>
  app.executeCommandPipeline([
    /** 1. 从服务端获取数据 */
    {
      commandId: XFlowGraphCommands.LOAD_DATA.id,
      getCommandOption: async () => {
        HooksApi.setGraphDataReady(false);
        return {
          args: {
            loadDataService: HooksApi.loadGraphData(),
          },
        };
      },
    } as IGraphPipelineCommand<NsGraphCmd.GraphLoadData.IArgs>,
     /** 2. 渲染画布 */
    {
      commandId: XFlowGraphCommands.GRAPH_RENDER.id,
      getCommandOption: async (ctx) => {
        const { graphData } = ctx.getResult();
        const nodes = graphData.nodes;
        // 找到所有异常的group
        const groups = graphData.nodes.filter((v) => v.isGroup);
        const groupIds = groups.map(v => v.id);
        // group对应的节点根本不存在
        nodes.filter(v => v.group).forEach(v => {
          if (!groupIds.includes(v.group)) {
            delete v.group;
          }
        })
        return {
          originData: graphData,
          args: {
            graphData: {
              ...graphData,
              nodes,
            },
          },
        };
      },
    } as IGraphPipelineCommand<NsGraphCmd.GraphRender.IArgs>,
    /** 3. 渲染group */
    {
      commandId: CustomCommands.ADD_GROUPS.id,
      getCommandOption: async (ctx) => {
        let count = 0;
        const graphData = await $.getConfig();
        // 需要考虑层级的关系，父节点需要后置，这样才能子节点放到父节点上面
        const groups = graphData.nodes.filter((v) => v.isGroup);
        count = groups.length;
        const groupIds = groups.map(v => v.id);
        console.log("groupxxxx", groups, graphData.nodes);
        const hasPushed = {};
          // 先把所有没有parent的节点入栈，说明他们都是父节点
        const newGroups = groups.filter(v => {
          // 没有group节点，或者说group根本不存在
          return !v?.group || !groupIds.includes(v.group);
        }).map(v => {
          hasPushed[v.id] = 1;
          return v;
        });
        // 然后依次把所有的孩子节点入栈，入栈前提是他们的父亲节点已经在栈内
        while (newGroups.length < groups.length && count) {
          // 用count防止死循环
          count--;
          groups.forEach(v => {
            // 如果父节点在栈内，才入栈
            if (!hasPushed[v.id] && hasPushed[v.group] === 1) {
              hasPushed[v.id] = 1;
              newGroups.unshift(v);
            }
          });
        }
        return {
          args: {
            groups: newGroups,
          },
        };
      },
    },
    /** 4. 缩放画布 */
    {
      commandId: XFlowGraphCommands.GRAPH_ZOOM.id,
      getCommandOption: async (ctx) => {
        const { graph } = ctx;
        // 把所有的groups全部unselect
        const { cells } = ctx.getResult();
        graph.unselect(cells);
        const graphData = await getGraphData();
        $.setOriginData(graphData);
        HooksApi.setGraphDataReady();
        return {
          args: {
            factor: 'real',
            zoomOptions: {
              maxScale: 1.5,
              minScale: 0.05,
            },
          },
        };
      },
    } as IGraphPipelineCommand<NsGraphCmd.GraphZoom.IArgs>,
  ]);

export const initGraphCmdsWithData = (graphData) => (app: IApplication) => {
  app.executeCommandPipeline([
    {
      commandId: XFlowGraphCommands.GRAPH_RENDER.id,
      getCommandOption: async () => {
        HooksApi.setGraphDataReady(false);
        return {
          args: {
            graphData,
          },
        };
      },
    } as IGraphPipelineCommand<NsGraphCmd.GraphRender.IArgs>,
    /** 4. 缩放画布 */
    {
      commandId: XFlowGraphCommands.GRAPH_ZOOM.id,
      getCommandOption: async () => {
        HooksApi.setGraphDataReady();
        return {
          args: {
            factor: 'fit',
          },
        };
      },
    } as IGraphPipelineCommand<NsGraphCmd.GraphZoom.IArgs>,
  ]);
};
