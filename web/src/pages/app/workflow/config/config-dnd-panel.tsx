// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import type { NsNodeCmd } from '@antv/xflow';
import type { NsNodeCollapsePanel } from '@antv/xflow';
import { uuidv4 } from '@antv/xflow';
import { XFlowNodeCommands } from '@antv/xflow';
import { Card, Image, Skeleton } from 'antd';

import { ComponentApi } from '@/api/component';

import { renderDndComponent } from '../react-node/base-node';
import { NODE_CODE } from '@/utils/constant';

export const onNodeDrop: NsNodeCollapsePanel.IOnNodeDrop = async (
  node,
  commands,
  modelService,
) => {
  const args: NsNodeCmd.AddNode.IArgs = {
    nodeConfig: { ...node, id: uuidv4() },
    drag: true,
  };
  commands.executeCommand(XFlowNodeCommands.ADD_NODE.id, args);
};

const NodeDescription = (props: { name: string; description?: string }) => {
  return (
    <Card size="small" title={props.name} style={{ width: '200px' }} bordered={false}>
      {props.description || '这里可以根据服务端返回的数据显示不同的内容'}
    </Card>
  );
};

const DescMap = {
  [NODE_CODE.INPUT_CIO]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59961171587/2165/da9c/2123/7ba8d72bac84dabeb6e88609631ffc66.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#特殊组件---cio输入组件',
    outputs: [
      {
        type: 'any',
        title: 'CIO输出（根据选择的资源池，可以自定义不同的输入参数）',
      }
    ],
  },
  [NODE_CODE.INPUT_VIDEO]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960690627/a0c7/decd/df8b/530b01c3ecab411ac850e95d7bb217e1.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#多媒体组件--视频输入',
    outputs: [
      {
        type: 'Video | VideoNosKey',
        title: '视频输入',
      }
    ],
  },
  [NODE_CODE.INPUT_IMAGE]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960594130/2d94/94ec/22bf/520672261099a8e7d8d875e4fa8ea564.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#多媒体组件--图片输入',
    outputs: [
      {
        type: 'Image | ImageNosKey',
        title: '图片输入',
      }
    ],
  },
  [NODE_CODE.INPUT_AUDIO]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960832856/4f65/08d9/be74/f014fc4edeffdad99d99b08c27142813.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#多媒体组件--音频输入',
    outputs: [
      {
        type: 'Audio | AudioNosKey',
        title: '音频输入',
      }
    ],
  },
  [NODE_CODE.INPUT_TEXT]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400055288/ad7a/bf04/3df9/40bf1f918398d193165c06f5a94046c2.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#基础组件---文本输入',
  },
  [NODE_CODE.INPUT_FLOAT]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960394503/c935/5037/d5d9/7aeaea77885fbdce7daa67e8fcf30ba8.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#基础组件---浮点数输入',
  },
  [NODE_CODE.INPUT_NUMBER]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960394503/c935/5037/d5d9/7aeaea77885fbdce7daa67e8fcf30ba8.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#基础组件---整数输入',
  },
  [NODE_CODE.INPUT_BOOL]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960392715/b1bf/bbaf/08e5/5c87fc865ab68be012b49f08c1beea39.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#基础组件---布尔值输入',
  },
  [NODE_CODE.INPUT_DATE]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960394899/3725/5dcc/5c50/f0f9f04e5ac49769873e85b68259be9e.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#基础组件---日期输入',
  },
  [NODE_CODE.INPUT_ARRAY]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960402516/3d24/2604/7d4c/f43432dddbb505c3ce857992aadf2f11.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#特殊组件---数组输入',
  },
  [NODE_CODE.INPUT_ENUM]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59960489289/5b90/5ac4/9a9a/a4aeb644fff375923d0eba9c558e843e.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#特殊组件---枚举输入',
  },
  [NODE_CODE.INPUT_UPLOAD]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59961075850/e6e2/8db5/d861/77d6a5509b3bb87cbb81a3ba35348c07.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/input#多媒体组件---通用上传组件',
    outputs: [
      {
        type: 'Url | NosKey',
        title: '上传结果',
      }
    ],
  },
  [NODE_CODE.LLM]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59962270112/553b/a8b1/1ab4/981734eea6c1796ee6848ac04eeee895.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/llm#大模型相关组件说明',
  },
  [NODE_CODE.RECOGNIZE]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59962733483/720e/3fca/07ec/ced7f91e6fe82de8cb0f2cf571d521af.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/llm#文本分类对话流',
  },
  [NODE_CODE.PARAMETER_EXTRACT]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59963107508/3bb2/4156/2540/997d8dabf7e8543197386d722344bc81.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/llm#参数提取对话流',
  },
  [NODE_CODE.INTENTION]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59963107508/3bb2/4156/2540/997d8dabf7e8543197386d722344bc81.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin/llm#意图识别对话流',
  },
  [NODE_CODE.INPUT_PARAM]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59965595791/04ec/3b51/baea/34ceb260d3fea36429b55606d9bee284.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.TEMPLATE]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400057230/95d1/95f2/4d49/31417968a80a4910aaf670f97884179c.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.EXTRACT]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400057230/95d1/95f2/4d49/31417968a80a4910aaf670f97884179c.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.TRANSFORM]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400057230/95d1/95f2/4d49/31417968a80a4910aaf670f97884179c.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.TEXT_SPLIT]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400057230/95d1/95f2/4d49/31417968a80a4910aaf670f97884179c.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.JOIN]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400057230/95d1/95f2/4d49/31417968a80a4910aaf670f97884179c.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.MOCK]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400057230/95d1/95f2/4d49/31417968a80a4910aaf670f97884179c.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.JS]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400055566/22a8/1810/2798/ec970ddeb26cf4089aaa11acb87ff0cb.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.PYTHON]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400055566/22a8/1810/2798/ec970ddeb26cf4089aaa11acb87ff0cb.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.SCRIPT]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400055566/22a8/1810/2798/ec970ddeb26cf4089aaa11acb87ff0cb.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.GROOVY]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400055566/22a8/1810/2798/ec970ddeb26cf4089aaa11acb87ff0cb.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#23-脚本组件',
  },
  [NODE_CODE.FOREACH]: {
    imageUrl: 'https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/44400056333/3305/7245/466d/d054a2e137f0c8bc73a8008ee8eb475f.jpg',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#24-逻辑组件',
  },
  [NODE_CODE.SWITCH]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59975407932/7fad/9b3f/d98a/3cba93b7a9e8b02b3e2f79224bd92aba.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#24-逻辑组件',
  },
  [NODE_CODE.SELECT]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59975407932/7fad/9b3f/d98a/3cba93b7a9e8b02b3e2f79224bd92aba.png',
    link: 'https://music-doc.st.netease.com/st/langbase-doc/plugin#24-逻辑组件',
  },
  [NODE_CODE.NEW_KNOWLEDGE]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59976355138/866f/35a2/4f35/af52d59c4daaba04f2ba98682b133803.png'
  },
  [NODE_CODE.TEXT_SHOW]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59976355138/866f/35a2/4f35/af52d59c4daaba04f2ba98682b133803.png'
  },
  [NODE_CODE.UNI_HTTP]: {
    imageUrl: 'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59979170242/2061/b557/bd59/46850202084065bb4587e5a0df1f03fd.png'
  }
  




}

export const nodeDataService: (string) => NsNodeCollapsePanel.INodeDataService = (type: string) => async (
  meta,
  modelService,
) => {
  console.log('nodeService', type, meta, modelService);
  let components: any = [], customComponents: any = [];
  try {
    components = await ComponentApi.list('', '', [type]);
    // customComponents = await ComponentApi.listCustom('', '', [type]);
    // 过滤所有废弃的组件
    components = components.filter((c) => !c.deprecated);
    console.log('components', components);
  } catch (err) {
    window.corona.warn('componentList失败', err);
  }
  const tabs: any = {
    输入: [],
    // 自定义: [],
  };

  // 根据components里的category进行分组
  components.forEach((c) => {
    const { category } = c;
    if (type !== 'agentWorkflow' && category === '输出') {
      return;
    }
    if (!tabs[category]) {
      tabs[category] = [];
    }
    // 说明是自定义插件，需要特殊处理
    let renderCode = c.code;
    tabs[category].push({ ...c.config, componentId: c.id, category, renderCode, name: c.name, description: c.description });
  });

  // customComponents.forEach(c => {
  //   tabs['自定义'].push({...c.config, componentId: 'business', category: '自定义'})
  // })

  // 拓展一些展示
  const newNodeData = Object.keys(tabs).map((tab) => {
    return {
      header: tab,
      id: tab,
      children: tabs[tab].map((child: any) => {
        const { description, inputs, outputs } = child;
        const inputDesc = (DescMap[child.code]?.inputs || inputs || []).map((i: any) => <li>{i.title || i.name}({i.type})</li>);
        const outputDesc = (DescMap[child.code]?.outputs || outputs || []).map((i: any) => <li>{i.title || i.name}({i.type})</li>);
        return ({
          ...child,
          popoverContent: <div style={{ maxWidth: 300 }}>
            {child.description || ''}
            {DescMap[child.code]?.imageUrl && (
              <div style={{ marginTop: 10, minWidth: 300, minHeight: 100 }}>
                <Image src={DescMap[child.code]?.imageUrl} alt="组件示例" style={{ width: '100%' }}
                  placeholder={
                    <Skeleton.Image active={true} style={{ width: 300, height: 100 }}/>
                  }
                />
              </div>
            )}
            {/* {child.code} */}
            {inputDesc.length > 0 && (
              <div style={{ marginTop: 10 }}>
                【输入】
                <ul>{inputDesc}</ul>
              </div>
            )}
            {outputDesc.length > 0 && (
              <div style={{ marginTop: 10 }}>
                【输出】
                <ul>{outputDesc}</ul>
              </div>
            )}
            {DescMap[child.code]?.link && (
              <div>
                <a target="_blank" href={DescMap[child.code]?.link}>查看详情</a>
              </div>
            )}
          </div>,
          renderComponent: renderDndComponent,
        })
      }),
    };
  });
  console.log('tabs...', tabs, newNodeData);
  return newNodeData;
};

export const searchService: NsNodeCollapsePanel.ISearchService = async (
  nodes: NsNodeCollapsePanel.IPanelNode[] = [],
  keyword: string,
) => {
  const list = nodes.filter((node) => node.name.toLowerCase().includes(keyword.toLowerCase()));
  console.log('searchService', list, keyword, nodes);
  return list;
};
