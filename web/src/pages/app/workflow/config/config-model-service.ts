import type { IModelService } from '@antv/xflow';
import { createModelServiceConfig } from '@antv/xflow';

import { IRunState } from '@/interface';

export const CUSTOM_STATE = 'custom';
export const RUN_STATE = 'run-state';

interface ICustomMState {
  loading: boolean;
  preview: boolean;
  canIntoPlayground: boolean;
  showSchema: boolean;
}

export const useModelServiceConfig = (preview) =>
  createModelServiceConfig((config) => {
    config.registerModel((registry) => {
      registry.registerModel({
        id: CUSTOM_STATE,
        getInitialValue: (): ICustomMState => ({
          loading: true,
          preview,
          canIntoPlayground: false,
          showSchema: false,
        }),
      });
      return registry.registerModel({
        id: RUN_STATE,
        getInitialValue: (): IRunState => ({
          message: '',
          inputs: [],
          outputs: [],
          status: 'queued',
          nodes: [],
        }),
      });
    });
  });

export const getCustomState = async (contextService: IModelService) => {
  const ctx = await contextService.awaitModel<ICustomMState>(CUSTOM_STATE);
  return ctx.getValidValue();
};

export const getCustomModal = async (contextService: IModelService) => {
  return await contextService.awaitModel<ICustomMState>(CUSTOM_STATE);
};

export const getRunState = async (contextService: IModelService) => {
  const ctx = await contextService.awaitModel<IRunState>(RUN_STATE);
  return ctx.getValidValue();
};

export const getRunModal = async (contextService: IModelService) => {
  return await contextService.awaitModel<IRunState>(RUN_STATE);
};
