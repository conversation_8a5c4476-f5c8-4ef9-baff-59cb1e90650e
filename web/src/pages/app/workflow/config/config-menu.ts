// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import type {
  IMenuOptions,
  NsEdgeCmd,
  NsGraph,
  NsGraphCmd,
  NsNodeCmd,
} from '@antv/xflow';
import { createCtxMenuConfig, MenuItemType, XFlowGraphCommands } from '@antv/xflow';
import { XFlowEdgeCommands, XFlowNodeCommands } from '@antv/xflow';

import { NODE_CODE, NODE_TYPE } from '@/utils/constant';

import type { NsRenameNodeCmd } from '../cmd-extensions/cmd-rename-node-modal';
import * as CustomCommands from '../cmd-extensions/constants';
import { renameNode } from './cmds/hooks-services';

export const DELETE_EDGE: IMenuOptions = {
  id: XFlowEdgeCommands.DEL_EDGE.id,
  label: '删除边',
  iconName: 'DeleteOutlined',
  onClick: async ({ target, commandService }) => {
    commandService.executeCommand<NsEdgeCmd.DelEdge.IArgs>(
      XFlowEdgeCommands.DEL_EDGE.id,
      {
        edgeConfig: target.data as NsGraph.IEdgeConfig,
      },
    );
  },
};

export const EXPORT_NODE: IMenuOptions = {
  id: 'export_plugin',
  label: '导出自定义插件',
  iconName: 'ExportOutlined',
  onClick: async ({ target, commandService }) => {
    commandService.executeCommand(CustomCommands.SHOW_EXPORT_MODAL.id, {
      nodeConfig: target.data,
    }).then((res) => {
      console.log('res', res);
    });
  },
};


export const DELETE_NODE: IMenuOptions = {
  id: XFlowNodeCommands.DEL_NODE.id,
  label: '删除节点',
  iconName: 'DeleteOutlined',
  onClick: async ({ target, commandService }) => {
    commandService.executeCommand<NsNodeCmd.DelNode.IArgs>(
      XFlowNodeCommands.DEL_NODE.id,
      {
        nodeConfig: { id: target?.data?.id || '' },
      },
    );
  },
};

export const EMPTY_MENU: IMenuOptions = {
  id: 'EMPTY_MENU_ITEM',
  label: '暂无可用',
  isEnabled: false,
  iconName: 'DeleteOutlined',
};

export const COPY_NODE: IMenuOptions = {
  id: CustomCommands.COPY_NODE.id,
  label: '复制节点',
  isVisible: true,
  iconName: 'CopyOutlined',
  onClick: async ({ target, commandService }) => {
    const args: NsNodeCmd.SelectNode.IArgs = {
      nodeIds: [target?.data?.id],
      resetSelection: true,
    };
    commandService.executeCommand<NsNodeCmd.SelectNode.IArgs>(
      XFlowNodeCommands.SELECT_NODE.id,
      args,
    );
    commandService.executeCommand<NsGraphCmd.GraphCopySelection.IArgs>(
      XFlowGraphCommands.GRAPH_COPY.id,
      {},
    );
    setTimeout(() => {
      commandService.executeCommand<NsGraphCmd.GraphPasteSelection.IArgs>(
        XFlowGraphCommands.GRAPH_PASTE.id,
        {},
      );
    }, 0);
  },
};

export const RENAME_NODE: IMenuOptions = {
  id: CustomCommands.SHOW_RENAME_MODAL.id,
  label: '重命名节点',
  isVisible: true,
  iconName: 'EditOutlined',
  onClick: async ({ target, commandService }) => {
    const nodeConfig = target.data as NsGraph.INodeConfig;
    commandService.executeCommand<NsRenameNodeCmd.IArgs>(
      CustomCommands.SHOW_RENAME_MODAL.id,
      {
        nodeConfig,
        updateNodeNameService: renameNode,
      },
    );
  },
};

export const SEPARATOR: IMenuOptions = {
  id: 'separator',
  type: MenuItemType.Separator,
};

const showExportNode = (target) => {
  // 目前只支持子工作流，CIO，HTTP，大模型，脚本节点
  const whiteList = [NODE_CODE.CIO, NODE_CODE.SCRIPT, NODE_CODE.LLM, NODE_CODE.PYTHON, NODE_CODE.JS, NODE_CODE.UNI_HTTP, NODE_CODE.SUB_WORKFLOW];
  const code = target?.cell?.data?.renderCode;
  return whiteList.includes(code);
}


export const useMenuConfig = createCtxMenuConfig((config) => {
  config.setMenuModelService(async (target, model, modelService, toDispose) => {
    const { type } = target as any;
    console.log('type', type, target);
    let submenu = [DELETE_NODE, RENAME_NODE, COPY_NODE];
    if (type === 'node' && target.cell.data) {
      const nodeType = target?.cell.data.type;
      if (showExportNode(target)) {
        submenu.push(EXPORT_NODE);
      }
      if (nodeType === NODE_TYPE.OUTPUT) {
        submenu = [EMPTY_MENU];
      }
      if (target?.cell.data.fixed) {
        submenu = [EMPTY_MENU];
      }
    }
    switch (type) {
      /** 节点菜单 */
      case 'node':
        model.setValue({
          id: 'root',
          type: MenuItemType.Root,
          submenu,
        });
        break;
      /** 边菜单 */
      case 'edge':
        model.setValue({
          id: 'root',
          type: MenuItemType.Root,
          submenu: [DELETE_EDGE],
        });
        break;
      /** 画布菜单 */
      case 'blank':
        model.setValue({
          id: 'root',
          type: MenuItemType.Root,
          submenu: [EMPTY_MENU],
        });
        break;
      /** 默认菜单 */
      default:
        model.setValue({
          id: 'root',
          type: MenuItemType.Root,
          submenu: [EMPTY_MENU],
        });
        break;
    }
  });
});
