// @ts-nocheck
/* eslint-disable no-promise-executor-return */
import type { NsGraph } from '@antv/xflow';
import { MODELS, NsJsonSchemaForm } from '@antv/xflow';
import { getOriginType, NODE_CODE, NODE_TYPE } from '@utils/constant';
import { updateEdge, updateNode } from '@utils/node';

import { getCustomModal } from './config-model-service';
import { controlMapService } from './schema-panel';
import configMap from './schema-panel/config-schemas';
import { formatScript } from '@/pages/app/workflow/config/schema-panel/formValueFormat/formatScript';
import { formatHttp } from './schema-panel/formValueFormat/formatHttp';
import { formatTypeValue } from '@/utils/common';
import { formatIO } from './schema-panel/formValueFormat/formatio';

export function delay(ms: number) {
  return new Promise((resolve) =>
    setTimeout(() => {
      resolve(true);
    }, ms),
  );
}

const i = 0;
export const formSchemaService: NsJsonSchemaForm.IFormSchemaService = async (args) => {
  // const { ControlShape } = NsJsonSchemaForm;
  const { targetData, modelService, targetType, graph } = args;
  /** 可以使用获取 graphMeta */
  const graphMeta = await MODELS.GRAPH_META.useValue(modelService);
  const custom = await getCustomModal(modelService);
  const value = await custom.getValidValue();
  if (targetType === 'canvas') {
    custom.setValue({
      ...value,
      showSchema: false,
    });

    return {
      tabs: [
        {
          name: '项目配置',
          groups: [],
        },
      ],
    };
  }
  custom.setValue({
    ...value,
    showSchema: true,
  });

  await delay(100);
  console.log('target', targetData);
  targetData.disabled = !!value.preview;

  if (configMap[targetData?.type || NODE_TYPE.ATOMIC]) {
    return configMap[targetData?.type || NODE_TYPE.ATOMIC](targetData, graph);
  }

  return {
    tabs: [],
  };
};

/** 保存form的values */
export const formValueUpdateService: NsJsonSchemaForm.IFormValueUpdateService = async (
  args,
) => {
  const { values, commandService, modelService } = args;
  const node = await MODELS.SELECTED_CELL.useValue(modelService);
  if (!node) {
    return;
  }
  // 验证失败都返回
  if (values[0].validating || values[0].errors?.length) {
    return;
  }
  let data: any = {
    ...node.data,
  };
  const valMap: any = {};
  let errors = [];
  values.forEach((val) => {
    if (val.errors) {
      errors = val.errors
    };
    const name = String(val.name);
    const value = val.value;
    valMap[name] = value;
  });
  // if (errors.length) {
  //   return;
  // }

  // 从 vars 中替换对应的值
  data.vars = (data.vars || []).map((v) => {
    let value = typeof valMap[v.name] !== 'undefined' ? valMap[v.name] : v.value;
    value = formatTypeValue(value, v.type);
    return {
      ...v,
      value,
    };
  });
  if (valMap.inputs) {
    data.inputs = valMap.inputs;
  }
  if (valMap.outputs) {
    data.outputs = valMap.outputs;
  }
  if (valMap.configs) {
    data.configs = valMap.configs;
  }
  if (valMap.data) {
    data = { ...data, ...valMap.data };
  }
  if (valMap.name) {
    data.name = valMap.name;
  }

  /**
   * 特殊处理
   */

  // 如果是 Switch 需要把类型改成匹配的类型
  if (data.type === NODE_TYPE.SWITCH) {
    // switch的数据需要从condition中获取
    if (!valMap.conditions) {
      return;
    }
    const { type, outputs } = valMap.conditions;
    data.vars = data.vars && data.vars[0] ? data.vars : [{ value: 'boolean', name: 'conditions', type: 'boolean' }];
    data.vars[0].value = type;
    data.vars[0].type = type;
    data.vars[0].name = 'conditions';
    // 修改输入输出的 type
    data.inputs = data.inputs.map((v) => ({ ...v, type }));
    data.outputs = outputs.map((v) => ({ ...v, type: '_logic_' }));
  }

  // 如果是 Select 需要 同步inputs和outputs
  if (data.type === NODE_TYPE.SELECT || data.code === NODE_CODE.MOCK) {
    // select 的数据需要从 selections 中获取
    data.outputs = data.inputs;
  }

  // 如果是Script相关的组件
  formatScript(data, valMap);
  // 如果是http相关组件
  formatHttp(data, valMap);
  // 如果是输入输出相关组件
  formatIO(data, valMap)

  if (args.targetType === 'edge') {
    updateEdge(data as NsGraph.IEdgeConfig, commandService);
  } else {
    updateNode(data as NsGraph.INodeConfig, commandService);
  }
};

export { controlMapService };


