import type { IToolbarItemOptions } from '@antv/xflow';
import type { IModelService } from '@antv/xflow';
import type { NsGraphCmd } from '@antv/xflow';
import { createToolbarConfig } from '@antv/xflow';
import { MODELS, NsGraphStatusCommand, XFlowGraphCommands } from '@antv/xflow';

import { getAppId } from '@/utils/state';

import type { NsDeployDagCmd } from '../cmd-extensions/cmd-deploy';
import * as CustomCommands from '../cmd-extensions/constants';
import * as MockApi from './cmds/hooks-services';
import { getCustomModal, getCustomState } from './config-model-service';
import Deploy from '../components/deploy-btn';
import Menu from './menu-btn';
import Save from './save-btn';
import TitleBtn from './title-btn';

/** toolbar依赖的状态 */
export interface IToolbarState {
  isMultiSelectionActive: boolean;
  isNodeSelected: boolean;
  isGroupSelected: boolean;
  canIntoPlayground: boolean;
  isProcessing: boolean;
  isPreview: boolean;
}

export const getDependencies = async (modelService: IModelService) => {
  return [
    await MODELS.SELECTED_CELLS.getModel(modelService),
    await MODELS.GRAPH_ENABLE_MULTI_SELECT.getModel(modelService),
    await NsGraphStatusCommand.MODEL.getModel(modelService),
    await getCustomModal(modelService),
  ];
};

/** toolbar依赖的状态 */
export const getToolbarState = async (modelService: IModelService) => {
  // isMultiSelectionActive
  const { isEnable: isMultiSelectionActive } =
    await MODELS.GRAPH_ENABLE_MULTI_SELECT.useValue(modelService);
  // isGroupSelected
  const isGroupSelected = await MODELS.IS_GROUP_SELECTED.useValue(modelService);
  // isNormalNodesSelected: node不能是GroupNode
  const isNormalNodesSelected =
    await MODELS.IS_NORMAL_NODES_SELECTED.useValue(modelService);
  // statusInfo
  const statusInfo = await NsGraphStatusCommand.MODEL.useValue(modelService);
  const customState = await getCustomState(modelService);

  return {
    isNodeSelected: isNormalNodesSelected,
    isGroupSelected,
    isMultiSelectionActive,
    isPreview: customState.preview,
    canIntoPlayground: customState.canIntoPlayground,
    isProcessing: statusInfo.graphStatus === NsGraphStatusCommand.StatusEnum.PROCESSING,
  } as IToolbarState;
};

export const getExtraToolbarItems = async (state: IToolbarState) => {
  const toolbarGroup1: IToolbarItemOptions[] = [];
  toolbarGroup1.push({
    id: XFlowGraphCommands.GRAPH_TOGGLE_MULTI_SELECT.id,
    tooltip: '开启框选',
    text: '框选',
    iconName: 'GatewayOutlined',
    active: state.isMultiSelectionActive,
    onClick: async ({ commandService }) => {
      commandService.executeCommand<NsGraphCmd.GraphToggleMultiSelect.IArgs>(
        XFlowGraphCommands.GRAPH_TOGGLE_MULTI_SELECT.id,
        {},
      );
    },
  });
  toolbarGroup1.push({
    id: XFlowGraphCommands.SAVE_GRAPH_DATA.id,
    iconName: 'SaveOutlined',
    text: '保存',
    render: Save,
    onClick: async ({ commandService }) => {
      commandService.executeCommand<NsGraphCmd.SaveGraphData.IArgs>(
        XFlowGraphCommands.SAVE_GRAPH_DATA.id,
        {
          saveGraphDataService: (meta, graphData) =>
            MockApi.saveGraphData(meta, graphData),
        },
      );
    },
  });
  // toolbarGroup1.push({
  //   tooltip: state.isPreview ? '退出预览' : '预览',
  //   iconName: state.isPreview ? 'EyeInvisibleOutlined' : 'EyeOutlined',
  //   isEnabled: true,
  //   id: CustomCommands.PREVIEW_CMD.id,
  //   onClick: ({ commandService }) => {
  //     // 如果是进入预览模式,先执行debug，保存一下当前的图数据
  //     if (state.isPreview) {
  //       // 暂停服务
  //       commandService.executeCommand(XFlowDagCommands.QUERY_GRAPH_STATUS.id, {
  //         graphStatusService: MockApi.stopGraphStatusService,
  //         loopInterval: 1000,
  //       });
  //     }
  //     commandService.executeCommand<NsPreviewDagCmd.IArgs>(
  //       CustomCommands.PREVIEW_CMD.id,
  //       {
  //         intoPreview: !state.isPreview,
  //       },
  //     );
  //   },
  // });
  /** 部署服务按钮 */

  toolbarGroup1.push({
    iconName: 'CloudSyncOutlined',
    text: '发布',
    tooltip: '部署服务',
    id: CustomCommands.DEPLOY_SERVICE.id,
    render: Deploy,
    onClick: ({ commandService }) => {
      commandService.executeCommand<NsDeployDagCmd.IArgs>(
        CustomCommands.DEPLOY_SERVICE.id,
        {
          deployDagService: (meta, graphData) =>
            MockApi.deployDagService(meta, graphData),
        },
      );
    },
  });
  return [{ name: 'extra', items: toolbarGroup1 }];
};

export const getToolbarItems = async (state: IToolbarState) => {
  const toolbarGroup1: IToolbarItemOptions[] = [];
  const toolbarGroup2: IToolbarItemOptions[] = [];
  /** 保存数据 */

  toolbarGroup1.push({
    id: 'menu',
    render: Menu,
  });
  toolbarGroup1.push({
    id: 'title',
    render: TitleBtn,
  });
  toolbarGroup1.push({
    id: 'playground',
    tooltip: '预览上次发布成功的工作流',
    text: '预览',
    iconName: 'EyeOutlined',
    isEnabled: state.canIntoPlayground,
    onClick: async () => {
      window.open(`/preview/workflow-playground?appId=${getAppId()}`, 'blank');
    },
  });

  // // /** 新建群组 */
  // toolbarGroup2.push({
  //   id: XFlowGroupCommands.ADD_GROUP.id,
  //   tooltip: '新建群组',
  //   iconName: 'GroupOutlined',
  //   isEnabled: state.isNodeSelected,
  //   onClick: async ({ commandService, modelService }) => {
  //     const cells = await MODELS.SELECTED_CELLS.useValue(modelService);
  //     const groupChildren = cells.map((cell) => cell.id);
  //     commandService.executeCommand<NsGroupCmd.AddGroup.IArgs>(
  //       XFlowGroupCommands.ADD_GROUP.id,
  //       {
  //         nodeConfig: {
  //           id: uuidv4(),
  //           renderKey: GROUP_NODE_RENDER_ID,
  //           type: GROUP_NODE_RENDER_ID,
  //           groupChildren,
  //           groupCollapsedSize: { width: 200, height: 100 },
  //           inputs: [],
  //           outputs: [],
  //           configs: [],
  //           name: '迭代器',
  //         },
  //       },
  //     );
  //   },
  // });
  // /** 解散群组 */
  // toolbarGroup2.push({
  //   id: XFlowGroupCommands.DEL_GROUP.id,
  //   tooltip: '解散群组',
  //   iconName: 'UngroupOutlined',
  //   isEnabled: state.isGroupSelected,
  //   onClick: async ({ commandService, modelService }) => {
  //     const cell = await MODELS.SELECTED_NODE.useValue(modelService);
  //     const nodeConfig = cell.getData();
  //     commandService.executeCommand<NsGroupCmd.AddGroup.IArgs>(
  //       XFlowGroupCommands.DEL_GROUP.id,
  //       {
  //         nodeConfig: nodeConfig,
  //       },
  //     );
  //   },
  // });

  return [
    { name: 'graphData', items: toolbarGroup1 },
    { name: 'groupOperations', items: toolbarGroup2 },
  ];
};

/** toolbar依赖的状态 */
export const useToolbarConfig = createToolbarConfig((toolbarConfig) => {
  /** 生产 toolbar item */
  toolbarConfig.setToolbarModelService(async (toolbarModel, modelService, toDispose) => {
    const updateToolbarModel = async () => {
      const state = await getToolbarState(modelService);
      const toolbarItems = await getToolbarItems(state);
      const extraBarItems = await getExtraToolbarItems(state);
      toolbarModel.setValue((toolbar) => {
        toolbar.layout = 'horizontal';
        toolbar.mainGroups = toolbarItems;
        toolbar.extraGroups = extraBarItems;
      });
    };
    const models = await getDependencies(modelService);
    const subscriptions = models.map((model) => {
      return model.watch(async () => {
        updateToolbarModel();
      });
    });
    toDispose.pushAll(subscriptions);
  });
});
