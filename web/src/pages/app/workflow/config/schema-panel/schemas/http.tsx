// @ts-nocheck
/* eslint-disable no-promise-executor-return */
import { NsJsonSchemaForm } from '@antv/xflow';
import { NODE_CODE, NODE_TYPE } from '@utils/constant';
import { PanelTitle } from '../switch-case';

import { parsePortId } from '@/utils/node';

import { ControlShapeEnum } from '../index';
import { getTotalConfig, typeSchema } from './base';
import { scriptSchemaGen } from './script';
import { Label } from '@/components/label';

/**
 * cioLoader
 * @param props
 * @returns
 */
const cioSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  // 固定顺序
  let totalOutput = props.outputs.map(v => typeSchema(v, props.disabled)).sort((a, b) => {
    return a.name.localeCompare(b.name)
  });
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: '输入',
        groups: [
          {
            tooltip: '',
            controls: [
              {
                label: 'CIO配置',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.CIO_LOADER,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },

        ],
      },
      {
        name: '输出',
        groups: [
          {
            controls: [
              {
                name: 'fallbackConfig',
                value: { totalConfig: totalOutput, fallbackOut: props.fallbackOut, id: props.id },
                shape: ControlShapeEnum.OUTPUT
              }]
          }
        ],
      },
    ],
  };
};

/**
* cioLoader
* @param props
* @returns
*/
const airshipSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  // 固定顺序
  let totalOutput = props.outputs.map(v => typeSchema(v, props.disabled)).sort((a, b) => {
    return a.name.localeCompare(b.name)
  });
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: '输入',
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '算法选择',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.AIR_SHIP,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
      {
        name: '输出',
        groups: [
          {
            controls: [
              {
                name: 'fallbackConfig',
                value: { totalConfig: totalOutput, fallbackOut: props.fallbackOut, id: props.id },
                shape: ControlShapeEnum.OUTPUT
              }]
          }
        ],
      },
    ],
  };
};

const skyeyeSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  // 固定顺序
  let totalOutput = props.outputs.map(v => typeSchema(v, props.disabled)).sort((a, b) => {
    return a.name.localeCompare(b.name)
  });
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: '输入',
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                disabled: props.disabled,
                shape: ControlShapeEnum.SKY_EYE,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
      {
        name: '输出',
        groups: [
          {
            controls: [
              {
                name: 'fallbackConfig',
                value: { totalConfig: totalOutput, fallbackOut: props.fallbackOut, id: props.id },
                shape: ControlShapeEnum.OUTPUT
              }]
          }
        ],
      },
    ],
  };
};

const NewKnowledgeSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '知识库',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.NEW_KNOWLEDGE,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
    ],
  };
};


const knowledgeSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '知识库',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.KNOWLEDGE,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
    ],
  };
};

const mockSchemaGen = (props): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '类型',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM_ONE,
                originData: { nameDisabled: true },
                value: props.inputs,
                name: 'inputs',
              },
            ],
          },
        ],
      },
    ],
  };
};

const universalHttpGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  // 查询所有的已经链接的inputs
  const cell = graph.getCellById(props.id);
  const edges = graph.getIncomingEdges(cell);
  const portIds = edges?.map((v) => parsePortId(v.data.targetPortId).portId);
  const httpWhiteList = ['headers', 'url', 'timeout', 'method', 'successExp'];
  // 拿到所有的inputs和vars
  const { vars, inputs } = props;
  const findSuccess = vars.find((v) => v.name === 'successExp');
  const findUrl = vars.find((v) => v.name === 'url');
  if (!findSuccess) {
    vars.push({
      name: 'successExp',
      type: 'string',
      title: '成功条件(el表达式)',
      value: '#code==200'
    })
  }
  if (!findUrl) {
    const curUrl = props.configs.find((v) => v.name === 'url');
    vars.push({
      name: 'url',
      type: 'string',
      title: '请求地址',
      value: curUrl?.value || 'http://xxx',
    })
  }
  const newInputs = inputs.map((v) => ({ ...v, disabled: portIds?.includes(v.id) }));
  const varConfig = (vars || []).map((v) => typeSchema(v, props.disabled));
  const inputConfig = (newInputs || []).map((v) =>
    typeSchema(v, v.disabled || props.disabled),
  );
  // 固定顺序
  const totalConfig = varConfig.concat(inputConfig).filter(v => httpWhiteList.includes(v.name)).sort((a, b) => {
    console.log('a', a, b);
    return a.name.localeCompare(b.name)
  });
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: `输入`,
        groups: [
          {
            controls: [
              {
                label: '输入',
                name: 'inputs',
                disabled: props.disabled,
                value: props.inputs.filter(v => !httpWhiteList.includes(v.name)),
                shape: ControlShapeEnum.PARAM_WITH_DETAIL,
              },
            ],
          }
        ],
      },

      {
        name: `输出`,
        groups: [
          {
            controls: [
              {
                label: '输出',
                name: 'outputs',
                disabled: props.disabled,
                value: props.outputs,
                shape: ControlShapeEnum.PARAM_WITH_DETAIL,
              },
            ],
          },
        ],
      },
      {
        name: `配置`,
        groups: [
          {
            name: '配置',
            tooltip: '',
            controls: totalConfig,
          }
        ],
      },
    ],
  };

}

/**
 * 子应用流
 * @param props 
 * @param graph 
 * @returns 
 */
const subCompletionSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  const totalConfig = getTotalConfig(props, graph, { });
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '配置',
            tooltip: '',
            // controls: totalConfig,
            controls: [
              {
                Label: '选择应用',
                name: 'sub_app',
                value: { totalConfig, outputs: props.outputs, name: props.name },
                shape: ControlShapeEnum.SUB_COMPLETION
              }
            ]
          }
        ],
      },
    ],
  };
}


/**
 * 子对话流
 * @param props 
 * @param graph 
 * @returns 
 */
const subChatflowSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  const totalConfig = getTotalConfig(props, graph, { });
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '配置',
            tooltip: '',
            // controls: totalConfig,
            controls: [
              {
                Label: '选择工作流',
                name: 'sub_app',
                value: { totalConfig, outputs: props.outputs, name: props.name },
                shape: ControlShapeEnum.SUB_CHATFLOW
              }
            ]
          }
        ],
      },
    ],
  };
}


/**
 * 子工作流
 * @param props 
 * @param graph 
 * @returns 
 */
const subWorkflowSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  const totalConfig = getTotalConfig(props, graph, { exclude: ['oneWay', 'workflowId'] });
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '配置',
            tooltip: '',
            // controls: totalConfig,
            controls: [
              {
                Label: '选择工作流',
                name: 'workflow',
                value: { appId: props.appId, totalConfig, id: props.id },
                shape: ControlShapeEnum.SUB_WORKFLOW
              }
            ]
          }
        ],
      },
    ],
  };
}

const aiMvSchemaGen = (totalConfig: any, props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  props.originValue = props.originValue || {};
  props.originValue.appId = props.appId;
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            controls: [
              {
                name: 'aiMvConfig',
                value: { totalConfig, originValue: props.originValue },
                shape: ControlShapeEnum.AI_MV
              }]
          }
        ],
      },
    ],
  };
};

const llmSchemaGen = (totalConfig: any, totalOutput: any, props: any): NsJsonSchemaForm.ISchema => {
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: '输入',
        groups: [
          {
            tooltip: '',
            controls: [
              {
                Label: '模型设置',
                name: 'totalConfig',
                value: totalConfig,
                shape: ControlShapeEnum.MODEL_SETTING
              }
            ]
          }
        ],
      },
      {
        name: '输出',
        groups: [
          {
            controls: [
              {
                name: 'fallbackConfig',
                value: { totalConfig: totalOutput, fallbackOut: props.fallbackOut, id: props.id },
                shape: ControlShapeEnum.OUTPUT
              }]
          }
        ],
      },
    ],
  };
};

export const httpSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  // 查询所有的已经链接的inputs
  const cell = graph.getCellById(props.id);
  const edges = graph.getIncomingEdges(cell);
  const portIds = edges?.map((v) => parsePortId(v.data.targetPortId).portId);
  // 拿到所有的inputs和vars
  const { vars, inputs, outputs } = props;
  const newInputs = inputs.map((v) => ({ ...v, disabled: portIds?.includes(v.id) }));
  const varConfig = (vars || []).map((v) => typeSchema(v, props.disabled));
  const inputConfig = (newInputs || []).map((v) =>
    typeSchema(v, v.disabled || props.disabled),
  );
  // 固定顺序
  let totalConfig = varConfig.concat(inputConfig).sort((a, b) => {
    return a.name.localeCompare(b.name)
  });
  // 固定顺序
  let totalOutput = outputs.map(v => typeSchema(v, props.disabled)).sort((a, b) => {
    return a.name.localeCompare(b.name)
  });
  // 过滤掉大模型自动填充数据
  // if (props.type === NODE_TYPE.LLM) {
  //   totalConfig = totalConfig.filter(v => !["providerKind"].includes(v.name));
  // }
  const groups = [];
  groups.push({
    name: '配置',
    tooltip: '',
    controls: totalConfig,
  });

  if (props.renderCode === NODE_CODE.AI_MV) {
    return aiMvSchemaGen(totalConfig, props, graph);
  }
  if (props.code === NODE_CODE.SUB_WORKFLOW) {
    return subWorkflowSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.SUB_CHATFLOW) {
    return subChatflowSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.SUB_COMPLETION) {
    return subCompletionSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.CIO) {
    return cioSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.MOCK) {
    return mockSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.UNI_HTTP) {
    return universalHttpGen(props, graph);
  }
  if ([NODE_CODE.PYTHON, NODE_CODE.JS].includes(props.code)) {
    return scriptSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.AIR_SHIP) {
    return airshipSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.SKY_EYE) {
    return skyeyeSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.KNOWLEDGE) {
    return knowledgeSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.NEW_KNOWLEDGE) {
    return NewKnowledgeSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.LLM) {
    return llmSchemaGen(totalConfig, totalOutput, props, graph);
  }
  console.log("props", props.renderCode, props);
  if (props.renderCode === NODE_CODE.AI_MV) {
    return aiMvSchemaGen(props, graph);
  }
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: `输入`,
        groups: [
          {
            controls: totalConfig,
          }
        ],
      },
      {
        name: `输出`,
        groups: [
          {
            controls: [
              {
                name: 'fallbackConfig',
                value: { totalConfig: totalOutput, fallbackOut: props.fallbackOut, id: props.id },
                shape: ControlShapeEnum.OUTPUT
              }]
          }
        ],
      },
      // {
      //   name: <PanelTitle {...props} />,
      //   groups,
      // },
    ],
  };
};
