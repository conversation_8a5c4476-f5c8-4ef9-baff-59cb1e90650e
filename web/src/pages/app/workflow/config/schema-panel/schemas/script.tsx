// @ts-nocheck
/* eslint-disable no-promise-executor-return */
import type { NsGraph } from '@antv/xflow';
import { NsJsonSchemaForm } from '@antv/xflow';
import { NODE_CODE, NODE_TYPE, objectTypes, transTypes } from '@utils/constant';

import { parsePortId } from '@/utils/node';

import { ControlShapeEnum } from '../index';
import { typeSchema } from './base';
import { IconFont } from '@/components/icons';
import { PanelTitle } from '../switch-case';

const transformSchemaGen = (props): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '输入',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM_ONE,
                options: transTypes,
                originData: { nameDisabled: true },
                value: props.inputs,
                name: 'inputs',
              },
              {
                label: '输出',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM_ONE,
                originData: { nameDisabled: true },
                options: transTypes,
                name: 'outputs',
                value: props.outputs,
              },
            ],
          },
        ],
      },
    ],
  };
};

const joinSchemaGen = (props): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '输入',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM,
                name: 'inputs',
                value: props.inputs,
              },
              {
                label: '输出',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM_ONE,
                options: objectTypes,
                originData: { nameDisabled: true, typeDisabled: true },
                value: props.outputs,
                name: 'outputs',
              },
            ],
          },
        ],
      },
    ],
  };
};


const extractSchemaGen = (props): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '输入',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM_ONE,
                options: objectTypes,
                originData: { nameDisabled: true, typeDisabled: true },
                value: props.inputs,
                name: 'inputs',
              },
              {
                label: '待提取的参数（参数名需要匹配）',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM,
                name: 'outputs',
                value: props.outputs,
              },
            ],
          },
        ],
      },
    ],
  };
};

const splitSchemaGen = (props): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数',
            tooltip: '',
            controls: [
              {
                label: '分割符号',
                disabled: props.disabled,
                required: true,
                shape: 'input',
                value: props.configs[0].value,
                name: 'splitter',
              },
            ],
          },
        ],
      },
    ],
  };
};

const templateSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  const cell = graph.getCellById(props.id);
  const edges = graph.getIncomingEdges(cell);
  const portIds = edges?.map((v) => parsePortId(v.data.targetPortId).portId);
  // 拿到所有的inputs和vars
  const { vars, inputs } = props;
  const template = [...vars, ...inputs].find((v) => v.name === 'template');
  const disableTemplate = (portIds || []).includes(template.id);
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数',
            tooltip: '',
            controls: [
              {
                label: '模板',
                disabled: disableTemplate,
                tooltip: '可以使用{}语法定义变量，把内容删除可以从外部输入模板',
                required: true,
                shape: 'textarea',
                options: transTypes,
                value: template.value,
                name: 'template',
              },
              {
                label: '输入',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM_WITH_DETAIL,
                originData: { typeDisabled: true },
                options: transTypes,
                value: props.inputs.filter(v => v.name !== 'template'),
                name: 'inputs',
              },
              {
                label: '输出',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAM_ONE,
                originData: { nameDisabled: true, typeDisabled: true },
                options: transTypes.filter((v) => !v.label.includes('Nos')),
                name: 'outputs',
                value: props.outputs,
              },
            ],
          },
        ],
      },
    ],
  };
};


/**
 * 脚本schema
 * @param props
 * @param graph
 * @returns
 */
export const scriptSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {

  if (props.code === NODE_CODE.TRANSFORM) {
    return transformSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.EXTRACT) {
    return extractSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.TEMPLATE || props.type === NODE_TYPE.DISPLAY) {
    return templateSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.JOIN) {
    return joinSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.TEXT_SPLIT) {
    return splitSchemaGen(props, graph);
  }
  const scriptType = (props.vars || []).find(v => v.name === 'scriptType');
  const scriptConfig = (props.vars || [])
    .filter((v) => v.name !== 'scriptType')
    .map((v) => typeSchema(v, props.disabled, { scriptType: scriptType?.value, nodeId: props.id, title: props.name }));
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '脚本配置',
            tooltip: '123',
            disabled: props.disabled,
            controls: scriptConfig,
          },
          {
            name: '脚本配置',
            controls: [
              {
                label: '输入',
                name: 'inputs',
                disabled: props.disabled,
                value: props.inputs,
                originData: {
                  useValue: '作为调试输入',
                },
                shape: ControlShapeEnum.PARAM_WITH_DETAIL,
              },
              {
                label: '输出',
                name: 'outputs',
                disabled: props.disabled,
                value: props.outputs,
                shape: ControlShapeEnum.PARAM_WITH_DETAIL,
              },
            ],
          },
        ],
      },
    ],
  };
};
