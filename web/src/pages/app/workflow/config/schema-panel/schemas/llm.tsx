// @ts-nocheck
/* eslint-disable no-promise-executor-return */
import { NsJsonSchemaForm } from '@antv/xflow';
import { NODE_CODE, NODE_TYPE } from '@utils/constant';
import { PanelTitle } from '../switch-case';

import { parsePortId } from '@/utils/node';

import { ControlShapeEnum } from '../index';
import { getTotalConfig, typeSchema } from './base';
import { scriptSchemaGen } from './script';
import { Label } from '@/components/label';

/**
 * 新版知识库
 * @param props 
 * @param graph 
 * @returns 
 */
const NewKnowledgeSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '知识库',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.NEW_KNOWLEDGE,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
    ],
  };
};

/**
 * 知识库
 * @param props 
 * @param graph 
 * @returns 
 */
const knowledgeSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: '知识库',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.KNOWLEDGE,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
    ],
  };
};

/**
 * 文本分类大模型节点
 * @param totalConfig 
 * @param props 
 * @returns 
 */
const recognizeSchemaGen = (totalConfig, props: any): NsJsonSchemaForm.ISchema => {
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: `参数`,
        groups: [
          {
            controls: [
              {
                label: '分类配置',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.RECOGNIZE,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
      {
        name: `基础`,
        groups: [
          {
            name: '配置',
            tooltip: '',
            controls: [
              {
                Label: '模型设置',
                name: 'totalConfig',
                value: totalConfig,
                // originData: { noSystem: true },
                shape: ControlShapeEnum.MODEL_SETTING
              }
            ]
          }
        ]
      },
    ]
  };
};

/**
 * 知识库问答大模型节点
 * @param totalConfig 
 * @param props 
 * @returns 
 */
const knowledgeLLMSchemaGen = (totalConfig, props: any): NsJsonSchemaForm.ISchema => {
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: `参数`,
        groups: [
          {
            controls: [
              {
                label: '知识库',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.NEW_KNOWLEDGE,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
      {
        name: `模型配置`,
        groups: [
          {
            name: '配置',
            tooltip: '',
            controls: [
              {
                Label: '模型设置',
                name: 'totalConfig',
                value: totalConfig,
                // originData: { noSystem: true },
                shape: ControlShapeEnum.MODEL_SETTING
              }
            ]
          }
        ]
      },
    ]
  };
};

/**
 * 函数调用大模型节点
 * @param totalConfig 
 * @param props 
 * @returns 
 */
const functionCallSchemaGen = (totalConfig, props: any): NsJsonSchemaForm.ISchema => {
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: `参数`,
        groups: [
          {
            controls: [
              {
                label: '插件列表',
                disabled: props.disabled,
                required: false,
                shape: ControlShapeEnum.LLM_PLUGINS,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
      {
        name: `模型配置`,
        groups: [
          {
            name: '配置',
            tooltip: '',
            controls: [
              {
                Label: '模型设置',
                name: 'totalConfig',
                value: totalConfig,
                // originData: { noSystem: true },
                shape: ControlShapeEnum.MODEL_SETTING
              }
            ]
          }
        ]
      },
    ]
  };
};


/**
 * 意图识别大模型节点
 * @param totalConfig 
 * @param props 
 * @returns 
 */
const intentionSchemaGen = (totalConfig, props: any): NsJsonSchemaForm.ISchema => {
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: `参数`,
        groups: [
          {
            controls: [
              {
                label: '分类配置',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.INTENTION,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
      {
        name: `模型配置`,
        groups: [
          {
            name: '配置',
            tooltip: '',
            controls: [
              {
                Label: '模型设置',
                name: 'totalConfig',
                value: totalConfig,
                // originData: { noSystem: true },
                shape: ControlShapeEnum.MODEL_SETTING
              }
            ]
          }
        ]
      },
    ]
  };
};

/**
 * 参数提取大模型节点
 * @param totalConfig 
 * @param props 
 * @returns 
 */
const parameterExtractSchemaGen = (totalConfig, props: any): NsJsonSchemaForm.ISchema => {
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: `参数`,
        groups: [
          {
            controls: [
              {
                label: '参数配置',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.PARAMETER_EXTRACT,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
      {
        name: `模型配置`,
        groups: [
          {
            name: '配置',
            tooltip: '',
            controls: [
              {
                Label: '模型设置',
                name: 'totalConfig',
                value: totalConfig,
                originData: { disabledConfig: { json_object: true }, filterFn: (model) => model.name.includes('gpt-4o') },
                shape: ControlShapeEnum.MODEL_SETTING
              }
            ]
          }
        ]
      },
    ]
  };
};

/**
 * 通用大模型节点
 * @param totalConfig 
 * @param totalOutput 
 * @param props 
 * @returns 
 */
const universalLlmSchemaGen = (totalConfig: any, totalOutput: any, props: any): NsJsonSchemaForm.ISchema => {
  return {
    name: <PanelTitle {...props} />,
    tabs: [
      {
        name: '输入',
        groups: [
          {
            tooltip: '',
            controls: [
              {
                Label: '模型设置',
                name: 'totalConfig',
                value: totalConfig,
                shape: ControlShapeEnum.MODEL_SETTING
              }
            ]
          }
        ],
      },
      {
        name: '输出',
        groups: [
          {
            controls: [
              {
                name: 'fallbackConfig',
                value: { totalConfig: totalOutput, fallbackOut: props.fallbackOut, id: props.id },
                shape: ControlShapeEnum.OUTPUT
              }]
          }
        ],
      },
    ],
  };
};

export const llmSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  // 查询所有的已经链接的inputs
  const cell = graph.getCellById(props.id);
  const edges = graph.getIncomingEdges(cell);
  const portIds = edges?.map((v) => parsePortId(v.data.targetPortId).portId);
  // 拿到所有的inputs和vars
  const { vars, inputs, outputs } = props;
  const newInputs = inputs.map((v) => ({ ...v, disabled: portIds?.includes(v.id) }));
  const varConfig = (vars || []).map((v) => typeSchema(v, props.disabled));
  const inputConfig = (newInputs || []).map((v) =>
    typeSchema(v, v.disabled || props.disabled),
  );
  // 固定顺序
  let totalConfig = varConfig.concat(inputConfig).sort((a, b) => {
    return a.name.localeCompare(b.name)
  });
  // 固定顺序
  let totalOutput = outputs.map(v => typeSchema(v, props.disabled)).sort((a, b) => {
    return a.name.localeCompare(b.name)
  });

  if (props.code === NODE_CODE.RECOGNIZE) {
    return recognizeSchemaGen(totalConfig, props);
  }
  if (props.code === NODE_CODE.INTENTION) {
    return intentionSchemaGen(totalConfig, props);
  }
  if (props.code === NODE_CODE.KNOWLEDGE_LLM) {
    return knowledgeLLMSchemaGen(totalConfig, props);
  }
  if (props.code === NODE_CODE.PARAMETER_EXTRACT) {
    return parameterExtractSchemaGen(totalConfig, props);
  }
  if (props.code === NODE_CODE.FUNCTION_CALL) {
    return functionCallSchemaGen(totalConfig, props);
  }
  return universalLlmSchemaGen(totalConfig, totalOutput, props, graph);
};
