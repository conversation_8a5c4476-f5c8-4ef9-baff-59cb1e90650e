// @ts-nocheck
/* eslint-disable no-promise-executor-return */
import { ControlShapeEnum } from '../index';
import { parsePortId } from '@/utils/node';

export function delay(ms: number) {
  return new Promise((resolve) =>
    setTimeout(() => {
      resolve(true);
    }, ms),
  );
}

export const aliasMap: any = {
  inputs: '输入',
  outputs: '输出',
  scriptType: '脚本类型',
  script: '脚本',
};

/**
 * 获取所有和vars的结构
 * @param props 
 * @param graph 
 * @param filters 
 * @returns 
 */
export const getTotalConfig = (props: any, graph: NsGraph, filters): any => {
  // 查询所有的已经链接的inputs
  let portIds = [];
  if (props.id) {
    const cell = graph.getCellById(props.id);
    const edges = graph.getIncomingEdges(cell);
    portIds = edges?.map((v) => parsePortId(v.data.targetPortId).portId);
  }
  // 拿到所有的inputs和vars
  const { vars, inputs } = props;
  const newInputs = inputs.map((v) => ({ ...v, disabled: portIds?.includes(v.id) }));
  const varConfig = (vars || []).map((v) => typeSchema(v, props.disabled));
  const inputConfig = (newInputs || []).map((v) =>
    typeSchema(v, v.disabled || props.disabled),
  );
  // 固定顺序
  const totalConfig = varConfig.concat(inputConfig).sort((a, b) => {
    return a.name.localeCompare(b.name)
  });
  if (filters.include) {
    return totalConfig.filter(v => filters.include.includes(v.name));
  }
  if (filters?.exclude) {
    return totalConfig.filter(v => !filters.exclude.includes(v.name));
  }
  return totalConfig;
}

export const typeSchema = (typeObj: any, disabled?: boolean, otherProps?: any) => {
  const obj = {
    name: typeObj.name,
    label: `${aliasMap[typeObj.name] || typeObj.title || typeObj.name}(${typeObj.type})`,
    type: typeObj.type,
    originData: { rule: typeObj.rule, ...otherProps },
    id: typeObj.id,
  };

  if (typeObj.enum) {
    return {
      ...obj,
      shape: ControlShapeEnum.SELECT,
      defaultValue: typeObj.enum[0],
      disabled,
      value: typeObj.value,
      options: typeObj.enum.map((v) => {
        if (typeof v !== 'object') {
          return ({ title: v, value: v });
        }
        return ({ title: v.label ?? v.value, value: v.value })
      }),
    };
  }
  if (typeObj.name === 'script') {
    return {
      ...obj,
      value: typeObj.value,
      disabled,
      shape: ControlShapeEnum.CODE_EDITOR,
    };
  }
  if (
    typeObj.type === 'number' ||
    typeObj.type === 'float' ||
    typeObj.type === 'integer'
  ) {
    return {
      ...obj,
      value: typeObj.value,
      disabled,
      shape: ControlShapeEnum.Number,
    };
  }
  if (typeObj.type === 'datetime') {
    return {
      ...obj,
      value: typeObj.value,
      disabled,
      shape: ControlShapeEnum.DATE,
    };
  }
  if (typeObj.type === 'object') {
    return {
      ...obj,
      value: typeObj.value && typeof typeObj.value === 'object' ? JSON.stringify(typeObj.value, null, 2) : (typeObj.value || ''),
      disabled,
      originData: { scriptType: 'json', title: 'JSON' },
      shape: ControlShapeEnum.CODE_EDITOR,
    };
  }
  return {
    ...obj,
    disabled,
    value: typeObj.value,
    shape: 'textarea',
  };
};
