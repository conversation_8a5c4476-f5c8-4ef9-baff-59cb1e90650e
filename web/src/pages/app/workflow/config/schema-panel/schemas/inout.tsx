// @ts-nocheck
/* eslint-disable no-promise-executor-return */
import { NsJsonSchemaForm } from '@antv/xflow';
import { NODE_CODE } from '@utils/constant';
import { PanelTitle } from '../switch-case';
import { ControlShapeEnum } from '../index';

const needPermanentCode = {
  INPUT_AUDIO: 1,
  INPUT_VIDEO: 1,
  INPUT_IMAGE: 1,
  INPUT_UPLOAD: 1,
};

const needTypeCode = {
  INPUT_TEXT: [
    {
      value: 'string',
      label: 'string',
    },
    {
      value: 'NosKey',
      label: 'NosKey',
    },
    {
      value: 'Url',
      label: 'Url',
    },
    {
      value: 'Audio',
      label: 'Audio',
    },
    {
      value: 'Video',
      label: 'Video',
    },
    {
      value: 'Image',
      label: 'Image',
    },
    {
      value: 'AudioNosKey',
      label: 'AudioNosKey',
    },
    {
      value: 'VideoNosKey',
      label: 'VideoNosKey',
    },
    {
      value: '<PERSON>NosKey',
      label: '<PERSON>Nos<PERSON>ey',
    },
  ],
  INPUT_ENUM: [
    {
      value: 'string',
      label: 'string',
    },
    {
      value: 'float',
      label: 'float',
    },
    {
      value: 'integer',
      label: 'integer',
    },
  ],
  INPUT_AUDIO: [
    {
      value: 'Audio',
      label: 'Audio',
    },
    {
      value: 'AudioNosKey',
      label: 'AudioNosKey',
    },
  ],
  INPUT_VIDEO: [
    {
      value: 'Video',
      label: 'Video',
    },
    {
      value: 'VideoNosKey',
      label: 'VideoNosKey',
    },
  ],
  INPUT_IMAGE: [
    {
      value: 'Image',
      label: 'Image',
    },
    {
      value: 'ImageNosKey',
      label: 'ImageNosKey',
    },
  ],
  INPUT_UPLOAD: [
    {
      value: 'Url',
      label: 'Url',
    },
    {
      value: 'NosKey',
      label: 'NosKey',
    },
  ],
};

/**
 * cioLoader
 * @param props
 * @returns
 */
const cioLoaderSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '输入参数类型',
            tooltip: '',
            controls: [
              {
                label: 'CIO配置',
                disabled: props.disabled,
                required: true,
                shape: ControlShapeEnum.CIO_LOADER,
                originData: { nameDisabled: true },
                value: props,
                name: 'data',
              },
            ],
          },
        ],
      },
    ],
  };
};

export const varSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: 'groupName',
            controls: [
              {
                label: '输入参数',
                tooltip: '额外变量注入',
                disabled: props.name === '开始' ? true : props.disabled,
                name: 'outputs',
                value: props.outputs,
                originData: { showVariable: true, noType: props.name === '开始', options: needTypeCode[props.code] || undefined },
                shape: ControlShapeEnum.INPUT_PARAM
              },
            ],

          },
        ],
      },
    ],
  };
}


export const enumSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: 'groupName',
            controls: [
              {
                label: '输入参数',
                tooltip: '作为整个workflow的输入参数',
                disabled: props.disabled,
                name: 'outputs',
                value: props.outputs,
                originData: { showEnum: true, options: needTypeCode[props.code] || undefined, showDesc: true },
                shape: ControlShapeEnum.INPUT_PARAM
              },
            ],

          },
        ],
      },
    ],
  };
}

export const numberSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: 'groupName',
            controls: [
              {
                label: '输入参数',
                tooltip: '作为整个workflow的输入参数',
                disabled: props.disabled,
                name: 'outputs',
                value: props.outputs,
                originData: { showRange: true, options: needTypeCode[props.code] || undefined, showDesc: true },
                shape: ControlShapeEnum.INPUT_PARAM_WITHOUT_TYPE
              },
            ],

          },
        ],
      },
    ],
  };
}

/**
 * Input Schema
 * @param props
 * @returns
 */
export const inputSchemaGen = (props: any, graph: NsGraph): NsJsonSchemaForm.ISchema => {
  if (props.code === NODE_CODE.INPUT_ENUM) {
    return enumSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.INPUT_PARAM) {
    return varSchemaGen(props, graph);
  }
  if (props.code === NODE_CODE.INPUT_NUMBER || props.code === NODE_CODE.INPUT_FLOAT) {
    return numberSchemaGen(props, graph);
  }
  if (props.code === 'CIO_LOADER') {
    return cioLoaderSchemaGen(props, graph);
  }
  let originData = {};
  if (props.code === NODE_CODE.INPUT_VAR) {
    originData = { nameDisabled: ['message'] };
  }
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: 'groupName',
            controls: [
              {
                label: '输入参数名',
                tooltip: '作为整个workflow的输入参数',
                disabled: props.disabled,
                name: 'outputs',
                value: props.outputs,
                originData: { options: needTypeCode[props.code] || undefined, showDesc: true, showPermanent: needPermanentCode[props.code], ...originData },
                shape:
                  needTypeCode[props.code] || props.code === NODE_CODE.INPUT_ARRAY
                    ? ControlShapeEnum.INPUT_PARAM
                    : ControlShapeEnum.INPUT_PARAM_WITHOUT_TYPE,
              },
            ],
          },
        ],
      },
    ],
  };
};

/**
 * 输出节点
 * @param props
 * @returns
 */
export const outputSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  let originData = {};
  if (props.code === NODE_CODE.OUTPUT) {
    originData = { nameDisabled: ['output'] };
  }
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '脚本配置',
            tooltip: '123',
            controls: [
              {
                label: '输出参数',
                tooltip: '作为整个workflow的所有输出参数（会组合成data）',
                disabled: props.disabled,
                required: true,
                name: 'inputs',
                originData,
                value: props.inputs,
                shape: ControlShapeEnum.PARAM_WITH_DETAIL,
              },
            ],
          },
        ],
      },
    ],
  };
};
