// @ts-nocheck
/* eslint-disable no-promise-executor-return */
import { NsJsonSchemaForm } from '@antv/xflow';
import { PanelTitle } from '../switch-case';
import { objectTypes } from '@utils/constant';

import { ControlShapeEnum } from '../index';

/**
 * switch的schema
 * @param props
 * @returns
 */
export const switchSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  const { vars = [] } = props;
  const groups = [];
  groups.push({
    name: 'switch配置',
    controls: [
      {
        label: '条件匹配结果',
        tooltip: '可以定制多条匹配的条件',
        required: true,
        disabled: props.disabled,
        name: 'conditions',
        value: {
          type: vars[0]?.value || 'boolean',
          outputs: props.outputs,
        },
        shape: ControlShapeEnum.SWITCH_PARAMS,
        dependencies: [
          { name: 'type', condition: 'string', hidden: false },
          { name: 'type', condition: 'boolean', hidden: true },
        ],
      },
    ],
  });
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups,
      },
    ],
  };
};

export const selectSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '脚本配置',
            tooltip: '123',
            controls: [
              {
                label: '输入参数',
                required: true,
                name: 'inputs',
                disabled: props.disabled,
                value: props.inputs,
                shape: ControlShapeEnum.PARAM,
              },
            ],
          },
        ],
      },
    ],
  };
};




/**
 * joinSchemaGen
 * @param props
 * @returns
 */
export const joinSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '脚本配置',
            tooltip: '123',
            controls: [
              {
                label: '输入参数',
                required: true,
                name: 'inputs',
                disabled: props.disabled,
                value: props.inputs,
                shape: ControlShapeEnum.PARAM,
              },
              {
                label: '输出参数',
                required: true,
                name: 'outputs',
                disabled: props.disabled,
                options: objectTypes,
                value: props.outputs,
                shape: ControlShapeEnum.PARAM_ONE,
              },
            ],
          },
        ],
      },
    ],
  };
};

/**
 * groupSchemaGen
 * @param props
 * @returns
 */
export const groupSchemaGen = (props: any): NsJsonSchemaForm.ISchema => {
  return {
    tabs: [
      {
        name: <PanelTitle {...props} />,
        groups: [
          {
            name: '脚本配置',
            tooltip: '123',
            controls: [
              {
                label: '输入参数',
                required: true,
                name: 'inputs',
                disabled: props.disabled,
                originData: { nameDisabled: true, withDetail: true, isArray: true },
                value: props.inputs,
                shape: ControlShapeEnum.PARAM_ONE,
              },
              {
                label: '输出参数',
                required: true,
                name: 'outputs',
                originData: { nameDisabled: true, withDetail: true, isArray: true },
                disabled: props.disabled,
                value: props.outputs,
                shape: ControlShapeEnum.PARAM_ONE,
              },
            ],
          },
        ],
      },
    ],
  };
};
