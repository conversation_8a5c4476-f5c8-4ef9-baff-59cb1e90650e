import { NODE_CODE, getOriginType } from '../../../../../../utils/constant';

export const formatScriptVars = (script) => [
  {
    name: 'script',
    type: 'string',
    value: script,
  },
  {
    enum: ['GROOVY'],
    name: 'scriptType',
    type: 'string',
    value: 'GROOVY',
  },
];

export function formatScript(data, valMap) {
  // 如果是 Transform
  if (data.code === NODE_CODE.TRANSFORM) {
    const fromType = data.inputs[0].type;
    const toType = data.outputs[0].type;
    const script = getTransformScript(fromType, toType);
    data.vars = formatScriptVars(script);
    console.log('script', script);
  }

  // 如果是 Text_Split
  if (data.code === NODE_CODE.TEXT_SPLIT) {
    data.configs[0].value = valMap.splitter || data.configs[0].value;
    const temp = data.configs[0].value;
    const inputs = data.inputs;
    const script = getTextSplitScript(inputs, temp);
    data.vars = formatScriptVars(script);
  }

  // 如果是Extract
  if (data.code === NODE_CODE.EXTRACT) {
    const script = getExtractScript(data.outputs)
    data.vars = formatScriptVars(script);
  }

  // 如果是Join
  if (data.code === NODE_CODE.JOIN) {
    const script = getJoinScript(data.inputs)
    data.vars = formatScriptVars(script);
  }

  // 如果是template
  if (data.code === NODE_CODE.TEMPLATE) {
    // 如果是用户通过控制面板输入的值
    const temp = valMap.template;
    if (valMap.template) {
      data.vars = [
        {
          label: '模板输入',
          name: 'template',
          type: 'string',
          value: temp,
        },
      ];
      data.inputs = data.inputs.filter(v => v.name !== 'template');
    } else {
      // 只保留template
      data.vars = data.vars.filter(v => v.name === 'template' && v.value);
      // 说明没有template，或者template的内容清空了
      if (data.vars.length === 0) {
        data.inputs = [
          {
            label: '模板输入',
            name: 'template',
            type: 'string',
          },
          ...data.inputs,
        ];
      }
    }
    const inputs = data.inputs;
    const script = getTextTemplateScript(inputs, temp);
    data.vars = [
      ...data.vars,
      ...formatScriptVars(script),
    ];
    console.log('script', script);
  }
}

const template = (
  input,
  result,
) => `import com.netease.music.content.produce.service.dto.workflow.GroovyScript
import com.netease.music.content.produce.service.dto.workflow.GroovyTaskContext 

class GroovyScriptTask implements GroovyScript {
    @Override
    Map<String, Object> evaluate(GroovyTaskContext context) {
        def result = new HashMap<String, Object>();
        if (context == null) {
            return result
        }
        def inputData = context.getInputData();
        ${input}
        ${result}
        return result;
    }
}`;


/**
 * 获取分支合并节点脚本
 * @param from
 * @param to
 * @returns
 */
function getJoinScript(inputs) {
  const getInputs = (name) => `def ${name} = inputData.get("${name}")`;
  const mergeData = `\ndef mergedObject = [${inputs.map(v => `${v.name}: ${v.name}`)}];`
  let input = inputs.map((v) => getInputs(v.name)).join('\n');
  const result = 'result["output"] = mergedObject;'
  const res = template(input + mergeData, result);
  return res;
}


/**
 * 获取参数提取节点脚本
 * @param from
 * @param to
 * @returns
 */
function getExtractScript(outputs) {
  const inputPrefix = 'def inputObject = inputData.get("inputObject");\n';
  const getInputs = (name) => `def ${name} = inputObject.get("${name}")`;
  const getOutputs = (name) => `result.put("${name}", ${name});`;
  let input = outputs.map((v) => getInputs(v.name)).join('\n');
  let output = outputs.map(v => getOutputs(v.name)).join('\n');
  const res = template(inputPrefix + input, output);
  console.log('textT', res);
  return res;
}

/**
 * 获取文本节点
 * @param from
 * @param to
 * @returns
 */
function getTextTemplateScript(inputs, temp) {
  if (!temp) {
    return templateDefault;
  }
  const getInputs = (name) => `def ${name} = inputData.get("${name}")`;
  let input = inputs.map((v) => getInputs(v.name)).join('\n');
  const newTemp = (temp || '').replace(/{(.*?)}/g, (...args) => {
    console.log('args', args);
    return '${' + args[1] + '}';
  });
  input += `\ndef myStr = """${newTemp}""";\n`;
  const result = 'result.put("output", myStr.toString())';
  const res = template(input, result);
  console.log('textT', res);
  return res;
}

const templateDefault = `import com.netease.music.content.produce.service.dto.workflow.GroovyScript
import com.netease.music.content.produce.service.dto.workflow.GroovyTaskContext
import org.apache.commons.collections.MapUtils
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.text.StrSubstitutor

class PromptTemplate implements GroovyScript{
    static final String TEMPLATE = "template"

    @Override
    Map<String, Object> evaluate(GroovyTaskContext context) {
        def result = new HashMap<String, Object>()
        if (!context) {
            result << ["output": ""]
            return result
        }
        def inputData = context.getInputData()
        if (!inputData || !inputData.get("template")) {
            result << ["output": ""]
            return result
        }
        def template = inputData.get(TEMPLATE)
        Map<String, String> replaceContext = [:]
        inputData.forEach { key, value ->
              replaceContext.put(key, value)
        }
        result << ["output": replace(template, replaceContext)]
        return result
    }

    String replace(String template, Map<String, String> replaceContext) {
        if (StringUtils.isBlank(template) || MapUtils.isEmpty(replaceContext)) {
            return template
        }
        StrSubstitutor strSubstitutor = new StrSubstitutor(replaceContext, "{", "}")
        return strSubstitutor.replace(template)
    }
}`;

/**
 * 获取文本节点
 * @param from
 * @param to
 * @returns
 */
function getTextSplitScript(inputs, splitter) {
  const getInputs = (name) => `def ${name} = inputData.get("${name}")`;
  let input = inputs.map((v) => getInputs(v.name)).join('\n');
  input += `\ndef lines = input.split('${splitter}');\n`;
  const result = 'result.put("output", lines)';
  const res = template(input, result);
  console.log('getTextSplitScript', res);
  return res;
}

/**
 * 获取脚本节点
 * @param from
 * @param to
 * @returns
 */
function getTransformScript(from, to) {
  const toType = getOriginType(to);
  const parse = (trans) =>
    template('def input = inputData.get("input");', `result.put("output", ${trans});`);
  const transMap = {
    string: 'input.toString()',
    float: 'input.toFloat()',
    int: 'input.toInteger()',
    Video: (from) => {
      if (from === 'VideoNosKey' || from === 'NosKey') {
        return template('def input = inputData.get("input");\ndef prefix = input.tokenize("/")[0]\ndef output = "";\nif (prefix.contains("jd")) {\noutput = "https://${prefix}.nos-jd.163yun.com/${input.drop(prefix.size() + 1)}";\n } else if (prefix == "yyimgs") {\noutput = "https://p6.music.126.net/${input.drop(prefix.size() + 1)}";\n} else {\noutput = "https://nos.netease.com/${input}"; \n}\n', 'result.put("output", output.toString());');
      }
      return parse('input')
    },
    Image: (from) => {
      if (from === 'ImageNosKey' || from === 'NosKey') {
        return template('def input = inputData.get("input");\ndef prefix = input.tokenize("/")[0]\ndef output = "";\nif (prefix.contains("jd")) {\noutput = "https://${prefix}.nos-jd.163yun.com/${input.drop(prefix.size() + 1)}";\n } else if (prefix == "yyimgs") {\noutput = "https://p6.music.126.net/${input.drop(prefix.size() + 1)}";\n} else {\noutput = "https://nos.netease.com/${input}"; \n}\n', 'result.put("output", output.toString());');
      }
      return parse('input')
    },
    Audio: (from) => {
      if (from === 'AudioNosKey' || from === 'NosKey') {
        return template('def input = inputData.get("input");\ndef prefix = input.tokenize("/")[0]\ndef output = "";\nif (prefix.contains("jd")) {\noutput = "https://${prefix}.nos-jd.163yun.com/${input.drop(prefix.size() + 1)}";\n } else if (prefix == "yyimgs") {\noutput = "https://p6.music.126.net/${input.drop(prefix.size() + 1)}";\n} else {\noutput = "https://nos.netease.com/${input}"; \n}\n', 'result.put("output", output.toString());');
      }
      return parse('input')
    },
    Url: (from) => {
      if (from === 'AudioNosKey' || from === 'NosKey' || from === 'ImageNosKey' || from === 'VideoNosKey') {
        return template('def input = inputData.get("input");\ndef prefix = input.tokenize("/")[0]\ndef output = "";\nif (prefix.contains("jd")) {\noutput = "https://${prefix}.nos-jd.163yun.com/${input.drop(prefix.size() + 1)}";\n } else if (prefix == "yyimgs") {\noutput = "https://p6.music.126.net/${input.drop(prefix.size() + 1)}";\n} else {\noutput = "https://nos.netease.com/${input}"; \n}\n', 'result.put("output", output.toString());');
      }
      return parse('input')
    },
    ImageNosKey: (from) => {
      if (from === 'Image' || from === 'Url') {
        return template('def input = inputData.get("input");\ndef url = inputData.get("input");\ndef tokenized = url.tokenize("//");\ndef output = "";\nif (url.contains("music.126.net")) {\n    def path = url.substring(url.indexOf("net/") + 4);\n    output = "yyimgs/${path}";\n} else {\n    def prefix = tokenized[1].tokenize(".")[0];\n    def path = tokenized.drop(2).join("/");\n    output = "${prefix}/${path}";\n}\n', 'result.put("output", output.toString());');
      }
      return parse('input')
    },
    AudioNosKey: (from) => {
      if (from === 'Audio' || from === 'Url') {
        return template('def input = inputData.get("input");\ndef url = inputData.get("input");\ndef tokenized = url.tokenize("//");\ndef output = "";\nif (url.contains("music.126.net")) {\n    def path = url.substring(url.indexOf("net/") + 4);\n    output = "yyimgs/${path}";\n} else {\n    def prefix = tokenized[1].tokenize(".")[0];\n    def path = tokenized.drop(2).join("/");\n    output = "${prefix}/${path}";\n}\n', 'result.put("output", output.toString());');
      }
      return parse('input')
    },
    VideoNosKey: (from) => {
      if (from === 'Video' || from === 'Url') {
        return template('def input = inputData.get("input");\ndef url = inputData.get("input");\ndef tokenized = url.tokenize("//");\ndef output = "";\nif (url.contains("music.126.net")) {\n    def path = url.substring(url.indexOf("net/") + 4);\n    output = "yyimgs/${path}";\n} else {\n    def prefix = tokenized[1].tokenize(".")[0];\n    def path = tokenized.drop(2).join("/");\n    output = "${prefix}/${path}";\n}\n', 'result.put("output", output.toString());');
      }
      return parse('input')
    },
    NosKey: (from) => {
      if (from === 'Image' || from === 'Audio' || from === 'Url' || from === 'Video') {
        return template('def input = inputData.get("input");\ndef url = inputData.get("input");\ndef tokenized = url.tokenize("//");\ndef output = "";\nif (url.contains("music.126.net")) {\n    def path = url.substring(url.indexOf("net/") + 4);\n    output = "yyimgs/${path}";\n} else {\n    def prefix = tokenized[1].tokenize(".")[0];\n    def path = tokenized.drop(2).join("/");\n    output = "${prefix}/${path}";\n}\n', 'result.put("output", output.toString());');
      }
      return parse('input')
    },
  
    number: 'input.toInteger()',
    boolean: (f: string) => {
      if (f === 'number') {
        return parse('input != 0');
      }
      if (f === 'boolean') {
        return parse('input');
      }
      return parse('input.toBoolean()');
    },
  };
  // 如果不在map里，直接返回
  if (!transMap[toType]) {
    return parse('input');
  }
  // 优先看最子的节点
  if (transMap[to]) {
    if (typeof transMap[to] === 'string') {
      return parse(transMap[to]);
    }
    return transMap[to](from);
  }
  // 再看继承类型
  if (typeof transMap[toType] === 'string') {
    return parse(transMap[toType]);
  }
  return transMap[toType](from);
}
