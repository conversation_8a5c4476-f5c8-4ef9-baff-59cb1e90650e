import { formatTypeValue, mergeArr } from '@/utils/common';
import { NODE_CODE, NODE_TYPE, MODULE_CONFIG_MAP, MODULE_NAME_MAP } from '../../../../../../utils/constant';

const validValue = (val) => {
  return val || val === 0 || val === false
};

const online = window.location.host === 'langbase.netease.com';
const cioBase = online ? 'https://music-cms.hz.netease.com' : 'http://cms.qa.igame.163.com';
const cioPath = '/api/content/pool/resource/content/detail'

const transInputsAndVars = (data, valMap) => {
  // 先把所有的inputs和vars合并，同时根据当前的 valMap 决定是否需要删除或增加值
  const totalInputs = new Map();
  data.inputs.forEach(v => {
    validValue(valMap[v.name]) ?
      totalInputs.set(v.name, { ...v, value: valMap[v.name] }) :
      totalInputs.set(v.name, v)
  });
  data.vars.forEach(v => {
    // 特殊处理system_prompt
    if (v.name === 'system_prompt') {
      totalInputs.delete('systemPrompt');
    }
    // 如果是删除值，则需要把值删除
    if (valMap.hasOwnProperty(v.name)) {
      totalInputs.set(v.name, { ...v, value: validValue(valMap[v.name]) ? formatTypeValue(valMap[v.name], v.type): undefined}) 
    } else {
      if (totalInputs.has(v.name)) {
        const tmp = totalInputs.get(v.name);
        totalInputs.set(v.name, { ...tmp, value: v.value });
      } else {
        totalInputs.set(v.name, v)
      }
    }
  })
  // 删掉maxConversations
  if (data.type === NODE_TYPE.LLM || data.code === NODE_CODE.LLM) {
    totalInputs.delete('maxConversations');
    totalInputs.delete('max_conversations');
  }

  data.vars = Array.from(totalInputs.values()).filter(v => validValue(v.value));
  data.inputs = Array.from(totalInputs.values()).filter(v => !validValue(v.value));
  console.log('transInputsAndVars data', data, totalInputs);
  return data;
}

export function formatHttp(data, valMap) {
  // 如果开启了fallbackOut，则需要把fallbackOut的值添加到data.fallbackOut中
  if (valMap.fallbackConfig) {
    data.fallbackOut = valMap.fallbackConfig.fallbackOut;
    // 把data.outputs中的value设置成valMap.fallbackConfig.totalConfig中的value
    data.outputs = data.outputs.map(v => {
      const find = valMap.fallbackConfig.totalConfig.find(v => v.name === v.name);
      return { ...v, value: find?.value || v.value }
    })
  }
  if (valMap.aiMvConfig) {
    data.vars = data.vars.map(v => {
      const find = valMap.aiMvConfig.totalConfig.find(v1 => v1.name === v.name);
      return { ...v, value: find?.value || v.value }
    })
    data.inputs = data.inputs.map(v => {
      const find = valMap.aiMvConfig.totalConfig.find(v1 => v1.name === v.name);
      return { ...v, value: find?.value || v.value }
    })
    data.originValue = valMap.aiMvConfig.originValue;
  }
  // 如果是 Transform
  if (data.code === NODE_CODE.UNI_HTTP) {
    // 把 config 的对应字段修改
    data.configs = data.configs.map(c => {
      const findVar = data.vars.find(v => v.name === c.name);
      return { ...c, type: findVar?.type || c.type, value: findVar?.value || c.value }
    })
  }
  if (data.code === NODE_CODE.SUB_WORKFLOW) {
    const workflow = valMap.workflow;
    if (workflow && !workflow.error) {
      // 说明属性变更
      if (workflow.change) {
        console.log("change");
        valMap[String(workflow.change.name)] = workflow.change.value;
        // 否则是新的工作流
      } else {
        console.log("workflow change", workflow);
        data.inputs = workflow.inputs || data.inputs;
        data.vars = [{
          name: 'workflowId',
          title: "工作流Id",
          type: "string",
          value: workflow.workflowId
        },
        {
          name: 'oneWay',
          title: "无需等待结果",
          type: "boolean",
          value: false
        }];
        // transInputsAndVars(data);
        data.outputs = workflow.outputs || data.outputs;
        data.name = workflow.name || data.name;
        data.appId = workflow.appId || data.appId;
      }
    }
  }
  if (data.code === NODE_TYPE.LLM && valMap.totalConfig) {
    data.vars = valMap.totalConfig;
    console.log('data.vars', data.vars);
    const imageUrl = data.vars.find(v => v.name === 'imageUrl');
    const videoUrl = data.vars.find(v => v.name === 'videoUrl');
    const audioUrl = data.vars.find(v => v.name === 'audioUrl');
    // 说明该模型没有视觉
    if (!imageUrl) {
      data.inputs = data.inputs.filter(v => v.name !== 'imageUrl');
    }
    // 说明该模型没有视频
    if (!videoUrl) {
      data.inputs = data.inputs.filter(v => v.name !== 'videoUrl');
    }
    // 说明该模型没有音频
    if (!audioUrl) {
      data.inputs = data.inputs.filter(v => v.name !== 'audioUrl');
    }
    
    const timeout = data.vars.find(v => v.name === 'timeout');
    const configTimeout = data.configs.find(v => v.name === 'timeout');
    if (configTimeout) {
      configTimeout.value = timeout?.value || 20000;
    } else {
      data.configs.push({
        name: 'timeout',
        value: timeout?.value || 20000
      })
    }
    console.log('vals', data.vars, valMap.totalConfig)
  }
  if (valMap.totalConfig) {
    mergeArr(data.vars, valMap.totalConfig)
    console.log('vals2', data, valMap.totalConfig)
  }
  if (valMap.sub_app) {
    mergeArr(data.vars, valMap.sub_app.totalConfig)
    data.outputs = valMap.sub_app.outputs;
    data.name = valMap.sub_app.name;
  }
  if (data.type === NODE_TYPE.HTTP || data.type === NODE_TYPE.LLM) {
    transInputsAndVars(data, valMap);
  }
  if (data.code === NODE_CODE.CIO) {
    const queryCode = data.vars.find(v => v.name === 'queryCode');
    const outputs = data.configs.find(v => v.name === 'outputs') || [];
    if (queryCode && outputs) {
      queryCode.value = outputs.value.map(v => v.code);
    }
    data.configs = data.configs.concat([
      {
        "name": "url",
        "value": `${cioBase}${cioPath}`
      },
      {
        "name": "method",
        "value": "POST"
      }, {
        'name': 'successExp',
        "value": "#code==200"
      }
    ])

  }
}
