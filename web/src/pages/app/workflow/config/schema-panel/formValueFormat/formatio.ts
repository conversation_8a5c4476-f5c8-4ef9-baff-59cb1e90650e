import { NODE_CODE } from '@/utils/constant';


export function formatIO(data) {
  if (data.code === NODE_CODE.INPUT_CIO) {
    data.outputs = data.configs.find(v => v.name === 'outputs')?.value || [];
  }
  // 如果是 Transform
  if (data.code === NODE_CODE.INPUT_ENUM) {
    // 如果发现值不在enum中，则清空
    const enumArr = data.outputs[0].enum || [];
    if (!enumArr.includes(data.originValue)) {
      data.originValue = undefined;
    }
  }
  if (data.code === NODE_CODE.INPUT_PARAM) {
    const output = data.outputs[0];
    if (output.historyMessageLimit) {
      data.vars = [
        {
          name: 'historyMessageLimit',
          value: output.historyMessageLimit,
        },
      ];
    }
  }
  console.log('data123', data);
}
