// @ts-nocheck
/* eslint-disable no-promise-executor-return */
import { NODE_TYPE } from '@utils/constant';

import { httpSchemaGen } from './schemas/http';
import { llmSchemaGen } from './schemas/llm';
import { inputSchemaGen, outputSchemaGen } from './schemas/inout';
import {
  groupSchemaGen,
  joinSchemaGen,
  selectSchemaGen,
  switchSchemaGen,
} from './schemas/logic';
import { scriptSchemaGen } from './schemas/script';

/**
 * 注意：通用大模型节点type是http，但是code是llm，所以会在httpSchemaGen中处理
 */
const configMap: any = {
  [NODE_TYPE.INPUT]: inputSchemaGen,
  [NODE_TYPE.OUTPUT]: outputSchemaGen,
  [NODE_TYPE.SCRIPT]: scriptSchemaGen,
  [NODE_TYPE.HTTP]: httpSchemaGen,
  [NODE_TYPE.SWITCH]: switchSchemaGen,
  [NODE_TYPE.SELECT]: selectSchemaGen,
  [NODE_TYPE.JOIN]: joinSchemaGen,
  [NODE_TYPE.FOREACH]: groupSchemaGen,
  [NODE_TYPE.LLM]: llmSchemaGen,
  [NODE_TYPE.DISPLAY]: scriptSchemaGen,
};

export default configMap;
