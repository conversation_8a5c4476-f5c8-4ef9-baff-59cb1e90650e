import { IconFont } from '@/components/icons';
import { getGraphInstance } from '@/utils/flow';
import styled from 'styled-components';
import { Popover } from "antd";


const PanelTitleId = styled.span`
  color: #999;
  font-size: 10px;
  border-radius: 4px;
  padding: 0px 4px;
  background: #f0f0f0;
`

const PanelTitleWrapper = styled.span`
  margin-top: 2px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`

const PanelLine = styled.span`
  display: flex;
  align-items: center;
  justify-content: center;
`

const PanelTitleName = styled.span`
  cursor: pointer;
  max-width: 180px;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
`




export const PanelTitle = (props) => {
  return <PanelTitleWrapper>
    <PanelLine>
      <PanelTitleName>{props.name}</PanelTitleName>
      <SwitchCase branch={props.branch} />
    </PanelLine>
    <PanelLine>
      <PanelTitleId>{props.id}</PanelTitleId>
    </PanelLine>
  </PanelTitleWrapper>
}


export const SwitchCase = (props) => {
  const graph = getGraphInstance();
  if (!props.branch) {
    return null;
  }
  const branch = props.branch.split('-');
  const nodeNames = [];
  branch.forEach(v => {
    const node = graph.getCellById(v);
    if (node) {
      nodeNames.push(node.data.name);
    }
  })
  const content = (
    <div>
      <div style={{ textAlign: 'center', fontWeight: 'bold' }}>分支情况</div>
      {(nodeNames || []).join('->')}
    </div>
  )
  return <Popover content={content}><IconFont type="icon-switchcase"></IconFont></Popover>
}