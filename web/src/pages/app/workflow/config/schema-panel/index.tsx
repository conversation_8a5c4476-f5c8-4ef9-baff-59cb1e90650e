import type { NsJsonSchemaForm } from '@antv/xflow';

import CIO from './controls-components/cio-loader';
import Editor from './controls-components/code-editor';
import Date from './controls-components/date';
import { LinkShape } from './controls-components/link';
import Param, { ParamOnlyOne, ParamWithDetail, InputParam, InputParamWithoutType } from './controls-components/param';
import Select from './controls-components/select';
import SelectParam from './controls-components/select-param';
import Switch from './controls-components/switch-param';
import AirShip from './controls-components/air-ship';
import SkyEye from './controls-components/sky-eye';
import Knowledge from './controls-components/knowledge';
import Number from './controls-components/number';
import SubWorkflow from './controls-components/sub-workflow';
import Recognize from './controls-components/recognize';
import Intention from './controls-components/intention';
import Output from './controls-components/output';
import ParameterExtract from './controls-components/parameter-extract';
import Model from './controls-components/model';
import NewKnowledge from './controls-components/new_knowledge';
import SubChatflow from './controls-components/sub-chatflow';
import SubCompletion from './controls-components/sub-completion';
import { $ } from '@/utils/state';
import AiMv from './controls-components/ai-mv';
import LlmPlugins from './controls-components/llm-plugins';
/** 自定义form控件 */
export enum ControlShapeEnum {
  'EDITOR' = 'EDITOR',
  'LINKSHAPE' = 'LINKSHAPE',
  'CODE_EDITOR' = 'CODE_EDITOR',
  'SUB_WORKFLOW' = 'SUB_WORKFLOW',
  'PARAM' = 'PARAM',
  'INPUT_PARAM_WITHOUT_TYPE' = 'INPUT_PARAM_WITHOUT_TYPE',
  'INPUT_PARAM' = 'INPUT_PARAM',
  'PARAM_WITH_DETAIL' = 'PARAM_WITH_DETAIL',
  'PARAM_ONE' = 'PARAM_ONE',
  'DATE' = 'DATE',
  'SELECT' = 'SElECT',
  'SWITCH_PARAMS' = 'SWITCH_PARAMS',
  'SELECT_PARAMS' = 'SELECT_PARAMS',
  'CIO_LOADER' = 'CIO_LOADER',
  'AIR_SHIP' = 'AIR_SHIP',
  'AI_MV' = 'AI_MV',
  'Number' = 'Number',
  'RECOGNIZE' = 'RECOGNIZE',
  'SKY_EYE' = 'SKYEYE',
  'KNOWLEDGE' = 'KNOWLEDGE',
  'PARAMETER_EXTRACT' = 'PARAMETER_EXTRACT',
  'OUTPUT' = 'OUTPUT',
  'MODEL_SETTING' = 'MODEL_SETTING',
  'NEW_KNOWLEDGE' = 'NEW_KNOWLEDGE',
  'SUB_CHATFLOW' = 'SUB_CHATFLOW',
  'SUB_COMPLETION' = 'SUB_COMPLETION',
  'INTENTION' = 'INTENTION',
  'LLM_PLUGINS' = 'LLM_PLUGINS',
}

let init = false;

export const controlMapService: NsJsonSchemaForm.IControlMapService = (controlMap) => {
  controlMap.set(ControlShapeEnum.LINKSHAPE, LinkShape);
  controlMap.set(ControlShapeEnum.CODE_EDITOR, Editor);
  controlMap.set(ControlShapeEnum.PARAM, Param);
  controlMap.set(ControlShapeEnum.INPUT_PARAM_WITHOUT_TYPE, InputParamWithoutType);
  controlMap.set(ControlShapeEnum.DATE, Date);
  controlMap.set(ControlShapeEnum.SELECT, Select);
  controlMap.set(ControlShapeEnum.SWITCH_PARAMS, Switch);
  controlMap.set(ControlShapeEnum.SELECT_PARAMS, SelectParam);
  controlMap.set(ControlShapeEnum.PARAM_WITH_DETAIL, ParamWithDetail);
  controlMap.set(ControlShapeEnum.PARAM_ONE, ParamOnlyOne);
  controlMap.set(ControlShapeEnum.INPUT_PARAM, InputParam);
  controlMap.set(ControlShapeEnum.CIO_LOADER, CIO);
  controlMap.set(ControlShapeEnum.AIR_SHIP, AirShip);
  controlMap.set(ControlShapeEnum.SUB_WORKFLOW, SubWorkflow);
  controlMap.set(ControlShapeEnum.Number, Number);
  controlMap.set(ControlShapeEnum.RECOGNIZE, Recognize);
  controlMap.set(ControlShapeEnum.SKY_EYE, SkyEye);
  controlMap.set(ControlShapeEnum.KNOWLEDGE, Knowledge);
  controlMap.set(ControlShapeEnum.PARAMETER_EXTRACT, ParameterExtract);
  controlMap.set(ControlShapeEnum.MODEL_SETTING, Model);
  controlMap.set(ControlShapeEnum.OUTPUT, Output);
  controlMap.set(ControlShapeEnum.AI_MV, AiMv);
  controlMap.set(ControlShapeEnum.NEW_KNOWLEDGE, NewKnowledge);
  controlMap.set(ControlShapeEnum.INTENTION, Intention);
  controlMap.set(ControlShapeEnum.SUB_CHATFLOW, SubChatflow);
  controlMap.set(ControlShapeEnum.SUB_COMPLETION, SubCompletion);
  controlMap.set(ControlShapeEnum.LLM_PLUGINS, LlmPlugins);
  if (!init) {
    $.setControlMap(controlMap);
  }
  init = true;
  return controlMap;
};
