import { JSONParse } from '@/utils/common';
import { BaseShape } from './base-shape';
import { InputNumber } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';

const Param: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const [cur, setCur] = useState(value);

  const handleChange = () => {
    console.log('val', cur);
    onChange(cur);
  }

  return (
    <div className='param-form'>
      <InputNumber style={{ width: '100%' }} value={cur} onChange={setCur} onBlur={handleChange} disabled={disabled}></InputNumber>
    </div>
  );
};

function WithBase(props: any) {
  return <BaseShape {...props}>
    <Param></Param>
  </BaseShape>
}


export default WithBase;