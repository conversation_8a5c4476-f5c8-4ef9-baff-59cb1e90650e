// @ts-nocheck
import { FullscreenExitOutlined, FullscreenOutlined, PlayCircleFilled, DoubleRightOutlined } from '@ant-design/icons';
import { langs } from '@uiw/codemirror-extensions-langs';

import CodeMirror from '@uiw/react-codemirror';
import { Button, Modal, Row, Col, Tooltip, Collapse, Segmented, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { linter, lintGutter } from "@codemirror/lint";
import { jsonParseLinter } from '@codemirror/lang-json'
import styled from 'styled-components';

import { BaseShape } from './base-shape';
import { getGraphInstance } from '@/utils/flow';
import { getRealType } from '@/utils/node';
import { proxyApi } from '@/api/proxy';
import { CollapsedCard } from '../../../components/response-panel';
import { JSONParse } from '@/utils/common';
import { getAppState } from '@/utils/state';

const Title = styled.p`
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  margin: 5px 0;
  color: #1f3fa0;
  height: 30px;
`;

const typeDefault = {
  integer: 0,
  float: 0.0,
  string: '',
  boolean: true,
  object: {},
}

const Header = styled.div`
  height: 40px;
  width: 100%;
  position: absolute;
  top: -40px;
  left: 0;
  background: whitesmoke;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const EditorBox = styled.div`
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
`;

const Footer = styled.div`
  display: flex;
  flex-direction: row-reverse;
  width: 100%;
  margin-top: 10px;
`;

const TermHeader = ({ setCollapsed, onChange, collapseLevel, tab }) => {
  const handleCollapse = () => {
    if (collapseLevel) {
      setCollapsed(0);
    } else {
      setCollapsed(1);
    }
  }
  return <Header><Segmented options={['输入', '输出', '控制台']} value={tab} onChange={onChange} onClick={() => setCollapsed(1)}/>
    <span onClick={handleCollapse} style={{ marginRight: 10, cursor: 'pointer', userSelect: 'none' }}>
      <span style={{ marginRight: 5 }}>
        {collapseLevel ? '折叠调试' : '展开调试'}
      </span>
      <DoubleRightOutlined style={{ transform: collapseLevel ? 'rotate(90deg)' : 'rotate(-90deg)' }} />
    </span>
  </Header>
}


const EditModal = React.memo(({ defaultValue, onChange, disabled, scriptType, isFullscreen, nodeId, title }) => {
  const [inputs, setInputs] = useState('');
  const [url, setUrl] = useState('');
  const [script] = useState(defaultValue);
  const [outputs, setOutputs] = useState('');
  const [tab, setTab] = useState('输入');
  const scriptRef = useRef(defaultValue);
  const inputRef = useRef('');
  const appType = getAppState('type');

  useEffect(() => {
    if (nodeId) {
      const graph = getGraphInstance();
      const cell = graph.getCellById(nodeId);
      const inputObj = {};
      setUrl(cell.data.url);
      cell.data.inputs.forEach(v => {
        const [type] = getRealType(v.type);
        console.log("celltype", type);
        inputObj[v.name] = typeDefault[type]
      });
      setInputs(JSON.stringify(inputObj, null, '  '));
      inputRef.current = JSON.stringify(inputObj, null, '  ');
    }  }, [nodeId])

  const handleDebug = async () => {
    const input = JSON.parse(inputRef.current);
    try {
      const res = await proxyApi.exec({ url, script: scriptRef.current, scriptType, ...input },);
      if (res.code === 200) {
        delete res.code;
        delete res.message;
        setTab('输出');
        setOutputs(JSON.stringify(res, null, '  '))
      } else {
        setTab('控制台');
        setOutputs(res.message)
      }
    } catch (err) {
      const msg =
        err.response?.data?.info || err.response?.data?.message || err.toString();
      setTab('控制台');
      setOutputs(msg)
    }
  };

  const disableDebug = appType !== 'agent-workflow' && (scriptType === 'GROOVY' || scriptType === 'json');
  console.log('title', title, scriptType);

  let extensions = [langs.python()];
  if (scriptType === 'GROOVY') {
    extensions = [langs.groovy()];
  }
  if (scriptType === 'js') {
    extensions = [langs.javascript()];
  }
  if (scriptType === 'json') {
    extensions = [langs.json(), linter(jsonParseLinter()), lintGutter()];
  }


  return <EditorBox>
    <Title>{title || '脚本'}</Title>
    <CodeMirror
      value={script}
      onChange={(s) => {
        scriptRef.current = s;
        onChange(s)
      }}
      editable={!disabled}
      defaultValue={script}
      basicSetup={{
        tabSize: 4,
      }}
      extensions={extensions}
      height={isFullscreen ? 'calc(100vh - 210px)' : '600px'}
    />
    {disableDebug ? null :
      <CollapsedCard defaultLevel={0} style={{ width: '100%', background: 'whitesmoke' }} stepHeight={isFullscreen ? 240 : 180} header={(collapseLevel, setCollapsed) => <TermHeader tab={tab} onChange={setTab} collapseLevel={collapseLevel} setCollapsed={setCollapsed}></TermHeader>}>
        {(collapseLevel) => <Row>
          <Col span={24}>
            {/* <Title>输入(Inputs)</Title> */}
            <CodeMirror
              editable={tab === '输入'}
              value={tab === '输入' ? inputs : outputs}
              onChange={(s) => {
                if (tab === '输入') {
                  inputRef.current = s;
                }
              }}
              extensions={tab === '控制台' ? [langs.python()] : [langs.json()]}
              height={`${collapseLevel * (isFullscreen ? 180 : 120)}px`}
            />
          </Col>
          <Footer>
            <Tooltip title={disableDebug ? '暂不支持调试' : ''}><Button type="primary" icon={<PlayCircleFilled />} onClick={handleDebug} disabled={disableDebug}>运行调试</Button></Tooltip>
          </Footer>
        </Row>
        }
      </CollapsedCard>
    }
  </EditorBox>
});

const Editor: React.FC = (props: any) => {
  const { value, onChange, disabled, scriptType, nodeId, title } = props;
  const [script, setScript] = useState(value);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(true);

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    if (scriptType === 'json' && !JSONParse(script)) {
      message.error('JSON格式不正确，请检查！');
      return;
    }
    onChange(script);
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // console.log('code props', props, script);

  const fullStyle = isFullscreen ? { height: '100vh', top: 0, left: 0 } : { top: 40 };

  return (
    <>
      <Button type="primary" onClick={showModal}>
        点击{disabled ? '查看' : '编辑'}
      </Button>
      <Modal
        title={
          <>
            <span>{`${title || '脚本'}${disabled ? '查看' : '编辑'}`}</span>{' '}
            <Button
              onClick={toggleFullscreen}
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            ></Button>
          </>
        }
        width={isFullscreen ? '100vw' : 1000}
        style={{ overflow: 'hidden', ...fullStyle }}
        classNames={{ wrapper: isFullscreen ? 'full' : '' }}
        destroyOnClose
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <EditModal nodeId={nodeId} disabled={disabled} defaultValue={script} onChange={setScript} isFullscreen={isFullscreen} scriptType={scriptType} title={title}></EditModal>
      </Modal>
    </>
  );
};

function WithBase(props: any) {
  console.log('code props', props);
  return (
    <BaseShape {...props}>
      <Editor></Editor>
    </BaseShape>
  );
}

export default WithBase;
