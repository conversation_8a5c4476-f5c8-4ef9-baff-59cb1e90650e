// @ts-nocheck
import { useRequest } from 'ahooks';
import { Form, Select } from 'antd';
import { debounce } from 'lodash';
import { useMemo, useState } from 'react';
import qs from 'querystring';

import { AppApi } from '@/api/app';

import { BaseShape } from './base-shape';
import { useGlobalState } from '@/hooks/useGlobalState';
import { $ } from '@/utils/state';
import { getGraphInstance } from '@/utils/flow';
import { getTotalConfig } from '../schemas/base';

const getBusiness = async (groupId) => {
  if (!groupId) {
    return []
  }
  const res = await AppApi.listAppsByGroup(groupId, { appType: 'agent-completion', pageSize: 300 });
  console.log('res', res);
  return (res?.items || []).map(v => ({
    label: v.name,
    value: v.id
  }));
};

const getDetail = async (appId) => {
  if (!appId) {
    return null
  }
  const res = await AppApi.getAppDetail(appId)
  console.log('res', res);
  return res;
};

const SubWorkflowSelect = ({ defaultValue, value, onChange, disabled, businessOptions }) => {
  const search = qs.parse(window.location.search.replace('?', ''));
  search.appId = value || defaultValue;

  return <><Select disabled={disabled} options={businessOptions} showSearch
    value={value}
    onChange={onChange}
    defaultValue={defaultValue}
    filterOption={(input, option) => (option?.label ?? '').includes(input)}
    filterSort={(optionA, optionB) =>
      (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
    }
  />
    {value || defaultValue ?
      <a href={`?${qs.stringify(search)}`} target='_blank'>查看</a>
      : null}
  </>
}

export const SubCompletion: React.FC = (props: any) => {
  const { value, onChange, disabled, collapsed } = props;
  const { globalState } = useGlobalState();
  const { name, totalConfig, outputs } = value;
  const [error, setError] = useState();
  const { group } = globalState;
  const { data: businessOptions } = useRequest(() => getBusiness(group?.id), {
    refreshDeps: [group],
  });
  const controlMap = $.controlMap;
  const graph = getGraphInstance();
  const initialValues = useMemo(() => {
    const obj = {};
    totalConfig.map(param => {
      obj[param.name] = param.value;
    })
    return obj;
  }, [totalConfig]);

  console.log('configs', value, initialValues);

  const [form] = Form.useForm();

  const onFieldsChange = async (items) => {
    if (!items?.length) {
      return;
    }
    const formValues = form.getFieldsValue();
    // 说明是修改appId
    if (items[0].name.includes('appId')) {
      const res = await getDetail(formValues.appId);
      if (!res?.config?.paramsInPrompt?.length) {
        setError('该应用没有变量，请确定是否正确');
        return;
      }
      const newTotalConfig = res.config.paramsInPrompt.map(v => ({
        name: v.key,
        title: v.title,
        label: v.title ? `${v.title}(${v.key})` : v.key,
        shape: 'textarea',
        type: 'string',
      }));
      // 然后将appId加上
      newTotalConfig.push({ name: 'appId', type: 'string', value: formValues.appId });
      setError('');
      form.resetFields();
      form.setFieldValue('appId', formValues.appId);
      console.log("fields", form.getFieldsValue(), newTotalConfig);
      onChange({ totalConfig: newTotalConfig, outputs: [{
        name: 'content',
        title: 'AI回复',
        type: 'string',
      }], name: res.name });
      // 否则是修改参数
    } else {
      const newTotalConfig = totalConfig.map(v => {
        const find = items[0].name.includes(v.name);
        if (find) {
          return ({ ...v, value: items[0].value })
        }
        return v;
      })
      onChange({ totalConfig: newTotalConfig, outputs, name })
    }
  };

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        onFieldsChange={debounce(onFieldsChange, 100)}
        layout={'vertical'}
        initialValues={initialValues}
      >
        <div>
          <Form.Item label="生成式应用" name="appId" rules={[{ required: true }]}>
            <SubWorkflowSelect disabled={disabled} businessOptions={businessOptions}></SubWorkflowSelect>
          </Form.Item>
          {totalConfig.filter(v => v.name !== 'appId').map(control => {
            const { shape, name: controlName } = control
            const key = controlName;
            console.log("configs key", key);
            const ControlComponent = controlMap?.get(shape);
            if (!ControlComponent) {
              console.error('未找到对应的控件:', shape)
              return null
            }
            return (
              <ControlComponent
                key={key as string}
                form={form}
                controlSchema={control}
              />
            )
          })
          }
        </div>
      </Form>
      {error}
    </div>
  );
};

function WithBase(props) {
  const { withDetail, onlyOne } = props;
  return (
    <BaseShape {...props}>
      <SubCompletion withDetail={withDetail} onlyOne={onlyOne}></SubCompletion>
    </BaseShape>
  );
}

export default WithBase;
