// @ts-nocheck
// @ts-nocheck
import { useEffect, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Card, Form, Input, Row, Col, Checkbox, InputNumber, Select } from 'antd';

import { typeOptions } from '@/utils/constant';

import { ParamType } from './base-shape';
import { Enum } from './enum';
import { EnvApi } from '@/api/env';
import { IAppType } from '@/interface/app';
import { getAppId } from '@/utils/state';

const Range = ({ value, onChange }) => {
  const [min, setMin] = useState(value ? value[0] : undefined);
  const [max, setMax] = useState(value ? value[1] : undefined);
  const handleChange = (type, val) => {
    if (type === 'min') {
      setMin(val);
      if (max !== undefined) {
        onChange([val, max])
      }
    } else {
      setMax(val)
      if (min !== undefined) {
        onChange([min, val])
      }
    }
  }
  return <> <InputNumber value={min} style={{ width: 70 }} onChange={v => handleChange('min', v)} /> -  <InputNumber value={max} style={{ width: 70 }} onChange={v => handleChange('max', v)} /></>
}

export const getParamList = (props: any) => {
  const {
    withDetail,
    requireDesc,
    showEnum,
    showVariable,
    disabled,
    noAdd,
    options,
    form,
    useValue,
    nameDisabled,
    typeDisabled,
    showPermanent,
    showRule,
    name,
    isArray,
    noType,
    showDesc,
    showDefault,
    showRange,
    varOptions,
  } = props;

  return (
    <Form.List name={name}>
      {(fields, { add, remove }) => {
        return (
          <div style={{ display: 'flex', rowGap: 16, flexDirection: 'column' }}>
            {fields.map((field) => {
              const name = form?.getFieldValue(['items', field.name, 'name']) || '';
              const nameDisable = Array.isArray(nameDisabled) ? nameDisabled.includes(name) : nameDisabled;
              return (
                <Card
                  size="small"
                  key={field.key}
                  extra={
                    !disabled &&
                    !noAdd && (
                      <CloseOutlined
                        onClick={() => {
                          remove(field.name);
                        }}
                        rev={undefined}
                      />
                    )
                  }
                >
                  <Form.Item
                    label="参数名"
                    name={[field.name, 'name']}
                    rules={[
                      { required: true },
                      {
                        pattern: /^\w*$/,
                        message: '参数名只能为英文',
                      },
                    ]}
                  >
                    <Input
                      disabled={disabled || nameDisable}
                      placeholder='参数名'
                    />
                  </Form.Item>
                  {withDetail &&
                    <Form.Item
                      label="中文名称"
                      name={[field.name, 'title']}
                      rules={[{ required: true }]}
                    >
                      <Input disabled={disabled} />
                    </Form.Item>
                  }
                  {!noType &&
                    <Form.Item
                      label="参数类型"
                      name={[field.name, 'type']}
                      rules={[{ required: true }]}
                    >
                      <ParamType
                        isArray={isArray}
                        options={options || typeOptions}
                        disabled={disabled || typeDisabled}
                      />
                    </Form.Item>
                  }
                  {useValue && (
                    <Form.Item
                      label="参数默认值"
                      name={[field.name, 'value']}
                    >
                      <Input.TextArea placeholder={typeof useValue === 'string' ? useValue : '参数默认值'}></Input.TextArea>
                    </Form.Item>
                  )}
                  {showRange &&
                    <Form.Item
                      label="展示范围"
                      name={[field.name, 'range']}
                    >
                      <Range></Range>
                    </Form.Item>
                  }
                  {!noType && (
                    <>
                      {showEnum && (
                        <Form.Item
                          label="枚举值"
                          name={[field.name, 'enum']}
                        >
                          <Enum disabled={disabled} type={'string'} placeholder=""></Enum>
                        </Form.Item>
                      )}
                    </>
                  )}
                  {showRule && (
                    <Form.Item label="校验规则" name={[field.name, 'rule']} extra={<span>JSON结构，内部参数参考：<a href="https://ant.design/components/form-cn#rule" target="_blank">规则</a></span>}>
                      <Input.TextArea />
                    </Form.Item>
                  )}
                  {(showDesc || requireDesc) &&
                    <Form.Item label="参数描述" name={[field.name, 'description']}
                      rules={[{ required: requireDesc }]}
                    >
                      <Input.TextArea disabled={disabled} />
                    </Form.Item>
                  }
                  {showPermanent &&
                    <Form.Item
                      label="永久链接 (<50MB，大模型输入必勾)"
                      name={[field.name, 'permanent']}
                      valuePropName="checked"
                    >
                      <Checkbox></Checkbox>
                    </Form.Item>
                  }
                  {showVariable &&
                    <>
                      <Form.Item
                        label="引用全局变量"
                        name={[field.name, 'value']}
                      >
                        <Select
                          options={varOptions}
                        />
                      </Form.Item>
                      <Form.Item
                        label="轮次"
                        name={[field.name, 'historyMessageLimit']}
                      >
                        <InputNumber
                          style={{ width: 100 }}
                        />
                      </Form.Item>
                    </>
                  }
                  {showDefault &&
                    <Form.Item
                      label="用作默认值"
                      name={[field.name, 'asDefault']}
                      valuePropName="checked"
                    >
                      <Checkbox></Checkbox>
                    </Form.Item>
                  }

                </Card>
              )
            })}
            {!disabled && !noAdd && (
              <Button
                type="dashed"
                onClick={() =>
                  add({
                    name: `param${fields.length + 1}`,
                    type: 'string',
                  })
                }
                block
              >
                + 添加参数
              </Button>
            )}
          </div>)
      }
      }
    </Form.List>
  );
};
