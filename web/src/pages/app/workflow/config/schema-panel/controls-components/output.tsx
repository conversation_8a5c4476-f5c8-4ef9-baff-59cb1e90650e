// @ts-nocheck
import { useRequest } from 'ahooks';
import { Form, Select, Switch } from 'antd';
import { debounce } from 'lodash';
import { useState } from 'react';
import qs from 'querystring';

import { AppApi } from '@/api/app';

import { BaseShape } from './base-shape';
import { useGlobalState } from '@/hooks/useGlobalState';
import { $ } from '@/utils/state';
import { getGraphInstance } from '@/utils/flow';
import { getTotalConfig } from '../schemas/base';
import { formatNodeValue, formatTypeValue } from '@/utils/common';

const getBusiness = async (groupId) => {
  if (!groupId) {
    return []
  }
  const res = await AppApi.listAppsByGroup(groupId, { appType: 'workflow', pageSize: 300 });
  console.log('res', res);
  return (res?.items || []).map(v => ({
    label: v.name,
    value: v.id
  }));
};

export const SubWorkflow: React.FC = (props: any) => {
  const { value, onChange, disabled, collapsed } = props;
  const { totalConfig, id, fallbackOut } = value;
  const { globalState } = useGlobalState();
  const [fallOut, setFallOut] = useState(fallbackOut);
  const { group } = globalState;
  const controlMap = $.controlMap;
  const graph = getGraphInstance();
  console.log('configs....', totalConfig, fallbackOut);

  const [form] = Form.useForm();

  const handleFallOutChange = (checked) => {
    setFallOut(checked);
    console.log('checked', checked);
    if (!checked) {
      onChange({ ...value, fallbackOut: null });
    } else {
      // 先看所有totalConfig中是否有value，如果不全为空
      const find = totalConfig.find(v => v.value);
      if (find) {
        const fallbackOut = totalConfig.reduce((acc, v) => {
          acc[v.name] = v.value;
          return acc;
        }, {});
        onChange({ ...value, fallbackOut });
      }
    }
  }

  const onFieldsChange = async (items) => {
    if (!items?.length) {
      return;
    }
    const formValues = form.getFieldsValue();
    const newTotalConfig = totalConfig.map(v => {
      const find = items[0].name.includes(v.name);
      if (find) {
        return ({ ...v, value: items[0].value })
      }
      return v;
    })
    // 把newTotalConfig转换成map，key为name，value为value，根据类型转换
    const fallbackOut = newTotalConfig.reduce((acc, v) => {
      acc[v.name] = formatTypeValue(v.value, v.type);
      return acc;
    }, {});
    console.log("formValues", items, fallbackOut, newTotalConfig);
    onChange({ ...value, totalConfig: newTotalConfig, fallbackOut })
  };

  return (
    <div className="param-form">
      <div>
        <span style={{ marginRight: 10, color: '#666' }}>启用兜底(fallbackOut)</span>
        <Switch checked={fallOut} onChange={handleFallOutChange}></Switch>
        <p style={{ color: '#999', fontSize: 12 }}>兜底开启后，出错后不会停止工作流，而是将下面的默认值作为输入参数传递给下一个节点</p>
      </div>
      {fallOut && <Form
        form={form}
        autoComplete="off"
        onFieldsChange={debounce(onFieldsChange, 100)}
        layout={'vertical'}
        initialValues={{ items: value }}
      >
        <div>
          {totalConfig.map(control => {
            const { shape, name: controlName } = control
            const key = (controlName);
            console.log("configs key", key);
            const ControlComponent = controlMap?.get(shape);
            if (!ControlComponent) {
              console.error('未找到对应的控件:', shape)
              return null
            }
            return (
              <ControlComponent
                key={key as string}
                form={form}
                controlSchema={control}
              />
            )
          })
          }
        </div>
      </Form>
      }
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <SubWorkflow></SubWorkflow>
    </BaseShape>
  );
}

export default WithBase;
