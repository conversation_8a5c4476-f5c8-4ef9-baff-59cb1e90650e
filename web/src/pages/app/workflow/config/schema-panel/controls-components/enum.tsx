import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input, Space } from 'antd';

export const Enum: React.FC = (props: any) => {
  const { value, onChange, disabled, type } = props;

  const [form] = Form.useForm();

  const formatType = (item) => {
    if (type === 'number' || type === 'integer' || type === 'float') {
      item.value = Number(item.value);
    }
    return item;
  };

  const formatValues = (val) => {
    if (val) {
      return val.map((v) => {
        if (typeof v !== 'object') {
          return ({ value: v });
        }
        return v;
      })
    }
    return [];
  };

  const handleBlur = () => {
    const values = form.getFieldsValue();
    console.log("blur", values);
    const result = (values?.items || []).filter(v => v.value).map((v) => ({ ...v, value: formatType(v).value }));
    console.log('values', values, result, type);
    onChange(result);
  }

  const content = (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onBlur={handleBlur}
        initialValues={{ items: formatValues(value) }}
      >
        <Form.List name="items">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Space
                  key={key}
                  style={{ display: 'flex', marginBottom: 0 }}
                  align="center"
                >
                  <Space.Compact
                    key={key}
                    direction="vertical"
                    style={{ display: 'flex', marginBottom: 0 }}
                  >
                    <Form.Item
                      {...restField}
                      name={[name, 'label']}
                      style={{ marginBottom: 2 }}
                      rules={[{ required: true, message: '缺少枚举值' }]}
                    >
                      <Input placeholder="标签" disabled={disabled} />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'value']}
                      rules={[{ required: true, message: '缺少枚举值' }]}
                    >
                      <Input placeholder="值" disabled={disabled} />
                    </Form.Item>
                  </Space.Compact>
                  {!disabled && <MinusCircleOutlined onClick={() => {
                    remove(name);
                    handleBlur();
                  }} />}
                </Space>
              ))}
              {!disabled && (
                <Form.Item style={{ marginBottom: 0 }}>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                  >
                    增加一个枚举值
                  </Button>
                </Form.Item>
              )}
            </>
          )}
        </Form.List>
      </Form>
    </div>
  );
  return content;
};
