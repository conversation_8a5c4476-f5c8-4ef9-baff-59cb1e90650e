// @ts-nocheck
import { useRequest } from 'ahooks';
import { Form, Input, Select } from 'antd';
import { debounce, pick } from 'lodash';
import { useEffect, useRef, useState, useMemo } from 'react';

import { SkyeyeApi } from '@/api/skyeye';

import { BaseShape } from './base-shape';
import { getParamList } from './param-list';
import styled from 'styled-components';

const Title = styled.div`
  color: #666;
  height: 24px;
  margin: 5px 0;
`;

const typeMap = {
  Number: 'float',
  Boolean: 'boolean',
  String: 'string',
  Object: 'object',
  Array_Number: 'array<float>',
  Array_Boolean: 'array<boolean>',
  Array_String: 'array<string>',
  Array_Object: 'array<object>',
};

const getType = (type) => {
  return typeMap[type];
};

const getList = async (data: { limit: number; offset: number, name?: string, platformName?: string, tag?: string }) => {
  const resources = await SkyeyeApi.getList(data);
  return resources && resources.map((v) => ({
    ...v,
    label: `${v.name}(${v.platformName})`,
    value: v.id,
  }));
};

const getPlatformList = async (data: { limit: number; offset: number, name?: string }) => {
  const resources = await SkyeyeApi.getPlatformList(data);
  return resources && resources.map((v) => ({
    label: `${v.description}`,
    value: v.name,
  }));
};

const getDetail = async (id: number) => {
  const config = await SkyeyeApi.getDetail({ atomicServiceId: id });
  const inputs = config.inputParams.map((v) => ({
    name: v.name,
    description: v.desc,
    type: getType(v.fieldType),
    value: v.value,
    title: v.desc,
  }));
  const outputs = config.outputParams.map((v) => ({
    name: v.name,
    description: v.desc,
    type: getType(v.fieldType),
    value: v.value,
    title: v.desc,
  }));
  return { inputs, outputs, invokeTopic: config.invokeTopic, tag: config.tag };
};

export const Skyeye: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const [name, setName] = useState<string>('');
  // const [atomicList, setAtomicList] = useState<any>();

  const { data: atomicList, run: refreshOpts } = useRequest((_tag, _platformName) => {
    const tag = _tag ?? value?.props?.tag ?? '';
    const platformName = _platformName ?? value?.props?.platformName ?? '';
    return getList({ limit: 20, offset: 0, name, platformName, tag });
  }, { refreshDeps: [name], manual: true });

  const { data: platformOptions } = useRequest(() => {
    return getPlatformList({ limit: 1000, offset: 0 });
  });

  const atomicConfig = useRef({ name: 'atomicId', value: '' });

  const [form] = Form.useForm();

  useEffect(() => {
    const obj = {};
    if (value) {
      let atomicId = '';
      if (value.configs) {
        const find = value.configs.find((v) => v.name === 'atomicId');
        if (find) {
          atomicConfig.current = find;
          atomicId = find.value;
        }
      }
      console.log('vaue', value.props, value.inputs, value.vars);
      form.setFieldsValue({ ...value, atomicId, ...(value.props || {}) });
      getOptions(obj);
      refreshOpts();
    }
  }, []);

  const onFieldsChange = (v) => {
    const values = form.getFieldsValue();
    console.log('v', v, values);
    if (v[0].name[0] === 'tag' || v[0].name[0] === 'platformName') {
      refreshOpts(values.tag || '', values.platformName || '');
      const newValues = {...values, inputs: [], outputs: [], 'atomicId': ''};
      form.setFieldsValue(newValues);
      onChange({ ...newValues, props: pick(values, ['platformName', 'tag']), vars: [], configs: [{ name: 'url', value: '' }, { name: 'method', value: 'POST'}] });
    } else if (v[0].name[0] !== 'atomicId') {
      onChange({ ...value, ...(pick(values, ['inputs', 'outputs'])), props: pick(values, ['platformName', 'tag']), vars: [] });
    }
  };

  const getOptions = async (values) => {
    const defaultValue = { ...values };
    if (defaultValue.id) {
      const config = await getDetail(defaultValue.id);
      // 存储当前id
      atomicConfig.current.value = defaultValue.id;
      form.setFieldValue('inputs', config.inputs);
      form.setFieldValue('outputs', config.outputs);
      const { invokeTopic, tag, ...other } = config;
      onChange({
        ...other,
        name: defaultValue.name,
        configs: [
          ...value.configs
            .filter((v) => v.name !== 'atomicId')
            .map((v) => {
              if (v.name === 'url') {
                v.value = `aio://ASYNC_CRAWLER_TASK/${invokeTopic}:${tag}`;
              }
              return v;
            }),
          atomicConfig.current,
        ],
        vars: []
      });
    }
    return defaultValue;
  };

  const handleAlgChange = (v, op) => {
    getOptions(op);
  };

  const debounceFetcher = useMemo(() => {
    const updateName = (value: string) => {
      setName(value)
    };
    return debounce(updateName, 400);
  }, []);


  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onFieldsChange={debounce(onFieldsChange, 100)}
        initialValues={{ items: value }}
      >
        <div>
          <Form.Item label="平台选择" name="platformName">
            <Select
              disabled={disabled}
              options={platformOptions}
              allowClear
              showSearch
            />
          </Form.Item>
          <Form.Item label="Tag" name="tag">
            <Input></Input>
          </Form.Item>
          <Form.Item label="能力选择" name="atomicId" rules={[{ required: true }]}>
            <Select
              disabled={disabled}
              options={atomicList}
              onChange={handleAlgChange}
              showSearch
              labelInValue
              filterOption={false}
              onSearch={debounceFetcher}
            />
          </Form.Item>
          <Title>输入参数</Title>
          {getParamList({
            noAdd: true,
            nameDisabled: true,
            withDetail: true,
            name: 'inputs',
            useValue: true,
          })}
          <Title>输出参数</Title>
          {getParamList({
            noAdd: true,
            nameDisabled: true,
            withDetail: true,
            name: 'outputs',
            useValue: true,
          })}
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <Skyeye></Skyeye>
    </BaseShape>
  );
}

export default WithBase;
