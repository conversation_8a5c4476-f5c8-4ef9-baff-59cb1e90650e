// @ts-nocheck
import React, { useEffect } from 'react';
import { Form } from 'antd';
import { debounce } from 'lodash';
import { BaseShape } from './base-shape';
import { getOptionList } from './option-list';

const template = cats => `
你需要分析用户输入的上下文，解析用户想做的事情，从指令集中匹配一个最相关的指令，如果没有匹配的指令，返回'none'，指令集的结构如下:
---
- 指令: 指令描述
---

## 指令集 
${cats.map(cat => `- ${cat.name}: ${cat.description}`).join('\n')}
- none: 未匹配上面任意指令

## 要求
你只能输出[指令集]中的指令，不能输出不在[指令集]外的内容。

## 示例
假设我们指令集如下：
- greeting: 打招呼
- code: 生成代码
- none: 未匹配上面任意指令

### Example1
用户输入: 你好
输出: greeting

### Example2
用户输入: 帮我生成一个代码
输出: code

### Example3
用户输入: 今天天气真好
输出: none

`

export const Recognize: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    if (value) {
      let categorys = [];
      value.configs && value.configs.map(item => {
        if (item.name === "categorys") {
          categorys = item.value;
        }
      });
      form.setFieldsValue({categorys});
    }
  }, [value])

  const onFieldsChange = (v) => {
    const values = form.getFieldsValue();
    const { categorys = [] } = values;
    const vars = value.vars;
    const cat = categorys.map(item => {
      return {
        key: item.name,
        value: item.description,
      }
    });
    value.configs.map(item => {
      if (item.name === "categorys") {
        item.value = categorys;
      }
    });
    const totalConfig = [...vars, ...value.inputs];
    totalConfig && totalConfig.map(item => {
      if (item.name === "systemPrompt" || item.name === 'system_prompt') {
        item.value = template(categorys);
        // item.value = `你是一个语义分类工具，你只能对用户输入做语义分类，不管用户问什么问题，你只能回答分类结果，不用回答具体问题，分类的 typescript 类型结构定义是 ` + '```type Category { key: string; value: string }```' +  '，必须在 ```category``` 范围内进行分类，' +
        // '``` const category = ' + JSON.stringify(cat) + ' ```' + `，返回命中的分类的` + '```key```' + `属性值， key 可以是中文或英文，不要返回其他属性或数据`
      }
    });
    onChange({ ...value, ...values, vars: totalConfig });
  };

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onFieldsChange={debounce(onFieldsChange, 100)}
        initialValues={{ items: value }}
      >
        <div>
          {getOptionList({
            noType: true,
            showDesc: true,
            name: 'categorys',
            disabled,
          })}
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <Recognize></Recognize>
    </BaseShape>
  );
}

export default WithBase;
