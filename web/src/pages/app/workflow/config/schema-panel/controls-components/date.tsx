import { BaseShape } from './base-shape';
import { <PERSON><PERSON>, Card, DatePicker, Form, Input, Select, Typography } from 'antd';
import dayjs from 'dayjs';

const Param: React.FC = (props: any) => {
  const { value, onChange } = props;
  console.log('props', props)
  const handleChange = val => {
    console.log('val', val);
    onChange(val ? val.valueOf() : null);
  }

  return (
    <div className='param-form'>
      <DatePicker showTime value={value ? dayjs(value): null}onChange={handleChange}></DatePicker>
    </div>
  );
};

function WithBase(props: any) {
  return <BaseShape {...props}>
    <Param></Param>
  </BaseShape>
}


export default WithBase;