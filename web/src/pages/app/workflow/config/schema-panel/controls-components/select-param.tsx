// @ts-nocheck
import { BaseShape, ParamType } from './base-shape';
import { Card, Form, Input, Button, Select } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { typeOptions } from '@/utils/constant';

const Param: React.FC = (props: any) => {
  const { value, onChange } = props;
  const [form] = Form.useForm();
  const typeValue = Form.useWatch('type', form);

  const onFieldsChange = () => {
    const values = form.getFieldsValue();
    // 如果是变更的 type
    onChange(values);
  }

  return (
    <div className='param-form'>
      <Form
        form={form}
        autoComplete="off"
        onFieldsChange={onFieldsChange}
        layout={'horizontal'}
        initialValues={value}
      >
        <Form.Item name="type" label="类型">
          <Select options={typeOptions} ></Select>
        </Form.Item>
        <Form.List name="inputs">
          {(fields, { add, remove }) => (
            <div style={{ display: 'flex', rowGap: 16, flexDirection: 'column' }}>
              {fields.map((field, idx) => {
                console.log('filed', field);
                return (
                  <Card
                    size="small"
                    title={`选择 ${field.name}`}
                    key={field.key}
                    extra={
                      idx >= 1 && typeValue !== 'boolean' ?
                        <CloseOutlined
                          onClick={() => {
                            remove(field.name);
                          }} rev={undefined} />
                        : null
                    }
                  >
                    <Form.Item required label="参数名" name={[field.name, 'name']}>
                      <Input />
                    </Form.Item>
                    <Form.Item required label="参数类型" name={[field.name, 'type']}>
                      <ParamType options={typeOptions} />
                    </Form.Item>
                  </Card>
                )
              })}

              {typeValue !== 'boolean' ?
                <Button type="dashed" onClick={() => add({
                  name: `选择 ${fields.length}`,
                })} block>
                  + 添加选择
                </Button>
                : null}
            </div>
          )}
        </Form.List>
      </Form>
    </div >
  );
};

function WithBase(props) {
  return <BaseShape {...props}>
    <Param></Param>
  </BaseShape>
}


export default WithBase;