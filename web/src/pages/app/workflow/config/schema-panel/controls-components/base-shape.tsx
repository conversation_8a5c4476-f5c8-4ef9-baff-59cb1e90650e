// @ts-nocheck
import type { NsJsonSchemaForm } from '@antv/xflow';
import { FormItemWrapper } from '@antv/xflow';
import { Form, Input, InputNumber, Select } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';

import { getRealType } from '@/utils/node';
import { JSONParse } from '@/utils/common';

export const BaseShape: React.FC<NsJsonSchemaForm.IControlProps> = (props) => {
  const { controlSchema, children } = props;
  const { required, tooltip, extra, name, label, disabled, options } = controlSchema;
  const rule = JSONParse(controlSchema?.originData?.rule);
  let rules = [];
  if (controlSchema.shape === 'Number' && rule && rule.max) {
    console.log('rule', rule);
    controlSchema.originData.max = rule.max;
  } else if (controlSchema?.originData?.rule && rule) {
    rules = [JSONParse(controlSchema.originData.rule)];
  }
  return (
    <>
      <FormItemWrapper schema={controlSchema}>
        {({ hidden, initialValue }) => {
          return (
            <Form.Item
              name={name}
              label={label}
              initialValue={initialValue}
              tooltip={tooltip}
              extra={extra}
              required={required}
              hidden={hidden}
              rules={rules}
            >
              {controlSchema.shape === 'Number' ? (
                <InputNumber disabled={disabled} {...(controlSchema.originData || {})} />
              ) : (
                // 这里的组件可以拿到onChange和value
                React.cloneElement(children, {
                  disabled,
                  options,
                  ...(controlSchema.originData || {}),
                })
              )}
            </Form.Item>

          );
        }}
      </FormItemWrapper>
    </>
  );
};

export const ParamType = ({ value, onChange, options, isArray, ...rest }) => {
  const [currentType, setCurrentType] = useState(value);
  const [dateType, innerType] = useMemo(() => {
    console.log('param...TYpe', currentType);
    return getRealType(currentType);
  }, [currentType]);

  useEffect(() => {
    setCurrentType(value);
  }, [value]);


  const handleTypeChange = (type) => {
    if (type === 'array') {
      setCurrentType('array<string>');
      onChange('array<string>');
    } else {
      setCurrentType(type);
      onChange(type);
    }
  };

  const handleInnerTypeChange = (type) => {
    setCurrentType(`array<${type}>`);
    onChange(`array<${type}>`);
  };

  return (
    <>
      <Select
        {...rest}
        options={options}
        value={dateType}
        onChange={handleTypeChange}
        disabled={dateType === 'array' && isArray || rest?.disabled}
        showSearch
        filterOption={(input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
        }
        filterSort={(optionA, optionB) =>
          (optionA?.label ?? '')
            .toLowerCase()
            .localeCompare((optionB?.label ?? '').toLowerCase())
        }
      />
      {dateType === 'array' ? (
        <Select
          {...rest}
          options={options}
          value={innerType}
          onChange={handleInnerTypeChange}
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          filterSort={(optionA, optionB) =>
            (optionA?.label ?? '')
              .toLowerCase()
              .localeCompare((optionB?.label ?? '').toLowerCase())
          }
        />
      ) : null}
    </>
  );
};
