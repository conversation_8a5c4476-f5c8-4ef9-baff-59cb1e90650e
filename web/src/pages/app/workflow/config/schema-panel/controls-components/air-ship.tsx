// @ts-nocheck
import { useRequest } from 'ahooks';
import { Collapse, Form, Input, Select, Tree } from 'antd';
import { debounce } from 'lodash';
import { useEffect, useRef, useState } from 'react';

import { AirshipApi } from '@/api/airship';
import { CioApi } from '@/api/cio';

import { BaseShape } from './base-shape';
import { Param } from './param';
import { getParamList } from './param-list';
import styled from 'styled-components';

const { Search } = Input;

const base = 'https://qa-gogo.igame.163.com';

const Title = styled.div`
  color: #666;
  height: 24px;
  margin: 5px 0;
`;

const typeMap = {
  string: () => 'string',
  date: () => 'datetime',
  number: () => 'integer',
  object: () => 'object',
  integer: () => 'integer',
  float: () => 'float',
  nos: () => 'NosKey',
};

const getType = (item) => {
  const isArray = item.fieldFlag;
  let type = item.dataTypeDetailDto.name;
  type = typeMap[type] ? typeMap[type](item) : 'any';
  return isArray ? `array<${type}>` : type;
};

const getAlgos = async () => {
  const resources = await AirshipApi.getAlgos();
  console.log('args', resources);
  return resources.map((v) => ({
    ...v,
    label: `${v.serviceName}(${v.algName})`,
    value: v.id,
  }));
};

const getConfig = async (id) => {
  const config = await AirshipApi.getConfig(id);
  const inputs = config.requestParam.dataFieldDtoList.map((v) => ({
    name: v.fieldName,
    description: v.description,
    type: getType(v),
  }));
  const outputs = config.responseParam.dataFieldDtoList.map((v) => ({
    name: v.fieldName,
    description: v.description,
    type: getType(v),
  }));
  return { inputs, outputs };
};

export const AirShip: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const { data: algOptions } = useRequest(getAlgos);
  const [alg, setAlg] = useState({});
  const algConfig = useRef({ name: 'algId', value: '' });

  const [form] = Form.useForm();

  useEffect(() => {
    const obj = {};
    console.log('value', value);
    if (value && algOptions) {
      let algId = '';
      if (value.configs) {
        const find = value.configs.find((v) => v.name === 'algId');
        if (find) {
          algConfig.current = find;
          algId = find.value;
        }
      }
      form.setFieldsValue({ ...value, algId });
      getOptions(obj);
    }
  }, [algOptions]);

  const onFieldsChange = (v) => {
    console.log('v', v, form.getFieldsValue());
    if (v[0].name[0] !== 'algId') {
      onChange(form.getFieldsValue());
    }
  };

  const getOptions = async (values) => {
    const defaultValue = { ...values };
    if (defaultValue.id) {
      const config = await getConfig(defaultValue.id);
      console.log('config', config, defaultValue);
      // 存储当前id
      algConfig.current.value = defaultValue.id;
      form.setFieldValue('inputs', config.inputs);
      form.setFieldValue('outputs', config.outputs);
      onChange({
        ...config,
        name: defaultValue.serviceName,
        configs: [
          ...value.configs
            .filter((v) => v.name !== 'algId')
            .map((v) => {
              if (v.name === 'url') {
                v.value = `aio://AIRSHIP_JNI_ASYNC_SERVICE/${defaultValue.code}`;
              }
              return v;
            }),
          algConfig.current,
        ],
      });
    }
    return defaultValue;
  };

  const handleAlgChange = (v, op) => {
    console.log('v', v, op);
    getOptions(op);
    setAlg(op);
  };

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onFieldsChange={debounce(onFieldsChange, 100)}
        initialValues={{ items: value }}
      >
        <div>
          <Form.Item label="" name="algId" rules={[{ required: true }]}>
            <Select
              disabled={disabled}
              options={algOptions}
              showSearch
              onChange={handleAlgChange}
              filterOption={(input, option) => {
                console.log('inpupt', input, option);
                return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
              }}
              filterSort={(optionA, optionB) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
            />
          </Form.Item>
          {/* <div>{alg.description}</div> */}
          <Title>输入参数</Title>
          {getParamList({
            noAdd: true,
            nameDisabled: true,
            withDetail: true,
            name: 'inputs',
          })}
          <Title>输出参数</Title>
          {getParamList({
            noAdd: true,
            nameDisabled: true,
            withDetail: true,
            name: 'outputs',
          })}
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <AirShip></AirShip>
    </BaseShape>
  );
}

export default WithBase;
