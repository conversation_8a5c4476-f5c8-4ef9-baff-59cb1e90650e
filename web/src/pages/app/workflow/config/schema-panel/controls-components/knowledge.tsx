// @ts-nocheck
import { useRequest } from 'ahooks';
import { Form, Select, InputNumber } from 'antd';
import { debounce } from 'lodash';
import { useEffect, useRef, useState, useMemo } from 'react';
import { useGlobalState } from '@/hooks/useGlobalState';
import { KnowledgeApi } from '@/api/knowledge';
import { DocumentApi } from '@/api/document';
import { BaseShape } from './base-shape';
import styled from 'styled-components';

const Title = styled.div`
  color: #666;
  height: 24px;
  margin: 5px 0;
`;

const getKnowledge = async (workspaceId, groupId) => {
  const knowledges = await KnowledgeApi.list(workspaceId, groupId);
  return knowledges.map((v) => ({
    ...v,
    label: `${v.name}(${v.documentCount})`,
    value: v.collectionId,
  }));
};

const getDocument = async (workspaceId, knowledgeId, keyword) => {
  const documents = await DocumentApi.list(workspaceId, knowledgeId, keyword);
  return documents.map((v) => ({
    ...v,
    label: `${v.title}`,
    value: v.doc_id,
  }));
};

const config = {
  inputs: [{
    name: 'input',
    title: "输入信息",
    description: '输入信息',
    type: 'string',
  }],
  outputs: [{
    name: 'data',
    title: "片段",
    description: '文档信息',
    type: 'array<object>',
  },{
    name: 'text',
    title: "文本",
    description: '文档信息',
    type: 'string',
  }],
}

export const Knowledge: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const { globalState } = useGlobalState();
  const { group, workspace } = globalState;
  const [form] = Form.useForm();
  const knowledgeConfig = useRef({ name: 'knowledgeId', value: '' });
  const [docKeyword ,setDocKeyword] = useState<string>();
  const [docOptions, setDocOptions] = useState<any>();

  const { data: knowledgeOptions } = useRequest(() => {
    if (workspace?.id && group?.id) {
      return getKnowledge(workspace?.id, group?.id)
    }
  }, {
    refreshDeps: [workspace?.id, group?.id],
  });

  const { run } = useRequest(() => {
    if (workspace?.id && knowledgeConfig.current.value) {
      return getDocument(workspace?.id, knowledgeConfig.current.value, docKeyword)
    }
  }, {
    refreshDeps: [workspace?.id, knowledgeConfig.current.value, docKeyword],
    onSuccess: (data) => {
      setDocOptions(data);
    }
  });

  useEffect(() => {
    if (value && knowledgeOptions) {
      let collectionId;
      let docIds = [];
      let count = 5;
      if (value.configs) {
        const find = value.configs.find((v) => v.name === 'knowledgeId');
        const knowledge = knowledgeOptions.find((v) => v.id === find.value);
        if (find) {
          knowledgeConfig.current.value = find.value;
          collectionId = knowledge?.collectionId;
          run();
        }
        value.configs.map(item => {
          if (item.name === 'count') {
            count = item.value || 5;
          }
          if (item.name === 'docIds') {
            docIds = item.value;
          }
        });
      }
      form.setFieldsValue({ ...value, ...config, collectionId, docIds, count });
    }
  }, [knowledgeOptions]);

  const onFieldsChange = (v) => {
    if (v[0].name[0] !== 'collectionId') {
      const values = form.getFieldsValue();
      onChange({
        ...value,
        ...values,
        configs: value.configs.map(item => {
          if (item.name === 'docIds') {
            item.value = values.docIds;
          } else if (item.name === 'count') {
            item.value = `${values.count}`;
          }
          return item;
        }),
        vars: [{
          name:  "collectionId",
          title: "文档表ID",
          type:  "string",
          value: `${values.collectionId}`
        },{
          name:  "count",
          title: "分片数量",
          type:  "string",
          value: `${values.count}`
        },{
          name:  "docId",
          title: "文档ID",
          type:  "string",
          value: values.docIds ? values.docIds.map(item => item.value).join(',') : " "
        }]
      });
    }
  };

  const handleKnowledgeChange = (v, knowledge) => {
    const defaultValue = { ...knowledge };
    if (defaultValue.id) {
      knowledgeConfig.current.value = defaultValue.id;
      const values = form.getFieldsValue();
      form.setFieldsValue({ docIds: []});
      setDocKeyword('');
      let newVars = value.vars;
      if (newVars.length > 0) {
        newVars = newVars.map(item => {
          if (item.name === 'collectionId') {
            item.value = knowledge.collectionId;
          }
          if (item.name === 'docId') {
            item.value = " ";
          }
          return item;
        })
      } else {
        newVars = [{
          name:  "collectionId",
          title: "文档表ID",
          type:  "string",
          value: knowledge.collectionId
        },{
          name:  "count",
          title: "分片数量",
          type:  "string",
          value: values.count,
        },{
          name:  "docId",
          title: "文档ID",
          type:  "string",
          value: " "
        }]
      }
      onChange({
        ...config,
        docIds: [],
        name: defaultValue.name,
        configs: value.configs.map(item => {
          if (item.name === 'knowledgeId') {
            item.value = defaultValue.id;
          } else if (item.name === 'docIds') {
            item.value = [];
          } else if (item.name === 'count') {
            item.value = values.count;
          }
          return item;
        }),
        vars: newVars
      });
    }
    return defaultValue;
  };

  const debounceFetcher = useMemo(() => {
    const updateKeyword = (value: string) => {
      setDocKeyword(value)
    };
    return debounce(updateKeyword, 400);
  }, []);

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onFieldsChange={debounce(onFieldsChange, 100)}
        initialValues={{ items: value }}
      >
        <div>
          <Form.Item label="" name="collectionId" rules={[{ required: true }]}>
            <Select
              disabled={disabled}
              options={knowledgeOptions}
              showSearch
              onChange={handleKnowledgeChange}
              filterOption={(input, option) => {
                return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
              }}
              filterSort={(optionA, optionB) =>
                (optionA?.label ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.label ?? '').toLowerCase())
              }
            />
          </Form.Item>
          <Title>文档</Title>
          <Form.Item label="" name="docIds">
            <Select
              disabled={disabled}
              options={docOptions}
              showSearch
              labelInValue
              mode='multiple'
              filterOption={false}
              onSearch={debounceFetcher}
            />
          </Form.Item>
          <Title>分片数量</Title>
          <Form.Item initialValue={5} label="" name="count" rules={[{ required: true }]}>
            <InputNumber style={{width: '100%'}} min={1} max={10} step={1} />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <Knowledge></Knowledge>
    </BaseShape>
  );
}

export default WithBase;
