// @ts-nocheck
import { useRequest } from 'ahooks';
import { Form, Select, InputNumber, Tag } from 'antd';
import { debounce } from 'lodash';
import { useEffect, useRef, useState, useMemo } from 'react';
import { useGlobalState } from '@/hooks/useGlobalState';
import { KnowledgeApi } from '@/api/knowledge';
import { NewKnowledge } from '@/api/new-knowledge';
import { DocumentApi } from '@/api/document';
import { BaseShape } from './base-shape';
import styled from 'styled-components';
import { ComponentApi } from '@/api/component';
import { NODE_CODE } from '@/utils/constant';
import PluginsCard from '@/pages/app/components/agent-dev/mods/plugins-card/plugin-card-chatflow';

const Title = styled.div`
  color: #666;
  height: 24px;
  margin: 5px 0;
`;


const config = {
  inputs: [{
    name: 'input',
    title: "输入信息",
    description: '输入信息',
    type: 'string',
  }],
  outputs: [{
    name: 'data',
    title: "片段",
    description: '文档信息',
    type: 'array<object>',
  }, {
    name: 'text',
    title: "文本",
    description: '文档信息',
    type: 'string',
  }],
}

const getTools = tools => {
  if (!tools || tools.length === 0) {
    return undefined;
  }
  return tools.map(item => {
    return item.function;
  })
}
export const Knowledge: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const { globalState } = useGlobalState();
  const { group, workspace } = globalState;
  const [form] = Form.useForm();
  const knowledgeConfig = useRef({ name: '知识库（新）', value: '' });
  const [docKeyword, setDocKeyword] = useState<string>();
  const [docOptions, setDocOptions] = useState<any>();
  console.log('value123...', value);

  useEffect(() => {
    if (value) {
      const tools = value.vars.find(item => item.name === 'tools');
      const tools_base = value.vars.find(item => item.name === 'tools_base');
      const oxIdList = value.vars.find(item => item.name === 'oxIdList');
      form.setFieldsValue({
        tools: tools?.value,
        tools_base: tools_base?.value,
        oxIdList: oxIdList?.value,
      });
    }
  }, [value]);

  const onFieldsChange = (v) => {
    console.log('v...', v);
    const values = form.getFieldsValue();
    console.log('values...', values);
    const tools = value.vars.find(item => item.name === 'tools') || { name: 'tools', value: '' };
    const tools_base = value.vars.find(item => item.name === 'tools_base') || { name: 'tools_base', value: '' };
    const oxIdList = value.vars.find(item => item.name === 'oxIdList') || { name: 'oxIdList', value: '' };
    tools_base.value = values.tools_base && values.tools_base.length > 0 ? values.tools_base : [];
    tools.value = getTools(tools_base.value);
    oxIdList.value = values.oxIdList && values.oxIdList.length > 0 ? values.oxIdList : undefined;
    onChange({
      ...value,
      vars: [...value.vars.filter(item => item.name !== 'tools' && item.name !== 'oxIdList'), tools, oxIdList, tools_base],
    });
  }


  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onFieldsChange={debounce(onFieldsChange, 100)}
        initialValues={{ items: value }}
      >
        <div>
          <Form.Item label="" name="tools_base" rules={[{ required: true }]}>
            <PluginsCard></PluginsCard>
          </Form.Item>
          <Title>OX的Id列表(输入完一个id后回车)</Title>
          <Form.Item label="" name="oxIdList">
            <Select
              disabled={disabled}
              options={[]}
              open={false}
              mode="tags"
            />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <Knowledge></Knowledge>
    </BaseShape>
  );
}

export default WithBase;
