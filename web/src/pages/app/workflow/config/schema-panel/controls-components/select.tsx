import type { NsJsonSchemaForm } from '@antv/xflow';
import { FormItemWrapper } from '@antv/xflow';
import { Form, Select } from 'antd';
import React from 'react';

const SelectComp: React.FC<NsJsonSchemaForm.IControlProps> = (props) => {
  const { controlSchema } = props;
  const { required, tooltip, extra, name, label, options, disabled } = controlSchema;

  return (
    <FormItemWrapper schema={controlSchema}>
      {({ hidden, initialValue }) => {
        return (
          <Form.Item
            name={name}
            label={label}
            initialValue={initialValue}
            tooltip={tooltip}
            extra={extra}
            required={required}
            hidden={hidden}
          >
            {/* 这里的组件可以拿到onChange和value */}
            <Select options={options} allowClear disabled={disabled}></Select>
          </Form.Item>
        );
      }}
    </FormItemWrapper>
  );
};

export default SelectComp;
