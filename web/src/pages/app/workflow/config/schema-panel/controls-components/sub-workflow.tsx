// @ts-nocheck
import { useRequest } from 'ahooks';
import { Form, Select } from 'antd';
import { debounce } from 'lodash';
import { useState } from 'react';
import qs from 'querystring';

import { AppApi } from '@/api/app';

import { BaseShape } from './base-shape';
import { useGlobalState } from '@/hooks/useGlobalState';
import { $ } from '@/utils/state';
import { getGraphInstance } from '@/utils/flow';
import { getTotalConfig } from '../schemas/base';

const getBusiness = async (groupId) => {
  if (!groupId) {
    return []
  }
  const res = await AppApi.listAppsByGroup(groupId, { appType: 'workflow', pageSize: 300 });
  console.log('res', res);
  return (res?.items || []).map(v => ({
    label: v.name,
    value: v.id
  }));
};

const getDetail = async (appId) => {
  if (!appId) {
    return null
  }
  const res = await AppApi.getAppDetail(appId)
  console.log('res', res);
  return res;
};

const SubWorkflowSelect = ({ defaultValue, value, onChange, disabled, businessOptions }) => {
  const search = qs.parse(window.location.search.replace('?', ''));
  search.appId = value || defaultValue;

  return <><Select disabled={disabled} options={businessOptions} showSearch
    value={value}
    onChange={onChange}
    defaultValue={defaultValue}
    filterOption={(input, option) => (option?.label ?? '').includes(input)}
    filterSort={(optionA, optionB) =>
      (optionA?.label ?? '').toLowerCase().localeCompare((optionB?.label ?? '').toLowerCase())
    }
  />
    {value || defaultValue ?
      <a href={`?${qs.stringify(search)}`} target='_blank'>查看</a>
      : null}
  </>
}

export const SubWorkflow: React.FC = (props: any) => {
  const { value, onChange, disabled, collapsed } = props;
  const { totalConfig, id, appId } = value;
  const { globalState } = useGlobalState();
  const [error, setError] = useState();
  const { group } = globalState;
  const { data: businessOptions } = useRequest(() => getBusiness(group?.id), {
    refreshDeps: [group],
  });
  const controlMap = $.controlMap;
  const graph = getGraphInstance();
  console.log('configs', totalConfig, appId);

  const [form] = Form.useForm();

  const onFieldsChange = async (items) => {
    if (!items?.length) {
      return;
    }
    const formValues = form.getFieldsValue();
    console.log("formValues", items, formValues);
    // 说明是修改appId
    if (items[0].name.includes('appId')) {
      const res = await getDetail(formValues.appId);
      if (!res?.config?.inputs?.length) {
        setError('该工作流还未发布，请先发布子工作流');
        onChange({ error: true, appId: value.appId, totalConfig: [], id });
      } else {
        const newTotalConfig = getTotalConfig({ ...res.config, id }, graph, { exclude: ['oneWay', 'workflowId'] });
        setError('');
        form.resetFields();
        form.setFieldValue('appId', formValues.appId);
        console.log("fields", form.getFieldsValue());
        onChange({ ...res.config, name: res.name, appId: formValues.appId, totalConfig: newTotalConfig, id });
      }
      // 否则是修改参数
    } else {
      const newTotalConfig = totalConfig.map(v => {
        const find = items[0].name.includes(v.name);
        if (find) {
          return ({ ...v, value: items[0].value })
        }
        return v;
      })
      onChange({ ...value, totalConfig: newTotalConfig, change: items[0], id })
    }
  };

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        onFieldsChange={debounce(onFieldsChange, 100)}
        layout={'vertical'}
        initialValues={{ items: value }}
      >
        <div>
          <Form.Item label="子工作流" name="appId" rules={[{ required: true }]}>
            <SubWorkflowSelect defaultValue={value?.appId} disabled={disabled} businessOptions={businessOptions}></SubWorkflowSelect>
          </Form.Item>
          {totalConfig.map(control => {
            const { shape, name: controlName } = control
            const key = (controlName + appId);
            console.log("configs key", key);
            const ControlComponent = controlMap?.get(shape);
            if (!ControlComponent) {
              console.error('未找到对应的控件:', shape)
              return null
            }
            return (
              <ControlComponent
                key={key as string}
                form={form}
                controlSchema={control}
              />
            )
          })
          }
        </div>
      </Form>
      {error}
    </div>
  );
};

function WithBase(props) {
  const { withDetail, onlyOne } = props;
  return (
    <BaseShape {...props}>
      <SubWorkflow withDetail={withDetail} onlyOne={onlyOne}></SubWorkflow>
    </BaseShape>
  );
}

export default WithBase;
