// @ts-nocheck
import { useRequest } from 'ahooks';
import { Form, Select, Switch, Button, Image } from 'antd';
import { debounce } from 'lodash';
import { useState, useEffect } from 'react';
import qs from 'querystring';
import { Modal } from 'antd';

import { AppApi } from '@/api/app';

import { BaseShape } from './base-shape';
import { useGlobalState } from '@/hooks/useGlobalState';
import { $ } from '@/utils/state';
import { getGraphInstance } from '@/utils/flow';
import { getTotalConfig } from '../schemas/base';
import { formatNodeValue, formatTypeValue } from '@/utils/common';
import { AiMvApi } from '@/api/ai-mv';
import { WorkflowPlayground } from '@/pages/preview/workflow-playground';
import { WorkflowPlaygroundContent } from '@/pages/preview/workflow-playground-content';

export const AiMv: React.FC = (props: any) => {
  const { value, onChange, disabled, collapsed } = props;
  const { totalConfig, id, originValue } = value;
  const [templates, setTemplates] = useState<Array<{ id: string, name: string }>>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>(originValue?.template || '');
  const { globalState } = useGlobalState();
  const { group } = globalState;
  const controlMap = $.controlMap;
  const graph = getGraphInstance();
  console.log('configs....', totalConfig, originValue);

  const [form] = Form.useForm();

  // 获取模板列表
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const res = await AiMvApi.getTemplates(); // 替换成实际的API调用
        console.log("res", res);
        setTemplates(res);
      } catch (error) {
        console.error('获取模板列表失败:', error);
      }
    };
    fetchTemplates();
  }, []);

  const handleTemplateChange = (value) => {
    setSelectedTemplate(value);
    const find = templates.find(t => t.name === value);
    if (find) {
      // 把totalConfig中的width和height设置成find.inputs中的width和height
      const replaces = {
        width: 'width',
        height: 'height',
        background: 'defaultBackgroundNosKey',
        // stickerImage: 'defaultBackgroundImgUrl',
        template: 'templateNosKey',
      }
      const defaultReplace = {
        outputBucket: 'jd-music-vip-aisong',
        coverOutputBucket: 'yyimgs',
      }
      const formData = {};
      const newTotalConfig = totalConfig.map(v => {
        if (replaces[v.name]) {
            formData[v.name] = find[replaces[v.name]];
            return { ...v, value: find[replaces[v.name]] };
        }
        if (defaultReplace[v.name]) {
          formData[v.name] = defaultReplace[v.name];
          return { ...v, value: defaultReplace[v.name] };
        }
        return v;
      })
      form.setFieldsValue(formData);
      onChange({ totalConfig: newTotalConfig, originValue: { ...originValue, template: value } });
    }
  }

  const onFieldsChange = async (items) => {
    if (!items?.length) {
      return;
    }
    const formValues = form.getFieldsValue();
    const newTotalConfig = totalConfig.map(v => {
      const find = items[0].name.includes(v.name);
      if (find) {
        return ({ ...v, value: items[0].value })
      }
      return v;
    })
    // 把newTotalConfig转换成map，key为name，value为value，根据类型转换
    onChange({ ...value, totalConfig: newTotalConfig })
  };
  const showTemplatePreview = () => {
    const formValues = form.getFieldsValue();
    console.log("formValues", formValues, originValue);
    Modal.info({
      title: '模板预览',
      width: 1200,
      content: (
        <div style={{ overflow: 'auto' }}>
          {/* 这里展示模板详情 */}
          <WorkflowPlaygroundContent
            hideDefaultInputs={true}
            defaultInputs={formValues}
            appId={originValue.appId}
            showHeader={false}
          />
        </div>
      ),
    });
  };


  return (
    <div className="param-form">
      <Form.Item label="选择模板" name="template">
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Select
            allowClear
            style={{ width: '100%' }}
            placeholder="请选择模板"
            options={templates.map(t => ({ label: t.name, value: t.name }))}
            value={selectedTemplate}
            onChange={(value) => handleTemplateChange(value)}
          />
        <Image style={{ height: 40 }}src={templates.find(t => t.name === selectedTemplate)?.defaultBackgroundImgUrl} />
        </div>
        <Button
            type="link"
            onClick={showTemplatePreview}
            disabled={!selectedTemplate}
          >
            预览模板
          </Button>
      </Form.Item>
      <Form
        form={form}
        autoComplete="off"
        onFieldsChange={debounce(onFieldsChange, 100)}
        layout={'vertical'}
        initialValues={{ items: value }}
      >
        <div>
          {totalConfig.map(control => {
            const { shape, name: controlName } = control
            const key = (controlName);
            const ControlComponent = controlMap?.get(shape);
            if (!ControlComponent) {
              console.error('未找到对应的控件:', shape)
              return null
            }
            return (
              <ControlComponent
                key={key as string}
                form={form}
                controlSchema={control}
              />
            )
          })
          }
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <AiMv></AiMv>
    </BaseShape>
  );
}

export default WithBase;
