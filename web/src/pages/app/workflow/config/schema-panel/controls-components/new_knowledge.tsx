// @ts-nocheck
import { useRequest } from 'ahooks';
import { Form, Select, InputNumber, Tag } from 'antd';
import { debounce } from 'lodash';
import { useEffect, useRef, useState, useMemo } from 'react';
import { useGlobalState } from '@/hooks/useGlobalState';
import { KnowledgeApi } from '@/api/knowledge';
import { NewKnowledge } from '@/api/new-knowledge';
import { DocumentApi } from '@/api/document';
import { BaseShape } from './base-shape';
import styled from 'styled-components';

const Title = styled.div`
  color: #666;
  height: 24px;
  margin: 5px 0;
`;

const getKnowledge = async (workspaceId, groupId) => {
  const knowledges = await NewKnowledge.getKnowledgeList({
    workspaceId,
    pageSize: 1000,
    groupId
  });
  console.log('knowledges', knowledges);
  return knowledges.values.map((v) => ({
    ...v,
    label: <span><Tag color={typeMap[v.type]?.color || 'blue'}>{typeMap[v.type]?.label || v.type}</Tag>{v.name}({v.documentCount})</span>,
    value: v.id,
  }));
};

const typeMap = {
  text: { label: '文本', color: 'blue' },
  table: { label: '表格', color: 'green' },
  cio: { label: 'CIO', color: 'purple' },
}

const getDocument = async (workspaceId, knowledgeId, keyword) => {
  const documents = await NewKnowledge.getDocumentList({
    knowledgeId,
    content: keyword,
    pageSize: 1000,
  });
  console.log('documents', documents);
  return documents.values.filter(v => keyword ? v.name.includes(keyword) : true).map((v) => ({
    ...v,
    label: v.name,
    value: v.knowledgeItemId,
  }));
};

const config = {
  inputs: [{
    name: 'input',
    title: "输入信息",
    description: '输入信息',
    type: 'string',
  }],
  outputs: [{
    name: 'data',
    title: "片段",
    description: '文档信息',
    type: 'array<object>',
  }, {
    name: 'text',
    title: "文本",
    description: '文档信息',
    type: 'string',
  }],
}

export const Knowledge: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const { globalState } = useGlobalState();
  const { group, workspace } = globalState;
  const [form] = Form.useForm();
  const knowledgeConfig = useRef({ name: '知识库（新）', value: '' });
  const [docKeyword, setDocKeyword] = useState<string>();
  const [docOptions, setDocOptions] = useState<any>();

  useEffect(() => {
    if (value) {
      const values = value.vars.reduce((acc, item) => {
        return {
          ...acc,
          [item.name]: item.value
        };
      }, {});
      const docIds = value.configs.find(item => item.name === 'docIds');
      const knowledgeItems = value.vars.find(item => item.name === 'knowledgeItems');
      const knowledgeItemIds = (knowledgeItems?.value || [])?.map(item => ({
        label: item.name?.props?.children || item.name,
        value: item.id
      }));
      // console.log('docIds', value, docIds, knowledgeItems, knowledgeItemIds);
      knowledgeConfig.current.value = values.knowledgeId;

      form.setFieldsValue({ ...values, docIds: docIds?.value ?? knowledgeItemIds ?? [] });
    }
  }, [value]);

  const { data: knowledgeOptions } = useRequest(() => {
    if (workspace?.id && group?.id) {
      return getKnowledge(workspace?.id, group?.id)
    }
  }, {
    refreshDeps: [workspace?.id, group?.id],
  });

  const { run } = useRequest(() => {
    if (workspace?.id && knowledgeConfig.current.value) {
      return getDocument(workspace?.id, knowledgeConfig.current.value, docKeyword)
    }
  }, {
    refreshDeps: [workspace?.id, knowledgeConfig.current.value, docKeyword],
    onSuccess: (data) => {
      setDocOptions(data);
    }
  });


  const onFieldsChange = (v) => {
    const params = [...value.inputs, ...value.vars];
    console.log('v', v);
    const values = form.getFieldsValue();
    const newValues = {...values};
    // 防止跟handleKnowledgeChange的方法冲突
    if (v[0].name[0] === 'knowledgeId') {
      knowledgeConfig.current.value = v[0].value;
      newValues.docIds = [];
      newValues.useAll = true;
    }
    // 如果选中了文件，说明useAll=false
    if (values.docIds && values.docIds.length > 0) {
      newValues.useAll = false;
      newValues.knowledgeItems = values.docIds.map(item => ({name: item.label, id: item.value}));
    } else {
      newValues.useAll = true;
    }
    onChange({
      ...value,
      configs: [...value.configs.filter(item => item.name !== 'docIds'), { name: 'docIds', value: (newValues.docIds || []) }],
      vars: params.map(item => {
        item.value = newValues[item.name] ?? item.value;
        return item;
      })
    });
  };

  // const handleKnowledgeChange = (id, value) => {
  //   console.log('value', value);
  //   const params = [...value.inputs, ...value.vars];
  //   // 重新发起请求获取所有的文档列表
  //   knowledgeConfig.current.value = id;
  //   knowledgeConfig.current.name = value.name;
  //   // run();
  // }


  const debounceFetcher = useMemo(() => {
    const updateKeyword = (value: string) => {
      setDocKeyword(value)
    };
    return debounce(updateKeyword, 400);
  }, []);

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onFieldsChange={debounce(onFieldsChange, 100)}
        initialValues={{ items: value }}
      >
        <div>
          <Form.Item label="" name="knowledgeId" rules={[{ required: true }]}>
            <Select
              disabled={disabled}
              options={knowledgeOptions}
              showSearch
              // onChange={handleKnowledgeChange}
              filterOption={(input, option) => {
                return (option?.name ?? '').toLowerCase().includes(input.toLowerCase());
              }}
              filterSort={(optionA, optionB) =>
                (optionA?.name ?? '')
                  .toLowerCase()
                  .localeCompare((optionB?.name ?? '').toLowerCase())
              }
            />
          </Form.Item>
          <Title>文档（可以不填，不填默认使用所有文档）</Title>
          <Form.Item label="" name="docIds">
            <Select
              disabled={disabled}
              options={docOptions}
              showSearch
              labelInValue
              mode='multiple'
              filterOption={false}
              onSearch={debounceFetcher}
            />
          </Form.Item>
          <Title>分片数量（返回的分片数量）</Title>
          <Form.Item label="" name="limit" rules={[{ required: true }]}>
            <InputNumber style={{ width: '100%' }} min={1} max={10} step={1} />
          </Form.Item>
          <Title>检索方式</Title>
          <Form.Item label="" name="searchType" rules={[{ required: true }]}>
            <Select options={[{ label: '混合检索', value: "MIX" }, { label: '全文检索', value: 'FULL_TEXT' }, { label: '语义检索', value: 'SEMANTIC' }]} />
          </Form.Item>
          <Title>相似度（大于下面相似度的才会返回）</Title>
          <Form.Item label="" name="similarity" rules={[{ required: true }]}>
            <InputNumber style={{ width: '100%' }} min={0} max={1} step={0.1} />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <Knowledge></Knowledge>
    </BaseShape>
  );
}

export default WithBase;
