// @ts-nocheck
import { useState, useEffect } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Card, Collapse, Form, Input } from 'antd';

import { typeOptions } from '@/utils/constant';

import { BaseShape, ParamType } from './base-shape';
import { Enum } from './enum';
import { getParamList } from './param-list';
import { toLower } from 'lodash';
import { EnvApi } from '@/api/env';
import { IAppType } from '@/interface';
import { getAppId } from '@/utils/state';

const DataTypes = {
  LONG: 'integer',
  DOUBLE: 'float',
  DATE: 'string',
}

export const Param: React.FC = (props: any) => {
  const {
    value,
    onChange,
    onlyOne,
    collapsed,
    showVariable,
    disabled
  } = props;

  const [form] = Form.useForm();
  const [varOptions, setVarOptions] = useState([]);

  const onFieldsChange = (changedFields, allFields) => {
    // 说明是修改变量，需要联动修改title和type
    const values = form.getFieldsValue();
    if (changedFields[0]?.name[2] === 'value' && showVariable) {
      const curValue = values[changedFields[0].name[0]][changedFields[0].name[1]].value;
      const selectedVar = varOptions.find(v => v.value === curValue);
      // 更新title字段
      if (selectedVar?.label && selectedVar?.dataType) {
        values[changedFields[0].name[0]][changedFields[0].name[1]].title = selectedVar.label;
        values[changedFields[0].name[0]][changedFields[0].name[1]].type = DataTypes[selectedVar.dataType] || toLower(selectedVar.dataType);
        // form.setFieldsValue(values)
      }
      console.log('selectedVar...', selectedVar, values);
    }
    onChange((values?.items || []));
  };

  useEffect(() => {
    if (!showVariable) {
      return;
    }
    EnvApi.getNonUserVariables(IAppType.AgentWorkflow, getAppId()).then(res => {
      console.log('res...', res);
      setVarOptions(res?.variables?.map(v => ({ label: v.title, value: `$${v.key}`, dataType: v.dataType })) || []);
    });
  }, [showVariable]);

  const content = (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        onFieldsChange={onFieldsChange}
        layout={'horizontal'}
        initialValues={{ items: value }}
        disabled={disabled}
      >
        {getParamList({ ...props, name: 'items', noAdd: onlyOne, form, varOptions })}
      </Form>
    </div>
  );
  if (collapsed) {
    return (
      <Collapse
        items={[
          {
            label: '参数编辑',
            children: content,
          },
        ]}
      ></Collapse>
    );
  }
  return content;
};

export function ParamWithDetail(props) {
  return WithBase({ ...props, withDetail: true });
}

export function ParamOnlyOne(props) {
  return WithBase({ ...props, onlyOne: true });
}

export function InputParam(props) {
  return WithBase({ ...props, input: true });
}

export function InputParamWithoutType(props) {
  return WithBase({ ...props, input: true, noType: true, });
}

function WithBase(props) {
  const { withDetail, onlyOne, input, noType, options, showDesc, requireDesc, showPermanent } = props;
  const inputProps = input ? {
    withDetail: true, showDefault: true, showDesc: true,
    onlyOne: true,
  } : {};

  return (
    <BaseShape {...props}>
      <Param withDetail={withDetail} onlyOne={onlyOne} noType={noType} {...inputProps} options={options} showDesc={showDesc} requireDesc={requireDesc}></Param>
    </BaseShape>
  );
}

export default WithBase;
