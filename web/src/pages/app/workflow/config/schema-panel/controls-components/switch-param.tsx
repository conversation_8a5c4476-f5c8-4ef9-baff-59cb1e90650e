import { CloseOutlined } from '@ant-design/icons';
import { Button, Card, Form, Input, Select } from 'antd';

import { BaseShape } from './base-shape';

const Param: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const [form] = Form.useForm();
  const typeValue = Form.useWatch('type', form);

  const onFieldsChange = (fields: any) => {
    const values = form.getFieldsValue();
    // 如果是变更的 type
    if (fields[0].name == 'type') {
      if (fields[0].value === 'boolean') {
        values.outputs = [
          { name: 'true', title: '真' },
          { name: 'false', title: '假' },
        ];
      } else {
        values.outputs = [
          {
            name: 'default',
            title: '默认值',
          },
        ];
      }
      form.setFieldValue('outputs', values.outputs);
    }
    console.log('file', values);
    console.log('filed', fields);
    onChange(values);
  };

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        onFieldsChange={onFieldsChange}
        layout={'horizontal'}
        initialValues={value}
      >
        <Form.Item name="type" label="类型">
          <Select
            disabled={disabled}
            options={[
              { name: 'string', value: 'string' },
              { name: 'boolean', value: 'boolean' },
              { name: 'number', value: 'number' },
            ]}
          ></Select>
        </Form.Item>
        <Form.List name="outputs">
          {(fields, { add, remove }) => (
            <div style={{ display: 'flex', rowGap: 16, flexDirection: 'column' }}>
              {fields.map((field, idx) => {
                console.log('filed', field);
                return (
                  <Card
                    size="small"
                    title={`条件 ${field.name}`}
                    key={field.key}
                    extra={
                      idx >= 1 && typeValue !== 'boolean' ? (
                        <CloseOutlined
                          onClick={() => {
                            remove(field.name);
                          }}
                          rev={undefined}
                        />
                      ) : null
                    }
                  >
                    <Form.Item label="匹配值" name={[field.name, 'name']}>
                      <Input disabled={disabled || typeValue === 'boolean' || idx < 1} />
                    </Form.Item>
                  </Card>
                );
              })}

              {typeValue !== 'boolean' && !disabled ? (
                <Button
                  type="dashed"
                  onClick={() =>
                    add({
                      name: `条件 ${fields.length}`,
                      type: 'string',
                    })
                  }
                  block
                >
                  + 添加条件
                </Button>
              ) : null}
            </div>
          )}
        </Form.List>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <Param></Param>
    </BaseShape>
  );
}

export default WithBase;
