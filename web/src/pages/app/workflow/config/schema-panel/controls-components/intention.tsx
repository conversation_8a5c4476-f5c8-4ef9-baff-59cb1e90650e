// @ts-nocheck
import React, { useEffect } from 'react';
import { Form } from 'antd';
import { debounce } from 'lodash';
import { BaseShape } from './base-shape';
import { getOptionList } from './option-list';

const template = cats => `
你需要分析用户输入的上下文，解析用户想做的事情，从指令集中匹配一个最相关的指令，如果没有匹配的指令，返回'none'，指令集的结构如下:
---
- 指令: 指令描述
---

## 指令集 
${cats.map(cat => `- ${cat.name}: ${cat.description}`).join('\n')}
- none: 未匹配上面任意指令

## 要求
你只能输出[指令集]中的指令，不能输出不在[指令集]外的内容。

## 示例
假设我们指令集如下：
- greeting: 打招呼
- code: 生成代码
- none: 未匹配上面任意指令

### Example1
用户输入: 你好
输出: greeting

### Example2
用户输入: 帮我生成一个代码
输出: code

### Example3
用户输入: 今天天气真好
输出: none

`

export const Recognize: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    if (value) {
      let categorys = [];
      value.configs && value.configs.map(item => {
        if (item.name === "categorys") {
          categorys = item.value;
        }
      });
      form.setFieldsValue({categorys});
    }
  }, [value])

  const onFieldsChange = (v) => {
    const values = form.getFieldsValue();
    const { categorys = [] } = values;
    const vars = value.vars;
    const cat = categorys.map(item => item.name);
    value.configs = [...value.configs.filter(item => item.name !== 'categorys'), { name: 'categorys', value: categorys }];
    vars && vars.map(item => {
      if (item.name === "systemPrompt" || item.name === 'system_prompt') {
        item.value = template(categorys);
      }
    });
    const newOutputs = value.outputs.filter(item => (item.name === 'content' || item.name === 'none'));
    if (newOutputs.length === 1) {
      newOutputs.unshift({ name: 'none', type: '_logic_', title: '未匹配' });
    }
    value.outputs = [...categorys.map(item => {
      const find = value.outputs.find(output => output.name === item.name);
      return ({ name: item.name, type: '_logic_', title: item.name, id: find?.id || '' })
    }), ...newOutputs];
    onChange({ ...value, ...values, vars});
  };

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onFieldsChange={debounce(onFieldsChange, 100)}
        initialValues={{ items: value }}
      >
        <div>
          {getOptionList({
            noType: true,
            showDesc: true,
            name: 'categorys',
            disabled,
          })}
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <Recognize></Recognize>
    </BaseShape>
  );
}

export default WithBase;
