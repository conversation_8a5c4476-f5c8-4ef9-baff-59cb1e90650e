// @ts-nocheck
import { useRequest } from 'ahooks';
import { Button, Collapse, Form, Input, Select, Tree } from 'antd';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';

import { CioApi } from '@/api/cio';
import { JSONParse } from '@/utils/common';
import { request } from '@/utils/request';

import { BaseShape } from './base-shape';

const { Search } = Input;

const base = 'https://qa-gogo.igame.163.com';

const getBusiness = async () => {
  // const resources = await request({
  //   url: `${base}/api/content/pool/business/resourcetype/all/get`,
  // });
  const resources = await CioApi.getBusinessResourceTypes();
  return resources?.config || [];
};

const getConfig = async (values) => {
  // const configs = await request({
  //   url: `${base}/api/content/pool/config/get?business=${business}&resourceType=${resourceType}&rolePermission=false&configTypes=PROPERTY_TYPE&poolCode=${poolCode}&merge=true`,
  // });
  const configs = await CioApi.getPoolConfig(values);
  const config =
    (configs ? configs : {})?.fieldGroupList?.map((v) => v.fieldList).flat() || [];
  return config;
};

const getPool = async (values) => {
  // const { business, resourceType } = values;
  // const pools = await request({
  //   url: `${base}/api/content/pool/list?business=${business}&resourceType=${resourceType}&limit=10000`,
  // });
  const pools = await CioApi.getPools(values);
  return (pools || []).map((v) => ({
    label: v?.name,
    value: v?.poolCode,
  }));
};

const typeMap = {
  string: () => 'string',
  STRING: () => 'string',
  DATE: () => 'datetime',
  number: () => 'integer',
  OBJECT: () => 'object',
  LONG: () => 'integer',
  INTEGER: () => 'integer',
  DOUBLE: () => 'float',
  TAG: () => 'array<string>',
  ENUM: (item: any) => {
    let subType = item.subType;
    if (item?.enumConfig?.options) {
      subType = typeof item?.enumConfig?.options[0].value;
    }
    if (subType !== 'ENUM') {
      return getType(item, subType);
    }
    return 'any';
  },
  ARRAY: (item: any) => {
    let subType = item.subType;
    if (item?.enumConfig?.options) {
      subType = typeof item?.enumConfig?.options[0].value;
    }
    return `array<${getType(item, subType)}>`;
  },
};

const getType = (item, type) => {
  return typeMap[type] ? typeMap[type](item) : 'any';
};

export const ParamTree: React.FC = (props: any) => {
  const { configs, onChange, checks } = props;
  const [search, setSearch] = useState([]);

  useEffect(() => {
    setSearch('');
  }, [configs]);

  const treeData = useMemo(
    () =>
      configs
        .filter((v) => v.name.includes(search))
        .map((v) => ({
          title: `${v.name}(${getType(v, v.type)})`,
          key: v.code,
        })),
    [configs, search],
  );

  const handleSearch = (ev) => {
    setSearch(ev.target.value);
  };

  const handleCheck = (_, check) => {
    console.log('check', check);
    let newChecks = [...checks];
    if (check.checked || check.selected) {
      newChecks.push(check.node.key);
    } else {
      newChecks = newChecks.filter((v) => v !== check.node.key);
    }
    newChecks = Array.from(new Set(newChecks));
    onChange(newChecks);
  };

  if (!configs.length) {
    return null;
  }

  return (
    <div
      className="param-form"
      style={{
        padding: '10px',
        background: '#fff',
        marginTop: '10px',
      }}
    >
      <Search style={{ marginBottom: 8 }} placeholder="搜索" onChange={handleSearch} />
      <Tree
        height={500}
        selectable={false}
        checkable
        treeData={treeData}
        onSelect={handleCheck}
        onCheck={handleCheck}
        checkedKeys={checks}
      ></Tree>
    </div>
  );
};

export const CIO: React.FC = (props: any) => {
  const { value, onChange, disabled, collapsed } = props;
  const { data: businessOptions } = useRequest(getBusiness);
  const [resOption, setResOptions] = useState([]);
  const [poolOptions, setPoolOptions] = useState([]);
  const [configs, setConfigs] = useState([]);
  const [checks, setChecks] = useState([]);

  const [form] = Form.useForm();

  const getOptions = async (values) => {
    const defaultValue = { ...values };
    if (values.business) {
      const find = businessOptions.find((v) => v.value === values.business);
      if (find) {
        setResOptions(find.children);
        defaultValue.resourceType = defaultValue.resourceType || find.children[0].value;
      }
    }
    if (defaultValue.resourceType && defaultValue.business) {
      const pools = await getPool(defaultValue);
      setPoolOptions(pools);
      defaultValue.poolCode = defaultValue.poolCode || pools[0].value;
    }
    if (defaultValue.resourceType && defaultValue.business && defaultValue.poolCode) {
      const config = await getConfig(defaultValue);
      setConfigs(config);
    }
    return defaultValue;
  };

  const handleConfigsChange = (c) => {
    setChecks(c);
    const outputs = c.map((key) => {
      const find = configs.find((v) => v.code === key);
      return {
        name: key,
        title: find.name,
        code: find.code,
        type: getType(find, find.type),
        path: find.path,
      };
    });
    handleChange('outputs', outputs);
  };

  useEffect(() => {
    const obj = {};
    if (value && businessOptions) {
      (value?.configs || []).forEach((v) => {
        obj[v.name] = v.value;
      });
      form.setFieldsValue(obj);
      const outputs = value.configs.find(v => v.name === 'outputs')?.value || null;
      if (outputs) {
        setChecks(outputs.map((v) => v.name));
      }
      getOptions(obj);
    }
  }, [businessOptions]);

  const handleChange = (type, data) => {
    const values = type === 'form' ? data : form.getFieldsValue();
    let outputs = [];
    if (type === 'outputs') {
      outputs = data;
    }
    const configs = Object.keys(values).map((key) => ({
      name: key,
      type: 'string',
      value: values[key],
    }));
    configs.push({
      name: 'outputs',
      value: outputs
    });
    onChange({
      configs
    });
  };

  const onFieldsChange = async (items) => {
    const values = form.getFieldsValue();
    let defaultValue = { ...values };
    // 说明在修改business
    if (items[0].name == 'business') {
      defaultValue = await getOptions({
        business: items[0].value,
      });
    }
    if (items[0].name == 'resourceType') {
      defaultValue = await getOptions({
        ...values,
        resourceType: items[0].value,
      });
    }
    if (items[0].name == 'poolCode') {
      defaultValue = await getOptions({
        ...values,
        poolCode: items[0].value,
      });
    }
    setChecks([]);
    form.setFieldsValue(defaultValue);
    handleChange('form', defaultValue);
  };

  const content = (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        onFieldsChange={debounce(onFieldsChange, 100)}
        layout={'horizontal'}
        initialValues={{ items: value }}
      >
        <div>
          <Form.Item label="业务线" name="business" rules={[{ required: true }]}>
            <Select disabled={disabled} options={businessOptions} />
          </Form.Item>
          <Form.Item label="资源类型" name="resourceType" rules={[{ required: true }]}>
            <Select disabled={disabled} options={resOption} />
          </Form.Item>
          <Form.Item label="资源池" name="poolCode" rules={[{ required: true }]}>
            <Select disabled={disabled} options={poolOptions} />
          </Form.Item>
          <ParamTree
            configs={configs}
            onChange={handleConfigsChange}
            checks={checks}
          ></ParamTree>
        </div>
      </Form>
    </div>
  );
  if (collapsed) {
    return (
      <Collapse
        items={[
          {
            label: '参数编辑',
            children: content,
          },
        ]}
      ></Collapse>
    );
  }
  return content;
};

function WithBase(props) {
  const { withDetail, onlyOne } = props;
  return (
    <BaseShape {...props}>
      <CIO withDetail={withDetail} onlyOne={onlyOne}></CIO>
    </BaseShape>
  );
}

export default WithBase;
