// @ts-nocheck
// @ts-nocheck
import { CloseOutlined } from '@ant-design/icons';
import { Button, Card, Form, Input, Checkbox, InputNumber } from 'antd';

import { typeOptions } from '@/utils/constant';

import { ParamType } from './base-shape';
import { Enum } from './enum';
import { useState } from 'react';

const Range = ({ value, onChange }) => {
  const [min, setMin] = useState(value ? value[0] : undefined);
  const [max, setMax] = useState(value ? value[1] : undefined);
  const handleChange = (type, val) => {
    if (type === 'min') {
      setMin(val);
      if (max !== undefined) {
        onChange([val, max])
      }
    } else {
      setMax(val)
      if (min !== undefined) {
        onChange([min, val])
      }
    }
  }
  return <> <InputNumber value={min} style={{ width: 70 }} onChange={v => handleChange('min', v)} /> -  <InputNumber value={max} style={{ width: 70 }} onChange={v => handleChange('max', v)} /></>
}

export const getOptionList = (props: any) => {
  const {
    withDetail,
    showEnum,
    disabled,
    noAdd,
    options,
    useValue,
    nameDisabled,
    typeDisabled,
    showRule,
    name,
    isArray,
    noType,
    showDesc,
    showDefault,
    showRange
  } = props;

  return (
    <Form.List name={name}>
      {(fields, { add, remove }) => (
        <div style={{ display: 'flex', rowGap: 16, flexDirection: 'column' }}>
          {fields.map((field) => {
            return (
              <Card
                size="small"
                key={field.key}
                extra={
                  !disabled &&
                  !noAdd && (
                    <CloseOutlined
                      onClick={() => {
                        remove(field.name);
                      }}
                      rev={undefined}
                    />
                  )
                }
              >
                <Form.Item
                  label="名称"
                  name={[field.name, 'name']}
                  rules={[
                    { required: true },
                    // {
                    //   pattern: /^\w*$/,
                    //   message: '参数名只能为英文',
                    // },
                  ]}
                >
                  <Input disabled={disabled || nameDisabled} placeholder='参数名' />
                </Form.Item>
                {withDetail &&
                  <Form.Item
                    label="中文名称"
                    name={[field.name, 'title']}
                    rules={[{ required: true }]}
                  >
                    <Input disabled={disabled} />
                  </Form.Item>
                }
                {!noType &&
                  <Form.Item
                    label="参数类型"
                    name={[field.name, 'type']}
                    rules={[{ required: true }]}
                  >
                    <ParamType
                      isArray={isArray}
                      options={options || typeOptions}
                      disabled={disabled || typeDisabled}
                    />
                  </Form.Item>
                }
                {useValue && (
                  <Form.Item
                    label="参数默认值"
                    name={[field.name, 'value']}
                  >
                    <Input.TextArea placeholder={typeof useValue === 'string' ? useValue : '参数默认值'}></Input.TextArea>
                  </Form.Item>
                )}
                {showRange &&
                  <Form.Item
                    label="展示范围"
                    name={[field.name, 'range']}
                  >
                    <Range></Range>
                  </Form.Item>
                }
                {!noType && (
                  <>
                    {showEnum && (
                      <Form.Item
                        label="枚举值"
                        name={[field.name, 'enum']}
                      >
                        <Enum disabled={disabled} type={'string'} placeholder=""></Enum>
                      </Form.Item>
                    )}
                  </>
                )}
                {showRule && (
                  <Form.Item label="校验规则" name={[field.name, 'rule']} extra={<span>参考：<a href="https://ant.design/components/form-cn#rule" target="_blank">https://ant.design/components/form-cn#rule</a></span>}>
                    <Input.TextArea />
                  </Form.Item>
                )}
                {showDesc &&
                  <Form.Item label="描述" name={[field.name, 'description']}>
                    <Input.TextArea disabled={disabled} />
                  </Form.Item>
                }
                {showDefault &&
                  <Form.Item
                    label="用作默认值"
                    name={[field.name, 'asDefault']}
                    valuePropName="checked"
                  >
                    <Checkbox></Checkbox>
                  </Form.Item>
                }

              </Card>
            )
          })}
          {!disabled && !noAdd && (
            <Button
              type="dashed"
              onClick={() =>
                add({
                  name: `param${fields.length + 1}`,
                  type: 'string',
                })
              }
              block
            >
              + 添加分类
            </Button>
          )}
        </div>
      )}
    </Form.List>
  );
};
