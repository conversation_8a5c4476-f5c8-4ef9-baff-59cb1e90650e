import { MinusCircleOutlined, PlusOutlined, RobotOutlined, CloseCircleFilled, QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Form, Input, InputNumber, Select, Space, Tooltip } from 'antd';
import { ModelSelect } from '@/components/model-select';
import { BaseShape } from './base-shape';
import { LFormSliderInput } from '@/components/form/form-items/form-range';
import { useEffect, useMemo, useRef } from 'react';
import { useSafeState } from 'ahooks';
import { getModelConfigSchema } from '@/utils/model-helper';
import { TextTip } from '@/components/text-tip';
import { getDefaultModel } from '@/utils/common';
import { debounce, pick } from 'lodash';
import { useGlobalState } from '@/hooks/useGlobalState';
import { GlobalModel, IAppType } from '@/interface';
import { LFormSwitch } from '@/components/form';
import ModelModal from '@/components/model-modal';

const default_model_params = [
  {
    "name": "modelName",
    "type": "string",
    "value": getDefaultModel().modelName
  },
  {
    "name": "system_prompt",
    "type": "string",
    "value": "你是一个智能助理，你需要友好地与用户进行对话。"
  },
  {
    "name": "systemPrompt",
    "type": "string",
    "value": "你是一个智能助理，你需要友好地与用户进行对话。"
  },
  {
    "name": "providerKind",
    "type": "string",
    "value": getDefaultModel().providerKind
  },
  {
    "name": "temperature",
    "type": "float",
    "value": 1
  },
  {
    "name": "max_tokens",
    "type": "integer",
    "value": 512
  },
  {
    "name": "maxConversations",
    "type": "integer",
    "value": 5
  },
  {
    "name": "frequency_penalty",
    "type": "float",
    "value": 0.0
  },
  {
    "name": "presence_penalty",
    "type": "float",
    "value": 0.0
  },
  {
    "name": "top_p",
    "type": "float",
    "value": 0.85
  },
  {
    "name": "timeout",
    "type": "integer",
    "value": 20000
  },
  {
    "name": "imageUrl",
    "type": "Image",
    "value": ""
  },
  {
    "name": "videoUrl",
    "type": "Video",
    "value": ""
  },
  {
    "name": "audioUrl",
    "type": "Audio",
    "value": ""
  },
  {
    "name": "historyMessages",
    "type": "string",
    "value": ""
  },{
    "name": "historyMessageLimit",
    "type": "integer",
    "value": 0
  },{
    "name": "json_object",
    "type": "boolean",
    "value": false
  },{
    "name": "audio_timestamp",
    "type": "boolean",
    "value": false
  },
]
const filterConfig = (configs, useSystemPrompt) => {
  // 做过滤逻辑
  const system_prompt = configs.find(v => v === 'system_prompt');
  const systemPrompt = configs.find(v => v === 'systemPrompt');
  if (system_prompt && systemPrompt) {
    if (useSystemPrompt) {
      configs = configs.filter(v => v !== 'system_prompt');
    } else {
      configs = configs.filter(v => v !== 'systemPrompt');
    }
  }
  return configs;
}

// const model_params = default_model_params.map(v => v.name);
const param_type = Object.fromEntries(default_model_params.map(v => [v.name, v.type]));

const ModelSetting: React.FC = (props: any) => {
  const { value, onChange, disabled, type, noSystem, filterFn, disabledConfig } = props;
  const { globalState } = useGlobalState();
  const { app } = globalState;
  const model_params = useRef(value.map(v => v.name));
  const [editModel, setEditModel] = useSafeState(() => {
    const modelName = value.find(v => v.name === 'modelName');
    const providerKind = value.find(v => v.name === 'providerKind');
    const model = globalState.modelList.find(m => m.name === modelName?.value);
    // console.log('model...', model)
    return ({
      modelName: modelName?.value || getDefaultModel().modelName,
      name: model?.alias || model?.name,
      tag: model?.tag,
      alias: model?.alias || model?.name,
      providerKind: providerKind || getDefaultModel().providerKind,
      config: {}
    })
  });

  const initialValues = useMemo(() => {
    const obj = { ...getDefaultModel() };
    value.filter(param => {
      if (noSystem) {
        return param.name !== 'system_prompt'
      }
      return true;
    }).map(param => {
      obj[param.name] = param.value;
    })
    const model = globalState.modelList.find(m => m.name === obj.modelName);
    setEditModel({
      modelName: obj.modelName as string,
      tag: model?.tag,
      name: model?.alias || obj.modelName,
      alias: model?.alias || obj.modelName,
      providerKind: obj.providerKind as string,
      config: obj
    })
    console.log('initialValues...', obj)
    return obj;
  }, [value])


  const [form] = Form.useForm();

  const handleBlur = (chgValues, _values) => {
    const values = _values || form.getFieldsValue();
    console.log("chgValues...", values, chgValues, _values, model_params.current)
    let preSystemPrompt = value.find(v => v.name === 'system_prompt' || v.name === 'systemPrompt');
    let useSystemPrompt = true, useMaxConversations = true;
    let useHistoryMessages = false;

    if (chgValues && 'audio_timestamp' in chgValues) {
      if (chgValues.audio_timestamp) {
        model_params.current = [...new Set([...model_params.current, 'audio_timestamp'])]
      } else {
        model_params.current = model_params.current.filter(v => v !== 'audio_timestamp')
      }
    }
    if (chgValues && 'json_object' in chgValues) {
      if (chgValues.json_object) {
        model_params.current = [...new Set([...model_params.current, 'json_object'])]
      } else {
        model_params.current = model_params.current.filter(v => v !== 'json_object')
      }
    }
    
    // 说明修改系统提示词，此时需要以system_prompt为准
    if (typeof chgValues?.system_prompt !== 'undefined') {
      model_params.current = [...new Set([...model_params.current, 'system_prompt'])]
      preSystemPrompt.value = chgValues.system_prompt;
      useSystemPrompt = false;
    }
    // 如果需要修改historyMessages
    if (values.historyMessages && values.historyMessageLimit) {
      model_params.current = [...new Set([...model_params.current, 'historyMessages', 'historyMessageLimit'])]
      useHistoryMessages = true;
    }

    const result = {
      ...values,
      modelName: values?.modelName,
      providerKind: values?.providerKind,
      timeout: values?.timeout || 20000,
      [preSystemPrompt?.name]: preSystemPrompt?.value || '',
    }
    const totalConfig = filterConfig(model_params.current, useSystemPrompt).filter(name => {
      if (noSystem) {
        return name !== 'system_prompt' && name !== 'systemPrompt'
      }
      return true;
    }).map(name => {
      let value = result[name] ?? initialValues[name];
      if (name === 'json_object' && disabledConfig && disabledConfig[name]) {
        value = true
      }
      return ({
        name,
        type: param_type[name],
        value,
      })

    })
    if (!noSystem) {
      const curSystemPrompt: any = totalConfig.find(v => v.name === 'system_prompt' || v.name === 'systemPrompt');
      if (curSystemPrompt.value === '') {
        curSystemPrompt.id = preSystemPrompt.id
      }
    }

    console.log("vvvvv total", totalConfig)
    onChange(totalConfig);
  }



  const filterModelTag = (model, tag, param) => {
    if (model.tag.includes(tag)) {
      addModelParams([param])
    } else {
      clearModelParams([param])
    }
  }

  const clearModelParams = (params) => {
    model_params.current = model_params.current.filter(v => !params.includes(v))
  }

  const addModelParams = (params) => {
    model_params.current = [...new Set([...model_params.current, ...params])]
  }

  const handleModelChange = (model) => {
    let newModel = model;
    if (model?.selectedModels) {
      newModel = model.selectedModels[0];
      newModel = globalState.modelList.find(m => m.name === newModel.name);
    }
    // console.log('newModel...', newModel, model)
    setEditModel(newModel);
    // 如果model的tag包含view说明是视觉模型
    const values = form.getFieldsValue();
    clearModelParams(['imageUrl', 'videoUrl', 'audioUrl']);
    filterModelTag(newModel, 'view', 'imageUrl');
    filterModelTag(newModel, 'video', 'videoUrl');
    filterModelTag(newModel, 'audio', 'audioUrl');
    // console.log('model_params...', model_params.current);
    const schema = getModelConfigSchema(newModel, app.type);
    // 把所有的值都替换成合理值
    schema.map((schemaConfig) => {
      const {
        paramKey,
        validRange,
        default: initialValues,
      } = schemaConfig;

      const { range: backendRange, default: backendDefault } =
        newModel?.config[paramKey] || {};
      const range = backendRange || validRange;
      // @ts-ignore
      let defaultValue = values[paramKey] ?? backendDefault ?? initialValues;
      // 要看范围是否正常，如果异常，需要切换会正常值
      if (defaultValue < range[0] || defaultValue > range[1]) {
        defaultValue = backendDefault ?? initialValues;
      }
      values[paramKey] = defaultValue
      if (paramKey === 'json_object' && disabledConfig && disabledConfig[paramKey]) {
        values[paramKey] = true
      }
    });
    values.modelName = newModel.name;
    values.providerKind = newModel.providerKind;

    form.setFieldsValue(values);
    handleBlur(null, values)
  }

  const handleClearModel = (e) => {
    e.stopPropagation();
    setEditModel({
      modelName: '',
      name: '',
      tag: [],
      alias: '',
      providerKind: '',
      config: {}
    });
    addModelParams(['imageUrl', 'videoUrl', 'audioUrl'])
    const values = form.getFieldsValue();
    values.modelName = '';
    values.providerKind = '';
    form.setFieldsValue(values);
    handleBlur(null, values);
  };

  const renderFormItem = useMemo(() => {
    const schema = getModelConfigSchema(editModel, app.type);
    // console.log('schema...', schema, editModel)
    return schema.map((schemaConfig) => {
      const {
        paramKey,
        paramName,
        description,
        validRange,
        type,
        step = 0.01,
        default: defaultValue,
      } = schemaConfig;

      const { range: backendRange, defaultValue: backendDefault } =
        editModel?.config[paramKey] || {};
      const range = backendRange || validRange;
      const disable = disabled ?? (disabledConfig || {})[paramKey];

      const label = (
        <TextTip
          text={`${paramName} ${paramKey}`}
          tip={
            <div>
              {(description || []).map((it, idx) => (
                <p key={`description-${idx}`}>{it}</p>
              ))}{' '}
            </div>
          }
        />
      ) as any

      if (type === 'switch') {
        // 由于现在只有json模式是开关
        return <LFormSwitch
          name={paramKey}
          label={label}
          disabled={disable}
          defaultValue={true}
        ></LFormSwitch>
      }

      return (
        <LFormSliderInput
          key={`sliderInput-${paramKey}`}
          name={paramKey}
          label={label}
          min={range[0]}
          max={range[1]}
          step={step}
          disabled={disable}
        // defaultValue={backendDefault || defaultValue}
        />
      );
    });
  }, [editModel]);

  const modelSelectSettings = useMemo(() => {
    return {
      selectedModels: editModel ? [{
        key: editModel.modelName,
        name: editModel.modelName,
        // 其他必要的字段可以从 modelList 中获取
        ...globalState.modelList.find(m => m.name === editModel.modelName)
      }] : [],
      multiple: {
        cost: 1,
        speed: 1,
        context: 1,
        performance: 1
      }
    }
  }, [editModel]);

  const content = (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'vertical'}
        initialValues={initialValues}
        // onValuesChange={debounce(handleBlur, 400)}
        onValuesChange={debounce(handleBlur, 200)}
      >
        <div style={{ marginBottom: '10px', userSelect: 'none', display: 'flex', alignItems: 'center' }}>
          <ModelModal
            showCandidate={false}
            title="选择模型"
            onConfirm={handleModelChange}
            defaultValue={modelSelectSettings as any}
          >
            <div style={{
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              padding: '4px 11px',
              cursor: 'pointer',
              minWidth: '120px',
              display: 'inline-block',
              lineHeight: '22px',
              position: 'relative',
            }}>
              {editModel.modelName ? (editModel.alias || editModel.name) : "选择模型"} <RobotOutlined />
            </div>
          </ModelModal>
          {editModel.modelName && (
            <CloseCircleFilled
              style={{
                color: '#999',
                fontSize: '12px',
                marginLeft: '10px',
                cursor: 'pointer'
              }}
              onClick={handleClearModel}
            />
          )}
        </div>
        {/* <ModelSelect filterFn={filterFn} value={editModel.modelName} onChange={handleModelChange} style={{ marginBottom: '10px' }}></ModelSelect> */}
        {!noSystem ?
          <Form.Item label="系统提示词" name="system_prompt">
            <Input.TextArea allowClear autoSize={{
              minRows: 5,
              maxRows: 10
            }} />
          </Form.Item> : null}
        {renderFormItem}
        <Form.Item name="providerKind" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="modelName" hidden>
          <Input />
        </Form.Item>
        {app.type === IAppType.AgentWorkflow && (
          <Form.Item label={<span>上下文引用<Tooltip  title="上下文引用是指在当前节点的 Messages 中引用上下文中的内容。可以选择全局/当前节点上下文，并设置引用轮次，0轮等同于不引用"><QuestionCircleOutlined style={{ marginLeft: 4 }}/></Tooltip></span>}>
            <Space>
              <Form.Item name="historyMessages" noStyle initialValue=''>
                <Select style={{ width: 140 }} options={[{
                  label: '不引用',
                  value: ''
                }, {
                  label: '全局上下文',
                  value: '$_GLOBAL_LLM_CONTEXT'
                }, {
                  label: '当前节点上下文',
                  value: '$_CURRENT_LLM_CONTEXT'
                }]} />
              </Form.Item>
              <Form.Item name="historyMessageLimit" noStyle>
                <InputNumber min={0} max={10} />
              </Form.Item>
              轮
            </Space>
          </Form.Item>
        )}
        <Form.Item name="timeout" label="超时时间(ms)">
          <InputNumber max={60000} />
        </Form.Item>
      </Form>
    </div>
  );
  return content;
};

export default function WithBase(props) {
  return (
    <BaseShape {...props}>
      <ModelSetting></ModelSetting>
    </BaseShape>
  );
};