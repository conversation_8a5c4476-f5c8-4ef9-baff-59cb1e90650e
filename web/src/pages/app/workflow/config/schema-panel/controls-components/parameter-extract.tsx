// @ts-nocheck
import React, { useEffect } from 'react';
import { Form } from 'antd';
import { debounce } from 'lodash';
import { BaseShape } from './base-shape';
import { getParamList } from './param-list';

const getSystemTime = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const week = now.getDay();
  const weekMap = {
    0: 'Sunday',
    1: 'Monday',
    2: 'Tuesday',
    3: 'Wednesday',
    4: 'Thursday',
    5: 'Friday',
    6: 'Saturday',
  }
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds},${weekMap[week]}`;
};

export const ParameterExtract: React.FC = (props: any) => {
  const { value, onChange, disabled } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    if (value) {
      let parameters = [];
      if (value.parameters) {
        parameters = value.parameters;
      } else {
        value.configs && value.configs.map(item => {
          if (item.name === "parameters") {
            parameters = item.value;
          }
        });
      }
      form.setFieldsValue({ parameters });
    }
  }, [value])

  const getParams = (param) => {
    if (typeof param !== 'object') {
      return '';
    }
    const { description, type, enum: initEnum, name } = param;
    let paramPair = `${name}: `;
    let value = '';
    let enumComment = '', enums = ''
    if (initEnum && initEnum.length) {
      enums = initEnum.map(v => {
        enumComment += `${v.value}:${v.label} `;
        return Number(v.value) ? v.value : `'${v.value}'`
      }).join(' | ');
      value = enums;
    } else {
      value = (type || 'STRING').toLowerCase();
    }
    paramPair += value;
    paramPair += '| null'
    return `
    // ${description} ${enumComment}
    ${paramPair};`;
  }

  const getActionSchema = (parameters) => `
${parameters ? `type ParameterType = {
${parameters.map(getParams).join('')}
};` : ''}
`

  const convertSchema = (parameters, systemTime) => {
    const schemas = getActionSchema(parameters)
    const template = `// The current system time is ${systemTime}. If time-related conversions are involved, this time can be used.The following types define the structure of an object of type Parameter
//  If a parameter does not match successfully, set the value of that parameter to null.
${schemas}
`
    return template;
  }

  const getSchemaPrompt = (parameters) => {
    const systemTime = getSystemTime();
    const hint = `You are a service that translates user requests into JSON objects of type "ParameterType" according to the following TypeScript definitions:\n`
    const schema = '```\n' + convertSchema(parameters, systemTime) + '```\n';
    const prompt = `${hint}\n${schema}\nThe following is the user request translated into a JSON object with 2 spaces of indentation and no properties with the value undefined:\n`
    return prompt;
  }

  const onFieldsChange = (v) => {
    const values = form.getFieldsValue();
    const { parameters = [] } = values;
    const prompt = getSchemaPrompt(parameters);
    value.inputs.map(item => {
      if (item.name === "systemPrompt" || item.name === 'system_prompt') {
        item.value = prompt;
      }
    });
    value.vars.map(item => {
      if (item.name === "systemPrompt" || item.name === 'system_prompt') {
        item.value = prompt;
      }
    });
    value.configs.map(item => {
      if (item.name === "parameters") {
        item.value = parameters;
      }
      if (item.name === "systemPrompt" || item.name === 'system_prompt') {
        item.value = prompt;
      }
    });
    onChange({ ...value, ...values });
  };

  return (
    <div className="param-form">
      <Form
        form={form}
        autoComplete="off"
        layout={'horizontal'}
        onFieldsChange={debounce(onFieldsChange, 100)}
        initialValues={{ items: value }}
      >
        <div>
          {getParamList({
            showDesc: true,
            showEnum: true,
            name: 'parameters',
            disabled,
          })}
        </div>
      </Form>
    </div>
  );
};

function WithBase(props) {
  return (
    <BaseShape {...props}>
      <ParameterExtract></ParameterExtract>
    </BaseShape>
  );
}

export default WithBase;
