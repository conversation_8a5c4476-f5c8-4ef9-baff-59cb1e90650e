// @ts-nocheck
/* eslint-disable jsx-a11y/anchor-is-valid */
import { ExportOutlined, ImportOutlined, MenuOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Dropdown, message, Modal, Space } from 'antd';
import fileSaver from 'file-saver';
import React from 'react';
import FileReaderInput from 'react-file-reader-input';

import { useGlobalState } from '@/hooks/useGlobalState';

const { confirm } = Modal;

import { pick } from 'lodash';

import { AppApi } from '@/api/app';
import { JSONParse } from '@/utils/common';
import { $, mergeConfig } from '@/utils/state';

import { getSaveData, saveGraphData } from './cmds/hooks-services';

const getItems = (onClick): MenuProps['items'] => [
  {
    label: (
      <span style={{ fontSize: '12px' }} onClick={() => onClick('export')}>
        <ExportOutlined style={{ marginRight: 5 }} />
        文件导出
      </span>
    ),
    key: '0',
  },
  {
    label: (
      <FileReaderInput
        as="text"
        id="my-file-input"
        onChange={(e, val) => onClick('import', e, val)}
      >
        <span style={{ fontSize: '12px' }} onClick={() => onClick('import')}>
          <ImportOutlined style={{ marginRight: 5 }} />
          文件导入
        </span>
      </FileReaderInput>
    ),
    key: '1',
  },
];

const Menu: React.FC = () => {
  const { globalState } = useGlobalState();
  const { app } = globalState;
  const handleClick = async (v, e, data) => {
    console.log('v', v, e, data);
    const saveData = await getSaveData();
    if (v === 'export') {
      console.log(saveData);
      const blob = new Blob([JSON.stringify(saveData)], {
        type: 'text/plain;charset=utf-8',
      });
      fileSaver.saveAs(blob, `${app.name}.conf`);
    }
    if (v === 'import') {
      if (data && data[0] && data[0][0]) {
        const rawData = data[0][0].target.result;
        const configs: any = JSONParse(rawData);
        if (!configs || !configs.workflowId) {
          message.error('导入数据异常');
          return;
        } else {
          const newConfigs = mergeConfig(configs, saveData);
          confirm({
            content: '是否覆盖当前页面',
            onOk: async () => {
              $.setConfig(JSON.parse(rawData));
              const res = await AppApi.debugApp('', newConfigs);
              if (res) {
                window.location.reload();
              }
            },
          });
        }
        console.log('raw', rawData);
      }
    }
  };

  return (
    <Dropdown
      menu={{ items: getItems(handleClick) }}
      trigger={['click']}
      placement="bottomLeft"
    >
      <MenuOutlined style={{ marginLeft: 10 }} />
    </Dropdown>
  );
};

export default Menu;
