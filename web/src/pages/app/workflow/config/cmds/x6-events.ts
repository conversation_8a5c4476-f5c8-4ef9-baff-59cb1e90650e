import { IEvent } from '@antv/xflow';

import { getGraphInstance } from '@/utils/flow';
import { resizeGroup } from '@/utils/node';
import { saveGraphConfigToLocal } from './hooks-services';

let cells = [];

/**
 * edge:mouseenter
 */
export const edgeMouseenterEvent: IEvent<'edge:mouseenter'> = {
  eventName: 'edge:mouseenter',
  callback: async (props) => {
    addDeleteTool(props);
  },
};

/**
 * edge:mouseleave
 */
export const edgeMouseleaveEvent: IEvent<'edge:mouseleave'> = {
  eventName: 'edge:mouseleave',
  callback: async ({ e, view }) => {
    view.container.classList.remove(`x-1`);
    view.container.classList.remove(`x-1-2`);
    view.container.classList.remove(`x-1-3`);
    view.container.classList.remove(`x-1-6`);
    cells.map((v: any) => {
      v.removeTools();
    });
    cells = [];
  },
};

/**
 * blank:click
 */
export const blankClickEvent: IEvent<'blank:click'> = {
  eventName: 'blank:click',
  callback: async () => {
    cells.map((v: any) => {
      v.removeTools();
    });
    cells = [];
  },
};

export const addNode2Group = (props) => {
  const { currentParent: group, node, isInit, previousParent } = props;
  if (!group) {
    return;
  }
  // 如果parent不同的，需要把之前的parent的对应节点删除
  if (previousParent && previousParent !== group) {
    previousParent.removeChild(node);
    const idx = previousParent.data.groupChildren.findIndex(v => v === node.id);
    if (idx) {
      previousParent.data.groupChildren.splice(idx, 1);
    }
    // 重新修改当前node的group
    node.data.group = group.id;
  }
  // if (node?.data?.isGroup) {
  //   return;
  // }
  /**
   *  如果已经完成 embedded，就把当前节点加入group
   *  xflow 里要完成 group 添加 node 主要有以下几个环节：
   *  1. group 需要往 groupChildren（child的id数组）增加 node 的id
   *  2. node.group = group.id
   *  3. 需要根据所有的孩子做一次大小调整（这是我加的规则）
   */
  const data = group?.getData();
  const groupChildren = data.groupChildren;
  // 如果已经添加了，就退出
  if (groupChildren.includes(node.id) && !isInit) {
    return true;
  }
  // 规则2
  node.setData({ ...node.getData(), group: data.id });


  // 规则1
  let newGroupChildren = [...groupChildren];
  newGroupChildren.push(node.id);
  newGroupChildren = Array.from(new Set(newGroupChildren));
  const graph = node.model.graph;
  // 查询有效节点, 如果已经删除的则在children里删除
  newGroupChildren = newGroupChildren.filter((childId) =>
    graph.getNodes().find((cell) => cell.id === childId),
  );
  console.log("groupChildren", newGroupChildren);
  group.setData({
    ...data,
    groupChildren: newGroupChildren,
  });
  group.addChild(node);
  // debugger
  if (group.parent) {
    group.setZIndex(group.parent.zIndex + 1);
  } else {
    group.toBack();
  }

  // 规则3
  resizeGroup(group);
  console.log('props222', { group, node });
};

/**
 * node:embedded
 */
export const nodeEmbeddedEvent: IEvent<'node:embedded'> = {
  eventName: 'node:embedded',
  callback: async (props) => {
    console.log("embedded", props);
    addNode2Group(props);
  },
};

export const nodeClick: IEvent<'node:mousedown'> = {
  eventName: 'node:mousedown',
  callback: async ({ e, node, view }) => {
    console.log('v2', e, node, view);
    e.stopPropagation();
    node.setProp('nodeMovable', false);
    return;
  },
};

let position: any = null;
let size:any = null;
let childs = [];

export const nodeMove: IEvent<'node:move'> = {
  eventName: 'node:move',
  callback: async ({ e, node }) => {
    // if (e.target.classList.contains('resize-ctl')) {
    //   // childs = node.getChildren();
    //   position = node.prop('position');
    //   size = node.prop('size');
    //   e.stopPropagation();
    //   // childs?.forEach(v => node.removeChild(v));
    // } else {
    //   position = null;
    // }
    return;
  },
};

export const nodeMoving: IEvent<'node:moving'> = {
  eventName: 'node:moving',
  callback: async ({ node }) => {
    const isGroup = node.prop('isGroup')
    let curParent: any = node.parent;
    if (isGroup) {
      node.prop('originPosition', node.getPosition())
    }
    while(curParent) {
      resizeGroup(curParent);
      curParent = curParent.parent;
    }
  },
};

/**
 * 增加tools
 * @param props
 * @returns
 */
function addDeleteTool(props) {
  const { cell, e, view } = props;
  if (e.target.tagName !== 'path') {
    return;
  }
  const pathLen = e.target.getTotalLength();
  const graph = getGraphInstance();
  const scale = graph.transform.getMatrix().a;
  cells.map((v: any) => {
    v.removeTools();
  });
  cells = [];

  let ratio = 1;
  if (scale < 1) {
    ratio = 1.2;
  }
  if (scale < 0.7) {
    ratio = 1.3;
  }
  if (scale < 0.5) {
    ratio = 1.6;
  }
  view.container.classList.add(`x-${String(ratio).replace('.', '-')}`);
  cell.addTools([
    {
      name: 'button',
      args: {
        markup: [
          {
            tagName: 'circle',
            selector: 'button',
            attrs: {
              r: 12 * ratio,
              stroke: '#ff4e4e',
              strokeWidth: 2,
              fill: '#faf2f0',
              cursor: 'pointer',
            },
          },
          {
            tagName: 'text',
            textContent: 'X',
            selector: 'icon',
            attrs: {
              fill: '#ff4e4e',
              fontSize: 16 * ratio,
              textAnchor: 'middle',
              pointerEvents: 'none',
              y: '0.35em',
            },
          },
        ],
        distance: pathLen / 2,
        onClick({ view }: any) {
          const edge = view.cell;
          graph.removeEdge(edge);
          saveGraphConfigToLocal();
        },
      },
    },
  ]);
  (cells as any).push(cell);
}
