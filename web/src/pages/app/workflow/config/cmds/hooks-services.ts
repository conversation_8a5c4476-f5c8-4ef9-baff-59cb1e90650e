// @ts-nocheck
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Graph } from '@antv/x6';
import type { NsEdgeCmd, NsGraphCmd, NsNodeCmd } from '@antv/xflow';
import { NsGraph, NsGraphStatusCommand, uuidv4, XFlowNodeCommands } from '@antv/xflow';
import {
  ATOMIC_RENDER_ID,
  NODE_CODE,
  NODE_HEIGHT,
  NODE_TYPE,
  NODE_WIDTH,
  TypeMap,
} from '@utils/constant';
import { addPorts, resizeGroup } from '@utils/node';
import { message } from 'antd';
import { pick } from 'lodash';

import { AppApi } from '@/api/app';
import { IRunState } from '@/interface';
import { formatNodeValue, getLocalData, saveLocalData } from '@/utils/common';
import { getGraphData, getGraphInstance } from '@/utils/flow';
import { $, addLLmConfig, getAppId } from '@/utils/state';

import type { NsRenameNodeCmd } from '../../cmd-extensions/cmd-rename-node-modal';
import { getRunModal } from '../config-model-service';
import { getDefaultConfig } from '@/utils/model-helper';

/** mock 后端接口调用 */
export const NODE_COMMON_PROPS = {
  renderKey: ATOMIC_RENDER_ID,
  width: NODE_WIDTH,
  height: NODE_HEIGHT,
  resizable: true,
} as const;

/** 查图的meta元信息 */
export const queryGraphMeta: NsGraphCmd.GraphMeta.IArgs['graphMetaService'] = async (
  args,
) => {
  console.log('queryMeta', args);
  return { ...args, flowId: args?.meta?.flowId || '' };
};

/** 加载图数据的api */
export const loadGraphData = (type) => async () => {
  const nodes: NsGraph.INodeConfig[] = [];
  const edges: NsGraph.IEdgeConfig[] = [];
  const defaultData = getDefaultConfig(type) || { nodes, edges };
  return await $.getConfig(defaultData ?? { nodes, edges }, true);
};

/**
 * 获取需要保存的数据
 * @param graphData
 * @returns
 */
export const nodeArgs = [
  'appId',
  'no',
  'id',
  'branch',
  'fallbackOut',
  'category',
  'code',
  'type',
  'description',
  'x',
  'y',
  'height',
  'width',
  'renderKey',
  'renderCode',
  'icon',
  'inputs',
  'outputs',
  'configs',
  'vars',
  'componentId',
  'name',
  'ports',
  'value',
  'originValue',
  'props',
  'async',
  'group',
  'groupChildren',
  'groupChildrenSize',
  'groupCollapsedSize',
  'label',
  'isGroup',
  'fixed',
  'asDefault',
  'permanent',
];

/**
 * 获取保存的数据
 * @param _graphData 
 * @param debug 
 * @returns 
 */
export const getSaveData = async (_graphData?: NsGraph.IGraphData, debug?: boolean) => {
  const graphData = _graphData || (await getGraphData());
  const _config = await $.getConfig({});
  const newConfig: any = { ..._config, ...graphData };
  if (!newConfig.version) {
    message.error('未发现version字段');
    return;
  }
  newConfig.nodes = await Promise.all(
    newConfig.nodes.map((node) =>
      formatNodeValue({
        ...pick(node, nodeArgs),
        debug,
      }),
    ),
  );
  // 根据 graphData 生成 inputs 和 outputs
  newConfig.inputs = newConfig.nodes
    .filter((v) => v.type === NODE_TYPE.INPUT)
    .map((v) => {
      const valueIsObject = typeof v.value === 'object' && !Array.isArray(v.value);
      return v.outputs.map((port: any) => {
        if (port.asDefault) {
          port.value = valueIsObject ? v.value[port.name] : v.value;
        }
        return port;
      });
    })
    .flat();
  newConfig.outputs = graphData.nodes
    .filter((v) => v.type === NODE_TYPE.OUTPUT)
    .map((v) => {
      return v.inputs;
    })
    .flat();
  return newConfig;
};

let graphDataReady = false;

export const setGraphDataReady = (ready = true) => {
  console.log("graphData ready", ready);
  graphDataReady = ready;
}

/**
 * 保存到本地
 * @param config 
 */
export const saveGraphConfigToLocal = async (reset?) => {
  // 必须要画布ready才能进行后续保存操作
  const appId = getAppId();
  console.log("[saveGraphConfigToLocal]", graphDataReady);
  if (!graphDataReady) {
    return false;
  }
  if (reset) {
    return saveLocalData(`app-${appId}`, { time: 0 });
  }
  const graphData = await getGraphData();
  const config = await getSaveData(graphData);
  return saveLocalData(`app-${appId}`, { time: Date.now(), config });
}

/**
 * 从本地读取数据
 * @returns 
 */
export const getGraphConfigFromLocal = () => {
  const appId = getAppId();
  return getLocalData(`app-${appId}`);
}

/**
 * 在操作后执行保存本地
 * @param _ 
 * @param handler 
 * @returns 
 */
export const afterActionSaveToLocal = async (_, handler) => {
  const main = async (args) => {
    const res = await handler(args);
    saveGraphConfigToLocal()
    console.log("edge", res);
    return res;
  };
  return main;
}

/**
 * 保存图数据的api
 * @param meta
 * @param graphData
 * @returns
 */
export const saveGraphData = async (
  meta: NsGraph.IGraphMeta,
  graphData: NsGraph.IGraphData,
  debug?: boolean,
  // type?: IAppType.AgentWorkflow | IAppType.Workflow = IAppType.Workflow,
) => {
  const newConfig: any = await getSaveData(graphData);
  let res = null;
  console.log('saveGraphData api', meta, graphData, newConfig);
  if (debug) {
    // 先触发一下debug保存
    res = await AppApi.debugApp('', newConfig);
    if (res) {
      $.setConfig(newConfig);
    }
  } else {
    res = await AppApi.saveAppConfigSnapshot('', newConfig);
    // 把新的id保存，和上一次的对比，是否要刷新会话
    newConfig.configId = res.id;
    console.log('res', res);
    if (res) {
      $.setConfig(newConfig);
      message.success('保存成功');
    }
  }
  saveGraphConfigToLocal(true);
  window.localStorage.setItem('graphData', JSON.stringify(newConfig));
  return {
    success: true,
    res,
    data: graphData,
  };
};

export const diffGraph = async () => {
  const graphData = await getGraphData();
  const configData = $.getOriginData();
  return JSON.stringify(graphData) !== JSON.stringify(configData);
};

/** 部署图数据的api */
export const deployDagService = async (
  meta: NsGraph.IGraphMeta,
  graphData: NsGraph.IGraphData,
  pubMessage?: string
) => {
  const newConfig: any = await getSaveData(graphData);
  const res = await AppApi.deployApp('', { ...newConfig, message: pubMessage });
  console.log('deployService api', res, graphData, newConfig);
  if (res) {
    message.success('部署成功');
  }
  return {
    success: true,
    data: graphData,
  };
};

/** 添加节点 */
export const addNode: NsNodeCmd.AddNode.IArgs['createNodeService'] = async (
  args: NsNodeCmd.AddNode.IArgs,
) => {
  const { nodeConfig } = args;
  const graph = getGraphInstance();
  // 从节点中找到符合当前code的，看编号到多少，主要是处理添加多个同样的节点的情况，需要每个节点命名不一致（通过添加编号）
  const nodesNos = graph.getNodes()
    .filter(cls => cls.data.code === nodeConfig?.code)
    .map(v => v?.data?.no || 0)
    .sort((a, b) => b - a);
  const no = nodesNos[0] + 1;
  console.log('args', args, nodesNos);
  const nodeId = 'a' + uuidv4().split('-')[0];
  console.info('addNode service running, add node:', args, nodeId);
  // 如果是复制输入节点，需要把outputs的id都删掉，不然作为输出会报错
  /** 这里添加连线桩 */
  const node: NsNodeCmd.AddNode.IArgs['nodeConfig'] = {
    ...NODE_COMMON_PROPS,
    ...nodeConfig,
    inputs: (nodeConfig?.inputs || []).map(v => {
      return { ...v, id: undefined };
    }),
    outputs: (nodeConfig?.outputs || []).map(v => {
      return { ...v, id: undefined };
    }),
    vars: nodeConfig.vars || [],
    name: (nodeConfig?.name || '') + (no || ''),
    no,
    id: nodeId,
  };
  // 增加大语言模型相关配置
  addLLmConfig(node);
  // 如果是foreach，则是group组件
  if (nodeConfig?.type === NODE_TYPE.FOREACH) {
    node.isGroup = true;
    node.groupChildren = [];
  }
  // 输入组件需要把no带上，因为入参名必须唯一
  if (nodeConfig?.type === NODE_TYPE.INPUT) {
    node.outputs = node.outputs.map(port => ({ ...port, name: port.name + (no || ''), title: port.title + (no || '') }));
  }
  // 添加链接桩（根据inputs，output)
  const newNode = addPorts(node);
  return newNode;
};

/** 更新节点 name，可能依赖接口判断是否重名，返回空字符串时，不更新 */
export const renameNode: NsRenameNodeCmd.IUpdateNodeNameService = async (
  name,
  node,
  graphMeta,
) => {
  console.log('rename node', node, name, graphMeta);
  return { err: null, nodeName: name };
};

/**
 * 删除节点的服务
 * @param args
 * @returns
 */
export const delNode: NsNodeCmd.DelNode.IArgs['deleteNodeService'] = async (args) => {
  const { id: nodeId, group: groupId } = args.nodeConfig;
  const graph = getGraphInstance();
  const group = graph.getCellById(groupId);

  // 如果存在ground，要进行联动删除
  if (group) {
    const data = group.getData();
    const { groupChildren } = data;
    group.removeChild(nodeId);
    group.setData({
      ...data,
      groupChildren: groupChildren.filter((id) => id !== nodeId),
    });
    console.log('group remove child', group);
    resizeGroup(group);
  }

  console.info('delNode service running, del node:', args.nodeConfig, graph, group);
  return true;
};

export const addEdgeAndSaveLocal = async (args) => {
  const res = await addEdge(args);
  saveGraphConfigToLocal()
  console.log("edge", res);
  return res;
}

export const isSelectNode = (node) => {
  return node?.data?.type === NODE_TYPE.SELECT;
}

export const isSwitchNode = (node) => {
  return node?.data?.type === NODE_TYPE.SWITCH || node?.data?.code === NODE_CODE.INTENTION;
}

export const addBranch = (commandService, graph, edge) => {
  const traverseNode = false;
  const source = graph.getCellById(edge.source);
  const target = graph.getCellById(edge.target);
  // 如果target是结束节点，且source含有branch信息，则需要把该边删掉
  if (target.data.type === NODE_TYPE.OUTPUT && source.data.branch) {
    graph.removeEdge(edge.id);
    return;
  }

  // 如果有分支信息
  if (isSwitchNode(source) || source?.data?.branch) {
    let branch = source.data.branch || source.data.id;
    const ids = branch.split('-');
    // 说明终止了branch，则分支弹出一层
    if (isSelectNode(target)) {
      ids.pop();
    }
    // 如果目标也是一个分支节点，则需要增加一层
    if (isSwitchNode(target)) {
      ids.push(target.data.id);
    }
    // 更新branch信息
    branch = ids.join('-');
    // 把branch信息带上
    commandService.executeCommand<NsNodeCmd.UpdateNode.IArgs>(
      XFlowNodeCommands.UPDATE_NODE.id,
      {
        nodeConfig: {
          ...target.data,
          branch,
        },
      },
    )
    console.log(`处理节点分支 ${target.data.name} ${target.data.branch} - ${branch}`)
    target.setData({
        ...target.data,
        branch,
    })
  }
  if (target && traverseNode) {
    // 递归处理后置节点
    const outgoingEdges = graph.getOutgoingEdges(target) || []
    console.log(`该节点 ${target.data.name} 有 ${outgoingEdges.length}个后置节点`, outgoingEdges)
    if (outgoingEdges.length) {
      outgoingEdges.forEach(edge => {
        // 说明是group里面的边，则不处理
        if (edge._parent) {
          return;
        }
        console.log(`处理边`, edge)
        addBranch(commandService, graph, edge.data);
      })
    }
  }
}

/**
 * 添加边的服务
 * @param args
 */
export const addEdge = async (args) => {
  const { commandService, getX6Graph } = args;
  const graph: Graph = await getX6Graph();
  console.log('edgeConfig', args.edgeConfig, args)
  // 判断边的两边是否有 Switch 节点，有的话，需要把 分支信息加上，除非连接的是 Select 节点
  const source = graph.getCellById(args.edgeConfig.source);
  const target = graph.getCellById(args.edgeConfig.target);
  if (!source) {
    return;
  }
  const sourcePort = source && source.getPortProp(args.edgeConfig.sourcePortId);

  console.log('source', source, sourcePort);
  let color = '';
  if (sourcePort.dataType.includes('array')) {
    color = TypeMap.array.color;
  } else {
    color = TypeMap[sourcePort.dataType].color;
  }

  // 如果source是分支节点，则需要把当前节点的信息加上
  addBranch(commandService, graph, args.edgeConfig);

  console.log("colors...", color);

  args.edgeConfig = {
    ...args.edgeConfig,
    attrs: {
      line: {
        strokeDasharray: '',
        targetMarker: {
          name: 'circle',
          r: 5,
        },
        stroke: color,
        strokeWidth: 3,
      },
    },
    // router: 'normal',
  };
};

/** 删除边的api */
export const delEdge: NsEdgeCmd.DelEdge.IArgs['deleteEdgeService'] = async (args) => {
  console.info('delEdge service running, del edge:', args);
  return true;
};

const statusMap = {} as NsGraphStatusCommand.IStatusInfo['statusMap'];
let graphStatus: NsGraphStatusCommand.StatusEnum =
  NsGraphStatusCommand.StatusEnum.DEFAULT;

const STATUS_MAP = {
  running: NsGraphStatusCommand.StatusEnum.PROCESSING,
  queued: NsGraphStatusCommand.StatusEnum.DEFAULT,
  success: NsGraphStatusCommand.StatusEnum.SUCCESS,
  failed: NsGraphStatusCommand.StatusEnum.ERROR,
};

/**
 * 获取runState
 * @param runID
 * @returns
 */
export const getRunStatus = async (appId, runID, debug?: boolean) => {
  let runState: IRunState = {
    status: 'queued',
  } as IRunState;
  try {
    runState = await AppApi.getWorkFlowRun(appId, runID, debug);
  } catch (err) {
    window.corona.warn('getRunStatus失败', err);
    console.log('err', err);
    return;
  }
  return runState;
};

/**
 * 调试接口
 * @param args
 * @param rest
 * @returns
 */
export const graphStatusService: NsGraphStatusCommand.IArgs['graphStatusService'] =
  async (args: any, ...rest) => {
    const { getX6Graph, modelService, runID, noDebug } = args;
    if (!runID) {
      return;
    }
    const graph: Graph = await getX6Graph();
    const cells = graph.getNodes();
    const runModal = await getRunModal(modelService);
    const runStat = await getRunStatus('', runID, !noDebug);
    // 设置当前执行状态
    if (runStat) {
      runModal.setValue(runStat);
    }
    // 当前图的状态
    graphStatus = STATUS_MAP[runStat.status];
    if (graphStatus === STATUS_MAP.failed) {
      graphStatus = STATUS_MAP.success;
    }

    console.log('args', args, runStat, cells, rest);
    // 先根据nodes，把当前已经处理结束的设置好
    runStat.nodes.forEach((node) => {
      statusMap[node.nodeId] = { status: STATUS_MAP[node.status] };
    });

    // 所有的inputs先设置为完成
    cells.forEach((cell) => {
      if (cell.data.type === NODE_TYPE.INPUT) {
        statusMap[cell.id] = { status: NsGraphStatusCommand.StatusEnum.SUCCESS };
      }
      // 如果完成了，把outputs设置为完成
      if (cell.data.type === NODE_TYPE.OUTPUT && runStat.status === 'success') {
        statusMap[cell.id] = { status: NsGraphStatusCommand.StatusEnum.SUCCESS };
      }
      if (cell.data.type === NODE_TYPE.OUTPUT && runStat.status === 'failed') {
        statusMap[cell.id] = { status: NsGraphStatusCommand.StatusEnum.ERROR };
      }
    });

    return {
      graphStatus: graphStatus,
      statusMap: statusMap,
    };
  };

export const stopGraphStatusService: NsGraphStatusCommand.IArgs['graphStatusService'] =
  async () => {
    Object.entries(statusMap).forEach(([, val]) => {
      const { status } = val as { status: NsGraphStatusCommand.StatusEnum };
      if (status === NsGraphStatusCommand.StatusEnum.PROCESSING) {
        val.status = NsGraphStatusCommand.StatusEnum.ERROR;
      }
    });
    return {
      graphStatus: NsGraphStatusCommand.StatusEnum.ERROR,
      statusMap: statusMap,
    };
  };
