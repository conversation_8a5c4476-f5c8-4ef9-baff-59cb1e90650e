import { ATOMIC_RENDER_ID, TEXT_RENDER_ID, IMAGE_RENDER_ID } from '@utils/constant'


export const nodeData = [
  {
    id: 'input',
    header: '输入',
    children: [
      {
        id: '0',
        label: 'CIO Loader',
        parentId: 'input',
        category: 'INPUT',
        code: 'INPUT_TEXT',
        type: 'INPUT',
        icon: 'FontSizeOutlined',
        outputs: [
          {
            "name": "cioID",
            "type": "number",
          },
        ]
      },
      {
        id: '1',
        label: '文本组件',
        category: 'INPUT',
        code: 'INPUT_TEXT',
        type: 'INPUT',
        icon: 'FontSizeOutlined',
        description: '用于输入/展示文本',
        outputs: [
          {
            "name": "text",
            "type": "string",
          },
        ]
      },
      {
        id: '1',
        label: '图片组件',
        category: 'INPUT',
        code: 'INPUT_IMAGE',
        type: 'INPUT',
        icon: 'FileImageOutlined',
        description: '用于上传/展示图片',
        outputs: [
          {
            "name": "image",
            "type": "Image",
          },
        ]
      }
    ]
  },
  {
    id: 'output',
    header: '输出',
    children: [
      {
        id: '1',
        label: '输出节点',
        category: 'OUTPUT',
        code: 'OUTPUT',
        type: 'OUTPUT',
        icon: 'CodeOutlined',
        description: '用于上传/展示图片',
        inputs: [{
          name: '123',
          type: 'number'
        }, {
          name: '3222',
          type: 'Image'
        }
        ],
      }
    ]
  },
  {
    id: 'logic',
    header: '逻辑组件',
    children: [
      {
        id: '6',
        label: '迭代组件',
        parentId: 'logic',
        icon: 'ReloadOutlined',
      },
      {
        id: '7',
        label: 'Switch组件',
        icon: 'icon-fork',
        category: 'logic',
        code: 'SWITCH',
        type: 'SWITCH',
        description: '可以根据输入条件，拆分成多条分支',
        inputs: [{
          name: 'switchVal',
          type: 'boolean'
        }],
        vars: [{
          name: '类型',
          type: 'string',
          value: 'boolean',
          enum: ['string', 'boolean', 'number'],
        }],
        outputs: [{
          name: 'true',
          type: '_logic_',
        }, {
          name: 'false',
          type: '_logic_',
        }],
      },
      {
        id: '7',
        label: 'Select组件',
        icon: 'icon-merge',
        description: '用于switch的多分支结果合并后进行后续处理',
        category: 'logic',
        code: 'SELECT',
        type: 'SELECT',
        inputs: [],
        vars: [{
          name: '类型',
          type: 'string',
          value: 'boolean',
          enum: ['string', 'boolean', 'number'],
        }],
        outputs: [{
          name: 'output',
          type: 'any',
        }],
      },
    ],
  },
  {
    id: 'script',
    header: '脚本组件',
    children: [
      {
        id: '6',
        label: '脚本',
        category: 'SCRIPT',
        code: 'SCRIPT',
        type: 'SCRIPT',
        icon: 'FunctionOutlined',
        inputs: [],
        vars: [
          {
            name: 'script',
            type: 'string',
            value: ''
          },
          {
            name: 'scriptType',
            type: 'string',
            enum: ["python", "shell", "groovy"],
            value: ''
          },
        ],
        outputs: [],
      },
    ],
  },
  {
    id: 'AIGC',
    header: 'AIGC',
    children: [
      {
        id: '6',
        alias: 'ChatGPT',
        code: 'HTTP',
        type: 'HTTP',
        categoryId: 'xxxx',
        icon: 'MessageOutlined',
        inputs: [{
          name: 'text',
          type: 'string'
        }, {
          name: 'temperature',
          type: 'number'
        },
        {
          name: 'template',
          type: 'string',
        },
        ],
        vars: [],
        configs:[],
        outputs: [{
          name: 'message',
          type: 'string'
        }],
      },
      {
        id: '2',
        label: '音频转文字',
        parentId: 'AIGC',
        icon: 'AudioOutlined',
      },
      {
        id: '3',
        label: '文本转图片',
        parentId: 'AIGC1',
        icon: 'ReloadOutlined',
        category: 'AIGC',
        code: 'HTTP',
        type: 'HTTP',
        inputs: [{
          name: 'text',
          type: 'string'
        }],
        outputs: [{
          name: 'pic',
          type: 'Image'
        }],
      },
      {
        id: '4',
        label: '音频分片',
        parentId: 'AIGC1',
        icon: 'SplitCellsOutlined',
      },
    ],
  },

]