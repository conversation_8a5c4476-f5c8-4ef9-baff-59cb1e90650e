/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import styled from 'styled-components';

import { useGlobalState } from '@/hooks/useGlobalState';

const Title = styled.div`
  font-size: 18px;
  font-weight: bold;
  margin: 0 10px;
`;

const TitleBtn: React.FC = () => {
  const { globalState } = useGlobalState();
  const { app } = globalState;
  return <Title>{app?.name}</Title>;
};

export default TitleBtn;
