import { CloudSyncOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React from 'react';

const App: React.FC = (props: any) => {
  const onClose = () => {
    props.onClick();
  };

  return (
    <>
      <Button
        className="x6-toolbar-item xflow-toolbar-item"
        type="primary"
        onClick={onClose}
        icon={<CloudSyncOutlined />}
      >
        保存
      </Button>
    </>
  );
};

export default App;
