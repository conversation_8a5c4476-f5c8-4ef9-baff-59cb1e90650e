// @ts-nocheck
import './config-icon';

import { Graph } from '@antv/x6';
import { createGraphConfig, createHookConfig, DisposableCollection } from '@antv/xflow';
import { getExtendsMap, GROUP_HEIGH, GROUP_NODE_RENDER_ID, GROUP_WIDTH, NODE_CODE, NODE_HEIGHT, NODE_TYPE } from '@utils/constant';

import { getRealType } from '@/utils/node';

import type { IProps } from '../index';
import { Atomic } from '../react-node/atomic';
import { GroupNode } from '../react-node/group';
import { InputNode } from '../react-node/input-nodes';
import * as HooksApi from './cmds/hooks-services';
import {
  addNode2Group,
  blankClickEvent,
  edgeMouseenterEvent,
  edgeMouseleaveEvent,
  nodeEmbeddedEvent,
  nodeMove,
  nodeMoving,
} from './cmds/x6-events';

let currentParent = null;

const dagOptions: Graph.Options = {
  highlighting: {
    nodeAvailable: {
      name: 'className',
      args: {
        className: 'connecting',
      },
    },
    magnetAvailable: {
      name: 'className',
      args: {
        className: 'connecting',
      },
    },
    magnetAdsorbed: {
      name: 'className',
      args: {
        className: 'adsorbed',
      },
    },
    embedding: {
      name: 'className',
      args: {
        className: 'embedding',
      },
    },
  },
  embedding: {
    enabled: true,
    findParent({ node }) {
      // 边不能加入group
      if (node.isEdge()) {
        currentParent = null;
        return [];
      }
      const bbox = node.getBBox();
      const groups = this.getNodes().filter((group) => {
        // 只有 data.parent 为 true 的节点才是父节点
        const data = group.getData<any>();
        if (data && data.isGroup) {
          const targetBBox = group.getBBox();
          // 说明进入了这个组内
          if (bbox.isIntersectWithRect(targetBBox)) {
            return true;
          }
        }
        return false;
      });
      // 如果没有renderKey，说明是刚拖入的节点
      if (!node?.data?.renderKey) {
        if (groups.length) {
          currentParent = groups[0];
        } else {
          currentParent = null;
        }
      }
      return groups;
    },
  },
  mousewheel: {
    enabled: true,
    minScale: 0.05,
    maxScale: 2,
    factor: 1.1,
    modifiers: ['ctrl', 'meta'],
  },
  /** 缩放参数 */
  scaling: {
    min: 0.05,
    max: 2,
  },
  connecting: {
    snap: {
      radius: 100,
    },
    // 是否触发交互事件
    validateMagnet(props) {
      const { magnet, cell } = props;
      console.log('validateMagnet', magnet, cell, props);
      const portId = magnet?.parentNode?.getAttribute('port');
      const [, , , type] = portId.split('-');
      if (type === 'output') {
        return true;
      }
      return false;
    },
    // 显示可用的链接桩
    validateConnection(props) {
      const graph = this;
      const { sourceMagnet, targetMagnet, sourceCell, targetCell, sourcePort, targetPort } = props;
      // 找到targetView的所有输入边，这些对应的节点
      const edges = graph.getIncomingEdges(targetCell) || [];
      const connectedPorts = edges.map((v) => {
        if (v.data) {
          return v.data.targetPort;
        }
        return '';
      });

      // 不允许连接到自己
      if (sourceCell === targetCell) {
        return false;
      }
      if (!sourceMagnet || !targetMagnet) {
        return false;
      }
      if (!targetPort) {
        return false;
      }

      const sourcePortId = sourcePort || '';
      const targetPortId = targetPort || '';
      const source = sourcePortId.split('-');
      const target = targetPortId.split('-');
      const [, , sPortType, sType] = source;
      const [, , tPortType, tType] = target;
      const [sourceDateType, sourcePortType] = getRealType(sPortType);
      const [targetDateType, targetPortType] = getRealType(tPortType);
      const sourcePortData = sourceCell?.getPortProp(sourcePort);
      const targetPortData = targetCell?.getPortProp(targetPort);

      // 如果在group里，则只能连接 group 的元素
      if (sourceCell?.data.group || sourceCell?.data?.code === NODE_CODE.FOREACH) {
        let curGroup;
        // 如果当前节点是迭代器，则需要看 sourcePort 是什么
        // 如果是 forItem 或 forIndex，则只能连内部的节点
        if (sourcePortData?.data?.subType) {
          curGroup = sourceCell.id;
        } else if (sourceCell?.data?.group) {
          // 然后再看是否有group信息，有的话，则只能连当前group内的节点
          curGroup = sourceCell.data.group;
        }

        // 如果目标的port是有subType的，说明是迭代器的内部节点，则要看迭代器的id是否是当前groupID
        if (targetPortData?.data?.subType === "forOutput") {
          if (targetCell?.data.id !== curGroup) {
            return false;
          }
        } else if (targetCell?.data.group !== curGroup) {
          // 否则就看当前节点的group是否是当前的group
          return false;
        }
      }

      const extendsMap = getExtendsMap();
      // 如果是 any 直接连
      if (targetPortType === 'any' || sourcePortType === 'any') {
        return true;
      }

      // 如果外层类型不匹配
      if (!(extendsMap[sourceDateType] || sourceDateType).includes(targetDateType)) {
        return false;
      }
      // 输入必须链接输出，不能一样
      if (sType === tType) {
        return false;
      }
      // 如果是 targe 是 select 节点, 则 source 只能是分支
      if (targetCell?.data?.type === NODE_TYPE.SELECT) {
        // debugger
        if (!sourceCell?.data?.branch) {
          return false;
        }
        // 如果已经有一个分支链接上来了，需要看branch是否一致
        const edge = edges[0];
        if (edge) {
          const tmpSource = graph.getCellById(edge?.data.source);
          if (tmpSource?.data?.branch !== sourceCell?.data?.branch) {
            return false;
          }
        }
        // return true;
        console.log('ex', extendsMap[targetPortType], sourcePortType);
        return (extendsMap[targetPortType] || targetPortType).includes(sourcePortType);
      }
      // 不允许连接到已经连接的节点，除非是select节点
      if (
        connectedPorts.includes(targetPortId) &&
        targetCell?.data?.type !== NODE_TYPE.SELECT
      ) {
        return false;
      }
      // 不允许branch中的节点直接连到输出节点
      if (sourceCell?.data?.branch && targetCell?.data?.type === NODE_TYPE.OUTPUT) {
        return false;
      }
      return (extendsMap[sourcePortType] || sourcePortType).includes(targetPortType);
      // return targetPortType === sourcePortType;
    },
  },
};

export const useGraphHookConfig = createHookConfig<IProps>((config, proxy) => {
  // 获取 Props
  const props = proxy.getValue();
  console.log('get main props', props);
  config.setRegisterHook((hooks) => {
    const disposableList = [
      // 注册增加 react Node Render
      hooks.reactNodeRender.registerHook({
        name: 'add react node',
        handler: async (renderMap) => {
          renderMap.set(NODE_TYPE.ATOMIC, Atomic);
          renderMap.set(NODE_TYPE.OUTPUT, Atomic);
          renderMap.set(NODE_TYPE.JOIN, Atomic);
          renderMap.set(NODE_TYPE.SCRIPT, Atomic);
          renderMap.set(NODE_TYPE.SWITCH, Atomic);
          renderMap.set(NODE_TYPE.SELECT, Atomic);
          renderMap.set(NODE_TYPE.HTTP, Atomic);
          renderMap.set(NODE_TYPE.LLM, Atomic);
          renderMap.set(NODE_TYPE.DISPLAY, Atomic);
          renderMap.set(NODE_TYPE.INPUT, InputNode);
          renderMap.set(NODE_TYPE.FOREACH, GroupNode);
        },
      }),
      // 注册修改graphOptions配置的钩子
      hooks.graphOptions.registerHook({
        name: 'custom-x6-options',
        after: 'dag-extension-x6-options',
        handler: async (options) => {
          options.keyboard = {
            enabled: true,
          };
          Object.assign(options.connecting, dagOptions.connecting);
          Object.assign(options.mousewheel, dagOptions.mousewheel);
          Object.assign(options.scaling, dagOptions.scaling);
          Object.assign(options.highlighting, dagOptions.highlighting);
          options.embedding = dagOptions.embedding;
          options.grid = true;
          console.log('options=======', options);
        },
      }),
      // 图相关
      hooks.graphMeta.registerHook({
        name: 'get graph meta from backend',
        handler: async (args) => {
          args.graphMetaService = HooksApi.queryGraphMeta;
        },
      }),
      // 保存图数据
      hooks.saveGraphData.registerHook({
        name: 'save graph data',
        handler: async (args) => {
          if (!args.saveGraphDataService) {
            args.saveGraphDataService = HooksApi.saveGraphData;
          }
        },
      }),

      // node event 相关
      hooks.x6Events.registerHook({
        name: 'bind node event',
        handler: async (args) => {
          // 替换node:moving
          const idx = args.findIndex(v => v.eventName === 'node:moving');
          args.splice(idx, 1, nodeMoving);
          args.push(nodeEmbeddedEvent);
          args.push(nodeMove);
          args.push(nodeMoving);
        },
      }),
      // edge event 相关
      hooks.x6Events.registerHook({
        name: 'bind edge event',
        handler: async (args) => {
          if (!props.preview) {
            args.push(edgeMouseenterEvent);
            args.push(edgeMouseleaveEvent);
          }
        },
      }),
      // graph event 相关
      hooks.x6Events.registerHook({
        name: 'bind graph event',
        handler: async (args) => {
          args.push(blankClickEvent);
        },
      }),
      // 添加节点
      hooks.addNode.registerHook({
        name: 'get node config from backend api',
        handler: async (args) => {
          args.createNodeService = HooksApi.addNode;
        },
      }),
      // 添加节点完成后
      hooks.addNode.registerHook({
        name: 'after add node',
        handler: async (handlerArgs, handler) => {
          const main = async (args) => {
            const res = await handler(args);
            const { nodeCell: node } = res;
            const graph = node.model.graph;
            // 如果发现是有 group 的，则执行添加 group 的命令
            if (node.data.isGroup) {
              node.toBack();
            }
            if (currentParent) {
              addNode2Group({
                currentParent,
                node,
              });
            } else if (args.drag) {
              // 如果是拖动进入的，自动选中
              graph.resetSelection([node]);
            }
            console.log('after res', res);
            HooksApi.saveGraphConfigToLocal()
            return res;
          };
          return main;
        },
      }),
      // 删除节点
      hooks.delNode.registerHook({
        name: 'delNode config from backend api',
        handler: async (args) => {
          args.deleteNodeService = HooksApi.delNode;
        },
      }),
      // 删除节点后执行
      hooks.delNode.registerHook({
        name: 'after delNode from backend api',
        handler: HooksApi.afterActionSaveToLocal,
      }),
      // 添加边
      hooks.addEdge.registerHook({
        name: 'addEdge config from backend api',
        handler: HooksApi.addEdgeAndSaveLocal,
      }),
      // 删除边
      // hooks.delEdge.registerHook({
      //   name: 'delEdge config from backend api',
      //   handler: async (args) => {
      //     args.deleteEdgeService = HooksApi.delEdge;
      //   },
      // }),
      // 删除边后执行
      hooks.delEdge.registerHook({
        name: 'after delEdge from backend api',
        handler: HooksApi.afterActionSaveToLocal,
      }),
      // 注册增加 graph event
      // hooks.x6Events.registerHook({
      //   name: 'add',
      //   handler: async (events) => {
      //     events.push({
      //       eventName: 'node:moved',
      //       callback: (e, cmds) => {
      //         const { node } = e;
      //         cmds.executeCommand<NsNodeCmd.MoveNode.IArgs>(
      //           XFlowNodeCommands.MOVE_NODE.id,
      //           {
      //             id: node.id,
      //             position: node.getPosition(),
      //           },
      //         );
      //       },
      //     } as NsGraph.IEvent<'node:moved'>);
      //   },
      // }),
    ];
    const toDispose = new DisposableCollection();
    toDispose.pushAll(disposableList);
    return toDispose;
  });
});

export const useGraphConfig = (preview) =>
  createGraphConfig((config) => {
    config.setNodeTypeParser((node) => {
      return node.type;
    });
    /** 可用配置项参考: https://x6.antv.vision/zh/docs/api/graph/graph */
    config.setX6Config({
      /** 画布网格 */
      grid: true,
      // resizing: {
      //   enabled: (node) => {
      //     return node?.data?.isGroup;
      //   },
      //   minWidth: (node) => {
      //     return GROUP_WIDTH;
      //   },
      //   minHeight: (node) => {
      //     return GROUP_HEIGH;
      //   },
      // },
      interacting: {
        /** 节点默认可以被移动 */
        nodeMovable: function () {
          return !preview;
        },
        /** 边上标签默认不可以被移动 */
        edgeLabelMovable: function () {
          return false;
        },
      },
    });
  });
