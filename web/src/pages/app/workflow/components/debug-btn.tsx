/* eslint-disable react/display-name */
import { PauseCircleOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useXFlowApp, XFlowDagCommands, XFlowGraphCommands } from '@antv/xflow';
import { Button, message } from 'antd';
import { useEffect } from 'react';

import { AppApi } from '@/api/app';
import { useGlobalState } from '@/hooks/useGlobalState';
import { NODE_TYPE } from '@/utils/constant';

import * as HookApi from '../config/cmds/hooks-services';
import { getRunModal } from '../config/config-model-service';
import { getAppState, getGlobalState } from '@/utils/state';

const saveFn = async (meta, graphData, isAgent) => {
  if (isAgent) {
    // 获取当前的运行状态
    const { res } = await HookApi.saveGraphData(meta, graphData, false);

  }

}

export const DebugButton = ({ type }) => {
  const app = useXFlowApp();
  const agent = type === 'agentWorkflow';
  const { globalState, updateGlobalState } = useGlobalState();

  const watchRunState = async () => {
    if (app) {
      const runModal = await getRunModal(app.modelService);
      runModal.watch((v) => {
        console.log('va', v);
        if (v.status === 'success' || v.status === 'failed') {
          updateGlobalState('running', false);
        }
      });
    }
  };

  useEffect(() => {
    watchRunState();
  }, [app]);

  const pause = async () => {
    updateGlobalState('running', false);
    return app.executeCommand(XFlowDagCommands.QUERY_GRAPH_STATUS.id, {
      graphStatusService: HookApi.stopGraphStatusService,
      loopInterval: 1000,
    });
  };

  const validation = async () => {
    const { nodes } = await HookApi.getSaveData(undefined, true);
    
    // const nodes = await app.getAllNodes();
    console.log('nodes', nodes);
    let inputs: any = {};
    const map = {};
    let errorMessage = '';
    // 获取所有的输入节点
    const inputNodes = nodes
      .filter((v: any) => v.type === NODE_TYPE.INPUT)
      .map((v) => {
        // 拿到所有输入连接桩，如果value是对象，且outputs不止一个，说明需要把对象进行拆解
        if (typeof v.value === 'object' && v.outputs.length > 1) {
          inputs = { ...inputs, ...v.value };
        } else {
          // 否则取第一个元素作为输入节点的输出
          const name = v.outputs[0].name;
          if (v && typeof v.value !== 'undefined') {
            if (map[name]) {
              errorMessage = `【${v.name}】的参数【${name}】重名，请检查`;
            }
            map[name] = 1;
            inputs[name] = v.value;
          } else {
            errorMessage = `【${v.name}】的【${name}】没有输入信息，请确认`;
          }
        }
        return v;
      });
    // 检查输入节点
    if (!inputNodes.length) {
      errorMessage = '没有发现输入节点，请确认';
    }
    // 获取所有输出节点
    const outputsNodes = nodes.filter((cell: any) => cell.type === NODE_TYPE.OUTPUT);
    if (!outputsNodes.length) {
      errorMessage = '没有输出节点，请检查';
    }
    if (errorMessage) {
      message.error(errorMessage);
      return;
    }
    return inputs;
  };

  const call = async () => {
    const querySample = getAppState('querySample');
    const [business, scene] = querySample ? querySample.split('@'): ['', ''];
    if (globalState.running) {
      pause();
      return;
    }
    const inputs = await validation();
    console.log('inputs', inputs);
    if (!inputs) {
      return;
    }
    updateGlobalState('running', true);
    let res: any = { runID: '' };
    let snapId = '';
    try {
      // 触发debug保存
      await app.executeCommand(XFlowGraphCommands.SAVE_GRAPH_DATA.id, {
        saveGraphDataService: async (meta, graphData) => {
          const { res: response } = await HookApi.saveGraphData(meta, graphData, !agent)
          snapId = response?.id;
        }
      });
      res = agent ? await AppApi.triggerAgentWorkflow('', inputs, snapId) : await AppApi.triggerApp('', inputs, true, {
        business: business || 'langbase',
        scene: scene || 'langbase-test',
      });
      // res = await AppApi.triggerApp('', inputs, true);
      if (res) {
        message.success('触发执行成功');
      }
    } catch (err: any) {
      window.corona.warn('call 执行失败', err);
    }
    if (!res.runID) {
      updateGlobalState('running', false);
      return;
    }
    return app.executeCommand(XFlowDagCommands.QUERY_GRAPH_STATUS.id, {
      graphStatusService: HookApi.graphStatusService,
      loopInterval: 3000,
      runID: res.runID,
    });
  };
  return (
    <Button
      danger={globalState.running}
      icon={globalState.running ? <PauseCircleOutlined /> : <PlayCircleOutlined rev={undefined} />}
      onClick={call}
      style={{ flex: 1, color: globalState.running ? 'red' : '#2f77ff' }}
    >
      {globalState.running ? '暂停' : '调试'}
    </Button>
  );
};

export default DebugButton;
