/* eslint-disable react/jsx-key */
import { MoreOutlined, SaveOutlined, ExportOutlined, ImportOutlined, EyeOutlined, ReloadOutlined, SwapOutlined } from '@ant-design/icons';
import { XFlowGraphCommands, useXFlowApp } from '@antv/xflow';
import { Button, Dropdown, message, Modal } from 'antd';
import type { MenuProps } from 'antd';
import React from 'react';
import fileSaver from 'file-saver';
import * as HookApi from '../config/cmds/hooks-services';
import { JSONParse } from '@/utils/common';
import { useGlobalState } from '@/hooks/useGlobalState';
import { pick } from 'lodash';
import FileReaderInput from 'react-file-reader-input';
import { AppApi } from '@/api/app';
import { $ } from '@/utils/state';
import VersionList from '@/components/version-list';

const { confirm } = Modal;

const getItems = (onClick): MenuProps['items'] => [
  {
    label: (
      <span style={{ fontSize: '12px' }} onClick={() => onClick('switch')}>
        <SwapOutlined  style={{ marginRight: 5 }} />
        切换快照
      </span>
    ),
    key: '0',
  },
  {
    label: (
      <span style={{ fontSize: '12px' }} onClick={() => onClick('export')}>
        <ExportOutlined style={{ marginRight: 5 }} />
        文件导出
      </span>
    ),
    key: '1',
  },
  {
    label: (
      <FileReaderInput
        as="text"
        id="my-file-input"
        onChange={(e, val) => onClick('import', e, val)}
      >
        <span style={{ fontSize: '12px' }} onClick={() => onClick('import')}>
          <ImportOutlined style={{ marginRight: 5 }} />
          文件导入
        </span>
      </FileReaderInput>
    ),
    key: '2',
  },
];

const MoreButton: React.FC<any> = (props: any) => {
  const xflowApp = useXFlowApp();
  const { globalState, updateGlobalState } = useGlobalState();
  const { app } = globalState;

  const switchSnapshot = () => {
    console.log('切换快照')
    confirm({
      width: 600,
      title: <span>选择版本<span style={{
        fontSize: 12,
        fontWeight: 'normal',
        color: '#999',
        marginLeft: 5
      }}>(每次保存或调试都会生成一个快照)</span></span>,
      content: <VersionList type="snapshot" app={app} updateGlobalState={updateGlobalState}></VersionList>,
      onOk() { },
      onCancel() { },
    });
  }

  const handleMore = async (key, e, data) => {
    console.log("132", key);
    const saveData = await HookApi.getSaveData();
    if (key === 'switch') {
      switchSnapshot()
    } else if (key === 'export') {
      console.log(saveData);
      const blob = new Blob([JSON.stringify(saveData)], {
        type: 'text/plain;charset=utf-8',
      });
      fileSaver.saveAs(blob, `${app.name}.conf`);
    }
    if (key === 'import') {
      if (data && data[0] && data[0][0]) {
        const rawData = data[0][0].target.result;
        const configs: any = JSONParse(rawData);
        if (!configs || !configs.workflowId) {
          message.error('导入数据异常');
          return;
        } else {
          const newConfigs = {
            ...pick(configs, ['edges', 'inputs', 'outputs', 'nodes']),
            ...pick(saveData, [
              'version',
              'workflowEngineId',
              'workflowEngineName',
              'workflowId',
            ]),
          };
          confirm({
            content: '是否覆盖当前页面',
            onOk: async () => {
              $.setConfig(JSON.parse(rawData));
              const res = await AppApi.saveAppConfigSnapshot('', newConfigs);
              if (res) {
                window.location.reload();
              }
            },
          });
        }
        console.log('raw', rawData);
      }
    }
    if (props.onClick) {
      props.onClick();
    }
  };

  const handleSave = () => {
    xflowApp.executeCommand(
      XFlowGraphCommands.SAVE_GRAPH_DATA.id,
      {
        saveGraphDataService: (meta, graphData) =>
          HookApi.saveGraphData(meta, graphData),
      },
    );
  }

  return (
    <Dropdown.Button menu={{ items: getItems(handleMore) }} style={{ width: 100 }} buttonsRender={buttons => [<Button style={{ width: 70 }} icon={<SaveOutlined />} onClick={handleSave}>保存</Button>, <Button style={{ width: 30 }} icon={<MoreOutlined />}></Button>]}>保存</Dropdown.Button>
  );
};

export default MoreButton;
