/* eslint-disable react/display-name */
import { SettingOutlined } from '@ant-design/icons';
import { useXFlowApp, XFlowGraphCommands } from '@antv/xflow';
import { Button, Drawer, Form, Input, message, Popover, Tooltip } from 'antd';
import { $, addLLmConfig, getAppId } from '@/utils/state';
import { useState } from 'react';

import { useGlobalState } from '@/hooks/useGlobalState';
import { IconFont } from '@/components/icons';

export const ConfigButton = () => {
  const { globalState, updateGlobalState } = useGlobalState();
  const app = useXFlowApp();

  const handleSubmit = () => {
    app.executeCommand(XFlowGraphCommands.GRAPH_TOGGLE_MULTI_SELECT.id, {});
    updateGlobalState('isMultiSelectionActive', !globalState.isMultiSelectionActive);
  };

  return (
    <Tooltip
      title={globalState.isMultiSelectionActive ? '退出框选模式(Alt+Space)' : '进入框选模式(Alt+Space)'}
      placement="bottom"
    >
      <Button
        icon={<IconFont type="icon-kuangxuan" />}
        style={{
          width: '40px', color: globalState.isMultiSelectionActive ? '#1890ff' : '#000',
          border: globalState.isMultiSelectionActive ? '1px solid #1890ff' : 'none',
        }}
        onClick={handleSubmit}
      >
      </Button>
    </Tooltip>
  );
};

export default ConfigButton;
