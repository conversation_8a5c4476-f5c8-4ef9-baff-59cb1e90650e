/* eslint-disable react/display-name */
import { PauseCircleOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useXFlowApp, XFlowDagCommands, XFlowGraphCommands } from '@antv/xflow';
import { Button, Flex, message, Tooltip } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import LangBot, { ChatView } from '@music/lang';
import { $ } from '@/utils/state';

import { AppApi } from '@/api/app';
import { useGlobalState } from '@/hooks/useGlobalState';
import { NODE_TYPE } from '@/utils/constant';

import * as HookApi from '../config/cmds/hooks-services';
import styled from 'styled-components';
import { getCustomModal } from '../config/config-model-service';
import { pick } from 'lodash';
import { ClearIcon } from '@/components/icons';
import ParamsDebug from '../../components/chat-bot/params-debug';
import TopBar from '../../components/chat-bot/top-bar';
import { NodeFlowModal } from '../../components/overview/chatflow-log';

const LangRoot = styled.div`
  width: 500px;
  height: calc(100vh - 200px);
  position: absolute;
  top: 40px;
  left: -200px;
`;

const diff = (a, b, args) => JSON.stringify(pick(a, args)) === JSON.stringify(pick(b, args));
const compare = (a, b, args) => {
  if (a.length !== b.length) {
    return false;
  }
  const map = {};
  a.forEach((v) => {
    map[v.id] = v;
  })
  for (let i = 0, len = b.length; i < len; i++) {
    const edge = b[i];
    if (!map[edge.id] || !diff(edge, map[edge.id], args)) {
      console.log(edge, map[edge.id])
      return false;
    }
  }
  return true;
}

// const Extra = ({ message }: { message: MessageDTO }) => {
const Extra = ({ message }: { message: any }) => {
  console.log('message', message);
  const nodeList = message.extra.filter(v => v?.status).filter(v => v.status === 'done' || v.status === 'error' || v.status === 'finish').map((v) => {
    return {
      id: v.nodeId,
      nodeId: v.nodeId,
      status: v.status,
      nodeName: v.nodeName,
      inputContent: JSON.stringify(v.inputs || {}),
      outputContent: JSON.stringify(v.outputs || {}),
    }
  })
  const [visible, setVisible] = useState(false);

  return <>
    <span onClick={() => setVisible(true)} style={{
      background: 'rgb(109 143 230 / 9%)',
      color: '#6d8fe6',
      padding: '2px 8px',
      borderRadius: '4px',
    }}>查看详情</span>
    <NodeFlowModal
      visible={visible}
      onClose={() => setVisible(false)}
      nodeList={nodeList}
    />
  </>
}

const renderMessage = (message) => {
  if (message.type !== 'workflow') {
    return {}
  }
  return {
    extra: <Extra message={message} />
  }
}

export const DebugAgentButton = () => {
  const xflowApp = useXFlowApp();
  const { globalState, updateGlobalState } = useGlobalState();
  const [paramsValue, setParamsValue] = useState({});
  const [showChat, setShowChat] = useState(false);
  const greeting = $.config.greeting;
  // const paramsValueRef = useRef({});
  // const paramsInPrompt = useRef([]);
  const [paramsInPrompt, setParamsInPrompt] = useState([]);
  const [configId, setConfigId] = useState('');
  const [show, setShow] = useState(false);
  const { app } = globalState;
  const ref = useRef(null);

  useEffect(() => {
    console.log('paramsInPrompt', paramsInPrompt);
  }, [paramsInPrompt])

  const validation = async () => {
    const { nodes } = await HookApi.getSaveData(undefined, true);
    // const nodes = await app.getAllNodes();
    console.log('nodes', nodes);
    let inputs: any = {};
    const map = {};
    let params = [];
    let errorMessage = '';
    // 获取所有的输入节点
    const inputNodes = nodes
      .filter((v: any) => v.type === NODE_TYPE.INPUT)
      .map((v) => {
        // 拿到所有输入连接桩，如果value是对象，且outputs不止一个，说明需要把对象进行拆解
        if (typeof v.value === 'object' && v.outputs.length > 1) {
          inputs = { ...inputs, ...v.value };
        } else {
          // 否则取第一个元素作为输入节点的输出
          const name = v.outputs[0].name;
          const title = v.outputs[0].title;
          if (map[name]) {
            errorMessage = `【${v.name}】的参数【${name}】重名，请检查`;
          }
          map[name] = 1;
          if (name !== 'message') {
            inputs[name] = {
              title,
              value: v.value,
            };
          }
        }
        return v;
      });
    // 检查输入节点
    if (!inputNodes.length) {
      errorMessage = '没有发现输入节点，请确认';
    }
    // 获取所有输出节点
    const outputsNodes = nodes.filter((cell: any) => cell.type === NODE_TYPE.OUTPUT);
    if (!outputsNodes.length) {
      errorMessage = '没有输出节点，请检查';
    }
    if (errorMessage) {
      message.error(errorMessage);
      return;
    }
    params = Object.keys(inputs).map(v => ({
      key: v,
      type: 'area',
      title: inputs[v].title,
      required: false,
    }));
    setParamsInPrompt(params);
    return inputs;
  };

  const setPreview = async (preview) => {
    const ctx = await getCustomModal(xflowApp.modelService);
    const values = await ctx.getValidValue();
    console.log('values', values);
    ctx.setValue({
      ...values,
      preview,
    });
  }

  const onParamsChange = parameters => {
    setParamsValue(parameters)
    // paramsValueRef.current = parameters;
  };

  const call = async (ev) => {
    const inputs = await validation();
    console.log('inputs..,', inputs);
    if (inputs) {
      setShow(!show);
    }
    if (show) {
      setPreview(false);
      return;
    }
    setPreview(true);
    const change = await xflowApp.executeCommand(XFlowGraphCommands.SAVE_GRAPH_DATA.id, {
      saveGraphDataService: async (meta, graphData) => {
        const config = await $.getConfig();
        const { nodes, edges } = config;
        const sameEdges = compare(edges, graphData.edges, ['source', 'sourcePort', 'target', 'targetPort']);
        const sameNodes = compare(nodes, graphData.nodes, HookApi.nodeArgs);
        // 保存后会出现edges和nodes相同且configId.current存在的情况，不会更新会话，要判断两个id是否相同
        if (sameEdges && sameNodes && configId && configId === config.configId) {
          return false;
        }
        const { res: response } = await HookApi.saveGraphData(meta, graphData, false)
        if (response?.id) {
          setConfigId(response.id)
          setShowChat(true)
          return true;
        }
      }
    });
  };
  return (
    <div style={{ flex: 1, position: 'relative' }}>
      <Button
        ref={ref}
        icon={show ? <PauseCircleOutlined /> : <PlayCircleOutlined rev={undefined} />}
        danger={show}
        onClick={call}
        style={{ width: '100%', color: show ? 'red' : '#2f77ff' }}
      >
        {show ? '结束' : '调试'}
      </Button>
      <div
        style={{
          position: 'fixed', top: 40, left: -200, width: 500, height: 600, visibility: show ? 'visible' : 'hidden',
          padding: '10px 10px',
          background: '#fff',
          borderRadius: 10,
          boxShadow: '0 0 10px 0 rgba(0, 0, 0, 0.1)',
          zIndex: 1000,
        }}
      >
        {configId && <ChatView
          appId={app.id}
          type='chat-flow'
          key={configId + greeting}
          greetings={greeting || 'Hi，我是 LangBot 智能助手，请问有什么可以帮您~'}
          host={window.location.host}
          configId={configId}
          renderMessage={renderMessage}
          float={false}
          rootId="lang-root"
          paramsValue={paramsValue}
          debug={true}
          showParams={false}
          renderNavbar={navbar => {
            const resetIcon = navbar.rightContent?.find(item => item.label === '删除会话记录')
            return <TopBar {...navbar} style={{ fontSize: 14, color: '#aaa' }}
              rightContent={<Flex justify="end" gap={8}>
                <ParamsDebug
                  paramsInPrompt={paramsInPrompt}
                  onChange={onParamsChange}
                  value={paramsValue}
                />
                <Tooltip title={resetIcon.label} >
                  <ClearIcon
                    style={{ cursor: 'pointer', marginLeft: 10 }}
                    onClick={(val) => {
                      resetIcon.onClick(val)
                    }}
                  />
                </Tooltip>
              </Flex>}
            />
          }}
        />
        }
      </div>
    </div >
  );
};

export default DebugAgentButton;
