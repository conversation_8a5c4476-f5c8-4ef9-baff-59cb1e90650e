/* eslint-disable react/display-name */
import styled from 'styled-components';
import { DebugButton } from './debug-btn';
import DeployButton from './deploy-btn';
import MoreButton from './more-btn';
import DebugAgentButton from './debug-agent-btn';
import ConfigButton from './config-btn';
import GroupButton from './group-btn';
const Container = styled.div<{ width?: string, right?: string }>`
  width: ${props => props.width || '400px'};
  right: ${props => props.right || '-180px'};
  transform: translateX(-50%);
  position: absolute;
  z-index: 100;
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 40px;
  gap: 10px;
  top: 10px;
  button {
    border-radius: 2px;
    padding: 0;
  }
`;


export const DebugCard = ({ type }: { type: string }) => {
  let width = '380px';
  let right = '-120px';
  if (type === 'agentWorkflow') {
    width = '440px';
    right = '-180px';
  }
  return (
    <Container width={width} right={right}>
      <GroupButton></GroupButton>
      {type === 'agentWorkflow' ? <DebugAgentButton></DebugAgentButton> : <DebugButton type={type}></DebugButton>}
      {type === 'agentWorkflow' ? <ConfigButton></ConfigButton> : null}
      {/* <DebugButton type={type}></DebugButton> */}
      <MoreButton type={type}></MoreButton>
      <DeployButton type={type}></DeployButton>
    </Container>
  );
};


export default DebugCard;
