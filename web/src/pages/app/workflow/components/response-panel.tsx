/* eslint-disable react/display-name */
/* eslint-disable react/no-children-prop */
// @ts-nocheck
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import type { IPosition } from '@antv/xflow';
import { MODELS, useXFlowApp } from '@antv/xflow';
import { Button, Card, Col, Flex, Row, Spin } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import React from 'react';
import ReactJson from 'react-json-view';
import styled from 'styled-components';

import { IRunState } from '@/interface';
import { formatRes } from '@/pages/preview/workflow-playground-content';
import { NODE_TYPE } from '@/utils/constant';
import { getGraphData, getGraphInstance } from '@/utils/flow';

import { getRunModal } from '../config/config-model-service';
import { Collapse } from 'antd/lib';
import { useNode } from '../hooks/useNode';
import { AppApi } from '@/api/app';
import { get, set } from 'lodash';

const CallbackContainer = styled.div`
  height: ${({ collapseLevel, stepHeight }) => (collapseLevel ? `${collapseLevel * (stepHeight || 300)}px` : '0')};
  width: calc(100% - 700px);
  box-sizing: border-box;
  transition: height 0.4s;
  position: absolute;
  bottom: 0;
  left: 50%;
  background: #fff;
  border-top: 1px solid #efefef;
  padding: ${({ collapseLevel }) => (collapseLevel ? '16px' : '0')};
  padding-left: 20px
  color: #777;
  border-radius: 20px;
  transform: translateX(-50%);

  h1 {
    font-size: 14px;
    margin: 0;
    color: #1f3fa0;
    height: 30px;
  }
  h3 {
    color: #333;
    font-size: 12px;
    font-weight: bold;
    margin: 8px 0;
  }
`;

const Response = styled.div`
  height: ${({ collapseLevel }) =>
    collapseLevel ? `${collapseLevel * 300 - 50}px` : '0'};
  overflow: auto;
`;

const CollapseButtonBox = styled.div`
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -12px;
  display: flex;
`;

const CollapseButton = styled.div`
  height: 12px;
  width: 42px;
  border: 1px solid #efefef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
  background: #fff;
  border-bottom: none;
`;

export interface IPanelProps {
  position: IPosition;
}

export function CollapsedCard(props) {
  const { children, defaultLevel } = props;
  const [collapseLevel, setCollapsed] = useState(defaultLevel === undefined ? 1 : defaultLevel);

  const handleCollapse = (type) => {
    if (type === 'up') {
      setCollapsed(Math.min(collapseLevel + 1, 2));
    } else {
      setCollapsed(Math.max(collapseLevel - 1, 0));
    }
  };
  const header = props.header ? props.header(collapseLevel, setCollapsed) : (<CollapseButtonBox>
    {collapseLevel < 2 && (
      <CollapseButton
        onClick={() => handleCollapse('up')}
        children={<CaretUpOutlined />}
      ></CollapseButton>
    )}
    {collapseLevel > 0 && (
      <CollapseButton
        onClick={() => handleCollapse('down')}
        children={<CaretDownOutlined />}
      ></CollapseButton>
    )}
  </CollapseButtonBox>);

  return <CallbackContainer collapseLevel={collapseLevel} {...props}>
    {header}
    {children(collapseLevel)}
  </CallbackContainer>
}

const pick = (obj: any, params: Array<string>) => {
  const newObj: any = {};
  params.forEach((key) => {
    newObj[key] = obj[key];
  });
  return newObj;
};

const formatInputOrOutput = (params, data) => {
  if (!Array.isArray(params)) {
    return [];
  }
  if (typeof data !== 'object') {
    return [];
  }
  // const keys = Object.keys(data);
  let newData = { ...data };
  // 处理HTTP层级异常
  // if (keys.length !== params.length && typeof data[keys[0]] === 'object') {
  //   newData = data[keys[0]];
  // }
  return (params || []).map((v) => ({
    ...v,
    value: (newData || {})[v.name] ?? '',
  }));
};

const getParents = (node, graph) => {
  let result = [];
  let parent;
  if (node.data.group) {
    parent = graph.getCellById(node.data.group);
    result.push({ id: parent.id, index: parent.data.index || 0 });
  }
  if (parent?.data?.group) {
    return result.concat(getParents(parent, graph));
  }
  return result
}

export const ResponsePanel = (props) => {
  const app = useXFlowApp();
  const [node, setNode] = useState({} as any);
  const { updateNode } = useNode();
  const [runState, setRunState] = useState({} as IRunState);
  const [current, setCurrent] = useState({} as any);
  const [loading, setLoading] = useState(false);

  // 监听model变化
  useEffect(() => {
    let d = null;
    async function getModel() {
      const model = await MODELS.SELECTED_NODE.getModel(app.modelService);
      d = model.watch((val: any) => {
        setNode(val);
      });
      const runModel = await getRunModal(app.modelService);
      runModel.watch((val: IRunState) => {
        setRunState(val as IRunState);
        console.log("runModal", val);
        // 查找里面所有的迭代器节点，查找里面的count然后整体设置到node上
        val.nodes.filter(v => v.count > 0).map(v => updateNode(v.nodeId, { count: v.count }));
      });
    }
    if (app) {
      getModel();
    }
    return () => {
      if (d && d.dispose) {
        d.dispose();
      }
    };
  }, [app]);

  const setCurrentDisplay = (res) => {
    const outputs = formatInputOrOutput(node?.data?.outputs, res?.outputs);
    const inputs = formatInputOrOutput(node?.data?.inputs, res?.inputs);
    console.log('inputs', inputs, outputs);
    setCurrent({ ...find, inputs, outputs, status: pick(res, ['message', 'status']) });
  };

  /**
   * 获取迭代器的输出信息
   * @param parents 
   * @param runId 
   */
  const getParentsGroupInfo = (parents, runId) => {
    parents.map((node, idx) => {
      if (idx !== parents.length - 1) {
        AppApi.getWorkFlowNodeStatus(null, runId, node.id, parents.slice(idx + 1), true).then(res => {
          if (res) {
            updateNode(res.nodeId, { count: res.count })
          }
        })
      }
    })
  }

  useEffect(() => {
    // 说明是点击画布，返回整体输入输出
    if (!node) {
      const graph = getGraphInstance();
      let inputs = [];
      let outputs = [];
      if (graph) {
        const nodes = graph.getNodes();
        const outputNode = nodes.find((v) => v.data.type === NODE_TYPE.OUTPUT);
        inputs = [
          { name: 'input', type: 'object', title: '整体输入', value: runState.inputs },
        ];
        outputs = formatInputOrOutput(outputNode?.data?.inputs, runState?.outputs);
      }
      setCurrent({
        ...runState,
        inputs,
        outputs,
        status: pick(runState, ['message', 'status']),
      });
    }
    // 这里专门处理迭代器

    // 如果是迭代器内部节点，则需要通过接口查数据
    if (node?.data?.group && runState.runId) {
      // 循环获取所有父节点的信息
      const parents = getParents(node, getGraphInstance());
      if (!parents.length) {
        return;
      }
      // 如果parents.length > 1 说明有嵌套，需要把嵌套的group相关count信息记录下来
      if (parents.length > 1) {
        getParentsGroupInfo(parents, runState.runId);
      }

      setLoading(true);
      setCurrent({});
      // 要处理迭代器内部新增的节点报错
      AppApi.getWorkFlowNodeStatus(null, runState.runId, node.id, parents, true).then(res => {
        if (res) {
          setCurrentDisplay(res);
        }
      }).finally(() => {
        setLoading(false);
      })
    } else if (runState.inputs && runState.nodes && runState.nodes.length) {
      const find = runState.nodes.find((n) => n.nodeId === node?.id);
      if (find) {
        setCurrentDisplay(find);
      }
    }
  }, [runState, node])

  return (
    <CollapsedCard {...props} stepHeight={300}>
      {collapseLevel =>
        <Row>
          <Col span={8}>
            <h1>输入(Inputs)</h1>
            <Spin spinning={loading}>
              <Response collapseLevel={collapseLevel}>{formatRes(current?.inputs || {})}</Response>
            </Spin>
          </Col>
          <Col span={8}>
            <h1>输出(Outputs)</h1>
            <Spin spinning={loading}>
              <Response collapseLevel={collapseLevel}>{formatRes(current?.outputs || {})}</Response>
            </Spin>
          </Col>
          <Col span={8}>
            <h1>节点状态(Status)</h1>
            <Response collapseLevel={collapseLevel}>
              <ReactJson
                theme="summerfruit:inverted"
                name={false}
                displayDataTypes={false}
                onSelect={(v) => console.log('v', v)}
                iconStyle="square"
                collapsed={2}
                src={current?.status || { status: '', message: '' }}
              />
            </Response>
          </Col>
        </Row>
      }
    </CollapsedCard>
  );
};

export default (props: any) => {
  return <ResponsePanel {...props} />;
};
