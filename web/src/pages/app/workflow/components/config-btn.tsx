/* eslint-disable react/display-name */
import { SettingOutlined } from '@ant-design/icons';
import { useXFlowApp } from '@antv/xflow';
import { Button, Drawer, Form, Input, message } from 'antd';
import { $, addLLmConfig, getAppId } from '@/utils/state';
import { useState } from 'react';

import { useGlobalState } from '@/hooks/useGlobalState';

export const ConfigButton = () => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const app = useXFlowApp();
  const { globalState, updateGlobalState } = useGlobalState();

  const handleSubmit = (values: any) => {
    $.setConfig({
      greeting: values.greeting
    });
    message.success('设置已保存');
    setOpen(false);
  };

  return (
    <>
      <Button
        icon={<SettingOutlined />}
        style={{ flex: 1 }}
        onClick={() => setOpen(true)}
      >
        配置
      </Button>
      
      <Drawer
        title="全局配置"
        placement="right"
        onClose={() => setOpen(false)}
        open={open}
        width={400}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            greeting: $.config.greeting || 'Hi，我是 LangBot 智能助手，请问有什么可以帮您~'
          }}
        >
          <Form.Item
            label="开场白"
            name="greeting"
            rules={[{ required: true, message: '请输入开场白' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入对话开场白"
            />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              保存
            </Button>
          </Form.Item>
        </Form>
      </Drawer>
    </>
  );
};

export default ConfigButton;
