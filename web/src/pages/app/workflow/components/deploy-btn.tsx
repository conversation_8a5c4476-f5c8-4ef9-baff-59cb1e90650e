/* eslint-disable react/jsx-key */
import { CloudSyncOutlined, MoreOutlined, ReloadOutlined } from '@ant-design/icons';
import {
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  CopyFilled,
  ExclamationCircleTwoTone,
} from '@ant-design/icons';
import { useXFlowApp } from '@antv/xflow-core';
import { Button, Card, Collapse, Drawer, Dropdown, Form, Input, MenuProps, message, Modal, Space, Spin } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import ReactJson from 'react-json-view';
import * as CustomCommands from '../cmd-extensions/constants';
import * as HookApi from '../config/cmds/hooks-services';

import { copyText } from '@/utils/common';
import { getAppId } from '@/utils/state';
import { getSaveData } from '../config/cmds/hooks-services';
import { useGlobalState } from '../../../../hooks/useGlobalState';
import { IAppType } from '../../../../interface';
import { AccessContent } from '../../components/agent-dev/command/access-drawer';
import { PopoApi } from '@/api/popo';
import { useSafeState } from 'ahooks';
import { ParamTypeEnum } from '@/interface/agent';
import { showPublishModal } from '@/components/pub-confirm';
import VersionList from '@/components/version-list';
import { AppApi } from '@/api/app';


const { confirm } = Modal;

const getItems = (onClick): MenuProps['items'] => [
  {
    label: (
      <span style={{ fontSize: '12px' }} onClick={() => onClick('rollback')}>
        <ReloadOutlined style={{ marginRight: 5 }} />
        线上回滚
      </span>
    ),
    key: '0',
  }
];

const mapParams = (inputs) => {
  const obj = {};
  inputs.forEach((v) => {
    obj[v.name] = v.type;
  });
  return obj;
};

const mapParamsInPrompts = (inputs) => {
  console.log('inputs', inputs);
  return inputs.map((v) => ({
    type: ParamTypeEnum.textInput,
    title: v.title ?? v.name,
    required: true,
    key: v.name,
  }));
};

const CopyableInput = (props) => {
  const inputRef = useRef(null);

  const handleCopy = async () => {
    // document.execCommand('copy');
    copyText(props.value);
  };

  const handleChange = (ev) => {
    if (props.onChange) {
      props.onChange(ev.target.value);
    }
  };

  return (
    <Input
      {...props}
      onChange={handleChange}
      ref={inputRef}
      suffix={<CopyFilled onClick={handleCopy}></CopyFilled>}
    />
  );
};

const JSON = (props: any) => {
  const { value } = props;
  console.log('config value', value);
  return (
    <ReactJson
      theme="summerfruit:inverted"
      name={false}
      displayDataTypes={false}
      iconStyle="square"
      collapsed={2}
      src={value}
    />
  );
};

const codeMap = {
  'no-input': '缺少输入参数',
  'input-error': '输入参数信息待完善',
  'no-output': '缺少输出参数',
  'output-error': '输出参数信息待完善',
};

const validateParams = (data, type) => {
  const errors: any = [];
  const map = {};
  data.map((v) => {
    if (map[v.name]) {
      errors.push({
        code: `${type}-error`,
        type: 'error',
        message: `${v.name}重名`,
      });
    }
    if (!v.title) {
      errors.push({
        code: `${type}-error`,
        type: 'error',
        message: `${v.name}缺少标题`,
      });
    }
    if (!v.description && type === 'input') {
      errors.push({
        code: `${type}-error`,
        type: 'warning',
        message: `${v.name}缺少描述`,
      });
    }
    map[v.name] = v;
  });
  return errors;
};

const Icon = ({ right, correct, type }) => {
  if (type === 'warning') {
    return (
      <ExclamationCircleTwoTone
        twoToneColor="#e0ab10"
        style={{ [right ? 'marginLeft' : 'marginRight']: 10 }}
      ></ExclamationCircleTwoTone>
    );
  }
  const MyIcon = correct ? CheckCircleTwoTone : CloseCircleTwoTone;
  return (
    <MyIcon
      twoToneColor={correct ? '#52c41a' : 'red'}
      style={{ [right ? 'marginLeft' : 'marginRight']: 10 }}
    />
  );
};

const validate = (data) => {
  let errors: any = [];
  if (!data.inputs.length) {
    errors.push({
      type: 'error',
      code: 'no-input',
    });
  } else {
    errors = [...errors, ...validateParams(data.inputs, 'input')];
  }
  if (!data.outputs.length) {
    errors.push({
      type: 'no-output',
    });
  } else {
    errors = [...errors, ...validateParams(data.outputs, 'output')];
  }
  return errors;
};

const DeployForm = ({ form, setCanDeploy, canDeploy, graphData }) => {
  const [data, setData] = useState({} as any);
  const [loading, setLoading] = useState(true);

  const getData = async () => {
    const config = graphData;
    const appID = getAppId();
    const _data = {
      ...config,
      errors: validate(config),
      input: {
        appID,
        inputs: mapParams(config.inputs),
        callback: {
          type: 'nydus',
          info: {},
        },
        bizContext: 'string',
        bizInfo: {
          business: 'string',
          scene: 'string',
        },
      },
      output: mapParams(config.outputs),
      method: 'POST',
      url: `${window.location.origin}/api/v1/app/trigger`,
      appID
    };
    setData(_data);
    if (_data.errors && !_data.errors.filter((err) => err.type !== 'warning').length) {
      setCanDeploy(true);
    }
    setLoading(false);
    form.setFieldsValue(_data);
  };

  useEffect(() => {
    getData();
  }, []);

  if (loading) {
    return <Spin></Spin>;
  }

  const items: any = [];
  let errorContent: any = [<span>所有信息校验通过</span>];

  if (data?.errors && data.errors.length) {
    const errorMap = {};
    data.errors.forEach((err) => {
      if (!errorMap[err.code]) {
        errorMap[err.code] = [];
      }
      errorMap[err.code].push(err);
    });
    errorContent = Object.keys(errorMap).map((code) => (
      <div key={code}>
        <p style={{ fontSize: 14 }}>{codeMap[code]}</p>
        {errorMap[code].map((v) => (
          <div>
            <Icon {...v}></Icon>
            {v.message}
          </div>
        ))}
      </div>
    ));
  }
  items.push({
    id: '0',
    label: (
      <span>
        workflow校验
        {!canDeploy ? (
          <Icon right correct={undefined} type={undefined}></Icon>
        ) : data?.errors.length ? (
          <Icon right type="warning" correct={undefined}></Icon>
        ) : (
          <Icon right correct type={undefined}></Icon>
        )}
      </span>
    ),
    children: errorContent,
  });
  items.push({
    id: '1',
    label: '发布信息',
    isActive: true,
    children: (
      <Form form={form} layout="vertical" hideRequiredMark>
        <Form.Item name="appID" label="appID">
          <Input disabled />
        </Form.Item>
        <Form.Item name="url" label="服务地址">
          <CopyableInput disabled />
        </Form.Item>
        <Form.Item name="method" label="请求方式">
          <CopyableInput disabled />
        </Form.Item>
        <Form.Item name="input" label="输入">
          <JSON />
        </Form.Item>
        <Form.Item name="output" label="输出">
          <JSON />
        </Form.Item>
      </Form>
    ),
  });

  return (
    <Collapse
      accordion
      items={items}
      defaultActiveKey={canDeploy ? ['1'] : ['0']}
    ></Collapse>
  );
};

const DeployButton: React.FC<any> = (props: any) => {
  const xflowApp = useXFlowApp();
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [canDeploy, setCanDeploy] = useState(false);
  const { globalState, updateGlobalState } = useGlobalState();
  const { app } = globalState;
  const [config, setConfig] = useSafeState();

  function getConfig() {
    return getSaveData().then(res => {
      console.log("resssss", res);
      setConfig(res)
    });
  }

  const showDrawer = () => {
    getConfig().then(() => {
      setOpen(true);
    })
  };

  const onClose = () => {
    setOpen(false);
    form.resetFields();
  };

  const rollback = () => {
    console.log('回滚')
    confirm({
      width: 600,
      title: <span>选择版本<span style={{
        fontSize: 12,
        fontWeight: 'normal',
        color: '#999',
        marginLeft: 5
      }}>回滚后线上调用立刻生效</span></span>,
      content: <VersionList app={app} updateGlobalState={updateGlobalState}></VersionList>,
      onOk() { },
      onCancel() { },
    });
  }

  const handleSubmit = () => {
    showPublishModal((pubMessage) => {
      xflowApp.commandService.executeCommand(
        CustomCommands.DEPLOY_SERVICE.id,
        {
          deployDagService: (meta, graphData) => {
            HookApi.deployDagService(meta, graphData, pubMessage).then(() => {
              if (app?.type !== IAppType.AgentWorkflow) {
                onClose();
                window.open(
                  `${window.location.origin}/preview/workflow-playground?appId=${getAppId()}`,
                );
              } else {
                // 更新popo的类型为workflow
                PopoApi.updateType(true, app.id);
                // 更新app信息，主要是app_config_id
                AppApi.getAppDetail(app.id).then(res => {
                  updateGlobalState('app', res);
                });
                onClose();
              }
            })
          }
        },
      );
    })
  };

  return (
    <>
      <Dropdown.Button menu={{ items: getItems(rollback) }} style={{ width: 100 }} buttonsRender={buttons => [<Button style={{ width: 70 }} icon={<CloudSyncOutlined />} type="primary" onClick={showDrawer}>发布</Button>, <Button type="primary" style={{ width: 30 }} icon={<MoreOutlined />}></Button>]}>保存</Dropdown.Button>
      <Drawer
        title="发布服务"
        width={720}
        destroyOnClose
        onClose={onClose}
        open={open}
        styles={{
          body: {
            paddingBottom: 80,
          },
        }}
        extra={
          <Space>
            <Button onClick={handleSubmit} type="primary" disabled={!canDeploy && app.type !== IAppType.AgentWorkflow}>
              发布
            </Button>
          </Space>
        }
      >
        {app.type === IAppType.AgentWorkflow ?
          <AccessContent app={app} agentConfig={{ paramsInPrompt: mapParamsInPrompts((config as any)?.inputs || []) }}></AccessContent>
          :
          <DeployForm
            form={form}
            graphData={config}
            setCanDeploy={setCanDeploy}
            canDeploy={canDeploy}
          ></DeployForm>
        }
      </Drawer>
    </>
  );
};

export default DeployButton;
