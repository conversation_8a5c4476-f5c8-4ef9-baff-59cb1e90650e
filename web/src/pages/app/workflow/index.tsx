/* eslint-disable react-hooks/rules-of-hooks */
// @ts-nocheck
import '@antv/xflow/dist/index.css';
import './index.less';

import { Alert } from 'antd';
// import { Transform } from '@antv/x6-plugin-transform';
import { useRef, useState } from 'react';
import React, { useEffect } from 'react';

import { AppApi } from '@/api/app';
import { getUserState } from '@/utils/state';

import { loadGraphData } from './config/cmds/hooks-services';
import Preview from './workflow-preview';
import WorkflowEdit from './workflow-edit';
import { useQuery } from '@/hooks/useQuery';
import AgentWorkflowEdit from './workflow-agent';
import { IAppType } from '@/interface';
import { useLock } from '@/hooks/useLock';

export interface IProps {
  meta: { flowId: string };
}

export const WorkflowIndex: React.FC<any> = ({ type }) => {
  const { lock } = useLock();
  const [graphData, setGraphData] = useState(null);
  const { parsedQuery } = useQuery()

  useEffect(() => {
    loadGraphData()().then((res) => {
      setGraphData(res);
    });
  }, []);

  return lock && graphData ? (
    <>
      <Alert
        style={{ zIndex: 10000 }}
        message={
          <span>
            当前workflow{' '}
            <a
              href={`http://popo.netease.com/static/html/open_popo.html?ssid=${lock.username}@corp.netease.com&sstp=0`}
              target="_blank"
              rel="noreferrer"
            >
              {lock.username}
            </a>{' '}
            正在编辑中，暂时只能查看，如果需要可以联系对方释放资源
          </span>
        }
        banner
      />
      <Preview graphData={graphData}></Preview>
    </>
  ) : (
    type === IAppType.AgentWorkflow || parsedQuery.type === 'agent-workflow' ?
      <AgentWorkflowEdit></AgentWorkflowEdit>
      :
      <WorkflowEdit></WorkflowEdit>
  );
};

export default WorkflowIndex;

WorkflowIndex.defaultProps = {
  meta: { flowId: 'test-meta-flow-id' },
};
