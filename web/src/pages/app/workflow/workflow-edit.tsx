/* eslint-disable react-hooks/rules-of-hooks */
// @ts-nocheck
import '@antv/xflow/dist/index.css';
import './index.less';

// import { Transform } from '@antv/x6-plugin-transform';
import type { IApplication, IAppLoad } from '@antv/xflow';
/** app 核心组件 */
import {
  CanvasNodePortTooltip,
  IModelService,
  KeyBindings,
  XFlow,
  XFlowCanvas,
} from '@antv/xflow';
/** 交互组件 */
import {
  CanvasContextMenu,
  /** 触发Command的交互组件 */
  CanvasScaleToolbar,
  CanvasToolbar,
  DagGraphExtension,
  // NodeCollapsePanel,
} from '@antv/xflow';
import { JsonSchemaForm } from '@/components/canvas-json-schema-form';
import { NodeCollapsePanel } from '@components/canvas-collapse-panel'
import React, { useEffect } from 'react';

import { AppApi } from '@/api/app';
import { setInstance } from '@/utils/flow';
import { $, getAppId, mergeConfig } from '@/utils/state';

/** 配置自定义面板 */
import ResponsePanel from './components/response-panel';
import { diffGraph } from './config/cmds/hooks-services';
/** 配置Command */
import { initGraphCmds, useCmdConfig } from './config/config-cmd';
/** 配置Dnd组件面板 */
import * as dndPanelConfig from './config/config-dnd-panel';
/** 配置画布 */
import { useGraphHookConfig } from './config/config-graph';
/** 配置Canvas */
import { useGraphConfig } from './config/config-graph';
/** 配置JsonConfigForm */
import {
  controlMapService,
  formSchemaService,
  formValueUpdateService,
} from './config/config-json';
/** 配置快捷键 */
import { useKeybindingConfig } from './config/config-keybinding';
/** 配置Menu */
import { useMenuConfig } from './config/config-menu';
/** 配置Model */
import {
  getCustomModal,
  getCustomState,
  useModelServiceConfig,
} from './config/config-model-service';
/** 配置Toolbar */
import { useToolbarConfig } from './config/config-toolbar';
/** 配置自定义面板 */
import DebugCard from './components/debug-card';
import { EyeOutlined, SwitcherOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { TemplateSelect } from '@/components/template-select';
import { IAppType } from '@/interface';

export interface IProps {
  meta: { flowId: string };
}

// const Tab = () => {
//   const scopes = ['基础组件', '运算组件', 'AIGC组件', 'HTTP组件']
//   return  <Tabs
//   defaultActiveKey="1"
//   tabPosition={'top'}
//   tabBarStyle={{ paddingLeft: '20px'}}
//   style={{ position: 'absolute', top: 40, width: '100%', height: 40 }}
//   items={scopes.map((item, id) => {
//     return {
//       label:item,
//       key: id,
//       children: ''
//     };
//   })}
// />
// }

const leaveWarning = '离开当前页后，所有未保存的数据将会丢失，是否继续？';
const listener = (e): void => {
  diffGraph().then((diff) => {
    if (diff) {
      e.preventDefault();
      e.returnValue = leaveWarning;
    }
  });
};

export const WorkflowEdit: React.FC<any> = (props) => {
  useEffect(() => {
    window.addEventListener('beforeunload', listener);
    return (): void => {
      window.removeEventListener('beforeunload', listener);
    };
  }, []);

  const [preview, setPreview] = React.useState(true);
  const [canIntoPlayground, setCanIntoPlayground] = React.useState(false);
  const [collapsed, setCollapsed] = React.useState(false);
  const [showSchema, setShowSchema] = React.useState(false);
  const { meta } = props;
  const graphHooksConfig = useGraphHookConfig(props);
  const toolbarConfig = useToolbarConfig();
  const menuConfig = useMenuConfig();
  const cmdConfig = useCmdConfig();
  const modelServiceConfig = useModelServiceConfig(false)();
  const keybindingConfig = useKeybindingConfig();

  const cache = React.useMemo<{ app: IApplication }>(
    () => ({
      app: null as any,
    }),
    [],
  );
  /**
   * @param app 当前XFlow工作空间
   * @param extensionRegistry 当前XFlow配置项
   */

  const onLoad: IAppLoad = async (app) => {
    cache.app = app;
    setInstance(app);
    const appId = getAppId();
    const histories = await AppApi.getAppHistory(appId);
    const ctx = await getCustomModal(app.modelService as IModelService);
    const state = await getCustomState(app.modelService as IModelService);
    ctx.watch((val) => {
      setPreview(val.preview);
      setShowSchema(val.showSchema);
    });
    ctx.setValue({ ...state, canIntoPlayground: histories?.length });
    setCanIntoPlayground(histories?.length);
    initGraphCmds(cache.app);
    // const x6graph = await app.getGraphInstance();
  };

  /**
   * 折叠
   */
  const handleCollapse = (val) => {
    console.log('co', val);
    setCollapsed(val);
  };

  /** 父组件meta属性更新时,执行initGraphCmds */
  const config = useGraphConfig(false);

  const handlePlayground = () => {
      window.open(`/preview/workflow-playground?appId=${getAppId()}`, 'blank');
  }

  const handleSelect = async (template: Template) => {
    console.log('选择的模板：', template);
    const app = await AppApi.getAppDetail(template.id);
    const curConfig = await $.getConfig();
    console.log('app...', app, curConfig);
    if (app) {
      const res = await AppApi.saveAppConfigSnapshot('', mergeConfig(app.config, curConfig));
      if (res) {
        window.location.reload();
      }
    }
  };

  return (
    <XFlow
      className="dag-user-custom-clz"
      hookConfig={graphHooksConfig}
      modelServiceConfig={modelServiceConfig}
      commandConfig={cmdConfig}
      onLoad={onLoad}
      key={getAppId()}
      meta={meta}
    >
      <DagGraphExtension layout="LR" />

      {/* <CanvasToolbar
        className="xflow-workspace-toolbar-top"
        layout="horizontal"
        config={toolbarConfig}
        position={{ top: 0, left: 0, right: 0, bottom: 0 }}
      ></CanvasToolbar> */}
      <XFlowCanvas
        position={{ top: 0, left: 0, right: 0, bottom: 0 }}
        config={config()}
      >
        <CanvasScaleToolbar layout='horizontal' style={{ height: 40, borderRadius: 10, overflow: 'hidden', left: 'auto' }} position={{ bottom: 12, left: 'auto', right: 20 }} />
        <CanvasContextMenu config={menuConfig} />
        {/* <CanvasSnapline color="#faad14" /> */}
        <ResponsePanel></ResponsePanel>
        <CanvasNodePortTooltip></CanvasNodePortTooltip>
      </XFlowCanvas>
      <div style={{ visibility: preview ? 'hidden' : 'visible' }}>
        <NodeCollapsePanel
          className="xflow-node-panel"
          collapsible
          onCollapseChange={handleCollapse}
          searchService={dndPanelConfig.searchService}
          nodeDataService={dndPanelConfig.nodeDataService('workflow')}
          onNodeDrop={dndPanelConfig.onNodeDrop}
          position={{ width: 320, top: 60, bottom: 0, left: 10 }}
          footerPosition={{ height: 0 }}
          bodyPosition={{ top: 0, bottom: 0, left: 0 }}
        />
        <TemplateSelect
          appType={IAppType.Workflow}
          buttonStyle={{ position: 'absolute', top: 19, left: 70, padding: '4px 12px' }}
          onSelect={handleSelect}
        />
      </div>
      <DebugCard/>
      <JsonSchemaForm
        controlMapService={controlMapService}
        formSchemaService={formSchemaService}
        targetType={['group', 'node', 'canvas']}
        formValueUpdateService={formValueUpdateService}
        bodyPosition={{ top: 0, bottom: 0, right: 0 }}
        position={{ width: showSchema ? 320 : 0, top: 60, bottom: 0, right: 20 }}
        footerPosition={{ height: 0 }}
      />
      <KeyBindings config={keybindingConfig} />
      <Button disabled={!canIntoPlayground} type="link" style={{ position: 'fixed', right: 10, top: 15}} icon={<EyeOutlined />} onClick={handlePlayground}>查看Playground</Button>
    </XFlow>
  );
};

export default WorkflowEdit;

WorkflowEdit.defaultProps = {
  meta: { flowId: 'test-meta-flow-id' },
};
