import React, { useState, useEffect } from 'react';
import { useGlobalState } from '@/hooks/useGlobalState';
import { DefaultModelApi, ModelProviderApi } from '@/api/model-provider';
import { useQuery } from '@/hooks/useQuery';
import { useSafeState } from 'ahooks';
import { useDebounce } from 'ahooks';
import { message } from 'antd';
import ModelModalContent from '@/components/model-modal/model-modal-content';
import { transformModelList, isDisabled, getScore, getColumns } from '@/components/model-modal/utils';
import type { DataType } from '@/components/model-modal/types';

const ModelsPage: React.FC = () => {
  const { globalState, fetchGlobalState } = useGlobalState();
  const [data, setData] = useState<DataType[]>([]);
  const [multiple, setMultiple] = useSafeState({
    cost: 1,
    speed: 1,
    context: 1,
    performance: 1,
  });
  const [selectedModels, setSelectedModels] = useState<DataType[]>([]);
  const [searchText, setSearchText] = useState('');
  const debouncedSearchText = useDebounce(searchText, { wait: 300 });

  // 加载模型数据
  useEffect(() => {
    fetchGlobalState('modelProviderList');
    console.log('listGlobalModel');
    DefaultModelApi.listGlobalModel({
      workspaceId: 'f97bf87b-a8ec-4bc9-a8e3-662e111cd2e4',
    }).then((res) => {
      console.log('res', res);
      const transformedData = transformModelList(res);
      setData(transformedData.filter(item => item.enable));
    });
  }, []);

  // 处理搜索和排序后的数据
  const sortedData = React.useMemo(() => {
    let filteredData = [...data];

    if (debouncedSearchText) {
      const searchLower = debouncedSearchText.toLowerCase();
      filteredData = filteredData.filter(item =>
        item.name.toLowerCase().includes(searchLower) ||
        item.provider.toLowerCase().includes(searchLower)
      );
    }

    return filteredData.sort((a, b) => getScore(b, multiple) - getScore(a, multiple));
  }, [multiple, data, debouncedSearchText]);

  // 获取表格列配置
  const columns = React.useMemo(() => {
    return getColumns({
      data,
      multiple,
      showOperation: false,
      selectedModels,
      setSelectedModels,
      disabledList: [],
      globalState,
    });
  }, [data, multiple, selectedModels, globalState]);

  // 排序列
  const newColumns = columns.slice().sort((a, b) => {
    const aWeight = a.fixed ? 10 : multiple[(a as DataType)?.dataIndex || ''] || 0;
    const bWeight = b.fixed ? 10 : multiple[(b as DataType)?.dataIndex || ''] || 0;
    return bWeight - aWeight;
  });

  return (
    <div className="models-page" style={{ padding: '24px' }}>
    <h3 style={{ marginTop: 0, marginBottom: 16 }}>LangBase模型列表</h3>
    <div style={{ height: 'calc(100vh - 130px)', overflowY: 'auto' }}>
      <ModelModalContent
        multiple={multiple}
        showOperation={false}
        setMultiple={setMultiple}
        selectedModels={selectedModels}
        setSelectedModels={setSelectedModels}
        searchText={searchText}
        setSearchText={setSearchText}
        pageSize={100}
        sortedData={sortedData}
        data={data}
        columns={newColumns}
        disabledList={[]}
        getScore={(record) => getScore(record, multiple)}
        isDisabled={isDisabled}
      />
    </div>
  </div>
  );
};

export default ModelsPage;
