import { useQuery } from "@/hooks/useQuery";
import IFrame from "./iframe"

const WorkspaceMonitor = () => {
  const { parsedQuery } = useQuery();
  const { workspaceId } = parsedQuery;

  const params = {
    workspaceId: workspaceId ? [workspaceId] : [],
  };
  return <>
    <IFrame
      src={`https://music-pylon.hz.netease.com/monitor-analysis-system/workspace/661/dashboard/view/5740?filters=${JSON.stringify(params)}`} />
  </>
}

export default WorkspaceMonitor