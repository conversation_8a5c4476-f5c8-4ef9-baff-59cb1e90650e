import { useQuery } from "@/hooks/useQuery";
import <PERSON>ram<PERSON> from "./iframe";
import { useEffect, useState } from "react";
import { AppApi } from "@/api/app";
import { IAppType } from "@/interface";

const AppMonitor = (props) => {
  const { parsedQuery } = useQuery();
  const { workspaceId, groupId, appId } = parsedQuery;
  const [appType, setAppType] = useState('');


  useEffect(() => {
    
    //@ts-ignore
    AppApi.getAppDetail(appId).then(res => {
      setAppType(res?.type);
    });
  }, [appId]);

  const params = {
    workspaceId: workspaceId ? [workspaceId] : [],
    groupId: groupId ? [groupId] : [],
    appId: appId ? [appId] : []
  };

  return <>
    <IFrame
      src={appType === IAppType.VirtualHuman ?
        `https://music-pylon.hz.netease.com/monitor-analysis-system/workspace/661/dashboard/view/5738?filters=${JSON.stringify(params)}` :
        `https://music-pylon.hz.netease.com/monitor-analysis-system/workspace/661/dashboard/view/5741?filters=${JSON.stringify(params)}`
        }>
    </IFrame>
  </>
}

export default AppMonitor;
