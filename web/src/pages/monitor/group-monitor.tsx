import { useQuery } from "@/hooks/useQuery";
import IFrame from "./iframe"

const GroupMonitor = () => {
  const { parsedQuery } = useQuery();
  const { workspaceId, groupId } = parsedQuery;

  const params = {
    workspaceId: workspaceId ? [workspaceId] : [],
    groupId: groupId ? [groupId] : [],
  };

  return <IFrame src={`https://music-pylon.hz.netease.com/monitor-analysis-system/workspace/661/dashboard/view/5739?filters=${JSON.stringify(params)}`} />
}

export default GroupMonitor