import { useQuery } from '@/hooks/useQuery';
import { Space, Tag } from 'antd';
import React, { useState } from 'react';

const { CheckableTag } = Tag;

export const whitelist = ['输入', '输出', '脚本', '逻辑'];

const Category = (props: any) => {
  const { categories, onChange, debug } = props;
  const tagsData = ['全部'].concat(
    categories.filter((c) => debug || !whitelist.includes(c.name)).map((c) => c.name),
  );

  const [selectedTags, setSelectedTags] = useState<string[]>(['全部']);

  const handleChange = (tag: string, checked: boolean) => {
    let nextSelectedTags = checked
      ? [...selectedTags, tag]
      : selectedTags.filter((t) => t !== tag);
    if (tag === '全部') {
      nextSelectedTags = ['全部'];
    } else {
      nextSelectedTags = nextSelectedTags.filter((t) => t !== '全部');
    }
    console.log('You are interested in: ', nextSelectedTags);
    if (onChange) {
      onChange(nextSelectedTags);
    }
    setSelectedTags(nextSelectedTags);
  };

  return (
    <div style={{ marginBottom: '20px' }}>
      <span style={{ marginRight: 8, fontSize: 14 }}>分类:</span>
      <Space size={[0, 8]} wrap>
        {tagsData.map((tag) => (
          <CheckableTag
            key={tag}
            checked={selectedTags.includes(tag)}
            onChange={(checked) => handleChange(tag, checked)}
          >
            {tag}
          </CheckableTag>
        ))}
      </Space>
    </div>
  );
};

export default Category;
