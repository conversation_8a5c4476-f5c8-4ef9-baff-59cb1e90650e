/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable react/jsx-key */
// @ts-nocheck
import { PlusOutlined, ExportOutlined, ImportOutlined } from '@ant-design/icons';
import { Button, Drawer, Form, message, Modal } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import styled from 'styled-components';
import fileSaver from 'file-saver';
import FileReaderInput from 'react-file-reader-input';
import lodash, { pick } from 'lodash';

import { ComponentApi } from '@/api/component';
import {
  LForm,
  LFormTextArea,
  LFormIcon,
  LFormInput,
  LFormItem,
  LFormSelect,
  LFormSwitch,
  LFormInputNumber,
} from '@/components/form';
import { useQuery } from '@/hooks/useQuery';
import { AppTypeImgMap, IAppType } from '@/interface';
import { getGroupId, getWorkspaceId } from '@/utils/state';

import { Param } from '../app/workflow/config/schema-panel/controls-components/param';
import { whitelist } from './category';
import { JSONParse } from '@/utils/common';

interface IProps {
  TriggerComponent?: any;
  defaultAppType?: IAppType;
  defaultGroupId?: string;
  categories?: any;
}

const types = ['INPUT', 'OUTPUT', 'SCRIPT', 'SWITCH', 'FOREACH', 'HTTP', 'LLM-PLUGIN', 'LLM'];
const methods = ['POST', 'GET', 'HEAD', 'PUT', 'DELETE'];

const getValFromConfig = (configs, name) => {
  const index = configs.findIndex((conf) => conf.name === name);
  const value = (configs[index] || { value: '' }).value;
  if (index >= 0) {
    configs.splice(index, 1);
  }
  return value;
};

const formatValue = (value) => {
  if (!value) {
    return;
  }
  const config = value.config;
  const configs: array<any> = [...(config.configs || [])];
  const url = getValFromConfig(configs, 'url');
  const method = getValFromConfig(configs, 'method');
  const timeout = getValFromConfig(configs, 'timeout');
  // const isAgent = lodash.includes(value?.appTypes, 'agent');
  // const isAgentWorkflow = lodash.includes(value?.appTypes, 'agentWorkflow');
  return { ...config, ...value, url, method, configs, timeout };
};

const compareParams = (oldParams: any[], newParams: any[]) => {
  if (oldParams.length !== newParams.length) return false;
  return oldParams.every(oldItem =>
    newParams.some(newItem => newItem.key === oldItem.key)
  );
};

export function CreateUpdateComp(props: IProps) {
  const {
    TriggerComponent, onSuccess, value, categories, drawerProps = {}, disabled = false
  } = props;
  // const [categories, setCategories] = useState(props.categories);
  const [showCreateApp, setShowCreateApp] = useState(false);
  const [showTimeout, setShowTimeout] = useState(
    value ? !formatValue(value).async : true,
  );
  const [isAgent, setIsAgent] = useState(lodash.includes(value?.appTypes, 'agent'));
  const [form] = Form.useForm();
  const { debug } = useQuery();

  useEffect(() => {
    setIsAgent(lodash.includes(value?.appTypes, 'agent'))
  }, [value?.appTypes])
  /**
   * 保存组件信息
   */
  const onFinish = (values: any) => {
    const { workspaceID, method, url, configs, timeout } = values;
    const groupID = getGroupId();

    const newConfigs = [...(configs || [])];
    newConfigs.push(
      {
        name: 'url',
        value: url,
      },
      {
        name: 'method',
        value: method,
      },
    );
    if (timeout) {
      newConfigs.push({
        name: 'timeout',
        value: timeout,
      });
    }
    values.configs = newConfigs;
    const cat = categories.find((v) => v.id === values.categoryID);
    if (cat) {
      values.category = cat.name;
    }
    const preEnvs = (value?.config?.configs || [])
      .filter((conf) => (conf?.value ? String(conf?.value) : '').startsWith('$'))
      .map((e) => ({
        name: e.value.replace('$', ''),
        param: e.name,
      }));
    const envs = newConfigs
      .filter((conf) => (conf?.value ? String(conf?.value) : '').startsWith('$'))
      .map((e) => ({
        name: e.value.replace('$', ''),
        param: e.name,
      }));
    if (value) {
      // 如果有新的环境变量，则报错
      const newEnvs = envs.filter(env => !preEnvs.find(v => v.name === env.name));
      if (newEnvs.length) {
        message.error(`不能新增环境变量: ${newEnvs.map(e => e.name)}`);
        return;
      }
      // 如果输入和输出跟之前不一样，需要弹出提示，让用户确认：该组件的输入输出参数可能已经发生变化，是否继续更新
      const oldInputs = value?.config?.inputs || [];
      const oldOutputs = value?.config?.outputs || [];
      const newInputs = values.inputs || [];
      const newOutputs = values.outputs || [];
      const hasParamsChanged = !compareParams(oldInputs, newInputs) || !compareParams(oldOutputs, newOutputs);
      console.log('hasParamsChanged', value, hasParamsChanged, oldInputs, newInputs, oldOutputs, newOutputs);

      if (hasParamsChanged) {
        Modal.confirm({
          title: '参数变更确认',
          content: <div>该组件的输入输出参数已经发生变化，请<span style={{ color: 'red', fontWeight: 'bold' }}>确认不会影响原有组件的调用</span>，是否继续更新？</div>,
          onOk: () => {
            ComponentApi.update(
              {
                scope: value.scope,
                ...values,
                config: values,
                groupID,
                envs,
              },
              value.id,
            )
              .then(() => {
                message.success('更新成功');
                if (onSuccess) {
                  onSuccess();
                }
                setShowCreateApp(false);
              })
              .catch((err) => {
                window.corona.warn('更新Component失败', err);
                message.error(`更新失败 ${err?.message}`);
              });
          },
        });
      } else {
        // 这里更新的时候需要注意，config中的code需要使用value.config中的code和name
        ComponentApi.update(
          {
            scope: value.scope,
            ...values,
            config: { ...value.config, ...pick(values, ['name', 'description', 'inputs', 'configs', 'outputs', 'vars', 'icon', 'url', 'method', 'type', 'timeout', 'async', 'deprecated']) },
            groupID,
            envs,
          },
          value.id,
        )
          .then(() => {
            message.success('更新成功');
            if (onSuccess) {
              onSuccess();
            }
            setShowCreateApp(false);
          })
          .catch((err) => {
            window.corona.warn('更新Component失败', err);
            message.error(`更新失败 ${err?.message}`);
          });
      }
    } else {
      ComponentApi.create(
        {
          scope: 'scoped',
          ...values,
          config: values,
          envs,
          groupID,
          deprecated: false,
        },
        workspaceID,
      )
        .then(() => {
          message.success('创建成功');
          if (onSuccess) {
            onSuccess();
          }
          setShowCreateApp(false);
        })
        .catch((err) => {
          window.corona.warn('创建components失败', err);
          message.error(`创建失败 ${err?.message}`);
        });
    }
  };

  const formValue = useMemo(() => {
    return formatValue(value);
  }, [value]);

  const onClick = (type, e, data) => {
    console.log('v', type, e, data, formValue);
    if (type === 'export') {
      const newData = { ...formValue };
      delete newData.updatedBy;
      delete newData.createdBy;
      delete newData.categoryID;
      delete newData.workspaceID;
      delete newData.id;
      delete newData.createdAt;
      delete newData.updatedAt;
      const blob = new Blob([JSON.stringify(newData)], {
        type: 'text/plain;charset=utf-8',
      });
      fileSaver.saveAs(blob, `components_${formValue.name}.conf`);
      return;
    }
    if (data && data[0] && data[0][0]) {
      const rawData = data[0][0].target.result;
      const configs: any = JSONParse(rawData);
      form.setFieldsValue(configs);
      console.log('raw', configs);
    }

  }

  const appTypes = Form.useWatch('appTypes', form);
  const scope = Form.useWatch('scope', form);

  return (
    <>
      {TriggerComponent ? (
        <TriggerComponent
          onClick={() => {
            setShowCreateApp(true);
          }}
        />
      ) : (
        <Button
          onClick={() => setShowCreateApp(true)}
          type="primary"
          icon={<PlusOutlined rev={undefined} />}
        >
          创建组件
        </Button>
      )}
      <Drawer
        title={`${value ? '更新' : '创建'}插件`}
        placement="right"
        extra={
          <div>
            {value ?
              <span style={{ fontSize: '12px', cursor: 'pointer' }} onClick={() => onClick('export')}>
                <ExportOutlined style={{ marginRight: 5 }} />
                导出配置
              </span> : <FileReaderInput
                as="text"
                id="my-file-input"
                onChange={(e, val) => onClick('import', e, val)}
              >
                <span style={{ fontSize: '12px', cursor: 'pointer' }} onClick={() => onClick('import')}>
                  <ImportOutlined style={{ marginRight: 5 }} />
                  导入配置
                </span>
              </FileReaderInput>}
          </div>}
        destroyOnClose
        maskClosable={false}
        open={showCreateApp}
        onClose={() => setShowCreateApp(false)}
        width={600}
        footer={
          disabled ? null : <>
            <Button onClick={() => setShowCreateApp(false)}>取消</Button>
            <CreateBtn onClick={() => form.submit()} type="primary">
              {value ? '更新' : '创建'}
            </CreateBtn>
          </>
        }
        {...drawerProps}
      >
        <Container>
          {/* <LeftContainer> */}
          <LForm
            disabled={disabled}
            form={form}
            onFinish={onFinish}
            initialValues={
              value
                ? formValue
                : {
                  categoryID: '40be8931-c859-44fb-ad34-8c1f64db013f',
                  category: '大语言',
                  type: 'HTTP',
                  method: 'POST',
                  scope: 'group',
                  vars: [],
                  inputs: [],
                  outputs: [],
                  workspaceID: getWorkspaceId(),
                  groupID: getGroupId(),
                  appTypes: ['workflow'],
                }
            }
          >
            <LFormInput
              name="name"
              label="名称"
              rules={[{ required: true }]}
              placeholder="组件名称（建议中文）"
            />
            <LFormInput
              name="code"
              label="CODE"
              disabled={!!value}
              rules={[
                { required: true },
                {
                  pattern: /^[A-Za-z]\w*$/g,
                  message: '只支持英文、数字或者下划线且必须英文开头',
                },
              ]}
              placeholder="组件唯一标识（英文、数字或者下划线）"
            />
            <LFormIcon
              name="icon"
              label="图标地址"
              placeholder="可以直接粘贴 iconfont 的svg代码 / 图片上传地址"
            />
            <LFormSelect
              name="appTypes"
              label="应用场景"
              mode="multiple"
              // onChange={v => console.log("vvvvv", v)}
              options={[
                { id: 'agent', name: 'Agent' },
                { id: 'workflow', name: '工作流' },
                { id: 'agentWorkflow', name: '对话流' },
                { id: 'virtual-human', name: '虚拟人' },
              ]}
            />
            <LFormInput
              name="description"
              label="描述"
              rules={[{ required: true }]}
              placeholder="组件描述"
            />
            {/* <LFormCheckBox
              options={[
                { label: '工作流', value: 'workflow' },
                { label: 'Agent', value: 'agent' },
              ]}
              name="appTypes"
              label="应用场景"
              rules={[{ required: true }]}
            /> */}
            <LFormInput name="workspaceID" label="workspaceID" hidden disabled />
            <LFormInput name="groupID" label="groupID" hidden disabled />
            <LFormInput
              name="url"
              label="服务地址"
              rules={[{ required: true }]}
              placeholder="请输入组件服务地址"
            />
            <LFormSelect
              name="method"
              label="服务请求类型"
              options={methods.map((v) => ({ id: v, name: v }))}
              rules={[{ required: true }]}
            />
            <LFormSwitch
              name="async"
              label="异步服务"
              help={<span><a href="https://music-doc.st.netease.com/st/langbase-doc/plugin#32-%E5%BC%82%E6%AD%A5-api%E5%8F%AA%E8%83%BD%E5%9C%A8%E5%B7%A5%E4%BD%9C%E6%B5%81%E4%BD%BF%E7%94%A8" target="_blank">点击查看</a> 如何实现异步服务</span>}
              onChange={(v) => setShowTimeout(!v)}
            />
            <LFormInputNumber
              name="timeout"
              label="服务超时时间(ms)"
              hidden={!showTimeout}
              placeholder="毫秒"
              help={<span>最长设置60秒，如果是长耗时任务请改成异步服务</span>}
            />
            <LFormSelect
              name="type"
              label="type"
              hidden={!debug}
              options={types.map((t) => ({ id: t, name: t }))}
            />
            <LFormSelect
              name="scope"
              label="可见范围"
              help={
                scope === 'public' && '设置成公开后【无法二次编辑】'
              }
              options={[
                { id: 'group', name: '组内访问' },
                { id: 'scoped', name: '租户内访问' },
                { id: 'public', name: '公开(所有租户可见)' },
              ]}
            />

            <LFormSelect
              name="categoryID"
              label="分类"
              options={(categories || []).filter(
                (c) => debug || !whitelist.includes(c.name),
              )}
            />
            <LFormItem name="inputs" label="输入参数">
              <Param collapsed withDetail requireDesc={appTypes && appTypes.includes('virtual-human')} disabled={disabled}></Param>
            </LFormItem>
            <LFormItem name="vars" label="选填参数">
              <Param collapsed useValue showEnum withDetail showRule showDesc disabled={disabled}></Param>
            </LFormItem>
            <LFormItem name="outputs" label="输出参数">
              <Param collapsed withDetail disabled={disabled}></Param>
            </LFormItem>
            <LFormItem name="configs" label="其他配置">
              <Param collapsed useValue disabled={disabled}></Param>
            </LFormItem>

            {/* <LFormSwitch
              label='支持Agent调用'
              name="isAgent"
              onChange={(v) => setIsAgent(v)}
            /> */}
            {
              appTypes && appTypes.includes('agent') && (
                <>
                  <h4>Agent特有配置</h4>
                  <LFormTextArea
                    name="description_for_llm"
                    label="模型描述"
                    autoSize={{ minRows: 2, maxRows: 4 }}
                    rules={[{ required: true }]}
                    placeholder="组件描述，将被传给底层大语言模型，用于指导调用该组件"
                  />
                  {/* <LFormTextArea
                    name="response_script"
                    label="返回值处理脚本"
                    autoSize={{ minRows: 3, maxRows: 6 }}
                    placeholder="简化返回值的python代码，可使用resp变量访问返回值，例如：&#13;&#10;{ 'name': resp.name, 'age': resp.age }"
                    /> */}
                </>
              )
            }
            <LFormSwitch
              name="deprecated"
              help="废弃的组件将不会出现在组件列表中"
              label="是否废弃"
            />
          </LForm>
        </Container>
      </Drawer>
    </>
  );
}

function AgentExample({ type }: { type: IAppType }) {
  return (
    <div>
      <StyleImg>
        <img src={AppTypeImgMap['workflow']} />
      </StyleImg>
    </div>
  );
}

const Container = styled.div`
  .avatar-icon {
    .icon path {
      fill: #4543af !important
    }
  }
`;

const LeftContainer = styled.div`
  width: 50%;
`;

const RightContainer = styled.div`
  width: 40%;
  padding-right: 4px;
`;

const StyleImg = styled.div`
  /* background-color: #eee; */
  width: 100%;
  height: 200px;
  text-align: center;
  line-height: 300px;

  img {
    height: 100%;
  }
`;

const CreateBtn = styled(Button)`
  margin-left: 5px;
`;
