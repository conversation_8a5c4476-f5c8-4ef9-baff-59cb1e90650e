/* eslint-disable react/jsx-key */
// @ts-nocheck
import './components.less';

import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  PartitionOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import { Card, List, message, Pagination, Popconfirm, Tag, Tooltip, Row, Col, Badge, Button } from 'antd';
import styled from 'styled-components';

import { useAdmin } from '@/hooks/useAdmin';
import { useGlobalState } from '@/hooks/useGlobalState';

import { ComponentApi } from '../../api/component';
import { AvatarIcon } from '../app/workflow/react-node/base-node';
import { CreateUpdateComp } from './create-component';

const Desc = styled.div`
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin: 10px 0;
  min-height: 36px;
  font-size: 12px;
`;

const Info = styled.div`
  margin: 5px 0;
  font-size: 12px;
`;

const Title = styled.div`
  display: flex;
`;
const Left = styled.div`
  flex: 0 0 30px;
  display: flex;
  align-items: center;
  .avatar-icon {
    color: #2f77ff !important;

    .icon path {
        fill: #2f77ff !important;
      }
    }
  }
`;
const Right = styled.div``;

const TopCircle = styled.div`
  position: absolute;
  top: -15px;
  background: #fff;
  right: 5px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  box-shadow: 0px -1px 6px 1px #92909047;
  justify-content: center;
  align-items: center;
  display: none;
`;

const Close = styled(TopCircle)`
  background: #fff2f0;
  color: #ff4d4f;
`;

const Edit = styled(TopCircle)`
  right: 45px;
  background: rgb(230, 244, 255);
  color: rgb(22, 119, 255);
`;

export default function ComponentList(props: any) {
  const { globalState } = useGlobalState();
  const { user } = globalState;
  const { list, getList, page, debug, categories } = props;
  const isAdmin = useAdmin('workspace');

  const handleDelete = async (item) => {
    const res: any = await ComponentApi.remove(item.id);
    if (res) {
      message.success('删除成功');
      getList();
    }
  };

  const enableWorkflow = (item) => (item?.appTypes || '').includes('workflow');
  const enableAgent = (item) => (item?.appTypes || '').includes('agent');
  const colorMap = {
    '音视频': '#c67ee9',
    '脚本': 'green',
    '大模型': '#ff8380',
    '图片': '#ff80c3',
    '输入': 'blue',
    '逻辑': 'orange',
    '请求': '#83d4d4',
    '废弃': 'gray',
  }

  return (
    <>
      <Row gutter={[16, 16]}>
        {list.map((item) => (
          <Col key={item.id} xs={12} sm={24} md={8} lg={6} xl={6} xxl={4}>
            <Badge.Ribbon text={item.deprecated ? '废弃' : item.category} color={colorMap[item.deprecated ? '废弃' : item.category]}>
              <Card bodyStyle={{ padding: '12px 16px' }} hoverable>
                <CreateUpdateComp value={item}
                  drawerProps={{
                    title: '插件详情'
                  }}
                  disabled
                  TriggerComponent={(props) => <div {...props}>
                    <Card.Meta
                      // avatar={<AvatarIcon icon={item.config.icon}></AvatarIcon>}
                      title={
                        <Title>
                          <Left>
                            <AvatarIcon
                              icon={item.config.icon}
                              style={{ marginRight: '10px' }}
                            ></AvatarIcon>
                          </Left>
                          <Right>
                            <div>{item.name}</div>
                            <div>
                              <Tooltip title="workflow使用">
                                <Tag
                                  icon={
                                    enableWorkflow(item) ? (
                                      <CheckCircleOutlined />
                                    ) : (
                                      <CloseCircleOutlined />
                                    )
                                  }
                                  color={enableWorkflow(item) ? 'success' : 'error'}
                                >
                                  <PartitionOutlined />
                                </Tag>
                              </Tooltip>
                              <Tooltip title="Agent使用">
                                <Tag
                                  icon={
                                    enableAgent(item) ? (
                                      <CheckCircleOutlined />
                                    ) : (
                                      <CloseCircleOutlined />
                                    )
                                  }
                                  color={enableAgent(item) ? 'success' : 'error'}
                                >
                                  <RobotOutlined />
                                </Tag>
                              </Tooltip>
                            </div>
                          </Right>
                        </Title>
                      }
                      description={
                        <>
                          <Desc>{item.description}</Desc>
                          <Info>创建人：{item?.createdBy?.fullname || '系统'}</Info>
                        </>
                      }
                    />
                  </div>} />
                {(item.scope !== 'public' || debug) &&
                  (isAdmin || item?.createdBy?.email === user?.email) ? (
                  <>
                    <CreateUpdateComp
                      onSuccess={getList}
                      categories={categories}
                      TriggerComponent={(props) => (
                        <Edit onClick={e => {
                          e.stopPropagation();
                          props.onClick();
                        }} className="edit">
                          <EditOutlined />
                        </Edit>
                      )}
                      value={item}
                    ></CreateUpdateComp>
                    <Popconfirm
                      title="提醒"
                      description="是否确认删除该组件?"
                      onConfirm={() => handleDelete(item)}
                      okText="是的"
                      cancelText="取消"
                    >
                      <Close className="close">
                        <DeleteOutlined key="delete" className="delete" />
                      </Close>
                    </Popconfirm>
                  </>
                ) : null}
              </Card>
            </Badge.Ribbon>
          </Col>
        ))}
        {/* {!appList.length && <Empty />} */}
      </Row>
      <Pagination
        style={{ float: 'right', marginTop: '8px' }}
        simple
        size="small"
        onChange={getList}
        {...page}
      />
      {/* <List
        grid={{ gutter: 20, xs: 2, md: 3, lg: 4, xxl: 6 }}
        dataSource={list}
        footer={<Pagination {...page} onChange={getList} />}
        renderItem={(item) => (
          <List.Item key={item.id}>
            <Card bodyStyle={{ padding: '12px 16px' }} hoverable>
              <Card.Meta
                // avatar={<AvatarIcon icon={item.config.icon}></AvatarIcon>}
                title={
                  <Title>
                    <Left>
                      <AvatarIcon
                        icon={item.config.icon}
                        style={{ marginRight: '10px' }}
                      ></AvatarIcon>
                    </Left>
                    <Right>
                      <div>{item.name}</div>
                      <div>
                        <Tag>{item.category}</Tag>
                        <Tooltip title="workflow使用">
                          <Tag
                            icon={
                              enableWorkflow(item) ? (
                                <CheckCircleOutlined />
                              ) : (
                                <CloseCircleOutlined />
                              )
                            }
                            color={enableWorkflow(item) ? 'success' : 'error'}
                          >
                            <PartitionOutlined />
                          </Tag>
                        </Tooltip>
                        <Tooltip title="Agent使用">
                          <Tag
                            icon={
                              enableAgent(item) ? (
                                <CheckCircleOutlined />
                              ) : (
                                <CloseCircleOutlined />
                              )
                            }
                            color={enableAgent(item) ? 'success' : 'error'}
                          >
                            <RobotOutlined />
                          </Tag>
                        </Tooltip>
                      </div>
                    </Right>
                  </Title>
                }
                description={
                  <>
                    <Desc>{item.description}</Desc>
                    <Info>创建人：{item?.createdBy?.fullname || '系统'}</Info>
                  </>
                }
              />
              {(item.scope === 'scoped' || debug) &&
                (isAdmin || item?.createdBy?.email === user?.email) ? (
                <>
                  <CreateUpdateComp
                    onSuccess={getList}
                    categories={categories}
                    TriggerComponent={(props) => (
                      <Edit {...props} className="edit">
                        <EditOutlined />
                      </Edit>
                    )}
                    value={item}
                  ></CreateUpdateComp>
                  <Popconfirm
                    title="提醒"
                    description="是否确认删除该组件?"
                    onConfirm={() => handleDelete(item)}
                    okText="是的"
                    cancelText="取消"
                  >
                    <Close className="close">
                      <DeleteOutlined key="delete" className="delete" />
                    </Close>
                  </Popconfirm>
                </>
              ) : null}
            </Card>
          </List.Item>
        )}
      /> */}
    </>
  );
}
