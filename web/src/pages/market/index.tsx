import { Space } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

import { ComponentApi, IComponent } from '@/api/component';
import { CategoryApi } from '@/api/component-category';
import { useQuery } from '@/hooks/useQuery';

import Category from './category';
import ComponentsList from './component-list';
import { CreateUpdateComp } from './create-component';
import Usage from './usage';

const PAGE_SIZE = 12;

const Container = styled.div`
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
`;

export default function Market() {
  const [comps, setComponents] = useState({} as any);
  const [categories, setCategories] = useState([] as IComponent[]);
  const [cate, setCate] = useState([] as any);
  const [pageSize, setPageSize] = useState(0);
  const [usage, setUsage] = useState([] as any);
  const { debug } = useQuery();

  const changeSize = () => {
    const width = window.innerWidth
    if (width < 990) {
      setPageSize(9);
    } else if (width < 1600) {
      setPageSize(PAGE_SIZE);
    } else {
      setPageSize(24);
    }
    console.log("width", width);
  }


  useEffect(() => {
    window.addEventListener('resize', () => {
      changeSize();
    })
    changeSize();
  }, [])

  async function getList(page?: number) {
    if (!pageSize) {
      return;
    }
    const res = await ComponentApi.list(
      '',
      cate,
      usage,
      debug ? null : ['HTTP'],
      page || 1,
      pageSize,
      true,
      debug ? false : true
    );
    if (Array.isArray((res as any).items)) {
      setComponents({
        data: (res as any).items,
        page: { current: page || 1, pageSize, total: (res as any).total },
      });
    }
  }
  async function getOptions() {
    const res = await CategoryApi.list();
    setCategories(res as any);
  }

  useEffect(() => {
    getOptions();
    getList();
  }, []);

  useEffect(() => {
    getList(1);
  }, [cate, usage, pageSize]);

  return (
    <Container style={{ height: '100%' }}>
      <Space style={{ width: '100%', justifyContent: 'space-between', marginBottom: 10 }}>
        <Space.Compact direction='vertical'>
          <Category debug={debug} categories={categories} onChange={setCate}></Category>
          <Usage onChange={setUsage}></Usage>
        </Space.Compact>
        <CreateUpdateComp categories={categories} />
      </Space>
      <ComponentsList
        debug={debug}
        list={comps?.data || []}
        page={comps?.page || {}}
        categories={categories}
        getList={getList}
      />
    </Container>
  );
}
