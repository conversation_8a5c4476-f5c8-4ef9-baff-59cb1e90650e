import { <PERSON><PERSON>ate<PERSON><PERSON> } from "@/api/evaluate";
import { usePathQuery, useQuery } from "@/hooks/useQuery";
import { DownloadOutlined, DownOutlined, ExpandOutlined, ExportOutlined, InfoCircleOutlined, LeftOutlined, LoadingOutlined, PlusOutlined, ReloadOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import { Badge, Button, Descriptions, Flex, Layout, Popconfirm, Space, Table, Tag, Tooltip, Typography } from "antd";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { evaluateCnMap, evaluateColorMap, evaluatorRuleCnMap, evaluatorTypeCnMap, evaluatorTypeColorMap, IEvaluateType, IEvaluatorType } from "@/interface/evaluate";
import { useGlobalState } from "@/hooks/useGlobalState";
import { title } from "process";
import EllipsisText from '@/components/common/EllipsisText';

const PAGE_SIZE = 12;

const EvaluateTaskDetail = (props) => {

  const [pageInfo, setPageInfo] = useState({
    pageNum: 1,
    pageSize: PAGE_SIZE
  });

  const { parsedQuery } = useQuery();
  const { appId, } = parsedQuery;
  const params = useParams();
  const { pushAddQuery } = usePathQuery();

  const { globalState } = useGlobalState();
  const { app } = globalState;

  const { data, loading, run } = useRequest(async (val?) => {
    if (!params?.evaluationTaskId) return;
    const res = await EvaluateApi.queryEvaluateTaskDetail({
      evaluationTaskId: params?.evaluationTaskId,
      ...pageInfo,
      ...(val || {})
    });

    return res;
  }, {
    manual: true
  });

  const { data: detail } = useRequest(async () => {
    if (!params?.evaluationTaskId) return;

    const taskDetail = await EvaluateApi.getTaskDetail({
      id: params?.evaluationTaskId,
    });
    const taskDetailConfig = JSON.parse(taskDetail?.config);
    const collectionDetail = await EvaluateApi.queryEvaluationDetail({ id: taskDetailConfig?.evaluationCollection?.evaluationCollectionId })

    return {
      taskDetail: {
        ...taskDetail,
        config: JSON.parse(taskDetail?.config || '{}')
      },
      collectionDetail: {
        ...collectionDetail,
      }
    }
  }, {
    refreshDeps: [params?.evaluationTaskId]
  });

  const { taskDetail, collectionDetail } = detail || {};

  useEffect(() => {
    if (!params?.evaluationTaskId) return;
    run({
      pageNum: 1
    });
  }, [params?.evaluationTaskId]);

  const fieldMap = taskDetail?.config?.evaluationSubject?.fieldMapping?.reduce((acc, cur) => {
    // acc[cur.evaluationSetField] = cur.evaluationSubjectField;
    acc[cur.evaluationSubjectField] = cur.evaluationSetField;

    return acc;
  }, {});

  const { config: taskConfig } = taskDetail || {};

  const onReload = () => {
    run();
  };

  const onDownload = () => {
    EvaluateApi.downloadEvaluateTask({
      evaluationTaskId: params?.evaluationTaskId
    }).then(res => {
      if (res?.url) {
        window.open(res?.url)
      }
    });
  };

  const onRest = async (val) => {
    const res = await EvaluateApi.retryByEvaluationSubTaskId({
      evaluationTaskId: params?.evaluationTaskId,
      evaluationSubTaskId: val
    });
    
    run();
  };

  return <div style={{ position: 'relative' }}>
    <Flex justify="center"
      style={{
        width: '100%',
        fontSize: '18px',
        position: 'absolute',

        // borderBottom: '1px solid rgba(29, 28, 35, .08)',
        // paddingBottom: '20px',
      }}>
      <Typography.Text strong style={{ fontSize: '18px' }}>评测任务详情</Typography.Text>
    </Flex>

    <Flex style={{
      // marginBottom: 20,
      paddingBottom: '10px',
      zIndex: 10, position: 'relative',
      borderBottom: '1px solid rgba(29, 28, 35, .08)',

    }}>
      <Flex gap={10} align="center">
        <LeftOutlined style={{ cursor: 'pointer' }}
          onClick={() => {
            pushAddQuery('/app/evaluate-task', {});
          }}
        />
        <Typography.Text strong style={{ fontSize: '16px' }}>{app?.name}</Typography.Text>
        {/* <Typography.Text type="secondary">{taskDetail?.description}</Typography.Text> */}
      </Flex>
    </Flex>
    <div style={{ padding: '20px 30px', background: '#fff' }}>
      <div style={{ marginTop: 0, }}>
        <Descriptions items={[
          {
            label: '评测任务',
            children: <Flex gap={8}>
              <span>
                {taskDetail?.name}
              </span>
              {taskDetail?.description && <Tooltip title={taskDetail?.description}><InfoCircleOutlined /></Tooltip>}
            </Flex>
          },
          {
            label: '创建', children: <Flex gap={12}>
              <span>
                {taskDetail?.creator}
              </span>
              <span>
                {taskDetail?.createTime}
              </span>
            </Flex>
          },
          {
            label: '更新', children: <Flex gap={12}>
              <span>
                {taskDetail?.updater}
              </span>
              <span>
                {taskDetail?.updateTime}
              </span>
            </Flex>
          },
          {
            label: '关联评测集', children:
              <a
                target="_blank"
                href={`/group/evaluate/collection/${collectionDetail?.id}?workspaceId=${collectionDetail?.workspaceId}&groupId=${collectionDetail?.groupId}`}>
                {collectionDetail?.name || collectionDetail?.id}
              </a>
          },
          {
            label: '评测方式', children: <>{
              taskConfig?.evaluator?.type === 'RULE'
                ? <Flex align="start">
                  <Tag color={evaluatorTypeColorMap[taskConfig?.evaluator?.type]}>{evaluatorTypeCnMap[taskConfig?.evaluator?.type]}</Tag>
                  <div>{evaluatorRuleCnMap[taskConfig?.evaluator?.rule?.rule]} {taskConfig?.evaluator?.rule?.value}</div>
                </Flex>
                : <Flex align="start">
                  <Tag color={evaluatorTypeColorMap[taskConfig?.evaluator?.type]}>{evaluatorTypeCnMap[taskConfig?.evaluator?.type]}</Tag>
                  <a href={`/app/dev?appId=${taskConfig?.evaluator?.appId}`} target="_blank">{taskConfig?.evaluator?.appName || taskConfig?.evaluator?.appId}</a>
                </Flex>
            }</>
          },
          { label: '状态', children: <Badge status={evaluateColorMap[taskDetail?.status]} text={evaluateCnMap[taskDetail?.status]}></Badge> },
          {
            label: '结果', children: <div>
              {taskConfig?.evaluator?.type === IEvaluatorType.规则匹配 ? '通过率' : '平均分'}
              {taskConfig?.evaluator?.type === IEvaluatorType.规则匹配 ? (taskDetail?.extInfo?.passRate ? `${taskDetail?.extInfo?.passRate * 100}%` : taskDetail?.extInfo?.passRate) : taskDetail?.extInfo?.avgScore}
            </div>
          },

        ]} />
      </div>
      <Flex justify="end" style={{ marginBottom: 10 }}>
        <Flex gap={14}>
          <Tooltip title="刷新">
            <Button type="link" onClick={onReload} icon={<ReloadOutlined />}></Button>
          </Tooltip>
          <Tooltip title="下载">
            <Button type="link" onClick={onDownload} icon={<DownloadOutlined />} disabled={[IEvaluateType.NOT_STARTED, IEvaluateType.EVALUATING].includes(Number(taskDetail?.status))}>
            </Button>
          </Tooltip>
        </Flex>
      </Flex>
      <Table
        loading={loading}
        columns={[

          {
            title: '状态', dataIndex: 'status', render: (val) => {
              // return <Badge status={evaluateColorMap[val]} text={evaluateCnMap[val]}></Badge>
              return <Tag color={evaluateColorMap[val]}>{evaluateCnMap[val]}</Tag>
            }
          },
          ...(taskDetail?.config?.evaluationSubject?.fieldMapping?.map(field => ({
            title: `${field?.evaluationSubjectField}(${fieldMap?.[`${field?.evaluationSubjectField}`]})`,
            // title: fieldMap?.[`${field?.evaluationSubjectField}`],
            dataIndex: fieldMap?.[`${field?.evaluationSubjectField}`],
            render: (val, rec) => {
              const inputs = JSON.parse(rec?.input || '{}')
              return <Tooltip title={inputs?.[fieldMap?.[`${field?.evaluationSubjectField}`]]}>
                <EllipsisText style={{ maxWidth: '400px' }}>
                  {inputs?.[fieldMap?.[`${field?.evaluationSubjectField}`]]}
                </EllipsisText>
              </Tooltip>
            }
          })) || []),
          { title: '预期输出', dataIndex: 'referenceOutput', },
          {
            title: '实际输出', dataIndex: 'actualOutput',
            render: (val) => (
              <EllipsisText style={{ maxWidth: '400px' }}>
                {val}
              </EllipsisText>
            )
          },

          ...(collectionDetail?.extInfo?.columnConfig?.defaultRuleColumns?.map(col => {
            return {
              title: col?.description,
              dataIndex: col?.name,
              render: (val, rec) => {
                const inputs = JSON.parse(rec?.input || '{}')
                // return inputs?.[col?.name]
                return <Tooltip title={inputs?.[col?.name]}>
                  <EllipsisText style={{ maxWidth: '300px' }}>
                    {inputs?.[col?.name]}
                  </EllipsisText>
                </Tooltip>
              }
            }
          }) || []),

          {
            title: '评测结果', dataIndex: 'extInfo', render: (val, rec) => {

              const extInfo = JSON.parse(val || '{}');

              return <div>
                <div>{JSON.stringify(extInfo?.result)}</div>
              </div>
            }
          },
          {
            title: '评测原因', dataIndex: 'extInfo', render: (val, rec) => {

              const extInfo = JSON.parse(val || '{}');

              return <div>
                {extInfo?.reason}
              </div>
            }
          },
          {
            title: '操作', dataIndex: 'operate', render(value, record, index) {
              return <Flex>
                {record.status != IEvaluateType.NOT_STARTED && <Popconfirm title="确认重试？" onConfirm={() => onRest(record?.id)}>
                  <Button type="link">重试</Button>
                </Popconfirm>}
              </Flex>
            },
          },
        ]?.filter(col => col)}
        dataSource={data?.values || []}
        pagination={{
          total: data?.total,
          showTotal(total, range) {
            return `共 ${total} 条`
          },
          pageSizeOptions: [PAGE_SIZE],
          pageSize: PAGE_SIZE,
          onChange(pageNum, pageSize) {
            setPageInfo({
              ...pageInfo,
              pageNum
            });
            run({
              ...pageInfo,
              pageNum,
            });
          },
        }} />
    </div>

  </div>
};

export default EvaluateTaskDetail;