import { AppApi } from "@/api/app";
import AppCopyModal from "@/components/app-copy-modal/index";
import { IAppType } from "@/interface";
import { LoadingOutlined, ReloadOutlined } from "@ant-design/icons";
import { useDebounceFn, useRequest } from "ahooks";
import { Button, Divider, Flex, Form, Input, Radio, Select } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";

export const RuleEvaluate = (props) => {
  const { value, onChange } = props;

  const onRuleChange = val => {
    onChange?.({
      ...(value || {}),
      ...val
    });
  };

  return <Flex>
    <Select options={[
      { label: '全等', value: 'EQUALS' },
      { label: '包含', value: 'CONTAINS' },
      { label: '正则', value: 'REGEX' },
      // { label: '范围', value: 'RANGE' },
    ]}
      style={{ width: 200 }}
      value={value?.rule}
      onChange={val => onRuleChange({ rule: val })}
    />
    {value?.rule === 'REGEX' && <Input
      onChange={ev => onRuleChange({ value: ev.target.value })}
    />}
  </Flex>
};

const EvaluateForm = (props, ref) => {
  const { groupId, workspaceId } = props;
  const [appList, setAppList] = useState([]);

  const [form] = Form.useForm();
  const type = Form.useWatch('type', form);
  const [appLoading, setAppLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const { run, flush, cancel } = useDebounceFn(async (keyword?) => {
    try {

      setAppLoading(true);
      const res = await AppApi.listAppsByGroup(groupId, {
        appType: [IAppType.Evaluator],
        name: keyword,
        pageSize: 100,
        pageNumber: 1
      });

      const list = res?.items?.map(app => {
        return {
          value: app.id,
          label: app.name
        }
      });
      setAppList(list);
      setAppLoading(false);

      return list;
    } catch (error) {
      setAppLoading(false);
    }
  }, {
    wait: 100
    // manual: true
  });

  useEffect(() => {
    run();
    flush();
    return () => {
      cancel();
    }
  }, []);

  const onAppAdd = () => {
    run();
    flush();
  };

  useImperativeHandle(ref, () => {
    return form;
  });

  return <>
    <Form
      form={form}
      layout="vertical"
      initialValues={{ type: 'RULE' }}>
      <Form.Item label="类型" name="type" rules={[{ required: true }]}>
        <Radio.Group options={[
          { label: '规则匹配', value: 'RULE' },
          { label: '语义匹配', value: 'SEMANTIC' },
        ]}
        />
      </Form.Item>

      {type === 'RULE' &&
        <Form.Item label="规则（如果存在行内规则会以行内规则为准）" name="rule"
          rules={[{
            validator(rule, value, callback) {
              if (!value?.rule) {
                callback('请选择规则');
                return;
              }

              callback();
            },
          }]}>
          <RuleEvaluate />
        </Form.Item>
      }
      {type === 'SEMANTIC' &&
        <Form.Item label="应用" name="app" rules={[{ required: true }]}>
          <Select
            labelInValue
            showSearch
            options={appList}
            onSearch={run}
            filterOption={false}
            open={open}
            onDropdownVisibleChange={(visible) => setOpen(visible)}
            dropdownRender={menu => {
              return <>
                {menu}
                <Divider style={{ margin: '12px 0' }} />
                <AppCopyModal
                  trigger={
                    <Button type="link" onClick={() => setOpen(false)}>没有想要的？去创建</Button>
                  }
                  submitData={{
                    groupID: groupId,
                    workspaceId
                  }}
                  onChange={onAppAdd}
                />
              </>
            }}
            suffixIcon={appLoading ? <LoadingOutlined /> : <ReloadOutlined onClick={() => {
              run('');
              flush();
            }} />}
            loading={appLoading}
          />
        </Form.Item>
      }
    </Form>
  </>
};

export default forwardRef(EvaluateForm);