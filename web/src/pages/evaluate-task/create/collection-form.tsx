import { Eva<PERSON>ate<PERSON><PERSON> } from "@/api/evaluate";
import { useGlobalState } from "@/hooks/useGlobalState";
import { useQuery } from "@/hooks/useQuery";
import CollectionDrawer from "@/pages/evaluate/collection/collection-drawer";
import { ExportOutlined, LoadingOutlined, ReloadOutlined } from "@ant-design/icons";
import { useDebounceFn } from "ahooks";
import { Button, Divider, Flex, Form, Select, Space, Tag, Tooltip } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react"

const CollectionForm = (props, ref) => {
  const { app } = props;
  const [form] = Form.useForm();
  const [collectionList, setCollectionList] = useState();
  const [collectionValue, setCollectionValue] = useState(null);
  const [collectionLoading, setCollectionLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const { parsedQuery } = useQuery();
  const { workspaceId, groupId } = parsedQuery;

  const { run, flush, cancel } = useDebounceFn(async (keyword?) => {
    if (!app?.type) return;

    const params = {
      workspaceId,
      groupId,
      page: 1,
      pageSize: 100,
      name: keyword,
      // 评测集
      type: 'INNER_COLLECTION',
      appTypes: [app.type]
    };

    try {
      setCollectionLoading(true);
      const res = await EvaluateApi.getIndicators(params);

      const list = res?.values?.map(item => ({
        // label: <Flex justify="space-between">
        //   <Flex gap={4} align="center">
        //     {item.name}
        //     <span style={{ color: '#bbb', fontWeight: 'normal' }}>({item?.extInfo?.evaluationCollectionItemCount || 0})</span>
        //   </Flex>
        //   <Flex style={{color:'#333'}}>
        //     <Tooltip title={`查看【${item.name}】评测集`}>
        //       <ExportOutlined onClick={e => {
        //         e.stopPropagation();
        //         window.open(`/group/evaluate/collection/${item.id}?workspaceId=${item?.workspaceId}&groupId=${item?.groupId}`)
        //       }} />
        //     </Tooltip>
        //   </Flex>
        // </Flex>,
        label: item.name,
        value: item.id,
        data: item,
        disabled: !item?.extInfo?.evaluationCollectionItemCount
      }));

      setCollectionList(list);
      setCollectionLoading(false);
      return list;
    } catch (error) {
      setCollectionLoading(false);
    }
  }, {
    wait: 200
  });

  useImperativeHandle(ref, () => {
    return {
      ...form,
      getFieldsValue: () => {
        const values = form.getFieldsValue();
        return {
          ...values,
          data: collectionValue?.data
        }
      }
    }
  });

  useEffect(() => {
    run();
    flush()
  }, [app?.type]);

  const onCollectionChange = (val, obj) => {
    setCollectionValue(obj);
  };

  const onCollectionAdd = (val) => {
    run();
    flush();
    window.open(`/group/evaluate/collection/${val?.id}?workspaceId=${val?.workspaceId}&groupId=${val?.groupId}`)
  };

  if (!app) return;

  return <>
    <Form layout="vertical" form={form}>
      <Form.Item label="评测集" name="evaluationCollection" help="评测集数据总量为空时不可选择，请先添加数据。"
        rules={[{ required: true }]}>
        <Select
          labelInValue
          showSearch
          options={collectionList}
          onSearch={run}
          onChange={onCollectionChange}
          filterOption={false}
          open={open}
          onDropdownVisibleChange={(visible) => setOpen(visible)}
          optionRender={op => {
            const { data } = op || {};
            const item = data?.data || {};
            return <Flex justify="space-between">
              <Flex gap={4} align="center">
                {item.name}
                <span style={{ color: '#bbb', fontWeight: 'normal' }}>({item?.extInfo?.evaluationCollectionItemCount || 0})</span>
              </Flex>
              <Flex style={{ color: '#333' }}>
                <Tooltip title={`查看【${item.name}】评测集`}>
                  <ExportOutlined onClick={e => {
                    e.stopPropagation();
                    window.open(`/group/evaluate/collection/${item.id}?workspaceId=${item?.workspaceId}&groupId=${item?.groupId}`)
                  }} />
                </Tooltip>
              </Flex>
            </Flex>
          }}
          dropdownRender={menu => {
            return <>
              {menu}
              <Divider style={{ margin: '12px 0' }} />
              {/* <Button type="link" onClick={() => {
                window.open(`/group/evaluate?workspaceId=${workspaceId}&groupId=${groupId}`)
              }}>没有想要的？去创建</Button> */}
              <CollectionDrawer
                defaultValue={{
                  appTypes: [app.type]
                }}
                onChange={onCollectionAdd}
                trigger={<Button type="link" onClick={() => setOpen(false)}>没有想要的？去创建</Button>}
              />
            </>
          }}
          loading={collectionLoading}
          suffixIcon={collectionLoading
            ? <LoadingOutlined />
            : <ReloadOutlined
              onClick={() => {
                run('');
                flush();
              }} />}
        />
      </Form.Item>
      <Form.Item label="描述">
        {collectionValue?.data?.description}
      </Form.Item>
      <Form.Item label="输入">
        <Space>
          {collectionValue?.data?.extInfo?.columnConfig?.inputColumns?.map(input =>
            <Tooltip title={input.description || input.name}>
              <Tag key={input.name} bordered={false}>{input.name}</Tag>
            </Tooltip>
          )}
        </Space>
      </Form.Item>
      <Form.Item label="数据总量" name="evaluationCollectionItemCount">
        {
          collectionValue?.data?.extInfo?.evaluationCollectionItemCount || 0
        }
      </Form.Item>
    </Form>
  </>
}

export default forwardRef(CollectionForm);