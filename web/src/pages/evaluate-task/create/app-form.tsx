import { AppTypeCnMap, IAppType } from "@/interface";
import { Flex, Form, Input, Select, Tag, Typography } from "antd";
import { forwardRef, useImperativeHandle, useMemo } from "react";
import styled from "styled-components";

const StyledInput = styled(Flex)`
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;
  padding: 4px 11px;
  align-items: center;
`;

const InputMap = (props) => {
  const { inputs, value, onChange: onParentChange,collectionInputs } = props;

  const onChange = (key, val) => {
    onParentChange?.({
      ...value,
      [key]: val
    });
  };

  const values = Object.values(value || {})?.filter(v => v);

  return <>
    <Flex vertical gap={10}>
      {inputs?.map(input => {
        return <Flex gap={2}>
          <StyledInput style={{ width: '300px' }}>
            <Typography.Text type="secondary" style={{ marginRight: 12 }}>评测对象</Typography.Text>
            {`${input.key}(${input.title})`}
          </StyledInput>
          <StyledInput>
            =
          </StyledInput>
          <StyledInput style={{ width: '300px' }}>
            <Typography.Text type="secondary" style={{ marginRight: 12 }}>评测集</Typography.Text>
            <Select
              style={{ flex: 1 }}
              bordered={false}
              allowClear
              onChange={val => onChange(input.key, val)}
              options={collectionInputs?.map(input=>({
                ...input,
                disabled: values?.includes(input?.value)
              }))}
            />
          </StyledInput>
        </Flex>
      })}
    </Flex>
  </>
}

const AppForm = (props, ref) => {
  const { app, collection } = props;
  const [form] = Form.useForm();


  useImperativeHandle(ref, () => {
    return form
  });

  const inputs = useMemo(()=> {
    if(app?.type === IAppType.AgentWorkflow) {
      return app?.config?.inputs?.map(input => ({...input, key: input.name}))
    }
    return app?.config?.paramsInPrompt;
  }, [
    app
  ]);

  const collectionInputs = useMemo(()=>{
    return collection?.extInfo?.columnConfig?.inputColumns?.map(input => ({
      label: input.name,
      value: input.name
    }))
  }, [
    collection?.extInfo?.columnConfig?.inputColumns
  ]);

  if(!app) return;

  return <>
    <Form layout="vertical" form={form}>
      <Form.Item label="类型">
        {AppTypeCnMap[app?.type]}
      </Form.Item>
      <Form.Item label="应用名称">
        {app?.name}
      </Form.Item>
      <Form.Item label="字段映射" name="inputMap"
        rules={[{ required: true }, {
          validator(rule, value, callback) {
            if (!value) {
              callback('请选择字段映射');
              return;
            }
            const values = Object.values(value)?.filter(v => v);
            if (values?.length) {
              callback();
              return;
            } else {
              callback('至少选择一个字段映射');
              return;
            }
          }
        }]}>
        <InputMap inputs={inputs} collectionInputs={collectionInputs}/>
      </Form.Item>
    </Form>
  </>
};

export default forwardRef(AppForm);