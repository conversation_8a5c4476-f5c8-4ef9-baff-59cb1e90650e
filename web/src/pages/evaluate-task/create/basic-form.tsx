import { Form, Input } from "antd";
import { forwardRef, useImperativeHandle } from "react"

const BasicForm = (props, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => form);

  return <>
    <Form layout="vertical" form={form}>
      <Form.Item label="名称" name="name"
        rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item label="描述" name="description">
        <Input.TextArea />
      </Form.Item>
    </Form>
  </>
};


export default forwardRef(BasicForm);