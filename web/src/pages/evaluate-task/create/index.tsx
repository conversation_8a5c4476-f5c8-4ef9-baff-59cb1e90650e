import { CloseOutlined, LeftOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Card, Flex, Form, message, Modal, Space, Steps, Tooltip } from "antd";
import { useRef, useState } from "react";
import BasicForm from "./basic-form";
import CollectionForm from "./collection-form";
import AppForm from "./app-form";
import EvaluateForm from "./evaluate-form";
import { usePathQuery, useQuery } from "@/hooks/useQuery";
import { useGlobalState } from "@/hooks/useGlobalState";
import styled from "styled-components";
import { EvaluateApi } from "@/api/evaluate";
import { useRequest } from "ahooks";
import { AppApi } from "@/api/app";

const StyledCreate = styled.div`
  .ant-form-item-label {
    font-weight: bold;
  }
`;

const EvaluateTaskCreate = () => {
  const [step, setStep] = useState(0);

  const basicFormRef = useRef(null);
  const appFormRef = useRef(null);
  const collectionFormRef = useRef(null);
  const evaluateFormRef = useRef(null);

  const { globalState } = useGlobalState();
  const { user } = globalState;

  const { parsedQuery } = useQuery();

  const { workspaceId, groupId, appId } = parsedQuery;

  const { pushRetainQuery } = usePathQuery();
  const [collectionValue, setCollectionValue] = useState(null);


  const { data: app } = useRequest(async () => {
    if (!appId) return;
    try {
      const res = await AppApi.getAppDetailWithoutAuth(appId as string);
      return res;
    } catch (error) {
      console.log('error', error);
    }

  }, {
    refreshDeps: [appId]
  });

  const goNext = async () => {
    if (step === 0) {
    }

    if (step === 1) {
      await collectionFormRef.current?.validateFields();
      const res = await collectionFormRef.current?.getFieldsValue();
      if (!res.data?.extInfo?.evaluationCollectionItemCount) {
        message.error(<span>该评测集暂无数据，请先<a href={`/group/evaluate/collection/${res?.data?.id}?workspaceId=${res?.data?.workspaceId}&groupId=${res?.data?.groupId}`} target="_blank">添加评测数据</a></span>);
        return;
      }
      if (collectionValue?.id !== res?.data?.id) {
        appFormRef.current?.resetFields();
      }
      setCollectionValue(res.data);
    }

    if (step === 2) {
      const res = await appFormRef.current?.validateFields();
    }

    if (step === 3) {
    }

    setStep(step + 1);
  };

  const goPre = () => {
    // if (step === 1) {
    //   console.log('basicFormRef.current', basicFormRef.current);
    //   basicFormRef.current.setFieldsValue(basicFormValue);
    // }
    setStep(step - 1);
  };

  const onSubmit = async () => {

    const evaluatorValue = await evaluateFormRef.current.validateFields();
    const evaluator = {
      ...(evaluatorValue || {})
    };
    if (evaluatorValue.type === "SEMANTIC") {
      evaluator.appId = evaluatorValue.app?.value;
      evaluator.appName = evaluatorValue.app?.label;
      delete evaluator.app
    }

    const inputMap = appFormRef.current.getFieldsValue()?.inputMap;

    const fieldMap = Object.keys(inputMap || {}).map(evaluationSubjectField => {
      return {
        evaluationSubjectField,
        evaluationSetField: inputMap[evaluationSubjectField]
      }
    });

    const params = {
      workspaceId, groupId,
      appId: app.id,
      settingId: '',
      ...(basicFormRef.current?.getFieldsValue() || {}),
      source: "INNER",
      evaluationConfig: {
        evaluationCollection: {
          evaluationCollectionId: (collectionFormRef.current?.getFieldsValue() || {})?.evaluationCollection?.value,
          evaluationCollectionName: (collectionFormRef.current?.getFieldsValue() || {})?.evaluationCollection?.label,
          version: '1.0.0'
        },
        evaluationSubject: {
          appId: app.id,
          appName: app.name,
          settingId: '',
          settingName: '',
          version: "1.0.0",
          fieldMapping: fieldMap
        },
        evaluator
      },
      operator: user.name
    };

    const res = await EvaluateApi.createTask(params);
    if (res?.suc && res?.taskId) {
      message.success('新建评测任务成功');
      pushRetainQuery('/app/evaluate-task', ['workspaceId', 'groupId', 'appId']);
    }
  };

  const defaultProps = {
    groupId,
    workspaceId
  }

  return <div style={{ display: 'relative' }}>
    <Flex justify="center"
      style={{
        width: '100%',
        fontSize: '18px',
        fontWeight: 'bold', marginLeft: 10, position: 'absolute',

      }}>创建评测任务</Flex>
    <Flex style={{
      marginBottom: 40,
      paddingBottom: '20px',
      zIndex: 10, position: 'relative',
      borderBottom: '1px solid rgba(29, 28, 35, .08)',

    }}>
      <Tooltip title="返回应用">
        <LeftOutlined style={{
          // color: 'rgb(140, 140, 140)',
          fontSize: '16px'
        }}
          onClick={() => {
            Modal.confirm({
              title: '关闭',
              content: '关闭评测集不会保存数据，确认关闭?',
              onOk: () => {
                pushRetainQuery('/app/evaluate-task', ['workspaceId', 'groupId', 'appId']);
              }
            })
          }}
        />
      </Tooltip>
      <Flex style={{ fontSize: '16px', fontWeight: 'bold', marginLeft: 10 }}>{app?.name}</Flex>
    </Flex>

    <Card style={{ width: '80%', margin: '0 auto' }}>
      <Flex
        justify="space-between"
        align="center"
        vertical
        style={{ height: '100%', marginTop: 20 }}
      >
        <div style={{ width: '800px' }}>
          <Steps
            current={step}
            items={[
              { title: '基础信息' },
              { title: '评测集' },
              { title: '评测对象' },
              { title: '评估器' },
            ]}
          />
          <Flex justify="center">
            <StyledCreate style={{ marginTop: 20, width: '600px' }}>
              <div style={{ height: step === 0 ? '100%' : 0, overflow: 'scroll' }}>
                <BasicForm ref={basicFormRef} />
              </div>
              <div style={{ height: step === 1 ? '100%' : 0, overflow: 'scroll' }}>
                <CollectionForm ref={collectionFormRef} app={app} />
              </div>
              <div style={{ height: step === 2 ? '100%' : 0, overflow: 'scroll' }}>
                <AppForm app={app} ref={appFormRef} collection={collectionValue} />
              </div>
              <div style={{ height: step === 3 ? '100%' : 0, overflow: 'scroll' }}>
                <EvaluateForm {...defaultProps} ref={evaluateFormRef} />
              </div>
            </StyledCreate>
          </Flex>
        </div>
        <Flex justify="center">
          {step === 0 && <Button type="primary" onClick={goNext}>下一步</Button>}
        {(step === 1 || step === 2) && <Space>
          <Button onClick={goPre}>上一步</Button>
          <Button type="primary" onClick={goNext}>下一步</Button>
        </Space>}
        {step === 3 && <Space>
          <Button onClick={goPre}>上一步</Button>
            <Button type="primary" onClick={onSubmit}>提交</Button>
          </Space>}
        </Flex>
      </Flex>
    </Card>
  </div >
};

export default EvaluateTaskCreate;
