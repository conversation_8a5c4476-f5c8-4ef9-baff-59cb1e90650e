import { <PERSON><PERSON>ate<PERSON><PERSON> } from "@/api/evaluate";
import { usePathQuery, useQuery } from "@/hooks/useQuery";
import { PlusOutlined, ReloadOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import { Badge, Button, Card, Flex, Popconfirm, Space, Table, Tag, Tooltip } from "antd";
import { useEffect, useState } from "react";
import { evaluateCnMap, evaluateColorMap, evaluatorTypeCnMap, evaluatorTypeColorMap, evaluatorRuleCnMap, IEvaluateType, IEvaluatorType } from "@/interface/evaluate";

const PAGE_SIZE = 10;

const EvaluateTask = (props) => {
  const { pushAddQuery } = usePathQuery();

  const [pageInfo, setPageInfo] = useState({
    page: 1,
    pageSize: PAGE_SIZE
  });

  const { parsedQuery } = useQuery();
  const { appId, workspaceId, groupId } = parsedQuery;

  const { data, loading, run } = useRequest(async (params?) => {
    if (!appId) return null;

    const res = await EvaluateApi.getTaskList({
      appId,
      settingId: '',
      source: 'INNER',
      ...pageInfo,
      ...(params || {})
    });
    return res;
  }, {
    // refreshDeps: [appId, settingId],
    manual: true
  });

  useEffect(() => {
    run();
  }, [appId]);

  const onRefresh = () => {
    setPageInfo({
      ...pageInfo,
      page: 1
    });

    run({
      page: 1
    });
  };

  const onAdd = () => {
    pushAddQuery('/app/evaluate-task/create', {});
  };

  const onRest = async (evaluationTaskId) => {
    const res = await EvaluateApi.retryByEvaluationTaskId({ evaluationTaskId });
    run();

  };

  return <Card style={{ width: '80%', margin: '0 auto' }}>
    <Flex justify="space-between" style={{ marginBottom: 10 }}>
      <Flex style={{ fontSize: 16, fontWeight: 'bold' }}>评测任务</Flex>
      <Flex gap={14}>
        <Tooltip title="刷新">
          <a onClick={onRefresh}>
            <ReloadOutlined />
          </a>
        </Tooltip>
        <Tooltip title="新建评测任务">
          <a onClick={onAdd}>
            <PlusOutlined />
          </a>
        </Tooltip>
      </Flex>
    </Flex>

    <Table
      loading={loading}
      columns={[
        { title: 'ID', dataIndex: 'id' },
        { title: '评测任务', dataIndex: 'name' },
        // { title: '评测应用类型', dataIndex: '' },
        // { title: '评测应用', dataIndex: '' },
        {
          title: '关联评测集', dataIndex: 'collection', render(value, record, index) {
            const config = JSON.parse(record?.config || '{}');
            return <a
              target="_blank"
              href={`/group/evaluate/collection/${config?.evaluationCollection?.evaluationCollectionId}?workspaceId=${workspaceId}&groupId=${groupId}`}>
              {config?.evaluationCollection?.evaluationCollectionName || config?.evaluationCollection?.evaluationCollectionId}
            </a>
          },
        },
        {
          title: '状态', dataIndex: 'status', render(value, record, index) {
            return <Badge status={evaluateColorMap[value]} text={evaluateCnMap[value]}></Badge>
          },
        },
        {
          title: '评测方式', dataIndex: 'evaluator', render(value, record, index) {
            const config = JSON.parse(record?.config || '{}');

            if (config?.evaluator?.type === 'RULE') {
              return <Flex>
                <Tag color={evaluatorTypeColorMap[config?.evaluator?.type]}>{evaluatorTypeCnMap[config?.evaluator?.type]}</Tag>
                <div>{evaluatorRuleCnMap[config?.evaluator?.rule?.rule]} {config?.evaluator?.rule?.value}</div>
              </Flex>
            }

            return <Flex>
              <Tag color={evaluatorTypeColorMap[config?.evaluator?.type]}>{evaluatorTypeCnMap[config?.evaluator?.type]}</Tag>
              <a href={`/app/dev?appId=${config?.evaluator?.appId}`} target="_blank">{config?.evaluator?.appName || config?.evaluator?.appId}</a>
            </Flex>
          }
        },
        {
          title: '评测结果', dataIndex: 'extInfo', render(value, record, index) {
            const config = JSON.parse(record?.config || '{}');
            if (config?.evaluator?.type === IEvaluatorType.规则匹配) {
              if (typeof value?.passRate !== 'number') return value?.passRate;

              return <span>
                案例总数：{value?.totalCount}{value?.failureCount ? <span style={{ color: 'red' }}>（执行失败：{value?.failureCount}）</span> : <span style={{ color: 'green' }}>（全部执行成功）</span>}
                <p style={{ margin: 0 }}>通过率：{value?.passRate * 100} %（{value?.passedCount} / {value?.successCount}）</p>
              </span>
            }

            if (config?.evaluator?.type === IEvaluatorType.语义匹配) {
              if (typeof value?.avgScore !== 'number') return value?.avgScore;

              return <span>
                平均分：{value?.avgScore}
              </span>
            }
            return '-'
          },
        },
        {
          title: '操作', dataIndex: 'operate',
          render(value, record, index) {
            return <Flex>
              <Button type="link" onClick={() => {
                pushAddQuery(`/app/evaluate-task/detail/${record.id}`, {})
              }}>详情</Button>
              {IEvaluateType.NOT_STARTED != record?.status
                && <Popconfirm title="确认重试？" onConfirm={() => onRest(record?.id)}>
                  <Button type="link">重试</Button>
                </Popconfirm>
              }
              <Button type="link"
                disabled={[IEvaluateType.NOT_STARTED, IEvaluateType.EVALUATING].includes(Number(record?.status))}
                onClick={() => {
                  EvaluateApi.downloadEvaluateTask({
                    evaluationTaskId: record.id
                  }).then(res => {
                    if (res?.url) {
                      window.open(res?.url)
                    }
                  })
                }}>导出</Button>
            </Flex>
          },
        }
      ]}
      dataSource={data?.values as any[]}
      pagination={{
        total: data?.total,
        showTotal(total, range) {
          return `共 ${total} 条`
        },
        pageSizeOptions: [PAGE_SIZE],
        pageSize: PAGE_SIZE,
        onChange(page, pageSize) {
          setPageInfo({
            ...pageInfo,
            page
          });
          run({
            ...pageInfo,
            page,
          });
        },
      }}
    />
  </Card>
}

export default EvaluateTask;
