import React, { useCallback, useEffect, useState } from 'react';
import { Card, message } from 'antd';
import styled from 'styled-components';
import { getPmsFromCookie, getPmsLoginUrl, pmsInfo } from '@/api/pms';

const Container = styled.div`
  background-color: rgba(248, 250, 252);
  padding: 8px;
  height: calc(100vh - 90px);
  box-sizing: border-box;
`;

function SocialAigc() {
  const [visible, setVisilbe] = useState(false);
  const [pms_u, setPmsu] = useState('');

  const login = useCallback(() => {
    getPmsLoginUrl(window.location.href).then((res) => {
      if (res) {
        window.location.href = res;
      }
    })
  }, []);

  useEffect(() => {
    pmsInfo().then((res) => {
      if (res?.login) {
        getPmsFromCookie().then((res) => {
          if (res?.pms_u) {
            setPmsu(res.pms_u);
            setVisilbe(true);
          } else {
            message.error('系统问题，请联系管理员')
          }
        }).catch((e) => {
          message.error(e.message || '网络有点问题，请稍后再试哦');
        })
      } else {
        login();
      }
    }).catch(() => {
      login();
    })
  }, [login]);
  
  return (
    <Container className='knowledge'>
      <Card
        style={{ width: '100%', margin: '0 auto', height: '100%' }}
        bodyStyle={{height: '100%'}}
        title="社交直播AIGC"
      >
        {
          visible ? (
            <iframe style={{ width: '100%', height: '90%', margin: '0 auto', border: 0 }}  src={`https://crush-cms.hz.netease.com/aigc-admin/bot?pmsu=${pms_u}`} />
          ) : null
        }
      </Card>
    </Container>
  )
}

export default SocialAigc;