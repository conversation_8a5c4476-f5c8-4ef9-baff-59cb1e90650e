import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react";
import TableEdit from "./table-edit"
import { Flex, message, Select, Typography } from "antd";
import { InfoCircleOutlined, WarningOutlined } from "@ant-design/icons";

interface IProps {
  sheets: any[];
  value: {
    sheetIndex: number;
  },
  dataTypes: Record<string, number>;
  savedHeads: any[];
  onHeadsEqualChange: (val: boolean) => void;
  editableRows?: string[];
  type?: 'table' | 'cio';
  headsEqual?: (current: any, original?: any) => boolean;
}

const EditForm = forwardRef((props: IProps, ref) => {
  const { sheets, value, dataTypes, savedHeads, onHeadsEqualChange, editableRows = ['name', 'desc', 'type'], type = 'table', headsEqual: headsEqualFn } = props;
  const [sheetIndex, setSheetIndex] = useState(0);
  const [headers, setHeaders] = useState(sheets?.[0]?.heads);
  const tableRef = useRef(null);
  const [headsEqual, setHeadsEqual] = useState(true);

  const areHeadsEqual = headsEqualFn ? headsEqualFn : (current, original) => {
    if (current?.length !== original?.length) return false;
    for (let i = 0; i < current.length; i++) {
      if (
        // current[i].type !== original[i].type ||
        current[i].name !== original[i].name
      ) {
        return false
      }
    }
    return true;
  }


  useEffect(() => {
    const initial: any = { ...value };

    if (typeof value?.sheetIndex !== 'number') {
      initial.sheetIndex = 0;
      initial.headers = sheets?.[0]?.heads;
    }
    setSheetIndex(initial?.sheetIndex);
    setHeaders(initial?.headers);

    let newEqual = headsEqual;

    if (savedHeads) {

      // 如果存在savedHeads的话要进行对比
      if (savedHeads?.length > 0) {
        const equal = areHeadsEqual(initial?.headers, savedHeads);
        newEqual = equal;
        setHeadsEqual(equal);
      }
    }
    onHeadsEqualChange(newEqual)
  }, [value, savedHeads]);

  useImperativeHandle(ref, () => {
    return {
      validateFields: async () => {
        if (typeof sheetIndex === 'undefined') {
          message.error('请选择数据表');
          return false;
        }
        const res = await tableRef.current?.validateFields();
        if (res) {
          return {
            headers: res,
            sheetIndex,
          }
        }
      }
    }
  });

  const onSheetChange = (val, obj) => {
    let newEqual = headsEqual;
    setSheetIndex(val);
    // 第一次
    setHeaders(obj?.heads?.map(item => ({
      ...item,
      checked: !item.disabled
    })));

    // 如果存在savedHeads的话要进行对比
    if (savedHeads?.length > 0) {
      const equal = areHeadsEqual(obj?.heads, savedHeads);
      newEqual = equal;
      setHeadsEqual(equal);
    }
    onHeadsEqualChange(newEqual);

  }

  return <Flex vertical style={{ width: '100%' }}>
    {type === 'table' && <div>
      数据表选择：<Select
        style={{ width: 200 }}
        allowClear={false}
        value={sheetIndex}
        options={sheets}
        onChange={onSheetChange}
      />
      {!headsEqual && <Flex gap={6} style={{ fontSize: '16px', color: 'red' }}>
        <WarningOutlined />
        表头与当前表结构不一致,列名称及顺序需保持一致，请重新上传表格。</Flex>}
    </div>}
    {type === 'cio' && <div>
      投放池：{sheets?.[0]?.label}
      {!headsEqual && <Flex gap={6} style={{ fontSize: '16px', color: 'red' }}>
        <WarningOutlined />
        表头与当前表结构不一致,列名称、列类型需保持一致，请重新选择投放池。
      </Flex>}
    </div>}

    {savedHeads && <div style={{ fontSize: '12px', color: 'rgba(0,0,0,0.45)' }}>表头需要与现有表格结构保持一致。</div>}
    {savedHeads && <Flex vertical>
      <Typography.Title level={5}>当前知识库表结构</Typography.Title>
      <TableEdit
        value={savedHeads}
        dataTypes={dataTypes}
        tableProps={{
        }}
        disabled={!!savedHeads}
      />
    </Flex>}

    <Flex style={{ height: savedHeads ? 'auto' : 'auto', overflow: 'scroll' }} vertical>
      <Typography.Title level={5} style={{ marginTop: 40 }}>当前表结构</Typography.Title>
      <div style={{ color: 'var' }}>
        <Typography.Text type="secondary">
          <div>
            <InfoCircleOutlined />{' '}
            当前表可选数据类型：{Object.keys(dataTypes || {})?.map(type => `${type} ${dataTypes[type]} 个`).join('、')}。
          </div>
          <div style={{ marginLeft: '16px' }}>
            只有STRING类型的字段才可以作为语义理解字段。
          </div>
        </Typography.Text>
      </div>
      <TableEdit
        ref={tableRef}
        value={headers}
        dataTypes={dataTypes}
        savedHeads={savedHeads}
        tableProps={{
        }}
        disabled={!!savedHeads?.length}
        editableRows={editableRows}
        type={type}
      />
    </Flex>
  </Flex>
})

export default EditForm;
