import { Checkbox, Form, Input, message, Select, Table } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import styled from "styled-components";

const StyledFormItem = styled(Form.Item)`
  margin-bottom: 0;
`;

interface IProps {
  value: any;
  dataTypes: Record<string, number>;
  tableProps?: Record<string, any>;
  disabled?: boolean;
  savedHeads?: any;
  editableRows?: any[];
  type?: 'table' | 'cio';
}

const TableEdit = forwardRef((props: IProps, ref) => {
  const { value, dataTypes, disabled: tableDisabled = true, savedHeads,
    tableProps = {
      rowKey: 'code'
    }, editableRows, type = "table" } = props;

  const [semanticKey, setSemanticKey] = useState();
  const [form] = Form.useForm();
  const [data, setData] = useState([]);
  const [rowStatus, updateRowStatus] = useState(0);

  const rowKey = tableProps?.rowKey || 'code';

  useEffect(() => {
    const semanticKey = value?.find(item => !!item.semantic)?.[rowKey];
    const list = value?.map(item => {
      const { semantic, ...rest } = item;

      return rest
    });

    form.setFieldsValue(list);
    setSemanticKey(semanticKey);
    setData(list);
  }, [value]);


  useImperativeHandle(ref, () => ({
    ...form,
    validateFields: async () => {
      let currentSemanticKey = semanticKey;
      // 如果有savedHeads，semanticKey直接赋值
      if (savedHeads) {
        const semanticName = savedHeads?.find(item => !!item.semantic)?.name;
        const semanticCode = value?.find(item => item.name === semanticName)?.[rowKey];
        currentSemanticKey = semanticCode;
        setSemanticKey(semanticCode);
      }
      // 校验是否选择索引
      if (!currentSemanticKey) {
        message.error('请选择语义理解字段');
        throw new Error('请选择语义理解字段');
      }
      // 校验值
      await form.validateFields();
      const values = form.getFieldsValue(true);

      return values?.map((value, index) => {
        if (savedHeads && type === 'table') {
          value.type = savedHeads[index].type;
        }
        return {
          ...value,
          semantic: value[rowKey] === currentSemanticKey
        }
      })
    },
    getFieldsValue: async () => {
      const values = form.getFieldsValue(true);
      return values?.map(value => {
        return {
          ...value,
          semantic: value[rowKey] === semanticKey
        }
      })
    }
  }));

  const onSelect = (record, selected, selectedRows) => {
    const index = data?.findIndex(item => item[rowKey] === record[rowKey]);
    const checked = form.getFieldValue([index, 'checked']);

    if (selected) {
      if (!checked) {
        message.error('未使用的列无法作为语义理解字段，请重新选择！');
        return false;
      }
      setSemanticKey(record[rowKey]);

    }
  };

  const onDelete = index => {
    const newData = [...(form.getFieldsValue(true))];
    newData.splice(index, 1);
    form.setFieldsValue(newData);
    setData(newData);
  }

  const onRowCheck = (checked, record, index) => {
    if (!checked) {
      if (record[rowKey] === semanticKey) {
        setSemanticKey(undefined);
      }
    }
    updateRowStatus(rowStatus + 1);
  };

  const onDataTyeChange = (val, record) => {
    if (val !== 'STRING') {
      if (record[rowKey] === semanticKey) {
        setSemanticKey(undefined);
      }
    }
    updateRowStatus(rowStatus + 1);
  };

  return <>
    <Form form={form}
      size="small"
      style={{ width: '100%' }}
      disabled={tableDisabled}>
      <Table
        rowKey="code"
        size="small"
        rowSelection={{
          type: 'radio',
          columnWidth: 48,
          columnTitle: '语义理解字段',
          onSelect,
          selectedRowKeys: semanticKey ? [semanticKey] : [],
          getCheckboxProps: (record) => {
            const index = data?.findIndex(item => item.code === record.code);
            if (index > -1) {
              record.type = form.getFieldValue([index, 'type']);
            }
            if (record.disabled || record && record.type !== 'STRING') {
              return {
                disabled: true
              }
            }

          },
        }}
        columns={[
          {
            title: '列名',
            dataIndex: 'name',
            render: (value, record, index) => {
              const checked = form.getFieldValue([index, 'checked']);
              const editable = editableRows?.includes('name');

              const isValidateItem = !record.disabled && checked && !tableDisabled;

              return <StyledFormItem
                validateTrigger={['onChange', 'onBlur']}
                validateFirst
                rules={isValidateItem ? [
                  { required: true, message: '请填写列名' },
                  {
                    validateTrigger: 'onBlur',
                    validator: (rule, value, callback) => {
                      const currentNames = (form.getFieldsValue(true) || []).filter((item, i) => i !== index && item.checked && !item.disabled).map(item => item.name);

                      if (currentNames?.includes(value)) {
                        return Promise.reject(`列名不能重复`);
                      }
                      return Promise.resolve();

                    },
                  }] : []}
                name={[index, 'name']}
              >
                <Input disabled={!checked || record.disabled || !editable || tableDisabled} />
              </StyledFormItem>
            },
          },
          // {
          //   title: '索引', dataIndex: 'indexPath',
          //   render: (value, record, index) => {
          //     return <StyledFormItem
          //       rules={[{ required: true }]}
          //       name={[index, 'indexPath']}
          //     >
          //       <Input />
          //     </StyledFormItem>
          //   },
          // },
          {
            title: '描述', dataIndex: 'desc',
            render: (value, record, index) => {
              const checked = form.getFieldValue([index, 'checked']);
              const editable = editableRows?.includes('name');

              return <StyledFormItem
                name={[index, 'desc']}
              >
                <Input disabled={!checked || tableDisabled || !editable || tableDisabled} />
              </StyledFormItem>
            },
          },
          {
            title: '数据类型', dataIndex: 'type',
            render: (value, record, index) => {
              const checked = form.getFieldValue([index, 'checked']);
              const editable = editableRows?.includes('type');
              const isValidateItem = !record.disabled && checked && !tableDisabled;

              return <StyledFormItem
                name={[index, 'type']}
                rules={isValidateItem ? [{ required: isValidateItem }, {
                  validateTrigger: 'onChange',
                  validator(rule, value, callback) {
                    const currentTypes = (form.getFieldsValue(true) || []).filter((item, i) => {
                      return (!item.disabled) && item.checked && item.type === value
                    });
                    const maxCount = dataTypes[value];
                    if (maxCount) {
                      if (currentTypes?.length > maxCount) {
                        return Promise.reject(`${value} 类型最多可选择 ${maxCount} 个`);
                      }
                      return Promise.resolve();
                    }
                    return Promise.reject(`不存在 ${value} 类型 `);
                  },
                }] : []}
              >
                <Select options={Object.keys(dataTypes || {})?.map(type => {
                  return {
                    label: type,
                    value: type,
                    // count: dataTypes[type]
                  }
                })}
                  style={{ width: 100 }} disabled={!checked || tableDisabled || !editable}
                  onChange={val => onDataTyeChange(val, record)}
                />
              </StyledFormItem>
            },
          },
          tableDisabled ? null : {
            title: '使用该列', dataIndex: 'operate',
            render: (value, record, index) => {
              if (tableDisabled || record.disabled) return null;
              return <StyledFormItem name={[index, 'checked']} valuePropName="checked">
                <Checkbox onChange={ev => onRowCheck(ev.target.checked, record, index)} />
              </StyledFormItem>
            },
          }
        ]?.filter(item => item)}
        dataSource={data}
        pagination={false}
        {...tableProps}
      />
    </Form>
  </>
});

export default TableEdit;
