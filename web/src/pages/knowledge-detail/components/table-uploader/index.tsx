import { Button, message, Modal, Space, Steps, Table } from "antd";
import { useRef, useState } from "react";
import EditForm from "./edit-form";
import UploadForm from "./upload-form";
import { NewKnowledge } from "@/api";

const TableUploader = (props) => {
  const { knowledgeId, workspaceId, groupId, onChange, trigger } = props;
  const [open, setOpen] = useState(false);
  const [step, setStep] = useState(0);
  const [file, setFile] = useState<{
    name: string,
    description?: string;
    fileList: any[];
  } | undefined>(
    // {
    //   name: '游戏积分表2025.xlsx',
    //   fileList: [
    //     {
    //       "key": "jd-workflow-tmp/langbase/app/upload/files/86ce2ba8-a9f5-4463-86b4-5ab0cf0265a3.xlsx",
    //       "url": "https://jd-workflow-tmp.nos-jd.163yun.com/langbase/app/upload/files/86ce2ba8-a9f5-4463-86b4-5ab0cf0265a3.xlsx",
    //       "name": "游戏积分表2025.xlsx",
    //       "status": "done",
    //       "file": {
    //         "uid": "rc-upload-1741682664519-3"
    //       },
    //       "uid": "__AUTO__1741682716953_0__"
    //     }
    //   ]
    // }
  );

  const [headerConfig, setHeaderConfig] = useState({
    sheetIndex: 0,
    headers: undefined,
  });
  const [sheets, setSheets] = useState<any[]>();
  const [dataTypes, setDataTypes] = useState();
  const [previewData, setPreviewData] = useState<any>();
  const [uploadLoading, setUploadLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(true);
  const [savedHeads, setSavedHeads] = useState();
  const uploadForm = useRef(null);
  const editForm = useRef(null);

  const reset = () => {
    setStep(0);
    setFile(undefined);
    setHeaderConfig({ sheetIndex: 0, headers: undefined });
    setSheets(undefined);
    setDataTypes(undefined);
    setPreviewData(undefined);
    setSavedHeads(undefined);
  };

  const onClose = () => {
    setOpen(false);
    reset();
  };


  const onOpen = () => {
    setOpen(true);
  };

  const goNext = async () => {
    // 解析sheet
    if (step === 0) {
      try {
        setUploadLoading(true);
        const newFile = await uploadForm.current?.validateFields();
        setFile(newFile);
        if (!newFile?.fileList?.[0].key) {
          message.error('请上传文件');
          return false;
        }

        const knowledgeItemDetailRequestList = newFile?.fileList?.map(file => {
          return {
            name: newFile.name,
            type: 'table',
            description: newFile.description,
            metaData: JSON.stringify({
              nosKey: file?.key,
              type: file?.file?.type
            }),
            importType: 'local',
          }
        });
        // 解析sheet
        const res = await NewKnowledge.getSheet({
          knowledgeId,
          workspaceId,
          groupId,
          knowledgeItemDetailRequestList
        });
        if (res.debugInfo) {
          throw new Error(res.debugInfo)
        }
        // 有saveHeads说明不是第一次上传
        if (res.savedHeads?.length > 0) {
          setEditLoading(true);
          setSavedHeads(res.savedHeads);
        } else {
          // 第一次上传，没有saveHeads
          setEditLoading(false);
        }
        setHeaderConfig({
          sheetIndex: 0,
          headers: res?.values?.[0]?.heads?.map(item => {
            return {
              ...item,
              checked: !item.disabled
            }
          })
        });
        setSheets(res?.values?.map((item, index) => {
          return {
            label: item.sheetNames,
            value: index,
            heads: item.heads
          }
        }));
        setDataTypes(res?.optionalType)
        setUploadLoading(false);
        setStep(step + 1);
      } catch (error: any) {
        error.message && message.error(error.message)
        setUploadLoading(false);
      }
    }

    // 编辑表头
    if (step === 1) {
      // 保存修改后的表头
      const res = await editForm.current?.validateFields();
      setHeaderConfig({
        sheetIndex: res.sheetIndex,
        // headers: res.headers?.filter(item => item.checked)
        headers: res.headers
      });
      // 预览最后的表格
      const previewData = await NewKnowledge.previewSheet({
        sheetIndex: res.sheetIndex,
        updatedHeads: res.headers?.filter(item => item.checked),
        metaData: JSON.stringify({
          nosKey: file?.fileList?.[0]?.key,
          originalHeads: sheets?.find((_, index) => index === res.sheetIndex)?.heads
        }),
        page: 1,
        pageSize: 100,
      });
      setPreviewData(previewData)
      setStep(step + 1);
    }
  };

  const goPre = () => {
    setStep(step - 1);
  };

  const onSubmit = async () => {
    const knowledgeItemDetailRequestList = file?.fileList?.map(file => {
      return {
        name: file.name,
        type: 'table',
        description: file.description,
        metaData: JSON.stringify({
          nosKey: file?.key,
          type: file?.file?.type,
          // 原始表头
          originalHeads: sheets?.find((_, index) => index === headerConfig.sheetIndex)?.heads
        }),
        config: JSON.stringify({
          sheetIndex: headerConfig.sheetIndex,
          // 修改后的表头
          updatedHeads: headerConfig.headers?.filter(item => item.checked),
        }),
        importType: 'local',
      }
    });

    const res = await NewKnowledge.addDoc({
      knowledgeId,
      workspaceId,
      groupId,
      knowledgeItemDetailRequestList
    });
    if (res.debugInfo) {
      message.error(res.debugInfo);

    } else {
      onChange?.();
      setOpen(false);
      reset();
    }
  };

  const onFileChange = val => {
    // if (val?.fileList?.length > 0) {
    //   setUploadLoading(false);
    // } else {
    //   setUploadLoading(true);
    // }
  }

  return <>
    <div onClick={onOpen}>{trigger ?? <Button >上传</Button>}</div>
    <Modal
      open={open}
      title="表格上传"
      onCancel={onClose}
      width={880}
      footer={<>
        {/* 选择文档 */}
        {step === 0 &&
          <Button type="primary"
            // disabled={uploadLoading}
            loading={uploadLoading}
            onClick={() => goNext()}>
            下一步</Button>
        }
        {/* 表结构配置 */}
        {step === 1 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={goNext} disabled={editLoading}>下一步</Button>
        </Space>
        }
        {/* 预览 */}
        {step === 2 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={onSubmit} >提交</Button>
        </Space>}

        {/* 数据处理 */}
        {/* {step === 3 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={onSubmit}>完成</Button>
        </Space>} */}
      </>}>
      <div style={{ width: '100%' }}>
        <div style={{ marginBottom: '30px' }}>
          <Steps
            current={step}
            items={[
              { title: '上传' },
              { title: '表结构设置' },
              { title: '预览' },
              // { title: '数据处理' }
            ]}
          />
        </div>
        <div style={{ width: '100%' }}>

          {step === 0 &&
            <UploadForm
              value={file}
              ref={uploadForm}
              onChange={onFileChange}
            />
          }

          {step === 1 && <EditForm
            dataTypes={dataTypes}
            sheets={sheets}
            value={headerConfig}
            ref={editForm}
            savedHeads={savedHeads}
            onHeadsEqualChange={equal => {
              setEditLoading(!equal)
            }}
          />}

          {step == 2 && <Table
            columns={headerConfig?.headers?.filter(item=> item.checked)?.map(item => {
              return {
                dataIndex: item.code,
                title: item.name
              }
            })}
            dataSource={previewData?.content?.chunks?.map(chunk => chunk?.extData)}
          />}
        </div>
      </div>

    </Modal>
  </>
}


export default TableUploader;
