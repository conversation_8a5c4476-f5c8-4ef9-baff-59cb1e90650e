import FileUpload from "@/pages/app/workflow/react-node/file-uploader";
import { Form, Input } from "antd";
import { forwardRef, useEffect, useImperativeHandle } from "react";

interface IProps {
  value: any;
  onChange: (value: any) => void;
};

const UploadForm = forwardRef((props: IProps, ref) => {
  const { value, onChange } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(value);
  }, [value]);

  useImperativeHandle(ref, () => form);

  return <Form
    form={form}
    onValuesChange={(changedValues, values) => {
      if ('fileList' in changedValues) {
        const name = changedValues?.fileList?.[0]?.name;
        values.name = name;
        form.setFieldValue('name', name);
      }
      onChange?.(values);
    }}
  >
    <Form.Item name="fileList" rules={[{ required: true, message: '请上传文档' }]}>
      <FileUpload
        maxCount={1}
        value={value}
        onChange={onChange}
        description="上传一份Excel格式的文档，每个文件不超过20MB"
        accept=".xlsx"
        sizeLimit={20 * 1024}
        
      />
    </Form.Item>
    <Form.Item name="name" label="名称" rules={[{ required: true }]}>
      <Input />
    </Form.Item>
    <Form.Item name="description" label="描述">
      <Input />
    </Form.Item>
  </Form>
})

export default UploadForm;
