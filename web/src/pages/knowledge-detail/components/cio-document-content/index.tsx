import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import DynamicTable from '@/pages/knowledge-detail/components/dynamic-table';
import { KnowledgeModel } from '@/interface';
import { Link, useParams } from 'react-router-dom';
import { usePathQuery, useQuery } from '@hooks/useQuery';
import {
  Breadcrumb,
  Button,
  Card,
  Dropdown,
  Empty,
  Flex,
  Input,
  MenuProps,
  message,
  Popconfirm,
  Popover,
  Space,
  Tag,
  Tooltip,
} from 'antd';
import TableUploader from '@/pages/knowledge-detail/components/table-uploader';
import {
  DeleteOutlined,
  DownloadOutlined,
  DownOutlined,
  ExportOutlined,
  FileOutlined,
  FormOutlined,
  LeftOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { NewKnowledge } from '@/api';
import { LayoutContainer } from '@components/layout-container';
import EditKnowledgeModal from '@/pages/knowledge-detail/components/edit-knowledge-modal';
import styled from 'styled-components';
import { statusMap } from '@pages/new-knowledge/config';
import { getGroupId } from '@utils/state';
import ExportModal from '@/components/export-modal';

const PAGE_SIZE = 20;

interface Props {
  item: Record<string, any>;
  setRefreshSiderFlag: Dispatch<SetStateAction<boolean>>;
  onChangeStatus: (docId: string, status: 'waiting' | 'success' | 'fail') => void;
}

const Title = styled.div`
  font-size: 16px;
  font-weight: bold;
`;

const StyleLeftOutlined = styled(LeftOutlined)`
  margin-right: 10px;
  font-size: 16px;
  cursor: pointer;
  color: #8c8c8c;

  :hover {
    color: #1677ff;
  }
`;

const EllipsisText = styled.div`
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const CioDocumentContent: React.FC<Props> = ({
  item: document,
  setRefreshSiderFlag,
  onChangeStatus,
}) => {
  if (!document) return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;

  const [documentName, setDocumentName] = useState(''); // 为了修改时的backup
  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [curPage, setCurPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [deleting, setDeleting] = useState(false);
  const [renaming, setRenaming] = useState(false);
  const [renamePopOver, setRenamePopover] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);

  const params = useParams();
  const { dropQuery } = useQuery();

  useEffect(() => {
    setCurPage(1);
    setTotal(0);
    setData([]);
    setColumns([]);
    setDocumentName(document.name);
    fetchTable(1);
  }, [document]);

  // 获取表头表数据
  const fetchTable = async (pageNum: number) => {
    try {
      if (!params?.knowledgeId) return;
      if (!document.knowledgeItemId) return;
      if (pageNum - 1 > 0 && (pageNum - 1) * PAGE_SIZE >= total) return;
      setLoading(loading);
      const { knowledgeConfig, content } = await NewKnowledge.getDocumentDetail({
        knowledgeId: params.knowledgeId, // 知识库ID
        knowledgeItemId: document.knowledgeItemId, // 知识库 item ID
        pageNum,
        pageSize: PAGE_SIZE,
      });
      const formatColumns = JSON.parse(knowledgeConfig)?.headerFields?.map((i) => ({
        title: (
          <Flex>
            <Popover content={i.name} mouseEnterDelay={0.2}>
              <EllipsisText>{i.name}</EllipsisText>
            </Popover>
            <Tag bordered={false} style={{ marginLeft: 5, fontSize: 10 }}>
              {i.type}
            </Tag>
            {i.semantic && (
              <Tag
                bordered={false}
                color="success"
                style={{ marginLeft: 5, fontSize: 10 }}
              >
                语义理解字段
              </Tag>
            )}
          </Flex>
        ),
        dataIndex: i.code,
        key: i.code,
        width: 200,
        originData: i,
      }));
      setColumns(formatColumns);
      const semantic = JSON.parse(knowledgeConfig)?.headerFields.find((i) => i.semantic)
        ?.code;
      const formatData = content?.chunks?.map((item) => ({
        key: item.chunkId,
        order: item.order,
        ...item.extData,
        ...(semantic ? { [semantic]: item.text } : {}),
      }));
      setData((prev) => [...prev, ...formatData]);
      setCurPage((prev) => prev + 1);
      setTotal(content.total);
    } catch (err: any) {
      message.error(`获取数据失败, ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleChangeName = async () => {
    try {
      const trimmedName = documentName.trim();
      // 如果输入为空，提示用户
      if (!trimmedName) {
        message.warning('文档名称不能为空');
        return;
      }
      if (trimmedName === document.name) {
        setRenamePopover(false);
        return;
      }
      setRenaming(true);
      await NewKnowledge.updateDocumentInfo({
        ...document,
        name: trimmedName,
      });
      setRefreshSiderFlag((prev) => !prev);
      setRenamePopover(false);
      message.success('重命名成功');
    } catch (err: any) {
      message.error(`重命名失败, ${err.message}`);
    } finally {
      setRenaming(false);
    }
  };

  const handleDeleteDocument = async () => {
    if (!document.knowledgeItemId) return;
    try {
      setDeleting(true);
      await NewKnowledge.deleteDocument({
        knowledgeItemId: document.knowledgeItemId,
      });
      dropQuery(['docId']);
      setRefreshSiderFlag((prev) => !prev);
      message.success('删除成功');
    } catch (err: any) {
      message.error(`删除失败, ${err.message}`);
    } finally {
      setDeleting(false);
    }
  };

  const exportKnowledge = async (exportParams?: { pageSize: number; totalPages: number }) => {
    const res = await NewKnowledge.exportKnowledge({
      knowledgeId: params.knowledgeId, // 知识库ID
      knowledgeItemId: document.knowledgeItemId,
      type: 'cio',
      pageSize: exportParams?.pageSize,
      pageNumber: exportParams?.totalPages
    });
    if (res?.url) {
      window.open(res?.url, '_blank');
    }
    setExportModalVisible(false);
  };

  const handleExport = () => {
    setExportModalVisible(true);
  };

  const content = (
    <Flex vertical align="end">
      <Input.TextArea
        style={{ width: 300 }}
        autoSize={{ minRows: 3, maxRows: 10 }}
        value={documentName}
        onChange={(e) => setDocumentName(e.target.value)}
      />

      <Button
        type="primary"
        size="small"
        style={{ marginTop: 10 }}
        loading={renaming}
        onClick={handleChangeName}
      >
        保存
      </Button>
    </Flex>
  );

  return (
    <>
      <Flex align="center" justify="space-between">
        <Flex align="center">
          <Title>{document.name || '未命名文档'}</Title>

            <Popover
              title="文档重命名"
              content={content}
              trigger="click"
              placement="bottom"
              open={renamePopOver}
              onOpenChange={setRenamePopover}
            >
              <Button type="link" style={{ padding: 0, marginLeft: 8 }}>
                <FormOutlined />
              </Button>
            </Popover>
            <Tooltip
              title="下载表格"
            >
              <Button
                type="link"
                style={{ padding: 0, marginLeft: 8 }}
                onClick={handleExport}
              >
                <DownloadOutlined />
              </Button>
            </Tooltip>
          </Flex>

          <Flex align="center">

            <Popconfirm
              title="确认删除"
              description="该操作不可撤销"
              okType="danger"
              placement="bottomLeft"
              onConfirm={handleDeleteDocument}
              icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
            >
              <Button
                type="link"
                style={{ padding: 0, marginLeft: 8 }}
                loading={deleting}
              >
                <DeleteOutlined />
              </Button>
            </Popconfirm>
          </Flex>
        </Flex >

      <ExportModal
        open={exportModalVisible}
        onOk={exportKnowledge}
        onCancel={() => setExportModalVisible(false)}
        total={total}
      />
      <DynamicTable
        loading={loading}
        columns={columns}
        dataSource={data}
        loadMore={() => fetchTable(curPage)}
      />
    </>
  );
};

export default CioDocumentContent;
