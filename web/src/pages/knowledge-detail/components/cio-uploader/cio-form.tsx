import { Form, Select } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { CioApi } from '@/api/cio';
import { useRequest } from 'ahooks';
import { NewKnowledge } from "@/api";

const getBusiness = async () => {
  // const resources = await request({
  //   url: `${base}/api/content/pool/business/resourcetype/all/get`,
  // });
  const resources = await CioApi.getBusinessResourceTypes();
  return resources?.config || [];
};

const getPool = async (values) => {
  // const { business, resourceType } = values;
  // const pools = await request({
  //   url: `${base}/api/content/pool/list?business=${business}&resourceType=${resourceType}&limit=10000`,
  // });
  const pools = await CioApi.getPools(values);
  return (pools || []).map((v) => ({
    label: v?.name,
    value: v?.poolCode,
  }));
};

interface IProps {
  value: {
    business?: string;
    resourceType?: string;
    poolCode?: string;
    deliveryCode?: string;
  };
  onChange?: (value: any) => void;
  deliveryList?: any[];
}

const CioForm = forwardRef((props: IProps, ref) => {
  const { value, deliveryList } = props;
  const { data: businessOptions, loading } = useRequest(getBusiness);
  const [resOption, setResOptions] = useState([]);
  const [poolOptions, setPoolOptions] = useState([]);
  const [deliveryOptions, setDeliveryOptions] = useState([]);
  const [form] = Form.useForm();

  const [poolLoading, setPoolLoading] = useState(false);
  const [deliveryCodeLoading, setDeliveryCodeLoading] = useState(false);

  useEffect(() => {
    form.setFieldsValue(value);
  }, [value]);

  useEffect(() => {
    if (!loading) {
      onBusinessChange({
        business: value?.business,
        resourceType: value?.resourceType,
        poolCode: value?.poolCode,
        deliveryCode: value?.deliveryCode
      });
    }
  }, [loading]);

  useImperativeHandle(ref, () => {
    return {
      ...form,
      validateFields: async () => {
        const res = await form.validateFields();
        const current = deliveryOptions?.find(item => item.value === res.deliveryCode);
        return {
          ...(res || {}),
          properties: current.outputPropertyList,
          topic: current.topic,
          consumerGroup: current.consumerGroup,
          name: current.label
        }
      }
    }
  });
  // // 业务线变动的时候资源类型也变动
  // useEffect(() => {
  //   const obj = {};
  //   if (value && businessOptions) {
  //     (value?.configs || []).forEach((v) => {
  //       obj[v.name] = v.value;
  //     });
  //     form.setFieldsValue(obj);
  //     const outputs = value.configs.find(v => v.name === 'outputs')?.value || null;
  //     if (outputs) {
  //       // setChecks(outputs.map((v) => v.name));
  //     }
  //     getOptions(obj);
  //   }
  // }, [businessOptions]);

  const getOptions = async (values) => {
    const defaultValue = { ...values };

    // 业务线修改的时候要重置资源类型数据源
    if (defaultValue.business) {
      const find = businessOptions?.find((v) => v.value === values.business);
      if (find) {
        setResOptions(find.children);
        defaultValue.resourceType = defaultValue.resourceType || find.children[0].value;
      }
    }

    // 业务线和资源类型改动的时候，要重新请求资源池
    if (defaultValue.resourceType && defaultValue.business) {
      setPoolLoading(true);
      try {
        const pools = await getPool(defaultValue);
        setPoolOptions(pools);
        defaultValue.poolCode = defaultValue.poolCode || pools[0].value;
        setPoolLoading(false);
      } catch (error) {
        setPoolLoading(false);
      }
    }

    // 获取投放池
    if (defaultValue.resourceType && defaultValue.business && defaultValue.poolCode) {
      setDeliveryCodeLoading(true);
      try {
        const res = await NewKnowledge.getDeliveryList(defaultValue);
        setDeliveryOptions(res?.map(item => {
          return {
            value: item.code,
            label: item.name,
            outputPropertyList: item.outputPropertyList,
            topic: item.topic,
            consumerGroup: item.consumerGroup,
          }
        }));
        defaultValue.deliveryCode = defaultValue.deliveryCode || res?.[0]?.code;
        setDeliveryCodeLoading(false);
      } catch (error) {
        setDeliveryCodeLoading(false);
      }
    }
    return defaultValue;
  };

  const onBusinessChange = async defaultValue => {
    // 业务线修改的时候要重置资源类型数据源
    if (defaultValue.business) {
      const find = businessOptions?.find((v) => v.value === defaultValue.business);
      if (find) {
        setResOptions(find.children);
        defaultValue.resourceType = defaultValue.resourceType || find.children[0].value;
      }
    }
    const val = await onResourceTypeChange(defaultValue);
    return val;
  };

  const onResourceTypeChange = async defaultValue => {
    // 业务线和资源类型改动的时候，要重新请求资源池
    if (defaultValue.resourceType && defaultValue.business) {
      setPoolLoading(true);
      try {
        const pools = await getPool(defaultValue);
        setPoolOptions(pools);
        defaultValue.poolCode = defaultValue.poolCode || pools[0].value;
        setPoolLoading(false);
      } catch (error) {
        setPoolLoading(false);
      }
    }
    const val = await onPoolCodeChange(defaultValue);
    return val
  };

  const onPoolCodeChange = async (defaultValue) => {
    // 获取投放池
    if (defaultValue.resourceType && defaultValue.business && defaultValue.poolCode) {
      setDeliveryCodeLoading(true);
      try {
        const res = await NewKnowledge.getDeliveryList(defaultValue);
        const used = deliveryList?.filter(item => item.business === defaultValue.business && item.resourceType === defaultValue.resourceType && item.poolCode === defaultValue.poolCode);
        setDeliveryOptions(res?.map(item => {
          // 同一个 business resourceTypes poolCode deliverCode是唯一键

          const sameDeliveryCode = used?.find(it => it.deliveryCode === item.code);
          return {
            value: item.code,
            label: item.name,
            outputPropertyList: item.outputPropertyList,
            topic: item.topic,
            consumerGroup: item.consumerGroup,
            disabled: !!sameDeliveryCode
          }
        }));
        // defaultValue.deliveryCode = defaultValue.deliveryCode || res?.[0]?.code;
        setDeliveryCodeLoading(false);
      } catch (error) {
        setDeliveryCodeLoading(false);
      }
    }
    return defaultValue;

  }

  const onValuesChange = async (changedValue, values) => {
    let defaultValue = { ...values };
    console.log('changedValue', changedValue, 'values', values);
    if ('business' in changedValue) {
      defaultValue = await onBusinessChange({
        business: values.business,
      });
    }
    if ('resourceType' in changedValue) {
      defaultValue = await onResourceTypeChange({
        business: values?.business,
        resourceType: values?.resourceType,
      });
    }
    if ('poolCode' in changedValue) {
      defaultValue = await onPoolCodeChange({
        business: values?.business,
        resourceType: values?.resourceType,
        poolCode: values?.poolCode
      });
    }

    form.setFieldsValue({
      business: undefined,
      resourceType: undefined,
      poolCode: undefined,
      deliveryCode: undefined,
      ...(defaultValue || {})
    });
  };

  return <Form
    form={form}
    // onFieldsChange={debounce(onFieldsChange, 100)}
    onValuesChange={onValuesChange}
    labelCol={{ span: 7 }}
    wrapperCol={{ span: 12 }}
  >
    <Form.Item label="业务线" name="business" rules={[{ required: true }]}>
      <Select options={businessOptions} loading={loading} />
    </Form.Item>
    <Form.Item label="资源类型" name="resourceType" rules={[{ required: true }]}>
      <Select options={resOption} loading={loading} />
    </Form.Item>
    <Form.Item label="资源池" name="poolCode" rules={[{ required: true }]}>
      <Select options={poolOptions} loading={poolLoading} />
    </Form.Item>
    <Form.Item label="投放池" name="deliveryCode" rules={[{ required: true }]}>
      <Select options={deliveryOptions} loading={deliveryCodeLoading} allowClear />
    </Form.Item>
  </Form>
})

export default CioForm;
