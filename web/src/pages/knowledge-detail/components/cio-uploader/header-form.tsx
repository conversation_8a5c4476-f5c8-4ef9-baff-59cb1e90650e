import { Table } from "antd";

const HeaderForm = () => {
  return <Table
  rowSelection={{
    type:'radio',
    columnWidth: 48
  }}
    columns={[
      { title: '语义理解字段', dataIndex: '', },
      { title: '列名', dataIndex: 'headName', },
      { title: '索引', dataIndex: 'dataIndex', },
      { title: '描述', dataIndex: '', },
      { title: '数据类型', dataIndex: 'headType', },

    ]}
  />
}


export default HeaderForm;
