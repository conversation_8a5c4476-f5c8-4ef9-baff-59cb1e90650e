import { Button, message, Modal, Space, Steps } from "antd";
import { useRef, useState } from "react";
import CioForm from "./cio-form";
import { NewKnowledge } from "@/api";
import EditForm from "../table-uploader/edit-form";

const CioUploader = (props) => {
  const { knowledgeId, workspaceId, groupId, onChange, trigger } = props;
  const [open, setOpen] = useState(false);
  const [step, setStep] = useState(0);
  const [sheets, setSheets] = useState<any[]>();

  const [cioValue, setCioValue] = useState<any>();
  const [dataTypes, setDataTypes] = useState();
  const [savedHeads, setSavedHeads] = useState();

  const [selectLoading, setSelectLoading] = useState(false);
  const [editDisable, setEditDisable] = useState(true);
  const [editLoading, setEditLoading] = useState(false);

  const [headerConfig, setHeaderConfig] = useState({
    sheetIndex: 0,
    headers: undefined,
  });

  // 已经存在的cio投放池
  const [deliveryList, setDeliveryList] = useState();

  const editForm = useRef(null);
  const cioForm = useRef(null);

  const reset = () => {
    setStep(0);
    setCioValue(undefined);
    setHeaderConfig({ sheetIndex: 0, headers: undefined });
    setSheets(undefined);
    setDataTypes(undefined);
    setSavedHeads(undefined);
  };

  const onOpen = async () => {
    // 同一个 business resourceTypes poolCode deliverCode是唯一键
    const res = await NewKnowledge.getKnowledgeItemList({ knowledgeId });
    if (res.debugIngo) {
      message.error(res.debugInfo);
    }
    setDeliveryList(res?.map(item => {
      return item.config?.deliveryConfig
    }));
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
    reset();
  }

  const goNext = async () => {
    // 投放池选择
    if (step === 0) {
      // 获取cio表头
      const cioFormValue = await cioForm.current?.validateFields();

      setCioValue(cioFormValue);
      try {
        setSelectLoading(true);

        const res = await NewKnowledge.getCioHeader({
          knowledgeId,
          groupId,
          properties: cioFormValue?.properties
        });
        if (res.debugInfo) {
          message.error(res.debugInfo);
          return;
        }
        const dataTypesOptions: any = Object.keys(res?.optionalType || {})?.map(type => {
          return {
            label: type,
            value: type,
            count: res?.optionalType?.[type]
          }
        });

        // 有saveHeads说明不是第一次上传
        if (res.savedHeads?.length > 0) {
          setEditDisable(true);
          setSavedHeads(res.savedHeads);
        } else {
          // 第一次上传，没有saveHeads
          setEditDisable(false);
        }
        setSheets(res?.values?.map((item, index) => {
          return {
            label: item.sheetNames || cioValue?.deliveryCode,
            value: index,
            heads: item.heads
          }
        }));
        setHeaderConfig({
          sheetIndex: 0,
          headers: res?.values?.[0]?.heads?.map(item => {
            return {
              ...item,
              checked: !item.disabled,
            }
          })
        });
        setSelectLoading(false);

        setDataTypes(res?.optionalType);
        setStep(step + 1);

      } catch (error: any) {
        message.error(error?.message)
        setSelectLoading(false);
      }
    }
  };

  const goPre = () => {
    setStep(step - 1);
  };

  // 提交投放池
  const onSubmit = async () => {
    // 保存修改后的表头

    const editValue = await editForm.current?.validateFields();
    setHeaderConfig({
      sheetIndex: editValue.sheetIndex,
      headers: editValue.headers
    });
    try {
      setEditLoading(true);
      const { name, properties, ...otherConfigs } = cioValue;
      const res = await NewKnowledge.addDoc({
        knowledgeId,
        groupId,
        workspaceId,
        knowledgeItemDetailRequestList: [{
          name: cioValue?.name,
          type: 'cio',
          config: JSON.stringify({ deliveryConfig: otherConfigs }),
          importType: 'remote',
        }],
        propertyList: editValue.headers?.filter(item => item.checked),
      });
      if (res.debugInfo) {
        throw new Error(res.debugInfo);
      }
      onChange?.();
      setEditLoading(false);
      message.success('添加投放池成功');
      onClose();
    } catch (error: any) {
      message.error(error?.message);
      setEditLoading(false);
    }
  };

  return <>
    <span onClick={onOpen}>{trigger ?? <Button >上传</Button>}</span>
    <Modal
      open={open}
      title="CIO上传"
      onCancel={onClose}
      width={880}
      destroyOnClose
      footer={<>
        {/* 投放池选择 */}
        {step === 0 &&
          <Button type="primary"
            loading={selectLoading}
            onClick={() => goNext()}>
            下一步</Button>
        }
        {/* 表头设置 */}
        {step === 1 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={onSubmit} disabled={editDisable} loading={editLoading}>完成</Button>
        </Space>
        }
        {/* 配置消息队列 */}
        {/* {step === 2 && <Space>
          <Button onClick={goPre} >上一步</Button>
          <Button type="primary" onClick={onSubmit}>提交</Button>
        </Space>} */}
      </>}
    >
      <div style={{ marginBottom: '30px' }}>
        <Steps
          current={step}
          items={[
            { title: '投放池选择' },
            { title: '表头设置' },
          ]}
        />
      </div>
      <div>
        {step === 0 && <CioForm ref={cioForm} value={cioValue} deliveryList={deliveryList}/>}
        {step === 1 && <EditForm
          sheets={sheets}
          ref={editForm}
          dataTypes={dataTypes}
          value={headerConfig}
          savedHeads={savedHeads}
          onHeadsEqualChange={equal => {
            setEditDisable(!equal)
          }}
          editableRows={['name', 'desc']}
          type="cio"
          headsEqual={(current, original) => {
            // code name type 都一致就可以算同一个表头
            if (current?.length !== original?.length) return false;
            for (let i = 0; i < current.length; i++) {
              const curItem = current?.[i];
              const find = original?.find(item => item.name === curItem?.name && item.code === curItem.code && item.type === curItem.type);
              if (!find) {
                return false
              }
            }
            return true;
          }}
        />}
      </div>
    </Modal>

  </>
}


export default CioUploader;
