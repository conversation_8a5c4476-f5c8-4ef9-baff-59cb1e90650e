import React from 'react';
import { Empty, Flex, List, Popover, Tag } from 'antd';
import styled from 'styled-components';
import { DocumentListItemModal, KnowledgeType } from '@/interface';
import VirtualList from 'rc-virtual-list';
import { statusMap } from '@/pages/new-knowledge/config';
import { getFileType } from '@/pages/new-knowledge/utils';

interface DocumentsSliderProps {
  type: KnowledgeType;
  list: DocumentListItemModal[];
  activeItem: DocumentListItemModal;
  onItemClick: (item: any) => void;
  onScroll: () => void;
  loading: boolean;
}

interface DocumentItemProps {
  type: KnowledgeType;
  item: DocumentListItemModal;
  activeItem: DocumentListItemModal;
}

const SiderItem = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding-left: 5px;
  cursor: pointer;

  &:hover {
    color: #1677ff;
  }
`;

const StyleList = styled(List)`
  margin-top: 10px;
  overflow: scroll;
  border-top: 1px solid #efefef;
`;

const DocumentItem: React.FC<DocumentItemProps> = ({ type, item, activeItem }) => {
  const iconType = getFileType({ name: item?.name });
  const statusConf = statusMap[item.status];

  return (
    <Popover content={item?.name}>
      <SiderItem>
        {/*<span style={{ fontSize: '20px' }}>{textIconMap[iconType]}</span>*/}
        <Flex
          justify="space-between"
          style={{
            marginLeft: 5,
            fontWeight:
              item.knowledgeItemId === activeItem?.knowledgeItemId ? 'bold' : 'normal',
            color:
              item.knowledgeItemId === activeItem?.knowledgeItemId ? '#1677ff' : '#000',
            width: '100%',
          }}
        >
          <div
            style={{
              width: 100,
              flex: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {item?.name}
          </div>
          {item.status && item.status !== 'success' && statusConf?.label && (
            <Tag
              bordered={false}
              color={statusConf?.color}
              style={{ fontWeight: 'normal', margin: 0 }}
            >
              {statusConf.label}
            </Tag>
          )}
        </Flex>
      </SiderItem>
    </Popover>
  );
};

const DocumentsSlider: React.FC<DocumentsSliderProps> = ({
  type,
  list,
  activeItem,
  onScroll,
  onItemClick,
  loading,
}) => {
  const containerHeight = window.innerHeight - 270;

  const handleScroll = (e) => {
    if (
      Math.abs(
        e.currentTarget.scrollHeight - e.currentTarget.scrollTop - containerHeight,
      ) <= 1
    ) {
      onScroll();
    }
  };

  return (
    <>
      {list.length ? (
        <StyleList bordered={false} loading={loading}>
          <VirtualList
            data={list}
            itemKey="knowledgeItemId"
            onScroll={handleScroll}
            height={containerHeight}
          >
            {(item: DocumentListItemModal) => (
              <List.Item onClick={() => onItemClick(item)}>
                <DocumentItem type={type} item={item} activeItem={activeItem} />
              </List.Item>
            )}
          </VirtualList>
        </StyleList>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </>
  );
};

export default DocumentsSlider;
