import React, { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Form, Input, message, Modal, Radio } from 'antd';
import { NewKnowledge } from '@/api';
import { useQuery } from '@hooks/useQuery';
import styled from 'styled-components';
import { knowledgeTypeSchema } from '@/pages/new-knowledge/config';
import { KnowledgeModel } from '@/interface';
import { useParams } from 'react-router-dom';

interface EditKnowledgeModalProps {
  item: KnowledgeModel;
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  setRefreshFlag: Dispatch<SetStateAction<boolean>>;
}

const FlexRadioButton = styled(Radio.Button)`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 25%;
  height: 80px;
`;

const ButtonContent = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const EditKnowledgeModal: React.FC<EditKnowledgeModalProps> = ({
  item,
  open,
  setOpen,
  setRefreshFlag,
}) => {
  const [isEditing, setIsEditing] = useState(false);

  const [form] = Form.useForm();
  const { parsedQuery, updateQuery } = useQuery();
  const params = useParams();

  useEffect(() => {
    if (!open) return;
    form.setFieldsValue({
      type: 'text',
      ...item,
    });
  }, [item, open]);

  const editKnowledge = async () => {
    try {
      if (!parsedQuery.workspaceId) return;
      const values = await form.validateFields();
      setIsEditing(true);
      await NewKnowledge.updateKnowledge({
        ...values,
        id: params.knowledgeId,
      });
      updateQuery('name', values.name);
      updateQuery('description', values.description, true);
      setOpen(false);
      setRefreshFlag((prev) => !prev);
    } catch (err: any) {
      if (err?.response?.data?.message?.includes('存在重名知识库')) {
        message.error('修改失败, 存在重名知识库');
      } else {
        message.error(`修改失败, ${err.response.data.message}`);
      }
    } finally {
      setIsEditing(false);
    }
  };

  return (
    <Modal
      title="编辑知识库"
      open={open}
      width={600}
      onCancel={() => setOpen(false)}
      onOk={editKnowledge}
      okButtonProps={{ loading: isEditing }}
    >
      <Form
        form={form}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        style={{ padding: '24px 12px' }}
      >
        <Form.Item name="type" label="类型" rules={[{ required: true }]}>
          <Radio.Group
            disabled
            defaultValue={knowledgeTypeSchema[1].key}
            style={{ display: 'flex' }}
          >
            {knowledgeTypeSchema
              .filter((i) => i.key !== 'all')
              .map((item) => (
                <FlexRadioButton key={item.key} value={item.key} disabled>
                  <ButtonContent>
                    <div style={{ fontSize: 24 }}>{item.icon}</div>
                    {item.label}
                  </ButtonContent>
                </FlexRadioButton>
              ))}
          </Radio.Group>
        </Form.Item>

        <Form.Item name="name" label="名称" rules={[{ required: true }]}>
          <Input placeholder="请输入" maxLength={128} showCount />
        </Form.Item>

        <Form.Item name="description" label="描述">
          <Input.TextArea placeholder="请输入" rows={4} maxLength={256} showCount />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditKnowledgeModal;
