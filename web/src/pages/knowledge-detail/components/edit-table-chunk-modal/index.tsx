import React, { useState, useEffect } from 'react';
import { Modal, Table, Input, Tag, message } from 'antd';
import { lowerCase } from 'lodash';

interface Props {
  open: boolean;
  item: Record<string, any>;
  columns: Record<string, any>[];
  onOk: (formattedData: Record<string, any>) => void;
  onCancel: () => void;
}

const EditTableChunkModal: React.FC<Props> = ({
  open,
  item,
  columns,
  onOk,
  onCancel,
}) => {
  if (!item) return null;

  const [editedItem, setEditedItem] = useState<Record<string, any>>({}); // 编辑后的数据

  useEffect(() => {
    if (item) setEditedItem({ ...item });
  }, [item]);

  const shownColumns = [
    {
      title: '字段名',
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (text: string) => <Tag bordered={false}>{text}</Tag>,
    },
    {
      title: '字段值',
      dataIndex: 'value',
      key: 'value',
      render: (text: any, record: any) => (
        <Input.TextArea
          autoSize
          value={text}
          onChange={(e) => handleValueChange(record.fieldKey, e.target.value)}
        />
      ),
    },
  ];

  const dataSource = columns.map((i) => ({
    key: i.originData.name,
    type: i.originData.type,
    value: editedItem[i.key] !== undefined ? editedItem[i.key] : item[i.key],
    fieldKey: i.key,
  }));

  // 处理输入框值变化
  const handleValueChange = (key: string, value: any) => {
    const newItem = { ...editedItem, [key]: value };
    setEditedItem(newItem);
  };

  // 确认按钮处理函数
  const handleOk = () => {
    const formattedData: Record<string, any> = {};
    for (const column of columns) {
      const fieldKey = column.key;
      const fieldType = lowerCase(column.originData.type);
      const fieldValue =
        editedItem[fieldKey] !== undefined ? editedItem[fieldKey] : item[fieldKey];
      if (['integer', 'double'].includes(fieldType)) {
        if (isNaN(fieldValue)) {
          message.error(`保存失败, ${fieldType}类型必须为纯数字`);
          return;
        } else {
          formattedData[fieldKey] = Number(fieldValue);
        }
      } else {
        formattedData[fieldKey] = fieldValue;
      }
    }
    const finalData = {
      ...formattedData,
      key: item.key,
      order: item.order,
    };
    onOk(finalData);
  };

  return (
    <Modal
      title={`#${item?.index + 1 || '编辑'}`}
      open={open}
      onOk={handleOk}
      onCancel={onCancel}
      destroyOnClose
    >
      <Table
        columns={shownColumns}
        dataSource={dataSource}
        pagination={false}
        bordered={false}
        rowKey="key"
      />
    </Modal>
  );
};

export default EditTableChunkModal;
