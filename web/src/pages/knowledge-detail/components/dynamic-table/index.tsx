import React, { useContext, useEffect, useRef, useState } from 'react';
import { Flex, FormInstance, InputRef, message, Modal, TableProps } from 'antd';
import { Button, Form, Input, Table } from 'antd';
import './index.less';
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { debounce, lowerCase } from 'lodash';
import EditTableChunkModal from '@/pages/knowledge-detail/components/edit-table-chunk-modal';

interface EditableRowProps {
  index: number;
}

interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  dataIndex: string;
  record: Record<string, any>;
  onUpdate: (chunkId: string, record: Record<string, any>) => void;
  children: React.ReactNode;
  columns: Record<string, any>[];
}

interface DynamicTableProps {
  actions?: ('delete' | 'edit' | 'add' | 'select')[];
  loading: boolean;
  columns: Record<string, any>[];
  dataSource: Record<string, any>[];
  loadMore: () => Promise<void>;
  onAdd?: () => void;
  onDelete?: (ids: string[]) => Promise<void>;
  onUpdate?: (chunkId: string, data: any) => Promise<void>;
  showSerial?: boolean;
}

type ColumnTypes = Exclude<TableProps<Record<string, any>>['columns'], undefined>;

const EditableContext = React.createContext<FormInstance | null>(null);

const DynamicTableContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  overflow: hidden; /* 防止双滚动条 */
`;

const TableWrapper = styled.div`
  flex: 1;
  overflow: auto;
`;

const FooterContainer = styled.div`
  padding: 8px 16px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 10;
`;

const StyleTable = styled(Table)`
  .ant-table-container {
    border-bottom: none;
  }

  .ant-table-cell {
    background-color: #fff !important;
  }

  /* 移除原有的footer */

  .ant-table-footer {
    display: none;
  }
`;

const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

const EditableCell: React.FC<EditableCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  onUpdate,
  columns,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false);
  const [updating, setUpdating] = useState(false);

  const inputRef = useRef<InputRef>(null);
  const form = useContext(EditableContext)!;

  useEffect(() => {
    if (editing) {
      inputRef.current?.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
    form.setFieldsValue({ [dataIndex]: record[dataIndex] });
  };

  const handleUpdate = async () => {
    try {
      if (updating) return;
      setUpdating(true);
      const values = await form.validateFields();
      const curType = columns.find((i) => i.dataIndex === dataIndex)?.originData?.type;
      const curValue: any = Object.values(values)?.[0];
      toggleEdit();
      if (['integer', 'double'].includes(lowerCase(curType))) {
        if (isNaN(curValue)) {
          message.error(`保存失败, ${curType}类型必须为纯数字`);
          return;
        } else {
          values[dataIndex] = Number(curValue);
        }
      }
      onUpdate(record.key, { ...record, ...values });
    } catch (err: any) {
      message.error(`保存失败, ${err.message}`);
    } finally {
      setUpdating(false);
    }
  };

  let childNode = children;

  if (editable) {
    childNode = editing ? (
      <Form.Item style={{ margin: 0 }} name={dataIndex}>
        <Input.TextArea
          style={{ position: 'absolute', top: 0, zIndex: 999 }}
          autoSize
          ref={inputRef}
          onBlur={handleUpdate}
        />
      </Form.Item>
    ) : (
      <div className="editable-cell-value-wrap" onClick={toggleEdit}>
        {children}
      </div>
    );
  }

  return (
    <td {...restProps} className={editable ? 'editable-cell' : ''}>
      {childNode}
    </td>
  );
};

const DynamicTable: React.FC<DynamicTableProps> = ({
  actions = [],
  loading,
  columns = [],
  dataSource,
  loadMore,
  onAdd,
  onDelete,
  onUpdate,
  showSerial = true,
}) => {
  const [select, setSelect] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [editRow, setEditRow] = useState<Record<string, any>>();
  const [editChunkModalOpen, setEditChunkModalOpen] = useState(false);
  const [containerHeight, setContainerHeight] = useState(window.innerHeight);

  const lastScrollLeftRef = useRef(0);

  const selectable = actions.includes('select');
  const editable = actions.includes('edit');
  const deletable = actions.includes('delete');
  const addable = actions.includes('add');

  const defaultColumns: Record<string, any>[] = showSerial
    ? [
        {
          title: '',
          dataIndex: 'index',
          align: 'center',
          width: 50,
          render: (_, __, index) => <span style={{ color: '#999' }}>{index + 1}</span>,
        },
        ...columns.map((i) => ({
          ...i,
          editable,
        })),
      ]
    : columns.map((i) => ({
        ...i,
        editable,
      }));

  const finalColumns = defaultColumns.map((col, index) => {
    if (!col.editable) return col;
    return {
      ...col,
      index,
      onCell: (record: Record<string, any>) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        chunkId: col.chunkId,
        onUpdate: handleUpdate,
        columns,
      }),
    };
  });

  useEffect(() => {
    const handleResize = () => {
      setContainerHeight(window.innerHeight);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleUpdate = async (chunkId: string, updatedRecord: Record<string, any>) => {
    await onUpdate?.(chunkId, updatedRecord);
    setSelect((prevSelect) =>
      prevSelect.includes(chunkId)
        ? prevSelect.map((item) => (item === chunkId ? updatedRecord.key : item))
        : prevSelect,
    );
    setEditRow((prevEditRow) =>
      prevEditRow?.key === chunkId ? { ...prevEditRow, ...updatedRecord } : prevEditRow,
    );
  };

  const onTableScroll = debounce((e) => {
    const { scrollTop, scrollHeight, clientHeight, scrollLeft } = e.target;
    const isHorizontalScroll = scrollLeft !== lastScrollLeftRef.current;
    lastScrollLeftRef.current = scrollLeft;
    if (isHorizontalScroll) return;
    if (scrollTop >= scrollHeight - clientHeight - 10) {
      loadMore();
    }
  }, 200);

  const handleDelete = async () => {
    try {
      setDeleting(true);
      await onDelete(select);
      setSelect([]);
      setShowDeleteModal(false);
    } catch (err: any) {
      message.error(err.message);
    } finally {
      setDeleting(false);
    }
  };

  const renderFooter = () => (
    <Flex justify="space-between">
      {addable && (
        <Button
          type="primary"
          size="small"
          icon={<PlusOutlined />}
          onClick={onAdd}
          disabled={!columns.length}
        >
          新增一行
        </Button>
      )}

      <Flex>
        {selectable && !!select.length && (
          <div style={{ color: '#999' }}>已选中 {select.length} 行</div>
        )}

        {editable && (
          <Button
            style={{ margin: '0 10px' }}
            disabled={select.length !== 1}
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => setEditChunkModalOpen(true)}
          >
            编辑
          </Button>
        )}

        {deletable && (
          <Button
            disabled={!select.length}
            type="primary"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => setShowDeleteModal(true)}
          >
            删除
          </Button>
        )}
      </Flex>
    </Flex>
  );

  return (
    <DynamicTableContainer style={{ height: containerHeight - 290 }}>
      <TableWrapper onScroll={onTableScroll}>
        <StyleTable
          bordered={false}
          pagination={false}
          components={{
            body: {
              row: EditableRow,
              cell: EditableCell,
            },
          }}
          rowSelection={
            selectable
              ? {
                  hideSelectAll: true,
                  type: 'checkbox',
                  onChange: (_, selectedRows: any[]) => {
                    setSelect(selectedRows.map((i) => i.key));
                    if (selectedRows.length === 1)
                      setEditRow({
                        ...selectedRows?.[0],
                        index: dataSource
                          .map((i) => i.key)
                          .indexOf(selectedRows?.[0]?.key),
                      });
                    else setEditRow(null);
                  },
                }
              : null
          }
          rowClassName={() => 'editable-row'}
          columns={finalColumns as ColumnTypes}
          dataSource={dataSource}
          loading={loading}
          scroll={{ x: '100%' }}
        />
      </TableWrapper>

      <FooterContainer>{renderFooter()}</FooterContainer>

      {/* Modal组件不变 */}
      <Modal
        centered
        title="确认删除"
        width={400}
        open={showDeleteModal}
        onOk={handleDelete}
        okType="danger"
        confirmLoading={deleting}
        onCancel={() => setShowDeleteModal(false)}
      >
        <div>此操作不可撤回</div>
      </Modal>

      <EditTableChunkModal
        item={editRow}
        open={editChunkModalOpen}
        columns={columns}
        onOk={(value) => {
          onUpdate(editRow.key, value);
          setEditChunkModalOpen(false);
        }}
        onCancel={() => setEditChunkModalOpen(false)}
      />
    </DynamicTableContainer>
  );
};

export default DynamicTable;
