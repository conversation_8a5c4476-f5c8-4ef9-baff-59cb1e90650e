/* 编辑单元格的基本样式 */
.editable-cell {
  position: relative;
}

/* 可编辑单元格的值区域样式 */
.editable-cell-value-wrap {
  height: 22px;
  padding: 5px 12px;
  cursor: pointer;
  white-space: nowrap; // 确保文本不会换行
  overflow: hidden; // 隐藏超出容器的内容
  text-overflow: ellipsis; // 超出部分用省略号表示
}

/* 行悬停时的背景颜色 */
.editable-row:hover {
  background-color: #F2F3F7;
}

/* 只有当单元格悬停时，才显示边框，而不是整行悬停 */
.editable-cell:hover .editable-cell-value-wrap {
  padding: 4px 11px;
  border-radius: 4px;
  border: 1px solid #E6E8F0;
  background-color: #E6E8F0;
}

/* 确保行悬停时单元格边框不显示 */
.editable-row:hover .editable-cell .editable-cell-value-wrap {
  padding: 5px 12px;
  border: none;
}

/* 确保行悬停且单元格同时悬停时边框显示 */
.editable-row:hover .editable-cell:hover .editable-cell-value-wrap {
  padding: 4px 11px;
  border-radius: 4px;
  border: 1px solid #E6E8F0;
  background-color: #E6E8F0;
}
