import React, { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { NewKnowledge } from '@/api';
import { useParams } from 'react-router-dom';
import { Button, Empty, Flex, Input, message, Popconfirm, Popover, Tag } from 'antd';
import { DeleteOutlined, FormOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import {
  documentChunkStrategyMap,
  documentImportTypeMap,
  statusMap,
} from '@/pages/new-knowledge/config';
import { nos2url } from '@/pages/preview/type-map';
import DocPreview from '@/pages/new-knowledge/components/text-uploader/doc-preview';
import { useQuery } from '@hooks/useQuery';

interface TextDocumentProps {
  item: Record<string, any>;
  setRefreshSiderFlag: Dispatch<SetStateAction<boolean>>;
  onChangeStatus: (docId: string, status: 'waiting' | 'success' | 'fail') => void;
}

const Title = styled.div`
  font-size: 16px;
  font-weight: bold;
`;

const TextDocumentContent: React.FC<TextDocumentProps> = ({
  item: document,
  setRefreshSiderFlag,
  onChangeStatus,
}) => {
  if (!document) return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />;

  const [documentName, setDocumentName] = useState(''); // 为了修改时的backup
  const [importType, setImportType] = useState();
  const [chunkStrategy, setChunkStrategy] = useState();
  const [downloadUrl, setDownloadUrl] = useState('');
  const [docLink, setDocLink] = useState();
  const [statusConf, setStatusConf] = useState<Record<string, any>>({});
  const [deleting, setDeleting] = useState(false);
  const [renamePopOver, setRenamePopover] = useState(false);
  const [renaming, setRenaming] = useState(false);

  const linkRef = useRef(null);

  const params = useParams();
  const { dropQuery } = useQuery();

  useEffect(() => {
    setDocumentName(document.name);
    setStatusConf(statusMap[document.status] || {});
  }, [document]);

  const handleChangeName = async () => {
    try {
      const trimmedName = documentName.trim();
      // 如果输入为空，提示用户
      if (!trimmedName) {
        message.warning('文档名称不能为空');
        return;
      }
      if (trimmedName === document.name) {
        setRenamePopover(false);
        return;
      }
      setRenaming(true);
      await NewKnowledge.updateDocumentInfo({
        ...document,
        name: trimmedName,
      });
      setRefreshSiderFlag((prev) => !prev);
      setRenamePopover(false);
      message.success('重命名成功');
    } catch (err: any) {
      message.error(`重命名失败, ${err.message}`);
    } finally {
      setRenaming(false);
    }
  };

  const getSourceFile = () => {
    const url = nos2url(downloadUrl);
    fetch(url)
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        if (linkRef.current) {
          linkRef.current.href = url;
          linkRef.current.download = '';
          linkRef.current.click();
        }
      })
      .catch((error) => {
        console.error('文件下载失败', error);
      });
  };

  const handleDeleteDocument = async () => {
    if (!document.knowledgeItemId) return;
    try {
      setDeleting(true);
      await NewKnowledge.deleteDocument({
        knowledgeItemId: document.knowledgeItemId,
      });
      dropQuery(['docId']);
      setRefreshSiderFlag((prev) => !prev);
      message.success('删除成功');
    } catch (err: any) {
      message.error(`删除失败, ${err.message}`);
    } finally {
      setDeleting(false);
    }
  };

  const content = (
    <Flex vertical align="end">
      <Input.TextArea
        style={{ width: 300 }}
        autoSize={{ minRows: 3, maxRows: 10 }}
        value={documentName}
        onChange={(e) => setDocumentName(e.target.value)}
      />

      <Button
        type="primary"
        size="small"
        style={{ marginTop: 10 }}
        loading={renaming}
        onClick={handleChangeName}
      >
        保存
      </Button>
    </Flex>
  );

  return (
    <>
      <Flex align="center" justify="space-between">
        <Flex align="center">
          <Title>{document.name || '未命名文档'}</Title>

          <Popover
            title="文档重命名"
            content={content}
            trigger="click"
            placement="bottom"
            open={renamePopOver}
            onOpenChange={setRenamePopover}
          >
            <Button type="link" style={{ padding: 0, marginLeft: 8 }}>
              <FormOutlined />
            </Button>
          </Popover>

              <Tag style={{ marginLeft: 20 }}>{importType && documentImportTypeMap[importType]}</Tag>
              <Tag>{chunkStrategy}</Tag>
              <Tag color={statusConf.color} icon={statusConf.icon}>
                {statusConf.label}
              </Tag>
            </Flex>

            <Flex align="center">
              {importType === 'local' && <Button type="link" style={{ padding: 0 }} onClick={getSourceFile}>
                原始文件
              </Button>}

              {importType === 'popo' && docLink && <a target='_blank' href={docLink} >
                原始文件
                {downloadUrl}
              </a>}

              <a ref={linkRef} style={{ display: 'none' }}></a>

              <Popconfirm
                title="确认删除"
                description="该操作不可撤销"
                okType="danger"
                placement="bottomLeft"
                onConfirm={handleDeleteDocument}
                icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
              >
                <Button
                  type="link"
                  style={{ padding: 0, marginLeft: 8 }}
                  loading={deleting}
                >
                  <DeleteOutlined />
                </Button>
              </Popconfirm>
            </Flex>
          </Flex>

      {document.knowledgeItemId && (
        <DocPreview
          hideMessage
          itemKey="fragmentId"
          rights={['preview', 'edit', 'delete']}
          doc={{ reviewId: document.knowledgeItemId }}
          containerHeight={window.innerHeight - 310}
          onLoadData={(data) => {
            setImportType(data.importType);

            setChunkStrategy(
              documentChunkStrategyMap[
              JSON.parse(data.config || '{}')?.chunkConfig?.chunkType
              ],
            );
            setDownloadUrl(JSON.parse(data.metaData || '{}')?.nosKey);
            setDocLink(JSON.parse(data.metaData || '{}')?.link);
            setStatusConf(statusMap[data.status] || {});
            if (data.status !== 'waiting') {
              onChangeStatus(document.knowledgeItemId, data.status);
            }
          }}
          fetchData={NewKnowledge.getDocumentDetail}
          payload={{
            knowledgeId: params.knowledgeId,
            knowledgeItemId: document.knowledgeItemId,
          }}
        />
      )}
    </>
  );
};

export default TextDocumentContent;
