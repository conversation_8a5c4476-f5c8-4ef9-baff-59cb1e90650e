import React, { ChangeEvent, useEffect, useState } from 'react';
import styled from 'styled-components';
import ExportModal from '@/components/export-modal';
import {
  <PERSON><PERSON>crumb,
  Button,
  Card,
  Dropdown,
  Flex,
  Input,
  MenuProps,
  message,
  notification,
  Space,
} from 'antd';
import { Link, useParams } from 'react-router-dom';
import { usePathQuery, useQuery } from '@hooks/useQuery';
import { DocumentListItemModal, KnowledgeModel } from '@/interface';
import {
  AimOutlined,
  DownOutlined,
  FileOutlined,
  FormOutlined,
  LeftOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import EditKnowledgeModal from '../components/edit-knowledge-modal';
import { LayoutContainer } from '@components/layout-container';
import DocumentsSider from '../components/documents-sider';
import { NewKnowledge } from '@/api';
import TextUploader from '@/pages/new-knowledge/components/text-uploader';
import { debounce } from 'lodash';
import CioDocumentContent from '@/pages/knowledge-detail/components/cio-document-content';
import CioUploader from '../components/cio-uploader';
import Recall from '../text/recall';

const Container = styled.div`
  background-color: rgba(248, 250, 252);
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
`;

const StyleLeftOutlined = styled(LeftOutlined)`
  margin-right: 10px;
  font-size: 16px;
  cursor: pointer;
  color: #8c8c8c;

  :hover {
    color: #1677ff;
  }
`;

const Sider = styled.div`
  width: 300px;
  min-width: 200px;
  height: 100%;
  padding: 5px 10px;
  overflow: hidden;
  border-radius: 10px;
`;

const StyleDivider = styled.div`
  width: 1px;
  height: 100%;
  background: #efefef;
`;

const Content = styled.div`
  width: 100%;
  height: 100%;
  padding: 0;
  margin-left: 20px;
  overflow: hidden;
  border-radius: 10px;
`;

const ListTitle = styled.div`
  margin-top: 10px;
  padding-left: 5px;
  font-size: 12px;
  color: #666;
`;

const StyledDropdownWrapper = styled.div`
  padding: 8px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 120px;
`;

const DropdownItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.2s;

  &:hover {
    background: #f5f5f5;
  }
`;

const CioDocument = (props) => {
  const { type } = props;
  const [knowledgeDetail, setKnowledgeDetail] = useState<KnowledgeModel>();
  const [documentList, setDocumentList] = useState<DocumentListItemModal[]>([]);
  const [curDocument, setCurDocument] = useState<DocumentListItemModal>();
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [refreshKnowledgeFlag, setRefreshKnowledgeFlag] = useState(false);
  const [searchKey, setSearchKey] = useState('');
  const [current, setCurrent] = useState(1);
  const [refreshSiderFlag, setRefreshSiderFlag] = useState(false);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  const params = useParams();
  const { parsedQuery, addQuery } = useQuery();
  const { pushRetainQuery } = usePathQuery();
  const [api, contextHolder] = notification.useNotification();

  const PAGE_SIZE = 20;

  const items: MenuProps['items'] = [
    {
      key: 'local',
      label: (
        <CioUploader
          trigger="新增cio内容"
          groupId={parsedQuery.groupId}
          workspaceId={parsedQuery.workspaceId}
          knowledgeId={params.knowledgeId}
          onChange={() => {
            openNotification();
            setRefreshSiderFlag((prev) => !prev);
          }}
        />
      ),
      icon: <FileOutlined />,
      onClick: () => {},
    },
  ];

  useEffect(() => {
    fetchKnowledgeDetail();
  }, [refreshKnowledgeFlag]);

  useEffect(() => {
    setDocumentList([]);
    setCurrent(1);
    setTotal(0);
    setSearchKey('');
    fetchDocumentList(1, 0, '');
  }, [refreshSiderFlag]);

  // 轮训更新sider中文档的状态
  useEffect(() => {
    // 防抖 避免快速切换分页
    const debouncedCheck = debounce(async () => {
      // 1. 检查是否存在 waiting 状态的文档
      const hasWaiting = documentList.some((item) => item.status === 'waiting');
      if (!hasWaiting) return;
      try {
        // 2. 计算已加载的分页范围（current-1 表示已加载的页数）
        const loadedPages = Array.from({ length: current - 1 }, (_, i) => i + 1);
        // 3. 并发请求所有已加载分页的最新数据
        const pagesData = await Promise.all(
          loadedPages.map((page) =>
            NewKnowledge.getDocumentList({
              knowledgeId: params.knowledgeId!,
              page,
              pageSize: PAGE_SIZE,
              name: searchKey,
            }),
          ),
        );
        // 4. 扁平化数据并去重（避免分页间数据重复）
        const latestDocuments = pagesData
          .flatMap((res) => res.values)
          .reduce((acc, cur) => {
            acc.set(cur.knowledgeItemId, cur);
            return acc;
          }, new Map())
          .values();
        // 5. 更新文档列表
        setDocumentList(Array.from(latestDocuments));
        // 6. 同步更新当前选中的文档
        if (parsedQuery.docId) {
          const activeDoc = Array.from(latestDocuments).find(
            (doc) => Number(doc.knowledgeItemId) === Number(parsedQuery.docId),
          );
          if (activeDoc) setCurDocument(activeDoc);
        }
      } catch (error) {
        console.error('轮询失败:', error);
      }
    }, 10000);

    // 启动轮询监听
    const hasWaiting = documentList.some((item) => item.status === 'waiting');
    if (!hasWaiting) return;
    debouncedCheck();
    // 设置定时器持续轮询
    const interval = setInterval(debouncedCheck, 10000);
    return () => {
      clearInterval(interval);
      debouncedCheck.cancel(); // 清理防抖函数
    };
  }, [documentList, current, searchKey, params.knowledgeId, parsedQuery.docId]);

  // 获取知识库详情
  const fetchKnowledgeDetail = async () => {
    try {
      if (!params.knowledgeId) return;
      const detail = await NewKnowledge.getKnowledgeDetail({
        id: params.knowledgeId,
      });
      setKnowledgeDetail(detail);
    } catch (err) {
      console.log('err', err);
    }
  };

  // 获取文档列表
  const fetchDocumentList = async (page: number, totalCount: number, name: string) => {
    if (loading) return;
    try {
      setLoading(true);
      if (totalCount && totalCount === documentList.length) return;
      if (!params.knowledgeId) {
        message.error('没有找到对应知识库，请返回知识库页面重试～');
        return;
      }
      const { values, total: curTotal } = await NewKnowledge.getDocumentList({
        knowledgeId: params.knowledgeId,
        page,
        pageSize: PAGE_SIZE,
        name,
      });
      if (page === 1 && values.length) {
        // url有docId
        if (parsedQuery.docId) {
          const active = values.find(
            (item) => Number(item.knowledgeItemId) === Number(parsedQuery.docId),
          );
          // active是否出现在前十个
          if (active) setCurDocument(active);
          else setCurDocument(values[0]);
        } else {
          setCurDocument(values[0]);
          addQuery({ docId: values[0].knowledgeItemId });
        }
      }
      setTotal(curTotal);
      setDocumentList((prev) => [...prev, ...values]);
      setCurrent((prev) => prev + 1);
    } catch (err) {
      console.log('err', err);
    } finally {
      setLoading(false);
    }
  };

  const searchDocument = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchKey(e.target.value);
    setCurrent(1);
    setTotal(0);
    setDocumentList([]);
    fetchDocumentList(1, 0, e.target.value);
  };

  const openNotification = () => {
    const key = `open${Date.now()}`;
    const btn = (
      <Space>
        <Button
          type="primary"
          size="small"
          onClick={() => setRefreshSiderFlag((prev) => !prev)}
        >
          刷新
        </Button>
        <Button size="small" onClick={() => api.destroy()}>
          关闭
        </Button>
      </Space>
    );
    api.open({
      type: 'success',
      message: '上传成功',
      description:
        '您的文件已上传成功，但数据同步需要约1-2分钟完成。若列表没有及时刷新，请稍后刷新页面。',
      btn,
      key,
      duration: 0,
    });
  };

  const recallTest = () => {
    pushRetainQuery(`/new-knowledge/${params.knowledgeId}/recall`, [
      'workspaceId',
      'groupId',
      'knowledgeType',
    ]);
  };

  return (
    <LayoutContainer>
      <Container>
        <Flex align="center" style={{ margin: '20px 20px 0' }}>
          <StyleLeftOutlined
            onClick={() =>
              pushRetainQuery(`/group/new-knowledge`, [
                'workspaceId',
                'groupId',
                'knowledgeType',
                'isAllType',
              ])
            }
          />
          <Breadcrumb
            items={[
              {
                title: (
                  <Link
                    to={`/group/new-knowledge?workspaceId=${parsedQuery.workspaceId}&groupId=${parsedQuery.groupId}&isAllType=${parsedQuery.isAllType}`}
                  >
                    知识库(新)
                  </Link>
                ),
              },
              {
                title: (
                  <Link
                    to={`/new-knowledge/${params.knowledgeId}?workspaceId=${parsedQuery.workspaceId}&groupId=${parsedQuery.groupId}&knowledgeType=cio`}
                  >
                    {knowledgeDetail?.name}
                  </Link>
                ),
              },
              ...(type === 'recall'
                ? [
                    {
                      title: '召回测试',
                    },
                  ]
                : []),
            ]}
          />
        </Flex>

        <Card
          style={{ margin: '20px 20px' }}
          bodyStyle={{ padding: '10px' }}
          title={
            <div>
              {knowledgeDetail?.name}
              <Button type="link" style={{ padding: 0, marginLeft: 8 }}>
                <FormOutlined onClick={() => setEditModalOpen(true)} />
              </Button>
            </div>
          }
          extra={
            type === 'index' ? (
              <Flex>
                <Button
                  type="primary"
                  style={{ marginRight: 16 }}
                  icon={<AimOutlined />}
                  onClick={recallTest}
                >
                  召回测试
                </Button>

                <Dropdown
                  trigger={['click']}
                  menu={{ items }}
                  dropdownRender={() => (
                    <StyledDropdownWrapper>
                      {items.map((item: Record<string, any>) => (
                        <DropdownItem key={item.key}>
                          {item.icon && <span>{item.icon}</span>}
                          <span style={{ marginLeft: 5 }}>{item.label}</span>
                        </DropdownItem>
                      ))}
                    </StyledDropdownWrapper>
                  )}
                >
                  <Button type="primary">
                    <Space>
                      新增内容
                      <DownOutlined />
                    </Space>
                  </Button>
                </Dropdown>
              </Flex>
            ) : null
          }
          loading={!knowledgeDetail}
        >
          {type === 'index' ? (
            <Flex style={{ height: 'calc(100vh - 190px)' }}>
              <Sider>
                <Input
                  allowClear
                  value={searchKey}
                  onChange={searchDocument}
                  placeholder="输入搜索投放池"
                  prefix={<SearchOutlined />}
                />
                <ListTitle>投放池列表</ListTitle>
                <DocumentsSider
                  loading={loading}
                  type="text"
                  list={documentList}
                  activeItem={curDocument}
                  onItemClick={(item) => {
                    setCurDocument(item);
                    addQuery({ docId: item.knowledgeItemId });
                  }}
                  onScroll={() => fetchDocumentList(current, total, searchKey)}
                />
              </Sider>

              <StyleDivider />

              <Content>
                <CioDocumentContent
                  item={curDocument}
                  setRefreshSiderFlag={setRefreshSiderFlag}
                  onChangeStatus={(docId, status) => {
                    setDocumentList((prev) =>
                      prev.map((i) => ({
                        ...i,
                        status:
                          Number(i.knowledgeItemId) === Number(docId) ? status : i.status,
                      })),
                    );
                  }}
                />
                {/*<CioDocument />*/}
              </Content>
            </Flex>
          ) : (
            // @ts-ignore
            <Recall knowledgeId={params.knowledgeId!} type="cio" />
          )}
        </Card>

        <EditKnowledgeModal
          item={knowledgeDetail}
          open={editModalOpen}
          setOpen={setEditModalOpen}
          setRefreshFlag={setRefreshKnowledgeFlag}
        />

        {contextHolder}
      </Container>
    </LayoutContainer>
  );
};

export default CioDocument;
