import { useState, useEffect } from 'react';
import { Input, Button, message, Card, Tag, Typography, Space, Divider, Dropdown, Modal, Table } from 'antd';
import styled from 'styled-components';
import {
  CheckOutlined,
  CloseOutlined,
  SettingOutlined,
  HistoryOutlined,
  FileTextOutlined,
  LinkOutlined,
  FileTextTwoTone,
  DeleteOutlined
} from '@ant-design/icons';
import { NewKnowledge } from '@/api/new-knowledge';
import type { RecallResponseData } from '@/api/new-knowledge';
import { KnowledgeConfigModal, defaultKnowledgeConfig, searchTypeMap } from '@/pages/app/components/agent-dev/mods/knowledge-card/knowledge-config';

const { Paragraph, Text } = Typography;

const Container = styled.div`
  display: flex;
  gap: 20px;
  padding: 20px;
  height: 100vh;
  background-color: #fff;
  overflow: hidden;

  .left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    border-right: 1px solid #f0f0f0;
    padding-right: 20px;
    overflow-y: auto;
  }

  .right-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 30px;
    overflow-y: auto;
    padding-right: 10px;
  }

  .input-area {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .history-area {
    flex: 1;

    .history-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .history-item {
      padding: 12px;
      background-color: #fafafa;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .history-content {
        flex: 1;
        margin-right: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .delete-btn {
        visibility: hidden;
        color: #999;
        
        &:hover {
          color: #ff4d4f;
        }
      }

      &:hover {
        background-color: #f0f0f0;
        transform: translateX(4px);

        .delete-btn {
          visibility: visible;
        }
      }
    }

    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .history-tip {
      color: #999;
      font-size: 12px;
      margin: 8px 0;
      padding: 8px;
      background-color: #fafafa;
      border-radius: 4px;
      border: 1px solid #f0f0f0;
    }
  }

  .panel {
    position: relative;
    padding: 0;
  }

  .result-panel {
    margin-bottom: 20px;
  }

  .param-panel {
    flex: 0 0 auto;
    margin-bottom: 20px;
  }

  .result-item {
    padding: 16px;
    background-color: #fafafa;
    border-radius: 4px;
    margin-bottom: 16px;
    border: 1px solid #f0f0f0;
    transition: all 0.3s;
    
    &:hover {
      box-shadow: 0 2px 8px rgba(0,0,0,0.09);
      border-color: #e8e8e8;
    }
    
    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .result-content {
      margin: 12px 0;
      max-height: 100px;
      overflow-y: auto;
      background: #fff;
      padding: 12px;
      border-radius: 4px;
      border: 1px solid #f0f0f0;
    }
    
    .result-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      color: #999;
      font-size: 12px;

      a {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: blue;
        text-decoration: none;
      }
    }
  }

  h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 1;
    display: flex;
    align-items: center;
    
    .anticon {
      margin-right: 8px;
      font-size: 18px;
    }
  }

  .param-table {
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    
    .table-header {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      background: #f0f2f5;
      
      .header-cell {
        color: #666;
        font-size: 14px;
        padding: 10px 16px;
        text-align: left;
      }
    }

    .table-row {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      background: #f9f9fb;
      position: relative;
      
      .cell {
        color: #333;
        font-size: 14px;
        padding: 10px 16px;
        text-align: left;
      }

      .status-icon {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        
        &.success {
          color: #52c41a;
        }
        
        &.error {
          color: #ff4d4f;
        }

        .anticon {
          font-size: 16px;
        }
      }
    }
  }

  .detail-btn {
    margin-left: 8px;
    padding: 0 8px;
    font-size: 12px;
    height: 22px;
  }
`;

export default function Recall({ knowledgeId }: { knowledgeId: string, type?: string }) {
  const [inputText, setInputText] = useState('');
  const [history, setHistory] = useState<string[]>([]);
  const [testResults, setTestResults] = useState<RecallResponseData['results']>([]);
  const [recallResult, setRecallResult] = useState<RecallResponseData>();
  const [loading, setLoading] = useState(false);
  const [resultType, setResultType] = useState('');
  const [searchType, setSearchType] = useState('语义检索');
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [knowledgeConfig, setKnowledgeConfig] = useState({
    ...defaultKnowledgeConfig
  });
  const [selectedFragment, setSelectedFragment] = useState<RecallResponseData['results'][0] | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 从 localStorage 加载历史记录
  useEffect(() => {
    const savedHistory = localStorage.getItem(`recallTestHistory_${knowledgeId}`);
    if (savedHistory) {
      setHistory(JSON.parse(savedHistory));
    }
  }, [knowledgeId]);

  // 保存历史记录到 localStorage
  const saveHistory = (newHistory: string[]) => {
    // 限制最多保存10条记录
    const limitedHistory = newHistory.slice(0, 10);
    localStorage.setItem(`recallTestHistory_${knowledgeId}`, JSON.stringify(limitedHistory));
    setHistory(limitedHistory);
  };

  const headers = ['检索模式', '引用上限', '最低相关度', '结果重排', '问题优化'];

  // 根据实际配置生成显示的值
  const getDisplayValues = () => {
    return [
      searchType,
      knowledgeConfig.filter?.limit || '-',
      knowledgeConfig.filter?.similarity || '-',
      '-',
      '-' // 暂时不显示问题优化状态，因为默认配置中没有这个选项
    ];
  };

  const searchTypeItems = [
    {
      key: '语义检索',
      label: '语义检索',
    },
    {
      key: '混合检索',
      label: '混合检索',
    },
  ];

  const handleConfigChange = (key, val) => {
    setKnowledgeConfig(prev => ({
      ...prev,
      [key]: val
    }));
    if (key === 'searchType') {
      setSearchType(searchTypeMap[val] || '语义检索');
    }
  };

  const handleTest = async () => {
    if (!inputText.trim()) {
      message.warning('请输入测试文本');
      return;
    }

    setLoading(true);
    try {
      const response = await NewKnowledge.recall({
        searchType: Object.entries(searchTypeMap).find(([_, value]) => value === searchType)?.[0] || 'SEMANTIC',
        filter: knowledgeConfig.filter,
        queryRewrite: {
          use: false,
          prompt: "Rewrite this query."
        },
        groupId: "exampleGroupId",
        knowledgeIds: [Number(knowledgeId)],
        knowledgeItemIds: [],
        query: inputText
      });

      if (!history.includes(inputText)) {
        const newHistory = [inputText, ...history];
        saveHistory(newHistory);
      }

      setResultType(response.type || '');
      setTestResults(response.results || []);
      setRecallResult(response);
    } catch (error) {
      console.error('测试失败:', error);
      message.error('测试失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 渲染文档片段内容的类型定义
  const renderFragmentContent = (content: string) => {
    return (
      <Paragraph>
        {content}
      </Paragraph>
    );
  };

  // 提取文档URL的类型定义
  const getDocumentUrl = (metaData: string | null): string | null => {
    if (!metaData) return null;
    try {
      const parsed = JSON.parse(metaData);
      return parsed.url;
    } catch (e) {
      return null;
    }
  };

  // 显示详情弹窗
  const showDetailModal = (fragment) => {
    setSelectedFragment(fragment);
    setDetailModalVisible(true);
  };

  // 删除单条历史记录
  const deleteHistoryItem = (index: number, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止点击事件冒泡
    const newHistory = history.filter((_, i) => i !== index);
    saveHistory(newHistory);
  };

  // 清空历史记录
  const clearHistory = () => {
    localStorage.removeItem(`recallTestHistory_${knowledgeId}`);
    setHistory([]);
  };

  return (
    <Container>
      {/* 左侧部分 */}
      <div className="left-section">
        {/* 上部输入框 */}
        <div className="input-area">
          <Input.TextArea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请输入文本"
            autoSize={{ minRows: 8, maxRows: 12 }}
          />
          <Button type="primary" onClick={handleTest} loading={loading}>测试</Button>
        </div>

        {/* 下部历史记录 */}
        <div className="history-area">
          <div className="history-header">
            <h3 style={{ margin: 0 }}><HistoryOutlined />测试历史</h3>
            {history.length > 0 && (
              <Button
                type="link"
                size="small"
                onClick={clearHistory}
                danger
                icon={<DeleteOutlined />}
              >
                清空历史
              </Button>
            )}
          </div>

          <div className="history-tip">
            系统将自动保存最新的10次测试记录，支持点击记录快速重新测试
          </div>

          <div className="history-list">
            {history.map((item, index) => (
              <div
                key={index}
                className="history-item"
                onClick={() => setInputText(item)}
              >
                <div className="history-content">{item}</div>
                <CloseOutlined
                  className="delete-btn"
                  onClick={(e) => deleteHistoryItem(index, e)}
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 右侧部分 */}
      <div className="right-section">
        {/* 上部测试展示 */}
        <div className="panel param-panel">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3 style={{ margin: 0 }}><SettingOutlined />测试参数</h3>
            <Button
              type="link"
              onClick={() => setConfigModalVisible(true)}
              icon={<SettingOutlined />}
            >
              设置
            </Button>
          </div>
          <div className="param-table">
            <div className="table-header">
              {headers.map((header, index) => (
                <div key={index} className="header-cell">
                  {header}
                </div>
              ))}
            </div>
            <div className="table-row">
              {getDisplayValues().map((value, index) => (
                <div key={index} className="cell">
                  {value}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 下部测试结果列表 */}
        <div className="panel result-panel">
          <h3><FileTextOutlined />测试结果</h3>
          {resultType && testResults.length > 0 && (
            <div style={{ marginBottom: '16px' }}>
              <Tag color="blue">结果类型: {resultType}</Tag>
              <Tag color="green">共 {testResults.length} 条结果</Tag>
            </div>
          )}

          {testResults.length === 0 && !loading && (
            <div style={{ color: '#999', textAlign: 'center', padding: '30px 0' }}>
              暂无测试结果
            </div>
          )}

          {loading && (
            <div style={{ color: '#999', textAlign: 'center', padding: '30px 0' }}>
              正在加载结果...
            </div>
          )}

          {resultType === 'cio' && <Table
            columns={(recallResult?.knowledgeConfig?.headerFields?.map(col => {
              return {
                title: col?.name,
                dataIndx: col?.code,
                render: (val, rec) => {
                  return rec.extData?.[col?.code]
                }
              }
            }) || []).concat([
              {
                title: '相似度得分排序',
                dataIndex: 'index', render: (val, rec, index) => {
                  return `${index + 1}`
                }
              },
              {
                title: '投放池',
                dataIndex: 'poolName',
                render: (val, rec) => {
                  return rec.knowledgeItemInfo?.name
                }
              },
              {
                title: '片段ID',
                dataIndex: 'fragmentId',
                render: val => {
                  return <div style={{ wordBreak: 'break-all' }}>{val}</div>
                }
              }
            ])}
            dataSource={recallResult?.results?.map(rec => rec)}
          ></Table>}

          {resultType === 'text' && <>
            {testResults.map((result, index) => {
              return (
                <div key={index} className="result-item">
                  <div className="result-header">
                    <Space>
                      <FileTextTwoTone />
                      <Text strong>文档片段</Text>
                      <Button
                        type="link"
                        size="small"
                        className="detail-btn"
                        onClick={() => showDetailModal(result)}
                      >
                        详情
                      </Button>
                    </Space>
                    <Text type="secondary">相似度得分排序: #{index + 1}</Text>
                  </div>

                  <Divider style={{ margin: '8px 0' }} />

                  <div className="result-content">
                    {renderFragmentContent(result.content)}
                  </div>

                  <div className="result-footer">
                    <Space size={16} style={{ flex: 1, minWidth: 0 }}>
                      <Text type="secondary">来源文档:</Text>
                      <div style={{
                        maxWidth: '200px',
                        minWidth: 0,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        <a
                          href={`/new-knowledge/${result?.knowledgeItemInfo?.knowledgeId}?knowledgeType=text&docId=${result?.knowledgeItemId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {result?.knowledgeItemInfo?.name}
                        </a>
                      </div>
                      <Text type="secondary">片段ID: {result.fragmentId}</Text>
                    </Space>

                    {getDocumentUrl(result.knowledgeItemMetaData) && (
                      <a href={getDocumentUrl(result.knowledgeItemMetaData)} target="_blank" rel="noopener noreferrer">
                        <LinkOutlined /> 查看原文档
                      </a>
                    )}
                  </div>
                </div>
              )
            })}
          </>}

          {resultType === 'table' && <Table columns={(recallResult?.knowledgeConfig?.headerFields?.map(col => {
              return {
                title: col?.name,
                dataIndx: col?.code,
                render: (val, rec) => {
                  return rec.extData?.[col?.code]
                }
              }
            }) || []).concat([
              {
                title: '相似度得分排序',
                dataIndex: 'index', render: (val, rec, index) => {
                  return `${index + 1}`
                }
              },
             
              {
                title: '片段ID',
                dataIndex: 'fragmentId',
                render: val => {
                  return <div style={{ wordBreak: 'break-all' }}>{val}</div>
                }
              }
            ])}
            dataSource={recallResult?.results?.map(rec => rec)}/>}


        </div>
      </div>
      <KnowledgeConfigModal
        open={configModalVisible}
        onClose={() => setConfigModalVisible(false)}
        onOk={() => setConfigModalVisible(false)}
        value={knowledgeConfig}
        onChange={handleConfigChange}
      />

      {/* 详情弹窗 */}
      <Modal
        title="文档片段详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedFragment && (
          <div style={{ padding: '16px' }}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div>
                <Text type="secondary">片段ID：</Text>
                <Text strong>{selectedFragment.fragmentId}</Text>
              </div>
              <div>
                <Text type="secondary">内容：</Text>
                <div style={{
                  background: '#fafafa',
                  padding: '16px',
                  borderRadius: '4px',
                  marginTop: '8px',
                  maxHeight: '400px',
                  overflowY: 'auto'
                }}>
                  {renderFragmentContent(selectedFragment.content)}
                </div>
              </div>
              {selectedFragment.extData && (
                <div>
                  <Text type="secondary">扩展数据：</Text>
                  <pre style={{
                    background: '#fafafa',
                    padding: '16px',
                    borderRadius: '4px',
                    marginTop: '8px'
                  }}>
                    {JSON.stringify(selectedFragment.extData, null, 2)}
                  </pre>
                </div>
              )}
            </Space>
          </div>
        )}
      </Modal>
    </Container >
  );
}
