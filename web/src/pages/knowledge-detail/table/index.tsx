import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Dropdown,
  Flex,
  MenuProps,
  message,
  notification,
  Popover,
  Space,
  Tag,
  Tooltip,
} from 'antd';
import { Link, useParams } from 'react-router-dom';
import {
  AimOutlined,
  DownloadOutlined,
  DownOutlined,
  ExportOutlined,
  FileOutlined,
  FormOutlined,
  LeftOutlined,
} from '@ant-design/icons';
import { LayoutContainer } from '@components/layout-container';
import styled from 'styled-components';
import { usePathQuery, useQuery } from '@hooks/useQuery';
import EditKnowledgeModal from '../components/edit-knowledge-modal';
import { KnowledgeModel } from '@/interface';
import DynamicTable from '../components/dynamic-table';
import { NewKnowledge } from '@/api';
import TableUploader from '../components/table-uploader';
import Recall from '../text/recall';
import ExportModal from '@/components/export-modal';

const PAGE_SIZE = 20;

const Container = styled.div`
  background-color: rgba(248, 250, 252);
  padding: 8px;
  height: 100%;
  box-sizing: border-box;
`;

const StyleLeftOutlined = styled(LeftOutlined)`
  margin-right: 10px;
  font-size: 16px;
  cursor: pointer;
  color: #8c8c8c;

  :hover {
    color: #1677ff;
  }
`;

const StyledDropdownWrapper = styled.div`
  padding: 8px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 120px;
`;

const DropdownItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background 0.2s;

  &:hover {
    background: #f5f5f5;
  }
`;

const EllipsisText = styled.div`
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const TableDocument = (props) => {
  const { type } = props;
  const [editKnowledgeModalOpen, setEditKnowledgeModalOpen] = useState(false);
  const [documentDetail, setDocumentDetail] = useState<Record<string, any> | null>();
  const [knowledgeDetail, setKnowledgeDetail] = useState<KnowledgeModel | null>();
  const [refreshKnowledgeFlag, setRefreshKnowledgeFlag] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [curPage, setCurPage] = useState(1);
  const [total, setTotal] = useState(0);

  const params = useParams();
  const { parsedQuery } = useQuery();
  const { pushRetainQuery } = usePathQuery();
  const [api, contextHolder] = notification.useNotification();

  const items: MenuProps['items'] = [
    {
      key: 'local',
      label: (
        <TableUploader
          trigger="本地文档"
          groupId={parsedQuery.groupId}
          workspaceId={parsedQuery.workspaceId}
          knowledgeId={params.knowledgeId}
          onChange={() => {
            openNotification();
            setRefreshKnowledgeFlag((prev) => !prev);
          }}
        />
      ),
      icon: <FileOutlined />,
    },
  ];

  useEffect(() => {
    clear();
    fetchKnowledgeDetail();
    setLoading(true);
    setTimeout(() => fetchTable(1), 2000);
  }, [refreshKnowledgeFlag]);

  const openNotification = () => {
    const key = `open${Date.now()}`;
    const btn = (
      <Space>
        <Button
          type="primary"
          size="small"
          onClick={() => setRefreshKnowledgeFlag((prev) => !prev)}
        >
          刷新
        </Button>
        <Button size="small" onClick={() => api.destroy()}>
          关闭
        </Button>
      </Space>
    );
    api.open({
      type: 'success',
      message: '上传成功',
      description:
        '您的文件已上传成功，但数据同步需要约1-2分钟完成。若列表没有及时刷新，请稍后刷新页面。',
      btn,
      key,
      duration: 0,
    });
  };

  const clear = () => {
    setDocumentDetail(null);
    setKnowledgeDetail(null);
    setColumns([]);
    setData([]);
  };

  // 获取知识库详情
  const fetchKnowledgeDetail = async () => {
    try {
      if (!params.knowledgeId) return;
      const detail = await NewKnowledge.getKnowledgeDetail({
        id: params.knowledgeId,
      });
      setKnowledgeDetail(detail);
    } catch (err) {
      console.log('err', err);
    }
  };

  // 获取表头表数据
  const fetchTable = async (pageNum: number) => {
    try {
      if (!params.knowledgeId) return;
      setLoading(true);
      const { values } = await NewKnowledge.getDocumentList({
        knowledgeId: params.knowledgeId!,
        page: 1,
        pageSize: PAGE_SIZE,
        name: '',
      });
      setDocumentDetail(values?.[0]);
      if (!values?.[0]?.knowledgeItemId) return;
      const { knowledgeConfig, content } = await NewKnowledge.getDocumentDetail({
        knowledgeId: params.knowledgeId, // 知识库ID
        knowledgeItemId: values?.[0].knowledgeItemId, // 知识库 item ID
        pageNum,
        pageSize: PAGE_SIZE,
      });
      const formatColumns = JSON.parse(knowledgeConfig)?.headerFields?.map((i) => ({
        title: (
          <Flex>
            <Popover content={i.name} mouseEnterDelay={0.2}>
              <EllipsisText>{i.name}</EllipsisText>
            </Popover>
            <Tag bordered={false} style={{ marginLeft: 5, fontSize: 10 }}>
              {i.type}
            </Tag>
            {i.semantic && (
              <Tag
                bordered={false}
                color="success"
                style={{ marginLeft: 5, fontSize: 10 }}
              >
                语义理解字段
              </Tag>
            )}
          </Flex>
        ),
        dataIndex: i.code,
        key: i.code,
        width: 300,
        originData: i,
      }));
      setColumns(formatColumns);
      const semantic = JSON.parse(knowledgeConfig)?.headerFields.find((i) => i.semantic)
        ?.code;
      const formatData = content?.chunks?.map((item) => ({
        key: item.chunkId,
        order: item.order,
        ...(semantic ? { [semantic]: item.text } : {}),
        ...item.extData,
      }));
      setData((prev) => [...prev, ...formatData]);
      setCurPage((prev) => prev + 1);
      setTotal(content.total);
    } catch (err: any) {
      message.error(`获取表格数据失败, ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadMore = async () => {
    if (curPage - 1 > 0 && (curPage - 1) * PAGE_SIZE >= total) return;
    const { content } = await NewKnowledge.getDocumentDetail({
      knowledgeId: params.knowledgeId, // 知识库ID
      knowledgeItemId: documentDetail.knowledgeItemId, // 知识库 item ID
      pageNum: curPage,
      pageSize: PAGE_SIZE,
    });
    const formatData = content.chunks.map((item) => ({
      key: item.chunkId,
      order: item.order,
      ...item.extData,
    }));
    setData((prev) => [...prev, ...formatData]);
    setCurPage((prev) => prev + 1);
  };

  const onAdd = () => {
    const newRow = columns.reduce((pre, cur) => ({ ...pre, [cur.key]: '' }), {
      key: `temporary_${new Date().getTime()}`,
    });
    setData((prev) => [...prev, newRow]);
  };

  const onUpdate = async (chunkId: string, row: Record<string, any>) => {
    const type = row.order !== undefined ? 'update' : 'add';
    try {
      const old = data.find((i) => i.key === chunkId);
      if (JSON.stringify(old) === JSON.stringify(row)) return;
      const extData = Object.fromEntries(
        Object.entries(row).filter(([key]) => !['order', 'key'].includes(key)),
      );
      const res = await NewKnowledge.updateFragmentInfo({
        knowledgeId: params.knowledgeId, // 知识库ID
        knowledgeItemId: documentDetail.knowledgeItemId as string, // 知识库 item ID
        fragmentId: type === 'update' ? chunkId : undefined, // 分段ID
        serialNum: old.order,
        extData, //单行表格数据
      });
      setData((prev) =>
        prev.map((i) =>
          i.key === chunkId ? { ...row, key: res.fragmentId, order: res.serialNum } : i,
        ),
      );
      message.success(type === 'update' ? '修改成功' : '新增成功');
    } catch (err: any) {
      message.error(`${type === 'update' ? '修改失败' : '新增失败'}, ${err.message}`);
    }
  };

  const onDelete = async (chunkIds: string[]) => {
    try {
      // 如果chunkId是以temporary_开头的，是本地新增的临时数据，不需要接口
      const deletePromises = chunkIds
        .filter((i) => !i.startsWith('temporary_'))
        .map((chunkId) =>
          NewKnowledge.deleteFragment({
            knowledgeId: params.knowledgeId, // 知识库ID
            knowledgeItemId: documentDetail.knowledgeItemId, // 知识库 item ID
            fragmentId: chunkId, // 分段ID
          }),
        );
      await Promise.all(deletePromises);
      setData((prev) => prev.filter((item) => !chunkIds.includes(item.key)));
      message.success('删除成功');
    } catch (err: any) {
      message.error(`删除失败, ${err.message}`);
    }
  };

  const recallTest = () => {
    pushRetainQuery(`/new-knowledge/${params.knowledgeId}/recall`, [
      'workspaceId',
      'groupId',
      'knowledgeType',
    ]);
  };

  const exportKnowledge = async (exportParams?: {
    pageSize: number;
    totalPages: number;
  }) => {
    const res = await NewKnowledge.exportKnowledge({
      knowledgeId: params.knowledgeId, // 知识库ID
      knowledgeItemId: documentDetail.knowledgeItemId,
      type: 'table',
      pageSize: exportParams?.pageSize,
      pageNumber: exportParams?.totalPages,
    });
    if (res?.url) {
      window.open(res?.url, '_blank');
    }
    setExportModalVisible(false);
  };

  const handleExport = () => {
    setExportModalVisible(true);
  };

  return (
    <LayoutContainer>
      <Container>
        <Flex align="center" style={{ margin: '20px 20px 0' }}>
          <StyleLeftOutlined
            onClick={() =>
              pushRetainQuery(`/group/new-knowledge`, [
                'workspaceId',
                'groupId',
                'knowledgeType',
                'isAllType',
              ])
            }
          />
          <Breadcrumb
            items={[
              {
                title: (
                  <Link
                    to={`/group/new-knowledge?workspaceId=${parsedQuery.workspaceId}&groupId=${parsedQuery.groupId}&isAllType=${parsedQuery.isAllType}`}
                  >
                    知识库(新)
                  </Link>
                ),
              },
              {
                title: (
                  <Link
                    to={`/new-knowledge/${params.knowledgeId}?workspaceId=${parsedQuery.workspaceId}&groupId=${parsedQuery.groupId}&knowledgeType=cio`}
                  >
                    {knowledgeDetail?.name}
                  </Link>
                ),
              },
              ...(type === 'recall'
                ? [
                    {
                      title: '召回测试',
                    },
                  ]
                : []),
            ]}
          />
        </Flex>

        <Card
          style={{ margin: '20px 20px' }}
          title={
            <div>
              {knowledgeDetail?.name}
              <Button type="link" style={{ padding: 0, marginLeft: 8 }}>
                <FormOutlined onClick={() => setEditKnowledgeModalOpen(true)} />
              </Button>
              <Tooltip title="下载表格">
                <Button
                  type="link"
                  style={{ marginRight: 16 }}
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                ></Button>
              </Tooltip>
            </div>
          }
          extra={
            type === 'index' ? (
              <Flex>
                <Button
                  type="primary"
                  style={{ marginRight: 16 }}
                  icon={<AimOutlined />}
                  onClick={recallTest}
                >
                  召回测试
                </Button>
                <Dropdown
                  trigger={['click']}
                  menu={{ items }}
                  dropdownRender={() => (
                    <StyledDropdownWrapper>
                      {items.map((item: Record<string, any>) => (
                        <DropdownItem key={item.key}>
                          {item.icon && <span>{item.icon}</span>}
                          <span style={{ marginLeft: 5 }}>{item.label}</span>
                        </DropdownItem>
                      ))}
                    </StyledDropdownWrapper>
                  )}
                >
                  <Button type="primary">
                    <Space>
                      新增内容
                      <DownOutlined />
                    </Space>
                  </Button>
                </Dropdown>
              </Flex>
            ) : null
          }
          loading={!knowledgeDetail}
        >
          {type === 'index' ? (
            <DynamicTable
              actions={['add', 'edit', 'delete', 'select']}
              loading={loading}
              columns={columns}
              dataSource={data}
              loadMore={loadMore}
              onAdd={onAdd}
              onDelete={onDelete}
              onUpdate={onUpdate}
            />
          ) : (
            <Recall knowledgeId={params.knowledgeId!} type="table" />
          )}
        </Card>
      </Container>

      <ExportModal
        open={exportModalVisible}
        onOk={exportKnowledge}
        onCancel={() => setExportModalVisible(false)}
        total={total}
      />

      <EditKnowledgeModal
        item={knowledgeDetail}
        open={editKnowledgeModalOpen}
        setOpen={setEditKnowledgeModalOpen}
        setRefreshFlag={setRefreshKnowledgeFlag}
      />

      {contextHolder}
    </LayoutContainer>
  );
};

export default TableDocument;
