import React, { useEffect } from 'react';
import { usePathQuery, useQuery } from '@hooks/useQuery';
import TextKnowledge from './text';
import TableKnowledge from './table';
import CioDocument from './cio';
import { TopMenu } from '@components/top-menu';
import { SettingOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { useAdmin } from '@hooks/useAdmin';
import { menuConfig } from '@/pages/group';
import { GroupApi } from '@/api';

const groupMap = {};

const KnowledgeDetail: React.FC<{ type?: 'index' | 'recall' }> = ({ type = 'index' }) => {
  const { parsedQuery } = useQuery();
  const { pushRetainQuery } = usePathQuery();
  const isAdmin = useAdmin('group');

  const name = groupMap[parsedQuery.groupId as string]?.name;

  const renderMap = {
    text: <TextKnowledge type={type} />,
    table: <TableKnowledge type={type} />,
    cio: <CioDocument type={type} />,
  };

  useEffect(() => {
    if (parsedQuery.workspaceId) {
      GroupApi.list(parsedQuery.workspaceId).then((items) => {
        items.forEach((item) => {
          groupMap[item.id] = item;
        });
      });
    }
  }, [parsedQuery.workspaceId]);

  return (
    <>
      <PageLayout>
        <TopMenu
          style={{ paddingLeft: '15%' }}
          left={
            name ? (
              <GroupTitle>
                {name}
                {isAdmin && (
                  <SettingOutlined
                    onClick={() => {
                      pushRetainQuery('/group-setting', ['workspaceId', 'groupId']);
                    }}
                    style={{ fontSize: 16, color: '#999' }}
                  />
                )}
              </GroupTitle>
            ) : (
              <GroupTitle>暂无组信息</GroupTitle>
            )
          }
          path="group"
          menuConfig={menuConfig}
          retainQueries={['workspaceId', 'groupId', 'knowledgeType']}
        />

        {renderMap[parsedQuery.knowledgeType as string] || <TextKnowledge type={type} />}
      </PageLayout>
    </>
  );
};

export default KnowledgeDetail;

const PageLayout = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
`;

const GroupTitle = styled.div`
  user-select: none;
  display: flex;
  align-items: center;
  min-width: 160px;
  justify-content: start;
  gap: 10px;
  font-size: 18px;
  padding-left: 10px;
`;
