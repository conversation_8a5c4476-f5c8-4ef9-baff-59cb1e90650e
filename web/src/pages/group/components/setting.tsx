import { useRequest } from 'ahooks';
import { Button, Card, Form, message, Modal, Space, Tabs } from 'antd';
import { useCallback } from 'react';
import styled from 'styled-components';

import { GroupApi } from '@/api';
import { AppApi } from '@/api/app';
import { EnvSetting } from '@/components/env-setting';
import { LForm, LFormInput, LFormTextArea } from '@/components/form';
import { MidContainer } from '@/components/layout-container';
import { TokenSetting } from '@/components/TokenSetting';
import { useGlobalState } from '@/hooks/useGlobalState';
import { usePathQuery } from '@/hooks/useQuery';
import { GroupAppModelProvider } from '@/pages/workspace/components/model-provider';

export function GroupSettingPage() {
  const tabItems = [
    {
      key: '基础设置',
      label: '基础设置',
      children: <BasicSetting />,
    },
    {
      key: '环境变量',
      label: '环境变量',
      children: <EnvSetting type="group" />,
    },
    {
      key: '大模型绑定',
      label: '大模型绑定',
      children: <GroupAppModelProvider/>,
    },
    {
      key: 'API密钥',
      label: 'API密钥',
      children: <GroupToken />,
    },
  ];

  return (
    <MidContainer>
      <Card>
        <Tabs items={tabItems} centered></Tabs>
      </Card>
    </MidContainer>
  );
}

function GroupToken() {
  const { globalState } = useGlobalState();
  const { group } = globalState;

  if (!group) {
    return null;
  }

  const { data: apps } = useRequest(
    () => AppApi.listAppsByGroup(group.id, { pageSize: 300, pageNumber: 1 }),
    {
      refreshDeps: [group.id],
    },
  );

  return (
    <TokenSetting
      id={group.id}
      type="group"
      subResources={apps?.items}
      subType="app"
    ></TokenSetting>
  );
}

const layout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

function BasicSetting() {
  const { globalState, fetchGlobalState } = useGlobalState();
  const { group, workspace = { id: undefined } } = globalState;

  if (!group) {
    return null;
  }

  const [form] = Form.useForm();
  const { pushAddQuery } = usePathQuery();

  const { run: deleteGroupAPI } = useRequest(() => GroupApi.remove(group.id), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功');
      pushAddQuery(`/overview`, {
        workspaceId: workspace.id,
      });
    },
    onError: (err) => {
      message.error(`删除失败 ${err?.message}`);
    },
  });

  const deleteGroup = useCallback(() => {
    Modal.confirm({
      maskClosable: true,
      content: '删除Group前应确认该Group下所有的App已被删除，确定删除吗？',
      onOk: deleteGroupAPI,
    });
  }, [deleteGroupAPI]);

  const onFinish = (values: any) => {
    const { ...others } = values;
    GroupApi.update(
      {
        ...others,
      },
      group.id,
    )
      .then(() => {
        message.success('更新成功');
        fetchGlobalState('groups', workspace.id);
      })
      .catch((err) => {
        window.corona.warn('更新settings失败', err);
        message.error(`更新失败 ${err?.message}`);
      });
  };

  return (
    <StyledBasicSetting>
      <div className="form-wrapper">
        <LForm form={form} onFinish={onFinish} initialValues={group} {...layout}>
          <LFormInput
            name="name"
            label="业务组名称"
            rules={[{ required: true, message: '请输入业务组名称' }]}
          />
          <LFormTextArea
            name="description"
            label="业务组描述"
            rules={[{ required: true, message: '请输入业务组描述' }]}
          />
        </LForm>
      </div>
      <Space size={100}>
        <Button onClick={() => form.submit()} type="primary">
          保存
        </Button>
        <Button danger onClick={deleteGroup}>
          删除业务组
        </Button>
      </Space>
    </StyledBasicSetting>
  );
}

const StyledBasicSetting = styled.div`
  padding-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .form-wrapper {
    width: 500px;
    margin-bottom: 40px;
  }
`;
