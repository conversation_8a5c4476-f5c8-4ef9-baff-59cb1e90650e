import {
  BankOutlined,
  BuildOutlined,
  RobotOutlined,
  AppstoreOutlined,
  DashboardOutlined,
  BookOutlined
} from '@ant-design/icons';

import { createMenuPage, MenuItemConfig } from '../../components/pageLayout';
import { AppList } from '@/components/app-list';
import Market from '../market';
import Knowledge from '../knowledge';
import NewKnowledge from '../new-knowledge';
import SocialAigc from '../social-aigc';
import { env } from '@/utils/common';
import GroupMonitor from '../monitor/group-monitor';
import Evaluate from '../evaluate';
import CollectionItems from '../evaluate/collection-items';

export const menuConfig = [
  {
    title: '应用',
    path: 'apps',
    Icon: RobotOutlined,
    element: <AppList />,
  },
  {
    title: '插件',
    path: 'market',
    Icon: BuildOutlined,
    element: <Market />,
  },
  // {
  //   title: '知识库(旧)',
  //   path: 'knowledge',
  //   Icon: BankOutlined,
  //   // disabled: true,
  //   element: <Knowledge />,
  // },
  {
    title: '知识库',
    path: 'new-knowledge',
    Icon: BankOutlined,
    element: <NewKnowledge />,
  },
  {
    title: '监控',
    path: 'monitor',
    Icon: DashboardOutlined,
    element: <GroupMonitor />,
    admin: ['group'],
  },
  {
    title: '评测',
    path: 'evaluate',
    Icon: BookOutlined,
    matchPaths:['evaluate/collection/:collectionId'],
  
    element: <Evaluate />,
  },
  {
    title: '评测集详情',
    path: 'evaluate/collection/:collectionId',
    // matchPath: 'evaluate',
    element: <CollectionItems />,
    hideMenu: () => true,
  },
  {
    title: '社交直播AIGC',
    path: 'socialaigc',
    Icon: AppstoreOutlined,
    // disabled: true,
    hideMenu: (search) => {
      const groupId = (env === 'online' || env === 'pre') ? '97897edb-df1d-4ecf-9bb0-414afa73f733' : 'a3507672-6215-475e-b8ec-7e122f9ea3ae';
      return !search.includes(groupId);
    },
    element: <SocialAigc />,
  },
] as MenuItemConfig[];

// if (window.location.search.includes('debug')) {
//   menuConfig.push({
//     title: '评测',
//     path: 'evaluate',
//     Icon: BookOutlined,
//     element: <Evaluate />
//   });
// }

export function createGroupMenuPage() {
  // 在此函数中不能使用 useGlobalState 等 hooks， 否则会导致路由切换异常 (这是一个辅助函数，非 React Component)
  return createMenuPage({
    path: 'group',
    menuConfig,
    retainQueries: ['workspaceId', 'groupId'],
  });
}
