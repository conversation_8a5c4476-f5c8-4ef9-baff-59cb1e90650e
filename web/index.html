<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/src/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <title>LangBase</title>
    <script>
        !function (e, n, t, s, c) { var r = void 0 !== t && t.resolve, a = e[s]; (a = e[s] = function () { this.modules = {} }).callbacks = [], a.ready = r ? function () { return a.instance ? t.resolve(a.instance.vars()) : new t(function (e) { return a.callbacks.push(e) }) } : function (e) { return a.instance ? e(a.instance.vars()) : a.callbacks.push(e) }; var i = n.createElement(c), u = n.getElementsByTagName(c)[0]; i.async = !0, i.src = "https://s6.music.126.net/puzzle/<EMAIL>", u.parentNode.insertBefore(i, u) }(window, document, window.Promise, "puzzle", "script");
    </script>
</head>

<body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
</body>

</html>