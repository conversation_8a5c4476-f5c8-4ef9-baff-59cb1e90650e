# LangBase 前端工程

## 开发步骤

1. 将 langbase-local.yf-dev2.netease.com 配置到本地的 hosts 中,例如:
```
127.0.0.1 langbase-local.yf-dev2.netease.com
```

### 不用Nginx
现在已经支持不用nginx启动了，可以直接访问 `langbase-local.yf-dev2.netease.com:3100` 进行开发了。
```
  proxy: {
      '/oauth': {
        // target: 'http://langbase-workflow.yf-onlinetest4.netease.com',
        target: 'http://localhost:8000',
        headers: {
          'langbase-dev': 'true'
        },
        changeOrigin: true,
      },
      '/api': {
        target: 'http://localhost:8000',
        // target: 'http://langbase-workflow.yf-onlinetest4.netease.com',
        headers: {
          'langbase-dev': 'true'
        },
        changeOrigin: true,
      }
    },
```

**优点：** 跟在线dev环境域名不冲突，不需要切换 host 就可以直接访问在线的 langbase-workflow.yf-dev2.netease.com 环境

### Nginx

nginx 配置好后，其实比上面方便，可以只开前端调试。**不需要** vite.config.ts 中添加 proxy 配置

```
server {
    listen 80;
    server_name langbase-workflow.yf-dev2.netease.com;
    location / {
        proxy_pass http://127.0.0.1:3100;
        proxy_set_header Host $host;
    }

    location /api/v1 {
      proxy_pass http://**************;
      proxy_set_header Host $host;
    }

    location ~ /(docs|openapi.json) {
      proxy_pass http://**************;
      proxy_set_header Host $host;
    }

    # mock api
    location /api/mock {
      rewrite ^/api/mock/(.*) /api/v1/$1 break;
      proxy_pass http://127.0.0.1:1080;
    }
}

```

本地 host 配置

```
127.0.0.1 langbase-workflow.yf-dev2.netease.com
```

**优点：** 可以单独启动前端。
**缺点：** 想访问在线服务，需要关闭 host

另外，如果需要前后端都需要开发，注意nginx修改一下，然后server的.env中的 redirect_url 要从 `langbase-local.yf-dev2.netease.com:3100` 换成 `langbase-workflow.yf-dev2.netease.com`
```
  location /api/v1 {
      proxy_pass http://127.0.0.1:8000;
    #  proxy_pass http://**************;
      proxy_set_header Host $host;
    }
```


### 后续步骤

安装依赖:
`yarn install`

启动开发环境:
`yarn dev`

启动 Mock 服务器(非必须)：
`yarn mock`

启动后访问 `langbase.yf-dev2.netease.com`，如果不是用nginx，访问 `langbase-local.yf-dev2.netease.com:3100`

开发环境下，请求如果需要走 Mock 服务器，请在 api 中添加 useMock 参数

```
function get(workspaceId: string): Promise<WorkspaceModel> {
  return request({
    useMock: true,
    url: `/api/workspace/${workspaceId}`,
  });
}
```

Mock 服务器的 请求、响应、示例 等均由 openapi 文件夹内的 yml 生成，修改 yml 会同步生效。

## 技术栈

- Vite v2 + React v17 + Typescript v6 + Eslint + Prettier，项目初始化自[此模板](https://github.com/SafdarJamal/vite-template-react)
- css 样式库: [styled-components v5](https://styled-components.com/docs/basics#getting-started)
- 前端路由库: [react-router v6](https://reactrouter.com/en/main/start/tutorial)
- 组件库: [antd v5](https://ant-design.antgroup.com/components/button-cn)
- 接口 Mock: [mockserver](https://www.mock-server.com/#what-is-mockserver)

## Steps in Vscode

#### (works with better with this template)

1. Install Eslint and prettier extension for vs code.
2. Make Sure Both are enabled
3. Make sure all packages are Installed. (Mostly Eslint and prettier in node_modules)
4. Enable formatOnSave of vs code
5. Open a .tsx file and check if the bottom right corners of vs code have Eslint and Prettier with a double tick

![Screenshot (253)_LI](https://user-images.githubusercontent.com/52120562/162486286-7383a737-d555-4f9b-a4dd-c4a81deb7b96.jpg)

如果保存自动 format 的功能无法起作用可以尝试:

1. CTRL+ SHIFT+P
2. 格式化文档（在弹出栏中）
3. 选择 Format Document
4. 选择 Configure Default Formatter...
5. 选择 Prettier - Code formatter
