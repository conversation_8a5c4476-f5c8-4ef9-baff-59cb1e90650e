import json
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

# 读取JSON文件
with open('1.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 准备数据
excel_data = []
for item in data:
    # 将标签列表转换为字符串
    tags = '、'.join(item['tag']) if 'tag' in item else ''
    
    excel_data.append({
        '音色名称': item.get('voiceName', ''),
        '音色ID': item.get('voiceId', ''),
        '供应商': item.get('providerKind', ''),
        '标签': tags,
        '试听链接': item.get('voiceUrl', '')
    })

# 创建DataFrame
df = pd.DataFrame(excel_data)

# 创建一个工作簿
wb = Workbook()
ws = wb.active
ws.title = "音色列表"

# 添加表头
headers = ['音色名称', '音色ID', '供应商', '标签', '试听链接']
for col_idx, header in enumerate(headers, 1):
    ws.cell(row=1, column=col_idx, value=header)

# 添加数据
for row_idx, row_data in enumerate(excel_data, 2):
    for col_idx, header in enumerate(headers, 1):
        ws.cell(row=row_idx, column=col_idx, value=row_data[header])

# 设置列宽
column_widths = [15, 25, 15, 20, 80]
for i, width in enumerate(column_widths, 1):
    ws.column_dimensions[get_column_letter(i)].width = width

# 设置样式
thin_border = Border(
    left=Side(style='thin'),
    right=Side(style='thin'),
    top=Side(style='thin'),
    bottom=Side(style='thin')
)

header_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
row_fill = PatternFill(start_color="FFFDE7", end_color="FFFDE7", fill_type="solid")

# 应用样式到所有单元格
for row in ws.iter_rows(min_row=1, max_row=len(excel_data) + 1, min_col=1, max_col=len(headers)):
    for cell in row:
        cell.alignment = Alignment(wrap_text=True, vertical='center')
        cell.border = thin_border
        if cell.row == 1:  # 表头行
            cell.fill = header_fill
            cell.font = Font(bold=True)
        else:
            # 交替行颜色
            if cell.row % 2 == 0:
                cell.fill = row_fill

# 保存工作簿
wb.save("音色列表.xlsx")

print("Excel文件已创建: 音色列表.xlsx") 