diff --git a/node_modules/@antv/xflow/dist/index.css b/node_modules/@antv/xflow/dist/index.css
index c02add0..7562b53 100644
--- a/node_modules/@antv/xflow/dist/index.css
+++ b/node_modules/@antv/xflow/dist/index.css
@@ -401,15 +401,15 @@
   stroke-dasharray: 8px, 2px;
 }
 .dag-extension-container .x6-edge.x6-edge-selected path:nth-child(2) {
-  stroke: #1890ff;
+  /* stroke: #1890ff; */
   stroke-width: 2px;
 }
 .dag-extension-container .x6-edge:hover path:nth-child(2) {
-  stroke: #1890ff;
+  /* stroke: #1890ff; */
   stroke-width: 2px;
 }
 .dag-extension-container .x6-edge.hoverHighlight path:nth-child(2) {
-  stroke: #1890ff;
+  /* stroke: #1890ff; */
   stroke-width: 2px;
 }
 .dag-extension-container .x6-port .xflow-port-group .xflow-port-arrow {
