diff --git a/node_modules/@antv/xflow-core/es/command-contributions/command-contribution.js b/node_modules/@antv/xflow-core/es/command-contributions/command-contribution.js
index 9cc9a83..db1a4dc 100644
--- a/node_modules/@antv/xflow-core/es/command-contributions/command-contribution.js
+++ b/node_modules/@antv/xflow-core/es/command-contributions/command-contribution.js
@@ -29,7 +29,7 @@ let XFlowCommandContribution = class XFlowCommandContribution {
                         eventName: 'node:moving',
                         callback: ({ node }) => __awaiter(this, void 0, void 0, function* () {
                             const isGroup = node.prop('isGroup');
-                            if (isGroup) {
+                            if (isGroup && !node.parent) {
                                 node.prop('originPosition', node.getPosition());
                                 return;
                             }
