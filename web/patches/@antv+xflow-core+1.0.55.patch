diff --git a/node_modules/@antv/xflow-core/es/command-contributions/command-contribution.js b/node_modules/@antv/xflow-core/es/command-contributions/command-contribution.js
index da27af5..9cc9a83 100644
--- a/node_modules/@antv/xflow-core/es/command-contributions/command-contribution.js
+++ b/node_modules/@antv/xflow-core/es/command-contributions/command-contribution.js
@@ -58,7 +58,11 @@ let XFlowCommandContribution = class XFlowCommandContribution {
                             const childs = group.getChildren();
                             if (childs) {
                                 childs.forEach(child => {
-                                    const bbox = child.getBBox().inflate(12);
+                                    // 如果是边，不需要重置画布大小
+                                    if (graph.isEdge(child)) {
+                                        return;
+                                    }
+                                    const bbox = child.getBBox().inflate(80);
                                     const corner = bbox.getCorner();
                                     if (bbox.x < x) {
                                         x = bbox.x;
