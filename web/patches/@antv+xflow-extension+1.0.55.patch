diff --git a/node_modules/@antv/xflow-extension/es/canvas-dag-extension/contributions/dag.js b/node_modules/@antv/xflow-extension/es/canvas-dag-extension/contributions/dag.js
index 9dbf452..afed018 100644
--- a/node_modules/@antv/xflow-extension/es/canvas-dag-extension/contributions/dag.js
+++ b/node_modules/@antv/xflow-extension/es/canvas-dag-extension/contributions/dag.js
@@ -218,7 +218,7 @@ let DagHooksContribution = class DagHooksContribution {
                     name: 'dag-add-edge',
                     handler: (args) => __awaiter(this, void 0, void 0, function* () {
                         const { layout = LayoutEnum.TOP_BOTTOM } = yield this.propConfig.getConfig();
-                        const cellFactory = (edgeConfig) => __awaiter(this, void 0, void 0, function* () {
+                        const cellFactorya = (edgeConfig) => __awaiter(this, void 0, void 0, function* () {
                             const cell = new XFlowEdge(Object.assign(Object.assign({}, edgeConfig), { id: edgeConfig.id, source: {
                                     cell: edgeConfig.source,
                                     port: edgeConfig.sourcePortId,
@@ -231,17 +231,17 @@ let DagHooksContribution = class DagHooksContribution {
                                     anchor: {
                                         name: layout === LayoutEnum.TOP_BOTTOM ? 'center' : 'right',
                                     },
-                                }, attrs: {
+                                }, attrs: Object.assign({
                                     line: {
                                         strokeDasharray: '',
                                         targetMarker: '',
                                         stroke: '#d5d5d5',
                                         strokeWidth: 1,
                                     },
-                                }, data: Object.assign({}, edgeConfig) }));
+                                }, edgeConfig.attrs), data: Object.assign({}, edgeConfig) }));
                             return cell;
                         });
-                        args.cellFactory = cellFactory;
+                        args.cellFactory = cellFactorya;
                     }),
                 }),
                 hooks.addEdge.registerHook({
