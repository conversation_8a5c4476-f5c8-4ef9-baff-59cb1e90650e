import csv

# 定义过滤条件
def is_valid_name(name):
    # 检查是否包含'测试'或'test'
    if '测试' in name or 'test' in name:
        return False
    # 检查是否为纯数字
    if name.isdigit():
        return False
    return True

# 读取CSV文件并过滤数据
def filter_applications(filename):
    valid_count = 0
    with open(filename, mode='r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # 检查name列是否为基础应用
            if row['name'] != '基础应用':
                # 使用定义的函数检查name是否有效
                if is_valid_name(row['name']):
                    valid_count += 1

    return valid_count

# 调用函数并打印结果
filename = './6.csv'
valid_apps_count = filter_applications(filename)
print(f"符合条件的应用数量为: {valid_apps_count}")