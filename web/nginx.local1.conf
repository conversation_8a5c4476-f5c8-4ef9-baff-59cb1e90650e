worker_processes  1;

events {
    worker_connections  1024;
}

http {
  server {
      server_name *.ft.netease.com;

      listen 80;

      location / {
        # horizon 线上
        allow 59.111.22.10;
        allow 59.111.22.11;
        allow 59.111.22.12;
        # horizon 测试
        allow 103.196.65.60;
        allow 103.196.65.52;

        # set $cors_type "";
        # if ($http_origin) {
        #     set $cors_type "cros";
        # }

        # if ($request_method = "OPTIONS") {
        #     set $cors_type "${cors_type}_options";
        # }

        # if ($cors_type = cros_options) {
        #     add_header Access-Control-Allow-Origin "$http_origin" always;
        #     add_header Access-Control-Allow-Methods "$http_access_control_request_method" always;
        #     add_header Access-Control-Allow-Headers "$http_access_control_request_headers" always;
        #     add_header Access-Control-Max-Age "600" always;
        #     add_header Content-Type "text/plain charset=UTF-8";
        #     add_header Content-Length 0;
        #     return 204;
        # }

        # if ($cors_type = cros) {
        #     add_header Access-Control-Allow-Origin "$http_origin" always;
        #     add_header Access-Control-Allow-Methods "$http_access_control_request_method" always;
        #     add_header Access-Control-Allow-Headers "$http_access_control_request_headers" always;
        #     add_header Access-Control-Expose-Headers "Date,Content-Encoding,Vary,Server,Transfer-Encoding,Connection" always;
        #     proxy_pass http://ada-prod-s4x9sp.serverlesssync.yf-online1.service.163.org;
        #     break;
        # }

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host ada-prod-s4x9sp.serverlesssync.yf-online1.service.163.org;
        proxy_set_header X-Proxy-Forwarded-Host $host;
        proxy_set_header X-Proxy-Src-Host $host;
        proxy_pass http://ada-prod-s4x9sp.serverlesssync.yf-online1.service.163.org;
      }

      location ~ /\.svn {
          deny all;
      }

      location ~ /\.git {
          deny all;
      }
  }
}