worker_processes  1;

events {
    worker_connections  1024;
}

error_log /Users/<USER>/project/netease/langbase/web/error.log debug;

http {
  client_max_body_size 200m;
  server {
      listen 80;
      server_name langbase-workflow.yf-onlinetest4.netease.com;
      location / {
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-From-IP $remote_addr;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Remote-Port $remote_port;
        # 本地环境
        proxy_pass http://127.0.0.1:3100;
    }

      location /api/v1 {
        # 测试环境
        # proxy_pass http://langbase-server-test.yf-onlinetest1-gy1.service.gy.ntes;
        # 线上环境
        # proxy_pass http://langbase-server-online.yf-online-gy1.service.gy.ntes;
        # 本地环境
        proxy_pass http://127.0.0.1:8000;
      }

      location ~ /(docs|openapi.json) {
        #proxy_pass http://**************;
        proxy_pass http://*************:8000;
        proxy_set_header Host $host;
      }

      # mock api
      location /api/mock {
        rewrite ^/api/mock/(.*) /$1 break;
        proxy_pass http://127.0.0.1:1080;
      }
  }
}
