{"name": "vite-reactts-eslint-prettier", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3100", "mock": "node mockserver.js --port 1080", "build": "tsc && vite build", "serve": "vite preview", "lint:fix": "eslint ./src --ext .jsx,.js,.ts,.tsx --quiet --fix --ignore-path ./.gitignore", "lint:format": "prettier  --loglevel warn --write \"./**/*.{js,jsx,ts,tsx,css,md,json}\" ", "lint": "yarn lint:format && yarn lint:fix ", "postinstall": "patch-package", "type-check": "tsc"}, "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^5.2.6", "@antv/xflow": "^1.0.55", "@antv/xflow-hook": "^1.0.55", "@babel/plugin-proposal-class-properties": "^7.18.6", "@brianmcallister/react-auto-scroll": "^1.1.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/helpers": "^0.1.12", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/react": "^0.1.12", "@dnd-kit/sortable": "^10.0.0", "@microsoft/fetch-event-source": "^2.0.1", "@music/ct-langbase": "^1.0.7", "@music/ct-pc-paopaocaller": "^1.2.10", "@music/lang": "3.0.12", "@uiw/codemirror-extensions-langs": "^4.21.20", "@uiw/react-codemirror": "^4.21.20", "ahooks": "^3.7.8", "antd": "5.20.3", "axios": "^1.5.0", "axios-mock-adapter": "^1.22.0", "classnames": "^2.5.1", "dayjs": "1.11.1", "file-saver": "^2.0.5", "highlight.js": "^11.9.0", "identicon.js": "^2.3.3", "jwt-decode": "^3.1.2", "less": "^4.2.0", "less-loader": "^11.1.3", "lodash": "^4.17.21", "lowlight": "^3.1.0", "patch-package": "^8.0.0", "prismjs": "^1.29.0", "qs": "^6.11.2", "querystring": "^0.2.1", "rc-tween-one": "^3.0.6", "rc-virtual-list": "^3.11.2", "react": "^18.2.0", "react-diff-viewer": "^3.1.1", "react-dom": "^18.2.0", "react-file-reader-input": "^2.0.0", "react-js-cron": "^5.0.1", "react-json-view": "^1.21.3", "react-lowlight": "^3.0.0", "react-markdown": "^9.0.0", "react-router-dom": "^6.16.0", "react-simple-code-editor": "^0.13.1", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-code-extra": "^1.0.1", "remark-gfm": "^4.0.0", "styled-components": "^6.0.8", "uuid": "^9.0.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-transform-class-properties": "^7.22.5", "@types/identicon.js": "^2.3.2", "@types/lodash": "^4.14.198", "@types/markdown-it": "^13.0.2", "@types/node": "^20.8.4", "@types/qs": "^6.9.10", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "@types/styled-components": "^5.1.27", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.5", "@vitejs/plugin-react": "^4.1.0", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-simple-import-sort": "^10.0.0", "mockserver-client": "^5.15.0", "mockserver-node": "^5.15.0", "pre-commit": "^1.2.2", "prettier": "^3.0.3", "typescript": "^5.2.2", "vite": "^4.5.0", "yaml": "^2.3.2"}, "resolutions": {"styled-components": "^5"}, "pre-commit": "lint", "license": "MIT"}