/* eslint-disable @typescript-eslint/no-var-requires */
var mockserver = require('mockserver-node');
var mockServerClient = require('mockserver-client').mockServerClient;
var path = require('path');
var fs = require('fs');
var YAML = require('yaml');

const OPEN_API_PATH = '../openapi';
const PORT = getStartPort();

let client;

startMockServer();

function startMockServer() {
  mockserver
    .start_mockserver({
      serverPort: PORT,
      jvmOptions: [
        '-Dmockserver.enableCORSForAllResponses=true',
        '-Dmockserver.corsAllowMethods="CONNECT, DELETE, GET, HEAD, OPTIONS, POST, PUT, PATCH, TRACE"',
        '-Dmockserver.corsAllowHeaders="Allow, Content-Encoding, Content-Length, Content-Type, ETag, Expires, Last-Modified, Location, Server, Vary, Authorization"',
        '-Dmockserver.corsAllowCredentials=true',
        '-Dmockserver.corsMaxAgeInSeconds=300',
      ],
      // verbose: true,
      // trace: true,
    })
    .then(startClient);

  process.on('beforeExit', () => {
    mockserver.stop_mockserver({ serverPort: 1080 });
  });
}

async function startClient() {
  console.log('mock 服务器启动成功, 开始提交 OpenAPI 文档');
  await commitOpenAPIDoc();
  console.log(
    `OpenAPI 文档提交成功，你可以访问 http://127.0.0.1:${PORT}/mockserver/dashboard 查看所有生效的接口`,
  );
  console.log(
    `访问 http://127.0.0.1:${PORT}/xxxx 获取mock数据，如: http://127.0.0.1:${PORT}/workspace`,
  );
  console.log('开始监听 openapi 文件夹的变动...');
  fs.watch(path.join(__dirname, OPEN_API_PATH), async (eventType, filename) => {
    console.log(`${filename} 文件变更, 重新提交 OpenAPI 文档..`);
    await commitOpenAPIDoc();
    console.log(`开始监听 openapi 文件夹的变动...`);
  });
}

async function commitOpenAPIDoc() {
  if (!client) {
    client = mockServerClient('localhost', PORT);
  } else {
    await client.reset();
  }
  const docs = await getOpenAPIMergeDoc();

  for (let i = 0; i < docs.length; i++) {
    const { docName, content } = docs[i];
    await client
      .openAPIExpectation({
        specUrlOrPayload: content,
      })
      .then(
        function () {
          console.log(`${docName} expectation created`);
        },
        function (error) {
          console.error(`${docName} expectation error`, error);
        },
      );
  }
}

async function getOpenAPIMergeDoc() {
  const apiFiles = await listDirFile(path.join(__dirname, OPEN_API_PATH));
  const apiDocs = apiFiles.filter((name) => name !== 'common.yml');
  const commonFile = await readOpenAPIFile('common.yml');
  let commonYaml;
  try {
    commonYaml = YAML.parse(commonFile);
  } catch (error) {
    console.log('common.yml 文件格式错误', error);
    return []
  }
  const commonComponents = commonYaml.components;

  return Promise.all(apiDocs.map(merge));

  async function merge(docName) {
    const docContent = await readOpenAPIFile(docName);
    let docContentMergeCommon = docContent.replace(
      '- url: localhost:5000',
      `- url: http://127.0.0.1:${PORT}`,
    );
    if (docContent.includes('common.yml')) {
      docContentMergeCommon = `${docContentMergeCommon.replace(
        /common.yml/g,
        '',
      )}`;
    }
    let docYaml;
    try {
      docYaml = YAML.parse(docContentMergeCommon);
    } catch (error) {
      console.log(`${docName} 文件格式错误`, error);
      return null
    }
    const components = docYaml.components || {};
    ['parameters', 'schema', 'responses'].forEach((key) => {
      components[key] = {
        ...(components[key]||{}),
        ...(commonComponents[key] || {})
      }
    })
    docYaml.components = components;
    docContentMergeCommon = YAML.stringify(docYaml);

    return {
      docName,
      content: docContentMergeCommon,
    };
  }
}

async function listDirFile(dirPath) {
  return new Promise((res, rej) => {
    fs.readdir(dirPath, (err, files) => {
      if (err) {
        rej(err);
      }
      res(files);
    });
  });
}

async function readOpenAPIFile(filename) {
  return readFile(path.join(__dirname, `${OPEN_API_PATH}/${filename}`));
}

async function readFile(filePath) {
  return new Promise((res, rej) => {
    fs.readFile(filePath, (err, content) => {
      if (err) {
        rej(err);
      }
      res(content.toString());
    });
  });
}

function getStartPort() {
  const args = process.argv.slice();
  const portIndex = args.indexOf('--port');
  const portArg = portIndex !== -1 && args[portIndex + 1];
  return +portArg || 1089;
}
