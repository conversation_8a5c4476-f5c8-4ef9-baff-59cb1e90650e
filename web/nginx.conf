worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /tmp/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '{"remoteAddr": "$remote_addr", "remoteUser": "$remote_user", "timeLocal":
                          "$time_local", "request": "$request", "bodyBytesSent": "$body_bytes_sent", "requestTime": "$request_time",
                          "status": "$status", "requestLength": "$request_length", "httpReferrer":
                          "$http_referer", "httpUserAgent": "$http_user_agent", "upstreamAddr": "$upstream_addr",
                          "upstreamResponseLength": "$upstream_response_length",
                          "upstreamResponseTime": "$upstream_response_time", "upstreamStatus":
                          "$upstream_status"}'

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;
    client_max_body_size 200m;

    server {
        listen 8080;
        # gzip config
        client_max_body_size 200m;
        gzip on;
        gzip_min_length 1k;
        gzip_comp_level 9;
        gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
        gzip_vary on;
        gzip_disable "MSIE [1-6]\.";

        root /usr/share/nginx/html;

        # 增加缓存
        location ~* \.(css|js)$ {
          expires 30d;
          add_header Cache-Control "public, no-transform";
        }

        # 增加心跳检测
        location /health {
          return 200;
        }

        location / {
            # used with browserHistory
            try_files $uri /index.html;
            add_header Cache-Control no-store;
        }

      location /api/v1 {
          proxy_pass http://langbase-server-online.yf-online-gy1.service.gy.ntes;
      }

      location ~ ^/api/v1/(app/(workflow-runs|trigger)|(conversation|agentw-chat|chat|completion|message))$ {
          proxy_pass http://langbase-service-online.yf-online-gy1.service.gy.ntes;
      }
    }
}
