

# 从MySQL到DDB：现代化API服务的演进

在现代软件开发中，随着系统的复杂性和分布式架构的普及，传统的数据库访问方式逐渐暴露出一些局限性。为了提高系统的稳定性，灵活性和可扩展性，我们尝试将数据库迁移到DDB，并改造数据库访问为HTTP请求的方式。这篇博文将分享我们在这一过程中遇到的挑战和解决方案。


## 背景

在我们的项目中，数据库访问是通过SQLAlchemy的Session对象实现的。随着项目的扩展，我们希望将数据库访问抽象成HTTP请求，以便更好地支持微服务架构。我们的目标是：

1. **保持现有代码的兼容性**：在不修改现有代码的情况下，将`get_db`替换为`get_http_db`。
2. **支持链式调用**：保持SQLAlchemy风格的链式查询。
3. **支持事务提交**：提供类似于SQLAlchemy的`commit`功能。
4. **支持直接属性修改**：允许通过对象属性直接修改数据。

## 架构演进

### 传统架构
在传统的数据库访问架构中，应用服务器直接与数据库交互：

```mermaid
graph TD;
    A[Client] -->|HTTP Request| B[API Server];
    B -->|SQL Query| C[SQLAlchemy Session];
    C -->|Execute| D[MySQL];
    D -->|Results| C;
    C -->|Data Objects| B;
    B -->|Response| A;

    style D fill:#f96,stroke:#333,stroke-width:2px
    style C fill:#9cf,stroke:#333,stroke-width:2px
```

### 新架构
在新的HTTP请求架构中, 我们通过HTTP Session代理所有的数据库操作：

```mermaid
graph TD;
    A[Client] -->|HTTP Request| B[API Server];
    B -->|Query| C[HTTPSession];
    C -->|HTTP Call| D[Remote Service];
    D -->|Database Op| E[DDB];
    E -->|Results| D;
    D -->|Response| C;
    C -->|Data| B;
    B -->|Response| A;

    style E fill:#f96,stroke:#333,stroke-width:2px
    style C fill:#9cf,stroke:#333,stroke-width:2px
    style D fill:#9f9,stroke:#333,stroke-width:2px
```

主要变化：
1. 移除了直接的数据库访问
2. 引入了Remote Service层
3. 所有数据库操作都通过HTTP请求进行
4. 提供了更好的服务隔离和扩展性

## 接口设计

在将数据库访问改造为HTTP请求的过程中，我们设计了一套统一的接口规范，以确保系统的一致性和可维护性。

### 基础接口

我们设计了以下几个基础接口来支持常见的数据操作：

#### 1. 查询接口

```python
GET /langbase/inner/common/search/{resource}/
```

**请求参数**：
```python
{
    "filters": [
        {
            "name": "id",       // 需要的字段名
            "condition": "eq",   // 过滤条件
            "value": "123"      // 过滤值
        },
        {
            "name": "id",
            "condition": "in",
            "values": [1, 2, 3]
        }
    ],
        "page": 1, // 页数
        "pageSize": 100 // 每页大小
        "orderBy": "created_at"
        "asc": true
}
```
### 2. 创建接口

```
GET /langbase/inner/common/create/{resource}/
```

**请求参数**

```
{
    "values": [
        {
            "id": 123,
            "groupId": "abc"      
        },{...}
    ]
}
```



#### 2. 更新接口

```python
POST /langbase/inner/common/update/{resource}/
```

请求参数

```
{
    "uniqueKey"： "1234"
    "value": {
            "id": 123,
            "groupId": "abc"      
        }
}
```



#### 3. 删除接口

```python
POST /langbase/inner/common/delete/{resource}/
```

**请求参数**：
```python
{
    "uniqueKey"： "1234"
}
```



## 相关问题

迁移到DDB后，表结构还有一些联表查询都需要发生一些改变。

### ID变化

由于之前 MySQL 使用的是 uuid 的结构，迁移到DDB后，需要改成自增的ID，因此，在DDB中会使用${resource}_id来关联原id，而新的资源ID是一个自增的数。

### 联表查询

DDB不支持联表查询，因此需要python服务这边做多次查询再组装，其中有一个很多频繁的组装逻辑：

1. created_by：大量表的查询都需要组装member表，但是member表没有迁移到ddb，因此需要python服务这边做一些通用化处理。
2. config_id：APP表中会关联config表的信息，之前都是联表直接查出来的，现在需要多次查询，并进行拼装。



## 挑战与解决方案

### 1. 保持代码兼容性

**挑战**：我们希望在不修改现有代码的情况下，将数据库访问改为HTTP请求。这意味着我们需要提供一个与SQLAlchemy Session接口兼容的HTTP Session。

```python
# 原有代码
db = Depends(get_db)

# 新的HTTP请求式
db = Depends(get_http_db)
```

**解决方案**：通过使用依赖注入，我们可以轻松地将`get_db`替换为`get_http_db`，而无需修改其他代码。

### 2. 支持链式调用

**挑战**：SQLAlchemy支持链式调用，如`query.filter().order_by()`。我们需要在HTTP请求中实现类似的功能。

```python
# 示例代码
account = db.query(Account).filter(Account.id == id).first()
```

**解决方案**：通过在每个方法中返回`self`，我们可以实现类似SQLAlchemy的链式调用。

### 3. 支持事务提交

**挑战**：在SQLAlchemy中，`commit`方法用于提交事务。我们需要在HTTP请求中实现类似的功能。

```python
# 示例代码
db.update(xxx)
db.commit()
```

**解决方案**：我们在`HTTPSession`中实现了一个`commit`方法，用于批量提交所有待更新的对象。

### 4. 支持直接属性修改

**挑战**：在SQLAlchemy中，可以通过直接修改对象属性来更新数据。我们希望在HTTP请求中也能实现这一点。

```python
# 示例代码
user = await set_admin(db, mail)
user.is_admin = True
db.commit()
```

**解决方案**：通过重载`__setattr__`方法，我们可以跟踪属性的更改，并在`commit`时发送更新请求。

### 5. 处理布尔值

**挑战**：在SQLAlchemy中，布尔值可以直接使用True和False，但在HTTP请求中需要将其转换为True和False。

```python
# 示例代码
user = db.query(User).filter(User.is_admin == True).first()
```

```python
# 直接使用arg.right.value会报错
getattr(arg.right, 'value', arg.right)

# 报错
# Neither 'False_' object nor 'Comparator' object has an attribute 'value'
```

**解决方案**：通过在`HTTPQuery`中处理`sql.elements.False_`和`sql.elements.True_`对象，我们可以正确地处理布尔值。

```python
if arg.right.__class__.__name__ in ('True_', 'False_'):
    value = arg.right.__class__.__name__ == 'True_'
```

### 6. 处理复杂类型

**挑战**：在实际开发中，我们经常需要处理各种复杂的数据类型，包括：
- 枚举类型（Enum）
- 布尔值（SQLAlchemy的True_/False_）
- 列表类型（用于in查询）
- 基础类型（int, str, float等）

```python
# 处理枚举类型
users = db.query(User).filter(User.status == UserStatus.ACTIVE).all()

# 处理布尔值
admins = db.query(User).filter(User.is_admin == True).all()

# 处理列表查询
active_users = db.query(User).filter(User.status.in_([
    UserStatus.ACTIVE, 
    UserStatus.PENDING
])).all()

# 处理基础类型
user = db.query(User).filter(User.age >= 18).first()
```

**解决方案**：实现了一个通用的值提取方法来处理这些复杂类型：

```python
def _extract_value(self, value):
    """提取各种类型的值"""
    # 处理列表类型（用于 in 查询）
    if isinstance(value, list):
        return [self._extract_value(item) for item in value]
    
    # 处理基础类型
    if isinstance(value, (int, float, str, bool)):
        return value
        
    # 处理 SQLAlchemy 的布尔类型
    if value.__class__.__name__ in ('True_', 'False_'):
        return value.__class__.__name__ == 'True_'
        
    # 处理枚举类型
    if isinstance(value, Enum) or hasattr(value, 'value'):
        return value.value
        
    # 其他情况
    return getattr(value, 'value', value)
```

### 7. 处理操作符类型

**挑战**：SQLAlchemy支持多种操作符（>=, <=, !=, in_, like等），我们需要将这些操作符映射到HTTP请求的查询条件中。

```python
# 大于等于
users = db.query(User).filter(User.age >= 18).all()

# 不等于
users = db.query(User).filter(User.status != 'deleted').all()

# 包含
users = db.query(User).filter(User.id.in_(['1', '2', '3'])).all()

# 模糊查询
users = db.query(User).filter(User.name.like('%John%')).all()
```

**解决方案**：我们实现了一个操作符映射方法，将SQLAlchemy的操作符转换为对应的条件字符串：

```python
def _get_operator_condition(self, op):
    """将 SQLAlchemy 操作符转换为对应的条件字符串"""
    operator_map = {
        '>=': 'gte',
        '<=': 'lte',
        '>': 'gt',
        '<': 'lt',
        '!=': 'ne',
        '==': 'eq',
        'in_op': 'in',
        'like_op': 'like'
    }
    return operator_map.get(op, op)
```

这样我们就可以支持各种复杂的查询操作

## 具体实现

主要是需要实现 HTTPSession、HTTPQuery、ModelProxy 三大类，他们之间的关系如下：

```mermaid
classDiagram
    class HTTPSession {
        +base_url: str
        +name: str
        +client: httpx.Client
        -_to_commit: List
        +query(model_class)
        +add(obj)
        +delete(obj)
        +commit()
        +get(path, params)
        +create(params)
        +update(params)
        +remove(params)
    }
    
    class HTTPQuery {
        +session: HTTPSession
        +model_class: str
        -_filters: List
        -_offset: int
        -_limit: int
        -_order_by: str
        -_is_asc: bool
        +filter(*args)
        +first()
        +all()
        +count()
        +order_by(field)
        +offset(offset)
        +limit(limit)
    }
    
    class ModelProxy {
        -_session: HTTPSession
        -_data: dict
        -_changes: dict
        +model_dump()
        +__getattribute__(name)
        +__setattr__(name, value)
    }
    
    HTTPSession ..> HTTPQuery : creates
    HTTPSession ..> ModelProxy : manages
    HTTPQuery ..> ModelProxy : returns
```

这三个类之间的主要关系是：

1. **HTTPSession 创建 HTTPQuery**：
   - 通过 `query()` 方法创建查询对象
   - 管理事务和提交操作

2. **HTTPSession 管理 ModelProxy**：
   - 跟踪对象的变更
   - 处理提交操作

3. **HTTPQuery 返回 ModelProxy**：
   - 查询结果被包装成 ModelProxy 对象
   - 支持链式调用和属性访问

### 1. HTTPSession 类

`HTTPSession` 类是我们实现的核心，它模拟了 SQLAlchemy 的 Session 接口，并通过 HTTP 请求与远程服务交互。在实现的过程中，需要充分考虑之前说的挑战1，3，需要具备良好的代码兼容性和各种功能的适配性（事务提交）。通过实现该类，我们可以实现无缝从db查询切换到http查询。

```python
class HTTPSession:
    def __init__(self, base_url: str, name: Optional[str] = None):
        self.base_url = base_url
        self.name = name
        self.client = httpx.Client()
        self._to_commit = []

    def query(self, model_class):
        self.name = model_class.__tablename__.replace('tb_', '')
        return HTTPQuery(self, model_class)

    def add(self, obj):
        self._to_commit.append(('add', obj))  

    def delete(self, obj):
        self._to_commit.append(('delete', obj)) 

    def commit(self):
        for operation, obj in self._to_commit:
            if operation == 'delete':
                request_data = {
                    "uniqueKey": obj.id
                }
                self.remove(request_data)
            elif operation == 'add':
                request_data = {
                    "values": [{c.name: getattr(obj, c.name)
                               for c in obj.__table__.columns
                               if not c.name.startswith('_')}]
                }
                json = self.create(request_data)
                data = json.get('data', {})
                obj.__dict__.update(normalize_fields(
                    self.name, data.get('values', [{}])[0]))
            elif operation == 'update':
                request_data = {
                    "uniqueKey": obj._data["id"],
                    "value": {
                        **obj._changes
                    }
                }
                self.update(request_data)
                obj._changes.clear()

        self._to_commit.clear()

    def refresh(self, obj):
        if hasattr(obj, 'id'):
            return obj
	# …… 请求处理

```

### 2. HTTPQuery 类

最复杂的视线，主要是为了模拟了 SQLAlchemy 查询接口的链式调用，同时支持多种filter形式，同时支持`order_by`，`limit`，`offset`

```python
class HTTPQuery:
    def __init__(self, session: 'HTTPSession', model_class: Any):
        self.session = session
        #  DDB的映射关系，tb_user -> user
        self.model_class = model_class.__tablename__.replace('tb_', '')
        self._filters = [{
            "name": "deleted",
            "condition": "eq",
            "value": 0
        }]
        self._offset = None
        self._limit = None
        self._order_by = None
        self._is_asc = True
        self._last_query_result = None  # 用于存储最后一次查询结果

    def order_by(self, field: str):
        # 获取字段的字符串表示，例如 tb_token.created_at DESC,
        field_str = field.__str__()

        # 判断是否为降序
        if 'DESC' in field_str:
            self._order_by = field_str.split(
                '.')[1].replace(' DESC', '')  # 提取字段名并去掉 DESC
            self._is_asc = False  # 设置为降序
        else:
            self._order_by = field_str.split(
                '.')[1].replace(' ASC', '')  # 提取字段名
            self._is_asc = True  # 默认升序

        return self

    def _extract_value(self, value):
        # 处理基础类型，之前展示过……

    def _get_operator_condition(self, op):
       # 处理操作符，之前展示过……

    def filter(self, *args):
        # 解析过滤条件
        for arg in args:
            if hasattr(arg, 'left') and hasattr(arg, 'right'):
                filter_item = {
                    "name": f"{self.model_class}_id" if arg.left.name == 'id' else arg.left.name,
                    "condition": self._get_operator_condition(arg.operator.__name__),
                    "value": self._extract_value(arg.right)
                }
                self._filters.append(filter_item)
        return self

    def first(self):
        # 构建查询参数
        params = {
            "filters": self._filters,
            "page": 1,
            "pageSize": 1
        }
        response = self.session.get(self.model_class, params=params)

        # ……解析嵌套的响应结构，处理返回值

    def all(self):
        params = self._build_params()
        response = self.session.get(self.model_class, params=params)

        if response and response.get('code') == 200:
          # ……处理返回值
        return []

    def count(self):
        # 如果已经有查询结果，直接返回total
        if self._last_query_result:
            return self._last_query_result.get('total', 0)
        # 否则执行一次查询以获取total
        self.first()
      
      def _build_params(self):
        # 基础参数
        params = {
            "filters": self._filters,
            "page": 1,
            "pageSize": 100  # 默认页大小
        }

        # 添加分页参数
        if self._offset is not None:
            params['page'] = (self._offset // params['pageSize']) + 1
        if self._limit is not None:
            params['pageSize'] = self._limit

        # 添加排序参数
        if self._order_by:
            params['orderBy'] = self._order_by
            params['asc'] = self._is_asc

    def offset(self, offset: int):
        self._offset = offset
        return self

    def limit(self, limit: int):
        self._limit = limit
        return self

    def delete(self):
        params = self._build_params()
        self.session.delete(
            f"/{self.model_class.__name__.lower()}", params=params)

  
 
```

### 3. ModelProxy 类

`ModelProxy` 类用于代理数据对象，**支持直接属性修改** 。

```python
class ModelProxy:
    def __init__(self, session: 'HTTPSession', data: dict):
        self._session = session
        self._data = data
        self._changes = {}

        # 设置初始数据的属性
        for key, value in data.items():
            super().__setattr__(key, value)

    def __getattribute__(self, name):
        # 优先处理特殊属性和内部属性
        try:
            return super().__getattribute__(name)
        except AttributeError:
            if name in self._data:
                return self._data.get(name)

    def __setattr__(self, name, value):
        if name.startswith('_'):
            super().__setattr__(name, value)
            return

        if name not in self._data or self._data[name] != value:
            self._changes[name] = value
            self._data[name] = value
            self._session._to_commit.append(('update', self))  # 针对直接修改属性的，需要将更新字段添加到commit事务列表中

        super().__setattr__(name, value)

    def model_dump(self):
        """Return the underlying data dictionary."""
        return self._data
```

### 4. 接口映射

为了保持与SQLAlchemy的兼容性，我们将SQLAlchemy的操作映射到相应的HTTP请求：

```mermaid
graph LR
    A[SQLAlchemy] --> B[HTTP Request]
    B1[query.filter] --> C1[GET /search]
    B2[delete] --> C2[_delete.append] --> B5[commit]
    B3[update] --> C3[_update.append] --> B5[commit]
    B4[add] --> C4[_add.append] --> B5[commit]
    B5[commit] --> C5[POST /update /delete /add]
```

#### 查询操作映射

```python
def get(self, path: str, params: Optional[dict] = None) -> Any:
        url = f"{self.base_url}/langbase/inner/common/search/{path}/"
        response = self.client.post(url, json=params)
        return self._handle_response(response)

def create(self, params: Optional[dict] = None):
    url = f"{self.base_url}/langbase/inner/common/add/{self.name}/"
    response = self.client.post(url, json=params)
    return self._handle_response(response)

def update(self, params: Optional[dict] = None):
    url = f"{self.base_url}/langbase/inner/common/update/{self.name}/"
    response = self.client.post(url, json=params)
    return self._handle_response(response)

def remove(self, params: Optional[dict] = None):
    url = f"{self.base_url}/langbase/inner/common/delete/{self.name}/"
    response = self.client.post(url, json=params)
    return self._handle_response(response)
```

#### 响应处理

为了统一处理响应数据，所有的响应值都会通过 normalize_fields 这个工具函数，对响应字段做封装，以满足原来的接口字段的需求。

```python
def normalize_fields(name: str, items: Union[dict, List[dict]]) -> Union[dict, List[dict]]:
    """统一处理字段的命名和格式化"""
    if isinstance(items, dict):
        items = [items]
        is_single = True
    else:
        is_single = False

    # 处理 id 字段
    for item in items:
        model_id_key = f"{name}_id"
        if model_id_key in item:
            item['id'] = item.pop(model_id_key)

    # 处理 created_by 字段
    created_by_ids = [item['created_by']
                      for item in items if 'created_by' in item]
    if created_by_ids:
        created_by_accounts = fetch_created_by(created_by_ids)
        for item in items:
            if 'created_by' in item:
                created_by_id = item['created_by']
                if created_by_id in created_by_accounts:
                    item['createdBy'] = created_by_accounts[created_by_id]

    return items[0] if is_single else items
```

#### 错误处理

我们还实现了统一的错误处理机制：

```python
    def _handle_response(self, response):
        """统一处理响应"""
        response.raise_for_status()
        res = response.json()
        if res.get('code') != 200:
            error_msg = res.get('debugInfo') or res.get(
                'message')  # 优先使用 debugInfo
            logger_error(f"DDB请求出错：{error_msg}")
            raise Exception(f"DDB请求出错：{error_msg}")
        return res
```

