import openai
import json

# APP_ID = '{{your_app_id}}'
# APP_KEY = '{{your_app_key}}'
END_POINT = 'https://aigw-int.netease.com/v1'
APP_ID = 'zuv0s75y-1ipj-hi'
APP_KEY = 'uz07cwq2lfc4g00n70t8tgb583jpzqzh'
# END_POINT = 'http://127.0.0.1:8080/aliyun/simulation/v1'

model = 'qwq-plus'
# model = 'deepseek-r1-250120'
# model = 'qwq-plus-latest'
# model = 'qwq-32b'

client = openai.OpenAI(
    api_key=f'{APP_ID}.{APP_KEY}',
    base_url=END_POINT,
)

chunks = client.chat.completions.create(
    model=model,
    messages=[
        {
            'role': 'user',
            'content': '告诉我 1+1=?',
        }
    ],
    stream=True,
    max_tokens=1024,
)

reasoning_content = ''
content = ''

# 先流式输出 reasoning_content
for chunk in chunks:
    # 添加检查确保 choices 列表不为空
    usage = getattr(chunk, 'usage', None)
    if usage:
        print('usage: ' + str(usage))
    if chunk.choices and len(chunk.choices) > 0:
        if chunk.choices[0].delta:
            print('reasoning_content: ' + getattr(
                chunk.choices[0].delta, 'reasoning_content', ''))
            if chunk.choices[0].delta.content:
                print('content: ' + chunk.choices[0].delta.content)
    else:
        # 可能是心跳包或其他特殊消息
        print(f"收到没有choices的chunk: {chunk}")

# if __name__ == '__main__':
#     request()
