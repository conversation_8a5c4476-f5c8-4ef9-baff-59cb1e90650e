#!/usr/bin/env python3
# -*- coding: utf-8 -*-


import openai
import json

API_KEY = 'lzN9p6mrCVbgHQBhyYhgx2_pSNb0dDhq1EkJcWe2juw'
END_POINT = 'https://langbase-pre.netease.com/api/v1/app/8a6bb323-d0f4-4ef5-b8e1-7ce1c46c597a/openai'
# END_POINT = 'http://langbase-local.netease.com:8000/api/v1/app/8a6bb323-d0f4-4ef5-b8e1-7ce1c46c597a/openai'

model = 'claude-3-7-sonnet-20250219'


def get_current_weather(location, format='celsius'):
    if location == 'San Francisco, CA':
        return 'sunny, 30' + format
    return 'rain, 20' + format


available_functions = {
    'get_current_weather': get_current_weather,
}

model = 'claude-3-7-sonnet-20250219'

tools = [
    {
        "type": "function",
        "function": {
            "name": "get_current_weather",
            "description": "Get the current weather",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA",
                    },
                    "format": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "The temperature unit to use. Infer this from the users location.",
                    },
                },
                "required": ["location", "format"],
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_n_day_weather_forecast",
            "description": "Get an N-day weather forecast",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA",
                    },
                    "format": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "The temperature unit to use. Infer this from the users location.",
                    },
                    "num_days": {
                        "type": "integer",
                        "description": "The number of days to forecast",
                    }
                },
                "required": ["location", "format", "num_days"]
            },
        }
    },
]

messages = [
    {
        "role": "user",
        "content": "杭州今天天气怎么样，以摄氏度为单位",
    },
]

extra_body = {
    'thinking': {
        'type': 'enabled',
        'budget_tokens': 1024
    }
}

client = openai.OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

resp = client.chat.completions.create(
    model=model,
    messages=messages,
    tool_choice={"type": "auto"},
    tools=tools,
    stream=True,
    max_tokens=3000,
    extra_body=extra_body,
)
# 先流式输出 reasoning_content
for chunk in resp:
    print(chunk.choices[0])
    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
        print('reasoning_content: ' + chunk.choices[0].delta.reasoning_content)
    elif chunk.choices[0].delta.content:
        print('content: ' + chunk.choices[0].delta.content)
