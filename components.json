[{"id": "1", "label": "文本组件", "category": "INPUT", "code": "INPUT_TEXT", "type": "INPUT", "icon": "FontSizeOutlined", "description": "用于输入/展示文本", "outputs": [{"name": "text", "type": "string"}]}, {"id": "1", "label": "图片组件", "category": "INPUT", "code": "INPUT_IMAGE", "type": "INPUT", "icon": "FileImageOutlined", "description": "用于上传/展示图片", "outputs": [{"name": "image", "type": "Image"}]}, {"id": "1", "label": "输出节点", "category": "OUTPUT", "code": "OUTPUT", "type": "OUTPUT", "icon": "CodeOutlined", "description": "用于上传/展示图片", "inputs": []}, {"id": "7", "label": "Switch组件", "icon": "icon-fork", "category": "logic", "code": "SWITCH", "type": "SWITCH", "description": "可以根据输入条件，拆分成多条分支", "inputs": [{"name": "switchVal", "type": "boolean"}], "vars": [{"name": "类型", "type": "string", "value": "boolean", "enum": ["string", "boolean", "number"]}], "outputs": [{"name": "true", "type": "_logic_"}, {"name": "false", "type": "_logic_"}]}, {"id": "7", "label": "Select组件", "icon": "icon-merge", "description": "用于switch的多分支结果合并后进行后续处理", "category": "logic", "code": "SELECT", "type": "SELECT", "inputs": [], "vars": [{"name": "类型", "type": "string", "value": "boolean", "enum": ["string", "boolean", "number"]}], "outputs": [{"name": "output", "type": "any"}]}, {"id": "6", "label": "脚本", "category": "SCRIPT", "code": "SCRIPT", "type": "SCRIPT", "icon": "FunctionOutlined", "inputs": [], "vars": [{"name": "script", "type": "string", "value": ""}, {"name": "scriptType", "type": "string", "enum": ["python", "shell", "groovy"], "value": ""}], "outputs": []}, {"id": "6", "label": "脚本", "category": "SCRIPT", "code": "SCRIPT", "type": "SCRIPT", "icon": "FunctionOutlined", "inputs": [], "vars": [{"name": "script", "type": "string", "value": ""}, {"name": "scriptType", "type": "string", "enum": ["python", "shell", "groovy"], "value": ""}], "outputs": []}]