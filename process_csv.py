#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import pandas as pd

# 创建虚拟环境的命令:
# python3 -m venv venv
# source venv/bin/activate
# pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pandas


def process_csv(input_file, output_file):
    """
    处理CSV文件，根据url和id进行聚合，计算audit_score的平均分并合并opinion

    参数:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    print(f"正在处理文件: {input_file}")

    # 使用pandas读取CSV文件
    df = pd.read_csv(input_file)

    # 创建一个字典用于存储聚合数据
    aggregated_data = {}

    # 遍历数据框的每一行
    for _, row in df.iterrows():
        url = row['url']
        id_val = row['id']
        key = (url, id_val)

        # 如果键不存在，则初始化数据结构
        if key not in aggregated_data:
            aggregated_data[key] = {
                'issue_key': row['issue_key'],
                'credit': row['credit'],
                'audit_scores': [],
                'opinions': []
            }

        # 更新聚合数据
        aggregated_data[key]['audit_scores'].append(float(row['audit_score']))
        # 确保 opinion 是字符串类型
        aggregated_data[key]['opinions'].append(str(row['opinion']))

    # 准备聚合结果
    result = []
    for (url, id_val), data in aggregated_data.items():
        # 计算audit_score的平均分
        avg_score = sum(data['audit_scores']) / len(data['audit_scores'])

        # 合并opinions，使用 "\n" 分隔
        combined_opinions = "\n\n".join(data['opinions'])

        # 添加到结果列表
        result.append({
            'issue_key': data['issue_key'],
            'credit': data['credit'],
            'id': id_val,
            'url': url,
            'avg_audit_score': round(avg_score, 2),  # 保留两位小数
            'combined_opinions': combined_opinions,
            'opinion_count': len(data['opinions'])  # 添加意见数量
        })

    # 将结果转换为数据框并写入CSV文件
    result_df = pd.DataFrame(result)

    # 按平均分降序排序
    result_df = result_df.sort_values(by='avg_audit_score', ascending=False)

    # 写入CSV文件
    result_df.to_csv(output_file, index=False, encoding='utf-8')

    print(f"处理完成，结果已保存到: {output_file}")
    print(f"原始数据行数: {len(df)}")
    print(f"聚合后数据行数: {len(result_df)}")

    # 返回一些统计信息
    stats = {
        'original_rows': len(df),
        'aggregated_rows': len(result_df),
        'unique_urls': len(df['url'].unique()),
        'unique_ids': len(df['id'].unique()),
        'unique_url_id_pairs': len(result_df)
    }

    return stats


if __name__ == "__main__":
    input_file = "Result_techV2.csv"
    output_file = "Result_tech_aggregated.csv"

    stats = process_csv(input_file, output_file)

    print("\n统计信息:")
    for key, value in stats.items():
        print(f"{key}: {value}")
