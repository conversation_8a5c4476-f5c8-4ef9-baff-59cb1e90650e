openapi: "3.0.2"
info:
  title: Member API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /{resourceType}/{resourceID}/members:
    put:
      operationId: addMembers
      tags:
        - member
      summary: Add members
      description: Add members to resource
      parameters:
        - $ref: "#/components/parameters/resourceType"
        - $ref: "#/components/parameters/resourceID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                properties:
                  memberID:
                    type: integer
                    description: Member of resource
                    format: int64
                    example: 1
                  role:
                    type: string
                    example: admin
      responses:
        "200":
          description: OK
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /{resourceType}/{resourceID}/member:
    put:
      operationId: addMember
      tags:
        - member
      summary: Add member
      description: Add member to resource
      parameters:
        - $ref: "#/components/parameters/resourceType"
        - $ref: "#/components/parameters/resourceID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data: 
                  properties:
                    memberID:
                      type: integer
                      description: Member of resource
                      format: int64
                      example: 1
                    role:
                      type: string
                      example: admin
      responses:
        "200":
          description: OK
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    delete:
      operationId: removeMember
      tags:
        - member
      summary: Remove member
      description: Remove member from resource
      parameters:
        - $ref: "#/components/parameters/resourceType"
        - $ref: "#/components/parameters/resourceID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data: 
                  properties:
                    memberID:
                      type: integer
                      description: Member of resource
                      format: int64
                      example: 1
      responses:
        "200":
          description: OK
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    get:
      operationId: listMember
      tags:
        - member
      summary: List member
      description: List member of resource
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - $ref: "#/components/parameters/resourceType"
        - $ref: "#/components/parameters/resourceID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/memberGet"
                      total: 
                        type: integer
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /role: 
    get:
      operationId: listRole
      tags:
        - member
      summary: List permissions of role
      description: List permissions of role
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      defaultRole:
                        type: string
                        example: admin
                      rolePriorityDesc:
                        type: array
                        items:
                          type: string
                          example: admin
                      roles:
                        type: array
                        items:
                          type: object
                          properties:
                            name:
                              type: string
                              example: admin
                            desc:
                              type: string
                              example: admin
                            rules:
                              type: array
                              items:
                                type: object
                                properties:
                                  resources:
                                    type: array
                                    items:
                                      type: string
                                      example: workspace
                                  verbs:
                                    type: array
                                    items:
                                      type: string
                                      example: get
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    resourceType:
      name: resourceType
      in: path
      description: Resource type
      required: true
      schema:
        type: string
        enum:
          - workspace
          - group
          - app
          - dataset
          - plugin
    resourceID:
      name: resourceID
      in: path
      description: Resource ID
      required: true
      schema:
        type: string
  schemas:
    memberGet:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 1
        memberID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        memberName:
          type: string
          description: Member of resource
          example: name
        resourceID:
          description: could be uuid or int64
          type: string
          example: 123e4567-e89b-12d3-a456-************
        resourceType:
          type: string
          example: workspace
        role:
          type: string
          example: admin
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        grantedByID:
          type: integer
          description: Member who grant permission
          format: int64
          example: 1
        grantByName:
          type: string
          description: Member who grant permission
          example: name
