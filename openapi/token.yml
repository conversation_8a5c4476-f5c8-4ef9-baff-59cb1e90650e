openapi: "3.0.2"
info:
  title: Token API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/app/{appID}/token:
    get:
      summary: list token
      operationId: listToken
      tags:
        - token
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/tokenList"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      summary: create token
      operationId: createToken
      tags:
        - token
      parameters:
        - $ref: "#/components/parameters/appID"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/tokenCreated"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/token/{tokenID}/related-resources:
    get:
      summary: list token related resources
      operationId: getTokenRelatedResources
      tags:
        - token
      parameters:
        - $ref: "#/components/parameters/tokenID"
      responses:
        "201":
          description: list
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/tokenRelatedResourcesList"

        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/token/{tokenID}:
    delete:
      summary: delete token
      operationId: deleteToken
      tags:
        - token
      parameters:
        - $ref: "#/components/parameters/tokenID"
      responses:
        "204":
          description: No Content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      summary: update token
      operationId: updateToken
      tags:
        - token
      parameters:
        - $ref: "#/components/parameters/tokenID"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/tokenUpdated"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/temp-token:
    post:
      summary: create temp token, temporary token used for app's web page
      operationId: createTempToken
      tags:
        - token
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                        description: jwt token
                        example: xxx
                      expiredAt:
                        type: string
                        format: date-time
                        description: expired time
                        example: 2020-01-01T00:00:00Z
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    appID:
      name: appID
      in: path
      description: appID
      required: true
      schema:
        type: string
        format: uuid
    tokenID:
      name: tokenID
      in: path
      description: tokenID
      required: true
      schema:
        type: string
        format: uuid
  schemas:
    tokenRelatedResourcesList:
      properties:
        data:
          properties:
            items:
              type: array
              items:
                type: object
                properties:
                  tokenID:
                    type: string
                    format: uuid
                    example: 123e4567-e89b-12d3-a456-************
                  resourceType:
                    type: string
                    example: workspace/group/app
                  resourceID:
                    type: string
                    format: uuid
                    example: 123e4567-e89b-12d3-a456-************
                  createdAt:
                    type: string
                    format: date-time
                    example: 2020-01-01T00:00:00Z
            total:
              type: integer
              example: 1
    tokenList:
      properties:
        data:
          properties:
            items:
              type: array
              items:
                type: object
                properties:
                  id: 
                    type: string
                    format: uuid
                    example: 123e4567-e89b-12d3-a456-************
                  token: 
                    type: string
                    example: app-fadlkfjvzxclv
                  lastUsedAt: 
                    type: string
                    format: date-time
                    example: 2020-01-01T00:00:00Z
                  createdAt:
                    type: string
                    format: date-time
                    example: 2020-01-01T00:00:00Z
            total:
              type: integer
              example: 1
    tokenCreated:
      properties:
        resources:
          type: array
          items:
            $ref: "#/components/schemas/resource"

    tokenGet:
      properties:
        resources:
          type: array
          items:
            $ref: "#/components/schemas/resource"

    tokenUpdated:
      properties:
        resources:
          type: array
          items:
            $ref: "#/components/schemas/resource"

    resource:
        properties:
          id:
            type: string
            format: uuid
            example: 123e4567-e89b-12d3-a456-************
          type:
            type: string
            example: workflow/group/app

