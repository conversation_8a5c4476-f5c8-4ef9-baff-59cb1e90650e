openapi: "3.0.2"
info:
  title: Metrics API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/app/{appID}/conversation-count:
    get:
      operationId: getConversationCount
      tags:
        - metric
      summary: Get conversation count
      description: Get conversation count
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "#/components/parameters/timeRangeStart"
        - $ref: "#/components/parameters/timeRangeEnd"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/conversationCount"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/user-count:
    get:
      operationId: getUserCount
      tags:
        - metric
      summary: Get user count
      description: Get user count
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "#/components/parameters/timeRangeStart"
        - $ref: "#/components/parameters/timeRangeEnd"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/userCount"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/average-interaction-count:
    get:
      operationId: getAverageInteractionCount
      tags:
        - metric
      summary: Get average interaction count
      description: Get average interaction count, interaction count denotes the number of interactions in a conversation
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "#/components/parameters/timeRangeStart"
        - $ref: "#/components/parameters/timeRangeEnd"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/averageInteractionCount"
          "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/token-per-seconds:
    get:
      operationId: getTokenPerSeconds
      tags:
        - metric
      summary: Get token per seconds
      description: Get token per seconds
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "#/components/parameters/timeRangeStart"
        - $ref: "#/components/parameters/timeRangeEnd"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/tokenPerSeconds"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/token-count:
    get:
      operationId: getTokenCount
      tags:
        - metric
      summary: Get token count
      description: Get token count
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "#/components/parameters/timeRangeStart"
        - $ref: "#/components/parameters/timeRangeEnd"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/tokenCount"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

  
  

components:
  parameters:
    appID:
      name: appID
      in: path
      description: App ID
      required: true
      schema:
        type: integer
        format: int64
        example: 1
    timeRangeStart:
      name: timeRangeStart
      in: query
      description: Time range start, default is now
      required: false
      schema:
        type: string
        format: date-time
        example: 2020-01-01T00:00:00Z
    timeRangeEnd:
      name: timeRangeEnd
      in: query
      description: Time range end, default is timeRangeStart - 7 * day
      required: false
      schema:
        type: string
        format: date-time
        example: 2020-01-01T00:00:00Z
  schemas:
    conversationCount:
      type: array
      items:
        type: object
        properties:
          count: 
            type: integer
            format: int64
            example: 1
          date:
            type: string
            format: date
            example: 2020-01-01
    userCount:
      type: array
      items:
        type: object
        properties:
          count: 
            type: integer
            format: int64
            example: 1
          date:
            type: string
            format: date
            example: 2020-01-01
    averageInteractionCount:
      type: array
      items:
        type: object
        properties:
          count: 
            type: integer
            format: int64
            example: 1
          date:
            type: string
            format: date
            example: 2020-01-01
    tokenPerSeconds:
      type: array
      items:
        type: object
        properties:
          count: 
            type: number
            format: float
            example: 1.1
          date:
            type: string
            format: date
            example: 2020-01-01
    tokenCount:
      type: array
      items:
        type: object
        properties:
          count: 
            type: integer
            format: int64
            example: 1
          date:
            type: string
            format: date
            example: 2020-01-01
