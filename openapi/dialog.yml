openapi: "3.0.2"
info:
  title: Conversation API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/app/{appID}/conversation:
    get:
      tags:
        - dialog
      summary: List conversations
      operationId: listConversations
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - $ref: "#/components/parameters/appID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/Conversation"
                      total:
                        type: integer
                        example: 1
  /api/v1/app/{appID}/chat:
    post:
      tags:
        - dialog
      summary: Start a chat or continue a chat
      operationId: startChat
      description: sse, start a chat
      parameters:
        - $ref: "#/components/parameters/appID"
        - name: conversationID
          in: query
          description: Conversation ID, if not provided, a new conversation will be created
          required: false
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  $ref: "app.yml#/components/schemas/agentConfig"
                parameters:
                  type: object
                  description: Chat parameters, dependents on app params_in_prompt
                  example: {"parameter1": "John", "parameter2":"Jerry"}
                message:
                  type: string
                  description: Message to send to the LLM
                responseMode:
                  type: string
                  enum:
                    - streaming
                    - json
                  description: Response mode
                  example: text
      responses:
        "200":
          description: OK
          content:
            text/event-stream:
              schema:
                $ref: "#/components/schemas/SSEMessage"
            application/json:
              schema:
                $ref: "#/components/schemas/Message"
  /api/v1/conversation/{conversationID}:
    delete:
      tags:
        - dialog
      summary: Delete a conversation
      operationId: deleteConversation
      parameters:
        - $ref: "#/components/parameters/conversationID"
      responses:
        "204":
          description: No Content
    put:
      tags:
        - dialog
      summary: Update a conversation
      operationId: updateConversation
      parameters:
        - $ref: "#/components/parameters/conversationID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Conversation name
                  example: "Conversation name"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Conversation"
  /api/v1/conversation/{conversationID}/message:
    get:
      tags:
        - dialog
      summary: list messages for a conversation
      operationId: listMessages
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - $ref: "#components/parameters/conversationID"
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/Message"
                      total:
                        type: integer
                        example: 1
  /api/v1/app/{appID}/completion:
    post:
      tags:
        - dialog
      summary: Get completion
      operationId: getCompletion
      description: sse, get completion
      parameters:
        - $ref: "#/components/parameters/appID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                config:
                  $ref: "app.yml#/components/schemas/agentConfig"
                parameters:
                  type: object
                  description: Chat parameters, dependents on app params_in_prompt
                  example: {"parameter1": "John", "parameter2":"Jerry"}
                responseMode:
                  type: string
                  enum:
                    - streaming
                    - json
                  description: Response mode
                  example: text
      responses:
        "200":
          description: OK
          content:
            text/event-stream:
              schema:
                $ref: "#/components/schemas/SSECompletion"
            application/json:
              schema:
                $ref: "#/components/schemas/Completion"
  /api/v1/message/{messageID}/feedback:
    post:
      operationId: postFeedback
      tags:
        - dialog
      summary: Post feedback
      parameters:
        - $ref: "#/components/parameters/messageID"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                rating:
                  type: string
                  enum:
                    - like
                    - dislike
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      messageID:
                        type: string
                        format: uuid
                      rating:
                        type: string
                        enum:
                          - like
                          - dislike

  /api/v1/conversation:
    get:
      tags:
        - service
      summary: List conversations service
      operationId: listConversationsService
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - $ref: "#/components/parameters/user"
        - $ref: "#/components/parameters/appIDInQuery"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/Conversation"
                      total:
                        type: integer
                        example: 1
    delete:
      tags:
        - service
      summary: Delete a conversation
      operationId: deleteConversationService
      parameters:
        - $ref: "#/components/parameters/conversationIDInQuery"
        - $ref: "#/components/parameters/appID"
        # - $ref: "#/components/parameters/user"
      responses:
        "204":
          description: No Content
    put:
      tags:
        - service
      summary: Update a conversation
      operationId: updateConversationService
      parameters:
        - $ref: "#/components/parameters/conversationIDInQuery"
        - $ref: "#/components/parameters/user"
        - $ref: "#/components/parameters/appIDInQuery"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: Conversation name
                  example: "Conversation name"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Conversation"
  /api/v1/app-chat:
    post:
      summary: start or continue a conversation
      operationId: chat
      parameters:
        - $ref: "#/components/parameters/appIDInQuery"
        - name: conversationID
          in: query
          description: Conversation ID, if not provided, a new conversation will be created
          required: false
          schema:
            type: string
            format: uuid
      tags:
        - service
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/chatRequest"
      responses:
        "200":
          description: OK
          content:
            text/event-stream:
              schema:
                $ref: "#/components/schemas/SSEMessage"
            application/json:
              schema:
                $ref: "#/components/schemas/Message"
  /api/v1/completion:
    post:
      summary: completion user's input
      operationId: completion
      tags:
        - service
      parameters:
        - $ref: "#/components/parameters/appIDInQuery"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/completionRequest"
      responses:
        "200":
          description: OK
          content:
            text/event-stream:
              schema:
                $ref: "#/components/schemas/SSECompletion"
            application/json:
              schema:
                $ref: "#/components/schemas/Completion"
  /api/v1/message:
    get:
      tags:
        - service
      summary: list messages for a conversation
      operationId: listMessagesService
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - $ref: "#components/parameters/conversationIDInQuery"
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/Message"
                      total:
                        type: integer
                        example: 1
  /api/v1/message-feedback:
    post:
      operationId: postFeedbackService
      tags:
        - service
      summary: Post feedback
      parameters:
        - $ref: "#/components/parameters/messageIDInQuery"
        - $ref: "#/components/parameters/user"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                rating:
                  type: string
                  enum:
                    - like
                    - dislike
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      messageID:
                        type: string
                        format: uuid
                      rating:
                        type: string
                        enum:
                          - like
                          - dislike


components:
  parameters:
    appIDInQuery:
      name: appID
      in: query
      description: required if token is not belongs to the app
      schema:
        type: string
        format: uuid
    conversationIDInQuery:
      name: conversationID
      in: query
      description: Conversation ID
      required: true
      schema:
        type: string
        format: uuid
    user:
      name: user
      in: query
      description: identity of the user, defined by outter system, rqeuired in token's calling
      required: true
      schema:
        type: string
    conversationID:
      name: conversationID
      in: path
      description: Conversation ID
      required: true
      schema:
        type: string
        format: uuid
    messageIDInQuery:
      name: messageID
      in: query
      description: Message ID
      required: true
      schema:
        type: string
        format: uuid
    messageID:
      name: MessageID
      in: path
      description: MessageID
      required: true
      schema:
        type: string
        format: uuid
    appID:
      name: appID
      in: path
      description: App ID
      required: true
      schema:
        type: string
        format: uuid
  schemas:
    chatRequest:
      type: object
      properties:
        parameters:
          type: object
          description: Chat parameters, dependents on app params_in_prompt
          example: {"parameter1": "John", "parameter2":"Jerry"}
        message:
          type: string
          description: Message to send to the LLM
        responseMode:
          type: string
          enum:
            - streaming
            - json
          description: Response mode
          example: text
        user:
          type: string
          description: identity of the user, will be passed into the LLM
    completionRequest:
      type: object
      properties:
        parameters:
          type: object
          description: Chat parameters, dependents on app params_in_prompt
          example: {"parameter1": "John", "parameter2":"Jerry"}
        responseMode:
          type: string
          enum:
            - streaming
            - json
          description: Response mode
          example: text
        user:
          type: string
          description: identity of the user, defined by outter system
    Conversation:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: name
    SSEMessage:
      type: object
      properties:
        content:
          type: string
          description: a single char, this field exist only if the eventType is content
        messageID:
          type: string
          description: exist in all events
          format: uuid
        conversationID:
          type: string
          description: exist only if the eventType is end
          format: uuid
    Message:
      type: object
      properties:
        content:
          type: string
          description: message returned from AI
        rating:
          type: string
          enum:
            - like
            - dislike
        messageID:
          type: string
          format: uuid
        conversationID:
          type: string
          format: uuid
    SSECompletion:
      type: object
      properties:
        content:
          type: string
          description: a single char, this field exist only if the eventType is content
        messageID:
          type: string
          format: uuid
    Completion: 
      type: object
      properties:
        content:
          type: string
          description: response from AI
        messageID:
          type: string
          format: uuid
