openapi: "3.0.2"
info:
  title: Group API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/workspace/{workspaceID}/group:
    get:
      tags:
        - group
      operationId: listGroup
      summary: List groups
      description: List groups related to workspace
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - name: workspaceID
          in: path
          description: Workspace ID
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/groupGet"
                      total:
                        type: integer
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
    post:
      tags:
        - group
      operationId: createGroup
      summary: Create group
      description: Create group
      parameters:
        - name: workspaceID
          in: path
          description: Workspace ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  properties:
                    name:
                      type: string
                      example: group name
                    avatar:
                      type: string
                      format: binary
                      example: group avatar
                    description:
                      type: string
                      example: group description
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/groupGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/group/{groupID}:
    get:
      tags:
        - group
      operationId: getGroup
      summary: Get group
      description: Get group by user permission
      parameters:
        - $ref: "#/components/parameters/groupID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/groupGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      tags:
        - group
      operationId: updateGroup
      summary: Update group
      description: Update group
      parameters:
        - $ref: "#/components/parameters/groupID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/groupUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/groupGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    delete:
      tags:
        - group
      operationId: deleteGroup
      summary: Delete group
      description: Delete group
      parameters:
        - $ref: "#/components/parameters/groupID"
      responses:
        "204":
          description: No content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    workspaceID:
      name: workspaceID
      in: path
      description: Workspace ID
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    groupID:
      name: groupID
      in: path
      description: Group ID
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
  schemas:
    groupUpdate:
      type: object
      properties:
        name:
          type: string
          example: group name
        avatar:
          type: string
          format: binary
          example: group avatar
        description:
          type: string
          example: group description
    groupGet:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: group name
          example: name
        # avatar:
        #   type: string
        #   format: binary
        #   example: workspace avatar
        description:
          type: string
          example: workspace description
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01 00:00:00
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01 00:00:00
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
