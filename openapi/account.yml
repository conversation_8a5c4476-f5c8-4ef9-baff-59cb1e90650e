openapi: "3.0.2"
info:
  title: Account API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/userroles:
    get:
      tags:
        - account
      summary: Get user roles of resources for current user
      operationId: getUserroles
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Userrole'
  /api/v1/userinfo:
    get:
      tags:
        - account
      summary: Get Userinfo
      operationId: get_userinfo_api_v1_me_get
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: 'common.yml#/components/schemas/SessionUser'
  /api/v1/account:
    get:
      tags:
        - account
      operationId: getAccount
      summary: Get account
      description: Get account for current user if there's no query parameters. Only admin, get accounts by query parameters. 
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - name: id
          in: query
          description: Account ID
          required: false
          schema:
            type: array
            items:
              type: string
              format: uuid
              example: ********-0000-0000-0000-************
        - name: name
          in: query
          description: Fuzzy query by name
          required: false
          schema:
            type: array
            items:
              type: string
              example: name
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/accountGet"
                      total:
                        type: integer
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"


components:
  schemas:
    Userrole:
      type: array
      items:
        properties:
          resourceType: 
            type: string
            enum:
              - workspace
              - group
              - app
            example: workspace
          resourceID:
            type: string
            format: uuid
            example: ********-0000-0000-0000-************
          role:
            type: string
            enum:
              - admin
              - developer 
              - viewer
              - external_user
            example: admin
    accountGet:
      type: object
      properties:
        id:
          type: string
          format: uuid 
          example: ********-0000-0000-0000-************
        name:
          type: string
          example: name
        fullname:
          type: string
          example: fullname
        email:
          type: string
          example: email
        isAdmin:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
