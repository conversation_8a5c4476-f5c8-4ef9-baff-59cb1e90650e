openapi: "3.0.2"
info:
  title: App API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/components/{componentID}/env-variables:
    get:
      tags:
        - env-variables
        - components
      operationId: listComponentEnvVariables
      summary: List components env variables
      description: List components env variables
      parameters:
        - $ref: "#/components/parameters/componentID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/envVariableGet"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - env-variables
        - components
      operationId: createComponentEnvVariable
      summary: Create component env variable
      description: Create component env variable
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/envVariableCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/envVariableGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/env-variables/{envVariableID}:
    get:
      tags:
        - env-variables
        - components
      operationId: getComponentEnvVariable
      summary: Get component env variable
      description: Get component env variable by id
      parameters:
        - $ref: "#/components/parameters/componentID"
        - $ref: "#/components/parameters/envVariableID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/envVariableGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      tags:
        - env-variables
        - components
      operationId: updateComponentEnvVariable
      summary: Update component env variable
      description: Update component env variable by id
      parameters:
        - $ref: "#/components/parameters/componentID"
        - $ref: "#/components/parameters/envVariableID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  $ref: "#/components/schemas/envVariableCreate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/envVariableGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    delete:
      tags:
        - env-variables
        - components
      operationId: deleteComponentEnvVariable
      summary: Delete component env variable
      description: Delete component env variable by id
      parameters:
        - $ref: "#/components/parameters/componentID"
        - $ref: "#/components/parameters/envVariableID"
      responses:
        "204":
          description: No content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/workspace/{workspaceID}/env-variables:
    get:
      tags:
        - env-variables
        - workspace
      operationId: listEnvVariablesByWorkspace
      summary: List env variables by workspace
      description: List env variables by workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - componentID:
          name: componentID
          in: query
          description: component id
          required: true
          schema:
            type: string
            format: uuid
            example: 123e4567-e89b-12d3-a456-************
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/envVariableGet"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
        tags:
          - env-variables
          - workspace
        operationId: createComponentWorkspaceEnvVariable
        summary: Create component workspace env variable
        description: Create component workspace env variable
        requestBody:
          required: true
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/envVariableCreate"
        responses:
          "201":
            description: Created
            content:
              application/json:
                schema:
                  properties:
                    data:
                      $ref: "#/components/schemas/envVariableGet"
          "401":
            $ref: "common.yml#/components/responses/Unauthorized"
          "403":
            $ref: "common.yml#/components/responses/Forbidden"
          "404":
            $ref: "common.yml#/components/responses/NotFound"
  /api/v1/group/{groupID}/env-variables:
    get:
      tags:
        - env-variables
        - group
      operationId: listEnvVariablesByGroup
      summary: List env variables by group
      description: List env variables by group
      parameters:
        - $ref: "#/components/parameters/groupID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - componentID:
          name: componentID
          in: query
          description: component id
          required: true
          schema:
            type: string
            format: uuid
            example: 123e4567-e89b-12d3-a456-************
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/envVariableGet"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - env-variables
        - group
      operationId: createComponentGroupEnvVariable
      summary: Create component group env variable
      description: Create component group env variable
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/envVariableCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/envVariableGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/env-variables:
    get:
      tags:
        - env-variables
        - app
      operationId: listEnvVariablesByApp
      summary: List env variables by app
      description: List env variables by app
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - componentID:
          name: componentID
          in: query
          description: component id
          required: true
          schema:
            type: string
            format: uuid
            example: 123e4567-e89b-12d3-a456-************
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/envVariableGet"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - env-variables
        - app
      operationId: createComponentAppEnvVariable
      summary: Create component app env variable
      description: Create component app env variable
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/envVariableCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/envVariableGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    envVariableID:
      name: envVariableID
      in: path
      description: env variable id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    workspaceID:
      name: workspaceID
      in: path
      description: workspace id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    groupID:
      name: groupID
      in: path
      description: group id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    componentID:
      name: componentID
      in: path
      description: component id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    appID:
      name: appID
      in: path
      description: app id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
  schemas:
    envVariableCreate:
      type: object
      properties:
        name:
          type: string
          example: CHAT_GPT_API_TOKEN
        componentID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        param:
          type: string
          example: token
        value:
          type: string
          example: xxxxxx
        scopeType:
          type: string
          enum:
            - component
            - workspace
            - group
            - app
          example: app
        scopeID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
    envVariableUpdate:
      type: object
      properties:
        value:
          type: string
          example: xxxxxx
    envVariableGet:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: CHAT_GPT_API_TOKEN
        componentID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        param:
          type: string
          example: token
        value:
          type: string
          example: xxxxxx
        scopeType:
          type: string
          enum:
            - component
            - workspace
            - group
            - app
          example: app
        scopeID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
