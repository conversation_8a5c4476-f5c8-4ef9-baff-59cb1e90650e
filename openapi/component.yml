openapi: "3.0.2"
info:
  title: App API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/workspace/{workspaceID}/components:
    get:
      tags:
        - components
        - workspace
      operationId: listComponentsByWorkspace
      summary: List components by workspace
      description: List components by workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - $ref: "#/components/parameters/categories"
        - name: hasEnvVar
          in: query
          description: query hasEnvVar
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/componentGet"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - components
        - workspace
      operationId: createComponentForWorkspace
      summary: Create component for workspace
      description: Create component for workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/componentCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data: 
                    $ref: "#/components/schemas/componentGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/components/{componentID}:
    get:
      tags:
        - components
      operationId: getComponent
      summary: Get component
      description: Get component by id
      parameters:
        - $ref: "#/components/parameters/componentID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/componentGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    delete:
      tags:
        - app
      operationId: deleteComponent
      summary: Delete component
      description: Delete component by id
      parameters:
        - $ref: "#/components/parameters/componentID"
      responses:
        "204":
          description: No content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      tags:
        - app
      operationId: updateComponent
      summary: Update component
      description: Update component by id
      parameters:
        - $ref: "#/components/parameters/componentID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  $ref: "#/components/schemas/componentUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/componentGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    categories:
      name: categories
      in: query
      description: category name list
      required: false
      schema:
        type: string
        example: INPUT,OUTPUT
    categoryID:
      name: categoryID
      in: query
      description: category ID
      required: false
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    workspaceID:
      name: workspaceID
      in: path
      description: workspace id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    componentID:
      name: componentID
      in: path
      description: component id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
  schemas:
    componentCreate:
      type: object
      properties:
        name:
          type: string
          example: text-input
        code:
          type: string
          example: TEXT_INPUT
        description:
          type: string
          example: text input
        type:
          type: string
          enum:
            - INPUT
            - OUTPUT
            - SCRIPT
            - SWITCH
            - FOREACH
            - HTTP
            - LLM-PLUGIN
        categoryID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        category:
          type: string
        workspaceID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        scope:
          type: string
          enum:
            - public
            - scoped
        config:
          type: object
          additionalProperties: true
        deprecated:
          type: boolean
          example: false
        envs:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: dev
              param:
                type: string
                example: url
    componentGet:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: text-input
        code:
          type: string
          example: TEXT_INPUT
        description:
          type: string
          example: text input
        type:
          type: string
          enum:
            - INPUT
            - OUTPUT
            - SCRIPT
            - SWITCH
            - FOREACH
            - HTTP
            - LLM_PLUGIN
        categoryID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        category:
          type: string
        workspaceID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        scope:
          type: string
          enum:
            - public
            - scoped
        config:
          type: object
          additionalProperties: true
        deprecated:
          type: boolean
          example: false
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
    componentUpdate:
      type: object
      properties:
        name:
          type: string
          example: text-input
        code:
          type: string
          example: TEXT_INPUT
        description:
          type: string
          example: text input
        type:
          type: string
          enum:
            - INPUT
            - OUTPUT
            - SCRIPT
            - SWITCH
            - FOREACH
            - HTTP
            - LLM_PLUGIN
        categoryID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        category:
          type: string
        scope:
          type: string
          enum:
            - public
            - scoped
        config:
          type: object
          additionalProperties: true
        deprecated:
          type: boolean
          example: false
    componentGetWithoutConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: name
        code:
          type: string
          example: TEXT_INPUT
        description:
          type: string
          example: text input
        type:
          type: string
          enum:
            - INPUT
            - OUTPUT
            - SCRIPT
            - SWITCH
            - FOREACH
            - HTTP
            - LLM-PLUGIN
        categoryID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        category:
          type: string
        workspaceID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        scope:
          type: string
          enum:
            - public
            - scoped
        deprecated:
          type: boolean
          example: false
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
