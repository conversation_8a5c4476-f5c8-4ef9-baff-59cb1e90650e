openapi: "3.0.2"
info:
  title: ModelProvider API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/model-provider:
    get:
      tags:
        - model-provider
      operationId: listGlobalModelProvider
      summary: List global model providers
      description: List global model providers
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/globalModelProviderGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
  /api/v1/workspace/{workspaceID}/model-provider:
    get:
      tags:
        - model-provider
        - workspace
      operationId: listWorkspaceModelProviderBinding
      summary: List model provider's bindings for workspace
      description: List model provider's bindings related to workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/providerBindingGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - model-provider
        - workspace
      operationId: createWorkspaceModelProviderBinding
      summary: Create model provider binding for workspace
      description: Create model provider binding for workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  $ref: "#/components/schemas/providerBindingCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/providerBindingGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/model-provider/{providerID}:
    delete:
      tags:
        - model-provider
        - workspace
      operationId: deleteWorkspaceModelProviderBinding
      summary: Delete model provider binding for workspace
      description: Delete model provider binding for workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - $ref: "#/components/parameters/providerID"
      responses:
        "204":
          description: No Content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      tags:
        - model-provider
        - workspace
      operationId: updateWorkspaceModelProviderBinding
      summary: Update model provider binding for workspace
      description: Update model provider binding for workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - $ref: "#/components/parameters/providerID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/providerBindingUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/providerBindingGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/group/{groupID}/model-provider:
    get:
      tags:
        - model-provider
        - group
      operationId: listGroupModelProvider
      summary: List model providers for group
      description: List model providers related to group
      parameters:
        - $ref: "#/components/parameters/groupID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/providerBindingGet"
                      total:
                        type: integer
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - model-provider
        - group
      operationId: createGroupModelProvider
      summary: Create model provider for group
      description: Create model provider for group
      parameters:
        - $ref: "#/components/parameters/groupID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
                $ref: "#/components/schemas/providerBindingCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/providerBindingGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/workspace/{workspaceID}/default-model:
    put:
      tags:
        - model-provider
        - workspace
      operationId: updateWorkspaceDefaultModel
      summary: Update workspace default model
      description: Update workspace default model
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - name: modelType
          in: query
          description: Model type
          required: true
          schema:
            type: string
            enum:
              - embeding
              - llm
              - speech2txt
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/defaultModelConfigUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                 properties:
                  data:
                    $ref: "#/components/schemas/defaultModelConfigGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    get:
      tags:
        - model-provider
        - workspace
      operationId: getWorkspaceDefaultModel
      summary: Get workspace default model
      description: Get workspace default model
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - name: modelType
          in: query
          description: Model type
          required: false
          schema:
            type: string
            enum:
              - embeding
              - llm
              - speech2txt
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/defaultModelConfigGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/group/{groupID}/default-model:
    put:
      tags:
        - model-provider
        - group
      operationId: updateGroupDefaultModel
      summary: Update group default model
      description: Update group default model
      parameters:
        - $ref: "#/components/parameters/groupID"
        - name: modelType
          in: query
          description: Model type
          required: true
          schema:
            type: string
            enum:
              - embeding
              - llm
              - speech2txt
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/defaultModelConfigUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/defaultModelConfigGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    get:
      tags:
        - model-provider
        - group
      operationId: getGroupDefaultModel
      summary: Get group default model
      description: Get group default model
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - name: modelType
          in: query
          description: Model type
          required: false
          schema:
            type: string
            enum:
              - embeding
              - llm
              - speech2txt
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/defaultModelConfigGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/model:
    get:
      tags:
        - model-provider
      operationId: listModel
      summary: List model
      description: List model related to model provider
      parameters:
        - name: type
          in: query
          description: Model type
          required: false
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/modelGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
 
components:
  parameters:
    groupID:
      name: groupID
      in: path
      description: Group ID
      required: true
      schema:
        type: string
        format: uuid
        example: 00000000-0000-0000-0000-000000000000
    providerID: # binding id
      name: providerID
      in: path
      description: Model provider binding ID
      required: true
      schema:
        type: integer
        format: int64
    providerKind:
      name: providerKind
      in: path
      description: Model provider kind
      required: true
      schema:
        type: string
        example: openAI
    workspaceID:
      name: workspaceID
      in: path
      description: Workspace ID
      required: true
      schema:
        type: string
        format: uuid
        example: 00000000-0000-0000-0000-000000000000
  schemas:
    modelUpdate:
      type: object
      properties:
        name:
          type: string
          example: model name
        description:
          type: string
          example: model description
    modelGet:
      type: object
      properties:
        name:
          type: string
          example: model name
        description:
          type: string
        type:
          type: string
          example: embeding

    defaultModelConfigUpdate:
      type: object
      properties:
        modelName:
          type: string
          format: int64
          example: 1
        providerKind:
          type: string
          example: openAI
        modelType:
          type: string
          example: embeding

    defaultModelConfigGet:
      type: object
      properties:
        modelName:
          type: string
          format: int64
        modelType:
          type: string
          description: Model type, embeding, llm, speech2txt
          example: embeding
        providerKind:
          type: string
          description: Model provider kind, OpenAI, Claude
          example: openAI
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01 00:00:00
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01 00:00:00
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
    providerBindingCreate:
      type: object
      properties:
        providerKind:
          type: string
          description: Model provider kind, OpenAI, Claude
          example: openAI
        description:
          type: string
          example: model provider description
        endpoint:
          type: string
          description: LLM/Embedding model service endpoint
          example: http://localhost:5000
        apiKey:
          type: string
          description: LLM/Embedding model service api key
          example: api key
        config:
          type: object
          description: Model provider extra config
          example: {}
    providerBindingUpdate:
      type: object
      properties:
        description:
          type: string
          example: model provider description
        endpoint:
          type: string
          description: LLM/Embedding model service endpoint
          example: http://localhost:5000
        apiKey:
          type: string
          description: LLM/Embedding model service api key
          example: api key
        config:
          type: object
          description: Model provider extra config
          example: {}
    providerBindingGet:
      type: object
      properties:
        id: 
          type: integer
          format: int64
          example: 1
        kind:
          type: string
          description: Model provider kind, OpenAI, Claude
          example: openAI
        description:
          type: string
          example: model provider description
        endpoint:
          type: string
          description: LLM/Embedding model service endpoint
          example: http://localhost:5000
        config:
          type: object
          description: Model provider extra config
          properties:
            quota:
              type: object
              description: read only
              properties:
                type:
                  type: string
                  enum: 
                    - tokens
                    - requests
                total:
                  type: integer
                  example: 100
                used:
                  type: integer
                  example: 10
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01 00:00:00
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01 00:00:00
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
    globalModelProviderGet:
      type: object
      properties:
        kind:
          type: string
          description: Model provider kind, OpenAI, Claude
          example: openAI
        description:
          type: string
          example: model provider description
