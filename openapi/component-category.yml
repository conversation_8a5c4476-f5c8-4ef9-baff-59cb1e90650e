openapi: "3.0.2"
info:
  title: App API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/component-categories:
    get:
      tags:
        - component-categories
      operationId: listComponentCategories
      summary: List component categories
      description: List component categories
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/categoryGet"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - component-categories
      operationId: createComponentCategory
      summary: Create component category
      description: Create component category
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/categoryCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data: 
                    $ref: "#/components/schemas/categoryGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/component-categories/{categoryID}:
    get:
      tags:
        - component-categories
      operationId: getComponentCategory
      summary: Get component category
      description: Get component category by id
      parameters:
        - $ref: "#/components/parameters/categoryID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/categoryGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    delete:
      tags:
        - app
      operationId: deleteComponentCategory
      summary: Delete component category
      description: Delete component category by id
      parameters:
        - $ref: "#/components/parameters/categoryID"
      responses:
        "204":
          description: No content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      tags:
        - app
      operationId: updateComponentCategory
      summary: Update component category
      description: Update component category by id
      parameters:
        - $ref: "#/components/parameters/categoryID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  $ref: "#/components/schemas/categoryUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/categoryGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    categoryID:
      name: categoryID
      in: path
      description: category ID
      required: false
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
  schemas:
    categoryCreate:
      type: object
      properties:
        name:
          type: string
          example: 输入
        description:
          type: string
          example: 输入组件
    categoryUpdate:
      type: object
      properties:
        name:
          type: string
          example: 输入
        description:
          type: string
          example: 输入组件
    categoryGet:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: 输入
        description:
          type: string
          example: 输入组件
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
