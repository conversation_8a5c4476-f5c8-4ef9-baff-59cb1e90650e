components:
  parameters:
    pageSize:
      type: object
      name: pageSize
      in: query
      description: Item count per page
      required: false
      schema:
        type: integer
        minimum: 1
        maximum: 500
        default: 10
    pageNumber:
      type: object
      name: pageNumber
      in: query
      description: Page number
      required: false
      schema:
        type: integer
        minimum: 1
        default: 1
  schemas:
    SessionUser:
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
          title: Name
        isAdmin:
          type: boolean
          title: Is Admin
        fullname:
          type: string
          title: Fullname
        email:
          type: string
          title: Email
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: name
        fullname:
          type: string
          example: fullname
  responses:
    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                example: NotFound
              message:
                type: string
                example: Not found, resource not found
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                example: Unauthorized
              message:
                type: string
                example: Unauthorized, you have to login first
    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                example: Forbidden
              message:
                type: string
                example: Forbidden, you don't have permission to access
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            type: object
            properties:
              code:
                type: string
                example: BadRequest
              message:
                type: string
                example: Paramter invalid, bad request
