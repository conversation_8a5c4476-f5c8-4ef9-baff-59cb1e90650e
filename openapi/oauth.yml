openapi: "3.0.2"
info:
  title: Oauth API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/oauth/login/{OauthProvider}:
    get:
      operationId: oauthLogin
      tags:
        - oauth
        - login
      summary: Login with OAuth
      responses:
        "302":
          description: Redirect to OAuth provider
          headers:
            Location:
              schema:
                type: string
                format: uri
                example: https://accounts.google.com/o/oauth2/v2/auth?response_type=code&client_id=**********&redirect_uri=http%3A%2F%2Flocalhost%3A5000%2Foauth%2Fcallback%2Fgoogle&scope=openid+email+profile&state=**********
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

  /api/v1/oauth/callback/{OauthProvider}:
    get:
      tags:
        - oauth
        - login
      operationId: oauthCallback
      summary: OAuth callback
      responses:
        "307":
          description: Redirect to frontend
          headers:
            Location:
              schema:
                type: string
                format: uri
                example: http://localhost:5000/
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

  /api/v1/logout:
    get:
      tags:
        - login
      operationId: logout
      description: Logout
      summary: Logout
      responses:
        "200":
          description: OK
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"

  /api/v1/oauth/providers:
    get:
      tags:
        - oauth
      operationId: oauthProviders
      summary: Get all OAuth providers' name
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      type: string
                    example: ["google", "facebook", "github", "netease"]
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
