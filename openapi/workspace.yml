openapi: "3.0.2"
info:
  title: Workspace API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/workspace:
    get:
      tags:
        - workspace
      operationId: listWorkspace
      summary: List workspaces
      description: List workspaces related to user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/workspaceGet"
                      total:
                        type: integer
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
    post:
      tags:
        - workspace
      operationId: createWorkspace
      summary: Create workspace
      description: Create workspace
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: workspace name
                description:
                  type: string
                  example: workspace description
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/workspaceGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
  /api/v1/workspace/{workspaceID}:
    get:
      tags:
        - workspace
      operationId: getWorkspace
      summary: Get workspace
      description: Get workspace by user permission
      parameters:
        - $ref: "#/components/parameters/workspaceID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/workspaceGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    delete:
      tags:
        - workspace
      operationId: deleteWorkspace
      summary: Delete workspace
      description: Delete workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
      responses:
        "204":
          description: No content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      tags:
        - workspace
      operationId: updateWorkspace
      summary: Update workspace
      description: Update workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              required:
                - name
                - description
              $ref: "#/components/schemas/workspaceUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/workspaceGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  # /workspace/{workspaceID}/avatar:
  #   get:
  #     tags:
  #       - workspace
  #     operationId: getWorkspaceAvatar
  #     summary: Get workspace avatar
  #     description: Get workspace avatar
  #     parameters:
  #       - $ref: "#/components/parameters/workspaceID"
  #     responses:
  #       "200":
  #         description: OK
  #         content:
  #           application/jpeg:
  #             schema:
  #               type: string
  #               format: binary
  #       "401":
  #         $ref: "common.yml#/components/responses/Unauthorized"
  #       "403":
  #         $ref: "common.yml#/components/responses/Forbidden"
  #       "404":
  #         $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    workspaceID:
      name: workspaceID
      in: path
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
  schemas:
    workspaceUpdate:
      type: object
      properties:
        name:
          type: string
          example: workspace name
        description:
          type: string
          example: workspace description
    workspaceGet:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: workspace name
        # avatar:
        #   type: string
        #   format: binary
        #   example: workspace avatar
        description:
          type: string
          example: workspace description
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01 00:00:00
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01 00:00:00
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
