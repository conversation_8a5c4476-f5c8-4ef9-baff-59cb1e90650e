openapi: "3.0.2"
info:
  title: App API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/workflow-datatypes:
    get:
      tags:
        - workflow-datatype
      operationId: listWorkflowDatatypes
      summary: List workflow datatypes
      description: List workflow datatypes
      parameters:
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/datatypeGet"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - workflow-datatype
      operationId: createWorkflowDatatype
      summary: Create workflow datatype
      description: Create workflow datatype
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/datatypeCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/datatypeGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/workflow-datatypes/{workflowDataTypeID}:
    get:
      tags:
        - workflow-datatype
      operationId: GetWorkflowDatatypeByID
      summary: Get workflow datatype by id
      description: Get workflow datatype by id
      parameters:
        - $ref: "#/components/parameters/workflowDatatypeID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/datatypeGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    delete:
      tags:
        - workflow-datatype
      operationId: deleteWorkflowDatatype
      summary: Delete workflow datatype
      description: Delete workflow datatype by id
      parameters:
        - $ref: "#/components/parameters/workflowDatatypeID"
      responses:
        "204":
          description: No content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      tags:
        - workflow-datatype
      operationId: updateWorkflowDataType
      summary: Update workflow data type
      description: Update workflow data type by id
      parameters:
        - $ref: "#/components/parameters/workflowDatatypeID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  $ref: "#/components/schemas/datatypeUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/datatypeGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    workflowDatatypeID:
      name: workflowDatatypeID
      in: path
      description: workflow datatype id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
  schemas:
    datatypeCreate:
      type: object
      properties:
        name:
          type: string
          example: name
        config:
          type: object
          additionalProperties: true
        description:
          type: string
          example: integer datatype
    datatypeUpdate:
      type: object
      properties:
        config:
          type: object
          additionalProperties: true
        description:
          type: string
          example: integer datatype
    datatypeGet:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: name
        config:
          type: object
          additionalProperties: true
        description:
          type: string
          example: integer datatype
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
