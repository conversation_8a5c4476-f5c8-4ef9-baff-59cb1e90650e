openapi: "3.0.2"
info:
  title: App API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/workspace/{workspaceID}/app:
    get:
      tags:
        - app
        - workspace
      operationId: listAppsByWorkspace
      summary: List apps
      description: List apps by workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - $ref: "#/components/parameters/appType"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/appGetWithoutConfig"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - workspace
      operationId: createAppForWorkspace
      summary: Create app for workspace
      description: Create app for workspace
      parameters:
        - $ref: "#/components/parameters/workspaceID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/appCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data: 
                    $ref: "#/components/schemas/appGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/group/{groupID}/app:
    get:
      tags:
        - app
        - group
      operationId: listAppsByGroup
      summary: List apps
      description: List apps by group
      parameters:
        - $ref: "#/components/parameters/groupID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - $ref: "#/components/parameters/appType"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/appGetWithoutConfig"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - group
      operationId: createAppForGroup
      summary: Create app for group
      description: Create app for group
      parameters:
        - $ref: "#/components/parameters/groupID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/appCreate"
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                properties:
                  data: 
                    $ref: "#/components/schemas/appGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}:
    get:
      tags:
        - app
      operationId: getApp
      summary: Get app
      description: Get app by id
      parameters:
        - $ref: "#/components/parameters/appID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/appGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    delete:
      tags:
        - app
      operationId: deleteApp
      summary: Delete app
      description: Delete app by id
      parameters:
        - $ref: "#/components/parameters/appID"
      responses:
        "204":
          description: No content
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    put:
      tags:
        - app
      operationId: updateApp
      summary: Update app
      description: Update app by id
      parameters:
        - $ref: "#/components/parameters/appID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  $ref: "#/components/schemas/appUpdate"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/appGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/deploy-history: # history 代表用户发布过的版本
    get:
      tags:
        - app
      operationId: listAppDeployHistory
      summary: List app deploy history
      description: List app deploy history by app id
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items: 
                        type: array
                        items:
                          $ref: "#/components/schemas/appConfig"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/config-snapshot: # snapshot 代表一个暂存副本
    get:
      tags:
        - app
      operationId: listAppConfigSnapshot
      summary: List app config snapshots
      description: List app config snapshot by app id
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/appConfig"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
    post:
      tags:
        - app
      operationId: createAppConfigSnapshot
      summary: create app config snapshot
      description: create app config snapshot by app id
      parameters:
        - $ref: "#/components/parameters/appID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/appConfigUpdate"
      responses:
        "204":
          description: No content
        # "200":
        #   description: OK
        #   content:
        #     application/json:
        #       schema:
        #         $ref: "#/components/schemas/modelConfig"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/deploy:
    post:
      tags:
        - app
      operationId: deployApp
      summary: Deploy app
      description: Deploy app
      parameters:
        - $ref: "#/components/parameters/appID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  $ref: "#/components/schemas/appDeploy"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/appConfig"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/debug:
    post:
      tags:
        - app
      operationId: deployApp
      summary: Deploy app
      description: Deploy app
      parameters:
        - $ref: "#/components/parameters/appID"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                data:
                  $ref: "#/components/schemas/appDebug"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/appConfig"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/trigger:
    post:
      tags:
        - app
      operationId: triggerWorkflowApp
      summary: Trigger workflow app
      description: Trigger workflow app, for langbase backend use
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/workflowTriggerRequest"
      responses:
        "200":
          description: success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/workflowTriggerResponse"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/trigger:
    post:
      tags:
        - service
      operationId: triggerWorkflowApp
      summary: Trigger workflow app
      description: Trigger workflow app, for user client use
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/workflowTriggerRequest"
      responses:
        "200":
          description: success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/workflowTriggerResponse"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/workflow-runs?debug={debug}:
    get:
      tags:
        - app
      operationId: listAppWorkflowRuns
      summary: List workflow runs
      description: List workflow runs by app id
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - debug:
          name: debug
          in: query
          description: query debug
          required: false
          schema:
            type: boolean
        - startTime:
          name: startTime
          in: query
          description: start timestamp
          required: true
          schema:
            type: int
        - endTime:
          name: endTime
          in: query
          description: end timestamp
          required: true
          schema:
            type: int
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/appWorkflowRunListGet"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/workflow-runs/{runID}?debug={debug}:
    get:
      tags:
        - app
      operationId: GetAppWorkflowRunDetail
      summary: Get app workflow run detail
      description: Get app workflow run detail by app id and run id
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "#/components/parameters/runID"
        - debug:
          name: debug
          in: query
          description: query debug
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/appWorkflowRunDetailGet"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/status?runID={runID}&nodeID={nodeID}&debug={debug}:
    get:
      tags:
        - app
      operationId: ListAppWorkflowRunNodeEvents
      summary: List app workflow run node events
      description: List app workflow run node events
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - runID:
          name: runID
          in: query
          description: run id
          required: true
          schema:
            type: string
        - nodeID:
          name: nodeID
          in: query
          description: node id
          required: true
          schema:
            type: string
        - debug:
          name: debug
          in: query
          description: query debug
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  items:
                    type: array
                    items:
                      $ref: "#/components/schemas/workflowRunNodeStatus"
                  total:
                    type: integer
                    format: int64
                    example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/app/{appID}/events?runID={runID}&nodeID={nodeID}&debug={debug}:
    get:
      tags:
        - app
      operationId: ListAppWorkflowRunNodeEvents
      summary: List app workflow run node events
      description: List app workflow run node events
      parameters:
        - $ref: "#/components/parameters/appID"
        - $ref: "common.yml#/components/parameters/pageSize"
        - $ref: "common.yml#/components/parameters/pageNumber"
        - runID:
          name: runID
          in: query
          description: run id
          required: true
          schema:
            type: string
        - nodeID:
          name: nodeID
          in: query
          description: node id
          required: true
          schema:
            type: string
        - debug:
          name: debug
          in: query
          description: query debug
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  items:
                    type: array
                    items:
                      $ref: "#/components/schemas/workflowRunEvent"
                  total:
                    type: integer
                    format: int64
                    example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
components:
  parameters:
    appType: 
      name: appType
      in: query
      description: app type, agent or workflow
      required: false
      schema:
        type: string
        enum:
          - agent-completion
          - agent-conversation
          - workflow
    appID:
      name: appID
      in: path
      description: app id
      required: true
      schema:
        type: integer
        format: int64
    workspaceID:
      name: workspaceID
      in: path
      description: workspace id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    groupID:
      name: groupID
      in: path
      description: group id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    runID:
      name: runID
      in: path
      description: run id
      required: true
      schema:
        type: string
    nodeID:
      name: nodeID
      in: path
      description: node id
      required: true
      schema:
        type: string
  schemas:
    appCreate:
      type: object
      properties:
        name:
          type: string
          example: name
        description:
          type: string
          example: this app is used to ...
        type:
          type: string
          enum:
            - agent
            - workflow
        config:
          oneOf:
            - $ref: "#/components/schemas/workflowConfig"
            - $ref: "#/components/schemas/agentConfig"
    appUpdate:
      type: object
      properties:
        name:
          type: string
          example: name
        description:
          type: string
          example: this app is used to ...
        config:
          oneOf:
            - $ref: "#/components/schemas/workflowConfig"
            - $ref: "#/components/schemas/agentConfig"
    appDebug:
      type: object
      properties:
        name:
          type: string
          example: name
        description:
          type: string
          example: this app is used to ...
        config:
          oneOf:
            - $ref: "#/components/schemas/workflowConfig"
            - $ref: "#/components/schemas/agentConfig"
    appDeploy:
      type: object
      properties:
        name:
          type: string
          example: name
        description:
          type: string
          example: this app is used to ...
        config:
          oneOf:
            - $ref: "#/components/schemas/workflowConfig"
            - $ref: "#/components/schemas/agentConfig"
    appConfigUpdate:
      type: object
      properties:
        config:
          oneOf:
            - $ref: "#/components/schemas/workflowConfig"
            - $ref: "#/components/schemas/agentConfig"
    appConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        sha:
          type: string
          description: sha256 of app config
          example: f572d396fae9206628714fb2ce00f72e94f2258f
        config:
          oneOf:
            - $ref: "#/components/schemas/workflowConfig"
            - $ref: "#/components/schemas/agentConfig"
        status:
          type: integer
          enum:
            - 0
            - 1
            - 2
        message:
          type: string
          example: message
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        createdBy:
          $ref: "common.yml#/components/schemas/User"
    appGetWithoutConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        type:
          type: string
          enum:
            - agent
            - workflow
        name:
          type: string
          example: name
        description:
          type: string
          example: this app is used to ...
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
    appGet:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        type:
          type: string
          enum:
            - agent
            - workflow
        name:
          type: string
          example: name
        description:
          type: string
          example: this app is used to ...
        config:
          oneOf:
            - $ref: "#/components/schemas/workflowConfig"
            - $ref: "#/components/schemas/agentConfig"
        createdAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        updatedAt:
          type: string
          format: date-time
          example: 2020-01-01T00:00:00Z
        createdBy:
          $ref: "common.yml#/components/schemas/User"
        updatedBy:
          $ref: "common.yml#/components/schemas/User"
    workflowTriggerRequest:
      type: object
      properties:
        appID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
          description: app id
        inputs:
          type: object
          additionalProperties: true
          description: workflow input parameter values
        callback:
          type: object
          properties:
            type:
              type: string
              enum:
                - kafka
                - nydus
            info:
              type: object
              additionalProperties: true
          description: callback address
        bizContext:
          type: string
          description: workflow business context, langbase pass along and return it without any changes
        searchKey:
          type: string
          description: workflow search key, workflow engine will use it to search workflow run task
        bizInfo:
          type: object
          properties:
            business:
              type: string
            scene:
              type: string
        debug:
          type: boolean
          description: trigger for debug
    workflowTriggerResponse:
      type: object
      properties:
        code:
          type: string
        message:
          type: string
        data:
          type: object
          properties:
            appID:
              type: string
            runID:
              type: string
            outputs:
              type: object
              additionalProperties: true
              example:
                output-key-1: "value1"
                output-key-2: 2
    appWorkflowRunListGet:
      type: object
      properties:
        flowId:
          type: string
          example: name
          description: workflow id, unique name, not uuid
        version:
          type: string
          description: workflow version
        runId:
          type: string
        createTime:
          type: integer
          description: create timestamp
        startTime:
          type: integer
          description: start timestamp
        endTime:
          type: integer
          description: end timestamp
        status:
          type: string
          example: running
        message:
          type: string
          description: a human readable message indicating details about status
    appWorkflowRunDetailGet:
      type: object
      properties:
        flowId:
          type: string
          example: name
          description: workflow id, unique name, not uuid
        version:
          type: string
          description: workflow version
        runId:
          type: string
        createTime:
          type: integer
          description: create timestamp
        startTime:
          type: integer
          description: start timestamp
        endTime:
          type: integer
          description: end timestamp
        status:
          type: string
          example: running
        message:
          type: string
          description: a human readable message indicating details about status
        nodes:
          type: array
          items:
            $ref: "#/components/schemas/workflowRunNodeStatus"
    workflowRunNodeStatus:
      type: object
      properties:
        flowId:
          type: string
          example: name
          description: workflow id, unique name, not uuid
        version:
          type: string
          description: workflow version
        runId:
          type: string
        nodeId:
          type: string
        total:
          type: integer
          description: total foreach items
        startTime:
          type: integer
          description: start timestamp
        endTime:
          type: integer
          description: end timestamp
        status:
          type: string
          example: running
        message:
          type: string
          description: a human readable message indicating details about status
        inputs:
          type: object
          additionalProperties: true
          description: workflow input parameter values
        outputs:
          type: object
          additionalProperties: true
          description: workflow output parameter values
    workflowRunEvent:
      type: object
      properties:
        flowId:
          type: string
          example: name
          description: workflow id, unique name, not uuid
        runId:
          type: string
        nodeId:
          type: string
        createTime:
          type: integer
          description: create timestamp
        type:
          type: string
          description: event type
        event:
          type: string
          description: event content
    parameter:
      type: object
      properties:
        type:
          type: string
          enum:
            - string
            - integer
            - float
            - boolean
          description: parameter type only support basic type
        name:
          type: string
        title:
          type: string
        description:
          type: string
          example: script type, eg. python、shell
        example:
          type: string
    workflowConfig:
      type: object
      description: workflow json config
      additionalProperties: true
      example: |
        {
          "inputs": [
            {
                "name": "input1",
                "type": "string",
                "title": "input1",
                "description": "input1",
                "example": "input1"
            },
          ],
          "outputs": [
            {
                "name": "output1",
                "type": "string",
                "title": "output1",
                "description": "output1",
                "example": "output1"
            },
          ],
          ......
        }
    agentConfig:
      type: object
      description: config detail of app, config is a json object, could store config for workflow or agent
      properties:
        prePrompt:
          type: string
          description: pre-input prompt, could be rendered with params_in_prompt
        paramsInPrompt:
          type: object
          description: holds params in prompt. Now we support list and input
          properties:
            parameter1:
              type: object
              properties:
                type:
                  type: string
                  enum:
                    - list
                    - input
                list:
                  type: array
                  description: required, when parameter type is list
                  items:
                    type: string
                input:
                  description: required, when parameter type is input
                  type: string
            parameter2:
              type: object
              properties:
                type:
                  type: string
                  enum:
                    - list
                    - input
                list:
                  type: array
                  description: required, when parameter type is list
                  items:
                    type: string
                input:
                  description: required, when parameter type is input
                  type: string
        prologue:
          type: string
          description: this will show before a dialog
        modelName:
          type: string
        modelParams:
          type: object
          description: model params, model params is varied from model to model, structure defined below is an example for openai
          properties:
            top_p:
              type: number
            temperature:
              type: number
            max_tokens: 
              type: integer
            persence_penalty:
              type: number
            frequency_penalty:
              type: number
        providerKind:
          type: string
