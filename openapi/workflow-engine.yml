openapi: "3.0.2"
info:
  title: Member API
  version: "1.0"
servers:
  - url: localhost:5000
    description: Local server
paths:
  /api/v1/workspace/{workspaceID}/workflow-engines:
    get:
      tags:
        - workflow-engine
      operationId: listWorkflowEngines
      summary: List workspace workflow engines
      description: List workspace workflow engines
      parameters:
        - $ref: "#/components/parameters/workspaceID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    properties:
                      items:
                        type: array
                        items:
                          $ref: "#/components/schemas/workflowEngine"
                      total:
                        type: integer
                        format: int64
                        example: 1
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"
  /api/v1/workflow-engines/{workflowEngineID}:
    get:
      tags:
        - workflow-engine
      operationId: getWorkflowEngine
      summary: Get workspace workflow engine
      description: Get workspace workflow engine by id
      parameters:
        - $ref: "#/components/parameters/workspaceID"
        - $ref: "#/components/parameters/workflowEngineID"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/workflowEngine"
        "401":
          $ref: "common.yml#/components/responses/Unauthorized"
        "403":
          $ref: "common.yml#/components/responses/Forbidden"
        "404":
          $ref: "common.yml#/components/responses/NotFound"

components:
  parameters:
    workspaceID:
      name: workspaceID
      in: path
      description: workspace id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
    workflowEngineID:
      name: workflowEngineID
      in: path
      description: workflow engine id
      required: true
      schema:
        type: string
        format: uuid
        example: 123e4567-e89b-12d3-a456-************
  schemas:
    workflowEngine:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        workspaceID:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: aio
        endpoint:
          type: string
          example: 127.0.0.1:9000
        token:
          type: string
          example: xxxxxx
        description:
          type: string
          example: aio workflow engine
