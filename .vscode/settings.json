{
    "[python]": {
        "editor.defaultFormatter": "ms-python.autopep8",
        "editor.formatOnSave": true,
        "editor.tabSize": 4
    },
    "flake8.args": [
        "--exclude=venv,.venv,.git,.vscode,__pycache__,.mypy_cache,.pytest_cache,build,dist,docs,*.egg-info,node_modules",
        "--max-line-length=120",
        "--ignore=E402,F841,F401,E302,E305"
    ],
    "python.analysis.autoImportCompletions": true,
    "python.languageServer": "Pylance",
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.diagnosticMode": "workspace",
    "python.analysis.userFileIndexingLimit": 10000,
    "python.analysis.importFormat": "relative",
    "python.analysis.inlayHints.variableTypes": true,
    "python.analysis.inlayHints.functionReturnTypes": true,
    "python.analysis.autoFormatStrings": true,
    "python.analysis.ignore": [
        "**/venv/**",
        "**/.venv/**",
        "**/.git/**",
        "**/.vscode/**",
        "**/__pycache__/**",
        "**/.mypy_cache/**",
        "**/.pytest_cache/**",
        "**/build/**",
        "**/dist/**",
        "**/docs/**",
        "**/*.egg-info/**",
        "**/node_modules/**"
    ],
    "python.analysis.exclude": [
        "**/venv/",
        "**/.venv/",
        "**/.git/**",
        "**/.vscode/**",
        "**/__pycache__/**",
        "**/.mypy_cache/**",
        "**/.pytest_cache/**",
        "**/build/**",
        "**/dist/**",
        "**/docs/**",
        "**/*.egg-info/**",
        "**/node_modules/**"
    ],
    "python.testing.pytestEnabled": true,
    "python.testing.cwd": "${workspaceFolder}/server",
    "[javascript]": {
        "editor.tabSize": 2
    },
    "[typescriptreact]": {
        "editor.tabSize": 2
    },
    "[typescript]": {
        "editor.tabSize": 2
    },
    "cSpell.words": [
        "httpx",
        "loguru",
        "Netease",
        "pydantic"
    ],
}