stages:
  - ut
  - build

before_script:
  - "export DOCKER_CONFIG_JSON=$(jq 'reduce .[].auth.auths as $item ({}; . + $item) | {auths: .}' <<< $REGISTRIES)"
  - export APP_REVISION="${CI_COMMIT_TAG:-${CI_COMMIT_REF_NAME##*/}-r$CI_PIPELINE_ID}"

variables:
  KUBERNETES_HOST_NETWORK: "true"

flake8:
  stage: ut
  image: harbor.yf-online-gy3.service.gy.ntes/langbase/python:3.11.5-slim
  script:
    - |
      pip install -i https://pypi.tuna.tsinghua.edu.cn/simple flake8
      flake8 --max-line-length=120 ./server/
  only:
    - merge_requests
  except:
    - schedules

pytest:
  stage: ut
  image: harbor.yf-online-gy3.service.gy.ntes/langbase/python:********-slim
  script:
    - |
      export TESTING=true
      export OPENAI_API_KEY=sk-0-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
      export MOONSHOT_API_KEY=sk-0-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
      export NETEASE_API_APP_ID=73510d50-xxxx-41a8-b245-132c6abxxxxx
      export NETEASE_API_APP_KEY=a6bovrfxxxxasjbqjttqkqbtrxxxxx
      cd server
      pip install -i https://mirrors.aliyun.com/pypi/simple poetry
      poetry env use python3.11
      source $(poetry env info --path)/bin/activate
      poetry lock --no-update
      poetry install --no-root
      python -m pytest
      cov=$(poetry run pytest --cov | grep TOTAL | awk '{print substr($4, 1, length($4)-1)}')
      if [ "$cov" -lt 80 ]; then
        echo "coverage is $cov, less than 80"
        exit 1
      fi
  only:
    - merge_requests
  except:
    - schedules

build:
  stage: build
  script:
    - if [[ $CI_COMMIT_TAG == web* ]]; then
        cd web;
        docker build --network=host -f Dockerfile . -t web:latest || exit 1;
        while read IMAGE; do
          echo "pushed to $IMAGE";
          docker tag web:latest $IMAGE;
          docker push $IMAGE || exit 1;
        done < <(jq -r '.[] | .server + "/" + "langbase" + "/" + "web" + ":" + env.APP_REVISION'<<<$REGISTRIES);
      fi
    - if [[ $CI_COMMIT_TAG == server* ]]; then
        cd server;
        docker build --network=host -f Dockerfile . -t server:latest || exit 1;
        while read IMAGE; do
          echo "pushed to $IMAGE";
          docker tag server:latest $IMAGE;
          docker push $IMAGE || exit 1;
        done < <(jq -r '.[] | .server + "/" + "langbase" + "/" + "server" + ":" + env.APP_REVISION'<<<$REGISTRIES);
      fi
  only:
    - tags
  except:
    - schedules