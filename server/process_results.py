import csv
import json
import re
import os


def process_csv_results(csv_file='popo_results2.csv', output_csv='processed_results.csv'):
    """
    读取CSV文件，处理result字段，提取并处理其中的消息内容，计算ai_score，并生成新的CSV文件
    """
    # 检查文件是否存在
    if not os.path.exists(csv_file):
        print(f"错误：找不到文件 {csv_file}")
        return []

    results = []

    # 创建输出CSV文件并写入表头
    with open(output_csv, 'w', encoding='utf-8', newline='') as csvfile:
        fieldnames = ['id', 'popo_url', 'audit_score',
                      'ai_score', 'totalReason', 'advice', 'scores', 'content']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        # 读取原CSV文件并处理
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    # 尝试解析result字段为JSON
                    result_json = json.loads(row['result'])

                    # 初始化数据
                    content = None
                    ai_score = 0
                    total_reason = ""
                    advice = ""
                    scores_data = {}

                    # 检查是否存在 data?.messageList?[0]?.content
                    if 'data' in result_json and result_json['data']:
                        data = result_json['data']
                        if 'messageList' in data and data['messageList'] and len(data['messageList']) > 0:
                            if 'content' in data['messageList'][0]:
                                raw_content = data['messageList'][0]['content']

                                # 去除markdown符号 ```code```
                                if raw_content:
                                    # 替换开始标记 ```语言名称
                                    content_text = re.sub(
                                        r'```(\w*)', '', raw_content)
                                    # 替换结束标记 ```
                                    content_text = re.sub(
                                        r'```', '', content_text)

                                    # 尝试将处理后的内容转换为JSON
                                    try:
                                        # 去除可能的前导和尾随空白
                                        cleaned_content = content_text.strip()
                                        content_json = json.loads(
                                            cleaned_content)
                                        content = content_json  # 如果成功转换，使用转换后的JSON对象

                                        # 提取scores数据
                                        if 'scores' in content:
                                            scores = content['scores']
                                            scores_data = scores  # 保存完整的scores数据

                                            # 计算ai_score
                                            total_score = 0
                                            total_weight = 0

                                            for category, score_data in scores.items():
                                                if 'score' in score_data and 'weight' in score_data:
                                                    total_score += score_data['score'] * \
                                                        score_data['weight']
                                                    total_weight += score_data['weight']

                                            if total_weight > 0:
                                                ai_score = round(
                                                    total_score / total_weight, 2)

                                        # 提取totalReason和advice
                                        if 'totalReason' in content:
                                            total_reason = content['totalReason']
                                        if 'advice' in content:
                                            advice = content['advice']

                                    except json.JSONDecodeError:
                                        # 如果不是有效的JSON，保留原始处理后的字符串
                                        content = cleaned_content

                    # 构建结果记录
                    result_record = {
                        'id': row['id'],
                        'popo_url': row['popo_url'],
                        'audit_score': row['audit_score'],
                        'ai_score': ai_score,
                        'totalReason': total_reason,
                        'advice': advice,
                        'scores': json.dumps(scores_data, ensure_ascii=False) if scores_data else "",
                        'content': json.dumps(content, ensure_ascii=False) if content else ""
                    }

                    # 写入CSV
                    writer.writerow(result_record)

                    # 添加到结果列表用于返回
                    results.append(result_record)

                except json.JSONDecodeError as e:
                    print(f"解析JSON时出错 (ID: {row['id']}): {str(e)}")
                except Exception as e:
                    print(f"处理行时出错 (ID: {row['id']}): {str(e)}")

    return results


def main():
    output_csv = 'processed_results.csv'
    results = process_csv_results(output_csv=output_csv)

    if results:
        # 打印处理的总记录数
        print(f"\n总共处理了 {len(results)} 条记录并保存到 {output_csv}")

        # 打印前几条结果作为示例
        print("\n示例结果:")
        sample_count = min(3, len(results))
        for idx, result in enumerate(results[:sample_count], 1):
            print(f"\n记录 {idx}:")
            print(f"ID: {result['id']}")
            print(f"POPO URL: {result['popo_url']}")
            print(f"审核分数: {result['audit_score']}")
            print(f"AI分数: {result['ai_score']}")

            # 显示分数详情，如果存在
            if result['scores'] and result['scores'] != "":
                try:
                    scores_json = json.loads(result['scores'])
                    print("分数详情:")
                    for category, score_info in scores_json.items():
                        score = score_info.get('score', 'N/A')
                        weight = score_info.get('weight', 'N/A')
                        print(f"  - {category}: 分数={score}, 权重={weight}")
                except Exception as e:
                    print(f"分数详情: 无法解析 - {str(e)}")

            # 显示总体评价，最多显示100个字符
            if result['totalReason']:
                total_reason_preview = result['totalReason']
                if len(total_reason_preview) > 100:
                    total_reason_preview = total_reason_preview[:100] + "..."
                print(f"总体评价: {total_reason_preview}")

            # 显示建议，最多显示100个字符
            if result['advice']:
                advice_preview = result['advice']
                if len(advice_preview) > 100:
                    advice_preview = advice_preview[:100] + "..."
                print(f"建议: {advice_preview}")


if __name__ == '__main__':
    main()
