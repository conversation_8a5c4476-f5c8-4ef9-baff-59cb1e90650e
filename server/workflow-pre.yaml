engines:
  - id: f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea
    workspaceId: d144183b-1363-446f-99a3-12cddd767da8
    name: aio
    endpoint: "http://langbase-workflow-engine-pre-gz.yf-online-gy1.service.gy.ntes"
    token: "851a990b6083408c9243db8d8b7db666"
    description: "music aio workflow engine"
s3:
  accessKey: 319a693f5570493584f7271dd787a68a
  secretKey: 6faa919324244cf7861a7a10627562ce
  region: JD
  endpoint: nos-jd.163yun.com
  bucket: jd-workflow-tmp
  disableSSL: false
  skipVerify: true
  s3ForcePathStyle: false
aisong:
  endpoint: "http://music.163.com"
cio:
  endpoint: "http://music.163.com"
midjourney:
  endpoint: "http://*************:8080"
  pollIntervalMs: 8000
airship:
  endpoint: "http://qa.igame.service.163.org"
skyeye:
  endpoint: "http://music.163.com"
aioCallback:
  domain: "http://music-nydus-nameserver.service.163.org:80/rocketmq/nsaddr"
  topic: "music_content_workflow_external_task_complete"
  producerId: "pid_langbase_workflow_online"
dreammaker:
  endpoint: "http://api-in.dreammaker-public.netease.com"
  user: "_tmax__langbase"
  token: "65670df05ca41869cc57d001"
  auth_endpoint: "http://auth.nie.netease.com/api/v2/tokens"
  key: "8d442dba6ba14cf491890622d97ee5f3"
  ttl: 86400
  pollIntervalMs: 3000
langbaseProxy:
  endpoint: "http://langbase-proxy-online.yf-online-gy1.service.gy.ntes"
