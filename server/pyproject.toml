[tool.poetry]
name = "server"
version = "0.1.0"
description = ""
authors = ["kiloson <<EMAIL>>"]
readme = "README.md"

[tool.pyright]
venvPath = "."
venv = ".venv"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.103.1"
uvicorn = { extras = ["standard"], version = "^0.23.2" }
sqlalchemy = "^2.0.20"
pydantic = "^2.3.0"
pydantic-settings = "^2.0.3"
fastapi-sessions = "^0.3.2"
uuid = "^1.30"
requests = "^2.31.0"
requests-oauthlib = "^1.3.1"
mysqlclient = "^2.2.0"
pymysql = "^1.1.0"
starlette = "^0.27"
loguru = "^0.7.1"
pydantic-yaml = "^1.1.1"
redis = { extras = ["hiredis"], version = "^5.0.0" }
sqlalchemy-easy-softdelete = "^0.8.3"
sse-starlette = "^1.6.5"
openai = "^1.61.0"
jinja2 = "^3.1.2"
asyncinit = "^0.2.4"
pyjwt = "^2.8.0"
boto3 = "^1.28.73"
python-multipart = "^0.0.6"
apscheduler = "^3.10.4"
requests-mock = "^1.11.0"
prometheus-fastapi-instrumentator = "^6.1.0"
tiktoken = "^0.5.1"
blinker = "^1.7.0"
pytest-httpx = "0.27.0"

rocketmq = "0.4.4"
pillow = "10.1.0"
aiobotocore = "2.8.0"
yappi = "^1.6.0"
objgraph = "3.6.0"
curl-cffi = "^0.7.1"
pandas = "^2.0.0"
openpyxl = "^3.0.0"
markitdown = "^0.0.1a5"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.2"
httpx = "^0.25.0"
anyio = "^3.7.1"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"


[[tool.poetry.source]]
name = "mirrors"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "default"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
