
# API_KEY = 'sk-fe54640769344364aeeee71f0697eb15'
# END_POINT = 'https://api.deepseek.com'
# END_POINT = 'https://aigw-int.netease.com/v1'
import openai
import json

API_KEY = 'zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh'
END_POINT = 'https://aigw.netease.com/v1'
# END_POINT = 'http://ai-text-service-test.apps-hangyan.danlu.netease.com/api/v2/text/chat'

# model = 'deepseek-r1-250120-browsing'
model = 'qwen3-235b-a22b'

client = openai.OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

chunks = client.chat.completions.create(
    model=model,
    messages=[
        {
            'role': 'user',
            'content': '哪吒的票房多少?',
        }
    ],
    stream=False,
    max_tokens=500,
)

reasoning_content = ''
content = ''

# 先流式输出 reasoning_content
for chunk in chunks:
    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
        print('reasoning_content: ' + chunk.choices[0].delta.reasoning_content)
    elif chunk.choices[0].delta.content:
        print('content: ' + chunk.choices[0].delta.content)
