
import asyncio
from email import message
from openai import OpenAI

# API_KEY = 'zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh'
# END_POINT = 'https://aigw.netease.com/aliyun/simulation/v1'

API_KEY = 'os4mtnRbe7fqTQEokm4iUUHDuwn3Xpjm4k0_68stY4g'
END_POINT = 'https://langbase-pre.netease.com/api/v1/app/8a6bb323-d0f4-4ef5-b8e1-7ce1c46c597a/openai-proxy'

model = 'qwen-plus'

client = OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

completion = client.chat.completions.create(
    model=model,
    messages=[
        {
            "role": "user",
            "content": "我叫勾勾"
        },
        {
            "role": "assistant",
            "content": "好的，你叫勾勾"
        },
        {
            "role": "user",
            "content": "我叫什么名字"
        }
    ]
)

print(completion.choices[0].message.content)
