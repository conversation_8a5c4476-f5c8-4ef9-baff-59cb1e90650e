import time
import asyncio
import json
from openai import AsyncOpenAI
import csv
from datetime import datetime
from pathlib import Path
from speed_test_models import models

provider_white_list = ['google']

model_white_list = []

# 测试用例
json_test_cases = {
    "order_en": {
        "role": "user",
        "content": """Generate a JSON for a product order with these details:
Customer: <PERSON>
Items:
1. iPhone 15 Pro - $999 x 2
2. AirPods Pro - $249 x 1
3. iPhone Case - $49 x 2
Shipping Address: 123 Main St, New York, NY 10001
Contact: ******-0123
Note: Please gift wrap the AirPods

Return only valid JSON with subtotal, tax (8.875%), shipping ($15), and total."""
    },
    "order_zh": {
        "role": "user",
        "content": """生成一个订单的JSON，包含以下信息：
客户：张三
商品：
1. 华为 Mate 60 Pro - ¥6999 x 1
2. 华为手表 - ¥1999 x 1
3. 保护壳 - ¥199 x 2
收货地址：北京市海淀区中关村大街1号
联系电话：138-1234-5678
备注：手表需要礼品包装

只返回有效的JSON格式，包含小计、税费(6.5%)、运费(¥20)和总价。"""
    }
}


def is_valid_json(text: str) -> bool:
    """检查文本是否为有效的JSON格式"""
    try:
        json.loads(text)
        return True
    except json.JSONDecodeError:
        return False


async def test_json_capability(model_name: str, api_key: str, api_base: str, test_case: dict):
    """测试单个模型的JSON能力"""
    client = AsyncOpenAI(api_key=api_key, base_url=api_base)

    try:
        response = await client.chat.completions.create(
            model=model_name,
            messages=[
                test_case
            ],
            response_format={"type": "json_object"}
        )

        response_text = response.choices[0].message.content
        is_json = is_valid_json(response_text)

        return {
            'success': is_json,
            'text': response_text,
            'error': None if is_json else "Invalid JSON format"
        }
    except Exception as e:
        if 'please try again later' in str(e):
            await asyncio.sleep(10)
            return await test_json_capability(model_name, api_key, api_base, test_case)
        return {
            'success': False,
            'text': None,
            'error': str(e)
        }


def save_results(results: list, csv_path: str):
    """保存试结果到CSV文件"""
    file_exists = Path(csv_path).exists()
    with open(csv_path, 'a', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=[
            'timestamp', 'model', 'provider', 'test_case',
            'iteration', 'success', 'text', 'error'
        ])
        if not file_exists:
            writer.writeheader()
        writer.writerows(results)


async def main():
    # 配置
    test_iterations = 5  # 每个测试用例执行5次
    timestamp = datetime.now().strftime("%Y%m%d-%H%M")
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    csv_path = results_dir / f"json_test_results_{timestamp}.csv"

    # 收集支持JSON的模型
    if model_white_list:
        json_models = [m for m in models if m["name"]
                       in model_white_list and m.get("enable", True) and 'json' in m.get("tag", [])]
    else:
        json_models = [m for m in models if m["provider_kind"]
                       in provider_white_list and m.get("enable", True) and 'json' in m.get("tag", [])]

    print(f"Found {len(json_models)} models supporting JSON mode:")
    for model in json_models:
        print(f"- {model['name']} ({model['provider_kind']})")

    # 执行测试
    results = []
    total_tests = len(json_models) * len(json_test_cases) * test_iterations
    completed = 0

    for model in json_models:
        success_count = 0
        total_count = 0

        for case_name, test_case in json_test_cases.items():
            for iteration in range(test_iterations):
                completed += 1
                print(
                    f"Progress: {completed}/{total_tests} - Testing {model['name']} on {case_name} (iteration {iteration + 1})")

                result = await test_json_capability(
                    model['name'],
                    model['api_key'],
                    model['api_base'],
                    test_case
                )

                total_count += 1
                if result['success']:
                    success_count += 1

                results.append({
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'model': model['name'],
                    'provider': model['provider_kind'],
                    'test_case': case_name,
                    'iteration': iteration + 1,
                    'success': result['success'],
                    'text': result['text'],
                    'error': result['error']
                })

                # 每完成一次测试就保存结果
                save_results([results[-1]], csv_path)

                # 添加短暂延迟
                await asyncio.sleep(1)

        # 打印每个模型的成功率
        success_rate = (success_count / total_count) * 100
        print(f"\n{model['name']} JSON Success Rate: {success_rate:.2f}%")

    print("\nTesting completed!")
    print(f"Results saved to: {csv_path}")

if __name__ == "__main__":
    asyncio.run(main())
