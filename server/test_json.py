import openai
import json

API_KEY = 'zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh'
END_POINT = 'https://aigw.netease.com/v1'

# model = 'deepseek-r1-250120'
model = 'o3-mini-2025-01-31'

client = openai.OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

resp = client.chat.completions.create(
    model=model,
    messages=[
        {
            'role': 'user',
            'content': '告诉我 1+1=?',
        }
    ],
    stream=False,
    max_tokens=500,
)

# 输出：
# 1 + 1 = 2（数学上的标准结果）。
print(resp.choices[0].message.content)

# 输出：
# 不过，如果你是在特定情境下提问（例如二进制、逻辑运算、趣味谜题等），答案可能会不同。比如：
# - 二进制中：1 + 1 = 10
# - 布尔代数中：1 + 1 = 1（逻辑“或”运算）
# - 趣味回答：1滴水 + 1滴水 = 1大滴水 😄
#
# 需要具体场景的话可以告诉我哦！
# 嗯，用户问1加1等于多少，这个问题看起来很简单，但可能背后有原因。首先，我得确认用户是不是在测试我，或者有没有隐藏的意图。可能用户是刚开始学习数学的小孩子，或者他们想看看我会不会给出幽默的答案，比如在二进制里1+1等于10，或者在某种逻辑运算中的不同结果。
#
# 也有可能用户想确认我的回答能力，或者是否存在更深层次的问题。比如，在某些情况下，1加1可能代表其他含义，比如团队合作中的协同效应，结果可能大于2。但通常来说，数学上的基础加法，1+1等于2是标准答案。
#
# 我需要保持回答准确，同时也要简洁，避免让用户觉得啰嗦。不过用户可能希望得到更详细的解释，所以可以简要说明不同情况下的可能性，但主要给出正确答案。另外，要确保没有误解，用户确实是在问数学问题，而不是其他隐喻的问题。所以，直接回答2，然后补充如果有其他背景可以进一步解释，这样比较稳妥。
if resp.choices[0].message.reasoning_content:
    print(resp.choices[0].message.reasoning_content)
