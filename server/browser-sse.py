import openai
import json

API_KEY = 'zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh'
END_POINT = 'https://aigw-int.netease.com/v1'

# model = 'deepseek-r1-250120'
model = 'deepseek-v3-browsing-latest'

client = openai.OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

chunks = client.chat.completions.create(
    model=model,
    messages=[
        {
            'role': 'user',
            'content': '告诉我 1+1=?',
        }
    ],
    stream=True,
    max_tokens=500,
)

reasoning_content = ''
content = ''

# 先流式输出 reasoning_content
for chunk in chunks:
    print(chunk.choices[0])
    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
        print('reasoning_content: ' + chunk.choices[0].delta.reasoning_content)
    elif chunk.choices[0].delta.content:
        print('content: ' + chunk.choices[0].delta.content)
