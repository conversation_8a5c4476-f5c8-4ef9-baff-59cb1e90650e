
import asyncio
from email import message
from openai import AsyncOpenAI

# API_KEY = 'sk-fe54640769344364aeeee71f0697eb15'
# END_POINT = 'https://api.deepseek.com'
API_KEY = 'zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh'
# END_POINT = 'https://aigw-int.netease.com/v1'
END_POINT = 'https://aigw.netease.com/deepseek/simulation/v1'

# model = 'deepseek-chat1'
# model = 'claude-3-5-sonnet-20240620'
# model = 'deepseek-reasoner'
# model = 'deepseek-chat'
model = 'qwen-plus'

# model = 'gpt-4o-mini-2024-07-18'


messages = [
    {
        "role": "user",
        "content": [
            {"type": "text", "text": "下面这个图片里有什么？"},
            {"image_url": {"url": "https://jd-workflow-tmp.nos-jd.163yun.com/langbase/app/upload/files/person-skating.svg"}, "type": "image_url"},
            # {
            #     "type": "image_url",
            #     "image_url": {
            #         "url": "https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/45700641989/fe36/4f2e/a426/247152b444fa3aaf462f959e3b08bb34.png",
            #     },
            # }
        ],
    }
]


async def chat():
    response = await AsyncOpenAI(api_key=API_KEY, base_url=END_POINT).chat.completions.create(
        model=model,
        messages=messages,  # type: ignore
        # response_format={"type": "json_object"},
    )
    # response = await AsyncOpenAI(api_key=API_KEY, base_url=END_POINT).embeddings.create(
    #     model='text-embedding-ada-002',
    #     input=['今天是一个大阴天'],  # type: ignore
    # )
    # print(END_POINT, response.data[0].embedding)
    print(response)


if __name__ == "__main__":
    asyncio.run(chat())
