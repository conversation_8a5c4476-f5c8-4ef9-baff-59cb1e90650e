import openai

API_KEY = 'sk-XCJDtwvFEbl5qBtpXgTjYkTDvfOSzekcwPIz8nIC4Vb9TgVd'
END_POINT = 'http://ai-text-service-test.apps-hangyan.danlu.netease.com/v1'


model = 'deepseek-r1-local'

client = openai.OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

resp = client.chat.completions.create(
    model=model,
    messages=[
        {
            'role': 'user',
            'content': '1+1=?<think>\n',
        }
    ],
    temperature=0.6,
    stream=True,
    max_tokens=8192,
)

# print(resp.choices[0].message.content)

# if hasattr(resp.choices[0].message, 'reasoning_content') and resp.choices[0].message.reasoning_content:
#     print(resp.choices[0].message.reasoning_content)

for chunk in resp:
    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
        print('reasoning_content: ' + chunk.choices[0].delta.reasoning_content)
    elif chunk.choices[0].delta.content:
        print('content: ' + chunk.choices[0].delta.content)
