engines:
  - id: f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea
    workspaceId: d144183b-1363-446f-99a3-12cddd767da8
    name: aio
    endpoint: "http://qa.igame.163.com"
    token: "851a990b6083408c9243db8d8b7db666"
    description: "music aio workflow engine"
s3:
  accessKey: 319a693f5570493584f7271dd787a68a
  secretKey: 6faa919324244cf7861a7a10627562ce
  region: JD
  endpoint: nos-jd.163yun.com
  bucket: jd-workflow-tmp
  disableSSL: false
  skipVerify: true
  s3ForcePathStyle: false
aisong:
  endpoint: "http://cms.qa.igame.163.com"
cio:
  endpoint: "http://cms.qa.igame.163.com"
midjourney:
  endpoint: "http://*************:8080"
airship:
  endpoint: "https://cms.qa.igame.163.com"
skyeye:
  endpoint: "https://cms.qa.igame.163.com"
aioCallback:
  domain: "http://jmenv.igame.163.com:80/rocketmq/nsaddr_dev"
  topic: "music_content_workflow_external_task_complete"
  producerId: "pid_langbase_test"
dreammaker:
  endpoint: "http://api-in.dreammaker-public.netease.com"
  user: "_tmax__langbase"
  token: "65670df05ca41869cc57d001"
  auth_endpoint: "http://auth.nie.netease.com/api/v2/tokens"
  key: "8d442dba6ba14cf491890622d97ee5f3"
  ttl: 86400
langbaseProxy:
  endpoint: "http://langbase-workflow.yf-onlinetest4.netease.com"
