defaultRole: external_user
rolePriorityDesc:
  - admin
  - developer
  - viewer
  - external_user
roles:
  - name: admin
    desc: admin user
    rules: []

  - name: developer
    desc: Developer user
    rules:
      - resources:
          - workspace
          - component-categories
          - workflow-engines
          - workspace/group
          - workspace/app
          - roles
          - app/deploy-history
          - workspace/model-provider
          - group/model-provider
          - workspace/default-model
          - group/default-model
          - app/conversation
          - app/metrics
          - app/messages
          - conversation/message
          - app/conversation-per-user
          - app/user-usage-count
          - app/token-per-seconds
          - app/token-count
          - app/message-per-conv
          - workspace/workflow-engines
          - group/components
          - app/components
          - group/custom_components
          - app/custom_components
          - app-template
          - workspace/basic-app
          - group/token
          - workspace/knowledge
          - system/notify
          - app/setting-config
          - app/setting-versioned-config
          - app/setting
          - app/property-search
          - app/save-trigger
          - app/trigger-variables
          - app/query-trigger
          - app/token
          - app/events
          - app/status
          - app/workflow-runs
          - app-config
        verbs:
          - list
      - resources:
          - app/temp-token
          - app/uploadfile
          - app/agentw-chat
          - app/virtual-human-chat
          - app/agent-variables
          - curl/cffi
          - download/cffi
          - proxy
          - app/post-common-proxy
          - app/get-common-proxy
          - app/config-snapshot
          - group/app
          - app/copy
          - app/trigger
          - app/debug
          - app/deploy
          - components/env-variables
          - workspace/env-variables
          - group/env-variables
          - app/env-variables
          - group/knowledge
          - app/setting
          - app/setting-config
          - app/setting-publish
          - app/property-update
          - app/property-search
          - app/publish-channels
          - app/publish-rollback
          - app/publish-history
          - app/portrait-query
          - app/portrait-add
          - app/portrait-style
          - app/portrait-prompt-polish
          - app/portrait-generate
          - app/portrait-generate-result
          - app/task-query
          - app/emotion-add
          - app/emotion-delete
          - app/emotion-query
          - app/save-trigger
          - app/trigger-variables
          - app/emotion-query-by-ids
          - app/query-trigger
          - app/nonuser-variables
          - group/aigw_bind
          - workspace/aigw_bind
          - prompt-tools
          - app/star
          - app/unstar
          - app
        verbs:
          - list
          - post

  - name: viewer
    desc: Viewer user
    rules:
      - resources:
          - workspace
          - component-categories
          - workflow-engines
          - app-config
          - setting
          - system/notify
          - app/setting-config
          - app/setting-versioned-config
          - app/setting
          - workspace/group
          - workspace/app
          - roles
          - group/knowledge
          - app/deploy-history
          - app/deploy
          - app/config-snapshot
          - workspace/model-provider
          - group/model-provider
          - workspace/default-model
          - group/default-model
          - app/conversation
          - conversation/message
          - app/conversation-per-user
          - app/user-usage-count
          - app/token-per-seconds
          - app/token-count
          - app/debug
          - app/message-per-conv
          - workspace/workflow-engines
          - app/events
          - app/status
          - app-template
          - workspace/basic-app
          - workspace/knowledge
          - app/portrait-query
          - app/portrait-style
          - app/portrait-prompt-polish
          - app/portrait-generate
          - app/portrait-generate-result
          - app/task-query
          - app/emotion-query
          - app/trigger-variables
          - app/emotion-query-by-ids
          - app/get-common-proxy
          - group
          - app
          - workspace/admins
          - workspace/admin
          - group/admins
          - knowledge
        verbs:
          - list
      - resources:
          - app/temp-token
          - app/agent-variables
          - app/nonuser-variables
          - prompt-tools
          - app/portrait-add
          - workspace/member
          - group/member
          - app/member
          - workspace/members
          - group/members
          - app/members
          - app/star
          - app/unstar
          - app/lock
        verbs:
          - list
          - post

  - name: external_user
    desc: External user for langbase system
    rules:
      - resources:
          - workspace
          - component-categories
          - workflow-engines
          - app-config
          - setting
          - system/notify
          - app/setting-config
          - app/setting-versioned-config
          - app/setting
          - workspace/group
          - workspace/app
          - roles
          - group/knowledge
          - app/deploy-history
          - app/deploy
          - app/debug
          - app/config-snapshot
          - workspace/model-provider
          - group/model-provider
          - workspace/default-model
          - group/default-model
          - app/conversation
          - conversation/message
          - app/conversation-per-user
          - app/user-usage-count
          - app/token-per-seconds
          - app/token-count
          - app/message-per-conv
          - workspace/workflow-engines
          - app/events
          - app/status
          - app-template
          - workspace/basic-app
          - workspace/knowledge
          - app/portrait-query
          - app/portrait-style
          - app/portrait-prompt-polish
          - app/portrait-generate
          - app/portrait-generate-result
          - app/task-query
          - app/emotion-query
          - app/trigger-variables
          - app/emotion-query-by-ids
          - app/get-common-proxy
          - group
          - app
          - workspace/admins
          - workspace/admin
          - group/admins
          - knowledge
        verbs:
          - list
      - resources:
          - app/temp-token
          - prompt-tools
          - workspace/aigw_app/bind
          - group/aigw_app/bind
          - conversation
          - post-common-proxy
          - get-common-proxy
          - alert
          - workspace/sync
          - group/sync
          - aigc/popo-score
          - proxy
        verbs:
          - list
          - post