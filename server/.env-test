SQLALCHEMY_DATABASE_URI=mysql+pymysql://lang:<EMAIL>:3331/langbase-test
OAUTH=[{ "name":"netease", "issuer":"https://login.netease.com", "client_id":"61bebcf6945e11ecb70c246e965dfd84", "client_secret":"f451f82e7f2e4dc5b12e83bd7c7b6e9261bec25a945e11ecb70c246e965dfd84", "scope":"openid profile email", "redirect_uri":"http://langbase-local.yf-dev2.netease.com:3100/api/v1/oauth/callback/netease" }]
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_DB=0
REDIS_CLUSTER=[{ "host": "*************", "port": "7103" }]
ROLES_FILE_PATH=roles.yaml
OPENAI_API_KEY=sk-JRVjpsPPAimYOVGZ3mLH3hm7Yv7Hl0TqV0tKl60mcx0Gtuge
OPENAI_API_BASE=https://aigc-api.hz.netease.com/openai/v1
MOONSHOT_API_KEY=sk-VfGEEJSjbD6wRL3htuyjLyMGmuAWRLdeuvuXcz6XuDCw0Z5D
MOONSHOT_API_BASE=https://api.moonshot.cn/v1
NETEASE_API_APP_ID=73510d50-8261-41a8-b245-132c6ab818b6
NETEASE_API_APP_KEY=a6bovrf5l9sasjbqjttqkqbtr071tq
NETEASE_API_BASE=https://aigc-api.hz.netease.com/openai/api/v2
DEEPSEEK_API_KEY=***********************************
MINIMAX_API_KEY=***********************************
DEEPSEEK_API_BASE=https://api.deepseek.com
ENABLE_QUERY=true
DEFAULT_MODEL=[{"model_type":"llm", "model_name":"gpt-3.5-turbo", "provider_kind":"openai"}]
USE_MLOG=false
TESTING=false
DEBUG=false
DB_SERVICE_URL=http://qa.igame.163.com
WORKFLOW_ENGINE_FILE_PATH=workflow-dev.yaml
ADA_API_BASE=http://adora-proxy.ft.netease.com/api
USE_SERVICE_PROXY=true
SERVICE_PROXY_PREFIX=http://qa.igame.163.com
PMS_APP_KEY=langbase
PMS_APP_SECRET=D224F921A5DD150D
TEST_MODEL_WORKSPACE_ID=f97bf87b-a8ec-4bc9-a8e3-662e111cd2e4
AIGW_AUTH_URL=https://auth.nie.netease.com