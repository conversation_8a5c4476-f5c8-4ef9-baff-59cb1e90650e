import asyncio
import httpx
import csv
import json
import os
from typing import List, Dict, Optional, Any
from src.service.popo_doc import parse_popo_document

# 配置
CSV_FILE = 'popo_results.csv'
FILTERED_CSV_FILE = 'popo_results_filtered.csv'
APP_ID = '89d06be7-7157-464f-85cd-89c7dccf1e0a'
API_URL = 'http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes/api/langbase/agent/completion'
API_TOKEN = 'qVAn2w_pfjOMd99xVCLRbcsyDyorSUm1OQyztKi5R_0'


def update_csv_with_opinions():
    """更新已有CSV文件中opinions为空的记录"""
    if not os.path.exists(CSV_FILE):
        return

    # 读取原CSV文件内容
    rows = []
    fieldnames = None
    with open(CSV_FILE, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        fieldnames = reader.fieldnames
        if fieldnames:  # 确保fieldnames不为None
            for row in reader:
                rows.append(dict(row))

    if not fieldnames:
        return

    # 读取原始数据源以获取opinions
    opinion_map = {}
    score_map = {}
    with open('Result_tech_aggregated.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            popo_url = row.get('url')
            audit_score = row.get('avg_audit_score', '')
            if popo_url:
                opinion_map[popo_url] = row.get('combined_opinions', '')
                score_map[popo_url] = audit_score
    # 检查是否有需要更新的行
    updated = False
    for row in rows:
        popo_url = row.get('popo_url', '')
        row_opinions = row.get('opinions', '')
        row_score = row.get('audit_score', '')
        if not row_opinions and popo_url in opinion_map:
            row['opinions'] = opinion_map[popo_url]
            updated = True
        if not row_score and popo_url in score_map:
            print(f"发现空audit_score {score_map[popo_url]} {popo_url}...")
            row['audit_score'] = score_map[popo_url]
            updated = True

    # 如果有更新，重写CSV文件
    if updated:
        print("发现空opinions字段，正在更新...")
        with open(CSV_FILE, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
        print("更新完成")


# 过滤所有result
def filter_result():
    """过滤所有result json化后不为空json {}，或者 .code 不为200"""
    if not os.path.exists(CSV_FILE):
        print(f"文件 {CSV_FILE} 不存在，无法进行过滤操作")
        return

    # 读取原CSV文件内容
    rows = []
    fieldnames = None
    with open(CSV_FILE, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        fieldnames = reader.fieldnames
        if fieldnames:  # 确保fieldnames不为None
            for row in reader:
                rows.append(dict(row))

    if not fieldnames:
        print("CSV文件格式错误，无法解析表头")
        return

    # 过滤符合条件的行
    filtered_rows = []
    for row in rows:
        result = row.get('result', '{}')

        # 检查result是否为有效的JSON且不为空
        try:
            result_json = json.loads(result) if result else {}

            # 过滤条件：result为空JSON或code为200
            if result_json == {} or result_json.get('code') == 200:
                filtered_rows.append(row)
                print(f"过滤到符合条件的记录: {row.get('popo_url', 'unknown')}")
        except json.JSONDecodeError:
            # JSON解析失败，也将其包含在过滤结果中
            print(f"过滤到JSON解析失败的记录: {row.get('popo_url', 'unknown')}")

    # 将过滤后的结果写入新文件
    if filtered_rows:
        with open(FILTERED_CSV_FILE, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(filtered_rows)
        print(f"过滤完成，共有 {len(filtered_rows)} 条记录符合条件，已写入 {FILTERED_CSV_FILE}")
    else:
        print("没有发现符合条件的记录")

    return filtered_rows


async def main():
    # 先检查并更新已有CSV中缺失的opinions
    # update_csv_with_opinions()

    # 过滤不符合条件的记录
    filter_result()

    # 从 Result_tech_aggregated.csv 读取数据
    # data = read_tech_v2_data()
    # print(f"读取到 {len(data)} 条数据")

    # await process_popos(data)

if __name__ == '__main__':
    asyncio.run(main())
