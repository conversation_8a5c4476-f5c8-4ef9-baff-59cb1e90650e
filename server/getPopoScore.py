import asyncio
import httpx
import csv
import json
import os
import re
import time
from typing import List, Dict
from src.service.popo_doc import parse_popo_document

# 配置
CSV_FILE = 'popo_results_v3.csv'
API_URL = f'http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes/api/langbase/agent/completion'
# APP_ID = '89d06be7-7157-464f-85cd-89c7dccf1e0a'
# API_TOKEN = 'qVAn2w_pfjOMd99xVCLRbcsyDyorSUm1OQyztKi5R_0'
APP_ID = 'd0be9d6b-d318-4e5f-b37c-0a31adf87d24'
API_TOKEN = 'qLYvAmir6ej8OUkFTkBM9eLHnPz-R9M5Fv9tSE2tiK4'


async def call_completion_api(text_content: str) -> Dict:
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    data = {
        'parameters': {
            'document': text_content
        },
        'appId': APP_ID,
        'userId': '123'
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(API_URL, headers=headers, json=data, timeout=1000)
        return response.json()


def get_processed_popos() -> set:
    if not os.path.exists(CSV_FILE):
        return set()

    processed = set()
    with open(CSV_FILE, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            processed.add(row['popo_url'])
    return processed


def process_result(result: dict):
    # 初始化数据
    content = None
    ai_score = 0
    total_reason = ""
    advice = ""
    scores_data = {}
    result_json = result
    # 检查是否存在 data?.messageList?[0]?.content
    if 'data' in result_json and result_json['data']:
        data = result_json['data']
        if 'messageList' in data and data['messageList'] and len(data['messageList']) > 0:
            if 'content' in data['messageList'][0]:
                raw_content = data['messageList'][0]['content']

                # 去除markdown符号 ```code```
                if raw_content:
                    # 替换开始标记 ```语言名称
                    content_text = re.sub(
                        r'```(\w*)', '', raw_content)
                    # 替换结束标记 ```
                    content_text = re.sub(
                        r'```', '', content_text)

                    # 尝试将处理后的内容转换为JSON
                    try:
                        # 去除可能的前导和尾随空白
                        cleaned_content = content_text.strip()
                        content_json = json.loads(
                            cleaned_content)
                        content = content_json  # 如果成功转换，使用转换后的JSON对象

                        # 提取scores数据
                        if 'scores' in content:
                            scores = content['scores']
                            scores_data = scores  # 保存完整的scores数据

                            # 计算ai_score
                            total_score = 0
                            total_weight = 0

                            for category, score_data in scores.items():
                                if 'score' in score_data and 'weight' in score_data:
                                    total_score += score_data['score'] * \
                                        score_data['weight']
                                    total_weight += score_data['weight']

                            if total_weight > 0:
                                ai_score = round(
                                    total_score / total_weight, 2)

                        # 提取totalReason和advice
                        if 'evaluation' in content:
                            total_reason = content['evaluation']
                        if 'advice' in content:
                            advice = content['advice']

                    except json.JSONDecodeError:
                        # 如果不是有效的JSON，保留原始处理后的字符串
                        content = cleaned_content
    return {
        'ai_score': ai_score,
        'total_reason': total_reason,
        'advice': advice,
        'scores_data': scores_data,
        'content': content
    }


def save_result(id: str, popo_url: str, score: str, opinions: str, ai_score: float, total_reason: str, advice: str, scores_data: dict, content: dict, time: float):
    file_exists = os.path.exists(CSV_FILE)

    with open(CSV_FILE, 'a', encoding='utf-8', newline='') as f:
        fieldnames = ['id', 'popo_url', 'audit_score', 'opinions',
                      'ai_score', 'total_reason', 'advice', 'scores_data', 'content', 'time']
        writer = csv.DictWriter(f, fieldnames=fieldnames)

        if not file_exists:
            writer.writeheader()

        writer.writerow({
            'id': id,
            'popo_url': popo_url,
            'audit_score': score,
            'opinions': opinions,
            'ai_score': ai_score,
            'total_reason': total_reason,
            'advice': advice,
            'scores_data': scores_data,
            'content': content,
            'time': time
        })


async def process_popos(data: List[Dict]):
    processed_popos = get_processed_popos()
    total = len(data)

    print(f"总共需要处理 {total} 条数据")
    print(f"已经处理过 {len(processed_popos)} 条数据")

    for index, item in enumerate(data, 1):
        popo_url = item['popo_url']
        if popo_url in processed_popos:
            print(f"[{index}/{total}] 跳过已处理的 popo: {popo_url}")
            continue

        try:
            print(f"[{index}/{total}] 正在处理 popo: {popo_url}")
            # 获取popo文档内容
            popo_result = await parse_popo_document(popo_url)
            if popo_result['code'] != 200:
                print(
                    f"获取popo文档内容失败 {popo_url}: {popo_result}")
                # 保存结果
                save_result(item['id'], popo_url,
                            item['audit_score'], item['opinions'], 0, '', '', {}, popo_result, 0)
                continue
            # 统计耗时
            start_time = time.time()
            print('正在进行评分\n')
            # 调用completion API
            completion_result = await call_completion_api(popo_result['data']['text_content'])
            end_time = time.time()
            # 保存结果
            result = process_result(completion_result)
            print(
                f"评分耗时: {end_time - start_time} 秒, 评分结果: {item['audit_score']}/{result['ai_score']}")
            save_result(item['id'], popo_url,
                        item['audit_score'], item['opinions'], result['ai_score'], result['total_reason'], result['advice'], result['scores_data'], result['content'], end_time - start_time)
            print(f"处理成功: {popo_url}")

        except Exception as e:
            print(f"[{index}/{total}] 处理 popo {popo_url} 时出错: {str(e)}")


def read_tech_v2_data() -> List[Dict]:
    """从 popo_results_filtered 读取数据"""
    data = []
    with open('popo_results_filtered.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        # 用于去重的集合
        processed_urls = set()

        for row in reader:
            # 假设CSV中有 id 和 popo_url 列
            popo_url = row.get('popo_url')
            if not popo_url:
                continue

            # 如果URL已经处理过，跳过
            if popo_url in processed_urls:
                continue

            processed_urls.add(popo_url)
            data.append({
                'id': row.get('id', ''),
                'popo_url': popo_url,
                'audit_score': row.get('audit_score', ''),
                'opinions': row.get('opinions', ''),
            })

    return data


async def main():
    # 从 result_techV2.csv 读取数据
    data = read_tech_v2_data()
    print(f"读取到 {len(data)} 条数据")

    await process_popos(data)

if __name__ == '__main__':
    asyncio.run(main())
