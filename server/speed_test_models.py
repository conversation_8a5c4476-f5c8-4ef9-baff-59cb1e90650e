import httpx
from src.schema.llm_model import LLMModelBase
from typing import List

# Initialize proxy configurations
proxy = {
    "openai": {
        "api_base": "https://aigw.netease.com/v1",
        "api_key": "zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh"
    },
    "minimax": {
        "api_base": "https://aigw.netease.com/v1",
        "api_key": "zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh"
    },
    "deepseek": {
        "api_base": "https://aigw.netease.com/v1",
        "api_key": "zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh"
    },
    "moonshot": {
        "api_base": "https://aigw.netease.com/v1",
        "api_key": "zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh"
    },
    "doubao": {
        "api_base": "https://aigw.netease.com/v1",
        "api_key": "zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh"
    },
    "anthropic": {
        "api_base": "https://aigw.netease.com/v1",
        "api_key": "zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh"
    },
    "qwen": {
        "api_base": "https://aigw.netease.com/aliyun/simulation/v1",
        "api_key": "zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh"
    },
    'google': {
        'api_base': 'https://aigw.netease.com/v1',
        "api_key": "zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh"
    }
}


def get_models() -> List[dict]:
    """从API获取模型列表并处理为测试所需格式"""
    models = []

    try:
        # 发起API请求获取模型列表
        response = httpx.get(
            "https://langbase.netease.com/api/v1/model_v2?workspaceId=f97bf87b-a8ec-4bc9-a8e3-662e111cd2e4")
        response.raise_for_status()
        json = response.json()
        data = json['data']

        # 解析返回的模型数据
        api_models = [LLMModelBase(**model) for model in data]

        # 过滤和转换模型数据
        for model in api_models:
            if model.type != "llm" or (model.tag and 'deprecated' in model.tag):
                continue

            if model.providerKind in proxy:
                models.append({
                    "name": model.name,
                    "api_key": proxy[model.providerKind]["api_key"],
                    "api_base": proxy[model.providerKind]["api_base"],
                    "provider_kind": model.providerKind,
                    "tag": model.tag,
                    "enable": model.enable
                })

    except Exception as e:
        print(f"获取模型列表时发生错误: {e}")
        return []

    return models


# 获取并打印模型信息
models = get_models()
for model in models:
    print(
        f"Model: {model['name']}, "
        f"API Key: {model['api_key']}, "
        f"API Base: {model['api_base']}, "
        f"Provider: {model['provider_kind']}"
    )

# 测试场景定义
# JSON generation tasks
json_scenarios = {
    "json_task_en": """Generate a JSON for a coffee shop order with these items:
1. Latte (Large) - $4.50 x 2
2. Croissant - $3.00 x 3
3. Blueberry Muffin - $2.50 x 1

Customer: Mike Chen
Phone: 555-0123
Note: Extra hot latte, no sugar

Format as JSON with subtotal, tax (8%), and total.""",

    "json_task_zh": """生成一个奶茶店订单的JSON，包含以下商品：
1. 珍珠奶茶（大杯）- ¥18 x 2
2. 椰果奶绿 - ¥16 x 1
3. 蛋糕卷 - ¥12 x 2

顾客：张小明
电话：138-1234-5678
备注：奶茶少糖，多珍珠

请输出JSON格式，包含小计、税费(6%)和总价。"""
}

# Creative writing tasks
creative_scenarios = {
    "creative_text_en": (
        "Write a creative story about an ancient dragon in 300 words. "
        "Include physical description, magical abilities, an encounter with humans, "
        "and a moral lesson. Use vivid imagery and clear story structure."
    ),
    "creative_text_zh": (
        "用300字写一个关于远古神龙的创意故事。必须包含：龙的外形描写、神奇能力、"
        "与人类的相遇经历，以及一个寓意。要求使用生动的意象和比喻，"
        "故事需要有清晰的开端、发展和结尾结构。"
    )
}

# Weather report tasks
weather_scenarios = {
    "regular_conversation_en": (
        "Provide a detailed 300-word weather report analysis including: "
        "current temperature, humidity, wind conditions, precipitation forecast "
        "for the next 24 hours, and how these conditions compare to seasonal averages. "
        "Include specific recommendations for outdoor activities."
    ),
    "regular_conversation_zh": (
        "请提供一份300字的详细天气报告分析，包括：当前气温、湿度、风况、"
        "未来24小时降水预测，以及这些条件与季节平均值的比较。"
        "根据这些天气条件，给出具体的户外活动建议。"
    )
}

# Combine all scenarios and format them for API use
scenarios = {
    k: {"role": "user", "content": v}
    for d in [json_scenarios, creative_scenarios, weather_scenarios]
    for k, v in d.items()
}
