CREATE TABLE  `tb_prompt_template`
(
    `id`               char(36) NOT NULL COMMENT '模版ID',
    `name`             varchar(128) NOT NULL COMMENT '模版名称',
    `description`      varchar(256) DEFAULT NULL COMMENT '模版描述',
    `avatar_url`       varchar(128) NOT NULL COMMENT '模版头像',
    `workspace_id`     char(36) NOT NULL COMMENT '工作空间ID',
    `group_id`         char(36) COMMENT '组ID', 
    `scope`            varchar(20) NOT NULL DEFAULT 'scoped' COMMENT '访问范围',
    `struct_prompt`    json DEFAULT NULL COMMENT '结构化提示词',
    `text_prompt`      text NOT NULL COMMENT '文本提示词',
    `prompt_type`      varchar(36) NOT NULL COMMENT '当前提示词类型',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at`       datetime DEFAULT NULL COMMENT 'null for not deleted',
    `deleted_by`       char(36) DEFAULT NULL,
    `created_by`       char(36) NOT NULL DEFAULT '' COMMENT 'creator',
    `updated_by`       char(36) NOT NULL DEFAULT '' COMMENT 'updater',
    PRIMARY KEY (`id`),
    UNIQUE KEY (`name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;