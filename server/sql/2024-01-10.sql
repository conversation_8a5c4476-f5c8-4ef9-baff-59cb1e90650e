CREATE TABLE `tb_account` (
                              `id` char(36) NOT NULL COMMENT 'user id',
                              `name` varchar(128) NOT NULL DEFAULT '' COMMENT 'name of user',
                              `fullname` varchar(128) NOT NULL DEFAULT '' COMMENT 'fullname of user',
                              `email` varchar(128) DEFAULT NULL COMMENT 'email of user',
                              `is_admin` tinyint(1) NOT NULL DEFAULT 0,
                              `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `uk_email` (`email`),
                              KEY `idx_name` (`name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;


INSERT INTO tb_account (
    id,
    name,
    fullname,
    email,
    is_admin,
    created_at,
    updated_at
)
VALUES (
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           'system-admin',
           '系统管理员',
           '<EMAIL>',
           0,
           '2023-10-19 09:56:04',
           '2023-10-19 09:56:04'
       );


CREATE TABLE `tb_workspace` (
                                `id` char(36) NOT NULL COMMENT 'workspace id',
                                `name` varchar(128) NOT NULL DEFAULT '' COMMENT 'name of workspace',
                                `description` varchar(256) DEFAULT NULL COMMENT 'descr of workspace',
                                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                `deleted_at` datetime DEFAULT NULL COMMENT 'null for not deleted',
                                `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                                `updated_by` char(36) NOT NULL DEFAULT '' COMMENT 'updater',
                                `deleted_by` char(36) DEFAULT NULL,
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `uk_name` (`name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;


INSERT INTO tb_workspace (
    id,
    name,
    description,
    created_at,
    updated_at,
    deleted_at,
    created_by,
    updated_by
)
VALUES (
           '27d0f5da-790f-454f-a498-075d077bea09',
           'Langbase系统',
           NULL,
           '2023-10-19 10:07:59',
           '2023-10-19 10:08:27',
           NULL,
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4'
       );


CREATE TABLE `tb_group` (
                            `id` char(36) NOT NULL COMMENT 'group id, uuid',
                            `workspace_id` char(36) NOT NULL COMMENT 'workspace id',
                            `name` varchar(128) NOT NULL DEFAULT '' COMMENT 'name of workspace',
                            `description` varchar(256) DEFAULT NULL COMMENT 'descr of workspace',
                            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            `deleted_at` datetime DEFAULT NULL COMMENT 'null for not deleted',
                            `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                            `updated_by` char(36) NOT NULL DEFAULT '' COMMENT 'updater',
                            `deleted_by` char(36) DEFAULT NULL,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `uk_workspace_id_name` (`workspace_id`, `name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;


INSERT INTO tb_group (
    id,
    workspace_id,
    name,
    description,
    created_at,
    updated_at,
    deleted_at,
    created_by,
    updated_by
)
VALUES (
           'e2be70f5-cdd8-461d-bfa4-20c04fae7f05',
           'b6035dbe-ff4a-4d0c-bd16-e1d5588afe2f',
           '默认分组',
           NULL,
           '2023-10-19 10:09:28',
           '2023-10-19 10:16:49',
           NULL,
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4'
       );


CREATE TABLE `tb_member` (
                             `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                             `role` varchar(36) NOT NULL COMMENT 'binding role name',
                             `resource_type` varchar(36) NOT NULL COMMENT 'Worspace/Group/App',
                             `resource_id` char(36) NOT NULL COMMENT 'resource id',
                             `member_type` varchar(10) NOT NULL DEFAULT '0' COMMENT '0-user',
                             `member_id` char(36) NOT NULL COMMENT 'UserID',
                             `granted_by` char(36) NOT NULL COMMENT 'who grant the role',
                             `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                             `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `uk_resource_member` (
                                 `resource_id`,
                                 `member_id`,
                                 `resource_type`,
                                 `member_type`
                                 ),
                             KEY `idx_member` (`member_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4;


INSERT INTO tb_member (
    `role`,
    resource_type,
    resource_id,
    member_type,
    member_id,
    granted_by,
    created_at,
    updated_at
)
VALUES (
           'admin',
           'workspace',
           '27d0f5da-790f-454f-a498-075d077bea09',
           'user',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '2023-10-19 10:08:00',
           '2023-10-19 10:08:00'
       );


CREATE TABLE `tb_model_provider_binding` (
                                             `id` char(36) NOT NULL COMMENT 'group id, uuid',
                                             `kind` char(36) NOT NULL COMMENT 'provider type, openAI、claude, etc',
                                             `workspace_id` char(36) NOT NULL COMMENT 'reference to workspace',
                                             `group_id` char(36) COMMENT 'reference to group',
                                             `description` varchar(256) DEFAULT NULL COMMENT 'descr of workspace',
                                             `endpoint` varchar(256) COMMENT 'the api base addr of model provider',
                                             `api_key` varchar(256) COMMENT 'the default api key of model',
                                             `config` JSON DEFAULT NULL COMMENT 'basic config of model provider',
                                             `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                             `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                             `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                                             `updated_by` char(36) NOT NULL DEFAULT '' COMMENT 'updater',
                                             PRIMARY KEY (`id`),
                                             KEY `idx_workspace` (`workspace_id`),
                                             KEY `idx_group` (`group_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4;


INSERT INTO tb_model_provider_binding (
    id,
    kind,
    workspace_id,
    group_id,
    description,
    endpoint,
    api_key,
    config,
    created_at,
    updated_at,
    created_by,
    updated_by
)
VALUES (
           '764aa08b-b294-428f-b76b-e5c965b4dd25',
           'openai',
           '27d0f5da-790f-454f-a498-075d077bea09',
           NULL,
           NULL,
           NULL,
           NULL,
           NULL,
           '2023-10-19 10:19:10',
           '2023-10-19 10:19:10',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4'
       );


CREATE TABLE `tb_default_model_config` (
                                           `id` char(36) NOT NULL COMMENT 'group id, uuid',
                                           `workspace_id` char(36) NOT NULL COMMENT 'reference to workspace',
                                           `group_id` char(36) COMMENT 'reference to group',
                                           `model_type` char(36) NOT NULL COMMENT 'embeding/text-generation/speeach2text',
                                           `model_name` varchar(256) NOT NULL COMMENT 'name of model',
                                           `provider_kind` char(36) NOT NULL DEFAULT '',
    -- 可以直接找到provider
                                           `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                           `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                           `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                                           `updated_by` char(36) NOT NULL DEFAULT '' COMMENT 'updater',
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `uk_workspace_type` (`workspace_id`, `model_type`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4;


INSERT INTO tb_default_model_config (
    id,
    workspace_id,
    group_id,
    model_type,
    model_name,
    provider_kind,
    created_at,
    updated_at,
    created_by,
    updated_by
)
VALUES (
           'd86b9b23-c09f-49da-a343-eaf1e5bf9717',
           '27d0f5da-790f-454f-a498-075d077bea09',
           NULL,
           'llm',
           'gpt-3.5-turbo',
           'openai',
           '2023-10-19 10:19:10',
           '2023-10-19 10:19:10',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4'
       );


CREATE TABLE `tb_app` (
                          `id` char(36) NOT NULL,
                          `workspace_id` char(36) NOT NULL COMMENT 'reference to workspace',
                          `group_id` char(36) NOT NULL COMMENT 'reference to group',
                          `type` varchar(36) NOT NULL COMMENT 'agent/workflow',
                          `description` varchar(256) DEFAULT NULL,
                          `query_sample` varchar(256) DEFAULT NULL,
                          `name` varchar(256) NOT NULL,
                          `is_template` tinyint(1) NOT NULL DEFAULT 0,
                          `is_basic` tinyint(1) NOT NULL DEFAULT 0,
                          `app_config_id` char(36) DEFAULT NULL COMMENT 'reference to app model config',
                          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                          `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                          `deleted_at` datetime DEFAULT NULL COMMENT 'null for not deleted',
                          `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                          `updated_by` char(36) NOT NULL DEFAULT '' COMMENT 'updater',
                          `deleted_by` char(36) DEFAULT NULL,
                          PRIMARY KEY (`id`),
                          UNIQUE KEY `uk_workspace_id_name` (`workspace_id`, `name`),
                          KEY `idx_group` (`group_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

INSERT INTO tb_app (
    id,
    workspace_id,
    group_id,
    `type`,
    description,
    name,
    is_template,
    is_basic,
    app_config_id,
    created_at,
    updated_at,
    deleted_at,
    created_by,
    updated_by
)
VALUES (
           '1a7defb2-1aab-4035-849e-8648bb494a49',
           '27d0f5da-790f-454f-a498-075d077bea09',
           'e2be70f5-cdd8-461d-bfa4-20c04fae7f05',
           'agent-completion',
           NULL,
           '文本翻译器',
           1,
           0,
           '014377f7-e67a-49b4-b9ee-b521959f478c',
           '2023-10-19 10:17:12',
           '2023-10-19 10:26:46',
           NULL,
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4'
       ),
       (
           '1bd573f0-87d3-4e48-9536-7c61176a09e0',
           '27d0f5da-790f-454f-a498-075d077bea09',
           'e2be70f5-cdd8-461d-bfa4-20c04fae7f05',
           'agent-conversation',
           NULL,
           '前端面试官',
           1,
           0,
           'ba0abf42-c244-4cad-a989-64194e1b738e',
           '2023-10-19 10:27:26',
           '2023-10-19 10:30:37',
           NULL,
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4'
       );


CREATE TABLE `tb_versioned_app_config` (
                                           `id` char(36) NOT NULL,
                                           `app_id` char(36) NOT NULL,
                                           `hash` char(64) NOT NULL,
                                           `type` char(30) NOT NULL COMMENT 'snapshot or history',
                                           `config` json NOT NULL,
    -- 存放agent和workflow的配置
                                           `components` json DEFAULT NULL,
    -- 依赖组件id list，数据json，组件删除前必须保证没有任何workflow引用它
                                           `status` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '0: 成功， 1：失败，2：unknow',
                                           `message` varchar(256) DEFAULT NULL,
    -- 发布状态
                                           `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                           `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                           `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                                           `updated_by` char(36) NOT NULL DEFAULT '' COMMENT 'updater',
                                           `deleted_at` datetime DEFAULT NULL COMMENT 'null for not deleted',
                                           `deleted_by` char(36) DEFAULT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `idx_app_id` (`app_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;


INSERT INTO tb_versioned_app_config (
    id,
    app_id,
    hash,
    `type`,
    config,
    created_at,
    updated_at,
    created_by,
    updated_by,
    deleted_at,
    components,
    status,
    message
)
VALUES (
           '014377f7-e67a-49b4-b9ee-b521959f478c',
           '1a7defb2-1aab-4035-849e-8648bb494a49',
           'ecd36e9a415b95ae4d0a882b4c5e726eb69a1c83a6c2d1bcf420081b452d5e34',
           'history',
           '{"prologue": null, "modelName": "gpt-3.5-turbo", "prePrompt": "你作为一个翻译专家，帮助别人翻译语言，请将以下{{source}}内容翻译为{{target}}:\\n{{input}}", "modelParams": {}, "providerKind": "openai", "paramsInPrompt": [{"key": "source", "type": "list", "title": "源语言", "config": {"options": [{"value": "中文"}, {"value": "英文"}, {"value": "日文"}, {"value": "法语"}]}, "required": false}, {"key": "target", "type": "list", "title": "目标语言", "config": {"options": [{"value": "中文"}, {"value": "日文"}, {"value": "韩文"}, {"value": "法语"}]}, "required": false}, {"key": "input", "type": "area", "title": "内容", "config": {"options": []}, "required": false}]}',
           '2023-10-19 10:26:46',
           '2023-10-19 10:26:46',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           NULL,
           NULL,
           0,
           NULL
       ),
       (
           'ba0abf42-c244-4cad-a989-64194e1b738e',
           '1bd573f0-87d3-4e48-9536-7c61176a09e0',
           'eeef27d2e137caf17575f04ac792b54386ee36e357b8e19cdf21ae714fc3a696',
           'history',
           '{"prologue": null, "modelName": "gpt-3.5-turbo", "prePrompt": "你是一个专业的前端面试官，今天的面试者将要应聘{{career}}，请你列出五到十个问题", "modelParams": {}, "providerKind": "openai", "paramsInPrompt": [{"key": "career", "type": "list", "title": "职位", "config": {"options": [{"value": "react高级开发工程师"}, {"value": "vue高级开发工程师"}]}, "required": false}]}',
           '2023-10-19 10:30:37',
           '2023-10-19 10:30:37',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           '8faf052f-e57d-4e95-94e9-1981c79f58e4',
           NULL,
           NULL,
           0,
           NULL
       );


CREATE TABLE `tb_conversation` (
                                   `id` char(36) NOT NULL,
                                   `app_id` char(36) NOT NULL,
                                   `app_config_id` char(36) NOT NULL,
                                   `override_config` JSON,
    -- 如果该字段不为空，则使用该字段配置
                                   `name` varchar(128) NOT NULL DEFAULT '',
                                   `parameters` JSON,
    -- 用户开启对话时的参数
                                   `prompt_tokens_for_name` int(11) DEFAULT 0 NOT NULL,
                                   `response_tokens_for_name` int(11) DEFAULT 0 NOT NULL,
                                   `total_tokens_for_name` int(11) DEFAULT 0 NOT NULL,

                                   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                   `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                                   `updated_by` char(36) NOT NULL DEFAULT '' COMMENT 'updater',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_app_id` (`app_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;


CREATE TABLE tb_message (
                            `id` char(36) NOT NULL,
                            `app_id` char(36) NOT NULL,
                            `conversation_id` char(36),
                            `query` text NOT NULL,
                            `response` json NOT NULL,
                            `response_type` varchar(36) NOT NULL DEFAULT 'text' COMMENT 'text/tool_call',
                            `tool_call_response_ids` json,
                            `prompt_tokens` int(11) DEFAULT 0 NOT NULL,
                            `response_tokens` int(11) DEFAULT 0 NOT NULL,
                            `total_tokens` int(11) DEFAULT 0 NOT NULL,
                            `time_comsumption_in_ms` int(11) DEFAULT 0 NOT NULL,
                            `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                            PRIMARY KEY (`id`),
                            KEY `idx_app_id` (`app_id`),
                            KEY `idx_conversation_id` (`conversation_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;


CREATE TABLE tb_message_tool_call_response (
                            `id` char(36) NOT NULL,
                            `message_id` char(36) NOT NULL,
                            `tool_call_id` char(36) NOT NULL,
                            `response` text NOT NULL,
                            `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                            PRIMARY KEY (id),
                            KEY idx_message_id (message_id),
                            KEY idx_tool_call_id (tool_call_id)
)

CREATE TABLE tb_token (
                          `id` char(36) NOT NULL,
                          `value` varchar(256) NOT NULL,
                          `resource_type` varchar(36) NOT NULL COMMENT 'Worspace/Group/App',
                          `resource_id` char(36) NOT NULL COMMENT 'resource id',
                          `last_used_at` datetime,
                          `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                          `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
                          PRIMARY KEY(`id`),
                          UNIQUE KEY `uk_value` (`value`),
                          KEY `idx_resource` (`resource_id`, `resource_type`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE  `tb_workflow_datatype`
(
    `id`               char(36) NOT NULL,
    `name`             varchar(128) NOT NULL,
    `config`           json DEFAULT NULL,
    `description`      varchar(256) DEFAULT NULL COMMENT 'descr of data type',
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at`       datetime DEFAULT NULL COMMENT 'null for not deleted',
    `deleted_by` char(36) DEFAULT NULL,
    `created_by`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'creator',
    `updated_by`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'updater',
    PRIMARY KEY (`id`),
    UNIQUE KEY (`name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE `tb_component`
(
    `id`           char(36) NOT NULL,
    `name`         varchar(128) NOT NULL,
    `code`         varchar(128) NOT NULL,
    `description`  varchar(256) DEFAULT NULL COMMENT 'descr of component',
    `type`         varchar(64) NOT NULL,
    `category_id`  char(36) NOT NULL,
    `category_name` varchar(128) NOT NULL DEFAULT '',
    `workspace_id` char(36) NOT NULL,
    `scope`        varchar(20) NOT NULL DEFAULT 'scoped',
    `config`       json DEFAULT NULL,
    `deprecated`   bool NOT NULL DEFAULT false,
    `app_types`    JSON DEFAULT NULL,
    `created_at`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_by`   char(36) NOT NULL COMMENT 'creator',
    `updated_by`   char(36) NOT NULL COMMENT 'updater',
    `deleted_at`   datetime DEFAULT NULL  COMMENT 'null for not deleted',
    `deleted_by` char(36) DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY (`name`),
    UNIQUE KEY (`code`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE `tb_component_category`
(
    `id`           char(36) NOT NULL,
    `name`         varchar(128) NOT NULL  COMMENT 'name of component category',
    `description`  varchar(256) DEFAULT NULL COMMENT 'description of component category',
    `created_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at`  datetime              DEFAULT NULL  COMMENT 'null for not deleted',
    `deleted_by`  char(36) DEFAULT NULL,
    `created_by`   char(36) NOT NULL COMMENT 'creator',
    `updated_by`   char(36) NOT NULL COMMENT 'updater',
    PRIMARY KEY (`id`),
    UNIQUE KEY (`name`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE `tb_env_variable`
(
    `id`           char(36) NOT NULL,
    `name`         varchar(128) NOT NULL,
    `component_id` char(36) NOT NULL,
    `param`        varchar(128) NOT NULL,
    `value`        varchar(1024) DEFAULT NULL,
    `scope_type`   varchar(36) NOT NULL COMMENT 'component、workspace、group、app',
    `scope_id`     char(36) NOT NULL COMMENT 'component id、workspace id、group、app id',
    `created_at`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`   datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at`   datetime   DEFAULT NULL  COMMENT 'null for not deleted',
    `deleted_by` char(36) DEFAULT NULL,
    `created_by`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'creator',
    `updated_by`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'updater',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE `tb_token_related_resource`
(
    `id`         char(36) NOT NULL,
    `token_id`   char(36) NOT NULL,
    `resource_type` varchar(36)         NOT NULL COMMENT 'Group/APP/DATASET',
    `resource_id`   char(36)            NOT NULL COMMENT 'resource id',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `created_by` char(36) NOT NULL DEFAULT '' COMMENT 'creator',
    PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `tb_versioned_app_config_components`
(
    `id`             char(36) NOT NULL,
    `app_id`         char(36) NOT NULL,
    `app_config_id`  char(36) NOT NULL,
    `component_id`   char(36) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE `tb_collection`
(
    `id`         char(36) NOT NULL,
    `user_id`    char(36) NOT NULL,
    `resource_type` varchar(36)         NOT NULL,
    `resource_id`   char(36)            NOT NULL,
    PRIMARY KEY(`id`),
    INDEX idx_user_resource (user_id, resource_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
