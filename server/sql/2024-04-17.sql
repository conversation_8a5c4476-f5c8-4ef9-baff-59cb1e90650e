CREATE TABLE `tb_knowledge` (
    `id` char(36) NOT NULL COMMENT 'group id, uuid',
    `workspace_id` char(36) NOT NULL COMMENT 'workspace id',
    `group_id` char(36) NOT NULL COMMENT '分组 id',
    `name` varchar(128) NOT NULL DEFAULT '' COMMENT '名称',
    `description` varchar(256) DEFAULT NULL COMMENT '描述',
    `collection_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '向量数据库表名',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT 0 COMMENT '删除时间',
    `created_by` char(36) NOT NULL DEFAULT '' COMMENT '创建人',
    `updated_by` char(36) NOT NULL DEFAULT '' COMMENT '更新人',
    `deleted_by` char(36) DEFAULT NULL COMMENT '删除人',
    PRIMARY KEY (`id`),
    KEY `group_id` (`group_id`),
    KEY `collection_id` (`collection_id`),
    UNIQUE KEY `uk_workspace_id_name_deleted_at` (`workspace_id`, `name`, `deleted_at`)
) ENGINE = InnoDB AUTO_INCREMENT=10000 DEFAULT CHARSET = utf8mb4;

CREATE TABLE `tb_document` (
    `id` char(36) NOT NULL COMMENT '文档 id',
    `workspace_id` char(36) NOT NULL COMMENT '空间 id',
    `knowledge_id` char(36) NOT NULL COMMENT '知识库 id',
    `title` varchar(128) DEFAULT '' COMMENT '文档标题',
    `type` varchar(128) DEFAULT '' COMMENT '类型',
    `tag_name` varchar(128) DEFAULT '' COMMENT '文档标签',
    `source` varchar(128) DEFAULT '' COMMENT '来源',
    `config` JSON DEFAULT NULL COMMENT '文档配置',
    `doc_id` bigint(20) unsigned COMMENT '外部文档 id',
    `state` bigint(20) unsigned DEFAULT 0 COMMENT '导入状态',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT 0 COMMENT '删除时间',
    `created_by` char(36) NOT NULL DEFAULT '' COMMENT '创建人',
    `updated_by` char(36) NOT NULL DEFAULT '' COMMENT '更新人',
    `deleted_by` char(36) DEFAULT NULL COMMENT '删除人',
    PRIMARY KEY (`id`),
    KEY `workspace_id` (`workspace_id`),
    KEY `knowledge_id` (`knowledge_id`),
    UNIQUE KEY `uk_knowledge_id_name_deleted_at` (`knowledge_id`, `title`, `deleted_at`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;

CREATE TABLE `tb_document_task` (
    `id` char(36) NOT NULL COMMENT 'id',
    `workspace_id` char(36) NOT NULL COMMENT '空间 id',
    `knowledge_id` char(36) NOT NULL COMMENT '知识库 id',
    `title` varchar(128) DEFAULT '' COMMENT '文档标题',
    `type` varchar(128) DEFAULT '' COMMENT '类型',
    `tag_name` varchar(128) DEFAULT '' COMMENT '文档标签',
    `source` varchar(128) DEFAULT '' COMMENT '来源',
    `config` JSON DEFAULT NULL COMMENT '文档配置',
    `task_id` char(36) DEFAULT '' COMMENT '导入任务 id',
    `task_state` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '导入任务状态',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at` datetime DEFAULT 0 COMMENT '删除时间',
    `created_by` char(36) NOT NULL DEFAULT '' COMMENT '创建人',
    `updated_by` char(36) NOT NULL DEFAULT '' COMMENT '更新人',
    `deleted_by` char(36) DEFAULT NULL COMMENT '删除人',
    PRIMARY KEY (`id`),
    KEY `workspace_id` (`workspace_id`),
    KEY `knowledge_id` (`knowledge_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4;
