ALTER TABLE `horizonapp`.`tb_message`
<PERSON>ANGE `model` `model` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
CHANGE `kind` `kind` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL;

CREATE TABLE `tb_app_metric` (
  `id` char(36) NOT NULL,
  `app_id` char(36) DEFAULT NULL,
  `users` int(11) DEFAULT NULL COMMENT '每日用户数',
  `total_tokens` int(11) NOT NULL DEFAULT '1' COMMENT '每日token数',
  `messages` int(11) NOT NULL COMMENT '每日消息数',
  `conversations` int(11) NOT NULL COMMENT '每日会话数',
  `token_per_second` int(11) NOT NULL COMMENT '每秒token数',
  `token_per_message` int(11) NOT NULL COMMENT '每秒token数',
  `date` datetime NOT NULL,
  `created_at` datetime NOT NULL,
  `extra` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;