FROM harbor.yf-online.service.163.org/langbase/python:3.11-buster

ENV POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_IN_PROJECT=1 \
    POETRY_VIRTUALENVS_CREATE=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple && \
    pip install "poetry==1.6.1"

RUN groupadd -g 10001 netease && useradd -u 10001 -g netease appops

WORKDIR /home/<USER>/approot

COPY poetry.lock pyproject.toml ./
COPY src ./src
COPY roles.yaml ./
COPY workflow.yaml ./
COPY workflow-dev.yaml ./
COPY workflow-test.yaml ./
COPY workflow-pre.yaml ./

RUN poetry install --without dev --no-root && \
    rm -rf ${POETRY_CACHE_DIR} && \
    chown -R appops:netease /home/<USER>

USER 10001

ENTRYPOINT [ "poetry", "run", "python", "-m", "src.langbase" ]
