## Langbase

### Development



#### 环境配置

1. 用vscode打开项目文件夹
2. 进入拓展页面，输入`@recommended`，安装所有推荐拓展
3. 安装python3.11，并通过brew或者pip安装 poetry
4. 进入server目录下，运行命令 `poetry config virtualenvs.in-project true`和`poetry install`
5. 在当前目录下创建`.env`文件，文件内容可访问获取 https://docs.popo.netease.com/lingxi/f430b67b8a7f45d78e7aedf754c7ef9f#bORe-1695281888465
5. 按`F5`，项目运行于`localhost:8000`

#### 访问

访问langbase开发项目（前后端），需要配置hosts文件，将`langbase-local.yf-dev2.netease.com`指向`127.0.0.1`，然后访问`langbase-local.yf-dev2.netease.com:3100`即可


### 一些问题处理
#### rocketMP没有安装
如果已经执行了`poetry install`，但是rocketMP没有安装，可以 https://docs.popo.netease.com/team/pc/d43gdtal/pageDetail/6eafa4c5c0cc453c9b1d0a621e0549c3 把这个解压到 /usr/local/lib 目录下

#### 调试配置
在langbase的根目录下，`.vscode/launch.json`
```
{
	// 使用 IntelliSense 了解相关属性。
	// 悬停以查看现有属性的描述。
	// 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Python 调试程序: FastAPI",
			"type": "debugpy",
			"request": "launch",
			"module": "uvicorn",
			"args": ["src.langbase:app", "--reload"],
			"jinja": true,
      "cwd": "${workspaceFolder}/server"
		}
	]
}
```