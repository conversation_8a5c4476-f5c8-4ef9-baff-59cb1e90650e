import os
from typing import TypedDict, List
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, ToolMessage
from langgraph.checkpoint.memory import MemorySaver

# 初始化内存检查点
memory = MemorySaver()

# 配置API
API_KEY = 'lzN9p6mrCVbgHQBhyYhgx2_pSNb0dDhq1EkJcWe2juw'
END_POINT = 'https://langbase.netease.com/api/v1/app/8a6bb323-d0f4-4ef5-b8e1-7ce1c46c597a/openai'

# 1. 定义工具


@tool
def search_weather(city: str) -> str:
    """搜索指定城市的天气信息"""
    return f"{city}当前天气：晴朗，26°C，微风"


@tool
def calculate_expression(expression: str) -> str:
    """计算数学表达式，例如"35*42+100" """
    try:
        result = eval(expression)  # 实际应用需替换为安全计算方式
        return f"计算结果：{expression} = {result}"
    except Exception as e:
        print(f"计算错误：{str(e)}")
        raise e


tools = [search_weather, calculate_expression]

# 2. 定义状态结构


class AgentState(TypedDict):
    messages: List[BaseMessage]  # 对话历史
    need_continue: bool = True   # 是否继续循环


# 3. 初始化LLM
llm = ChatOpenAI(
    model="gpt-3.5-turbo",
    api_key=API_KEY,
    base_url=END_POINT,
    temperature=0
)

# 4. 创建ReAct Agent图（全局唯一，避免重复编译）
react_agent = create_react_agent(llm, tools)

# 5. Agent节点逻辑（修正：通过invoke执行Agent图）


def agent_node(state: AgentState):
    """Agent节点：调用ReAct Agent并处理结果"""
    # 关键修正：用invoke执行Agent图，而非直接调用
    result = react_agent.invoke(state)

    # 判断是否需要继续循环
    last_msg = result["messages"][-1] if result["messages"] else None
    need_continue = False

    if isinstance(last_msg, ToolMessage):
        # 工具调用后继续（无论成功或失败）
        need_continue = True
    elif isinstance(last_msg, AIMessage):
        # AI回复后检查是否有未完成的任务
        need_continue = "无法直接使用" in last_msg.content  # 根据实际提示判断

    return {
        "messages": result["messages"],
        "need_continue": need_continue
    }

# 6. 构建循环图


def create_looping_graph():
    builder = StateGraph(AgentState)
    builder.add_node("agent", agent_node)  # 添加Agent节点
    builder.set_entry_point("agent")       # 入口节点

    # 条件边：根据need_continue决定循环或结束
    builder.add_conditional_edges(
        "agent",
        lambda state: "agent" if state["need_continue"] else END
    )

    return builder.compile(checkpointer=memory)  # 启用检查点


# 7. 运行示例
if __name__ == "__main__":
    graph = create_looping_graph()
    user_input = "北京今天的天气如何？把北京的温度*11232131后的结果告诉我"
    config = {"configurable": {"thread_id": "loop-test-1"}}  # 线程ID

    print("Agent处理过程：")
    for chunk in graph.stream(
        {"messages": [HumanMessage(content=user_input)]},
        config=config
    ):
        if "agent" in chunk:
            last_msg = chunk["agent"]["messages"][-1]
            print(f"\n{last_msg.type}: {last_msg.content}")

    # 打印最终结果
    final_state = graph.get_state(config)
    print("\n=== 最终回答 ===")
    for msg in final_state.values["messages"]:
        if msg.type == "human":
            print(f"human: {msg.content}")
        if msg.type == 'ai':
            print(f"ai: {msg.content}")
        if msg.type == 'tool':
            print(f"tool: [{msg.name}]{msg.content}")
