import openai

API_KEY = 'zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh'
END_POINT = 'https://aigw.netease.com/v1'
# model = 'gemini-2.0-flash-exp'
model = 'gemini-2.5-pro'

client = openai.OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

resp = client.chat.completions.create(
    model=model,
    messages=[
        {
            'role': 'user',
            'content': [
                {
                    'type': 'text',
                    'text': '你是一个专业的音乐歌词时间轴制作助手。你的任务是为给定的歌曲创建精确的LRC格式歌词。\n\n输入资料:\n1. 音频文件: [歌曲的完整音频]\n2. 歌曲信息:\n   - 歌曲名: 唯一\n   - 专辑名: T.I.M.E\n   - 艺人名: G.E.M 邓紫棋\n3. 文本歌词: 你真的懂唯⼀的定义\n并不简单如呼吸\n你真的希望你能厘清\n若没交⼼怎么说明\n\n我真的爱你 句句不轻易\n眼神中飘移 总是在关键时刻清楚洞悉\n你的不坚定 配合我颠沛流离\n死去中清醒 明⽩你背着我聪明\n\n那些我 想说的 没说的 话\n有时我 怀疑呢 只是我 傻瓜\n但如果真的爱 不会算计\n爱是不嫉妒 不张狂 不求⾃⼰\n无关你的回应 永不⽌息\n\n你知道\n我真的爱你 没⼈能比拟\n眼神没肯定 总是在关键时刻清楚洞悉\n你的不坚定 配合我颠沛流离\n死去中清醒 明⽩你背着我聪明\n\n我知道\n爱本质无异 是因为⼈多得拥挤\n你不想证明 证明我是你唯⼀\n证明我是你唯⼀\n\n混音：Claudia Koh\n母带：T-Ma马敬恒\nOP：更漂亮音乐工作室\nSP：相信音乐国际股份有限公司\n\n请仔细聆听音频,并执行以下步骤:\n1. 分析音频的节奏、旋律和歌手的演唱。\n2. 识别歌词的每一行,并精确定位其开始时间。\n3. 如果提供了文本歌词,请将其作为参考,但优先以你听到的内容为准。\n4. 使用标准的LRC格式创建歌词,包括时间戳和对应的歌词文本。\n5. 确保时间戳的格式严格遵循 [mm:ss:xx] 的形式,其中:\n   - mm 表示分钟(两位数,如有需要用0填充)\n   - ss 表示秒钟(两位数,如有需要用0填充)\n   - xx 表示百分之一秒(两位数,如有需要用0填充)\n\n输出要求:\n- 使用标准LRC格式。\n- 每行歌词都应有精确的时间戳,格式为 [mm:ss:xx]。\n- 时间戳示例: [00:01:45] 表示 0分1秒45。\n\n请生成完整的LRC格式歌词,确保时间轴与音频完美同步,并严格遵循指定的时间戳格式。',
                },
                {
                    'type': 'audio_url',
                    'audio_url': {
                        'url': 'https://p6.music.126.net/8060f54c-e75a-4636-a73c-85d3ed2af7c3.mp3',
                    }
                }
            ],
        }  # type: ignore
    ],
    stream=False
)

# 输出：
print(resp.choices[0].message.content)

# for chunk in chunks:
#     if chunk.choices[0].delta.content:
#         print('content: ' + chunk.choices[0].delta.content)
