import asyncio
import httpx
import csv
import json
import os
from typing import List, Dict
from src.service.popo_doc import parse_popo_document

# 配置
CSV_FILE = 'popo_results2.csv'


async def call_completion_api(text_content: str) -> Dict:
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    data = {
        'parameters': {
            'document': text_content
        },
        'appId': APP_ID,
        'userId': '123'
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(API_URL, headers=headers, json=data, timeout=1000)
        return response.json()


def get_processed_popos() -> set:
    if not os.path.exists(CSV_FILE):
        return set()

    processed = set()
    with open(CSV_FILE, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            processed.add(row['popo_url'])
    return processed


def save_result(id: str, popo_url: str, score: str, result: Dict):
    file_exists = os.path.exists(CSV_FILE)

    with open(CSV_FILE, 'a', encoding='utf-8', newline='') as f:
        fieldnames = ['id', 'popo_url', 'audit_score', 'result']
        writer = csv.DictWriter(f, fieldnames=fieldnames)

        if not file_exists:
            writer.writeheader()

        writer.writerow({
            'id': id,
            'popo_url': popo_url,
            'audit_score': score,
            'result': json.dumps(result, ensure_ascii=False)
        })


async def process_popos(data: List[Dict]):
    processed_popos = get_processed_popos()
    total = len(data)

    print(f"总共需要处理 {total} 条数据")
    print(f"已经处理过 {len(processed_popos)} 条数据")

    for index, item in enumerate(data, 1):
        popo_url = item['popo_url']
        if popo_url in processed_popos:
            print(f"[{index}/{total}] 跳过已处理的 popo: {popo_url}")
            continue

        try:
            print(f"[{index}/{total}] 正在处理 popo: {popo_url}")
            # 获取popo文档内容
            popo_result = await parse_popo_document(popo_url)
            if popo_result['code'] != 200:
                print(
                    f"获取popo文档内容失败 {popo_url}: {popo_result}")
                # 保存结果
                save_result(item['id'], popo_url,
                            item['audit_score'], popo_result)
                continue

            print('正在进行评分\n')
            # 调用completion API
            completion_result = await call_completion_api(popo_result['data']['text_content'])

            print('评分完成\n')

            # 保存结果
            save_result(item['id'], popo_url,
                        item['audit_score'], completion_result)
            print(f"处理成功: {popo_url}")

        except Exception as e:
            print(f"[{index}/{total}] 处理 popo {popo_url} 时出错: {str(e)}")


def read_tech_v2_data() -> List[Dict]:
    """从 result_techV2.csv 读取数据"""
    data = []
    with open('Result_techV2.csv', 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        # 用于去重的集合
        processed_urls = set()

        for row in reader:
            # 假设CSV中有 id 和 popo_url 列
            popo_url = row.get('url')
            if not popo_url:
                continue

            # 如果URL已经处理过，跳过
            if popo_url in processed_urls:
                continue

            processed_urls.add(popo_url)
            data.append({
                'id': row.get('id', ''),
                'popo_url': popo_url,
                'audit_score': row.get('audit_score', ''),
            })

    return data


async def main():
    # 从 result_techV2.csv 读取数据
    data = read_tech_v2_data()
    print(f"读取到 {len(data)} 条数据")

    await process_popos(data)

if __name__ == '__main__':
    asyncio.run(main())
