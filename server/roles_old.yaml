defaultRole: external_user
rolePriorityDesc:
  - admin
  - developer
  - viewer
  - external_user
roles:
  - name: admin
    desc: admin user
    rules:
      - resources:
          - workspace
          - group
          - app
          - component-categories
          - workflow-engines
          - components
          - custom_components
          - env-variables
          - token
          - knowledge
          - prompt-template
          - prompt-tools
          - system/notify
          - app/setting-config
          - app/setting
          - workspace/aigw
          - workspace/aigw_app
          - workspace/aigw_apps
          - group/aigw
          - group/aigw_app
          - group/bind
          - group/aigw_apps
          - aigw_app
        verbs:
          - get
          - list
          - put
          - post
          - delete
      - resources:
          - workspace/group
          - group/app
          - app/config-snapshot
          - workspace/model-provider
          - group/model-provider
          - workspace/workflow-engines
          - workspace/components
          - workspace/custom_components
          - components/env-variables
          - workspace/env-variables
          - group/env-variables
          - app/env-variables
          - app/deploy
          - app/debug
          - app/trigger
          - workspace
          - workspace/token
          - group/token
          - group/knowledge
          - workspace/creator
          - prompt-template
          - prompt-tools
          - app/setting
          - app/setting-config
          - app/setting-publish
          - app/property-update
          - app/property-search
          - app/portrait-query
          - app/portrait-add
          - app/portrait-style
          - app/portrait-prompt-polish
          - app/portrait-generate
          - app/portrait-generate-result
          - app/emotion-add
          - app/emotion-delete
          - app/emotion-query
          - app/emotion-query-by-ids
          - app/task-query
          - app/save-trigger
          - app/trigger-variables
          - app/nonuser-variables
          - app/query-trigger
          - app/post-common-proxy
          - app/get-common-proxy
          - group/aigw_bind
          - workspace/aigw_bind
        verbs:
          - list
          - put
          - post
      - resources:
          - roles
          - workspace/app
          - workspace/knowledge
          - app/deploy-history
          - app/conversation
          - conversation/message
          - app/metrics
          - app/conversation-per-user
          - app/user-usage-count
          - app/token-per-seconds
          - app/token-count
          - app/message-per-conv
          - app/messages
          - app/events
          - app/status
          - app-template
          - workspace/basic-app
          - group/components
          - app/components
          - group/custom_components
          - app/custom_components
          - app/setting-config
          - app/setting-versioned-config
          - app/setting
        verbs:
          - list
      - resources:
          - model-provider
        verbs:
          - delete
          - put
      - resources:
          - workspace/default-model
          - app/config
          - group/default-model
          - app/setting-config
          - app/setting
          - copy-config-snapshot
        verbs:
          - put
          - list
      - resources:
          - workspace/member
          - group/member
          - app/member
          - workspace/members
          - group/members
          - app/members
          - app/setting
          - app/star
          - app/unstar
        verbs:
          - put
          - delete
          - list
      - resources:
          - token
        verbs:
          - delete
      - resources:
          - app/token
          - proxy
        verbs:
          - list
          - post
      - resources:
          - app/temp-token
          - app/uploadfile
          - workspace
          - group
          - app
          - knowledge
          - app/agentw-chat
          - app/virtual-human-chat
          - app/agent-variables
          - app/nonuser-variables
          - app/publish-channels
          - app/publish-rollback
          - app/publish-history
          - curl/cffi
          - download/cffi
        verbs:
          - post
      - resources:
          - app/workflow-runs
          - app-config
        verbs:
          - get
          - list
      - resources:
          - app/lock
        verbs:
          - post
          - delete
  - name: developer
    desc: Developer user
    rules:
      - resources:
          - workspace
          - component-categories
          - workflow-engines
        verbs:
          - get
      - resources:
          - workspace/group
          - workspace/app
          - roles
          - app/deploy-history
          - workspace/model-provider
          - group/model-provider
          - workspace/default-model
          - group/default-model
          - app/conversation
          - app/metrics
          - app/messages
          - conversation/message
          - app/conversation-per-user
          - app/user-usage-count
          - app/token-per-seconds
          - app/token-count
          - app/message-per-conv
          - workspace/workflow-engines
          - group/components
          - app/components
          - group/custom_components
          - app/custom_components
          - app-template
          - workspace/basic-app
          - group/token
          - workspace/knowledge
          - system/notify
          - app/setting-config
          - app/setting-versioned-config
          - app/setting
          - app/property-search
          - app/save-trigger
          - app/trigger-variables
          - app/query-trigger
        verbs:
          - list
      - resources:
          - workspace/components
          - group/components
          - components
          - workspace/custom_components
          - group/custom_components
          - custom_components
        verbs:
          - list
          - put
          - delete
          - post
      - resources:
          - group
          - env-variables
          - knowledge
          - prompt-template
        verbs:
          - get
      - resources:
          - app
          - env-variables
          - knowledge
          - app/config
          - app/setting
        verbs:
          - get
          - put
      - resources:
          - app/config-snapshot
          - group/app
          - app/copy
          - app/trigger
          - app/debug
          - app/deploy
          - components/env-variables
          - workspace/env-variables
          - group/env-variables
          - app/env-variables
          - group/knowledge
          - app/setting
          - app/setting-config
          - app/setting-publish
          - app/property-update
          - app/property-search
          - app/publish-channels
          - app/publish-rollback
          - app/publish-history
          - app/portrait-query
          - app/portrait-add
          - app/portrait-style
          - app/portrait-prompt-polish
          - app/portrait-generate
          - app/portrait-generate-result
          - app/task-query
          - app/emotion-add
          - app/emotion-delete
          - app/emotion-query
          - app/save-trigger
          - app/trigger-variables
          - app/emotion-query-by-ids
          - app/query-trigger
          - app/post-common-proxy
          - app/get-common-proxy
          - app/nonuser-variables
          - group/aigw_bind
          - workspace/aigw_bind
          - proxy
        verbs:
          - list
          - post
      - resources:
          - workspace/member
          - group/member
          - app/member
          - workspace/members
          - group/members
          - app/members
          - prompt-template
          - prompt-tools
          - copy-config-snapshot
        verbs:
          - put
          - delete
          - list
      - resources:
          - app/token
          - app/events
          - app/status
          - app/star
          - app/unstar
        verbs:
          - list
      - resources:
          - app/temp-token
          - app/uploadfile
          - app/agentw-chat
          - app/virtual-human-chat
          - app/agent-variables
          - curl/cffi
          - download/cffi
        verbs:
          - post
      - resources:
          - app/workflow-runs
          - app-config
        verbs:
          - get
          - list
      - resources:
          - app/lock
        verbs:
          - post
          - delete
  - name: viewer
    desc: Viewer user
    rules:
      - resources:
          - workspace
          - component-categories
          - workflow-engines
          - components
          - app-config
          - setting
          - prompt-template
          - system/notify
          - app/setting-config
          - app/setting-versioned-config
          - app/setting
        verbs:
          - get
      - resources:
          - workspace/group
          - workspace/app
          - roles
          - group/knowledge
          - app/deploy-history
          - app/config-snapshot
          - workspace/model-provider
          - group/model-provider
          - workspace/default-model
          - group/default-model
          - app/conversation
          - conversation/message
          - app/conversation-per-user
          - app/user-usage-count
          - app/token-per-seconds
          - app/token-count
          - app/message-per-conv
          - workspace/workflow-engines
          - app/events
          - app/status
          - app-template
          - app/virtual-human-chat
          - workspace/basic-app
          - workspace/knowledge
          - app/portrait-query
          - app/portrait-add
          - app/portrait-style
          - app/portrait-prompt-polish
          - app/portrait-generate
          - app/portrait-generate-result
          - app/task-query
          - app/emotion-query
          - app/trigger-variables
          - app/emotion-query-by-ids
          - app/get-common-proxy
        verbs:
          - list
      - resources:
          - knowledge
          - app/query-trigger
          - group/aigw_bind
          - workspace/aigw_bind
          - group/app
          - workspace/components
          - components
        verbs:
          - list
          - put
          - post
          - delete
      - resources:
          - workspace/custom_components
        verbs:
          - list
      - resources:
          - group
          - app
          - workspace/admins
          - workspace/admin
          - group/admins
          - app/star
          - app/unstar
          - knowledge
        verbs:
          - get
      - resources:
          - workspace/member
          - group/member
          - app/member
          - workspace/members
          - group/members
          - app/members
        verbs:
          - put
          - delete
          - list
      - resources:
          - app/temp-token
          - app/agent-variables
          - app/nonuser-variables
          - prompt-tools
        verbs:
          - post
  - name: external_user
    desc: External user for langbase system
    rules:
      - resources:
          - model-provider
          - roles
          - model
          - component-categories
          - app/messages/export
          - app-template
          - group/aigw_bind
          - workspace/aigw_bind
          - copy-config-snapshot
          - proxy
        verbs:
          - put
          - post
          - delete
          - list
      - resources:
          - app/temp-token
        verbs:
          - post
      - resources:
          - app/messages/export
          - prompt-template
          - llm-models
          - llm-models/batch
          - model_v2
        verbs:
          - get
          - post
          - put
          - delete
      - resources:
          - prompt-tools
          - workspace/admins
          - workspace/admin
          - workspace/aigw_app/bind
          - group/aigw_app/bind
          - group/aigw_bind
          - workspace/aigw_bind
          - group/admins
          - workspace/update-creators
          - conversation
          - post-common-proxy
          - get-common-proxy
          - alert
          - workspace/sync
          - group/sync
          - aigc/popo-score
          - app/star
          - app/unstar
          - group/app
          - proxy
        verbs:
          - get
          - post
      - resources:
          - app/lock
        verbs:
          - post
          - delete