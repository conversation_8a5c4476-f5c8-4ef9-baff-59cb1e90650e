import json
from typing import List, Tuple

from ..misc.log import logger_error, logger_info
from ..misc.http_client import HTT<PERSON>lient
from ..config import settings
from ..schema.workflow_engine import WorkflowEngine
from ..misc.errors import InternalServerError, LangbaseException

from loguru import logger

WORKFLOW_PATH = "flows"
WORKFLOW_RUN_PATH = "runs"
WORKFLOW_NODE_PATH = "nodes"


class WorkflowClient:
    http_client: HTTPClient
    endpoint: str

    def __init__(self, config: WorkflowEngine):
        self.http_client = HTTPClient()
        self.endpoint = config.endpoint
        self.token = config.token
        self.http_client.headers = {
            "Content-Type": "application/json",
            "LANGBASE-FLOW-TOKEN": self.token,
        }

    def create_workflow(self, flow_id: str, operator: str):
        url = f"{self.endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}/create"
        try:
            response = self.http_client.post(url, data={
                "flowId": flow_id,
                "operator": operator,
            })
        except Exception as e:
            logger_error(f"{flow_id} AIO引擎创建工作流失败: {e}")
            raise InternalServerError(
                f"{flow_id} AIO引擎创建工作流失败: {e}")

        if not response.ok:
            logger_error(
                f"{flow_id} AIO引擎创建工作流失败: {response.text}")
            raise LangbaseException(status_code=response.status_code,
                                    message=f"{flow_id} AIO引擎创建工作流失败: {response.text}")

        if response.json()['code'] != 200:
            message = response.json()['message']
            if message == 'workflow def already exist':
                raise InternalServerError(f"{flow_id} AIO引擎创建工作流失败: 该英文名已存在")
            logger_error(f"{flow_id} AIO引擎创建工作流失败: code: {response.json()['code']}, "
                         f"message: {message}")
            raise InternalServerError(f"{flow_id} AIO引擎创建工作流失败: "
                                      f"code({response.json()['code']}) message({message})")

    def submit_workflow(self, flow_id: str, version: str, flow_json: str, operator: str) -> dict:
        url = f"{self.endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}"
        try:
            response = self.http_client.post(url, data={
                "flowId": flow_id,
                "version": version,
                "flowJson": flow_json,
                "operator": operator
            })
        except Exception as e:
            logger_error(f"submit workflow {flow_id} error, exception: {e}")
            raise InternalServerError(
                f"Submit workflow({flow_id}) exception: {e}")

        if not response.ok:
            logger_error(
                f"submit workflow {flow_id} error, response: {response.text}")
            raise InternalServerError(
                f"Submit workflow({flow_id}) version({version}) failed: {response.text}")

        if response.json()['code'] != 200:
            logger_error(f"submit workflow {flow_id} error, code: {response.json()['code']}, "
                         f"message: {response.json()['message']}")
            raise InternalServerError(f"Submit workflow({flow_id}) "
                                      f"failed: code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']

    def delete_workflow(self, flow_id: str, operator: str):
        url = f"{self.endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}"
        try:
            response = self.http_client.post(url, data={
                "flowId": flow_id,
                "operator": operator,
            })
        except Exception as e:
            logger_error(f"delete workflow {flow_id} error, exception: {e}")
            raise InternalServerError(
                f"Delete workflow({flow_id}) exception: {e}")

        if not response.ok:
            logger_error(
                f"delete workflow {flow_id} error, response: {response.text}")
            raise InternalServerError(status_code=response.status_code,
                                      message=f"Delete workflow({flow_id}) failed: {response.text}")

        if response.json()['code'] != 200:
            logger_error(f"delete workflow {flow_id} error, code: {response.json()['code']}, "
                         f"message: {response.json()['message']}")
            raise InternalServerError(f"Delete workflow({flow_id}) "
                                      f"failed: code({response.json()['code']}) message({response.json()['message']})")

    def trigger_workflow_run(self, flow_id: str, inputs: dict, envs: dict,
                             callback: dict, biz_info: dict, biz_context: str) -> dict:
        url = f"{self.endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}/trigger"
        logger.info("trigger_workflow_run: {url} {flow_id} {inputs} {envs} {callback} {biz_info} {biz_context}",
                    url=url, flow_id=flow_id, inputs=inputs, envs=envs, callback=callback, biz_info=biz_info, biz_context=biz_context, uid="uid")
        try:
            response = self.http_client.post(url, data={
                "flowId": flow_id,
                "inputs": inputs,
                "envs": envs,
                "callback": callback,
                "bizInfo": biz_info,
                "bizContext": biz_context
            })
        except Exception as e:
            logger_error(f"trigger workflow {flow_id} error, exception: {e}")
            raise InternalServerError(
                f"Trigger workflow({flow_id}) exception: {e}")

        if not response.ok:
            logger_error(
                f"trigger workflow {flow_id} error, response: {response.text}")
            raise InternalServerError(f"Trigger workflow({flow_id}) "
                                      f"failed: code({response.status_code}) message({response.text})")

        if response.json()['code'] != 200:
            logger_error(f"trigger workflow {flow_id} error, code: {response.json()['code']}, "
                         f"message: {response.json()['message']}")
            raise InternalServerError(f"Trigger workflow({flow_id}) "
                                      f"failed: code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']

    def list_workflow_run(self, flow_id: str, page_size: int, page_number: int,
                          start_time: int, end_time: int) -> Tuple[List[dict], int]:
        url = f"{self.endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}/{WORKFLOW_RUN_PATH}"
        params = {}
        if flow_id is not None:
            params['flowId'] = flow_id
        if page_size is not None and page_number is not None:
            params['pageSize'] = page_size
            params['pageNum'] = page_number - 1
        if start_time is not None and end_time is not None:
            params['startTime'] = start_time
            params['endTime'] = end_time
        try:
            response = self.http_client.get(url=url, params=params)
        except Exception as e:
            logger_error(f"list workflow run {flow_id} error, exception: {e}")
            raise InternalServerError(
                f"List workflow run({flow_id}) exception: {e}")

        if not response.ok:
            logger_error(
                f"list workflow run {flow_id} error, response: {response.text}")
            raise InternalServerError(
                f"List workflow run({flow_id}) failed: {response.text}")

        if response.json()['code'] != 200:
            logger_error(f"list workflow run {flow_id} error, code: {response.json()['code']}, "
                         f"message: {response.json()['message']}")
            raise InternalServerError(f"List workflow run({flow_id}))"
                                      f"failed: code({response.json()['code']}) message({response.json()['message']})")
        if response.json()['data']['count'] == 0:
            return [], 0

        return response.json()['data']['list'] or [], response.json()['data']['count']

    def get_workflow_run(self, flow_id: str, run_id: str) -> dict:
        url = f"{self.endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}/run/detail"
        params = {}
        if flow_id is not None:
            params['flowId'] = flow_id
        if run_id is not None:
            params['runId'] = run_id
        try:
            response = self.http_client.get(url=url, params=params)
        except Exception as e:
            logger_error(
                f"get workflow run {flow_id} run {run_id} error, exception: {e}")
            raise InternalServerError(
                f"Get workflow run({flow_id}) run({run_id}) exception: {e}")

        if not response.ok:
            logger_error(
                f"get workflow run {flow_id} run {run_id} error, response: {response.text}")
            raise InternalServerError(
                f"Get workflow run({flow_id}) run({run_id}) failed: {response.text}")

        if response.json()['code'] != 200:
            logger_error(f"get workflow run {flow_id} run {run_id} error, code: {response.json()['code']}, "
                         f"message: {response.json()['message']}")
            raise InternalServerError(f"Get workflow run({flow_id}) run({run_id})"
                                      f"failed: code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']

    def get_workflow_run_node(self, flow_id: str, run_id: str, node_id: str, index_list: str) -> dict:
        url = f"{self.endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}/run/node"
        params = {}
        if flow_id is not None:
            params['flowId'] = flow_id
        if run_id is not None:
            params['runId'] = run_id
        if node_id is not None:
            params['nodeId'] = node_id
        if index_list is not None:
            params['indexList'] = index_list
        try:
            response = self.http_client.get(url=url, params=params)
        except Exception as e:
            logger_error(
                f"get workflow run node {flow_id} run {run_id} node {node_id} error, exception: {e}")
            raise InternalServerError(f"Get workflow run node({flow_id}) run({run_id}) "
                                      f"node({node_id} exception: {e}")

        if not response.ok:
            logger_error(f"get workflow run node {flow_id} run {run_id} node {node_id} error, response: {response.text}")  # noqa
            raise InternalServerError(f"Get workflow run node({flow_id}) run({run_id}) "
                                      f"node({node_id} failed: {response.text}")

        if response.json()['code'] != 200:
            logger_error(f"get workflow run node {flow_id} run {run_id} node {node_id} error, "
                         f"code: {response.json()['code']}, message: {response.json()['message']}")
            raise InternalServerError(f"Get workflow run node({flow_id}) run({run_id}) node({node_id}"
                                      f"failed: code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']

    def list_workflow_run_events(self, flow_id: str, run_id: str, node_id: str,
                                 page_number: int, page_size: int) -> Tuple[List[dict], int]:
        url = f"{self.endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}/" \
              f"{WORKFLOW_RUN_PATH}/{WORKFLOW_NODE_PATH}/events"
        params = {}
        if flow_id is not None:
            params['flowId'] = flow_id
        if run_id is not None:
            params['runId'] = run_id
        if node_id is not None:
            params['nodeId'] = node_id
        if page_number is not None and page_size is not None:
            params['pageSize'] = page_size
            params['pageNum'] = page_number - 1
        try:
            response = self.http_client.get(url=url, params=params)
        except Exception as e:
            logger_error(
                f"list workflow run events {flow_id} run {run_id} node {node_id} error, exception: {e}")
            raise InternalServerError(f"Get workflow run node events({flow_id}) run({run_id}) "
                                      f"node({node_id}) exception: {e}")

        if not response.ok:
            logger_error(f"list workflow run events {flow_id} run {run_id} node {node_id} error, "
                         f"response: {response.text}")
            raise InternalServerError(f"Get workflow run node events({flow_id}) run({run_id}) "
                                      f"node({node_id} failed: {response.text}")

        if response.json()['code'] != 200:
            logger_error(f"list workflow run events {flow_id} run {run_id} node {node_id} error, "
                         f"code: {response.json()['code']}, message: {response.json()['message']}")
            raise InternalServerError(f"Get workflow run node events({flow_id}) run({run_id}) node({node_id}"
                                      f"failed: code({response.json()['code']}) message({response.json()['message']})")
        if response.json()['data']['count'] == 0:
            return [], 0

        return response.json()['data']['list'], response.json()['data']['count']
