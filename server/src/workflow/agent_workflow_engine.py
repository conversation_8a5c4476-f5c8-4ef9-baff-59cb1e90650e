from email import message
from enum import Enum
import json
import traceback
from typing import Any, Dict, List, Optional, Set
from aiohttp import ClientSession
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from ..config import settings


from ..service.env_variable import \
    list_app_component_env_variable as list_app_component_env_variable_service
from ..service.chat import get_or_create_db_conversation as get_or_create_db_conversation_service

from ..models.app import App

from ..lib.llms.excutor import DomainLLMRunner

from ..schema.dialog import ConversationCreate, ConversationRequest, ResponseMode

from ..service.dialog import appendMultiModalMessages, construct_messages, dialogue

from ..misc.errors import ParameterInvalid

from ..schema.component import ComponentConfigInput, ComponentConfigItem, \
    ComponentConfigOutput, \
    ComponentConfigVar, \
    ComponentType
from ..schema.app import \
    AgentConfig, \
    AgentWorkflowConfig, \
    AgentWorkflowConfigInput, \
    AgentWorkflowNode

from ..service.chat import list_message


def parse_port_id(merged_id: str) -> str:
    return merged_id.split("-")[1]


def find_start_input(inputs: List[AgentWorkflowConfigInput]) -> Optional[AgentWorkflowConfigInput]:
    for i in inputs:
        # 以 message 为起始节点
        if i.name == "message":
            return i


class EngineRunFailed(Exception):
    pass


class NodeRunningStatus(str, Enum):
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"


class NodeResult(BaseModel):
    node_id: str
    inputs: Dict[str, Any] = Field(default={})
    outputs: Dict[str, Any] = Field(default={})
    response: Any = Field(default=None)
    running_status: NodeRunningStatus = Field(
        default=NodeRunningStatus.SUCCESS)
    node_name: str


class NodeScriptResult(NodeResult):
    pass


class NodeHTTPResult(NodeResult):
    code: int = Field(default=200)


class NodeLLMJSONResult(NodeResult):
    max_conversations: int
    conversation_id: str


class NodeInputResult(NodeResult):
    pass


class NodeOutputResult(NodeResult):
    pass


class NodeDisplayResult(NodeResult):
    pass


class StreamStage(str, Enum):
    CONTENT = "content"
    END = "end"


class NodeLLMStreamResult(NodeResult):
    stage: StreamStage = Field(default=StreamStage.CONTENT)
    max_conversations: int
    conversation_id: str


class Engine:
    def __init__(self) -> None:
        pass

    def assign_configs_into_map(self,
                                configs: Optional[List[ComponentConfigItem]],
                                target: Dict[str, Any],
                                envs: Dict[str, str]):
        if configs:
            for config in configs:
                if isinstance(config.value, str) and config.value.startswith("$"):
                    env_name = config.value[1:]
                    env_value = envs.get(env_name)
                    if env_value is None:
                        raise ParameterInvalid(
                            f"Env variable {env_name} not found")
                    target[config.name] = env_value
                else:
                    target[config.name] = config.value

    def assign_vars_into_map(self, vars: Optional[List[ComponentConfigVar]], target: Dict[str, Any]):
        if vars:
            for var in vars:
                target[var.name] = var.value

    def get_input_ports(self, nodes: List[AgentWorkflowNode]):
        id_to_port: Dict[str, ComponentConfigInput] = {}
        for node in nodes:
            if node.inputs:
                for port in node.inputs:
                    id_to_port[port.id] = port
        return id_to_port

    async def run(self,
                  db: Session,
                  user_id: str,
                  app: App,
                  app_config_id: str,
                  aw_config: AgentWorkflowConfig,
                  input_values: Dict[str, Any],
                  conversation_ids: Dict[str, str]):

        client = ClientSession()
        try:
            port_2_node_id: Dict[str, str] = {}
            for node in aw_config.nodes:
                if node.inputs:
                    for port in node.inputs:
                        port_2_node_id[port.id] = node.id
                if node.outputs:
                    for port in node.outputs:
                        port_2_node_id[port.id] = node.id

            end_port = aw_config.outputs[0]
            end_node_id = port_2_node_id.get(end_port.id)
            if end_node_id is None:
                raise ParameterInvalid("End node not found")
            id_2_node = {node.id: node for node in aw_config.nodes}

            # 以 node port 为 key，node_id-port_id 为 value
            next_edges: Dict[str, List[str]] = {}
            pre_edges: Dict[str, List[str]] = {}
            for edge in aw_config.edges:
                source_port_id = parse_port_id(edge.source_port)
                next_edges.setdefault(source_port_id, []).append(
                    edge.target_port_id)

                target_port_id = parse_port_id(edge.target_port)
                pre_edges.setdefault(target_port_id, []).append(
                    edge.source_port_id)
            id_2_input_port = self.get_input_ports(aw_config.nodes)

            env_list, _ = await list_app_component_env_variable_service(db,
                                                                        components=[
                                                                            node.component_id
                                                                            for node in aw_config.nodes],
                                                                        app_id=app.id,
                                                                        group_id=str(
                                                                            app.group_id),
                                                                        workspace_id=str(app.workspace_id))
            envs = {
                str(env.name): str(env.value) for env in env_list
            }
            # 找到值为none的并抛出
            none_envs = {key: value for key,
                         value in envs.items() if value == 'None'}
            if none_envs:
                none_keys = ', '.join(none_envs.keys())
                error_message = f"The envs below is none: {none_keys}"
                raise ParameterInvalid(error_message)

            # 找到 output 节点的前一个节点，如果为 LLM 节点，则为 stream 输出
            llm_stream_dict: Dict[str, bool] = {}
            if not aw_config.outputs:
                raise ParameterInvalid("No output node found")
            output_port = aw_config.outputs[0]
            pre_output_port_ids = pre_edges.get(output_port.id)
            if pre_output_port_ids is None:
                raise ParameterInvalid(
                    "Output node must have exactly one input")
            for pre_output_port_id in pre_output_port_ids:
                pre_output_node_id = pre_output_port_id.split("-")[0]
                pre_output_node = id_2_node.get(pre_output_node_id)
                if pre_output_node is None:
                    raise ParameterInvalid(
                        f"Node {pre_output_node_id} not found")
                if pre_output_node.type == ComponentType.LLM:
                    llm_stream_dict[pre_output_node_id] = True

            # 将 input 节点作为起点, 入队，因为 input 节点一定为入度为 0 的点
            node_queue: List[AgentWorkflowNode] = []
            selected_nodes: Set[str] = set()
            for input_port in aw_config.inputs:
                node_id = port_2_node_id.get(input_port.id)
                if node_id is None:
                    raise ParameterInvalid("Input node not found")
                node = id_2_node.get(node_id)
                if node is None:
                    raise ParameterInvalid(f"Node {node_id} not found")
                if node_id not in selected_nodes:
                    node_queue.append(node)
                selected_nodes.add(node_id)

            # 入度集合，入度为 0 的节点入队列
            in_degree: Dict[str, int] = {}
            for edge in aw_config.edges:
                node_id = edge.target_port_id.split("-")[0]
                degree = in_degree.setdefault(node_id, 0)
                in_degree[node_id] = degree + 1

            for node in aw_config.nodes:
                if node.type == ComponentType.SELECT:
                    if node.inputs:
                        in_degree[node.id] = len(node.inputs)

            component_2_inputs: Dict[str, Dict[str, Any]] = {
                node.id: input_values for node in node_queue
            }

            outputs: Dict[str, Any] = {}
            while len(node_queue) > 0:
                node = node_queue[0]
                node_queue = node_queue[1:]
                if node.type == ComponentType.SELECT or node.type == ComponentType.SWITCH:
                    node_inputs = component_2_inputs.get(node.id, {})
                    if node.type == ComponentType.SELECT:
                        selected_port = await self.run_select_component(node, node_inputs)
                    else:
                        selected_port = await self.run_switch_component(node, node_inputs)
                    next_port_ids = next_edges.get(selected_port.id)
                    if next_port_ids is None:
                        raise ParameterInvalid(
                            "Switch node must have exactly one output")
                    for next_port_id in next_port_ids:
                        next_node_id = next_port_id.split("-")[0]
                        next_port_id = next_port_id.split("-")[1]
                        next_node = id_2_node.get(next_node_id)
                        if next_node is None:
                            raise ParameterInvalid(
                                f"Node {next_node_id} not found")
                        if next_node.id not in selected_nodes:
                            in_degree[next_node_id] -= 1
                            if in_degree[next_node_id] == 0:
                                node_queue.append(next_node)
                                selected_nodes.add(next_node_id)
                        if node.type == ComponentType.SELECT:
                            next_inputs = component_2_inputs.setdefault(
                                next_node_id, {})
                            v = node_inputs.get(selected_port.name)
                            if v is None:
                                raise ParameterInvalid(
                                    f"Input {selected_port.name} not found")
                            input_port = id_2_input_port.get(next_port_id)
                            if not input_port:
                                raise ParameterInvalid(
                                    f"Port {next_port_id} not found")
                            next_inputs[input_port.name] = v
                else:
                    input_dir = component_2_inputs.setdefault(node.id, {})
                    llm_stream = llm_stream_dict.get(node.id, False)
                    generator = self.run_component(
                        node=node,
                        user_id=user_id,
                        db=db,
                        app=app,
                        app_config_id=app_config_id,
                        inputs=input_dir,
                        component_2_conversation_id=conversation_ids,
                        envs=envs,
                        client=client,
                        llm_stream=llm_stream)
                    async for result in generator:
                        if result is None:
                            continue
                        if isinstance(result, NodeResult):
                            if result.outputs:
                                outputs = result.outputs
                            else:
                                outputs = {}
                        if node.type != ComponentType.INPUT:
                            yield result

                    if node.type == ComponentType.OUTPUT:
                        break
                    if not node.outputs:
                        raise ParameterInvalid(
                            "Node must have at least one output")
                    for output_config in node.outputs:
                        next_port_ids = next_edges.get(output_config.id)
                        if next_port_ids is None:
                            continue
                        for next_port_id in next_port_ids:
                            next_node_id = next_port_id.split("-")[0]
                            next_port_id = next_port_id.split("-")[1]
                            next_port = id_2_input_port.get(next_port_id)
                            if next_port is None:
                                raise ParameterInvalid(
                                    f"Port {next_port_id} not found")
                            next_node_inputs = component_2_inputs.setdefault(
                                next_node_id, {})
                            next_node_inputs[next_port.name] = outputs.get(
                                output_config.name)
                            if next_node_id not in selected_nodes:
                                in_degree[next_node_id] -= 1
                                if in_degree[next_node_id] == 0:
                                    next_node = id_2_node.get(next_node_id)
                                    if next_node is None:
                                        raise ParameterInvalid(
                                            f"Node {next_node_id} not found")
                                    node_queue.append(next_node)
                                    selected_nodes.add(next_node_id)
        except Exception as e:
            print(e)
            print(traceback.format_exc())
            yield EngineRunFailed(e)
        finally:
            await client.close()

    async def run_component(self,
                            node: AgentWorkflowNode,
                            user_id: str,
                            db: Session,
                            app: App,
                            app_config_id: str,
                            inputs: Dict[str, Any],
                            component_2_conversation_id: Dict[str, str],
                            client: ClientSession,
                            envs: Dict[str, str],
                            llm_stream: bool,
                            ):
        if node.type != ComponentType.INPUT and node.type != ComponentType.OUTPUT:
            yield NodeResult(
                inputs=inputs,
                node_id=node.id,
                node_name=node.name,
                running_status=NodeRunningStatus.RUNNING,
            )

        if node.code == 'LLM' or node.type == ComponentType.LLM:
            generator = self.run_llm_component(
                node=node,
                user_id=user_id,
                db=db,
                response_mode=ResponseMode.STREAMING if llm_stream else ResponseMode.JSON,
                app=app,
                app_config_id=app_config_id,
                inputs=inputs,
                envs=envs,
                conversation_id=component_2_conversation_id.get(node.id))
            async for response in generator:
                yield response
        elif node.type == ComponentType.HTTP:
            result = await self.run_http_component(node, inputs, envs, client)
            yield result
        elif node.type == ComponentType.SCRIPT:
            result = await self.run_script_component(node, inputs, envs, client)
            yield result
        elif node.type == ComponentType.DISPLAY:
            yield await self.run_display_component(node, inputs)
        # input/output 节点不需要展示运行状态
        elif node.type == ComponentType.INPUT:
            conversation_id = ''
            if len(component_2_conversation_id) == 1:
                conversation_id = next(
                    iter(component_2_conversation_id.values()))
            yield await self.run_input_component(db, node, inputs, conversation_id)
        elif node.type == ComponentType.OUTPUT:
            yield await self.run_output_component(node, inputs)
        else:
            raise ParameterInvalid(
                f"Unsupported component type: {node.type} {node.code}")

    async def run_display_component(self,
                                    node: AgentWorkflowNode,
                                    inputs: Dict[str, Any]):
        k_template = "template"
        required_key = [k_template]

        self.assign_vars_into_map(node.vars, inputs)

        for key in required_key:
            if key not in inputs:
                raise ParameterInvalid(f"Missing required key: {key}")

        if not node.outputs or len(node.outputs) != 1:
            raise ParameterInvalid("Display node must have exactly one output")

        v_tempalte = inputs.pop(k_template, None)

        res = v_tempalte.format(**inputs)

        return NodeDisplayResult(outputs={
            node.outputs[0].name: res},
            node_name=node.name,
            node_id=node.id)

    async def run_output_component(self,
                                   node: AgentWorkflowNode,
                                   inputs: Dict[str, Any]):
        if not node.inputs or len(node.inputs) != 1:
            raise ParameterInvalid("Output node must have exactly one input")
        response = None
        for _, v in inputs.items():
            response = v
            break
        return NodeOutputResult(outputs=inputs,
                                response=response,
                                node_name=node.name,
                                node_id=node.id)

    async def run_input_component(self,
                                  db: Session,
                                  node: AgentWorkflowNode,
                                  inputs: Dict[str, Any],
                                  conversation_id: str):
        if not node.outputs:
            raise ParameterInvalid("Input node must have at least one output")
        outputs = {}
        for output_config in node.outputs:
            if output_config.name == "conversationHistory":
                messages = list()
                if conversation_id:
                    db_messages, count = await list_message(
                        db=db,
                        conversation_id=conversation_id,
                        limit=2,
                        ascending=False)
                    for db_message in db_messages:
                        msg = {}
                        msg['query'] = db_message.query
                        msg['response'] = db_message.response
                        messages.append(msg)
                    outputs[output_config.name] = messages
                else:
                    outputs[output_config.name] = []
            else:
                if output_config.name not in inputs:
                    raise ParameterInvalid(
                        f"Input {output_config.name} not found")
                else:
                    outputs[output_config.name] = inputs[output_config.name]

        return NodeInputResult(outputs=outputs,
                               node_name=node.name,
                               node_id=node.id)

    async def run_http_component(self,
                                 node: AgentWorkflowNode,
                                 inputs: Dict[str, Any],
                                 envs: Dict[str, str],
                                 client: ClientSession):
        k_method = "method"
        k_url = "url"
        k_headers = "headers"
        k_timeout = "timeout"
        required_keys = [k_method, k_url]

        self.assign_configs_into_map(node.configs, inputs, envs)
        self.assign_vars_into_map(node.vars, inputs)
        for key in required_keys:
            if key not in inputs:
                raise ParameterInvalid(f"Missing required key: {key}")

        method = inputs.pop(k_method, None)
        url = inputs.pop(k_url, None)
        headers = inputs.pop(k_headers, None)
        timeout_str = inputs.pop(k_timeout, None)
        try:
            timeout = int(timeout_str) / 1000
        except TypeError:
            timeout = None
        if method.lower() == "get":
            params = {}
            for k, v in inputs.items():
                if isinstance(v, str) and isinstance(k, str):
                    params[k] = v
            async with client.get(url, headers=headers, timeout=timeout, params=params) as response:
                code = response.status
                resp = json.loads(await response.text())
                # resp = await response.json()
        elif method.lower() == "post":
            async with client.post(url, headers=headers, timeout=1000, json=inputs) as response:
                code = response.status
                # resp = await response.json()
                resp = json.loads(await response.text())
        else:
            raise ParameterInvalid(f"Unsupported method: {method}")
        outputs = {}
        if node.outputs:
            for output in node.outputs:
                outputs[output.name] = resp.get(output.name)
        return NodeHTTPResult(outputs=outputs,
                              node_name=node.name,
                              response=resp,
                              code=code,
                              node_id=node.id)

    async def run_script_component(self,
                                   node: AgentWorkflowNode,
                                   inputs: Dict[str, Any],
                                   envs: Dict[str, str],
                                   client: ClientSession):
        url = f'${settings.workflow_config.langbase_proxy.endpoint}/api/v1/proxy/script/exec'
        k_script_type = "scriptType"
        k_script = "script"
        required_keys = [k_script_type, k_script]

        for key in required_keys:
            if key not in inputs:
                raise ParameterInvalid(f"Missing required key: {key}")

        self.assign_configs_into_map(node.configs, inputs, envs)
        self.assign_vars_into_map(node.vars, inputs)

        async with client.post(url, json=inputs) as response:
            code = response.status
            if code >= 400:
                raise ParameterInvalid(f"Request failed, status code: {code}")
            resp = await response.json()
            outputs = {}
            if node.outputs:
                for output in node.outputs:
                    outputs[output.name] = resp.get(output.name)
            return NodeScriptResult(outputs=outputs,
                                    node_name=node.name,
                                    response=outputs,
                                    node_id=node.id)

    async def run_llm_component(self,
                                node: AgentWorkflowNode,
                                app: App,
                                app_config_id: str,
                                user_id: str,
                                db: Session,
                                response_mode: ResponseMode,
                                inputs: Dict[str, Any],
                                envs: Dict[str, str],
                                conversation_id: Optional[str] = None):
        if not node.outputs or len(node.outputs) != 1:
            raise ParameterInvalid("LLM node must have exactly one output")
        output_config = node.outputs[0]
        # 大语言模型在agent-workflow中不需要通过环境变量拿api_key和api_base,完全走原来agent的逻辑从模型配置中获取即可
        # k_api_key = "apiKey"
        # k_api_base = "apiBase"
        k_max_conversations = "maxConversations"
        k_max_conversations_v2 = "max_conversations"
        k_model_name = "modelName"
        k_content = "content"
        k_provider_kind = "providerKind"
        k_system_prompt = "systemPrompt"
        k_system_prompt_v2 = "system_prompt"
        required_keys = [k_model_name, k_content, k_provider_kind]

        self.assign_configs_into_map(node.configs, inputs, envs)
        self.assign_vars_into_map(node.vars, inputs)

        for key in required_keys:
            if key not in inputs:
                raise ParameterInvalid(f"Missing required key: {key}")

        # v_api_key = inputs.pop(k_api_key, None)
        # v_api_base = inputs.pop(k_api_base, None)
        v_max_conversations = inputs.pop(
            k_max_conversations, None) or inputs.pop(k_max_conversations_v2, None)
        v_model_name = inputs.pop(k_model_name, None)
        v_prompt = inputs.pop(k_content, None)
        v_provider_kind = inputs.pop(k_provider_kind, None)
        v_system_prompt = inputs.pop(
            k_system_prompt, None) or inputs.pop(k_system_prompt_v2, None)

        executor = await DomainLLMRunner(
            workspace_id=str(app.workspace_id),
            group_id=str(app.group_id),
            db=db,
        )

        req = ConversationRequest(
            message=v_prompt,
            parameters=inputs)

        config = AgentConfig(
            modelName=v_model_name,
            prePrompt=v_system_prompt,
            providerKind=v_provider_kind)

        messages = list()
        # prompt = await system_prompt(config, params.parameters)
        # if prompt is not None:
        #     messages.append(prompt)

        max_conversations = int(v_max_conversations)
        # 添加多模态的用户输入
        messages = await construct_messages(
            db=db,
            conversation_id=conversation_id,
            config=config if v_system_prompt else None,
            prompt_params=None,
            max_conversations=max_conversations,
        )
        appendMultiModalMessages(req, config, messages, None)

        # construct_messages已经处理过了，这里不需要二次处理

        # cutted_messages = list()
        # if v_system_prompt:
        #     max_conversations -= 1
        #     cutted_messages.append(messages.pop(0))
        # cutted_messages.extend(messages[len(messages) - max_conversations:])
        # messages = cutted_messages

        # 如果有 conversationID，说明是继续对话
        # 否则 新建对话
        conv = await get_or_create_db_conversation_service(
            db,
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_create=ConversationCreate(
                app_id=str(app.id),
                app_config_id=str(app_config_id),
                override_config=AgentConfig(
                    modelName=v_model_name,
                    providerKind=v_provider_kind,
                ),
                parameters=inputs,
            ),
        )
        conversation_id = conv.id

        generator = dialogue(
            app_id=str(app.id),
            params=req,
            response_mode=response_mode,
            user_id=user_id,
            conversation=conv,
            config=config,
            executor=executor,
            messages=messages,
            plugins=None,
            db=db,
            # api_key=v_api_key,
            # api_base=v_api_base,
        )
        content = ''
        async for response in generator:
            if response_mode == ResponseMode.JSON:
                yield NodeLLMJSONResult(outputs={
                    output_config.name: response['content']
                },
                    node_name=node.name,
                    node_id=node.id,
                    response=response['content'],
                    max_conversations=max_conversations,
                    conversation_id=conversation_id
                )
            else:
                if response and 'content' in response:
                    content += response['content']
                    yield NodeLLMStreamResult(outputs={},
                                              node_name=node.name,
                                              node_id=node.id,
                                              response=response['content'],
                                              max_conversations=max_conversations,
                                              conversation_id=conversation_id)
        if response_mode == ResponseMode.STREAMING:
            yield NodeLLMStreamResult(outputs={
                output_config.name: content
            },
                stage=StreamStage.END,
                node_name=node.name,
                node_id=node.id,
                response=content,
                max_conversations=max_conversations,
                conversation_id=conversation_id)

    async def run_select_component(self,
                                   node: AgentWorkflowNode,
                                   inputs: Dict[str, Any]):
        if not node.inputs:
            raise ParameterInvalid("Select node must have at least one input")
        if not node.outputs:
            raise ParameterInvalid("Select node must have at least one output")
        selected_port = None
        output_port_dict = {
            output.name: output for output in node.outputs
        }
        for input_config in node.inputs:
            if input_config.name in inputs and input_config.name in output_port_dict:
                selected_port = output_port_dict[input_config.name]
                break
        if selected_port is None:
            raise ParameterInvalid("No output selected")
        return selected_port

    async def run_switch_component(self,
                                   node: AgentWorkflowNode,
                                   inputs: Dict[str, Any]):
        '''
        Switch 节点的执行逻辑
        1. 从输入中获取变量名
        2. 从全局变量中获取变量值
        3. 根据变量值选择分支
        4. 返回选择 port
        '''
        # inputs = node.inputs
        # if not inputs or len(inputs) != 1:
        #     raise ParameterInvalid("Switch node must have exactly one input")

        # inputVarName = inputs[0].name

        # inputVarValue = global_vars_map.get(inputVarName)
        # if inputVarValue is None:
        #     raise ParameterInvalid(f"Variable {inputVarName} not found")
        k_switch_value = "switchVal"
        v_switch_value = inputs.pop(k_switch_value, None)
        if v_switch_value is None:
            raise ParameterInvalid(f"Variable {k_switch_value} not found")
        branches = node.outputs
        if not branches or len(branches) == 0:
            raise ParameterInvalid("Switch node must have at least one branch")

        selected_branch: Optional[ComponentConfigOutput] = None
        for branch in branches:
            if branch.name == "default" and selected_branch is None:
                selected_branch = branch
            elif v_switch_value == branch.name:
                selected_branch = branch
                break
        if selected_branch is None:
            raise ParameterInvalid(
                f"Branch with value == {v_switch_value} not found")
        return selected_branch


engine = Engine()
