from prometheus_fastapi_instrumentator import Instrumentator
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import APIRouter, FastAPI

from .service.dialog import test_models_performance

from .cache.app import init_app_cache

from .cache.account import init_account_cache

# from .misc.middleware import ValidationErrorLoggingRoute

from .service.metric import alert_group_fee, join_mertic_data_V2
from . import controller, misc
from .cache.env_variable_cache import init_env_variable_cache, update_env_variable_cache_interval
from .service.document_task import check_ada_document_task
from .config import settings
import os
import time

# 修改time zone保证时间戳展示正常
os.environ['TZ'] = 'Asia/Shanghai'
time.tzset()

app = FastAPI(debug=False, redirect_slashes=False)

router = APIRouter(prefix="/api/v1")

instrument = Instrumentator(should_group_status_codes=False,
                            should_ignore_untemplated=True,
                            excluded_handlers=["/metrics"])

controller.register(router)
misc.init_app(app)
app.include_router(router)
instrument.instrument(app=app).expose(app=app, endpoint="/metrics")

executors = {
    'default': ThreadPoolExecutor(max_workers=1)
}
scheduler = BackgroundScheduler(executors=executors)
scheduler.add_job(update_env_variable_cache_interval,
                  "interval",
                  seconds=settings.env_variable_value_update_interval)


def start_async_job():
    asyncScheduler = AsyncIOScheduler()
    # 每天凌晨执行聚合任务
    asyncScheduler.add_job(join_mertic_data_V2, 'cron',
                           hour='5', minute='0')
    # 每周一早上8点执行周报任务
    asyncScheduler.add_job(alert_group_fee, 'cron',
                           day_of_week='0', hour='8', minute='0')
    # 每天凌晨执行聚合任务
    asyncScheduler.add_job(test_models_performance, 'cron',
                           hour='1', minute='0')
    # asyncScheduler.add_job(check_ada_document_task, 'interval',
    #                        seconds=settings.check_ada_document_task_interval)
    asyncScheduler.start()


@app.on_event("startup")
def app_startup():
    init_account_cache()
    init_app_cache()
    init_env_variable_cache()
    if not settings.is_service:
        start_async_job()


def main():
    import uvicorn
    scheduler.start()
    uvicorn.run("src.langbase:app",
                host=settings.server_host,
                port=settings.server_port,
                reload=settings.debug)


if __name__ == "__main__":
    main()
