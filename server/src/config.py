import os
from click import File
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Extra, Field, BaseModel, ConfigDict
from typing import Optional, List

from .schema.model import ModelProviderKind, ModelType
from pydantic_yaml import parse_yaml_file_as

from .schema.workflow_engine import WorkflowEngine


def getRootFilePath(fileName):
    """获取工程根目录(langbase/server)下的文件"""
    return os.path.join(os.path.abspath(__file__), os.pardir, os.pardir, fileName)


gen_name_prompt = """# 角色
你是一名智能助手，专注于理解用户的意图并提供简洁的标题。

## 背景
用户希望通过简短的对话获取信息，并希望助手能够准确提炼出他们的意图。

## 技能
### 技能 1: 理解用户意图
1. 分析用户输入的内容，提炼出核心意图。
2. 根据用户使用的语言返回相应的标题。

### 技能 2: 输出标题
1. 确保标题简洁明了，不超过20个字。
2. 根据语言类型（中文或英文）输出相应的标题。

## 要求:
- 只返回与用户输入相关的标题，不输出其他信息。
- 标题必须准确反映用户的意图。

## 任务
总结用户所说内容的意图并提供一个标题。

## 示例
Ex1:
用户输入: hello, what's your name
你的输出：
greeting

Example2:
用户输入：我的电脑出问题了，莫名蓝屏，你能给我一些解决方案吗？
你的输出：
帮用户处理电脑问题


用户输入：{query}
你的输出："""


class S3(BaseModel):
    access_key: str = Field(min_length=1,
                            validation_alias="accessKey",
                            serialization_alias="accessKey")
    secret_key: str = Field(min_length=1,
                            validation_alias="secretKey",
                            serialization_alias="secretKey")
    region: str = Field(min_length=1,
                        validation_alias="region",
                        serialization_alias="region")
    endpoint: str = Field(min_length=1,
                          validation_alias="endpoint",
                          serialization_alias="endpoint")
    bucket: str = Field(min_length=1,
                        validation_alias="bucket",
                        serialization_alias="bucket")
    disable_ssl: bool = Field(default=False,
                              validation_alias="disableSSL",
                              serialization_alias="disableSSL")
    skip_verify: bool = Field(default=True,
                              validation_alias="skipVerify",
                              serialization_alias="skipVerify")
    s3_force_path_style: bool = Field(default=False,
                                      validation_alias="s3ForcePathStyle",
                                      serialization_alias="s3ForcePathStyle")


class CIOConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    endpoint: str


class MidjourneyConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    endpoint: str
    poll_interval_ms: int = Field(default=5000, alias="pollIntervalMs")


class AirshipConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    endpoint: str


class SkyeyeConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    endpoint: str


class AIOCallbackConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    domain: str
    topic: str
    producer_id: str = Field(alias="producerId")


class DreamMakerConfig(BaseModel):
    endpoint: str
    user: str
    token: str
    auth_endpoint: str
    key: str
    ttl: int
    poll_interval_ms: Optional[int] = Field(
        default=3000, alias="pollIntervalMs")


class LangbaseProxyConfig(BaseModel):
    endpoint: str


class AisongConfig(BaseModel):
    endpoint: str


class WorkflowConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    engines: List[WorkflowEngine]
    s3: S3
    cio: CIOConfig
    midjourney: MidjourneyConfig
    skyeye: SkyeyeConfig
    aisong: AisongConfig
    airship: AirshipConfig
    aio_callback: AIOCallbackConfig = Field(alias="aioCallback")
    dream_maker: DreamMakerConfig = Field(alias="dreammaker")
    langbase_proxy: LangbaseProxyConfig = Field(alias="langbaseProxy")


class OauthProvider(BaseModel):
    name: str
    issuer: str
    client_id: str
    client_secret: str
    scope: Optional[str]
    redirect_uri: Optional[str]


class DefaultModel(BaseModel):
    model_type: ModelType
    provider_kind: ModelProviderKind
    model_name: str


class Settings(BaseSettings, extra='allow'):
    model_config = SettingsConfigDict(env_file=('.env', '.env.prod'))

    server_host: str = Field('0.0.0.0')
    server_port: int = Field(8000)

    sqlalchemy_database_uri: str = Field(
        "mysql+pymysql://root:langbase@localhost:3306/langbase")

    session_expire_seconds: int = Field(60 * 60 * 24 * 7)
    cookie_key: str = Field('langbase|session')
    cookie_secret: str = Field('any_random_string')

    roles_file_path: str = Field(getRootFilePath('roles.yaml'))
    provider_file_path: str = Field(getRootFilePath('providers.yaml'))

    workflow_engine_file_path: str = Field(getRootFilePath('workflow.yaml'))
    workflow_config: Optional[WorkflowConfig] = None

    oauth: List[OauthProvider] = Field([])

    default_model: List[DefaultModel] = Field([
        DefaultModel(
            provider_kind=ModelProviderKind.OPENAI,
            model_name='gpt-3.5-turbo-1106',
            model_type=ModelType.LLM,
        )
    ])
    default_group_name: str = Field('默认分组')
    default_workspace_name: str = Field('{}的工作空间')
    default_basic_app_name: str = Field('基础应用')
    default_basic_model: str = Field('gpt-4o-mini-2024-07-18')
    default_basic_provider: str = Field('openai')
    default_model_params: dict = Field({
        "top_p": 0.85,
        "temperature": 0.6,
        "max_tokens": 1024,
        "presence_penalty": 0,
        "frequency_penalty": 0,
    })

    redis_host: str = Field('localhost')
    redis_port: int = Field(6379)
    redis_db: int = Field(0)
    redis_cluster: Optional[list[dict]] = Field(
        [{"host": "*************", "port": "7103"}])
    redis_username: Optional[str] = Field(None)
    redis_password: Optional[str] = Field(None)

    openai_api_key: str = Field('')
    openai_api_base: str = Field("https://api.openai.com/v1")

    moonshot_api_key: str = Field('')
    moonshot_api_base: str = Field("https://api.moonshot.cn/v1")

    deepseek_api_key: str = Field('')
    deepseek_api_base: str = Field("https://api.deepseek.com")

    minimax_api_key: str = Field('')
    minimax_api_base: str = Field("https://api.minimax.chat/v1")

    doubao_api_key: str = Field('')
    doubao_api_base: str = Field(
        "https://aigw-int.netease.com/v1/chat/completions")

    netease_api_app_id: str = Field('')
    netease_api_app_key: str = Field('')
    netease_api_base: str = Field("https://aigc-api.hz.netease.com/openai")

    db_service_url: str = Field(
        "http://music-langbase-manager-reg-guiyang-test.yf-onlinetest1-gy1.service.gy.ntes")

    service_proxy_prefix: str = Field('http://qa.igame.163.com')
    music_proxy_prefix: str = Field('https://music.163.com')
    use_service_proxy: bool = Field(False)

    workspace_member_id: str = Field('d8a1ec95-db93-40f3-ae73-49ea2bc1caa3')

    conversation_history_limit: int = Field(5)
    conversation_title_prompt: str = Field(
        default=gen_name_prompt
    )
    conversation_title_query_length: int = Field(2000)

    jwt_secret_key: str = Field('any_random_string')
    jwt_expire_seconds: int = Field(3600 * 24)
    jwt_algorithm: str = Field('HS256')

    is_service: bool = Field(False)
    debug: bool = Field(False)
    testing: bool = Field(False)
    debug_workflow_id_suffix: str = Field('_debug')
    env_variable_value_update_interval: int = Field(30)
    check_ada_document_task_interval: int = Field(30)
    env_variable_value_update_interval_time_deta: int = Field(180)
    workflow_url_prefix: str = Field('api/v1/workflowengine')
    app_workflow_temp_upload_file_path: str = Field(
        'langbase/app/upload/files')

    app_lock_expire_seconds: int = Field(15)

    enable_profile: bool = Field(False)

    enable_query: bool = Field(True)

    ada_api_base: str = Field('')

    sse_api_base: str = Field('')

    use_mlog: bool = Field(False)

    default_workspace_id: str = Field('8f0c9289-0f01-496b-b896-2a21cbf380af')

    test_model_workspace_id: str = Field(
        'f97bf87b-a8ec-4bc9-a8e3-662e111cd2e4')

    test_model_app_id: str = Field(
        '2ffa6f22-3049-41ef-9c52-5a3db2bb6943')

    disable_mysql: bool = Field(False)

    pms_app_key: str = Field('langbase')

    pms_app_secret: str = Field('1DDFD54C951C9035')

    apollo_proc_def_key: str = Field('apollo_cloud_distribute_LangBase')

    aigw_base_url: str = Field("https://aigw-int.netease.com")
    aigw_auth_key: str = Field("26a607889a084bfb971efb5071f3a509")
    aigw_auth_url: str = Field("http://int-auth.nie.netease.com")

    rag_app_id: str = Field("8911765a-d3fa-48c2-8a51-dc492c5a0a5f")

    alert_popo_id: str = Field("6662962")

    overmind_host: str = 'http://music-overmind.service.163.org'
    overmind_client_id: str = '383ff3a5c3622f083ec9a5713ec3017e'
    overmind_secret: str = 'fcc95a6f29d3a804da1f5ddb6f1e0299'

    tmax_group_id: str = Field('2ec1abc7-b707-4a0e-808a-589da7638307')
    popo_score_app_id: str = Field('d0be9d6b-d318-4e5f-b37c-0a31adf87d24')
    popo_score_chatflow_app_id: str = Field(
        '85e0137c-4786-4b97-9649-6a2d2d05c784')


settings = Settings()  # type: ignore

settings.workflow_config = parse_yaml_file_as(
    WorkflowConfig, settings.workflow_engine_file_path)


dev_oatuth_setting = OauthProvider(
    name="netease",
    issuer="https://login.netease.com",
    client_id="6f9b5918f01f11efb7640242ac120003",
    client_secret="eb4d855b75b248c093efcc0c5393794f6f9b5c56f01f11efb7640242ac120003",
    scope="openid profile email",
    # redirect_uri="http://langbase-local.yf-dev2.netease.com:3100/api/v1/oauth/callback/netease"
    redirect_uri="http://langbase-local.netease.com:3100/api/v1/oauth/callback/netease"
)

# settings.db_service_url = 'http://qa-ankle.igame.163.com'

# settings.redis_cluster = [{"host": "***********", "port": "6503"}]
# settings.sqlalchemy_database_uri = 'mysql+pymysql://langbase:_M7zZ#<EMAIL>:3331/langbase'
# # settings.service_proxy_prefix = 'http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes' settings.service_proxy_prefix = 'http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes'
# # settings.service_proxy_prefix = 'http://qa.igame.163.com'
# settings.service_proxy_prefix = 'http://music-langbase-service-pre-guiyang1.yf-online-gy1.service.gy.ntes'
# settings.db_service_url = 'http://music-langbase-manager-online-gz.yf-online-gy1.service.gy.ntes'
# settings.redis_password = '5715b975b8bf'
