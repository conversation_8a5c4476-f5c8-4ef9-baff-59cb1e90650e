import datetime
import json

from loguru import logger
import threading
from typing import List, Dict

from ..misc.redis_utils import ENV_OPERATION_CACHE_KEY, delete_redis_sync, get_redis_sync

from ..misc.log import logger_info

from ..misc.db import get_db
from ..models.env_variable import EnvVariable
from ..schema.env_variable import EnvVariableScopeType
from ..config import settings
from ..service.env_variable import list_latest_update_variable_values


class EnvVariableCache:
    envs: Dict[str, List[str]]  # key: component_id, value: env name list
    components_values: Dict[str, str]  # key: env name, value: env value
    # workspace id: value : {key: env name, value: env value}
    workspace_values: Dict[str, Dict[str, str]]
    # group id: value: {key: env name, value: env value}
    group_values: Dict[str, Dict[str, str]]
    # app id: value {key: env name, value: env value}
    app_values: Dict[str, Dict[str, str]]

    def __init__(self):
        self.envs = {}
        self.envs_lock = threading.Lock()
        self.components_values = {}
        self.components_values_lock = threading.Lock()
        self.workspace_values = {}
        self.workspace_values_lock = threading.Lock()
        self.group_values = {}
        self.group_values_lock = threading.Lock()
        self.app_values = {}
        self.app_values_lock = threading.Lock()

    def get(self, components: List[str], app_id: str, group_id: str, workspace_id: str) -> Dict[str, str]:
        if components is None or len(components) == 0:
            return {}

        envs = []
        values = {}
        with self.envs_lock:
            for component_id in components:
                if component_id in self.envs:
                    envs.extend(self.envs[component_id])

        with self.components_values_lock:
            for env in envs:
                if env in self.components_values:
                    values[env] = self.components_values[env]
        with self.workspace_values_lock:
            for env in envs:
                if workspace_id in self.workspace_values:
                    if env in self.workspace_values[workspace_id]:
                        values[env] = self.workspace_values[workspace_id][env]
        with self.group_values_lock:
            for env in envs:
                if group_id in self.group_values:
                    if env in self.group_values[group_id]:
                        values[env] = self.group_values[group_id][env]
        with self.app_values_lock:
            for env in envs:
                if app_id in self.app_values:
                    if env in self.app_values[app_id]:
                        values[env] = self.app_values[app_id][env]

        return values

    def update(self,
               component_values: List[EnvVariable],
               workspace_values: List[EnvVariable],
               group_values: List[EnvVariable],
               app_values: List[EnvVariable]):
        # update cache
        with self.envs_lock:
            for value in component_values:
                if value.component_id not in self.envs:
                    self.envs[value.component_id] = [value.name]
                elif value.name not in self.envs[value.component_id]:
                    self.envs[value.component_id].append(value.name)

        with self.components_values_lock:
            for value in component_values:
                self.components_values[value.name] = value.value

        with self.workspace_values_lock:
            for value in workspace_values:
                if value.scope_id not in self.workspace_values:
                    self.workspace_values[value.scope_id] = {}
                self.workspace_values[value.scope_id][value.name] = value.value

        with self.group_values_lock:
            for value in group_values:
                if value.scope_id not in self.group_values:
                    self.group_values[value.scope_id] = {}
                self.group_values[value.scope_id][value.name] = value.value

        with self.app_values_lock:
            for value in app_values:
                if value.scope_id not in self.app_values:
                    self.app_values[value.scope_id] = {}
                self.app_values[value.scope_id][value.name] = value.value

    def components_values_len(self) -> int:
        with self.components_values_lock:
            return len(self.components_values)


env_variable_cache = EnvVariableCache()


def init_env_variable_cache():
    global env_variable_cache

    logger_info("init_env_variable_cache")

    db = next(get_db())
    component_values = list_latest_update_variable_values(
        db=db, scope_type=EnvVariableScopeType.COMPONENT.value)
    for value in component_values:
        env_variable_cache.components_values[value.name] = value.value
        if value.scope_id not in env_variable_cache.envs:
            env_variable_cache.envs[value.scope_id] = [value.name]
        elif value.name not in env_variable_cache.envs[value.scope_id]:
            env_variable_cache.envs[value.scope_id].append(value.name)

    workspace_values = list_latest_update_variable_values(
        db=db, scope_type=EnvVariableScopeType.WORKSPACE.value)
    for value in workspace_values:
        if value.scope_id not in env_variable_cache.workspace_values:
            env_variable_cache.workspace_values[value.scope_id] = {}
        env_variable_cache.workspace_values[value.scope_id][value.name] = value.value

    group_values = list_latest_update_variable_values(
        db=db, scope_type=EnvVariableScopeType.GROUP.value)
    for value in group_values:
        if value.scope_id not in env_variable_cache.group_values:
            env_variable_cache.group_values[value.scope_id] = {}
        env_variable_cache.group_values[value.scope_id][value.name] = value.value

    app_values = list_latest_update_variable_values(
        db=db, scope_type=EnvVariableScopeType.APP.value)
    for value in app_values:
        if value.scope_id not in env_variable_cache.app_values:
            env_variable_cache.app_values[value.scope_id] = {}
        env_variable_cache.app_values[value.scope_id][value.name] = value.value

    logger_info(f"end init_env_variable_cache, "
                f"load components values count: {env_variable_cache.components_values_len()}")


pre_env_variable_cache_update_time = datetime.datetime.now()


def update_env_variable_cache_interval():
    global env_variable_cache
    global pre_env_variable_cache_update_time

    if env_variable_cache.components_values_len() <= 0:
        logger.warning(
            "env_variable_value_update_interval: env_variable_cache.components_values is empty")
        init_env_variable_cache()
        return
    logger_info("begin env_variable_value_update_interval, pre update time:" +
                str(pre_env_variable_cache_update_time))
    logger_info(
        f" components values count: {env_variable_cache.components_values_len()}")

    operations = get_redis_sync(ENV_OPERATION_CACHE_KEY)
    if operations:
        operations = json.loads(operations)
    else:
        operations = {
            "create": [],
            "update": [],
            "remove": []
        }
    logger.info("env operations: {operations}",
                operations=operations, uid='uid')
    # 如果operations中存在create和update，则需要更新缓存
    if (operations.get("create") and len(operations["create"]) > 0 or
        operations.get("update") and len(operations["update"]) > 0 or
            operations.get("remove") and len(operations["remove"]) > 0):
        db = next(get_db())
        count = 0
        cur_time = datetime.datetime.now()
        query_update_time = \
            pre_env_variable_cache_update_time \
            - datetime.timedelta(seconds=settings.env_variable_value_update_interval_time_deta)

        component_values = list_latest_update_variable_values(db,
                                                              EnvVariableScopeType.COMPONENT.value,
                                                              query_update_time)
        count += len(component_values)
        workspace_values = list_latest_update_variable_values(db,
                                                              EnvVariableScopeType.WORKSPACE.value,
                                                              query_update_time)
        count += len(workspace_values)
        group_values = list_latest_update_variable_values(
            db, EnvVariableScopeType.GROUP.value, query_update_time)
        count += len(group_values)
        app_values = list_latest_update_variable_values(
            db, EnvVariableScopeType.APP.value, query_update_time)
        count += len(app_values)

        env_variable_cache.update(
            component_values, workspace_values, group_values, app_values)
        pre_env_variable_cache_update_time = cur_time
        logger_info(
            f"end env_variable_value_update_interval, update {count} env variable values")
        delete_redis_sync(ENV_OPERATION_CACHE_KEY)
