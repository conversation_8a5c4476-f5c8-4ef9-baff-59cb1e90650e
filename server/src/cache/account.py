import json

from ..misc.redis_utils import get_redis_sync, set_redis_sync
from ..models.account import Account
from ..misc.db import get_db
global account_cache
account_cache = {}


def init_account_cache():
    global account_cache
    db = next(get_db())
    try:
        temp_cache = get_redis_sync('account_cache')
        if temp_cache:
            account_cache = json.loads(temp_cache)
    except Exception as e:
        print(f"init_account_cache error: {e}")
    accounts = db.query(Account).all()
    # 防止测试数据覆盖线上数据
    if len(accounts) > len(account_cache.keys()):
        set_redis_sync('account_cache', json.dumps({
            account.id: {
                'email': account.email,
                'fullname': account.fullname,
                'name': account.name,
                'id': account.id,
                'is_admin': account.is_admin
            } for account in accounts
        }))


def get_account_cache():
    return account_cache
