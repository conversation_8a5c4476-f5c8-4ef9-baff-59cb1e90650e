import json
from ..misc.http_db import get_http_db
from ..models.app import App
from ..misc.redis_utils import APP_CACHE_KEY, set_redis_sync
global app_cache
app_cache = {}


def init_app_cache():
    global app_cache
    http_db = get_http_db()

    # 分批查询，每批2000条
    batch_size = 2000
    offset = 0
    all_apps = {}

    while True:
        apps_batch = http_db.query(App).limit(batch_size).offset(offset).all()
        if not apps_batch:
            break

        # 处理当前批次数据
        batch_data = {
            app.id: {
                'name': app.name,
                'group_id': app.group_id,
                'workspace_id': app.workspace_id
            } for app in apps_batch
        }
        all_apps.update(batch_data)

        offset += batch_size
        if len(apps_batch) < batch_size:
            break

    # 将完整数据存入Redis
    set_redis_sync(APP_CACHE_KEY, json.dumps(all_apps))


def get_app_cache():
    return app_cache
