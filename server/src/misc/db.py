from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
import json
from pydantic.json import pydantic_encoder
from ..config import settings


def _custom_json_searializer(*args, **kwargs) -> str:
    return json.dumps(*args, default=pydantic_encoder, **kwargs)


engine = create_engine(
    str(settings.sqlalchemy_database_uri),
    connect_args={
        'charset': 'utf8mb4',
        "init_command": "SET SESSION time_zone='+08:00'"},
    json_serializer=_custom_json_searializer,
    pool_recycle=3600,
    pool_size=1000,
    pool_pre_ping=True,
    max_overflow=1000,
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
ScopedSession = scoped_session(SessionLocal)


def get_db(use_http: bool = False):
    session = SessionLocal()
    hasException = False
    session.begin()
    try:
        yield session
    except Exception:
        hasException = True
        session.rollback()
        raise
    finally:
        if not hasException:
            session.commit()
        session.close()
