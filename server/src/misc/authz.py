from typing import Dict, List, Optional, Tuple
from fastapi import Depends, Request
from loguru import logger
from sqlalchemy.orm import Session
from pydantic_yaml import parse_yaml_file_as

from .log import logger_error

from ..service.http_session import ModelProxy

from ..schema.token import TwoKindPayload

from ..models.token import Token

from .authn import token_required

from ..service.member import get_member_of_resource_by_user
from .db import get_db
from .session import SessionUser
from ..config import settings
from .errors import BadRequest, LoginRequired, PermissionDenied
from ..schema.rbac import RoleConfig, Resource
from ..schema.common import ResourceType
from ..schema.member import Role
from ..service.account import get_by_id as get_account_by_id_service
from ..service.component import get as get_component_service
from ..schema.common import Scope
from ..service.token import get_related_resource


class Enforcer:
    role_config: RoleConfig
    role_priority_desc: Dict[str, int]
    # role -> resources -> verbs
    roles: Dict[str, Dict[str, List[str]]]

    def __init__(self):
        self.role_config = parse_yaml_file_as(RoleConfig,
                                              settings.roles_file_path)
        self.roles = dict()
        for role in self.role_config.roles:
            if role.name not in self.roles:
                self.roles[role.name] = dict()
            roles_to_resource = self.roles[role.name]
            for rule in role.rules:
                for resource in rule.resources:
                    if resource not in roles_to_resource:
                        roles_to_resource[resource] = list()
                    roles_to_resource[resource].extend(
                        [verb.lower() for verb in rule.verbs])

        self.role_priority_desc = dict()
        for i, role in enumerate(self.role_config.rolePriorityDesc):
            self.role_priority_desc[role] = i

    async def enforce(self,
                      db: Session,
                      user_id: str,
                      is_admin: bool,
                      resources: List[Resource],
                      verb: str) \
            -> Tuple[bool, Optional[str]]:
        if len(resources) == 0:
            return (False, None)
        if is_admin:
            return True, Role.SYS_ADMIN.value
        resource: Resource = resources[-1]
        if resource.type == ResourceType.LLM_MODELS.value:
            return True, Role.SYS_ADMIN.value
        if not hasattr(resource, "id") \
           or resource.id is None \
           or resource.id == "":
            if verb == "GET":
                verb = "LIST"
            if len(resources) == 1:
                if verb == "LIST":
                    # skip permission check for resource-list
                    # controller should check permission by itself
                    logger.bind(resources=resources).debug(
                        "resource-list request skipped permission check")
                    return (True, Role.SYS_ADMIN.value)
                # modifying the top level resource is allowed
                # only if the user is admin
                # 如果资源类型在external_user的roles中，则允许修改
                if resource.type in self.roles['external_user'] or resource.type in self.roles['viewer']:
                    return (True, Role.SYS_ADMIN.value)
                return (is_admin,
                        Role.SYS_ADMIN.value if is_admin else None)
            resource = resources[-2]
        if resource.type == ResourceType.AIGW_APP.value:
            return True, None

        if resource.type == ResourceType.APOLLO.value:
            return True, None

        if resource.type == ResourceType.AIGW_BIND.value:
            return True, None
        # if the resource is workflow run, we need to check app permission
        if resource.type == ResourceType.WORKFLOW_RUNS.value\
                and hasattr(resource, "id") and resource.id is not None:
            resource = resources[-2]

        if resource.type == ResourceType.COMPONENTS.value and verb == "GET":
            # type: ignore
            component = await get_component_service(db, resource.id)
            if component is not None and component.scope == Scope.PUBLIC.value:  # type: ignore
                return True, None

        if resource.type == ResourceType.COMPONENT_CATEGORIES.value and verb == "GET":
            return True, None

        try:
            member = await get_member_of_resource_by_user(
                db,
                user_id,
                resource.type,
                resource.id)  # type: ignore # noqa

            role = self.role_config.defaultRole \
                if member is None \
                else str(member.role)
            return (self.role_match(role, resources, verb), role)
        except Exception:
            db.rollback()
            raise

    def role_match(self, role: str,
                   resources: List[Resource],
                   verb: str) -> bool:
        if role not in self.roles \
           or len(resources) == 0:
            return False
        # 管理员拥有所有的权限
        if role == Role.ADMIN.value:
            return True
        verb = verb.lower()
        if verb == "get":
            verb = "list"
        roles_to_resource = self.roles[role]
        resourceKey = resources[0].type
        for i in range(1, len(resources)):
            resourceKey = f"{resourceKey}/{resources[i].type}"

        # 如果接口没有特殊定义的，则也拥有权限
        if resourceKey not in roles_to_resource:
            return True
        return verb in roles_to_resource[resourceKey]

    def role_compare(self, role1: str, role2: str) -> int:
        if role1 == role2:
            return 0
        if role1 == Role.SYS_ADMIN.value:
            return 1
        if role2 == Role.SYS_ADMIN.value:
            return -1
        if role1 not in self.role_priority_desc:
            raise BadRequest(f"Invalid role: {role1}")

        if role2 not in self.role_priority_desc:
            raise BadRequest(f"Invalid role: {role2}")

        return -(self.role_priority_desc[role1] - self.role_priority_desc[role2])


enforcer = Enforcer()


def parse_resource(path: str) -> List[Resource]:
    path = path.removeprefix("/api/v1")
    paths = path.split("/")
    resources: List[Resource] = []
    if len(paths) > 0:
        paths = paths[1:]
        for i in range(0, len(paths), 2):
            resource = Resource(type=paths[i])
            if i + 1 < len(paths):
                resource.id = paths[i + 1]
            resources.append(resource)
    if len(resources) == 0:
        logger.bind(path=path).warning("Resource is empty")
    return resources


async def permission_check(
        resources: List[Resource],
        method: str,
        user_id: str,
        is_admin: bool,
        enforcer: Enforcer,
        db: Session = Depends(get_db)) -> str:

    (passed, role) = await enforcer.enforce(
        db=db,
        user_id=user_id,
        is_admin=is_admin,
        resources=resources,
        verb=method)
    if passed:
        logger.bind(resources=resources) \
            .bind(method=method).debug("Enforce allowed")
    else:
        logger.bind(resources=resources) \
            .bind(method=method).debug("Enforce denied")
        logger.info("Enforce denied: {is_admin} {role} {resources} {method}",
                    is_admin=is_admin, role=role, resources=resources, method=method, uid=user_id)
        raise PermissionDenied("You have no permission to "
                               "access this resource")
    return role  # type: ignore


async def admit_by_token_or_cookie(
        request: Request,
        db: Session = Depends(get_db)):
    try:
        await admit_by_token(request, db)
    except Exception as e:
        print(e)
        await admit_by_cookie(request, db)


async def admit_by_cookie(
        request: Request,
        db: Session = Depends(get_db)):

    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    user: SessionUser = request.user

    resources = parse_resource(request.url.path)
    return await permission_check(
        resources=resources,
        enforcer=enforcer,
        user_id=user.id,
        # is_admin=user.is_admin,
        is_admin=False,
        db=db,
        method=request.method)


async def admit_by_token_for_service(db: Session, request: Request,
                                     resource_type: ResourceType, resource_id: str):
    token = await token_required(request, db)

    if isinstance(token, Token) or isinstance(token, ModelProxy):
        (rtype, rid) = await get_related_resource(db, resource_type, resource_id, token.id)
        if rtype is None or rid is None:
            raise PermissionDenied(
                "You have no permission to access this resource")

        resources = [
            Resource(
                type=str(rtype),
                id=str(rid))]
        account = await get_account_by_id_service(db, str(token.created_by))
        user_id = str(token.created_by)
        is_admin = bool(account.is_admin)
    else:
        payload = TwoKindPayload.model_validate(
            {'data': token}
        ).data
        resources = [Resource(
            type=ResourceType.APP.value,
            id=payload.app_id
        )]
        # skip permission check for jwt token
        return payload

    await permission_check(
        resources=resources,
        enforcer=enforcer,
        user_id=user_id,
        is_admin=is_admin,
        db=db,
        method=request.method)
    return token


async def admit_by_token(
        request: Request,
        db=Depends(get_db)):

    token = await token_required(request, db)

    # 检查 token.createdBy，如果不存在则使用 created_by
    if hasattr(token, 'createdBy') and token.createdBy is not None:
        user = token.createdBy
        # 检查必填字段的有效性
        if not user.get('id'):
            raise BadRequest("Invalid user id")
        if not user.get('name'):
            raise BadRequest("Invalid user name")
        # 使用默认值处理可选字段
        request.state.user = SessionUser(
            id=str(user['id']),
            name=user['name'],
            is_admin=user.get('is_admin', False),  # 默认非管理员
            fullname=user.get('fullname', ''),     # 默认空字符串
            email=user.get('email', '')            # 默认空字符串
        )
    if hasattr(token, 'created_by') and token.created_by is not None:
        # 当只有created_by时，创建基础用户信息
        request.state.user = SessionUser(
            id=str(token.created_by),
            name=str(token.created_by),  # 使用ID作为临时名称
            is_admin=False,  # 默认非管理员
            fullname='',  # 空字符串作为默认值
            email=''  # 空字符串作为默认值
        )
        return token

    if isinstance(token, Token) or isinstance(token, ModelProxy):
        resources = [
            Resource(
                type=str(token.resource_type),
                id=str(token.resource_id))]
        account = await get_account_by_id_service(db, str(token.created_by))
        user_id = str(token.created_by)
        is_admin = bool(account.is_admin)
    else:
        payload = TwoKindPayload.model_validate(
            {'data': token}
        ).data
        resources = [Resource(
            type=ResourceType.APP.value,
            id=payload.app_id
        )]
        # skip permission check for jwt token
        return payload

    await permission_check(
        resources=resources,
        enforcer=enforcer,
        user_id=user_id,
        is_admin=is_admin,
        db=db,
        method=request.method)
    return token


def only_admin(request: Request):
    if not request.user.is_admin:
        raise PermissionDenied("You have no permission to "
                               "access this resource")
