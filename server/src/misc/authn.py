from typing import Any, Dict
from fastapi import Depends, FastAP<PERSON>, Request
import jwt
from starlette.authentication import Authentication<PERSON>ack<PERSON>, \
    AuthCredentials, UnauthenticatedUser
from starlette.middleware.authentication import AuthenticationMiddleware

from ..util import is_jwt_token

from ..config import settings

from .errors import LoginRequired, NotFound

from .db import get_db

from .session import whoami
from ..service.token import get as get_token_service


SCOPE_AUTHENTICATED = "authenticated"
SCOPE_UNAUTHENTICATED = "unauthenticated"


# by cookie
class BasicAuth(AuthenticationBackend):
    async def authenticate(self, request):
        user = await self.cookie_authn(request)
        if user is None:
            return AuthCredentials([SCOPE_UNAUTHENTICATED]), \
                UnauthenticatedUser()

        return AuthCredentials([SCOPE_AUTHENTICATED]), user

    async def cookie_authn(self, request: Request):
        return await whoami(request)


def init_app(app: FastAPI):
    app.add_middleware(AuthenticationMiddleware, backend=BasicAuth())


async def authenticate_by_token(
        request: Request,
        db=Depends(get_db)):
    token_value = request.headers.get("Authorization")
    if token_value is None:
        return None
    if not token_value.startswith("Bearer "):
        return None

    # trim "Bearer "
    token_value = token_value[7:]

    # jwt token
    if is_jwt_token(token_value):
        payload: Dict[str, Any] = jwt.decode(token_value,
                                             settings.jwt_secret_key,
                                             algorithms=["HS256"])
        return payload

    try:
        token = await get_token_service(
            db,
            token_value=token_value,
        )
        return token
    except NotFound:
        return None


async def token_required(
    request: Request,
    db=Depends(get_db),
):
    token = await authenticate_by_token(request, db=db)
    if token is None:
        raise LoginRequired("Token is missing required")
    return token
