import json
from typing import Callable
from fastapi import Fast<PERSON><PERSON>, Request, Response
from fastapi.concurrency import iterate_in_threadpool
from fastapi.responses import StreamingResponse
from fastapi.routing import APIRoute
from loguru import logger
from .session import SessionUser


class ValidationErrorLoggingRoute(APIRoute):
    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            path = request.url.path + '?' + request.url.query
            userId = request.query_params.get('user')
            method = request.method
            request_body = None
            user = SessionUser(id="uid", name="uid",
                               is_admin=False, email=userId or "uid", fullname="uid")
            response = Response()
            # 先执行原始的路由处理
            try:
                response = await original_route_handler(request)
            except Exception as e:
                logger.error("{path} call_next error {e}",
                             path=path, e=e, uid="uid")
                raise e
            if method != 'GET' and not request._stream_consumed:
                request_body = await request.body()
                request_body = request_body.decode('utf-8')
            # 不是所有的请求都需要鉴权，所以不一定有 user 字段
            if 'user' in request.scope and isinstance(request.user, SessionUser):
                user: SessionUser = request.user
            # 日志记录过程中万一发生什么异常，也进行日志记录
            try:
                # 在每次请求结束，进行日志处理
                if isinstance(user, SessionUser) and isinstance(response, Response):
                    content = ''
                    # 处理非200返回
                    if response.status_code < 200 or response.status_code >= 300:
                        if isinstance(response, StreamingResponse):
                            response_body = [section async for section in response.body_iterator]
                            response.body_iterator = iterate_in_threadpool(
                                iter(response_body))
                            try:
                                content = json.loads(
                                    response_body[0].decode('utf-8'))
                            except Exception as e:
                                content = ''
                                logger.error("{method} {path} logger handle error {e}",
                                             path=path, e=e, uid=user.email, method=method)
                        logger.error("{method} {path} {status_code} body={body} response={content}", path=path, body=request_body,
                                     status_code=response.status_code, content=content, uid=user.email, method=method)
                    # 处理200返回
                    else:
                        logger.info("{method} {path} {status_code} body={body}", path=path, status_code=response.status_code,
                                    uid=user.email, method=method, body=request_body)
            # 日志记录过程中万一发生什么异常，也进行日志记录
            except Exception as e:
                logger.error("{method} {path} logger handle error2 {e}",
                             path=path, e=e, uid=user.email, method=method)
            # 原封不动返回response
            return response
        return custom_route_handler


def init_app(app: FastAPI):
    # app.router.route_class = ValidationErrorLoggingRoute
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        path = request.url._url
        method = request.method
        request_body = None

        # 下面代码添加的话会阻塞整个程序执行 https://github.com/tiangolo/fastapi/discussions/8187#discussioncomment-7649906
        # body = None
        # if request.method == "POST":
        #     body = await request.body()
        response = None
        user = SessionUser(id="uid", name="uid",
                           is_admin=False, email="uid", fullname="uid")
        # 处理中间处理过程的异常记录
        try:
            response = await call_next(request)
        except Exception as e:
            logger.error("{path} call_next error {e}",
                         path=path, e=e, uid="uid")
            raise e
         # 不是所有的请求都需要鉴权，所以不一定有 user 字段
        if 'user' in request.scope and isinstance(request.user, SessionUser):
            user: SessionUser = request.user
        try:
            # 在每次请求结束，进行日志处理
            if isinstance(user, SessionUser) and isinstance(response, Response):
                content = ''
                # 处理非200返回
                if response.status_code < 200 or response.status_code >= 300:
                    if isinstance(response, StreamingResponse):
                        response_body = [section async for section in response.body_iterator]
                        response.body_iterator = iterate_in_threadpool(
                            iter(response_body))
                        try:
                            content = json.loads(
                                response_body[0].decode('utf-8'))
                        except Exception as e:
                            content = ''
                            logger.error("{method} {path} logger handle error {e}",
                                         path=path, e=e, uid=user.email, method=method)
                    logger.error("{method} {path} {body}: {status_code} {content}", path=path, body=request_body,
                                 status_code=response.status_code, content=content, uid=user.email, method=method)
                # 处理200返回
                else:
                    logger.info("{method} {path} {body}: {status_code}", path=path, status_code=response.status_code,
                                uid=user.email, method=method, body=request_body)
        # 日志记录过程中万一发生什么异常，也进行日志记录
        except Exception as e:
            logger.error("{method} {path} logger handle error2 {e}",
                         path=path, e=e, uid=user.email, method=method)
        # finally:
        #     request_body = await request.body()
        #     # 通过下面的方式，可以避免获取body后程序阻塞

        #     async def receive() -> Message:
        #         return {"type": "http.request", "body": body}
        #     request._receive = receive
        #     logger.info("{method} {path} {body}: {status_code}", path=path,
        #                 status_code=response.status_code, uid=user.email, method=method, body=request_body)
        # 原封不动返回response
        return response
