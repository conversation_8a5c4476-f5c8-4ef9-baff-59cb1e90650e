from redis import Redis
from redis.cluster import RedisCluster, ClusterNode
from typing import Optional, Union
from ..config import settings

# 同步Redis客户端初始化
redis_cluster = settings.redis_cluster
sync_redis = None

if redis_cluster is not None:
    cluster_nodes = [ClusterNode(node["host"], node["port"])
                     for node in redis_cluster]
    sync_redis = RedisCluster(startup_nodes=cluster_nodes,
                              decode_responses=True,
                              username=settings.redis_username,
                              password=settings.redis_password)
else:
    sync_redis = Redis(host=settings.redis_host,
                       port=settings.redis_port,
                       username=settings.redis_username,
                       password=settings.redis_password,
                       db=settings.redis_db,
                       decode_responses=True)

prefix = 'langbase::'


def set_redis_sync(key: str, value: str, ex: Optional[int] = None) -> bool:
    """同步方式设置 Redis 值"""
    try:
        return bool(sync_redis.set(prefix + key, value, ex=ex))
    except Exception as e:
        print(f"Redis set error: {e}")
        return False


def get_redis_sync(key: str) -> Optional[str]:
    """同步方式获取 Redis 值"""
    try:
        return sync_redis.get(prefix + key)
    except Exception as e:
        print(f"Redis get error: {e}")
        return None


def delete_redis_sync(key: str) -> bool:
    """同步方式删除 Redis 键"""
    try:
        return bool(sync_redis.delete(prefix + key))
    except Exception as e:
        print(f"Redis delete error: {e}")
        return False


def get_sync_redis() -> Redis | RedisCluster:
    """获取同步Redis客户端实例"""
    return sync_redis


ENV_OPERATION_CACHE_KEY = "env_opt"

ACCOUNT_CACHE_KEY = "account_cache"

APP_CACHE_KEY = "app_cache"

ACCOUNT_CACHE_KEY_ID = "account_cache::"

DOCUMENT_TASKS_CACHE_KEY = "document_tasks"

DISABLE_DOCUMENT_TASK_CACHE_KEY = "disable_document_task"

MODEL_DISABLE_KEY = "model_disable"

MODEL_ALL_KEY = "model_all"

MODEL_TEST_KEY = "model_test"

FEE_ALERT_KEY = "fee_alert"

MEDIA_CACHE_KEY = "media_cache"

AIGW_ACCOUNT_MAP_KEY = "aigw_account_map"

GROUPS_CACHE_KEY = "groups"

WORKSPACES_CACHE_KEY = "workspaces"

POPO_DOC_CACHE_KEY = "popo_doc"

ADA_DOC_MAP_KEY = "ada_doc_map"

ALERT_POPO_KEY = "alert_popo"
