from typing import Optional
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from http.client import responses as status
from starlette.exceptions import HTTPException as StarletteHTTPException


class LangbaseException(Exception):
    message: Optional[str] = None
    code: str
    status_code: int

    def __init__(self,
                 status_code: int = 500,
                 message: Optional[str] = None,
                 code: Optional[str] = None):
        self.message = message
        if code is None:
            code = status[status_code]
        self.code = code
        self.status_code = status_code
        super().__init__(f"{self.code}({self.status_code}): {self.message}")


class BadRequest(LangbaseException):
    def __init__(self, message: str):
        super().__init__(400, message, "BadRequest")


class FileTooLong(LangbaseException):
    def __init__(self, message: str):
        super().__init__(413, message, "FileTooLong")


class ParameterInvalid(LangbaseException):
    def __init__(self, message: str):
        super().__init__(400, message, "ParameterInvalid")


class Unauthorized(LangbaseException):
    def __init__(self, message: str):
        super().__init__(401, message, "Unauthorized")


class LoginRequired(LangbaseException):
    def __init__(self, message: str):
        super().__init__(401, message, "LoginRequired")


class Forbidden(LangbaseException):
    def __init__(self, message: str):
        super().__init__(403, message, "Forbidden")


class PermissionDenied(LangbaseException):
    def __init__(self, message: str):
        super().__init__(403, message, "PermissionDenied")


class NotFound(LangbaseException):
    def __init__(self, message: str):
        super().__init__(404, message, "NotFound")


class InternalServerError(LangbaseException):
    def __init__(self, message: str):
        super().__init__(500, message, "InternalServerError")


def init_app(app: FastAPI):
    @app.exception_handler(LangbaseException)
    async def langbase_exception_handler(
            request: Request, exc: LangbaseException):

        content = {
            "code": exc.code,
        }
        if exc.message is not None:
            content["message"] = exc.message
        return JSONResponse(
            status_code=exc.status_code,
            content=content)

    @app.exception_handler(RequestValidationError)
    async def validation_error_handler(request, exc):
        content = {
            "code": "ParameterInvalid",
            "message": str(exc),
        }
        return JSONResponse(
            status_code=400,
            content=content)

    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(request, exc):
        content = {
            "code": status[exc.status_code],
            "message": str(exc.detail),
        }
        return JSONResponse(
            status_code=exc.status_code,
            content=content)

    @app.exception_handler(Exception)
    async def exception_handler(request, exc):
        content = {
            "code": 'Internal Server Error',
            "message": str(exc),
        }
        return JSONResponse(
            status_code=500,
            content=content)
