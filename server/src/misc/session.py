import json
from typing import Generic, Optional, <PERSON>
from pydantic import <PERSON><PERSON>ode<PERSON>, ConfigDict, Field
from uuid import UUID
from fastapi_sessions.backends.session_backend \
    import SessionBackend, SessionModel, BackendError
from fastapi_sessions.backends.implementations.in_memory_backend \
    import InMemoryBackend
from fastapi_sessions.frontends.session_frontend import ID
from fastapi_sessions.frontends.implementations \
    import SessionCookie, CookieParameters
from fastapi_sessions.frontends.session_frontend import FrontendError
from fastapi_sessions.session_verifier import SessionVerifier
from fastapi import HTTPException, Request
from starlette.authentication import BaseUser
from redis.asyncio import Redis
from redis.asyncio.cluster import RedisCluster, ClusterNode

from ..config import settings


class SessionUser(BaseModel, BaseUser):
    model_config = ConfigDict(from_attributes=True)
    id: str
    name: str
    is_admin: bool = Field(validation_alias='is_admin',
                           serialization_alias='isAdmin')
    fullname: str
    email: str

    @property
    def is_authenticated(self) -> bool:
        return True

    @property
    def identity(self) -> str:
        return self.id


redis_cluster = settings.redis_cluster
redis = None

if redis_cluster is not None:
    cluster_nodes = [ClusterNode(node["host"], node["port"])
                     for node in redis_cluster]
    redis = RedisCluster(startup_nodes=cluster_nodes,
                         decode_responses=True,
                         username=settings.redis_username,
                         password=settings.redis_password)  # type: ignore
else:
    redis = Redis(host=settings.redis_host,
                  port=settings.redis_port,
                  username=settings.redis_username,
                  password=settings.redis_password,
                  db=settings.redis_db,
                  decode_responses=True)


async def print_redis_nodes() -> Redis | RedisCluster:
    nodes = await redis.cluster_nodes()
    # 打印每个节点的角色
    for key in nodes:
        node = nodes[key]
        node_id = node['node_id']
        node_addr = key
        node_role = node['flags']
        if 'master' in node_role:
            print(f"Node {node_id} at {node_addr} is a MASTER")
        elif 'slave' in node_role:
            print(f"Node {node_id} at {node_addr} is a SLAVE")
    return redis  # type: ignore


def get_redis() -> Redis | RedisCluster:
    return redis  # type: ignore


async def rm_keys(prefix):
    async for key in redis.scan_iter(f"{prefix}:*"):
        await redis.delete(key)


async def rm_bindings_keys(workspace):
    await rm_keys(f'langbase::bindings::{workspace}')


class RedisBackend(Generic[ID, SessionModel],
                   SessionBackend[ID, SessionModel]):
    """Stores session data in a dictionary."""

    def __init__(self, redis_client: Redis,
                 exprire_in_seconds: Optional[int]) -> None:
        """Initialize a new in-memory database."""
        self.redis_client = redis_client
        self.expire_in_seconds = exprire_in_seconds

    async def create(self, session_id: ID, data: SessionModel):
        """Create a new session entry."""
        serialized_data = data.model_dump_json()
        await self.redis_client.set(str(session_id), serialized_data,
                                    ex=self.expire_in_seconds)

    async def read(self, session_id: ID):
        """Read an existing session data."""
        serialized_data = await self.redis_client.get(str(session_id))
        if serialized_data is None:
            return None
        data: SessionUser = SessionUser.model_validate_json(serialized_data)
        return data

    async def update(self, session_id: ID, data: SessionModel) -> None:
        if await self.redis_client.get(str(session_id)) is None:
            raise BackendError("session does not exist, cannot update")
        serialized_data = data.model_dump_json()
        await self.redis_client.set(str(session_id), serialized_data,
                                    ex=self.expire_in_seconds)

    async def delete(self, session_id: ID) -> None:
        await self.redis_client.delete(str(session_id))


cookie_params = CookieParameters(
    max_age=settings.session_expire_seconds,
)

# Uses UUID
cookie = SessionCookie(
    cookie_name=settings.cookie_key,
    identifier="general_verifier",
    auto_error=True,
    secret_key=settings.cookie_secret,
    cookie_params=cookie_params,
)


backend = RedisBackend[UUID, SessionUser](
    redis_client=redis,
    exprire_in_seconds=settings.session_expire_seconds)

if settings.testing:
    backend = InMemoryBackend[UUID, SessionUser]()


class BasicVerifier(SessionVerifier[UUID, SessionUser]):
    def __init__(
        self,
        *,
        identifier: str,
        auto_error: bool,
        backend: SessionBackend[UUID, SessionUser],
        auth_http_exception: HTTPException,
    ):
        self._identifier = identifier
        self._auto_error = auto_error
        self._backend = backend
        self._auth_http_exception = auth_http_exception

    @property
    def identifier(self):
        return self._identifier

    @property
    def backend(self):
        return self._backend

    @property
    def auto_error(self):
        return self._auto_error

    @property
    def auth_http_exception(self):
        return self._auth_http_exception

    def verify_session(self, model: SessionUser) -> bool:
        """If the session exists, it is valid"""
        return True


verifier = BasicVerifier(
    identifier="general_verifier",
    auto_error=True,
    backend=backend,
    auth_http_exception=HTTPException(
        status_code=403, detail="invalid session"),
)


async def whoami(request: Request):
    try:
        my_list = ["/api/v1/agentw-chat", "/api/v1/chat", "/api/v1/app/trigger", "/api/v1/conversation",
                   "/api/v1/completion", "/api/v1/app/workflow-runs", "/api/v1/app", "/api/v1/message"]
        if request.url.path in my_list:
            return None
        session_id: Union[UUID, FrontendError] = cookie(request)
        if isinstance(session_id, FrontendError):
            raise session_id
        user = await backend.read(session_id)
        return user
    except HTTPException:
        return None

# async def create_session(user, response: Response):

#     session = uuid4()
#     data = SessionUser(username=name)

#     await backend.create(session, data)
#     cookie.attach_to_response(response, session)

#     return f"created session for {name}"

# async def whoami(session_data: SessionUser = Depends(verifier)):
#     return session_data


# async def del_session(response: Response, session_id: UUID = Depends(cookie))
#     await backend.delete(session_id)
#     cookie.delete_from_response(response)
#     return "deleted session"
