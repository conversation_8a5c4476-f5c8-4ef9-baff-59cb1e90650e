from typing import Any, Dict, Optional
import requests


class HTTPClient:
    headers: Optional[dict[str, str]] = None

    def __init__(self, headers: Optional[dict[str, str]] = None) -> None:
        self.headers = headers

    def get(self, url: str, **kwargs: Any) -> requests.Response:
        return requests.get(url, headers=self.headers, **kwargs)

    def post(self, url: str, data: Dict[str, Any], **kwargs: Any) -> requests.Response:
        return requests.post(url, json=data, headers=self.headers, **kwargs)

    def patch(self, url: str, data: Dict[str, Any], **kwargs: Any) -> requests.Response:
        return requests.patch(url, json=data, headers=self.headers, **kwargs)

    def put(self, url: str, data: Dict[str, Any], **kwargs: Any) -> requests.Response:
        return requests.put(url, json=data, headers=self.headers, **kwargs)

    def delete(self, url: str, **kwargs: Any) -> requests.Response:
        return requests.delete(url, headers=self.headers, **kwargs)
