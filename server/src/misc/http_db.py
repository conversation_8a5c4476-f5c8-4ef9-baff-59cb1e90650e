import httpx
from ..service.http_session import HTTPSession
from ..config import settings
from typing import Dict, Optional
from urllib.parse import urljoin


def get_http_db(table_name: Optional[str] = None):
    return HTTPSession(settings.db_service_url, table_name=table_name)


def get_http_db_client():
    return HttpClient(settings.db_service_url)


class HttpClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.client = httpx.Client()

    def _build_url(self, path: str) -> str:
        # 确保路径以 / 开头
        if not path.startswith('/'):
            path = f'/{path}'
        return urljoin(self.base_url, path)

    def get(self, path: str, params: Optional[Dict] = None) -> httpx.Response:
        url = self._build_url(path)
        return self.client.get(url, params=params)

    def post(self, path: str, json: Optional[Dict] = None) -> httpx.Response:
        url = self._build_url(path)
        return self.client.post(url, json=json)

    def put(self, path: str, json: Optional[Dict] = None) -> httpx.Response:
        url = self._build_url(path)
        return self.client.put(url, json=json)

    def delete(self, path: str) -> httpx.Response:
        url = self._build_url(path)
        return self.client.delete(url)

    def close(self):
        self.client.close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
