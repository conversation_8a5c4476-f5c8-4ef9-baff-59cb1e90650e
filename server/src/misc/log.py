import os
import sys
from loguru import logger
from multiprocessing import current_process
import logging
from ..config import settings

log_path = os.getenv("MUSIC_CICD_CLUSTER", 'langbase-server')

logger.remove()
pid = current_process().pid
log_obj = 'service' if settings.is_service else 'server'

format_info = '{time:YYYY-MM-DD HH:mm:ss,SSS}[v3][langbase-server][' + log_path + '][' + str(pid) + ']' \
    '[traceId][spanId][{extra[uid]}][{name}:{function}:{line}][INFO][' + log_obj + '] - ' \
    '{message}'
format_error = '{time:YYYY-MM-DD HH:mm:ss,SSS}[v3][langbase-server][' + log_path + '][' + str(pid) + ']' \
    '[traceId][spanId][{extra[uid]}][{name}:{function}:{line}][ERROR][' + log_obj + '] - ' \
    '{message}'


def init_app():
    loglevel = "DEBUG" if settings.debug else "WARNING"
    # DEBUG直接输出到控制台
    logger.add(sys.stderr, format="{time} {level} {message}",
               enqueue=True, serialize=True, level=loglevel)
    # 其他日志输出到日志文件
    if settings.use_mlog:
        logger.add(f"/home/<USER>/music/langbase-server/{log_path}/approot/logs/mlog/{log_path}.log",
                   level="INFO", rotation="100 MB", enqueue=True, format=format_info)
        logger.add(f"/home/<USER>/music/langbase-server/{log_path}/approot/logs/mlog/{log_path}-error.log",
                   level="ERROR", rotation="100 MB", enqueue=True, format=format_error)
    # 本地测试用
    else:
        logger.add('./logs/mlog/langbase-server.log',
                   level="INFO", rotation="100 MB", enqueue=True, format=format_info)
        logger.add('./logs/mlog/langbase-server-error.log',
                   level="ERROR", rotation="100 MB", enqueue=True, format=format_error)

    logging.basicConfig()
    logging.getLogger('sqlalchemy.engine').setLevel(
        logging.INFO if settings.debug else logging.WARNING)


# 增加UID的默认值
def logger_info(msg, uid='uid'):
    logger.info(msg, uid=uid)


# 增加UID的默认值
def logger_error(*msg):
    logger.error(*msg, uid='uid')
