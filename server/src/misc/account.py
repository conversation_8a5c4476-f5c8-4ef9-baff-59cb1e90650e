import json

from ..utils.model_proxy import BaseModelProxy

from ..models.account import Account
from sqlalchemy.orm import Session
from .redis_utils import ACCOUNT_CACHE_KEY_ID, get_redis_sync, set_redis_sync


def get_or_set_account(target_id, context: Session):
    account = get_redis_sync(ACCOUNT_CACHE_KEY_ID + str(target_id))
    if account:
        return BaseModelProxy(json.loads(account))
    else:
        account = context.query(Account) \
            .filter(Account.id == target_id).first()
        account_dict = {}
        for c in account.__table__.columns:
            if not c.name.startswith('_'):
                value = getattr(account, c.name)
                if hasattr(value, 'isoformat'):
                    value = value.isoformat()
                account_dict[c.name] = value
        set_redis_sync(ACCOUNT_CACHE_KEY_ID + str(target_id),
                       json.dumps(account_dict), 4 * 60 * 60)
        return account
