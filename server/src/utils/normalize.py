import json
from typing import List, Union
from enum import Enum

from ..misc.redis_utils import ACCOUNT_CACHE_KEY, get_redis_sync


def fetch_created_by(created_by_ids: List[str]):
    account_cache = get_redis_sync(ACCOUNT_CACHE_KEY)
    if account_cache:
        account_cache = json.loads(account_cache)
        return {
            id: account_cache[id] if len(id) == 36 and id in account_cache
            else {'name': id, 'email': id, 'fullname': id, 'id': id} if len(id) > 0
            else None
            for id in created_by_ids
        }
    return {
        id: {'name': id, 'email': id, 'fullname': id,
             'id': id} if len(id) > 0 else None
        for id in created_by_ids
    }


def _parse_json_field(value: str) -> dict | None:
    """解析JSON字符串字段"""
    return json.loads(value) if value != "" else None


def normalize_fields(id_field: str, items: Union[dict, List[dict]]) -> Union[dict, List[dict]]:
    """统一处理字段的命名和格式化"""
    if isinstance(items, dict):
        items = [items]
        is_single = True
    else:
        is_single = False

    # 处理 id 字段
    for item in items:
        model_id_key = id_field
        if model_id_key in item:
            item['id'] = item.pop(model_id_key)

    # 字段映射关系
    field_mappings = {
        'app_types': ['app_types', 'appTypes']  # 字段名: [原始字段名, 映射后的字段名]
        # 可以添加更多映射关系
    }

    # 需要JSON解析的字段列表
    json_fields = ['config', 'components', 'extInfo',
                   'app_types', 'parameters', 'override_config', 'fee', 'tag']

    # 处理需要JSON解析的字段
    for item in items:
        for field in json_fields:
            # 检查是否存在映射关系
            field_names = field_mappings.get(field, [field])

            # 处理原始字段
            if field in item and isinstance(item[field], str):
                parsed_value = _parse_json_field(item[field])
                item[field] = parsed_value

                # 如果有映射字段，同步更新映射字段的值
                for mapped_field in field_names[1:]:
                    item[mapped_field] = parsed_value

    # 处理 created_by 和 updated_by 字段
    created_by_ids = [item['created_by']
                      for item in items if 'created_by' in item]
    updated_by_ids = [item['updated_by']
                      for item in items if 'updated_by' in item]

    all_user_ids = list(set(created_by_ids + updated_by_ids))
    if all_user_ids:
        user_accounts = fetch_created_by(all_user_ids)
        for item in items:
            if 'created_by' in item and item['created_by'] in user_accounts:
                item['createdBy'] = user_accounts[item['created_by']]
            if 'updated_by' in item and item['updated_by'] in user_accounts:
                item['updatedBy'] = user_accounts[item['updated_by']]

    return items[0] if is_single else items


def normalize_table_name(table_name: str) -> tuple[str, str]:
    """统一处理表名的映射关系
    Returns:
        tuple: (normalized_name, id_field)
        - normalized_name: 标准化后的表名
        - id_field: 该表的id字段名
    """
    # 特殊表名映射 (表名, id字段名)
    special_mappings = {
        'tb_versioned_app_config': ('appConfig', 'app_config_id'),
        'tb_versioned_app': ('app', 'app_id'),
        'tb_setting': ('setting', 'settingId'),
        'tb_model_provider_binding': ('modelProviderBinding', 'binding_id'),
        'tb_message_tool_call_response': ('toolCallResp', 'tool_call_response_id'),
        'tb_llm_model': ('model', 'modelId'),
        # 可以添加更多特殊映射
    }

    # 优先使用特殊映射
    if table_name in special_mappings:
        return special_mappings[table_name]

    # 默认处理：移除 tb_ 前缀，id字段为 表名_id
    normalized_name = table_name.replace('tb_', '')
    return normalized_name, f"{normalized_name}_id"


def extract_value(value, enable_list=False):
    if value is None:
        return None
    # 处理基础类型
    if isinstance(value, Enum):  # 直接判断是否为枚举类
        return extract_value(value.value, enable_list)
    if isinstance(value, dict):
        return json.dumps(value)
    if isinstance(value, bool):
        return 1 if value else 0
    if isinstance(value, (int, float, str, bool)):
        return value

    """提取各种类型的值"""
    if isinstance(value, list):
        arr = [extract_value(item, enable_list) for item in value]
        if enable_list:
            return arr
        return json.dumps(arr)

    if value.__class__.__name__ in ('True_', 'False_'):
        return value.__class__.__name__ == 'True_'
    if value.__class__.__name__ == 'Null':
        return 'null'
    elif hasattr(value, 'value'):  # 处理枚举类
        return extract_value(value.value, enable_list)
    elif isinstance(value, Enum):  # 直接判断是否为枚举类
        return extract_value(value.value, enable_list)
    else:
        return extract_value(getattr(value, 'value', value), enable_list)
