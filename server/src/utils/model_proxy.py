class BaseModelProxy:
    """基础模型代理类，提供基本的数据存取功能"""

    def __init__(self, data: dict):
        self._data = data

        # 设置初始数据的属性
        for key, value in data.items():
            super().__setattr__(key, value)

    def __getattribute__(self, name):
        # 优先处理特殊属性和内部属性
        try:
            return super().__getattribute__(name)
        except AttributeError:
            if name in self._data:
                return self._data.get(name)

    def __setattr__(self, name, value):
        if name.startswith('_'):
            super().__setattr__(name, value)
            return
        self._data[name] = value
        super().__setattr__(name, value)

    def model_dump(self):
        """Return the underlying data dictionary."""
        return self._data

    def dict(self):
        """Alias for model_dump for compatibility."""
        return self.model_dump()

    def __str__(self):
        return str(self._data)

    def __repr__(self):
        return f"{self.__class__.__name__}({self._data})"
