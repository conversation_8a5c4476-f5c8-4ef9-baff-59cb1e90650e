from typing import Dict

# JSON 生成任务
json_scenarios = {
    "json_task_en": {
        "role": "user",
        "content": """Generate a JSON for a coffee shop order with these items:
1. Latte (Large) - $4.50 x 2
2. Croissant - $3.00 x 3
3. Blueberry Muffin - $2.50 x 1

Customer: <PERSON> Chen
Phone: 555-0123
Note: Extra hot latte, no sugar

Format as JSON with subtotal, tax (8%), and total."""
    },
    "json_task_zh": {
        "role": "user",
        "content": """生成一个奶茶店订单的JSON，包含以下商品：
1. 珍珠奶茶（大杯）- ¥18 x 2
2. 椰果奶绿 - ¥16 x 1
3. 蛋糕卷 - ¥12 x 2

顾客：张小明
电话：138-1234-5678
备注：奶茶少糖，多珍珠

请输出JSON格式，包含小计、税费(6%)和总价。"""
    }
}

# 创意写作任务
creative_scenarios = {
    "creative_text_en": {
        "role": "user",
        "content": "Write a creative story about an ancient dragon in 300 words. Include physical description, magical abilities, an encounter with humans, and a moral lesson. Use vivid imagery and clear story structure."
    },
    "creative_text_zh": {
        "role": "user",
        "content": "用300字写一个关于远古神龙的创意故事。必须包含：龙的外形描写、神奇能力、与人类的相遇经历，以及一个寓意。要求使用生动的意象和比喻，故事需要有清晰的开端、发展和结尾结构。"
    }
}

# 常规任务
task_scenarios = {
    "regular_task_en": {
        "role": "user",
        "content": "Provide a detailed 300-word weather report analysis including: current temperature, humidity, wind conditions, precipitation forecast for the next 24 hours, and how these conditions compare to seasonal averages. Include specific recommendations for outdoor activities."
    },
    "regular_task_zh": {
        "role": "user",
        "content": "请提供一份300字的详细天气报告分析，包括：当前气温、湿度、风况、未来24小时降水预测，以及这些条件与季节平均值的比较。根据这些天气条件，给出具体的户外活动建议。"
    }
}

# 合并所有场景
test_scenarios: Dict[str, Dict] = {
    "json": json_scenarios["json_task_zh"],
    "creative": creative_scenarios["creative_text_zh"],
    "task": task_scenarios["regular_task_zh"]
}
