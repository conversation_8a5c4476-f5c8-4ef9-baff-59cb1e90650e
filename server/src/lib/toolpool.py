from typing import Any, Dict, List, Optional, Tuple
from loguru import logger
from sqlalchemy.orm import Session

from pydantic import ValidationError
import httpx

from ..service.component import get as get_component_service, \
    list_components_v2 as list_component_service \

from ..service.app import list_app_component_env_variable \
    as list_app_component_env_variable_service

from ..schema.common import Scope

from .base.singleton import Singleton

from ..schema.component import ComponentAppType, ComponentConfig, \
    ComponentConfigHTTPMethod, ComponentConfigHTTPTimeout, ComponentConfigHTTPURL

from ..models.component import Component

from ..schema.plugin import Plugin

from ..misc.errors import NotFound


class ToolPool(metaclass=Singleton):

    @classmethod
    def gen_tool_from_component(cls, component: Component, component_config: ComponentConfig):
        config = cls.component_config_to_json_schema(component_config)

        return Plugin(
            workspace_id=str(
                component.workspace_id) if str(component.scope) == Scope.SCOPED.value else None,
            id=component.id,
            name=str(component.name),
            description=component_config.description_for_llm
            if component_config.description_for_llm is not None
            else component.description,  # type: ignore
            parameters=config,
        )

    @classmethod
    def component_config_to_json_schema(cls, config: ComponentConfig) -> dict:
        params_with_default = {}
        params = {}
        if config.vars is not None:
            params_with_default = {
                var.name: {
                    "type": var.type,
                    "title": var.title,
                    "enum": var.enum,
                    "default": var.value,
                } for var in config.vars
            }

        if config.inputs is not None:
            for input in config.inputs:
                params[input.name] = {
                    "title": input.title,
                    "type": input.type,
                }
                if input.description is not None:
                    params[input.name]["description"] = input.description

        return {
            "type": "object",
            "properties": {
                **params_with_default,
                **params,
            },
            "required": [
                name for name in params.keys()
            ],
        }

    async def list_v2(
            self,
            db: Session,
            ids: Optional[List[str]] = None,
            workspace_id: Optional[str] = None,
            page_size: Optional[int] = 10,
            page_number: Optional[int] = 0,
    ) -> Tuple[List[Plugin], int]:
        components, count = await list_component_service(
            db=db,
            ids=ids,
            app_types=[ComponentAppType.AGENT.value],
            workspace_id=workspace_id,
            page_size=page_size,
            page=page_number)

        plugins: List[Plugin] = []
        for component in components:
            try:
                component_config = ComponentConfig.model_validate(
                    component.config)
                plugins.append(self.gen_tool_from_component(
                    component, component_config))
            except ValidationError as e:
                logger.bind(config=component.config).error(
                    f"component({component.id}) config error: {e}", uid="uid")
                raise e
        return plugins, count

    async def list(
            self,
            db: Session,
            ids: Optional[List[str]] = None,
            workspace_id: Optional[str] = None,
            limit: Optional[int] = 10,
            offset: Optional[int] = 0,
    ) -> Tuple[List[Plugin], int]:
        components, count = await list_component_service(
            db=db,
            ids=ids,
            app_types=[ComponentAppType.AGENT.value],
            workspace_id=workspace_id,
            limit=limit,
            offset=offset)

        plugins: List[Plugin] = []
        for component in components:
            try:
                component_config = ComponentConfig.model_validate(
                    component.config)
                plugins.append(self.gen_tool_from_component(
                    component, component_config))
            except ValidationError as e:
                logger.bind(config=component.config).error(
                    f"component({component.id}) config error: {e}")

        return plugins, count

    async def get(self, db: Session, tool_id: str):
        component = await get_component_service(
            db=db,
            component_id=tool_id,
        )
        component_config = ComponentConfig.model_validate(component.config)
        tool = self.gen_tool_from_component(component, component_config)

        return tool, component, component_config

    async def run(self, db: Session, app_id: str, tool_call_dict: Dict[str, Tuple[str, dict]]):
        results: Dict[str, Any | str] = {}
        for tool_call_id, (tool_id, params) in tool_call_dict.items():
            try:
                result = await self.run_tool(db, app_id, tool_id, params)
            except Exception as e:
                result = str(e)
            results[tool_call_id] = result
        return results

    async def run_tool(self, db: Session, app_id: str, tool_id: str, params: dict):
        _, component, config = await self.get(db, tool_id)
        if component is None or config is None:
            raise NotFound(f"tool({tool_id}) not found")
        return await self.run_component(db, app_id, component, config, params)

    async def run_component(self,
                            db: Session,
                            app_id: str,
                            component: Component,
                            config: ComponentConfig,
                            params: dict):
        url: Optional[str] = None
        method: str = "GET"
        timeout = 20  # seconds
        final_params = {}

        for c in config.configs:
            if isinstance(c, ComponentConfigHTTPURL):
                url = c.value
                continue
            elif isinstance(c, ComponentConfigHTTPMethod):
                method = c.value
                continue
            elif isinstance(c, ComponentConfigHTTPTimeout):
                t = int(c.value) / 1000
                if t < timeout:
                    timeout = t
            else:
                # handle environment variable
                pass

        if url is None:
            raise Exception(
                "url is tools defination is None, please check your tools defination")
        if config.vars:
            for var in config.vars:
                final_params[var.name] = var.value

        for key, value in params.items():
            final_params[key] = value

        envs, _ = await list_app_component_env_variable_service(
            db=db,
            app_id=app_id,
        )

        for env in envs:
            if env.value is None:
                logger.bind(app_id=app_id).warning(
                    f"env({env.name}) value is None")
                continue
            final_params[env.name] = env.value

        async with httpx.AsyncClient() as client:
            if method == 'GET':
                resp = await client.request(method, url, params=final_params, timeout=timeout)
            else:
                resp = await client.request(method, url, json=final_params, timeout=timeout)

        status = resp.status_code
        if status >= 400:
            return f"request failed, status code: {status}, message: {resp.text}"

        content_type = resp.headers.pop("Content-Type", None)
        if content_type == "application/json":
            return resp.json()

        return resp.text


__tool_pool = ToolPool()


async def get_tool_pool() -> ToolPool:
    # await __tool_pool.init()
    return __tool_pool


# @tool_added.connect
# async def on_tool_added(sender: ComponentAddedEvent, **kwargs: dict):
#     component = sender.created_conponent

#     if ComponentAppType.AGENT in component.app_types:
#         pool = await get_tool_pool()
#         config = ComponentConfig.model_validate(component.config)
#         await pool.add(pool.gen_tool_from_component(
#             component, config), component, config)


# @tool_updated.connect
# async def on_tool_updated(sender: ComponentUpdatedEvent, **kwargs: dict):
#     pool = await get_tool_pool()
#     component = sender.updated_component
#     config = ComponentConfig.model_validate(component.config)
#     await pool.delete(component.id)

#     if ComponentAppType.AGENT in component.app_types:
#         await pool.add(pool.gen_tool_from_component(
#             component, config), component, config)


# @tool_deleted.connect
# async def on_tool_deleted(sender: ComponentDeletedEvent, **kwargs: dict):
#     pool = await get_tool_pool()
#     await pool.delete(sender.component_id)
