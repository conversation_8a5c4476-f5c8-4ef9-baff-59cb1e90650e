from asyncinit import asyncinit
from typing import List, Optional, Literal
from sqlalchemy.orm import Session

from ...service.member import list as list_members


from ...misc.session import get_redis

from ...misc.errors import BadRequest

# from .wenxin import WenXin
from ...service.app import get as get_app
from ...schema.plugin import Plugin

from ...config import settings

from ...schema.common import ResourceType

from ...service.model import \
    getBinding as getBindingService

from .base import Interlocutor, Providers, Validator, Imager, Speecher
from .openai import OpenAI
from ...schema.dialog import LLMMessage, ResponseMode


def isOpenAILikeProvider(kind):
    # 是否为 OpenAI 兼容提供商
    provider = next((x for x in Providers if x.value.kind == kind), None)
    if provider and provider.value.openai_like:
        return True
    return False


def getOpenAILikeAPIKey(kind):
    # 获取 OpenAI 兼容提供商的 api_key
    for provider in Providers:
        if provider.value.kind == kind:
            return provider.value.api_key
    return Providers.OpenAI.value.api_key


def getOpenAILikeAPIEndpoint(kind):
    # 获取 OpenAI 兼容提供商的 endpoint
    for provider in Providers:
        if provider.value.kind == kind:
            return provider.value.endpoint
    return Providers.OpenAI.value.endpoint


class Executor(Speecher, Imager, Interlocutor, Validator):
    async def chat(self,
                   model_name: str,
                   provider_kind: str,
                   model_params: Optional[dict],
                   response_mode: ResponseMode,
                   user_id: Optional[str],
                   messages: List[LLMMessage],
                   plugins: Optional[List[Plugin]] = None,
                   api_key: Optional[str] = None,
                   api_app_id: Optional[str] = None,
                   api_app_key: Optional[str] = None,
                   api_base: Optional[str] = None):

        # Moonshot 可使用 OpenAI 相同的 Client
        if isOpenAILikeProvider(provider_kind):
            if api_key is None:
                api_key = getOpenAILikeAPIKey(provider_kind)
            if api_base is None:
                api_base = getOpenAILikeAPIEndpoint(provider_kind)
            response = await OpenAI().chat(
                model_name,
                provider_kind,
                model_params,
                response_mode,
                user_id,
                messages,
                plugins=plugins,
                api_key=api_key,
                api_base=api_base)
            return response
        raise Exception('该模型不是openAI兼容模型，暂不支持解析，请联系管理员')

        # if provider_kind == Providers.WenXin.value.kind:
        #     if api_app_id is None:
        #         api_app_id = Providers.WenXin.value.api_app_id
        #     if api_app_key is None:
        #         api_app_key = Providers.WenXin.value.api_app_key
        #     if api_base is None:
        #         api_base = Providers.WenXin.value.endpoint

        #     response = await WenXin().chat(
        #         model_name,
        #         provider_kind,
        #         model_params,
        #         response_mode,
        #         user_id,
        #         messages,
        #         api_app_id=api_app_id,
        #         api_app_key=api_app_key,
        #         api_base=api_base
        #     )
        #     return response
        # raise Exception('unknown response type')

    async def speech(self,
                     model_name: str,
                     provider_kind: str,
                     inputs: str,
                     voice: str,
                     response_format: str,
                     speed: float,
                     api_key: Optional[str] = None,
                     api_base: Optional[str] = None) -> str:
        if provider_kind == Providers.OpenAI.value.kind:
            if api_key is None:
                api_key = Providers.OpenAI.value.api_key
            if api_base is None:
                api_base = Providers.OpenAI.value.endpoint
            key = await OpenAI().speech(model_name=model_name,
                                        provider_kind=provider_kind,
                                        inputs=inputs,
                                        voice=voice,
                                        response_format=response_format,
                                        speed=speed,
                                        api_key=api_key,
                                        api_base=api_base)
            return key
        raise Exception(f'unknown provider_kind type: {provider_kind}')

    async def validate_config(self, provider_kind: str, config: dict):
        if provider_kind == Providers.OpenAI.value.kind:
            await OpenAI().validate_config(provider_kind, config)
        # if provider_kind == Providers.WenXin.value.kind:
        #     await WenXin().validate_config(provider_kind, config)

    async def image_generate(self,
                             prompt: str,
                             provider_kind: str,
                             model_name: Literal["dall-e-3"],
                             n: Optional[int] = None,
                             quality: Optional[Literal["standard",
                                                       "hd"]] = None,
                             response_format: Optional[Literal["url",
                                                               "b64_json"]] = None,
                             size: Optional[
                                 Literal["1024x1024", "1792x1024", "1024x1792"]] = None,
                             style: Optional[Literal["vivid",
                                                     "natural"]] = None,
                             api_key: Optional[str] = None,
                             api_base: Optional[str] = None):
        if provider_kind == Providers.OpenAI.value.kind:
            if api_key is None:
                api_key = Providers.OpenAI.value.api_key
            if api_base is None:
                api_base = Providers.OpenAI.value.endpoint

            response = await OpenAI().image_generate(
                prompt=prompt,
                model_name=model_name,
                provider_kind=provider_kind,
                n=n,
                quality=quality,
                response_format=response_format,
                size=size,
                style=style,
                api_key=api_key,
                api_base=api_base)
            return response

        raise Exception('unknown response type')


executor = Executor()


@asyncinit
class DomainLLMRunner(Imager, Interlocutor):

    async def __init__(self,
                       db: Session,
                       workspace_id: Optional[str] = None,
                       group_id: Optional[str] = None,
                       user_id: Optional[str] = None,
                       app_id: Optional[str] = None):
        self.redis_client = get_redis()
        if user_id is not None:
            self.user_id = user_id
        if app_id is not None:
            self.app_id = app_id
            await self.setBindingByApp(app_id, db)
        elif workspace_id is not None and group_id is not None:
            self.workspace_id = workspace_id
            self.group_id = group_id
            self.db = db
            await self.setBinding(workspace_id, group_id, db)

    async def setBinding(self,
                         workspace_id: str,
                         group_id: str,
                         db: Session):
        bindings = await getBindingService(workspace_id, group_id, db)
        self.bindings = bindings

    async def setBindingByApp(self, app_id: str, db: Session):
        app = await get_app(db, app_id)
        workspace_id = app.workspace_id
        group_id = app.group_id
        self.workspace_id = workspace_id
        self.group_id = group_id
        self.db = db
        await self.setBinding(str(workspace_id), str(group_id), db)

    async def chat(self,
                   model_name: str,
                   provider_kind: str,
                   model_params: Optional[dict],
                   response_mode: ResponseMode,
                   user_id: Optional[str],
                   messages: List[LLMMessage],
                   plugins: Optional[List[Plugin]] = None,
                   api_key: Optional[str] = None,
                   api_base: Optional[str] = None,
                   api_app_id: Optional[str] = None,
                   api_app_key: Optional[str] = None):
        # 找到默认值
        provider = next((x for x in Providers if x.value.kind ==
                        provider_kind), Providers.OpenAI)
        if len(self.bindings) > 0:
            targetKindBindings = [binding for binding in self.bindings if str(
                binding.kind) == provider_kind]
            binding = targetKindBindings[0] if len(
                targetKindBindings) > 0 else None
            if binding and binding.api_key is not None \
                    and (api_key is None or not api_key):
                api_key = str(binding.api_key)
            if binding and binding.endpoint is not None \
                    and (api_base is None or not api_base):
                api_base = str(binding.endpoint) or provider.value.endpoint
        # 禁止使用默认的API_KEY，目前暂时放开openAI的限制，但是如果不配置api_key，只允许使用gpt3
        if api_key is None and provider.name == 'OpenAI':
            api_key = provider.value.api_key
        if api_key is None:
            if provider_kind == 'tmax' or provider_kind == 'aigc':
                group_members, count = await list_members(
                    db=self.db,
                    resource_type=ResourceType.GROUP.value,
                    resource_id=settings.tmax_group_id)
                # 如果user_id在group_members中，则使用user_id的api_key
                if user_id is None:
                    raise BadRequest("私有部署模型请联系管理员进行绑定")
                user_member = next(
                    (x for x in group_members if x.member_id == user_id), None)
                if user_member:
                    api_key = provider.value.api_key
                else:
                    raise BadRequest("私有部署模型请联系管理员进行绑定使用")
            else:
                raise BadRequest(f"请绑定{provider_kind}的API_KEY")
        return await executor.chat(
            model_name=model_name,
            provider_kind=provider_kind,
            model_params=model_params,
            response_mode=response_mode,
            user_id=user_id,
            messages=messages,
            plugins=plugins,
            api_key=api_key,
            api_base=api_base)

    async def generate_name_for_query(
            self,
            query: str,
            provider_kind: str,
            model_name: str,
            user_id: Optional[str] = None):
        prompt = settings.conversation_title_prompt

        if len(query) > settings.conversation_title_query_length:
            query = query[:settings.conversation_title_query_length // 2] + \
                '...' + query[-settings.conversation_title_query_length // 2:]

        prompt = prompt.format(query=query)

        resp = await self.chat(
            model_name='gpt-4o-mini-2024-07-18',
            provider_kind='openai',
            model_params={
                "max_tokens": 30,
            },
            response_mode=ResponseMode.JSON,
            messages=[
                LLMMessage(role='system', content=prompt),
            ],
            user_id=user_id,
        )

        return resp

    async def image_generate(self,
                             prompt: str,
                             model_name: Literal["dall-e-3"],
                             provider_kind: str,
                             n: Optional[int] = None,
                             response_format: Literal["url",
                                                      "b64_json"] = "url",
                             quality: Optional[Literal["standard",
                                                       "hd"]] = None,
                             size: Optional[
                                 Literal["1024x1024", "1792x1024", "1024x1792"]] = None,
                             style: Optional[Literal["vivid",
                                                     "natural"]] = None,
                             api_key: Optional[str] = None,
                             api_base: Optional[str] = None):
        # 找到默认值
        provider = next((x for x in Providers if x.value.kind ==
                        provider_kind), {'value': {'endpoint': ''}})
        if len(self.bindings) > 0:
            targetKindBindings = [binding for binding in self.bindings if str(
                binding.kind) == provider_kind]
            binding = targetKindBindings[0]
            if binding and binding.api_key is not None \
                    and (api_key is None or not api_key):
                api_key = str(binding.api_key)
            if binding and binding.endpoint is not None \
                    and (api_base is None or not api_base):
                api_base = str(binding.endpoint) or provider.value.endpoint
        # 禁止使用默认的API_KEY，目前暂时放开openAI的限制，但是如果不配置api_key，只允许使用gpt3
        if api_key is None and provider.name == 'OpenAI':
            api_key = provider.value.api_key
        if api_key is None:
            if provider_kind == 'tmax' or provider_kind == 'aigc':
                raise BadRequest("私有部署模型请联系管理员进行绑定使用")
            else:
                raise BadRequest(f"请绑定{provider_kind}的API_KEY")
        return await executor.image_generate(
            prompt=prompt,
            model_name=model_name,
            provider_kind=provider_kind,
            n=n,
            quality=quality,
            response_format=response_format,
            size=size,
            style=style,
            api_key=api_key,
            api_base=api_base)
