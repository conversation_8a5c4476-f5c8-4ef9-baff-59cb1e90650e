from enum import Enum
from io import BytesIO
import json
from typing import AsyncGenerator, List, Literal, Optional, Union
from uuid import uuid4

from typing_extensions import override
from loguru import logger
from pydantic import BaseModel, ConfigDict, Field
from time import time
from openai import AsyncOpenAI, AsyncStream
from openai.types.chat import Chat<PERSON><PERSON>ple<PERSON>, ChatCompletionToolParam, \
    ChatCompletionChunk
from openai._types import NOT_GIVEN
from ...service.s3 import async_upload_file as async_app_upload_file_service

from ...config import settings
from ...schema.plugin import Plugin

from ...misc.errors import BadRequest, InternalServerError

from ...schema.dialog import ResponseMode

from .base import Interlocutor, Validator, Imager, Speecher
from ...schema.dialog import LLMMessage, Response as LLMResponse, \
    ResponseChunk as LLMResponseChunk


class FinishReason(str, Enum):
    STOP = "stop"
    LENGTH = "length"
    CONTENT_FILTER = "content_filter"
    FUNCTION_CALL = "function_call"
    TOOL_CALLS = "tool_calls"


class Function(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    description: Optional[str] = Field(default=None)
    parameters: dict


class Tool(BaseModel):  # for request
    model_config = ConfigDict(from_attributes=True)

    type: Literal['function'] = Field(default='function')

    function: Function


class FunctionCall(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    arguments: str


class ToolCall(BaseModel):  # for response
    model_config = ConfigDict(from_attributes=True)

    id: str
    index: int
    type: Literal['function'] = Field(default='function')

    function: FunctionCall


class FunctionCallChunk(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: Optional[str] = ''
    arguments: str


class ToolCallChunk(BaseModel):  # for response
    model_config = ConfigDict(from_attributes=True)

    id: Optional[str] = None
    type: Literal['function'] = Field(default='function')

    function: FunctionCallChunk


class MessageChoice(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    content: Optional[str] = Field(default=None)
    role: Optional[str] = Field(default=None)
    function_call: Optional[FunctionCall] = Field(default=None)
    tool_calls: Optional[List[ToolCall]] = Field(default=None)

    finish_reason: Optional[FinishReason] = Field(default=None)


class MessageDelta(BaseModel):
    content: Optional[str] = Field(default=None)
    tool_calls: Optional[List[ToolCallChunk]] = Field(default=None)
    role: Optional[str] = None


class Choice(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    index: int
    message: MessageChoice
    finish_reason: FinishReason


class ChoiceChunk(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    finish_reason: Optional[FinishReason] = Field(default=None)
    index: int
    delta: MessageDelta


class Usage(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ResponseObj(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    object: str
    created: int
    model: str
    choices: List[Choice]
    usage: Usage


class ResponseChunk(BaseModel):
    id: str
    object: str
    created: int
    model: str
    choices: List[ChoiceChunk]
    system_fingerprint: Optional[str] = Field(default=None)
    usage: Optional[Usage] = Field(default=None)


class ModelParams(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    top_p: Optional[float] = Field(None, ge=0.0, le=1.0)
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)
    max_tokens: Optional[int] = Field(None, ge=1)
    presence_penalty: Optional[float] = Field(None, ge=-2.0, le=2.0)
    frequency_penalty: Optional[float] = Field(None, ge=-2.0, le=2.0)


def getMoonShootStreamUsage(chunk):
    if len(chunk.choices) > 0 and chunk.choices[0].usage is not None:
        return chunk.choices[0].usage
    return {
        'total_tokens': 0, 'completion_tokens': 0, 'prompt_tokens': 0
    }


class OpenAI(Speecher, Imager, Interlocutor, Validator):

    def __init__(self,
                 api_key: Optional[str] = None,
                 api_base: Optional[str] = None):
        self.api_key = api_key
        self.api_base = api_base

    async def gen_tools(
            self,
            plugins: Optional[List[Plugin]],
    ):
        tools: List[ChatCompletionToolParam] = []
        if plugins is None or len(plugins) == 0:
            return NOT_GIVEN

        for plugin in plugins:
            tools.append(ChatCompletionToolParam({
                "function": {
                    "name": plugin.id,
                    "description": plugin.description if plugin.description else "",
                    "parameters": plugin.parameters,
                },
                "type": "function",
            }))

        return tools

    @classmethod
    async def get_generator(cls, generator: AsyncGenerator):
        resp: Optional[LLMResponseChunk] = None
        try:
            async for chunk in generator:
                if resp is None:
                    resp = cls.get_response_from_chunk(chunk)
                    resp.references = chunk.model_extra.get('references', None)
                    if not resp.is_tool_calls:
                        yield resp
                        resp = None
                else:
                    t = cls.get_response_from_chunk(chunk)
                    if resp.is_tool_calls and t.is_tool_calls:
                        if t.tool_calls is None or len(t.tool_calls) == 0:
                            continue
                        for i in range(len(resp.tool_calls)):  # type: ignore
                            # type: ignore
                            tool_call_resp: ToolCallChunk = resp.tool_calls[i]
                            tool_call_t: ToolCallChunk = t.tool_calls[i]
                            tool_call_resp.function.arguments += tool_call_t.function.arguments

                            if tool_call_resp.function.name is None:
                                tool_call_resp.function.name = tool_call_t.function.name
                    elif t.total_tokens is not None and t.total_tokens != 0:
                        resp.total_tokens = t.total_tokens
                        resp.prompt_tokens = t.prompt_tokens
                        resp.response_tokens = t.response_tokens
                        yield resp
                        resp = None

            if resp is not None:
                yield resp
        finally:
            # 确保异步生成器被正确关闭
            await generator.aclose()

    @override
    async def chat(self,
                   model_name: str,
                   provider_kind: str,
                   model_params_dict: Optional[dict],
                   response_mode: ResponseMode,
                   user_id: Optional[str],
                   messages: list[LLMMessage],
                   plugins: Optional[List[Plugin]] = None,
                   api_key: Optional[str] = None,
                   api_base: Optional[str] = None) \
            -> Union[LLMResponse, AsyncGenerator[LLMResponseChunk, None]]:
        if api_key is None or not api_key:
            api_key = self.api_key
        if api_base is None or not api_base:
            api_base = self.api_base

        if model_params_dict is None:
            model_params_dict = {}

        model_params = ModelParams.model_validate(model_params_dict)

        tools = await self.gen_tools(plugins)

        start = time()
        is_stream = response_mode == ResponseMode.STREAMING

        logger.bind(messages=messages).debug("openai chat")
        try:
            messages = [m.model_dump() for m in messages]
            # 修复 aigw 的 content 格式
            for m in messages:
                content = m.get('content', None)
                # 如果m['content'] 是一个数组，并且里面只有一个元素，这个元素的type还是text,则将m['content'] 的值赋值给m['content'][0]['text']
                if isinstance(content, list) and len(content) == 1 and isinstance(content[0], dict) and content[0].get('type', None) == 'text':
                    m['content'] = content[0]['text']
            # 剔除所有 content 为 None 或 '' 的 message
            messages = [m for m in messages if m.get(
                'content', None) is not None and m.get('content', None) != '']
            response_format = {
                "type": "json_object" if 'json_object' in model_params_dict and model_params_dict['json_object'] else 'text'
            }
            extra_body = {}
            if 'audio_timestamp' in model_params_dict and model_params_dict['audio_timestamp']:
                extra_body['vertexai'] = {
                    'audio_timestamp': model_params_dict['audio_timestamp']
                }
            response = await AsyncOpenAI(api_key=api_key, base_url=api_base).chat.with_raw_response.completions.create(
                # response = await AsyncOpenAI(api_key=api_key, base_url='http://aigc-api.apps-hangyan.danlu.netease.com/v1').chat.completions.create(
                model=model_name,
                messages=messages,  # type: ignore
                response_format=response_format,
                top_p=model_params.top_p,
                temperature=model_params.temperature,
                max_tokens=model_params.max_tokens,
                presence_penalty=model_params.presence_penalty,
                frequency_penalty=model_params.frequency_penalty,
                stream=is_stream,
                # moonshot 最大支持32位 user
                user=user_id[:32] if user_id else NOT_GIVEN,
                tools=tools,
                extra_body=extra_body,
            )
        except Exception as e:
            message = e.message if hasattr(e, 'message') else str(e)
            logger.error("openai error123 {model_name} {e} {api_key} {api_base} response_format={response_format} model_params_dict={model_params_dict}",
                         model_name=model_name, e=message, uid=user_id or 'uid', api_base=api_base, api_key=api_key, response_format=json.dumps(response_format), model_params_dict=model_params_dict)
            # logger.error('openai error {e}', e=e, uid=user_id)
            raise BadRequest(f"openai error {model_name} {e}")
        time_comsumed = time() - start

        try:
            trace_id = response.headers.get('ntes-trace-id')
            logger.info("openai chat response: trace_id={trace}, content={content} response_format={response_format}",
                        trace=trace_id, content=content[0:50] if content else '', uid="uid", response_format=json.dumps(response_format))
        except Exception as e:
            logger.error(
                "openai chat response: error={e}", e=e, uid="uid")
        response = response.parse()
        if isinstance(response, ChatCompletion):
            choice = response.choices[0]
            if choice.finish_reason == FinishReason.TOOL_CALLS:
                if choice.message.tool_calls is None:
                    raise InternalServerError(
                        "function call is not provided")
                return LLMResponse(
                    prompt_tokens=response.usage.prompt_tokens,  # type: ignore
                    response_tokens=response.usage.completion_tokens,  # type: ignore
                    total_tokens=response.usage.total_tokens,  # type: ignore
                    time_comsumed=time_comsumed,
                    is_tool_calls=True,
                    tool_calls=choice.message.tool_calls,
                )
            else:
                return LLMResponse(
                    content=choice.message.content,  # type: ignore
                    prompt_tokens=response.usage.prompt_tokens,  # type: ignore
                    response_tokens=response.usage.completion_tokens,  # type: ignore
                    total_tokens=response.usage.total_tokens,  # type: ignore
                    time_comsumed=time_comsumed,
                )
        if isinstance(response, AsyncStream):
            return self.get_generator((r async for r in response))

        raise BadRequest("could not handle response from OpenAI")

    @override
    async def speech(self,
                     model_name: str,
                     provider_kind: str,
                     inputs: str,
                     voice: str,
                     response_format: str,
                     speed: float,
                     api_key: Optional[str] = None,
                     api_base: Optional[str] = None) \
            -> str:
        if api_key is None:
            api_key = self.api_key
        if api_base is None:
            api_base = self.api_base

        client = AsyncOpenAI(api_key=api_key, base_url=api_base)

        speech_file_name = f"{str(uuid4())}.{response_format}"
        response = await client.audio.speech.create(input=inputs,
                                                    model=model_name,
                                                    voice=voice,
                                                    response_format=response_format,
                                                    speed=speed)
        key = f'{settings.app_workflow_temp_upload_file_path}/{speech_file_name}'
        await async_app_upload_file_service(s3_config=settings.workflow_config.s3,
                                            key=key,
                                            body=BytesIO(response.content))
        return f"https://{settings.workflow_config.s3.bucket}.{settings.workflow_config.s3.endpoint}/{key}"

    async def validate_config(self, provider_kind: str, params: dict):
        ModelParams.model_validate(params)

    @classmethod
    def get_response_from_chunk(cls, chunk: ChatCompletionChunk):
        try:
            # openai_chunk = ResponseChunk.model_validate(chunk_data)
            if len(chunk.choices) == 0:
                usage = getattr(chunk, 'usage', None)
                # openAI 的 chunk 可以从usage 中获取用量
                if isinstance(usage, dict):
                    return LLMResponseChunk(
                        prompt_tokens=usage.get('prompt_tokens', 0),
                        response_tokens=usage.get('completion_tokens', 0),
                        total_tokens=usage.get('total_tokens', 0)
                    )
                return LLMResponseChunk(
                    prompt_tokens=usage.prompt_tokens if usage is not None else 0,
                    response_tokens=usage.completion_tokens if usage is not None else 0,
                    total_tokens=usage.total_tokens if usage is not None else 0
                )
            if len(chunk.choices) > 0 \
                    and chunk.choices[0].finish_reason == FinishReason.TOOL_CALLS.value:
                return LLMResponseChunk(
                    is_tool_calls=True,
                    tool_calls=[],
                )
            # moonshoot 的 chunk要从choices[0].usage 中获取用量
            if hasattr(chunk.choices[0], 'usage') and chunk.choices[0].usage is not None:
                usage = getMoonShootStreamUsage(chunk)
                return LLMResponseChunk(
                    prompt_tokens=usage.get('prompt_tokens'),
                    response_tokens=usage.get('completion_tokens'),
                    total_tokens=usage.get('total_tokens'),
                )
            if chunk.choices[0].delta is not None:
                tool_calls = chunk.choices[0].delta.tool_calls
                if tool_calls is not None and len(tool_calls) > 0:
                    return LLMResponseChunk(
                        is_tool_calls=True,
                        tool_calls=tool_calls,
                    )

            usage = getattr(chunk, 'usage', None)
            # 如果usage
            if isinstance(usage, dict):
                return LLMResponseChunk(
                    content=getattr(chunk.choices[0].delta, 'content', None),
                    prompt_tokens=usage.get('prompt_tokens', 0),
                    response_tokens=usage.get('completion_tokens', 0),
                    total_tokens=usage.get('total_tokens', 0)
                )
            else:
                return LLMResponseChunk(
                    content=getattr(chunk.choices[0].delta, 'content', None),
                    reasoning_content=getattr(
                        chunk.choices[0].delta, 'reasoning_content', None),
                    prompt_tokens=usage.prompt_tokens if usage else 0,
                    response_tokens=usage.completion_tokens if usage else 0,
                    total_tokens=usage.total_tokens if usage else 0
                )
        except Exception as e:
            logger.bind(chunk=chunk).error(
                "could not validate openai chunk")
            raise e

    async def image_generate(self,
                             prompt: str,
                             model_name: Literal["dall-e-3"],
                             provider_kind: str,
                             n: Optional[int] = None,
                             quality: Optional[Literal["standard",
                                                       "hd"]] = None,
                             response_format: Optional[Literal["url",
                                                               "b64_json"]] = None,
                             size: Optional[
                                 Literal["1024x1024", "1792x1024", "1024x1792"]] = None,
                             style: Optional[Literal["vivid",
                                                     "natural"]] = None,
                             api_key: Optional[str] = None,
                             api_base: Optional[str] = None):
        if api_key is None:
            api_key = self.api_key
        if api_base is None:
            api_base = self.api_base

        resp = await AsyncOpenAI(api_key=api_key, base_url=api_base).images.generate(
            prompt=prompt,
            model=model_name,
            n=n,
            quality=quality if quality else NOT_GIVEN,
            response_format=response_format,
            size=size,
            style=style,
        )
        if resp.created is not None:
            return resp
        raise BadRequest(f"could not handle response {resp} from OpenAI")
