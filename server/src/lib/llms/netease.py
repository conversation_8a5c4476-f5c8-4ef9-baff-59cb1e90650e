import httpx
from time import time
from loguru import logger
from typing import Optional
from .util import modelParamsKeyToCamelCase, neteaseAuthHeader
from ...misc.errors import InternalServerError
from pydantic import BaseModel, ConfigDict, Field
from ...schema.dialog import LLMMessage, ResponseMode, Response as LLMResponse


class NeteaseModelParams(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    # 使用 NeteaseClient 的模型，可以继承 NeteaseModelParams，覆盖参数的取值范围
    temperature: Optional[float] = Field(None, ge=0.0, le=1.0)
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0)
    frequency_penalty: Optional[float] = Field(None, ge=1, le=2.0)


class NeteaseClient():

    def __init__(self,
                 api_app_id: Optional[str] = None,
                 api_app_key: Optional[str] = None,
                 api_base: Optional[str] = None):
        self.api_app_id = api_app_id
        self.api_app_key = api_app_key
        self.api_base = api_base

    async def chat(self,
                   model_name: str,
                   model_params_dict: Optional[NeteaseModelParams],
                   response_mode: ResponseMode,
                   messages: list[LLMMessage],
                   api_app_id: Optional[str],
                   api_app_key: Optional[str],
                   api_base: Optional[str]
                   ) -> LLMResponse:

        if api_app_id is None:
            api_app_id = self.api_app_id
        if api_app_key is None:
            api_app_key = self.api_app_key
        if api_base is None:
            api_base = self.api_base

        if model_params_dict is None:
            model_params_dict = {}

        start = time()
        # 将参数 key 转为驼峰 如 {top_p: 1} -> {topP: 1}
        model_params = modelParamsKeyToCamelCase(model_params_dict)
        # is_stream = response_mode == ResponseMode.STREAMING

        headers = neteaseAuthHeader(api_app_id, api_app_key)
        body = {
            "model": model_name,
            "messages": [m.model_dump() for m in messages],
            **model_params,  # type: ignore
        }
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    url=f"{api_base}/text/chat",
                    json=body,
                    headers=headers,
                    timeout=300.0)  # 超时时间5分钟
            except httpx.ReadTimeout:
                logger.error("[netease client] chat timeout")
                raise InternalServerError("[netease client] chat timeout")
            except Exception as e:
                logger.error(f"[netease client] chat exception: {e.__class__}")
                raise InternalServerError(
                    f"[netease client] chat exception: {e.__class__}")

            if not response.is_success:
                logger.error(f"[netease client] chat failed: {response.text}")
                raise InternalServerError(
                    f"[netease client] chat failed: {response.text}")

            resp = response.json()
            status = resp['status']

            if status != '000000':
                logger.error(f"[netease client] chat failed: {resp['desc']}")
                raise InternalServerError(
                    f"[netease client] chat failed: {resp['desc']}")

        time_consumed = time() - start
        logger.bind(response=response, time_comsumed=time_consumed).debug(
            "[netease client] chat response")
        detail = resp['detail']
        choice = detail['choices'][0]
        usage = detail['usage']
        # 判断 choice.finish_reason
        return LLMResponse(content=choice['message']['content'],  # type: ignore
                           prompt_tokens=usage['promptTokens'],  # type: ignore
                           # type: ignore
                           response_tokens=usage['completionTokens'],
                           total_tokens=usage['totalTokens'],  # type: ignore
                           time_comsumed=time_consumed)
