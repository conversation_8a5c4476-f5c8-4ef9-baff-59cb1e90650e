import hashlib
import time


def isPlainObject(obj):
    return type(obj) is dict


def isString(obj):
    return isinstance(obj, str)


def getSignKey(params, appKey):
    suffix = f"&appkey={appKey}" if appKey else ""
    if isPlainObject(params):
        return "&".join([f"{key}={params[key]}" for key in sorted(params)]) + suffix
    if isString(params):
        return params + suffix
    assert False, "invalid object to build key!"


def generateSign(encryptHeaders, appKey):
    key = getSignKey(encryptHeaders, appKey)
    return hashlib.md5(key.encode('utf-8')).hexdigest().upper()


def neteaseAuthHeader(appId, appKey):
    header = {
        "timestamp": str(int(time.time() * 1000)),
        "appId": appId,
        "nonce": '10000',
    }
    sign = generateSign(header, appKey)
    return {
        **header,
        "sign": sign,
        "version": "v2"
    }


def modelParamsKeyToCamelCase(param: dict):
    """将参数中的 key 转驼峰形式，如 {top_p: 1} -> {topP: 1}

    Args:
        param (dict): 原始参数
    """
    camelParams = {
        textToCamelCase(key): value for key, value in param.items()
    }
    return camelParams


def textToCamelCase(text: str):
    """字符串转驼峰形式，如 top_p -> topP

    Args:
        text (str): 原始字符串
    """

    arr = text.split('_')
    subArr = [sub.capitalize() for sub in arr[1:]]
    subArr.insert(0, arr[0])
    return ''.join(subArr)
