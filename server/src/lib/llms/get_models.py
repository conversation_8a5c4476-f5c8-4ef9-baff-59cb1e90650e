import json
from typing import List

from loguru import logger
from ...schema.llm_model import LLMModel
from ...config import settings
from ...misc.redis_utils import MODEL_ALL_KEY, get_redis_sync, set_redis_sync
from ...misc.errors import BadRequest
from ...utils.normalize import normalize_fields
import httpx


async def fetch_all_models(force_refresh: bool = False) -> List[LLMModel]:
    # 从redis中获取
    models = get_redis_sync(MODEL_ALL_KEY)
    if models is not None and not force_refresh:
        try:
            return [LLMModel(**model) for model in json.loads(models)]
        except Exception as e:
            logger.error("fetch_all_models error: {e}", uid='uid', e=e)
            pass
    # httpx请求/langbase/inner/common/getAll/model
    prefix = settings.db_service_url
    response = httpx.get(f'{prefix}/langbase/inner/common/getAll/model')
    res = response.json()
    if res['code'] != 200:
        raise BadRequest(res['message'])
    models = normalize_fields('modelId', res['data'])
    # 存一份redis
    set_redis_sync(MODEL_ALL_KEY, json.dumps(models))
    return [LLMModel(**model) for model in models]


# 从redis中获取所有模型，如果获取不到，则从数据库中获取
def get_all_models():
    models = get_redis_sync(MODEL_ALL_KEY)
    if models is not None:
        return [LLMModel(**model) for model in json.loads(models)]
    return []
