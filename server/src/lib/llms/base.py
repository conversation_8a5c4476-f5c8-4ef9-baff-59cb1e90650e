from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, AsyncGenerator, List, Optional, Union, Literal

from pydantic import BaseModel

from .get_models import get_all_models

from ...misc.redis_utils import get_redis_sync

from ...schema.model import ModelType, MultiModalType

from ...config import settings

from ...schema.dialog import ResponseChunk, ResponseMode, LLMMessage, Response


def isModelSupportMultiModal(model_name: str | None, tag: str) -> bool:
    # 从redis中获取
    models = get_all_models()
    # 如果获取失败，则认为所有模型都支持多模态，防止执行出错
    if models is None:
        return True
    for model in models:
        if model.name == model_name:
            return model.tag is not None and tag in model.tag
    return False


class Model(BaseModel):
    name: str
    description: str
    enable: bool = True
    type: ModelType
    support_multi_modal: List[MultiModalType]
    fee: Optional[dict] = {'input': 0, 'output': 0, 'avg': 0}
    context: Optional[int] = 0
    speed: Optional[int] = None
    performance: Optional[int] = None
    alias: Optional[str] = None
    url: Optional[str] = ''
    tag: Optional[List[str]] = []
    recommended: bool = False
    fast: bool = False
    config: Any


class Provider(BaseModel):
    kind: str
    # 中文描述
    description: str
    # 图标
    icon: Optional[str] = None
    # 是否为openai兼容模型
    openai_like: bool = False
    # 是否能一键绑定
    bindable: Optional[bool] = True
    endpoint: str
    api_key: Optional[str]
    api_app_id: Optional[str]
    api_app_key: Optional[str]
    config: Any
    models: List[Model]


class Providers(Enum):
    OpenAI = Provider(
        kind='openai',
        description='OpenAI(AIGW)',
        openai_like=True,
        icon='https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61236714880/7a26/8357/f7f1/727bed3e50a08b97fb88ec43b55b9d57.png',
        endpoint=settings.openai_api_base,
        api_key=settings.openai_api_key,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[],
    )
    Anthropic = Provider(
        kind='anthropic',
        description='Claude(AIGW)',
        icon='https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61236759542/f4c1/03cd/575b/fa1c0850b8a50a228dd25f1c12858dc6.png',
        openai_like=True,
        endpoint='',
        api_key=None,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    Qwen = Provider(
        kind='qwen',
        description='通义千问(AIGW)',
        icon='https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61236703991/bd89/e177/fa01/e8242eb71a900b73483c0863bdc8e74f.png',
        openai_like=True,
        endpoint='',
        api_key=None,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    Moonshot = Provider(
        kind='moonshot',
        description='月之暗面(AIGW)',
        icon='https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61236786084/50cd/e331/9089/72dd8488b8280fadd43c8dbd4803e164.png',
        openai_like=True,
        endpoint=settings.moonshot_api_base,
        api_key=settings.moonshot_api_key,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    DeepSeek = Provider(
        kind='deepseek',
        description='DeepSeek(AIGW)',
        icon='https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61236787427/0bca/0b2a/e199/3507aa04d33af680234da5fe02dee4b5.png',
        openai_like=True,
        endpoint=settings.deepseek_api_base,
        api_key=settings.deepseek_api_key,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    MiniMax = Provider(
        kind='minimax',
        description='MiniMax(AIGW)',
        icon='https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61236751051/e766/a0e1/2b50/d27142ecf100c28b7972aba20920d6a4.png',
        openai_like=True,
        endpoint=settings.minimax_api_base,
        api_key=settings.minimax_api_key,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    DouBao = Provider(
        kind='doubao',
        description='豆包(AIGW)',
        icon='https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61236755334/e63f/93eb/b887/d8fe383ecf075d4e90d3cae694f50a21.png',
        openai_like=True,
        endpoint='https://aigw-int.netease.com/v1',
        api_key=None,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    Google = Provider(
        kind='google',
        description='Google(AIGW)',
        icon='https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/61236741659/0e10/2b2b/776f/e77740a266d17781a93c5e0bbf399313.png',
        endpoint='https://aigw-int.netease.com/v1',
        openai_like=True,
        api_key=None,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    Tmax = Provider(
        kind='tmax',
        description='私有部署(AIGW)',
        icon='https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59113120979/5f61/a83f/4b1c/98e245e7f15157a0427f411f6f33bda2.png',
        endpoint='https://aigw-int.netease.com/v1',
        openai_like=True,
        api_key='493kozav-8qa5-tk.6e9w0tn5sbmv060tkcr79wsbfx5544c6',
        bindable=False,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    Aigc = Provider(
        kind='aigc',
        description='私有化部署(伏羲)',
        icon='https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59113120979/5f61/a83f/4b1c/98e245e7f15157a0427f411f6f33bda2.png',
        endpoint='http://ai-text-service-test.apps-hangyan.danlu.netease.com/v1',
        openai_like=True,
        api_key='sk-XCJDtwvFEbl5qBtpXgTjYkTDvfOSzekcwPIz8nIC4Vb9TgVd',
        bindable=False,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    YouDao = Provider(
        kind='youdao',
        description='私有化部署(有道)',
        icon='https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/59113120979/5f61/a83f/4b1c/98e245e7f15157a0427f411f6f33bda2.png',
        endpoint='https://aigw-int.netease.com/v1',
        openai_like=True,
        api_key='493kozav-8qa5-tk.6e9w0tn5sbmv060tkcr79wsbfx5544c6',
        bindable=False,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    Ark = Provider(
        kind='ark',
        description='火山引擎（外部）',
        icon='https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/60961965968/e6fa/a5b1/dba0/e762fb87e99570a4735bceedf8b903e8.png',
        endpoint='https://ark.cn-beijing.volces.com/api/v3',
        openai_like=True,
        api_key=None,
        bindable=False,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    MINIMAX_OUTER = Provider(
        kind='minimax_outer',
        description='MINIMAX（外部）',
        icon='https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/55966252488/1cf5/cef7/ae69/a98281f8ae61d12d6d155d2895cd8488.png',
        endpoint='',
        openai_like=True,
        api_key=None,
        bindable=False,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )
    Aliyun = Provider(
        kind='aliyun',
        description='阿里云（外部）',
        icon='https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/55966252533/bb8c/9874/5155/b5be5a30eaca019a4bf728f96ccdfd4a.png',
        endpoint='',
        openai_like=True,
        api_key=None,
        bindable=False,
        api_app_id=None,
        api_app_key=None,
        config={},
        models=[]
    )


class ModelInput(BaseModel):
    # pre_prompt: Optional[str]
    model_name: str
    provider_kind: str
    model_params: Optional[dict]
    response_mode: ResponseMode
    user_id: Optional[str]


class Imager(ABC):
    @abstractmethod
    async def image_generate(self,
                             prompt: str,
                             model_name: Literal["dall-e-3"],
                             provider_kind: str,
                             n: Optional[int] = None,
                             quality: Optional[Literal["standard",
                                 "hd"]] = None,
                             response_format: Optional[Literal["url",
                                 "b64_json"]] = None,
                             size: Optional[Literal["1024x1024", "1792x1024", "1024x1792"]] = None,  # noqa
                             style: Optional[Literal["vivid",
                                 "natural"]] = None,
                             api_key: Optional[str] = None,
                             api_base: Optional[str] = None):
        raise NotImplementedError


class Speecher(ABC):
    @abstractmethod
    async def speech(self,
                     model_name: str,
                     provider_kind: str,
                     input: str,
                     voice: str,
                     response_format: str,
                     api_key: Optional[str] = None,
                     api_base: Optional[str] = None):
        raise NotImplementedError


class Interlocutor(ABC):
    @abstractmethod
    async def chat(self,
                   model_name: str,
                   provider_kind: str,
                   model_params: Optional[dict],
                   response_mode: ResponseMode,
                   user_id: Optional[str],
                   messages: list[LLMMessage],
                   tools: Optional[List[Any]] = None,
                   api_key: Optional[str] = None,
                   api_base: Optional[str] = None,
                   api_app_id: Optional[str] = None,
                   api_app_key: Optional[str] = None) \
            -> Union[Response, AsyncGenerator[ResponseChunk, None]]:
        '''
        user_input: could be parameters or app config
        or and an ID of an existing conversation
        '''
        raise NotImplementedError


class Validator(ABC):
    @abstractmethod
    async def validate_config(self, provider_kind: str, config: dict):
        '''
        user_input: could be parameters or app config
        or and an ID of an existing conversation
        '''
        raise NotImplementedError
