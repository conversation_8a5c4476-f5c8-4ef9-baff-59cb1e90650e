from typing import Optional, List, Any
from typing_extensions import override
from .netease import NeteaseClient, NeteaseModelParams
from .base import <PERSON><PERSON>utor, Validator
from pydantic import ConfigDict, Field
from ...schema.dialog import LLMMessage, ResponseMode, Response as LLMResponse


class WenXinModelParams(NeteaseModelParams):
    model_config = ConfigDict(from_attributes=True)

    temperature: Optional[float] = Field(None, ge=0.01, le=1.0)
    top_p: Optional[float] = Field(None, ge=0.0, le=1.0)
    frequency_penalty: Optional[float] = Field(None, ge=1, le=2.0)


class WenXin(Interlocutor, Validator):

    def __init__(self,
                 api_app_id: Optional[str] = None,
                 api_app_key: Optional[str] = None,
                 api_base: Optional[str] = None):
        self.client = NeteaseClient(
            api_app_id=api_app_id,
            api_app_key=api_app_key,
            api_base=api_base)

    @override
    async def chat(self,
                   model_name: str,
                   provider_kind: str,
                   model_params_dict: Optional[dict],
                   response_mode: ResponseMode,
                   user_id: Optional[str],
                   messages: list[LLMMessage],
                   tools: Optional[List[Any]] = None,
                   api_key: Optional[str] = None,
                   api_base: Optional[str] = None,
                   api_app_id: Optional[str] = None,
                   api_app_key: Optional[str] = None
                   ) -> LLMResponse:

        if response_mode == ResponseMode.STREAMING:
            raise Exception(f"{provider_kind} 暂不支持流式请求")

        # 文心模型，不支持仅有一条 system 的模式
        if len(messages) == 1 and messages[0].role == 'system':
            messages[0].role = 'user'

        return await self.client.chat(
            model_name,
            model_params_dict,
            response_mode,
            messages,
            api_app_id,
            api_app_key,
            api_base)

    async def validate_config(self, provider_kind: str, params: dict):
        WenXinModelParams.model_validate(params)
