import base64
import io
import re

from PIL import Image
from uuid import UUID
import hashlib
import jwt
import json
from typing import Any


def to_sha256(obj: Any) -> str:
    json_str = json.dumps(obj, sort_keys=True)
    return hashlib.sha256(json_str.encode('utf8')).hexdigest()


def template_render(template: str, **variables):
    pattern = r'\{\{\s*(.*?)\s*\}\}'
    matches = re.findall(pattern, template)

    for match in matches:
        if match in variables:
            template = template.replace(
                '{{' + match + '}}', str(variables[match]))

    return template


def is_uuid(s: str) -> bool:
    if len(s) != 36:
        return False
    try:
        UUID(s)
        return True
    except ValueError:
        return False


def is_jwt_token(token: str):
    try:
        jwt.decode(token, options={"verify_signature": False})
        return True
    except jwt.exceptions.DecodeError:
        return False


def decode_base64_to_pil(encoding):
    if encoding.startswith("data:image/"):
        encoding = encoding.split(";")[1].split(",")[1]
    return Image.open(io.BytesIO(base64.b64decode(encoding)))


def format_number(num: float):
    if num >= 100000000:
        return f"{num / 100000000:.2f}亿"
    if num >= 10000:
        return f"{num / 10000:.2f}万"
    return f"{num:.0f}"


def get_string_width(string: str) -> int:
    """
    计算字符串的显示宽度，其中：
    - 中文字符计为2个长度
    - 英文字符计为1个长度

    Args:
        string: 要计算的字符串

    Returns:
        int: 字符串的显示宽度
    """
    width = 0
    for char in string:
        if '\u4e00' <= char <= '\u9fff':  # 判断是否是中文字符
            width += 2
        else:
            width += 1
    return width
