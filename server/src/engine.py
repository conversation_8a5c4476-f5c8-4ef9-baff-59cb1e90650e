import sys

from loguru import logger
from prometheus_fastapi_instrumentator import Instrumentator

from .controller.service import router as service_router
from fastapi import APIRouter, FastAPI
from . import misc
from .config import settings

app = FastAPI(debug=False, redirect_slashes=False)
router = APIRouter(prefix="/api/v1")

instrument = Instrumentator(should_group_status_codes=False,
                            should_ignore_untemplated=True,
                            excluded_handlers=["/metrics"])

misc.init_app(app)
router.include_router(service_router)
app.include_router(router)
instrument.instrument(app=app).expose(app=app, endpoint="/metrics")

# logger.add(sys.stdout, level="INFO")


def main():
    import uvicorn
    uvicorn.run("src.engine:app",
                host=settings.server_host,
                port=settings.server_port,
                reload=settings.debug)


if __name__ == "__main__":
    main()
