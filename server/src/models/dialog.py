from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VARC<PERSON><PERSON>, CHAR, JSON, TEXT, INTEGER, DATETIME, DECIMAL
from datetime import datetime

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, generate_uuid
from .operator_mixin import CreaterMixin, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class Conversation(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                   UpdaterMixin, UpdateTimeMixin, CreateTimeMixin):
    __tablename__ = 'tb_conversation'

    app_id = Column(CHAR(36), nullable=False)
    name = Column(VARCHAR(128), nullable=False, default='')
    app_config_id = Column(CHAR(36), nullable=True)
    override_config = Column(JSON, nullable=True)
    parameters = Column(JSON, nullable=True)
    prompt_tokens_for_name = Column(INTEGER(11), nullable=False, default=0)
    response_tokens_for_name = Column(INTEGER(11), nullable=False, default=0)
    total_tokens_for_name = Column(INTEGER(11), nullable=False, default=0)


event.listen(Conversation, 'before_insert', generate_uuid)
event.listen(Conversation, 'load', get_user_detail_for_load)
event.listen(Conversation, 'refresh', get_user_detail_for_refresh)


class Message(Base, UUIDPrimaryKeyMixin, CreaterMixin,
              UpdateTimeMixin):
    __tablename__ = 'tb_message'

    app_id = Column(CHAR(36), nullable=False)
    conversation_id = Column(CHAR(36), nullable=False)
    query = Column(TEXT, nullable=False, default='')
    response = Column(JSON, nullable=True)
    response_type = Column(CHAR(32), nullable=False, default='text')
    tool_call_response_ids = Column(JSON, nullable=True)

    prompt_tokens = Column(INTEGER(11), nullable=False, default=0)
    response_tokens = Column(INTEGER(11), nullable=False, default=0)
    total_tokens = Column(INTEGER(11), nullable=False, default=0)
    time_comsumption_in_ms = Column(INTEGER(11), nullable=False, default=0)
    kind = Column(CHAR(128), nullable=True)
    model_name = Column(CHAR(128), nullable=True)
    fee = Column(DECIMAL(10, 6), nullable=True)

    created_at = Column(DATETIME(fsp=6), nullable=False,
                        default=datetime.now, name='created_at')


event.listen(Message, 'before_insert', generate_uuid)
event.listen(Message, 'load', get_user_detail_for_load)
event.listen(Message, 'refresh', get_user_detail_for_refresh)


class MessageToolCallResponse(Base, UUIDPrimaryKeyMixin):
    __tablename__ = 'tb_message_tool_call_response'

    message_id = Column(CHAR(36), nullable=False)
    tool_call_id = Column(CHAR(128), nullable=False)
    response = Column(TEXT, nullable=False, default='')

    created_at = Column(DATETIME(fsp=6), nullable=False,
                        default=datetime.now, name='created_at')


event.listen(MessageToolCallResponse, 'before_insert', generate_uuid)
