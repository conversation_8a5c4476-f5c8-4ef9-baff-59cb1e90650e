from sqlalchemy import Column, String
from sqlalchemy.ext.declarative import declarative_base

from .common import CreateTimeMixin

Base = declarative_base()


class Collection(Base, CreateTimeMixin):
    __tablename__ = 'tb_collection'

    id = Column(String(36), primary_key=True)
    user_id = Column(String(36), nullable=False)
    resource_type = Column(String(36), nullable=False)
    resource_id = Column(String(36), nullable=False)
    group_id = Column(String(36), nullable=False)

    def __repr__(self):
        return f"<Collection(id={self.id}, user_id={self.user_id}, resource_type={self.resource_type}, resource_id={self.resource_id}, group_id={self.group_id})>"
