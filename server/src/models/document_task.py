from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VA<PERSON><PERSON><PERSON>, CHAR, JSON, INTEGER

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import CreaterMixin, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class DocumentTask(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                   UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                   SoftDeletableMixin):
    __tablename__ = 'tb_document_task'

    workspace_id = Column(CHAR(36), nullable=False)
    knowledge_id = Column(CHAR(36), nullable=False)
    title = Column(VARCHAR(128), nullable=True, default='')
    type = Column(VARCHAR(128), nullable=True, default='')
    tag_name = Column(VARCHAR(128), nullable=True, default='')
    source = Column(VARCHAR(128), nullable=True, default='')
    config = Column(JSON, nullable=True)
    task_id = Column(INTEGER(20), nullable=True)
    task_state = Column(INTEGER(20), nullable=True, default=0)


event.listen(DocumentTask, 'before_insert', generate_uuid)
event.listen(DocumentTask, 'load', get_user_detail_for_load)
event.listen(DocumentTask, 'refresh', get_user_detail_for_refresh)
