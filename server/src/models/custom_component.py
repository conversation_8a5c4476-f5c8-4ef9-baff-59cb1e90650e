from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VARCHA<PERSON>, CHAR, BOOLEAN, JSON

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from ..schema.common import Scope
from .operator_mixin import CreaterMixin, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class CustomComponent(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                      UpdaterMixin, UpdateTimeMixin, CreateTimeMixin):
    __tablename__ = 'tb_custom_component'

    name = Column(VARCHAR(128), nullable=False, default='', unique=True)
    render_key = Column(VARCHAR(128), nullable=False, unique=False)
    code = Column(VARCHAR(128), nullable=False, unique=False)
    description = Column(VARCHAR(256), nullable=True, default=None)
    type = Column(VARCHAR(64), nullable=False)
    category_name = Column(VARCHAR(128), nullable=False)
    category_id = Column(VARCHAR(128), nullable=False)
    workspace_id = Column(CHAR(36), nullable=False)
    group_id = Column(CHAR(36), nullable=False)
    scope = Column(VARCHAR(20), nullable=False, default=Scope.SCOPED.value)
    config = Column(JSON, nullable=True, default=None)
    extra_config = Column(JSON, nullable=True, default=None)
    deprecated = Column(BOOLEAN, nullable=False, default='false')
    app_types = Column(JSON, nullable=True, default=None)


event.listen(CustomComponent, 'before_insert', generate_uuid)
event.listen(CustomComponent, 'load', get_user_detail_for_load)
event.listen(CustomComponent, 'refresh', get_user_detail_for_refresh)
