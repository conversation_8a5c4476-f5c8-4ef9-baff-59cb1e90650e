from click import prompt
from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VARC<PERSON>R, JSON, CHAR, TEXT

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import CreaterMixin, UpdaterMixin


class Setting(Base, UUIDPrimaryKeyMixin, CreaterMixin,
              UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
              SoftDeletableMixin):
    __tablename__ = 'tb_setting'
    appId = Column(CHAR(36), nullable=False)
    name = Column(VARCHAR(128), nullable=False)
    settingId = Column(VARCHAR(128), unique=True, nullable=False)
    description = Column(VARCHAR(256), nullable=True, default=None)
    creator = Column(CHAR(36), nullable=False)
    updater = Column(CHAR(36), nullable=False)
    updateTime = Column(CHAR(36), nullable=False)
    createTime = Column(CHAR(36), nullable=False)
    extInfo = Column(JSON, nullable=True, default=None)
