from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VARC<PERSON><PERSON>, CHAR

from .operator_mixin import GrandedByMixin, get_user_detail_for_load, \
    get_user_detail_for_refresh

from .common import AutoIncrementKeyMixin, CreateTimeMixin, \
    UpdateTimeMixin
from .base import Base


class Member(Base, AutoIncrementKeyMixin, CreateTimeMixin,
             UpdateTimeMixin, GrandedByMixin):
    __tablename__ = "tb_member"

    role = Column(VARCHAR(64), nullable=False)
    resource_type = Column(VARCHAR(10), nullable=False,
                           comment="workspace/group/app/dataset etc")
    resource_id = Column(CHAR(36), nullable=False)
    member_type = Column(VARCHAR(10), nullable=False,
                         comment="user or group")
    member_id = Column(CHAR(36), nullable=False)

    def __repr__(self):
        return f"<Member id={self.id} resource_type={self.resource_type} " \
                f"resource_id={self.resource_id}" \
                f"member_type={self.member_type} " \
                f"member_id={self.member_id} granted_by={self.granted_by} " \
                f"role={self.role}>"


event.listen(Member, 'load', get_user_detail_for_load)
event.listen(Member, 'refresh', get_user_detail_for_refresh)
