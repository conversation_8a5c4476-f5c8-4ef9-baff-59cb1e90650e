from datetime import datetime
from sqlalchemy import Column, Integer
from sqlalchemy_easy_softdelete.mixin import generate_soft_delete_mixin_class
from sqlalchemy.dialects.mysql import BIGINT, DATETIME, CHAR
from uuid import uuid4


class AutoIncrementKeyMixin:
    id = Column(BIGINT(20).with_variant(Integer, "sqlite"), nullable=False,
                autoincrement=True, primary_key=True)


class CreateTimeMixin:
    created_at = Column(DATETIME, nullable=False,
                        default=datetime.now, name='created_at')


class UpdateTimeMixin:
    updated_at = Column(DATETIME, nullable=False, name='updated_at',
                        default=datetime.now, onupdate=datetime.now)


class SoftDeletableMixin(generate_soft_delete_mixin_class(
    deleted_field_name="deleted_by",
    deleted_field_type=CHAR(36),
    delete_method_default_value=lambda: None,
)):
    deleted_at = Column(DATETIME, name='deleted_at',
                        nullable=True, default='0000-00-00 00:00:00')
    deleted_by = Column(CHAR(36), name='deleted_by', default=None)


class UUIDPrimaryKeyMixin:
    id = Column(CHAR(36), nullable=False, primary_key=True)


def generate_uuid(mapper, connect, target):
    if isinstance(target, UUIDPrimaryKeyMixin):
        if target.id is None:
            target.id = str(uuid4())  # type: ignore
