from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VA<PERSON>HA<PERSON>, CHAR

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import Creater<PERSON><PERSON><PERSON>, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class EnvVariable(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                  UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                  SoftDeletableMixin):
    __tablename__ = 'tb_env_variable'

    name = Column(VARCHAR(128), nullable=False)
    component_id = Column(CHAR(36), nullable=False)
    param = Column(VARCHAR(128), nullable=True)
    value = Column(VARCHAR(1024), nullable=True)
    scope_type = Column(VARCHAR(36), nullable=False)
    scope_id = Column(VARCHAR(36), nullable=False)


event.listen(EnvVariable, 'before_insert', generate_uuid)
event.listen(EnvVariable, 'load', get_user_detail_for_load)
event.listen(EnvVariable, 'refresh', get_user_detail_for_refresh)
