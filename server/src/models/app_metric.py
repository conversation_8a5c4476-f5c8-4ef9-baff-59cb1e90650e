from datetime import datetime
from sqlalchemy import VA<PERSON>HA<PERSON>, Column, event
from sqlalchemy.dialects.mysql import DECIMAL, CHAR, JSON, TEXT, INTEGER, DATETIME
from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import CreateTimeMixin, generate_uuid
from .operator_mixin import CreaterMixin


class AppMetric(Base, UUIDPrimaryKeyMixin, CreateTimeMixin):
    __tablename__ = 'tb_app_metric'

    app_id = Column(CHAR(36), nullable=False)
    name = Column(VARCHAR(256), nullable=True)
    model_name = Column(VARCHAR(256), nullable=True)
    group_id = Column(CHAR(36), nullable=True)
    workspace_id = Column(CHAR(36), nullable=True)
    total_tokens = Column(INTEGER(11), nullable=False, default=0)
    users = Column(INTEGER(11), nullable=False, default=0)
    messages = Column(INTEGER(11), nullable=False, default=0)
    time = Column(INTEGER(11), nullable=True, default=0)
    conversations = Column(INTEGER(11), nullable=False, default=0)
    token_per_message = Column(INTEGER(11), nullable=False, default=0)
    token_per_second = Column(INTEGER(11), nullable=False, default=0)
    date = Column(DATETIME, nullable=False, default=datetime.now)
    fee = Column(DECIMAL(10, 6), nullable=True)
    extra = Column(JSON, nullable=True)


event.listen(AppMetric, 'before_insert', generate_uuid)
