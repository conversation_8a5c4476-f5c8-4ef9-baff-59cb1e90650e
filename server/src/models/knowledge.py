from sqlalchemy import Column, event, Integer
from sqlalchemy.dialects.mysql import VA<PERSON>HA<PERSON>, CHAR, INTEGER

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import CreaterMixin, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class Knowledge(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                SoftDeletableMixin):
    __tablename__ = 'tb_knowledge'

    workspace_id = Column(CHAR(36), nullable=False)
    group_id = Column(CHAR(36), nullable=False)
    name = Column(VARCHAR(128), nullable=False, default='')
    description = Column(VARCHAR(256), nullable=True, default=None)
    collection_id = Column(INTEGER(20).with_variant(
        Integer, "sqlite"), nullable=False, autoincrement=True)


event.listen(Knowledge, 'before_insert', generate_uuid)
event.listen(Knowledge, 'load', get_user_detail_for_load)
event.listen(Knowledge, 'refresh', get_user_detail_for_refresh)
