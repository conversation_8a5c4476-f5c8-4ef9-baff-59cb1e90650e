from sqlalchemy import Column
from sqlalchemy import event
from sqlalchemy import Boolean
from sqlalchemy.dialects.mysql \
    import VARCHAR

from .common import CreateTimeMixin, UUIDPrimaryKeyMixin, \
    UpdateTimeMixin, generate_uuid
from .base import Base


class Account(Base, UUIDPrimaryKeyMixin, CreateTimeMixin, UpdateTimeMixin):
    __tablename__ = 'tb_account'

    name = Column(VARCHAR(128), nullable=False, default='')
    fullname = Column(VARCHAR(128), nullable=False, default='')
    email = Column(VARCHAR(128), nullable=False, default='')
    is_admin = Column(Boolean, nullable=False, default=0)


event.listen(Account, 'before_insert', generate_uuid)
