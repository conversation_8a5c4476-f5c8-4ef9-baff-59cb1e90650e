from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VA<PERSON><PERSON><PERSON>

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import Creater<PERSON><PERSON><PERSON>, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class Workspace(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                SoftDeletableMixin):
    __tablename__ = 'tb_workspace'

    name = Column(VARCHAR(128), nullable=False, default='')
    description = Column(VARCHAR(256), nullable=True, default=None)


event.listen(Workspace, 'before_insert', generate_uuid)
event.listen(Workspace, 'load', get_user_detail_for_load)
event.listen(Workspace, 'refresh', get_user_detail_for_refresh)
