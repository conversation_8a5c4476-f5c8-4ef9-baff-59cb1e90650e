from sqlalchemy import Column, String, JSON
from sqlalchemy import event

from .operator_mixin import CreaterMixin, UpdaterMixin
from .base import Base
from .common import UUIDPrimaryKeyMixin, CreateTimeMixin, UpdateTimeMixin, generate_uuid


class AigwApp(Base, UUIDPrimaryKeyMixin, CreaterMixin, UpdaterMixin,
              UpdateTimeMixin, CreateTimeMixin):
    __tablename__ = "tb_aigw_app"

    app_code = Column(String(36), nullable=False)
    resource_type = Column(String(36), nullable=False)
    resource_id = Column(String(36), nullable=False)
    quota = Column(String(10), nullable=False, default="0")
    token = Column(String(36), nullable=False)
    extra = Column(JSON)


event.listen(AigwApp, 'before_insert', generate_uuid)
