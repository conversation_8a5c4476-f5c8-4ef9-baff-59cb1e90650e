from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VA<PERSON><PERSON><PERSON>, CHAR

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import Creater<PERSON><PERSON><PERSON>, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class Group(Base, UUIDPrimaryKeyMixin, CreaterMixin,
            UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
            SoftDeletableMixin):
    __tablename__ = 'tb_group'

    workspace_id = Column(CHAR(36), nullable=False)
    name = Column(VARCHAR(128), nullable=False, default='')
    description = Column(VARCHAR(256), nullable=True, default=None)


event.listen(Group, 'before_insert', generate_uuid)
event.listen(Group, 'load', get_user_detail_for_load)
event.listen(Group, 'refresh', get_user_detail_for_refresh)
