from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VARC<PERSON>R, CHAR, JSON, BOOLEAN, INTEGER

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, generate_uuid
from .operator_mixin import Creater<PERSON>ix<PERSON>, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class ModelProviderBinding(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                           UpdaterMixin, UpdateTimeMixin, CreateTimeMixin):
    __tablename__ = 'tb_model_provider_binding'

    kind = Column(CHAR(36), nullable=False)
    description = Column(VARCHAR(256), nullable=True)
    workspace_id = Column(CHAR(36), nullable=False)
    group_id = Column(CHAR(36), nullable=True)
    endpoint = Column(VARCHAR(256), nullable=True)
    api_key = Column(VARCHAR(256), nullable=True)
    config = Column(JSON, nullable=True, default=None)

    @property
    def is_default(self) -> bool:
        # 只有纯workspace的model_provider_binding才是default
        if self.group_id is None:
            return True
        return False


event.listen(ModelProviderBinding, 'before_insert', generate_uuid)
event.listen(ModelProviderBinding, 'load', get_user_detail_for_load)
event.listen(ModelProviderBinding, 'refresh', get_user_detail_for_refresh)


class DefaultModelConfig(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                         UpdaterMixin, UpdateTimeMixin, CreateTimeMixin):
    __tablename__ = 'tb_default_model_config'

    workspace_id = Column(CHAR(36), nullable=False)
    group_id = Column(CHAR(36), nullable=True)
    provider_kind = Column(CHAR(36), nullable=False)
    model_type = Column(CHAR(36), nullable=False)
    model_name = Column(VARCHAR(256), nullable=False)


event.listen(DefaultModelConfig, 'before_insert', generate_uuid)
event.listen(DefaultModelConfig, 'load', get_user_detail_for_load)
event.listen(DefaultModelConfig, 'refresh', get_user_detail_for_refresh)


class LLMModel(Base, UUIDPrimaryKeyMixin, CreaterMixin,
               UpdaterMixin, UpdateTimeMixin, CreateTimeMixin):
    __tablename__ = 'tb_llm_model'

    modelId = Column(VARCHAR(128), nullable=False)
    alias = Column(VARCHAR(128), nullable=True, comment='模型别名')
    fee = Column(JSON, nullable=False, comment='费用')
    name = Column(VARCHAR(128), nullable=False, comment='模型名称')
    providerName = Column(VARCHAR(128), nullable=False, comment='供应商中文')
    providerKind = Column(VARCHAR(128), nullable=False, comment='供应商英文')
    description = Column(VARCHAR(512), nullable=False, comment='描述')
    enable = Column(BOOLEAN, nullable=False,
                    default=True, comment='是否启用')
    tag = Column(JSON, nullable=True, comment='标签')
    url = Column(VARCHAR(512), nullable=True, comment='模型说明地址')
    config = Column(JSON, nullable=True, comment='模型默认配置')
    type = Column(VARCHAR(36), nullable=False, default='llm',
                  comment='类型 llm embedding image')
    context = Column(INTEGER(10), nullable=False, default=1, comment='上下文分数')
    speed = Column(INTEGER(10), nullable=False, default=1, comment='速度分数')
    performance = Column(INTEGER(10), nullable=False,
                         default=1, comment='性能分数')
    deleted = Column(BOOLEAN, nullable=False, default=False, comment='是否删除')
    createdBy = Column(VARCHAR(36), nullable=False, comment='创建者')


event.listen(LLMModel, 'before_insert', generate_uuid)
event.listen(LLMModel, 'load', get_user_detail_for_load)
event.listen(LLMModel, 'refresh', get_user_detail_for_refresh)
