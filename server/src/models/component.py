from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VARCHAR, CHAR, BOOLEAN, JSON

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from ..schema.common import Scope
from .operator_mixin import CreaterMixin, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class Component(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                SoftDeletableMixin):
    __tablename__ = 'tb_component'

    name = Column(VARCHAR(128), nullable=False, default='', unique=True)
    code = Column(VARCHAR(128), nullable=False, unique=True)
    description = Column(VARCHAR(256), nullable=True, default=None)
    type = Column(VARCHAR(64), nullable=False)
    category_id = Column(CHAR(36), nullable=False)
    category_name = Column(VARCHAR(128), nullable=False)
    workspace_id = Column(CHAR(36), nullable=False)
    group_id = Column(CHAR(36), nullable=False)
    scope = Column(VARCHAR(20), nullable=False, default=Scope.SCOPED.value)
    config = Column(JSON, nullable=True, default=None)
    deprecated = Column(BOOLEAN, nullable=False, default='false')
    app_types = Column(JSON, nullable=True, default=None)


event.listen(Component, 'before_insert', generate_uuid)
event.listen(Component, 'load', get_user_detail_for_load)
event.listen(Component, 'refresh', get_user_detail_for_refresh)
