from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VA<PERSON><PERSON><PERSON>

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import Creater<PERSON>ix<PERSON>, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class ComponentCategory(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                        UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                        SoftDeletableMixin):
    __tablename__ = 'tb_component_category'

    name = Column(VARCHAR(128), nullable=False, default='', unique=True)
    description = Column(VARCHAR(256), nullable=True, default=None)


event.listen(ComponentCategory, 'before_insert', generate_uuid)
event.listen(ComponentCategory, 'load', get_user_detail_for_load)
event.listen(ComponentCategory, 'refresh', get_user_detail_for_refresh)
