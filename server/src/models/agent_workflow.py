from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import <PERSON><PERSON><PERSON><PERSON>, CHAR, \
    JSON, INTEGER

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import CreateTimeMixin, generate_uuid
from .operator_mixin import Creater<PERSON>ix<PERSON>, get_user_detail_for_load, get_user_detail_for_refresh


class AgentWorkflowChatHistory(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                               CreateTimeMixin):
    __tablename__ = 'tb_agent_workflow_chat_history'

    app_id = Column(CHAR(36), nullable=False)
    app_config_id = Column(CHAR(36), nullable=False)
    inputs = Column(JSON, nullable=False)
    run_count = Column(INTEGER, nullable=False, default=0)
    status = Column(VARCHAR(36))


event.listen(AgentWorkflowChatHistory, 'before_insert', generate_uuid)
event.listen(AgentWorkflowChatHistory, 'load', get_user_detail_for_load)
event.listen(AgentWorkflowChatHistory, 'refresh', get_user_detail_for_refresh)


class AgentWorkflowComponentStatus(Base, UUIDPrimaryKeyMixin):
    __tablename__ = 'tb_agent_workflow_component_status'

    chat_id = Column(CHAR(36), nullable=False)
    idx = Column(INTEGER, nullable=False, default=1)
    component_id = Column(CHAR(36), nullable=False)
    component_type = Column(VARCHAR(256), nullable=False)
    status = Column(VARCHAR(36), nullable=False)
    result = Column(JSON)

event.listen(AgentWorkflowComponentStatus, 'before_insert', generate_uuid)
