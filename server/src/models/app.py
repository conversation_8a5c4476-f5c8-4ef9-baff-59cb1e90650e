from sqlalchemy import <PERSON><PERSON><PERSON>, Column, event
from sqlalchemy.dialects.mysql import VARCHAR, CHAR, \
    JSON, TEXT, INTEGER

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, SoftDeletableMixin, \
    CreateTimeMixin, generate_uuid
from .operator_mixin import CreaterMixin, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class App(Base, UUIDPrimaryKeyMixin, CreaterMixin,
          UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
          SoftDeletableMixin):
    __tablename__ = 'tb_app'

    workspace_id = Column(CHAR(36), nullable=False)
    group_id = Column(CHAR(36), nullable=False)
    description = Column(VARCHAR(256), nullable=True, default=None)
    query_sample = Column(TEXT, nullable=True, default=None)
    type = Column(CHAR(30), nullable=False, comment="agent or workflow")
    sub_type = Column(VARCHAR(256), nullable=True, default=None,
                      comment="业务类型")
    extInfo = Column(JSON, nullable=True, default=None,
                     comment="扩展字段")
    name = Column(VARCHAR(256), nullable=False)
    is_template = Column(Boolean, nullable=False, default=False)
    is_basic = Column(Boolean, nullable=False, default=False)
    app_config_id = Column(CHAR(36), nullable=True)


event.listen(App, 'before_insert', generate_uuid)
event.listen(App, 'load', get_user_detail_for_load)
event.listen(App, 'refresh', get_user_detail_for_refresh)


class VersionedAppConfig(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                         UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                         SoftDeletableMixin):
    __tablename__ = 'tb_versioned_app_config'

    app_id = Column(CHAR(36), nullable=False)
    hash = Column(CHAR(64), nullable=False)
    type = Column(CHAR(30), nullable=False, comment="snapshot or history")
    config = Column(JSON, nullable=False)
    components = Column(JSON, nullable=True)
    status = Column(INTEGER(20), nullable=False, default=2,
                    comment="0: success, 1: failed, 2: unknown")
    message = Column(VARCHAR(256), nullable=True, default=None)
    settingId = Column(CHAR(36), nullable=True)


class VersionedAppConfigComponents(Base, UUIDPrimaryKeyMixin):
    __tablename__ = 'tb_versioned_app_config_components'

    app_id = Column(CHAR(36), nullable=False)
    app_config_id = Column(CHAR(36), nullable=False)
    component_id = Column(CHAR(36), nullable=False)


event.listen(VersionedAppConfigComponents, 'before_insert', generate_uuid)
event.listen(VersionedAppConfig, 'before_insert', generate_uuid)
event.listen(VersionedAppConfig, 'load', get_user_detail_for_load)
event.listen(VersionedAppConfig, 'refresh', get_user_detail_for_refresh)
