from click import prompt
from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VARCHAR, JSON, CHAR, TEXT

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import CreaterMixin, UpdaterMixin


class PromptTemplate(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                     UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                     SoftDeletableMixin):
    __tablename__ = 'tb_prompt_template'
    name = Column(VARCHAR(128), unique=True, nullable=False)
    description = Column(VARCHAR(256), nullable=True, default=None)
    avatar_url = Column(VARCHAR(128), nullable=False)
    workspace_id = Column(CHAR(36), nullable=False,
                          comment="reference to workspace")
    group_id = Column(CHAR(36), nullable=True, comment="reference to group")
    struct_prompt = Column(JSON, nullable=True, default=None)
    text_prompt = Column(TEXT, nullable=False)
    prompt_type = Column(VARCHAR(36), nullable=False)
    app_type = Column(VARCHAR(36), nullable=True)
    scope = Column(VARCHAR(20), nullable=False, default='scoped')


event.listen(PromptTemplate, 'before_insert', generate_uuid)
# event.listen(WorkflowDataType, 'load', get_user_detail_for_load)
# event.listen(WorkflowDataType, 'refresh', get_user_detail_for_refresh)
