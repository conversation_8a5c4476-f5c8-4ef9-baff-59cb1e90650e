from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import <PERSON><PERSON><PERSON><PERSON>, <PERSON>SO<PERSON>

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import UpdateTimeMixin, \
    CreateTimeMixin, SoftDeletableMixin, generate_uuid
from .operator_mixin import Creater<PERSON><PERSON><PERSON>, UpdaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class WorkflowDataType(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                       UpdaterMixin, UpdateTimeMixin, CreateTimeMixin,
                       SoftDeletableMixin):
    __tablename__ = 'tb_workflow_datatype'

    name = Column(VARCHAR(128), nullable=False, default='', unique=True)
    config = Column(JSON, nullable=True, default=None)
    description = Column(VARCHAR(256), nullable=True, default=None)


event.listen(WorkflowDataType, 'before_insert', generate_uuid)
event.listen(WorkflowDataType, 'load', get_user_detail_for_load)
event.listen(WorkflowDataType, 'refresh', get_user_detail_for_refresh)
