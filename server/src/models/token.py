from sqlalchemy import Column, event
from sqlalchemy.dialects.mysql import VA<PERSON><PERSON><PERSON>, DATETIME, CHAR

from .base import Base
from .account import UUIDPrimaryKeyMixin
from .common import CreateTimeMixin, generate_uuid
from .operator_mixin import CreaterMixin, \
    get_user_detail_for_load, get_user_detail_for_refresh


class Token(Base, UUIDPrimaryKeyMixin, CreaterMixin,
            CreateTimeMixin):
    __tablename__ = 'tb_token'

    value = Column(VARCHAR(256), nullable=False, default='', unique=True)
    last_used_at = Column(DATETIME, nullable=True)
    resource_type = Column(VARCHAR(10), nullable=False,
                           comment="workspace/group/app/dataset etc")
    resource_id = Column(CHAR(36), nullable=False)


class TokenRelatedResource(Base, UUIDPrimaryKeyMixin, CreaterMixin,
                           CreateTimeMixin):
    __tablename__ = 'tb_token_related_resource'

    token_id = Column(CHAR(36), nullable=False)
    resource_type = Column(VARCHAR(10), nullable=False,
                           comment="workspace/group/app/dataset etc")
    resource_id = Column(CHAR(36), nullable=False)


event.listen(TokenRelatedResource, 'before_insert', generate_uuid)
event.listen(Token, 'before_insert', generate_uuid)
event.listen(Token, 'load', get_user_detail_for_load)
event.listen(Token, 'refresh', get_user_detail_for_refresh)
