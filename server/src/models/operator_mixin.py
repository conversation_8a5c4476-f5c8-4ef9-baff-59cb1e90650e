import json
from sqlalchemy import Column
from sqlalchemy.orm import QueryContext
from sqlalchemy.dialects.mysql \
    import CHAR

from ..misc.account import get_or_set_account

from ..schema.token import is_temporary_user

from .account import Account
from ..schema.account import is_outter_user


def get_user_detail_for_load(target, context: QueryContext):
    '''
    :param target: the instance of the model
    :param context: the context of the query
    get the related user from db, by the user id loaded
    '''

    if isinstance(target, CreaterMixin) \
       and hasattr(target, 'created_by') \
       and target.created_by is not None \
       and not is_outter_user(str(target.created_by)) \
       and not is_temporary_user(str(target.created_by)) \
       and context is not None:
        target.createdBy = get_or_set_account(
            target.created_by, context.session)

    if isinstance(target, UpdaterMixin) \
       and hasattr(target, 'updated_by') \
       and target.updated_by is not None \
       and not is_outter_user(str(target.updated_by)) \
       and not is_temporary_user(str(target.updated_by)) \
       and context is not None:
        target.updatedBy = get_or_set_account(
            target.updated_by, context.session)

    if isinstance(target, GrandedByMixin) \
       and hasattr(target, 'granted_by') \
       and target.granted_by is not None \
       and context is not None:
        target.grantedBy = get_or_set_account(
            target.granted_by, context.session)


def get_user_detail_for_refresh(target, context: QueryContext, attrs):
    return get_user_detail_for_load(target, context)


class CreaterMixin:
    created_by = Column(CHAR(36), nullable=False, name='created_by')
    createdBy: Account


class UpdaterMixin:
    updated_by = Column(CHAR(36), nullable=False, name='updated_by')
    updatedBy: Account


class GrandedByMixin:
    granted_by = Column(CHAR(36), nullable=False, name='granted_by')
    grantedBy: Account
