from fastapi import APIRouter, Depends, Request, Query
from typing import Annotated

from sqlalchemy.orm import Session

from ..misc.middleware import ValidationErrorLoggingRoute
from ..misc.session import SessionUser
from ..schema.knowledge import Knowledge, KnowledgeCreateBatch, KnowledgeUpdate, KnowledgeCreate
from ..schema.common import Pages, ResponseData, Items
from ..misc.db import get_db
from ..misc.authz import admit_by_cookie
from ..service.knowledge import get as get_knowledge_service, \
    update as update_knowledge_service, \
    delete_with_check as delete_knowledge_service, create_knowledge_with_member, list_knowledge_by_workspace


router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/knowledge/{knowledge_id}', tags=['knowledge'])
async def get_knowledge(knowledge_id: str, db=Depends(get_db)) -> ResponseData[Knowledge]:
    return ResponseData(data=Knowledge.model_validate(
        await get_knowledge_service(db, knowledge_id)))


@router.put('/knowledge/{knowledge_id}', tags=['knowledge'])
async def update_knowledge(knowledge_id: str, knowledge_update: KnowledgeUpdate, request: Request,
                           db=Depends(get_db)) -> ResponseData[Knowledge]:
    user: SessionUser = request.user
    db_knowledge = await update_knowledge_service(db, user.id, knowledge_id, knowledge_update)
    return ResponseData(data=Knowledge.model_validate(db_knowledge))


@router.delete('/knowledge/{knowledge_id}', tags=['knowledge'], status_code=204)
async def delete_knowledge(knowledge_id: str, request: Request, db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_knowledge_service(db, user.id, knowledge_id)
    return 'ok'


@router.get("/group/{group_id}/knowledge", dependencies=[Depends(admit_by_cookie)], tags=['group', "knowledge"])
async def list_knowledges(group_id: str,
                          pages: Annotated[Pages, Depends(Pages)],
                          ids=Query(
                              None, alias="ids"),
                          groupId: str = Query(
                              None, alias="groupId"),
                          workspaceId: str = Query(
                              None, alias="workspaceId"),
                          keyword: str = Query(
                              None, alias="keyword"),
                          db: Session = Depends(get_db)) \
        -> ResponseData[Items[Knowledge]]:
    if ids is not None and len(ids) > 0:
        ids = ids.split(',')

    (db_knowledges, count) = await list_knowledge_by_workspace(db,
                                                               workspace_id=workspaceId,
                                                               ids=ids,
                                                               group_id=groupId,
                                                               keyword=keyword,
                                                               limit=pages.limit,
                                                               offset=pages.offset)
    return ResponseData.new_items(items=[
        Knowledge.model_validate(db_knowledge) for db_knowledge in db_knowledges
    ], total=count)


@router.post("/group/{group_id}/knowledge-batch", dependencies=[Depends(admit_by_cookie)],
             tags=['group', "knowledge"], status_code=201)
async def create_knowledge_batch(group_id: str,
                                 knowledge_create: KnowledgeCreateBatch,
                                 request: Request,
                                 db: Session = Depends(get_db)):
    user: SessionUser = request.user
    knowledge_list = []
    for item in knowledge_create.items:
        db_knowledge = await create_knowledge_with_member(db=db, user_id=user.id, knowledge_create=KnowledgeCreate(
            name=item.name or '',
            description='ada导入',
            collection_id=item.collection_id,
            group_id=group_id,
            workspace_id=knowledge_create.workspace_id
        ))
        knowledge_list.append({
            'id': db_knowledge.id,
            'name': db_knowledge.name,
            'description': db_knowledge.description,
            'collection_id': db_knowledge.collection_id,
            'group_id': db_knowledge.group_id,
            'workspace_id': db_knowledge.workspace_id
        })
    return ResponseData(data={'items': knowledge_list})


@router.post("/group/{group_id}/knowledge", dependencies=[Depends(admit_by_cookie)],
             tags=['group', "knowledge"], status_code=201)
async def create_knowledge(group_id: str,
                           knowledge_create: KnowledgeCreate,
                           request: Request,
                           db: Session = Depends(get_db)) -> ResponseData[Knowledge]:
    user: SessionUser = request.user
    db_knowledge = await create_knowledge_with_member(db, user.id, knowledge_create)
    db_knowledge.documentCount = 0
    knowledge = Knowledge.model_validate(db_knowledge)
    return ResponseData(data=knowledge)
