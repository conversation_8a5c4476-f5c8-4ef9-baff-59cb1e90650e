from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, Query

from ..misc.middleware import ValidationErrorLoggingRoute

from ..misc.db import get_db

from ..schema.plugin import Plugin

from ..schema.common import Items, Pages, ResponseData

from ..misc.authz import admit_by_cookie
from ..lib.toolpool import get_tool_pool


router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/plugin',
            dependencies=[Depends(admit_by_cookie)],
            tags=['plugin'], response_model_exclude_none=True)
async def list_plugin(
        pages: Annotated[Pages, Depends(Pages)],
        workspace_id: Annotated[Optional[str],
                                Query(alias="workspaceID")] = None,
        tool_ids: Annotated[Optional[List[str]],
                            Query(alias="toolID")] = None,
        db=Depends(get_db)
) -> ResponseData[Items[Plugin]]:

    tool_pool = await get_tool_pool()
    plugins, count = await tool_pool.list_v2(
        db=db,
        ids=tool_ids,
        workspace_id=workspace_id,
        page_size=pages.page_size,
        page_number=pages.page_number
    )

    return ResponseData.new_items(items=plugins, total=count)
