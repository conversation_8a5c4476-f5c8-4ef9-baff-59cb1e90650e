from fastapi import APIRouter, Depends, HTTPException, Request, Response
from sqlalchemy.orm import Session

from ..schema.common import ResponseData

from ..misc.authz import admit_by_cookie

from ..misc.session import SessionUser

from ..schema.apollo import (
    CreateApproveRequest,
    ResetApproveRequest,
    WithdrawApproveRequest,
    ApproveDetailRequest,
    ApproveResponse
)
from ..service import apollo as apollo_service
from ..misc.db import get_db

router = APIRouter(prefix="/apollo", tags=["apollo"])


@router.post("/approve", dependencies=[Depends(admit_by_cookie)],
             status_code=200, tags=['apollo'], response_model_exclude_none=True)
async def create_approve(
    params: CreateApproveRequest,
    request: Request,
):
    user: SessionUser = request.user
    try:
        params.operator = user.name
        res = await apollo_service.create_approve(params)
        return ResponseData(data=res)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/approve/reset", dependencies=[Depends(admit_by_cookie)],
             status_code=200, tags=['apollo'], response_model_exclude_none=True)
async def reset_approve(
    params: ResetApproveRequest,
    request: Request,
    db: Session = Depends(get_db),
):
    user: SessionUser = request.user
    try:
        return await apollo_service.reset_approve(db, params)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/approve/withdraw", dependencies=[Depends(admit_by_cookie)],
             status_code=200, tags=['apollo'], response_model_exclude_none=True)
async def withdraw_approve(
    params: WithdrawApproveRequest,
    request: Request,
    db: Session = Depends(get_db),
):
    user: SessionUser = request.user
    try:
        return await apollo_service.withdraw_approve(db, params)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/approve/detail", dependencies=[Depends(admit_by_cookie)],
             status_code=200, tags=['apollo'], response_model_exclude_none=True)
async def approve_detail(
    params: ApproveDetailRequest,
    request: Request,
    db: Session = Depends(get_db),
):
    user: SessionUser = request.user
    try:
        return await apollo_service.approve_detail(db, params)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
