from typing import Annotated, Optional
from fastapi import APIRouter, Depen<PERSON>, <PERSON><PERSON>, Query, Request
from loguru import logger
from datetime import datetime

from pydantic import AliasChoices

from ..service.dialog import agentw_chat_v2, chat_v2, completion_v2

from ..misc.middleware import ValidationErrorLoggingRoute

from ..schema.app import Agent<PERSON>orkflowDebug, AgentWorkflowRun, AppGet

from ..service.depends import validate_and_get_ctx, Context

from ..misc.errors import BadRequest
from ..schema.app import AppTriggerRequest, AppTriggerResponse, AppWorkflowRunDetailGet
from ..misc.metrics import METRICS_APP_TRIGGER_COUNTER
from ..config import settings

from ..schema.common import ResourceType

from ..misc.authz import admit_by_token, admit_by_token_for_service
from ..schema.dialog import \
    CompletionRequest, \
    ConversationRequest, \
    ConversationUpdate
from ..misc.db import get_db
from ..schema.common import Pages, ResponseData
from .dialog import CONTENT_TYPE_SSE, list_conversation as list_conversation_inner, \
    update_conversation as update_conversation_inner, \
    chat as chat_inner, \
    agentw_chat as agentw_chat_inner, \
    completion as completion_inner, \
    list_messages as list_messages_inner
from ..service.chat import delete_conversation as delete_conversation_service
from ..service.app import trigger as trigger_app_service, \
    get_workflow_run as get_workflow_run_service, \
    get as get_app_service, \
    get_config as get_app_config_service

router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/conversation', tags=['service'])
async def list_conversation(
        pages: Annotated[Pages, Depends(Pages)],
        ctx: Context = Depends(validate_and_get_ctx),
        conversation_id: Annotated[Optional[str],
                                   Query(alias='conversationID')] = None,
        db=Depends(get_db),
):
    if ctx.user is None:
        raise BadRequest("param user is required when listing conversation")
    return await list_conversation_inner(
        app_id=ctx.app_id,
        user_id=ctx.user,
        conversation_id=conversation_id,
        pages=pages,
        db=db,
    )


@router.delete('/conversation', tags=['service'],
               status_code=204)
async def delete_conversation(
        conversation_id: Annotated[str, Query(alias='conversationID')],
        ctx: Context = Depends(validate_and_get_ctx),
        db=Depends(get_db),
):
    await delete_conversation_service(
        conversation_id=conversation_id,
        db=db,
        user_id=ctx.user,
        app_id=ctx.app_id,
    )


@router.put('/conversation', tags=['service'])
async def update_conversation(
        conversation_id: Annotated[str, Query(alias='conversationID')],
        conversation_update: ConversationUpdate,
        ctx: Context = Depends(validate_and_get_ctx),
        db=Depends(get_db),
):
    if ctx.user is None:
        raise BadRequest("param user is required when updating conversation")
    return await update_conversation_inner(
        conversation_id=conversation_id,
        db=db,
        conversation_update=conversation_update,
        user_id=ctx.user,
    )


@router.post("/agentw-chat", tags=['service'])
async def agent_workflow_chat(
    run_param: AgentWorkflowRun,
    ctx: Context = Depends(validate_and_get_ctx),
    db=Depends(get_db),
    authorization: Annotated[Optional[str], Header()] = None,
    no_proxy: Annotated[bool, Header(alias="x-no-proxy")] = False,
):
    if ctx.user is None:
        raise BadRequest("param user is required when chatting")
    if not no_proxy and settings.use_service_proxy:
        res = await agentw_chat_v2(
            app_id=ctx.app_id,
            inputs=run_param.inputs,
            user_id=ctx.user,
            authorization=authorization,
            chat_id=run_param.chat_id,
        )
    else:
        res = await agentw_chat_inner(
            debug_param=AgentWorkflowDebug(
                **run_param.dict(),
                chatID=run_param.chat_id,
            ),
            app_id=ctx.app_id,
            user_id=ctx.user,
            db=db,
        )
    return res


@router.post("/chat", tags=['service'])
async def chat(
        params: ConversationRequest,
        conversation_id: Annotated[Optional[str],
                                   Query(alias='conversationID')] = None,
        accept: Annotated[str, Header(alias="accept")] = CONTENT_TYPE_SSE,
        no_proxy: Annotated[bool, Header(alias="x-no-proxy")] = False,
        authorization: Annotated[Optional[str], Header()] = None,
        ctx: Context = Depends(validate_and_get_ctx),
        db=Depends(get_db),
):
    start_time = datetime.now()
    if ctx.user is None:
        raise BadRequest("param user is required when chatting")
    logger.info('/app/chat {app_id} conversation {params}',
                uid='uid', params=params.json(), app_id=ctx.app_id)

    if not no_proxy and settings.use_service_proxy and CONTENT_TYPE_SSE not in accept:
        responses = await chat_v2(
            app_id=ctx.app_id,
            conversation_id=conversation_id,
            user_id=ctx.user,
            chat_request=params,
            authorization=authorization,
        )
    else:
        responses = await chat_inner(
            app_id=ctx.app_id,
            params=params,
            accept=accept,
            conversation_id=conversation_id,
            user_id=ctx.user,
            db=db,
        )
    end_time = datetime.now()
    time_comsumption = int((end_time - start_time).total_seconds() * 1000)
    logger.info('/chat应用：{app_id} 总耗时: {cost1}', uid='uid',
                app_id=ctx.app_id, cost1=time_comsumption)
    return responses


@router.post("/completion", tags=['service'])
async def completion(
        params: CompletionRequest,
        accept: Annotated[str, Header(alias="accept")] = CONTENT_TYPE_SSE,
        authorization: Annotated[Optional[str], Header()] = None,
        ctx: Context = Depends(validate_and_get_ctx),
        db=Depends(get_db),
):
    if ctx.user is None:
        raise BadRequest("param user is required when chatting")
    logger.info('/app/completion {app_id} conversation {params}',
                uid=ctx.user, params=params.json(), app_id=ctx.app_id)
    if settings.use_service_proxy and CONTENT_TYPE_SSE not in accept:
        responses = await completion_v2(
            app_id=ctx.app_id,
            completion_request=params,
            authorization=authorization,
            user_id=ctx.user,
        )
    else:
        responses = await completion_inner(
            app_id=ctx.app_id,
            accept=accept,
            params=params,
            user_id=ctx.user,
            db=db,
        )
    return responses


@router.get("/message", tags=['service'],
            dependencies=[Depends(admit_by_token)])
async def list_messages(
        conversation_id: Annotated[str, Query(alias='conversationID')],
        pages: Pages = Depends(Pages),
        db=Depends(get_db),
):
    return await list_messages_inner(
        conversation_id=conversation_id,
        pages=pages,
        db=db)


@router.post("/app/trigger", status_code=200, tags=['service'],
             response_model_exclude_none=True)
async def trigger_app(
        request: Request,
        app_trigger_request: AppTriggerRequest,
        db=Depends(get_db)) -> ResponseData[AppTriggerResponse]:
    await admit_by_token_for_service(db=db,
                                     request=request,
                                     resource_type=ResourceType.APP,
                                     resource_id=app_trigger_request.app_id)
    app = await get_app_service(db, app_trigger_request.app_id)
    try:
        resp = await trigger_app_service(db=db,
                                         app_id=app_trigger_request.app_id,
                                         trigger_request=app_trigger_request)
        METRICS_APP_TRIGGER_COUNTER.labels(app.name, app.id, 200).inc(1)
    except Exception as e:
        METRICS_APP_TRIGGER_COUNTER.labels(app.name, app.id, 500).inc(1)
        raise e

    return ResponseData(data=AppTriggerResponse.model_validate(resp))


@router.get("/app/workflow-runs", status_code=200, tags=['service'],
            response_model_exclude_none=True)
async def get_app_workflow_run(request: Request,
                               app_id: str = Query(alias="appID"),
                               run_id: str = Query(alias="runID"),
                               db=Depends(get_db)) -> ResponseData[AppWorkflowRunDetailGet]:
    await admit_by_token_for_service(db=db,
                                     request=request,
                                     resource_type=ResourceType.APP,
                                     resource_id=app_id)
    run_detail = await get_workflow_run_service(db=db, app_id=app_id, run_id=run_id, debug=False)
    return ResponseData(data=AppWorkflowRunDetailGet.model_validate(run_detail))


@router.get("/app", tags=['app'], response_model_exclude_none=True)
async def get_app(
        ctx: Context = Depends(validate_and_get_ctx),
        db=Depends(get_db)) -> ResponseData[AppGet]:
    db_app = await get_app_service(db=db, app_id=ctx.app_id)
    db_config = await get_app_config_service(db, db_app.app_config_id)  # type: ignore # noqa
    db_app.config = db_config.config
    return ResponseData(data=AppGet.model_validate(db_app))
