from fastapi import APIRouter
from . import dialog, oauth, workspace, account, member, \
    group, app, model, service, token, metric, \
    component_category, workflow_engine, workflow_datatype, \
    health, component, env_variable, plugin, pprof, proxy, knowledge, \
    document, document_task, curl_cffi_api, prompt_template, prompt_tools, system, setting, \
    apollo, aigw, llm_model, overmind, aigc


def register(router: APIRouter) -> None:
    router.include_router(service.router)
    oauth.register_route(router)
    router.include_router(account.router)
    router.include_router(health.router)
    router.include_router(workspace.router)
    router.include_router(member.router)
    router.include_router(group.router)
    router.include_router(app.router)
    router.include_router(model.router)
    router.include_router(dialog.router)
    router.include_router(token.router)
    router.include_router(metric.router)
    router.include_router(component_category.router)
    router.include_router(workflow_engine.router)
    router.include_router(workflow_datatype.router)
    router.include_router(component.router)
    router.include_router(env_variable.router)
    router.include_router(plugin.router)
    router.include_router(pprof.router)
    router.include_router(knowledge.router)
    router.include_router(document.router)
    router.include_router(proxy.router)
    router.include_router(document_task.router)
    router.include_router(curl_cffi_api.router)
    router.include_router(prompt_template.router)
    router.include_router(setting.router)
    router.include_router(prompt_tools.router)
    router.include_router(system.router)
    router.include_router(apollo.router)
    router.include_router(aigw.router)
    router.include_router(llm_model.router)
    router.include_router(overmind.router)
    router.include_router(aigc.router)
