from venv import create
from pydantic import AliasChoices, Field

# from ..models.app import VersionedAppConfig
from ..schema.setting import SettingConfig
from ..schema.app import SaveSettingConfig, SaveSettingRequest, UpdateSettingRequest, DeleteSettingRequest
from ..misc.errors import LangbaseEx<PERSON>
from typing import Annotated, Optional
from fastapi import APIRouter, Depends, Request, Query
from ..misc.session import SessionUser
from ..misc.middleware import ValidationErrorLoggingRoute
from ..schema.common import ResponseData
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db

from ..service.setting import list_setting_service, \
    get_setting_lastest_config_service, \
    get_setting_all_snapshot_config_service, \
    create_setting_config as create_setting_config_service, \
    delete_setting as delete_setting_service, \
    update_setting as update_setting_service
from ..service.app import save_setting_config_service

router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/app/{app_id}/setting',
            dependencies=[Depends(admit_by_cookie)],
            tags=['app-setting'],
            description="Get Setting List")
async def list_setting(
    request: Request,
    app_id: str,
    db=Depends(get_db)
) -> ResponseData:

    (settings, total) = await list_setting_service(
        db,
        appId=app_id,
    )

    return ResponseData(
        data={
            "items": settings,
            "total": total,
        }
    )


@router.post('/app/{app_id}/setting',
             dependencies=[Depends(admit_by_cookie)],
             tags=['app-setting'],
             description="create Setting")
async def create_setting(
    request: Request,
    app_id: str,
    create_setting: SaveSettingRequest,
    db=Depends(get_db)
) -> ResponseData:

    setting_config = await create_setting_config_service(
        db,
        app_id=create_setting.appId,
        name=create_setting.name,
        creater_id=request.user.id,
        description=create_setting.description,
        extInfo=create_setting.extInfo,
    )

    return ResponseData(
        data=setting_config
    )


@router.put('/app/{app_id}/setting',
            dependencies=[Depends(admit_by_cookie)],
            tags=['app-setting'],
            description="update Setting")
async def update_setting(
    request: Request,
    app_id: str,
    update_setting: UpdateSettingRequest,
    db=Depends(get_db)
) -> ResponseData:

    setting_config = await update_setting_service(
        db,
        app_id=update_setting.appId,
        settingId=update_setting.settingId,
        creater_id=request.user.id,
        name=update_setting.name,
        description=update_setting.description,
        extInfo=update_setting.extInfo,
    )

    return ResponseData(
        data=setting_config
    )


@router.delete('/app/{app_id}/setting',
               #    dependencies=[Depends(admit_by_cookie)],
               tags=['app-setting'],
               description="delete Setting")
async def delete_setting(
    delete_setting: DeleteSettingRequest,
    db=Depends(get_db)
) -> ResponseData:

    # print(delete_setting)

    (setting) = await delete_setting_service(
        db,
        settingId=delete_setting.settingId
    )

    return ResponseData(
        data=setting
    )


@router.get('/app/{app_id}/setting-config',
            dependencies=[Depends(admit_by_cookie)],
            tags=['setting'],
            description="Get Setting List")
async def get_setting_config(
    settingId: str,
    db=Depends(get_db)
) -> ResponseData:

    db_setting_lastest_config = await get_setting_lastest_config_service(
        db,
        settingId=settingId,
    )

    return ResponseData(
        data=db_setting_lastest_config
    )


@router.get('/app/{app_id}/setting-versioned-config',
            dependencies=[Depends(admit_by_cookie)],
            tags=['setting'],
            description="Get Setting List")
async def get_all_snapshot_setting_config(
    settingId: str,
    page: int = 1,
    pageSize: int = 100,
    db=Depends(get_db)
) -> ResponseData:

    configs, count = await get_setting_all_snapshot_config_service(
        db,
        settingId=settingId,
        page=page,
        pageSize=pageSize
    )

    return ResponseData(
        data={
            "list": configs,
            "total": count
        }
    )


@router.post('/app/{app_id}/setting-config',
             dependencies=[Depends(admit_by_cookie)],
             tags=['setting'],
             description="Get Setting List")
async def save_setting_config(
        request: Request,
        save_config: SaveSettingConfig,
        db=Depends(get_db)) -> ResponseData:
    user: SessionUser = request.user

    db_config = await save_setting_config_service(db=db,
                                                  user_id=user.id,
                                                  appId=save_config.appId,
                                                  settingId=save_config.settingId,
                                                  config=save_config.config)

    db_config_dict = {
        "config": db_config.config,
    }

    return ResponseData(data=db_config_dict)
