from fastapi import APIRouter
import json
import sys
import uuid
import time
import logging
from curl_cffi import requests

from typing import List, Optional, Union, Any, Dict
from pydantic import BaseModel
from io import BytesIO
from ..config import settings
from ..service.s3 import upload_file_v2 as app_upload_file_service
from loguru import logger
from ..misc.log import logger_info

from concurrent.futures import ThreadPoolExecutor, as_completed


class CurlCffiRequest(BaseModel):
    cUrl: str
    headerParam: Dict[str, Any]


class BatchCurlCffiRequest(BaseModel):
    urlList: List[str]
    headerParam: Dict[str, Any]


router = APIRouter()


@router.post('/curl/cffi')
def curl_cffi_api(request: CurlCffiRequest):
    url: str = request.cUrl
    headers: Dict = request.headerParam
    result = do_curl_cffi(url, headers)
    return result


def do_curl_cffi(url: str, headers: Dict) -> dict:
    result = {}
    start_time = time.time()
    res = requests.get(url, headers=headers, impersonate='chrome124')
    result["code"] = res.status_code
    result["res"] = res.text
    result["content"] = res.content.hex()
    # 记录结束时间
    end_time = time.time()
    # 计算并打印耗时
    elapsed_time = end_time - start_time
    logger_info(f'方法执行耗时：{elapsed_time}秒')
    return result


@router.post('/download/cffi')
def downloadAndUpload(request: BatchCurlCffiRequest):
    start_time = time.time()
    result = {}
    requests: list[CurlCffiRequest] = []
    for url in request.urlList:
        requests.append(CurlCffiRequest(
            cUrl=url, headerParam=request.headerParam))
    # task_results = pool.map(task, requests)
    task_results = []
    with ThreadPoolExecutor(max_workers=4) as pool:
        futures = [pool.submit(task, request) for request in requests]
        for future in as_completed(futures):
            task_results.append(future.result())
    result["code"] = 200
    result["data"] = task_results
    elapsed_time = time.time() - start_time
    logger_info(
        f'{json.dumps(request.urlList)},downloadAndUpload总耗时：{elapsed_time}秒')
    return result


def task(request: CurlCffiRequest):
    result = {}
    url: str = request.cUrl
    headers: Dict = request.headerParam
    try:
        start_time = time.time()
        resp = requests.get(url, headers=headers, impersonate='chrome124')
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger_info(f'{url},下载执行耗时：{elapsed_time}秒')
        if resp.status_code == 200:
            bucket: str = 'jdymusic'
            key: str = str(uuid.uuid4())
            app_upload_file_service(s3_config=settings.workflow_config.s3,
                                    bucket=bucket,
                                    key=key,
                                    body=BytesIO(resp.content))
            result['url'] = url
            result['nosKey'] = bucket + "/" + key
        else:
            logger_info(f'{url},下载返回非200：{resp.status_code}')
            result['url'] = url
    except Exception as e:
        # 处理异常，记录日志，返回错误信息
        error_msg = str(e)
        logger_info(f'{url},下载异常：{error_msg}')
        result['url'] = url
    return result
