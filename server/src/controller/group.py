from fastapi import APIRouter, Depends, Request

from .workspace import get_workspace_admins

from ..schema.member import MemberGet

from ..misc.middleware import ValidationErrorLoggingRoute
from ..schema.member import MemberType

from ..misc.session import SessionUser
from ..schema.group import Group, GroupUpdate
from ..schema.common import Items, ResponseData
from ..misc.db import get_db
from ..misc.authz import admit_by_cookie
from ..service.group import get as get_group_service, sync_groups_to_redis, \
    update as update_group_service, \
    delete_with_check as delete_group_service, \
    get_group_admins as get_group_admins_service, \
    access_group as access_group_service

from ..service.account import list as list_account_service

router = APIRouter(prefix='/group', route_class=ValidationErrorLoggingRoute)


@router.get("/sync", tags=['group', "group"])
async def sync_group(db=Depends(get_db)):
    res = await sync_groups_to_redis(db)
    return {"message": "success", "data": res}


@router.get('/{group_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['group'])
async def get_group(group_id: str,
                    db=Depends(get_db)) -> ResponseData[Group]:
    return ResponseData(data=Group.model_validate(
        await get_group_service(db, group_id)))


@router.get('/{group_id}/admins',
            tags=['group'])
async def get_group_admins(group_id: str, db=Depends(get_db)) \
        -> ResponseData[Items[MemberGet]]:
    members, total = await get_group_admins_service(db, group_id)

    member_list = []
    for member in members:
        member_list.append(MemberGet.model_validate(member))

    return ResponseData.new_items(items=member_list, total=total)


@router.post('/{group_id}/access', dependencies=[Depends(admit_by_cookie)],
             tags=['group'])
async def access_group(group_id: str,
                       request: Request,
                       db=Depends(get_db)):
    # 获取所有用户
    users = await list_account_service(db)
    for user in users:
        await access_group_service(db, group_id, user.id)
    return {"message": "Hello, World!"}


@router.put('/{group_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['group'])
async def update_group(group_id: str,
                       group_update: GroupUpdate,
                       request: Request,
                       db=Depends(get_db)) -> ResponseData[Group]:
    user: SessionUser = request.user
    db_group = await update_group_service(db, user.id, group_id, group_update)
    return ResponseData(data=Group.model_validate(db_group))


@router.delete('/{groupID}', dependencies=[Depends(admit_by_cookie)],
               tags=['group'], status_code=204)
async def delete_group(groupID: str,
                       request: Request,
                       db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_group_service(db, user.id, groupID)
