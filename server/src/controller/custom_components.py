from typing import Annotated, Optional

from fastapi import APIRouter, Depends, Request, Query

from ..misc.middleware import ValidationErrorLoggingRoute

from ..schema.common import Pages, ResponseData, Items
from ..misc.session import SessionUser
from ..schema.custom_component import CustomComponentGet, CustomComponentCreate, CustomComponentUpdate
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db
from ..service.custom_component import get as get_custom_component_service, \
    list_custom_components as list_custom_component_service, \
    list_app_custom_components as list_app_custom_component_service, \
    update as update_custom_component_service, \
    delete as delete_custom_component_service, \
    create as create_custom_component_service, \
    favorite as favorite_custom_component_service, \
    cancel_favorite as cancel_favorite_custom_component_service

router = APIRouter(route_class=ValidationErrorLoggingRoute)


# 已迁移
@router.get('/workspace/{workspace_id}/custom_components', dependencies=[Depends(admit_by_cookie)],
            tags=['custom_components'], description="list custom components")
async def list_custom_component(workspace_id: str,
                                pages: Annotated[Pages, Depends(Pages)],
                                group_id: str = Query(None, alias="groupId"),
                                categories: str = Query(
                                    None, alias="categories"),  # category names
                                # component types
                                types: str = Query(None, alias="types"),
                                app_types: str = Query(
                                    None, alias="appTypes"),  # app types
                                user_id: Optional[str] = Query(
                                    alias="userID", default=None),
                                is_favorite: bool = Query(
                                    alias="isFavorite", default=False),
                                db=Depends(get_db)) -> ResponseData[Items[CustomComponentGet]]:
    category_names = []
    if categories is not None and len(categories) > 0:
        category_names = categories.split(',')
    if types is not None and len(types) > 0:
        types = types.split(',')
    if app_types is not None:
        app_types = app_types.split(',')

    (db_custom_components, total) = await list_custom_component_service(db,
                                                                        workspace_id,
                                                                        group_id,
                                                                        category_names=category_names,
                                                                        types=types,
                                                                        app_types=app_types,
                                                                        user_id=user_id,
                                                                        is_favorite=is_favorite,
                                                                        offset=pages.offset,
                                                                        limit=pages.limit)

    return ResponseData.new_items(items=[CustomComponentGet.model_validate(custom_component)
                                         for custom_component in db_custom_components],
                                  total=total)


@router.get('/app/{app_id}/custom_components', dependencies=[Depends(admit_by_cookie)],
            tags=['custom_components'], description="list custom components")
async def list_app_custom_component(app_id: str,
                                    pages: Annotated[Pages, Depends(Pages)],
                                    categories: str = Query(
                                        None, alias="categories"),  # category names
                                    db=Depends(get_db)) -> ResponseData[Items[CustomComponentGet]]:
    category_names = []
    if categories is not None:
        category_names = categories.split(',')

    (db_custom_components, total) = await list_app_custom_component_service(db,
                                                                            app_id,
                                                                            category_names=category_names,
                                                                            offset=pages.offset,
                                                                            limit=pages.limit)

    return ResponseData.new_items(items=[CustomComponentGet.model_validate(custom_component)
                                         for custom_component in db_custom_components],
                                  total=total)


# 已迁移测试
@router.post('/workspace/{workspace_id}/custom_components', dependencies=[Depends(admit_by_cookie)],
             status_code=201, tags=['components'])
async def create_component(workspace_id: str,
                           component_create: CustomComponentCreate,
                           request: Request, db=Depends(get_db)) \
        -> ResponseData[CustomComponentGet]:
    user: SessionUser = request.user
    db_custom_component = await create_custom_component_service(db,
                                                                workspace_id,
                                                                component_create,
                                                                user_id=user.id)
    return ResponseData(data=CustomComponentGet.model_validate(db_custom_component))


# 已迁移测试
@router.get('/custom_components/{custom_component_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['custom_components'])
async def get_custom_component(custom_component_id: str, db=Depends(get_db)) \
        -> ResponseData[CustomComponentGet]:
    db_custom_component = await get_custom_component_service(db, custom_component_id)
    custom_component = CustomComponentGet.model_validate(db_custom_component)
    return ResponseData(data=custom_component)


# 迁移测试完成
@router.put('/custom_components/{custom_component_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['custom_components'])
async def update_custom_component(custom_component_id: str,
                                  custom_component_update: CustomComponentUpdate,
                                  request: Request,
                                  db=Depends(get_db)) \
        -> ResponseData[CustomComponentGet]:
    user: SessionUser = request.user
    db_custom_component = await update_custom_component_service(db,
                                                                user.id,
                                                                custom_component_id,
                                                                custom_component_update)
    return ResponseData(data=CustomComponentGet.model_validate(db_custom_component))


@router.delete('/custom_components/{custom_component_id}/favorite', dependencies=[Depends(admit_by_cookie)],
               tags=['custom_components'], status_code=200)
async def cancel_favorite_custom_component(custom_component_id: str,
                                           request: Request,
                                           db=Depends(get_db)):
    user: SessionUser = request.user
    await cancel_favorite_custom_component_service(db, user.id, custom_component_id)


@router.post('/custom_components/{custom_component_id}/favorite', dependencies=[Depends(admit_by_cookie)],
             tags=['custom_components'], status_code=200)
async def favorite_custom_component(custom_component_id: str,
                                    request: Request,
                                    db=Depends(get_db)):
    user: SessionUser = request.user
    await favorite_custom_component_service(db=db, user_id=user.id, component_id=custom_component_id)


# 已迁移
@router.delete('/custom_components/{custom_component_id}', dependencies=[Depends(admit_by_cookie)],
               tags=['custom_components'], status_code=204)
async def delete_custom_component(custom_component_id: str,
                                  request: Request,
                                  db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_custom_component_service(db, user.id, custom_component_id)
