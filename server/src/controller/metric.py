from datetime import datetime
from typing import Annotated, Optional, List
from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel

from ..misc.middleware import ValidationErrorLoggingRoute

from ..misc.authz import admit_by_cookie

from ..schema.metric import ConversationCount, MessagePerConv, Metric, MetricRequest

from ..schema.common import ResponseData

from ..misc.db import get_db
from ..service.metric import alert_fee, alert_group_fee, alert_high_fee_apps, alert_high_fee_by_date, get_app_metric, get_conversation_count_per_user \
    as get_conversation_count_per_user_service, get_excel_of_metric, get_metrics, \
    get_user_usage_count as get_user_usage_count_service, \
    get_token_count as get_token_count_service, \
    get_tokens_per_seconds as get_tokens_per_seconds_service, \
    get_message_count_per_conv as get_message_count_per_conv_service, join_mertic_data_V2, modify_fee, refresh_metrics as refresh_metrics_service, \
    add_metrics_batch, get_model_metric


router = APIRouter(route_class=ValidationErrorLoggingRoute)


class Range(BaseModel):
    start: Optional[datetime]
    end: Optional[datetime]


async def range(start: Annotated[Optional[datetime], Query()] = None,
                end: Annotated[Optional[datetime], Query()] = None):
    return Range(start=start, end=end)


@router.get("/app/{app_id}/metrics",
            dependencies=[Depends(admit_by_cookie)])
async def get_metric(
        app_id: str,
        range: Range = Depends(range),
        db=Depends(get_db)):
    items = await get_metrics(
        db=db,
        start_date=range.start,
        end_date=range.end,
        app_id=app_id,
    )
    return ResponseData(
        data=[Metric.model_validate(item) for item in items]
    )


@router.get("/app/{app_id}/conversation-per-user",
            dependencies=[Depends(admit_by_cookie)])
async def get_conversation_count(
        app_id: str,
        range: Range = Depends(range),
        db=Depends(get_db)):
    items = await get_conversation_count_per_user_service(
        db=db,
        start_date=range.start,
        end_date=range.end,
        app_id=app_id,
    )
    return ResponseData(
        data=[ConversationCount.model_validate(item) for item in items]
    )


@router.get("/app/{app_id}/user-usage-count",
            dependencies=[Depends(admit_by_cookie)])
async def get_user_usage_count(
        app_id: str,
        range: Range = Depends(range),
        db=Depends(get_db)):
    items = await get_user_usage_count_service(
        db=db,
        start_date=range.start,
        end_date=range.end,
        app_id=app_id,
    )
    return ResponseData(
        data=[ConversationCount.model_validate(item) for item in items]
    )


@router.get("/app/{app_id}/token-per-seconds",
            dependencies=[Depends(admit_by_cookie)])
async def get_tokens_per_seconds(
        app_id: str,
        range: Range = Depends(range),
        db=Depends(get_db)):
    items = await get_tokens_per_seconds_service(
        db=db,
        start_date=range.start,
        end_date=range.end,
        app_id=app_id,
    )
    return ResponseData(
        data=[ConversationCount.model_validate(item) for item in items]
    )


@router.get("/app/{app_id}/token-count",
            dependencies=[Depends(admit_by_cookie)])
async def get_token_count(
        app_id: str,
        range: Range = Depends(range),
        db=Depends(get_db)):
    items = await get_token_count_service(
        db=db,
        start_date=range.start,
        end_date=range.end,
        app_id=app_id,
    )
    return ResponseData(
        data=[ConversationCount.model_validate(item) for item in items]
    )


@router.get("/app/{app_id}/message-per-conv",
            dependencies=[Depends(admit_by_cookie)])
async def get_message_count_per_conv(
        app_id: str,
        range: Range = Depends(range),
        db=Depends(get_db)):
    items = await get_message_count_per_conv_service(
        db=db,
        start_date=range.start,
        end_date=range.end,
        app_id=app_id,
    )
    return ResponseData(
        data=[MessagePerConv.model_validate(item) for item in items]
    )


@router.post("/metric",
             dependencies=[Depends(admit_by_cookie)])
async def add_metric(
        metric: Metric,
        db=Depends(get_db)):
    items = await get_metrics(
        db=db,
        start_date=range.start,
        end_date=range.end,
        app_id=app_id,
    )
    return ResponseData(
        data=[Metric.model_validate(item) for item in items]
    )


@router.get("/do-metrics")
async def do_metrics(
    date: Optional[str] = Query(None)
):
    items = await join_mertic_data_V2(date, need_alert=False)
    return ResponseData(
        data=[Metric.model_validate(item) for item in items]
    )


@router.get("/refresh-metrics")
async def refresh_metrics():
    items, count, not_updated = await refresh_metrics_service()
    return ResponseData(
        data={
            'count': count,
            'not_updated': not_updated
        }
    )


@router.get("/metrics")
async def metrics(
        sort_order: str = Query('descend', alias="sortOrder"),
        sort_filed: str = Query('tokens', alias="sortField"),
        group_id: Optional[str] = Query('all', alias="groupId"),
        workspace_id: Optional[str] = Query('all', alias="workspaceId"),
        range: Range = Depends(range),
        db=Depends(get_db)):
    items = await get_app_metric(
        db=db,
        group_id=group_id,
        workspace_id=workspace_id,
        sort_order=sort_order,
        sort_filed=sort_filed,
        start_date=range.start,
        end_date=range.end,
    )
    return ResponseData(
        data=items
    )


@router.get("/metrics-by-model")
async def metrics_by_model(
        sort_order: str = Query('descend', alias="sortOrder"),
        sort_filed: str = Query('tokens', alias="sortField"),
        group_id: Optional[str] = Query('all', alias="groupId"),
        workspace_id: Optional[str] = Query('all', alias="workspaceId"),
        range: Range = Depends(range),
        db=Depends(get_db)):
    items = await get_model_metric(
        db=db,
        group_id=group_id,
        workspace_id=workspace_id,
        sort_order=sort_order,
        sort_filed=sort_filed,
        start_date=range.start,
        end_date=range.end,
    )
    return ResponseData(
        data=items
    )


class AlertAppsRequest(BaseModel):
    apps: List[dict]


@router.post("/alert")
async def alert(
    request: AlertAppsRequest,
    db=Depends(get_db)
):
    items = await alert_high_fee_apps(db=db, apps=request.apps)
    return ResponseData(
        data=items
    )


@router.get("/do-alert")
async def do_alert(
    date: Optional[str] = None,
):
    items = await alert_fee(date_string=date, force=True)
    return ResponseData(
        data=items
    )


@router.get("/do-group-alert")
async def do_group_alert(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    to_popo: Optional[str] = None,
    workspace_id: Optional[str] = None,
):
    items = await alert_group_fee(start_date=start_date, end_date=end_date, workspace_id=workspace_id, to_popo=to_popo)
    return ResponseData(
        data=items
    )


@router.post("/metrics")
async def add_metrics(
        request: MetricRequest,
        db=Depends(get_db)):
    """
    批量添加指标数据
    """
    items = await add_metrics_batch(db=db, items=[item.model_dump() for item in request.items])
    return ResponseData(
        data=items
    )


class ModifyFeeRequest(BaseModel):
    app_id: str
    date: datetime
    type: str
    fee: float


@router.post("/modify-fee")
async def modifyFee(
    request: ModifyFeeRequest,
    db=Depends(get_db)
):
    await modify_fee(db=db, app_id=request.app_id, date=request.date, type=request.type, fee=request.fee)
    return ResponseData(
        data='success'
    )


@router.get("/get-excel-of-metric")
async def get_excel(
    start_date: datetime,
    end_date: datetime,
    workspace_id: Optional[str] = None,
    group_id: Optional[str] = None,
    db=Depends(get_db)
):
    data = await get_excel_of_metric(db=db, start_date=start_date, end_date=end_date, workspace_id=workspace_id, group_id=group_id)
    return ResponseData(
        data={
            'nos_url': data['nos_url'],
            'group_rows': data['group_rows'],
            'app_rows': data['app_rows'],
            'model_rows': data['model_rows']
        }
    )
