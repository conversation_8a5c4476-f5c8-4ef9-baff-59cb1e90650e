from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, Query, Request, Response
from sqlalchemy.orm import Session

from ..misc.middleware import ValidationErrorLoggingRoute

from ..service.account import list as list_account_service

from ..misc.session import SessionUser

from ..models.member import Member

from ..misc.errors import NotFound, PermissionDenied

from ..schema.member import MemberCreate, MemberGet, \
    MemberType, MemberUpdated
from ..misc.db import get_db
from ..schema.rbac import RoleConfig
from ..schema.common import Items, Pages, ResponseData, ResourceType
from ..misc.authz import admit_by_cookie, enforcer
from ..service.workspace import get as get_workspace_service
from ..service.group import get as get_group_service
from ..service.app import get as get_app_service
from ..service.member import create as create_member_service, \
    delete as delete_member_service, \
    update as update_member_service, \
    list as list_member_service, \
    get_member_of_resource


router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/role', dependencies=[Depends(admit_by_cookie)], tags=['member'])
async def get_roles(request: Request) -> ResponseData[RoleConfig]:
    return ResponseData(data=enforcer.role_config)


async def update_member_inner(db: Session,
                              resource_type: ResourceType,
                              resource_id: str,
                              member_create: MemberCreate,
                              user_id: str):

    member: Optional[Member] = None
    try:
        member = await update_member_service(
            db,
            resource_type=resource_type,
            resource_id=resource_id,
            member_create=member_create,
        )
    except NotFound:
        member = await create_member_service(
            db,
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            member_create=member_create,
        )
    return member


async def validate_priority(role1: str, role2: str):
    if enforcer.role_compare(role1, role2) < 0:
        raise PermissionDenied(f"you have no permission to grant "
                               f"this role({role2})")


@router.put('/{resource_type}/{resource_id}/members', tags=['member'])
async def udpate_members(resource_type: ResourceType,
                         resource_id: str,
                         member_creates: List[MemberCreate],
                         request: Request,
                         role=Depends(admit_by_cookie),
                         db=Depends(get_db)):
    user: SessionUser = request.user
    for member_create in member_creates:
        await validate_priority(role, member_create.role)

    if resource_type == ResourceType.WORKSPACE:
        await get_workspace_service(db, resource_id)

    if resource_type == ResourceType.GROUP:
        await get_group_service(db, resource_id)

    if resource_type == ResourceType.APP:
        await get_app_service(db, resource_id)

    members = [await update_member_inner(
        db,
        resource_type=resource_type,
        resource_id=resource_id,
        member_create=member_create,
        user_id=user.id,
    ) for member_create in member_creates]

    return ResponseData(data=[MemberUpdated.model_validate(member)
                              for member in members])


@router.put('/{resource_type}/{resource_id}/member_v2', tags=['member'])
async def udpate_member_v2(resource_type: ResourceType,
                           resource_id: str,
                           member_create: MemberCreate,
                           request: Request,
                           role=Depends(admit_by_cookie),
                           db=Depends(get_db)):
    await udpate_member(resource_type, resource_id, member_create, request, is_admin=True, role=role, db=db)
    return ResponseData(data=member_create)


@router.put('/{resource_type}/{resource_id}/member', tags=['member'])
async def udpate_member(resource_type: ResourceType,
                        resource_id: str,
                        member_create: MemberCreate,
                        request: Request,
                        is_admin: Optional[bool] = False,
                        role=Depends(admit_by_cookie),
                        db=Depends(get_db)):
    user: SessionUser = request.user

    if not is_admin:
        await validate_priority(role, member_create.role)

    if resource_type == ResourceType.WORKSPACE:
        await get_workspace_service(db, resource_id)

    if resource_type == ResourceType.GROUP:
        await get_group_service(db, resource_id)

    if resource_type == ResourceType.APP:
        await get_app_service(db, resource_id)

    member = await update_member_inner(
        db,
        resource_type=resource_type,
        resource_id=resource_id,
        member_create=member_create,
        user_id=user.id,
    )
    return ResponseData(data=MemberUpdated.model_validate(member))


@router.delete('/{resource_type}/{resource_id}/member',
               tags=['member'], dependencies=[Depends(admit_by_cookie)])
async def delete_member(resource_type: ResourceType,
                        resource_id: str,
                        member_id: Annotated[str, Query(alias="memberID")],
                        member_type: Annotated[
                            MemberType,
                            Query(alias="memberType")] = MemberType.USER,
                        db=Depends(get_db)):

    member = await get_member_of_resource(
        db,
        member_type=member_type.value,
        member_id=member_id,
        resource_type=resource_type.value,
        resource_id=resource_id,
    )

    if member is None:
        return Response(status_code=204)

    # if enforcer.role_compare(role, str(member.role)) < 0:
    #     raise PermissionDenied(f"you have no permission to grant "
    #                            f"this role({member.role})")

    await delete_member_service(
        db,
        resource_type=resource_type.value,
        resource_id=resource_id,
        member_id=member_id,
        member_type=member_type.value,
    )

    return Response(status_code=204)


@router.get('/{resource_type}/{resource_id}/member', tags=['member'])
async def list_members(
        resource_type: ResourceType,
        resource_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        name: Annotated[Optional[List[str]], Query()] = None,
        role: Annotated[Optional[str], Query()] = None,
        member_type: Annotated[
            MemberType,
            Query(alias="memberType")] = MemberType.USER,
        db=Depends(get_db)):

    members, count = await list_member_service(
        db,
        member_type=member_type.value,
        resource_type=resource_type.value,
        resource_id=resource_id,
        with_detail=True,
        names=name,
        role=role,
        limit=pages.limit,
        offset=pages.offset,
    )

    # users, _ = await list_account_service(
    #     db,
    #     ids=[str(member.member_id)
    #          for member in members
    #          if member_type == MemberType.USER],
    # )

    # users_dict = {user.id: user for user in users}
    # for member in members:
    #     if member_type == MemberType.USER:
    #         member.member = users_dict.get(str(member.member_id))

    return ResponseData(
        data=Items(
            items=[MemberGet.model_validate(member)
                   for member in members],
            total=count)
    )
