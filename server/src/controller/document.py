from fastapi import APIRouter, Depends, Request, Query, Form, File, UploadFile
from typing import Annotated, Optional
import json

from ..misc.middleware import ValidationErrorLoggingRoute

from ..misc.errors import InternalServerError
from ..misc.session import SessionUser
from ..schema.document import Document, DocumentUpdate, RecallResult
from ..schema.common import ResponseData
from ..misc.db import get_db
from ..service.document import get as get_document_service, get_most_matched_docs_service, \
    update as update_document_service, \
    delete_with_check as delete_document_service, get_segment, update_ada_document
from ..service.popo_doc import parse_popo_document


router = APIRouter(prefix='/document', route_class=ValidationErrorLoggingRoute)


@router.get('/parse-popo', tags=['document'])
async def parse_popo_document_controller(popo_url: Optional[str] = Query(None), popo_title: Optional[str] = Query(None)):
    html_content = await parse_popo_document(popo_url, popo_title)
    return html_content


@router.get('/{document_id}', tags=['document'])
async def get_document(document_id: str, db=Depends(get_db)) -> ResponseData[Document]:
    return ResponseData(data=Document.model_validate(
        await get_document_service(db, document_id)))


@router.get('/{document_id}/segment', tags=['document'])
async def get_document_segment(document_id: str, collectionId: int = Query(None), docId: int = Query(None),
                               db=Depends(get_db)):
    return await get_segment(db, collectionId, docId)


@router.put('/{document_id}', tags=['document'])
async def update_document(document_id: str,
                          request: Request,
                          doc_id: Annotated[int, Form()],
                          title: Annotated[str, Form()],
                          type: Annotated[Optional[str], Form()] = None,
                          source: Annotated[Optional[str], Form()] = None,
                          file: Annotated[Optional[UploadFile], File()] = None,
                          config: Annotated[Optional[str], Form()] = None,
                          collectionId: Annotated[Optional[str], Form(
                          )] = None,
                          db=Depends(get_db)) -> ResponseData[Document]:
    user: SessionUser = request.user
    configJson = None
    if config:
        configJson = json.loads(config)
    document_update = DocumentUpdate(
        title=title, type=type, source=source, config=configJson)
    if file:
        ada_document = await update_ada_document(collectionId, docId=doc_id, files=file, importConfig=config,
                                                 username=user.name)
        if 'error' in ada_document:
            raise InternalServerError(ada_document['error']['message'])
        document_update.doc_id = ada_document['docId']
    else:
        document_update = DocumentUpdate(title=title)
    db_document = await update_document_service(db, user.id, document_id, document_update)
    return ResponseData(data=Document.model_validate(db_document))


@router.get('/ada/getMostMatchedDocs', tags=['document'], status_code=200)
async def get_most_matched_docs(input: str = Query(...), count: int = Query(...), appId: str = Query(...)):
    docs = await get_most_matched_docs_service(input, count, appId)
    return {
        'code': 200,
        'data': [RecallResult.model_validate(doc) for doc in docs],
        'message': 'success'
    }


@router.delete('/{document_id}', tags=['document'], status_code=204)
async def delete_document(document_id: str, request: Request, collectionId: int = Query(None), docId: int = Query(None),
                          db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_document_service(db, user.id, document_id, collectionId, docId)
