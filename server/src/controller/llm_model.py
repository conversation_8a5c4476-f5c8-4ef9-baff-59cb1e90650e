from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session
from typing import List

from ..lib.llms.get_models import fetch_all_models

from ..misc.http_db import get_http_db
from ..misc.middleware import ValidationErrorLoggingRoute
from ..misc.http_db import get_http_db
from ..misc.middleware import ValidationErrorLoggingRoute
from ..misc.authz import admit_by_cookie
from ..schema.common import ResponseData, Items, Pages
from ..schema.llm_model import LLMModelCreate, LLMModelUpdate, LLMModel, LLMModelsCreate
from ..service import llm_model as llm_model_service

router = APIRouter(
    tags=["LLM Models"],
    route_class=ValidationErrorLoggingRoute
)


@router.post("/llm-models", dependencies=[Depends(admit_by_cookie)])
async def create_model(
    model_data: LLMModelCreate,
    request: Request
) -> ResponseData[LLMModel]:
    user: Session = request.user
    db = get_http_db('tb_llm_model')
    db_model = await llm_model_service.create(db, model_data, user.id)
    # 刷新redis中的模型
    await fetch_all_models(force_refresh=True)
    return ResponseData(data=LLMModel.model_validate(db_model))


@router.post("/llm-models/batch", dependencies=[Depends(admit_by_cookie)])
async def create_models(
    request: Request,
    models: LLMModelsCreate
) -> ResponseData[Items[LLMModel]]:
    user: Session = request.user
    db = get_http_db('tb_llm_model')
    db_models = []
    for model in models.values:
        db_model = await llm_model_service.create(db, model, user.id)
        db_models.append(LLMModel.model_validate(db_model))
    # 刷新redis中的模型
    await fetch_all_models(force_refresh=True)
    return ResponseData.new_items(
        items=db_models,
        total=len(db_models)
    )


@router.get("/llm-models/{model_id}", dependencies=[Depends(admit_by_cookie)])
async def get_model(
    model_id: str,
    db: Session = Depends(get_http_db)
) -> ResponseData[LLMModel]:
    db_model = await llm_model_service.get(db, model_id)
    return ResponseData(data=LLMModel.model_validate(db_model))


@router.get("/llm-models", dependencies=[Depends(admit_by_cookie)])
async def list_models(
    pages: Pages = Depends(Pages),
    db: Session = Depends(get_http_db)
) -> ResponseData[Items[LLMModel]]:
    db_models, count = await llm_model_service.list(
        db,
        limit=pages.limit,
        offset=pages.offset
    )
    return ResponseData.new_items(
        items=[LLMModel.model_validate(m) for m in db_models],
        total=count
    )


@router.get("/llm-models-no-auth")
async def list_models_no_auth(
    pages: Pages = Depends(Pages),
    db: Session = Depends(get_http_db)
) -> ResponseData[Items[LLMModel]]:
    db_models, count = await llm_model_service.list(
        db,
        limit=pages.limit,
        offset=pages.offset
    )
    return ResponseData.new_items(
        items=[LLMModel.model_validate(m) for m in db_models],
        total=count
    )


@router.put("/llm-models/{model_id}", dependencies=[Depends(admit_by_cookie)])
async def update_model(
    model_id: str,
    model_data: LLMModelUpdate,
    db: Session = Depends(get_http_db)
) -> ResponseData[LLMModel]:
    db_model = await llm_model_service.update(db, model_id, model_data)
    # 刷新redis中的模型
    await fetch_all_models(force_refresh=True)
    return ResponseData(data=LLMModel.model_validate(db_model))


@router.get("/llm-models-modified")
async def list_models_modified(
    db: Session = Depends(get_http_db)
) -> ResponseData[Items[LLMModel]]:
    await llm_model_service.update_all_model_configs(db)
    return {'data': 'success'}


@router.delete("/llm-models/{model_id}", dependencies=[Depends(admit_by_cookie)],
               status_code=204)
async def delete_model(
    model_id: str,
    db: Session = Depends(get_http_db)
):
    await llm_model_service.delete(db, model_id)
    # 刷新redis中的模型
    await fetch_all_models(force_refresh=True)
    return {'data': 'success'}
