from fastapi import APIRouter, Depends

from ..misc.middleware import ValidationErrorLoggingRoute
from ..config import settings
from ..misc.authz import admit_by_cookie

from ..schema.common import ResponseData, Items
from ..schema.workflow_engine import WorkflowEngine

router = APIRouter(prefix='', route_class=ValidationErrorLoggingRoute)


@router.get('/workspace/{workspace_id}/workflow-engines', dependencies=[Depends(admit_by_cookie)],
            tags=['workflow-engines'], description="list workflow engines")
async def list_workflow_engine(workspace_id: str) -> ResponseData[Items[WorkflowEngine]]:
    engines = []
    for engine in settings.workflow_config.engines:
        if engine.workspaceId == workspace_id:
            engines.append(WorkflowEngine(id=engine.id,
                                          name=engine.name,
                                          workspaceId=engine.workspaceId,
                                          endpoint=engine.endpoint,
                                          description=engine.description))
    return ResponseData.new_items(items=[WorkflowEngine.model_validate(engine)
                                         for engine in engines],
                                  total=len(settings.workflow_config.engines))


@router.get('/workflow-engines/{workflow_engine_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['workflow-engines'])
async def get_workflow_engine(workflow_engine_id: str) \
        -> ResponseData[WorkflowEngine]:
    for engine in settings.workflow_config.engines:
        if engine.id == workflow_engine_id:
            return ResponseData(data=WorkflowEngine.model_validate(WorkflowEngine(id=engine.id,
                                                                                  name=engine.name,
                                                                                  workspaceId=engine.workspaceId,
                                                                                  endpoint=engine.endpoint,
                                                                                  description=engine.description)))
    return ResponseData(code=404, message="workflow engine " + workflow_engine_id + "not found")
