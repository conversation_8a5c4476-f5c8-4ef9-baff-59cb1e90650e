from typing import Annotated, Dict, List, Optional
from uuid import uuid4
from fastapi import APIRouter, Query, Depends, \
    HTTPException, Header, Request, Response
from fastapi.responses import RedirectResponse
from loguru import logger
import requests
from requests_oauthlib import OAuth2Session

from ..schema.member import Member<PERSON><PERSON>, MemberType, Role
from ..schema.common import ResourceType

from ..service.member import create as create_member_service

from ..misc.middleware import ValidationErrorLoggingRoute

from ..misc.errors import ParameterInvalid

from ..misc.authz import admit_by_cookie

from ..schema.common import ResponseData
from ..schema.workspace import WorkspaceCreate
from ..service.workspace import create_with_initial as create_workspace
from ..models.account import Account
from ..misc.session import SessionUser, whoami
from ..misc.db import get_db
from ..schema.account import AccountCreate, Account as AccountSchema
from ..config import OauthProvider, settings, dev_oatuth_setting
from ..service.account import create as create_account, \
    get_by_email as get_account_by_email
from sqlalchemy.orm import Session
from ..service.workspace import list as list_workspace_service
from ..misc.session import backend, cookie

router = APIRouter(route_class=ValidationErrorLoggingRoute)
well_known_metadatas: Dict[str, Dict] = {}


def get_redirect_uri(host: Annotated[str, Header()],
                     request: Request,
                     provider_name: str,
                     url: Optional[str]):
    if url is None:
        return f"{request.url.scheme}://{host}/api/v1/oauth/callback/{provider_name}"
    if url.startswith("http"):
        return url

    schema = request.url.scheme
    return f"{schema}://{host}/{url.lstrip('/')}"


def get_well_known_metadata(discovery):
    response = requests.get(discovery, timeout=5)
    response.raise_for_status()
    return response.json()


def get_oauth2_session(client_id, scope, redirect_uri, **kwargs):
    oauth2_session = OAuth2Session(
        client_id,
        scope=scope,
        redirect_uri=redirect_uri,
        **kwargs
    )
    return oauth2_session


def register_route(v1_router: APIRouter):
    for oauth_setting in settings.oauth:
        well_known_metadatas[oauth_setting.name] = get_well_known_metadata(
            f"{oauth_setting.issuer}/connect/.well-known/openid-configuration")
        init_oauth_route(router, oauth_setting)
    v1_router.include_router(router)


def init_oauth_route(router: APIRouter, oauth_setting: OauthProvider):
    @router.get(f'/oauth/login/{oauth_setting.name}',
                status_code=307, tags=["authentication"])
    async def login(host: Annotated[str, Header()],
                    request: Request,
                    langbase_dev: Annotated[Optional[str], Header()] = None):

        cur_oauth = oauth_setting
        if langbase_dev == "true":
            cur_oauth = dev_oatuth_setting
        redirect_uri = get_redirect_uri(host,
                                        request,
                                        cur_oauth.name,
                                        cur_oauth.redirect_uri)

        # 获取schema
        oauth2_session = get_oauth2_session(
            cur_oauth.client_id,
            cur_oauth.scope,
            redirect_uri)

        authorization_url, _ = oauth2_session.authorization_url(
            well_known_metadatas[cur_oauth.name]["authorization_endpoint"])
        return RedirectResponse(authorization_url)

    @router.get(f'/oauth/callback/{oauth_setting.name}', status_code=200,
                response_model=AccountSchema, tags=["authentication"])
    async def callback(code: str,
                       state: str,
                       host: Annotated[str, Header()],
                       request: Request,
                       db: Session = Depends(get_db),
                       langbase_dev: Annotated[Optional[str], Header()] = None):

        cur_oauth = oauth_setting
        if langbase_dev == "true":
            cur_oauth = dev_oatuth_setting
        redirect_uri = get_redirect_uri(host,
                                        request,
                                        cur_oauth.name,
                                        cur_oauth.redirect_uri)

        well_known_metadata = well_known_metadatas[cur_oauth.name]
        oauth2_session = get_oauth2_session(
            cur_oauth.client_id,
            cur_oauth.scope,
            redirect_uri,
            state=state)
        tokens = oauth2_session.fetch_token(
            well_known_metadata["token_endpoint"],
            client_secret=cur_oauth.client_secret,
            code=code)
        acess_token = tokens["access_token"]
        resp = oauth2_session.get(
            well_known_metadata["userinfo_endpoint"],
            headers={"Authorization": f"Bearer {acess_token}"})
        user_info = resp.json()
        logger.info('userinfo: {user_info}', user_info=user_info, uid='uid')

        name = user_info["sub"]
        dep = user_info["dep"] if "dep" in user_info else 'S05-音乐事业部-技术中心'
        fullname = user_info["name"]
        email = user_info["email"]
        # 获取用户的事业部信息
        if dep is not None:
            # -分割的第二个
            dep = dep.split("-")[1]
        if len(email) == 0:
            email = f"{name}@{cur_oauth.name}.com"
        if len(fullname) == 0:
            fullname = name

        account = await get_account_by_email(db, email)
        if account is None:
            account_create = AccountCreate(
                name=name, fullname=fullname, email=email)
            account = await user_register_v2(account_create, db=db, dep=dep, admin=False)

        response = RedirectResponse("/logined")
        await create_session(account, response)
        return response


@router.post('/register', status_code=201, tags=["authentication"],
             dependencies=[Depends(admit_by_cookie)])
async def user_register(
        account: AccountCreate,
        db: Session = Depends(get_db)):
    if await get_account_by_email(db, account.email) is not None:
        raise ParameterInvalid("email already exists")
    account = await create_account(db, account)
    workspace_create = WorkspaceCreate(
        name=settings.default_workspace_name
        .format(account.name
                if str(account.fullname) == ''
                else account.fullname),
        description=None)
    # 不创建
    await create_workspace(
        db,
        workspace_create=workspace_create,
        user_id=account.id,
        user_name=str(account.name))
    return account


@router.post('/registerv2', status_code=201, tags=["authentication"])
async def user_register_v2(
        account: AccountCreate,
        db: Session = Depends(get_db),
        dep: Optional[str] = Query(None),
        admin: bool = Query(False)):
    if await get_account_by_email(db, account.email) is not None:
        raise ParameterInvalid("email already exists")
    account = await create_account(db, account)
    workspace_id = settings.default_workspace_id
    member_create = MemberCreate(
        memberType=MemberType.USER.value,
        memberID=account.id,
        role=Role.DEVELOPER.value)
    # 所有新用户默认关联 测试租户
    await create_member_service(
        db,
        user_id=settings.workspace_member_id,
        resource_type=ResourceType.WORKSPACE,
        resource_id=str(workspace_id),
        member_create=member_create)
    # 为dep关联对应的租户
    if dep is not None:
        dep_workspace_id, _ = await list_workspace_service(
            db,
            description=dep,
            user_id=settings.workspace_member_id,
            offset=0,
            limit=1)
        if len(dep_workspace_id) > 0:
            dep_workspace_id = dep_workspace_id[0].id
            if admin:
                member_create.role = Role.ADMIN.value
            else:
                member_create.role = Role.VIEWER.value
            await create_member_service(
                db,
                user_id=settings.workspace_member_id,
                resource_type=ResourceType.WORKSPACE,
                resource_id=str(dep_workspace_id),
                member_create=member_create)
    return account

# b0fb8011-b8ee-4ce4-a04e-00175105757e


@router.get('/logout', status_code=204, tags=["authentication"])
def logout():
    response = Response()
    cookie.delete_from_response(response)
    response.status_code = 204
    return response


@router.get('/oauth/providers', status_code=200,
            response_model=List[str], tags=["authentication"])
def list_oauth_providers() -> ResponseData[List[str]]:
    return ResponseData(data=[str(key) for key in well_known_metadatas.keys()])


async def create_session(account: Account, response: Response):

    sessionID = uuid4()
    data = SessionUser.model_validate(account)

    await backend.create(sessionID, data)
    cookie.attach_to_response(response, sessionID)


async def whether_login(request: Request):
    try:
        return await whoami(request)
    except HTTPException:
        return None
