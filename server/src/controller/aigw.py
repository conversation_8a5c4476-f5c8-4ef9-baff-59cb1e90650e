from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from typing import Optional
from ..schema.common import ResponseData

from ..misc.middleware import ValidationErrorLoggingRoute

from ..schema.aigw import AigwAppBind, AigwAppCreate, AigwAppUpdate, AigwAppResponse
from ..service import aigw as aigw_service
from ..misc.db import get_db
from ..misc.authz import admit_by_cookie
from ..misc.session import SessionUser
from ..schema.common import AigwResourceType

router = APIRouter(route_class=ValidationErrorLoggingRoute)


# 创建workspace维度的aigw账号
@router.post("/workspace/{workspace_id}/aigw_app", tags=['aigw', 'workspace'], dependencies=[Depends(admit_by_cookie)],
             response_model=AigwAppResponse, status_code=201)
async def create_workspace_aigw_app(
    workspace_id: str,
    request: Request,
    aigw_create: AigwAppCreate,
    db: Session = Depends(get_db)
):
    """Create a new aigw app for workspace"""
    user: SessionUser = request.user
    aigw_create.resource_type = AigwResourceType.WORKSPACE
    aigw_create.resource_id = workspace_id
    res = await aigw_service.create(db, user.id, aigw_create)
    # app = AigwAppResponse.model_validate(**res, strict=False)
    await aigw_service.refresh_aigw_account_map(db)
    return {
        'app_code': res.app_code,
        'token': res.token,
        'resource_type': res.resource_type,
        'resource_id': res.resource_id,
        'quota': res.quota,
        'extra': res.extra
    }


# 绑定workspace维度的aigw账号
@router.post("/workspace/{workspace_id}/aigw_bind", dependencies=[Depends(admit_by_cookie)],
             response_model=AigwAppResponse, status_code=201)
async def bind_aigw_app(
    workspace_id: str,
    request: Request,
    aigw_bind: AigwAppBind,
    db: Session = Depends(get_db)
):
    """Bind aigw app for workspace"""
    user: SessionUser = request.user
    aigw_bind.resource_type = AigwResourceType.WORKSPACE
    aigw_bind.resource_id = workspace_id
    try:
        res = await aigw_service.bind(db, user.id, aigw_bind)
        await aigw_service.refresh_aigw_account_map(db)
        return res
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 绑定group维度的aigw账号
@router.post("/group/{group_id}/aigw_bind", dependencies=[Depends(admit_by_cookie)],
             response_model=AigwAppResponse, status_code=201)
async def bind_group_aigw_app(
    group_id: str,
    request: Request,
    aigw_bind: AigwAppBind,
    db: Session = Depends(get_db)
):
    """Bind aigw app for group"""
    user: SessionUser = request.user
    aigw_bind.resource_type = AigwResourceType.GROUP
    aigw_bind.resource_id = group_id
    try:
        res = await aigw_service.bind(db, user.id, aigw_bind)
        await aigw_service.refresh_aigw_account_map(db)
        return res
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# 创建group维度的aigw账号


@router.post("/group/{group_id}/aigw_app", dependencies=[Depends(admit_by_cookie)],
             response_model=AigwAppResponse, status_code=201)
async def create_group_aigw_app(
    group_id: str,
    request: Request,
    aigw_create: AigwAppCreate,
    db: Session = Depends(get_db)
):
    """Create a new aigw app for group"""
    user: SessionUser = request.user
    aigw_create.resource_type = AigwResourceType.GROUP
    aigw_create.resource_id = group_id
    try:
        res = await aigw_service.create(db, user.id, aigw_create)
        await aigw_service.refresh_aigw_account_map(db)
        return res
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 获取workspace维度的aigw账号
@router.get("/workspace/{workspace_id}/aigw_app/{aigw_id}", dependencies=[Depends(admit_by_cookie)],
            response_model=AigwAppResponse)
async def get_workspace_aigw_app(
    aigw_id: str,
    db: Session = Depends(get_db)
):
    """Get aigw app by id for workspace"""
    try:
        return await aigw_service.get(db, aigw_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


# 获取group维度的aigw账号
@router.get("/group/{group_id}/aigw_app/{aigw_id}", dependencies=[Depends(admit_by_cookie)],
            response_model=AigwAppResponse)
async def get_group_aigw_app(
    aigw_id: str,
    db: Session = Depends(get_db)
):
    """Get aigw app by id for group"""
    try:
        return await aigw_service.get(db, aigw_id)
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


# 获取workspace维度的aigw账号列表
@router.get("/workspace/{workspace_id}/aigw_apps",
            dependencies=[Depends(admit_by_cookie)],
            response_model=ResponseData)
async def list_workspace_aigw_apps(
    workspace_id: str,
    token: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """List aigw apps for workspace"""
    try:
        apps, total = await aigw_service.list_aigw_apps(
            db, AigwResourceType.WORKSPACE, workspace_id, token
        )
        return ResponseData.new_items(items=[
            AigwAppResponse.model_validate(app) for app in apps
        ], total=total)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 获取workspace维度的aigw账号
@router.get("/workspace/{workspace_id}/aigw_app",
            dependencies=[Depends(admit_by_cookie)],
            response_model=ResponseData)
async def get_workspace_aigw_app_controller(
    workspace_id: str,
    token: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get aigw app for workspace"""
    try:
        app = await aigw_service.get_aigw_app(
            db, AigwResourceType.WORKSPACE, workspace_id, token
        )
        if app is None:
            return ResponseData(data=None)
        return ResponseData(data=AigwAppResponse.model_validate(app))
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 获取group维度的aigw账号
@router.get("/group/{group_id}/aigw_app",
            dependencies=[Depends(admit_by_cookie)],
            response_model=ResponseData)
async def get_group_aigw_app_controller(
    group_id: str,
    token: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get aigw app for group"""
    try:
        app = await aigw_service.get_aigw_app(db, AigwResourceType.GROUP, group_id, token)
        if app is None:
            return ResponseData(data=None)
        return ResponseData(data=AigwAppResponse.model_validate(app))
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 获取group维度的aigw账号列表
@router.get("/group/{group_id}/aigw_apps", dependencies=[Depends(admit_by_cookie)],
            response_model=ResponseData)
async def list_group_aigw_apps(
    group_id: str,
    request: Request,
    token: Optional[str] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """List aigw apps for group"""
    try:
        apps, total = await aigw_service.list_aigw_apps(
            db, AigwResourceType.GROUP, group_id, token, limit, offset
        )
        return ResponseData.new_items(items=[
            AigwAppResponse.model_validate(app) for app in apps
        ], total=total)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 更新workspace维度的aigw账号
@router.put("/workspace/{workspace_id}/aigw_app/{aigw_id}", dependencies=[Depends(admit_by_cookie)],
            response_model=AigwAppResponse)
async def update_workspace_aigw_app(
    workspace_id: str,
    aigw_id: int,
    aigw_update: AigwAppUpdate,
    request: Request,
    db: Session = Depends(get_db)
):
    """Update aigw app for workspace"""
    try:
        res = await aigw_service.update(db, aigw_id, AigwResourceType.WORKSPACE, workspace_id, aigw_update)
        await aigw_service.refresh_aigw_account_map(db)
        return res
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 更新group维度的aigw账号
@router.put("/group/{group_id}/aigw_app/{aigw_id}", dependencies=[Depends(admit_by_cookie)],
            response_model=AigwAppResponse)
async def update_group_aigw_app(
    group_id: str,
    aigw_id: int,
    aigw_update: AigwAppUpdate,
    request: Request,
    db: Session = Depends(get_db)
):
    """Update aigw app for group"""
    try:
        res = await aigw_service.update(db, aigw_id, AigwResourceType.GROUP, group_id, aigw_update)
        await aigw_service.refresh_aigw_account_map(db)
        return res
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# 删除aigw账号


@router.get("/aigw_app/refresh_account_map", tags=["aigw"], dependencies=[Depends(admit_by_cookie)])
async def refresh_aigw_account_map(
    db: Session = Depends(get_db)
):
    """Refresh aigw account map"""
    try:
        await aigw_service.refresh_aigw_account_map(db)
        return ResponseData(data='Successfully refreshed')
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


# 删除aigw账号
@router.delete("/aigw_app/{aigw_id}", tags=["aigw"], dependencies=[Depends(admit_by_cookie)])
async def delete_aigw_app(
    aigw_id: str,
    db: Session = Depends(get_db)
):
    """Delete aigw app for group"""
    try:
        await aigw_service.delete(db, aigw_id)
        return ResponseData(data='Successfully deleted')
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
