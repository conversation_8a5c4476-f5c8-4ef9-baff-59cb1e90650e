from typing import Annotated
from fastapi import APIRouter, Depends, Request

from ..misc.middleware import ValidationErrorLoggingRoute

from ..schema.common import Pages, ResponseData, Items
from ..misc.session import SessionUser
from ..schema.workflow_datatype import WorkflowDataType, WorkflowDataTypeCreate, WorkflowDataTypeUpdate
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db
from ..service.workflow_datatype import get as get_workflow_datatype_service, \
    list as list_workflow_datatype_service, \
    update as update_workflow_datatype_service, \
    delete as delete_workflow_datatype_service, \
    create as create_workflow_datatype_service

router = APIRouter(prefix='/workflow-datatypes',
                   route_class=ValidationErrorLoggingRoute)


@router.get('', dependencies=[Depends(admit_by_cookie)],
            tags=['workflow-datatypes'], description="list workflow datatypes")
async def list_workflow_datatype(pages: Annotated[Pages, Depends(Pages)],
                                 db=Depends(get_db)) -> ResponseData[Items[WorkflowDataType]]:
    (db_workflow_datatypes, total) = await list_workflow_datatype_service(
        db,
        offset=pages.offset,
        limit=pages.limit)
    return ResponseData.new_items(items=[WorkflowDataType.model_validate(db_workflow_datatype)
                                         for db_workflow_datatype in db_workflow_datatypes],
                                  total=total)


@router.post('', dependencies=[Depends(admit_by_cookie)],
             status_code=201, tags=['workflow-datatypes'])
async def create_workflow_datatype(component_workflow_datatype: WorkflowDataTypeCreate,
                                   request: Request, db=Depends(get_db)) \
        -> ResponseData[WorkflowDataType]:
    user: SessionUser = request.user
    db_workflow_datatype = await create_workflow_datatype_service(
        db,
        workflow_datatype_create=component_workflow_datatype,
        user_id=user.id)
    return ResponseData(data=WorkflowDataType.model_validate(db_workflow_datatype))


@router.get('/{workflow_datatype_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['workflow-datatypes'])
async def get_workflow_datatype(workflow_datatype_id: str, db=Depends(get_db)) \
        -> ResponseData[WorkflowDataType]:
    db_workflow_datatype = await get_workflow_datatype_service(db, workflow_datatype_id)
    return ResponseData(data=WorkflowDataType.model_validate(db_workflow_datatype))


@router.put('/{workflow_datatype_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['workflow-datatypes'])
async def update_workflow_datatype(workflow_datatype_id: str,
                                   workflow_datatype_update: WorkflowDataTypeUpdate,
                                   request: Request,
                                   db=Depends(get_db)) \
        -> ResponseData[WorkflowDataType]:
    user: SessionUser = request.user
    db_workflow_datatype = await update_workflow_datatype_service(db,
                                                                  user.id,
                                                                  workflow_datatype_id,
                                                                  workflow_datatype_update)
    return ResponseData(data=WorkflowDataType.model_validate(db_workflow_datatype))


@router.delete('/{workflow_datatype_id}', dependencies=[Depends(admit_by_cookie)],
               tags=['workflow-datatypes'], status_code=204)
async def delete_workflow_datatype(workflow_datatype_id: str,
                                   request: Request,
                                   db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_workflow_datatype_service(db, user.id, workflow_datatype_id)
