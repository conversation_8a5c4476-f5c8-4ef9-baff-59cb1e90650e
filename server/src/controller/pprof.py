import asyncio
import io
import linecache
from typing import Annotated
from fastapi import APIRouter, Query
from fastapi.responses import PlainTextResponse
import yappi
import objgraph
import tracemalloc

from ..misc.middleware import ValidationErrorLoggingRoute

from ..config import settings


class Stats(io.StringIO):
    def __init__(self):
        self.stat = ''

    def write(self, s):
        self.stat += s


router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/pprof/funcs', tags=['pprof'])
async def get_func_stats(
    seconds: Annotated[int, Query(alias="sec")] = 30,
):
    if not settings.enable_profile:
        return PlainTextResponse(content="profile not enabled")

    s = Stats()

    # with open('pprof.txt', 'w') as f:
    yappi.set_clock_type("cpu")
    yappi.start()
    await asyncio.sleep(seconds)
    yappi.get_func_stats().print_all(out=s, columns={
        0: ("name", 128),
        1: ("ncall", 5),
        2: ("tsub", 8),
        3: ("ttot", 8),
        4: ("tavg", 8)
    })
    yappi.stop()

    # with open('pprof.txt', 'r') as f:
    resp = PlainTextResponse(content=s.stat)
    return resp


@router.get('/pprof/objgraph/most-common-types', tags=['pprof'])
async def get_objgraph_most_common_types(limit: Annotated[int, Query(alias="limit")] = 200):
    if not settings.enable_profile:
        return PlainTextResponse(content="profile not enabled")

    stats = objgraph.most_common_types(limit=limit)

    output = io.StringIO()
    width = max(len(name) for name, count in stats)
    for name, count in stats:
        output.write('%-*s %i\n' % (width, name, count))

    resp = PlainTextResponse(content=output.getvalue())
    return resp


@router.get('/pprof/objgraph/growth', tags=['pprof'])
async def get_objgraph(limit: Annotated[int, Query(alias="limit")] = 200):
    if not settings.enable_profile:
        return PlainTextResponse(content="profile not enabled")

    gth = objgraph.growth(limit=limit)
    output = io.StringIO()
    if gth:
        width = max(len(name) for name, _, _ in gth)
        for name, count, delta in gth:
            output.write('%-*s%9d %+9d\n' % (width, name, count, delta))

    resp = PlainTextResponse(content=output.getvalue())
    return resp


pre_snapshot = None


@router.get('/pprof/trace-malloc/start', tags=['pprof'])
async def trace_malloc_start():
    if not settings.enable_profile:
        return PlainTextResponse(content="profile not enabled")

    global pre_snapshot
    tracemalloc.start()
    pre_snapshot = tracemalloc.take_snapshot()
    output = io.StringIO()
    output.write('trace malloc start ok\n')

    resp = PlainTextResponse(content=output.getvalue())
    return resp


@router.get('/pprof/trace-malloc/stop', tags=['pprof'])
async def trace_malloc_stop():
    if not settings.enable_profile:
        return PlainTextResponse(content="profile not enabled")

    tracemalloc.stop()
    output = io.StringIO()
    output.write('trace malloc stop ok\n')

    resp = PlainTextResponse(content=output.getvalue())
    return resp


@router.get('/pprof/trace-malloc/reset', tags=['pprof'])
async def trace_malloc_snapshot_reset():
    if not settings.enable_profile:
        return PlainTextResponse(content="profile not enabled")

    global pre_snapshot
    pre_snapshot = tracemalloc.take_snapshot()
    output = io.StringIO()
    output.write('trace malloc reset pre snapshot ok\n')

    resp = PlainTextResponse(content=output.getvalue())
    return resp


@router.get('/pprof/trace-malloc/compare', tags=['pprof'])
async def get_trace_malloc_snapshot():
    if not settings.enable_profile:
        return PlainTextResponse(content="profile not enabled")

    global pre_snapshot
    snapshot = tracemalloc.take_snapshot()
    top_stats = snapshot.compare_to(pre_snapshot, 'lineno')
    output = io.StringIO()
    for stat in top_stats:
        output.write(str(stat) + '\n')

    resp = PlainTextResponse(content=output.getvalue())
    return resp


@router.get('/pprof/trace-malloc/topk', tags=['pprof'])
async def get_trace_malloc_top_k(limit: Annotated[int, Query(alias="limit")] = 50):
    if not settings.enable_profile:
        return PlainTextResponse(content="profile not enabled")

    snapshot = tracemalloc.take_snapshot()
    top_stats = snapshot.statistics('lineno')
    output = io.StringIO()
    output.write("Top %s lines" % limit)
    output.write('\n')
    for index, stat in enumerate(top_stats[:limit], 1):
        frame = stat.traceback[0]
        output.write("#%s: %s:%s: %.1f KiB" %
                     (index, frame.filename, frame.lineno, stat.size / 1024))
        output.write('\n')
        line = linecache.getline(frame.filename, frame.lineno).strip()
        if line:
            output.write('    %s' % line)
            output.write('\n')

    other = top_stats[limit:]
    if other:
        size = sum(stat.size for stat in other)
        output.write("%s other: %.1f KiB" % (len(other), size / 1024))
        output.write('\n')
    total = sum(stat.size for stat in top_stats)
    output.write("Total allocated size: %.1f KiB" % (total / 1024))
    output.write('\n')

    resp = PlainTextResponse(content=output.getvalue())
    return resp
