import json
import math
import time
import httpx  # 添加httpx导入
from fastapi import APIRouter, Body, Depends, HTTPException, Query, Request
from openai.types.chat import ChatCompletion, ChatCompletionAudioParam, ChatCompletionMessageParam, ChatCompletionChunk, ChatCompletionModality, ChatCompletionPredictionContentParam, ChatCompletionReasoningEffort, ChatCompletionStreamOptionsParam, ChatCompletionToolChoiceOptionParam, ChatCompletionToolParam, completion_create_params
from openai.types.chat.chat_completion_content_part_param import ChatCompletionContentPartParam
from openai.types.shared_params.metadata import Metadata
from pydantic import BaseModel
from typing import Any, Dict, Iterable, Literal, Optional, List, Union
from openai import AsyncOpenAI
from sqlalchemy.orm import Session
from fastapi.responses import StreamingResponse
from loguru import logger

from ..lib.llms.get_models import fetch_all_models


from ..misc.authz import admit_by_token

from ..service.chat import create_message, find_provider_model
from ..schema.dialog import MessageCreate
from ..misc.db import get_db

from ..service.model import getBinding

from ..schema.dialog import ResponseMode

from ..service.app import generate_diff, get_config_by_app, get as get_app
from ..config import settings

from ..service.popo_doc import parse_popo_document
from ..service.popo_score import call_chatflow_api, call_completion_api, process_result

router = APIRouter(tags=["AIGC"])


class PopoScoreRequest(BaseModel):
    linkUrl: str
    source: Optional[str] = None
    appId: Optional[str] = None
    apiToken: Optional[str] = None
    raw: Optional[bool] = False
    inputKey: Optional[str] = None
    extra_params: Optional[Dict] = None


class ApiResponse(BaseModel):
    code: int
    message: str
    data: Optional[Dict] = None


@router.post("/popo-score", response_model=ApiResponse)
async def score_popo_document(request: PopoScoreRequest = Body(...)):
    """
    评分Popo文档

    根据提供的Popo文档链接获取文档内容，然后进行评分
    """
    try:
        # 获取popo文档内容
        popo_result = await parse_popo_document(request.linkUrl)
        if popo_result['code'] != 200:
            return ApiResponse(
                code=400,
                message="获取Popo文档内容失败",
                data=popo_result
            )

        # 记录开始时间
        start_time = time.time()

        if request.appId:
            # 调用completion API进行评分
            completion_result = await call_completion_api(popo_result['data']['text_content'], request.appId, request.apiToken, request.inputKey)
        else:
            completion_result = await call_completion_api(popo_result['data']['text_content'])
        # 计算耗时
        elapsed_time = time.time() - start_time

        # 处理结果
        result = process_result(completion_result, request.raw or False)

        # 添加来源信息和耗时
        result['source'] = request.source
        result['time'] = elapsed_time

        return ApiResponse(
            code=200,
            message="评分成功",
            data=result
        )

    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"处理出错: {str(e)}",
            data=None
        )


@router.post("/popo-score-v2", response_model=ApiResponse)
async def score_popo_document_v2(request: PopoScoreRequest = Body(...)):
    """
    评分Popo文档

    根据提供的Popo文档链接获取文档内容，然后进行评分
    """
    try:
        # 记录开始时间
        start_time = time.time()

        # 调用completion API进行评分
        completion_result = await call_chatflow_api(request.linkUrl, request.extra_params, request.appId)
        # 计算耗时
        elapsed_time = time.time() - start_time

        # 处理结果
        result = {}
        if completion_result.get('code') == 200 and completion_result.get('data', {}).get('result', {}).get('output', None):
            result = completion_result['data']['result']['output']
            # 添加来源信息和耗时
            result['source'] = request.source
            result['time'] = elapsed_time
            return ApiResponse(**result)
        else:
            return ApiResponse(
                code=500,
                message=completion_result.get('message', '评分失败'),
                data=None
            )
    except Exception as e:
        return ApiResponse(
            code=500,
            message=f"处理出错: {str(e)}",
            data=None
        )

# OpenAI API 相关的模型定义


class OpenAIMessage(BaseModel):
    role: str  # "system", "user", "assistant", "function"
    content: Any
    name: Optional[str] = None


class OpenAIFunction(BaseModel):
    name: str
    description: Optional[str] = None
    parameters: Optional[Dict] = None


class OpenAIFunctionCall(BaseModel):
    type: str
    function: OpenAIFunction


class OpenAIChatCompletionRequest(BaseModel):
    model: str
    n: Optional[int] = None
    stream: Optional[bool] = False
    stop: Optional[Union[str, List[str]]] = None
    audio: Optional[ChatCompletionAudioParam] = None
    max_tokens: Optional[int] = None
    max_completion_tokens: Optional[int] = None
    presence_penalty: Optional[float] = 0.0
    frequency_penalty: Optional[float] = 0.0
    logit_bias: Optional[Dict[str, float]] = None
    user: Optional[str] = None
    extra_headers: Optional[Dict] = None
    extra_query: Optional[Dict] = None
    extra_body: Optional[Dict] = None
    messages: List[Any]

    function_call: Optional[completion_create_params.FunctionCall] = None
    functions: Optional[List[completion_create_params.Function]] = None
    logprobs: Optional[bool] = None
    metadata: Optional[Metadata] = None
    modalities: Optional[List[ChatCompletionModality]] = None
    parallel_tool_calls: Optional[bool] = None
    prediction: Optional[ChatCompletionPredictionContentParam] = None
    reasoning_effort: Optional[ChatCompletionReasoningEffort] = None
    response_format: Optional[completion_create_params.ResponseFormat] = None
    seed: Optional[int] = None
    service_tier: Optional[Literal["auto",
                                   "default"]] = None
    store: Optional[bool] = None
    stream_options: Optional[ChatCompletionStreamOptionsParam] = None
    temperature: Optional[float] = None
    tool_choice: Optional[Any] = None
    tools: Any = None
    top_logprobs: Optional[int] = None
    top_p: Optional[float] = None
    # Use the following arguments if you need to pass additional parameters to the API that aren't available via kwargs.
    # The extra values given here take precedence over values defined on the client or passed to this method.
    timeout: Optional[float] = None

    model_config = {
        "arbitrary_types_allowed": True
    }


class OpenAIChoice(BaseModel):
    index: int
    message: OpenAIMessage
    finish_reason: Optional[str] = None


class OpenAIUsage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class OpenAIChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[OpenAIChoice]
    usage: OpenAIUsage


@router.post("/app/{app_id}/openai-proxy/chat/completions")
async def openai_proxy(
        app_id: str,
        request_body: OpenAIChatCompletionRequest = Body(...),
        userId: Optional[str] = Query(default="112")):
    """
    OpenAI Chat Completions API 代理接口

    完全兼容 OpenAI API 的入参格式，支持所有标准参数
    支持通过查询参数传入 appId 和 userId
    """
    base_url = settings.service_proxy_prefix
    try:
        # 记录开始时间
        start_time = time.time()

        # 从请求头中获取Authorization
        authorization = request.headers.get("Authorization")
        if not authorization:
            return ApiResponse(
                code=401,
                message="缺少Authorization头",
                data=None
            )

        # 调用langbase agent custom API
        async with httpx.AsyncClient() as client:
            # 构造请求数据
            api_data = {
                "appId": app_id,
                "userId": userId,
                "parameters": {},
                **request_body.model_dump()
            }

            # 发送请求，使用从请求头获取的Authorization
            response = await client.post(
                f"{base_url}/langbase/agent/custom",
                headers={
                    "Authorization": authorization,
                    "Content-Type": "application/json"
                },
                json=api_data,
                timeout=1000.0
            )

            # 检查响应状态
            response.raise_for_status()
            api_result = response.json()

            # 从API响应中提取内容
            if api_result and isinstance(api_result, dict):
                # 检查返回的code字段
                if api_result.get("code") == 200:
                    # code为200时，直接返回data的内容
                    data_content = api_result.get("data", {})
                    data_content['id'] = f"chatcmpl-{int(time.time())}"
                    data_content['created'] = int(time.time())
                    data_content['object'] = 'chat.completion'
                    return OpenAIChatCompletionResponse(**data_content)
                else:
                    # code不为200时，按OpenAI报错格式返回
                    error_message = api_result.get("message", "API调用失败")
                    raise HTTPException(
                        status_code=400,
                        detail={
                            "error": {
                                "message": error_message,
                                "type": "api_error",
                                "code": api_result.get("code", "unknown_error")
                            }
                        }
                    )
            else:
                assistant_content = "API返回格式异常"

        # 构造OpenAI格式的响应数据
        response_data = OpenAIChatCompletionResponse(
            id=f"chatcmpl-{int(time.time())}",
            created=int(time.time()),
            model=request_body.model,
            choices=[
                OpenAIChoice(
                    index=0,
                    message=OpenAIMessage(
                        role="assistant",
                        content=assistant_content
                    ),
                    finish_reason="stop"
                )
            ],
            usage=OpenAIUsage(
                prompt_tokens=sum(len(msg.content.split())
                                  for msg in request_body.messages),  # 简单估算
                completion_tokens=len(assistant_content.split()),  # 简单估算
                total_tokens=sum(len(msg.content.split())
                                 for msg in request_body.messages) + len(assistant_content.split())
            )
        )

        return response_data

    except httpx.HTTPStatusError as e:
        raise HTTPException(status_code=e.response.status_code,
                            detail=f"API调用失败: {e.response.text}")
    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="API调用超时")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理出错: {str(e)}")


@router.post("/app/{app_id}/openai_v2/chat/completions", dependencies=[Depends(admit_by_token)])
async def openai_proxy_v2_v2(
        app_id: str,
        db: Session = Depends(get_db),
        request_body: Dict = Body(...),
        userId: Optional[str] = Query(default="external_user")):
    logger.info("request_body_v2: {request_body}",
                request_body=request_body, uid="uid")
    # 构造OpenAI客户端
    data = {
        **request_body,
        "model": 'claude-3-7-sonnet-20250219'
    }
    openai_client = AsyncOpenAI(
        api_key='zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh', base_url='https://aigw-int.netease.com/v1')
    return await openai_client.chat.completions.create(**data)


@router.post("/app/{app_id}/openai/chat/completions", dependencies=[Depends(admit_by_token)])
async def openai_proxy_v2(
        app_id: str,
        db: Session = Depends(get_db),
        request_body: OpenAIChatCompletionRequest = Body(...),
        userId: Optional[str] = Query(default="external_user")):
    """
    OpenAI Chat Completions API 代理接口

    完全兼容 OpenAI API 的入参格式，支持所有标准参数
    """
    try:
        start_time = time.time()
        # 获取应用配置
        db_app = await get_app(app_id=app_id)
        db_config = await get_config_by_app(db=None, app_id=app_id)
        if not db_config:
            raise HTTPException(
                status_code=404,
                detail="应用配置不存在"
            )

        # 从配置中获取模型信息
        config = db_config.config
        if not config or not isinstance(config, dict):
            raise HTTPException(
                status_code=400,
                detail="应用配置格式错误"
            )

        model_config = config.get('modelsConfig', {}).get('models', [])
        if not model_config or not isinstance(model_config, list):
            raise HTTPException(
                status_code=400,
                detail="模型配置不存在"
            )

        # 根据模型名称获取模型配置
        model_name = request_body.model or model_config[0].get('modelName')
        model = find_provider_model(model_name=model_name)
        if not model:
            # 再二次通过config中的modelName获取
            model_name = model_config[0].get('modelName')
            model = find_provider_model(model_name=model_name)
            # 如果依旧没有，则抛出错误
            if not model:
                raise Exception(
                    "模型配置不存在, 请访问 https://langbase.netease.com/models 查看所有可用模型")
        provider_kind = model.providerKind
        bindings = await getBinding(str(db_app.workspace_id), str(db_app.group_id), db)
        api_key = None
        if len(bindings) > 0:
            targetKindBindings = [binding for binding in bindings if str(
                binding.kind) == provider_kind]
            binding = targetKindBindings[0] if len(
                targetKindBindings) > 0 else None
            if binding and binding.api_key is not None \
                    and (api_key is None or not api_key):
                api_key = str(binding.api_key)

        # 构造OpenAI客户端
        openai_client = AsyncOpenAI(
            api_key=api_key, base_url='https://aigw-int.netease.com/v1')

        # 根据是否需要流式输出设置响应模式
        # response_mode = ResponseMode.STREAMING
        response_mode = ResponseMode.STREAMING if request_body.stream else ResponseMode.JSON
        # 调用OpenAI接口
        create_params = {
            **request_body.model_dump(),
            "model": model_name,
        }
        if isinstance(request_body.tool_choice, str):
            create_params['tool_choice'] = request_body.tool_choice
        elif isinstance(request_body.tool_choice, dict):
            if request_body.tool_choice.get('type'):
                create_params['tool_choice'] = request_body.tool_choice.get(
                    'type')
        # 只有在不为None时才添加可选参数
        if request_body.logit_bias is not None:
            create_params["logit_bias"] = {str(k): int(
                v) for k, v in request_body.logit_bias.items()}
        if request_body.user is not None:
            create_params["user"] = str(request_body.user)

        # 处理messages中的tool_calls，将type改为function
        if 'messages' in create_params:
            for message in create_params['messages']:
                if isinstance(message, dict) and 'tool_calls' in message:
                    if isinstance(message['tool_calls'], list):
                        for tool_call in message['tool_calls']:
                            if isinstance(tool_call, dict) and 'type' in tool_call:
                                tool_call['type'] = 'function'

        response = await openai_client.chat.completions.create(**create_params)
        # 处理返回结果
        if response_mode == ResponseMode.JSON:
            logger.info("OpenAI JSON 代理接口处理结果, {request_body} {response}",
                        request_body=create_params['messages'][-1], response=response.choices[0].message, uid="uid")
            # 非流式输出
            elapsed_time = time.time() - start_time
            fee = 0
            if model and model.fee and response.usage.prompt_tokens:
                fee = model.fee['input'] * response.usage.prompt_tokens + \
                    model.fee['output'] * response.usage.completion_tokens
            fee = fee / 1000000
            await create_message(db, user_id=userId or '123',
                                 message_create=MessageCreate(
                                     kind=provider_kind,
                                     app_id=app_id,
                                     query=str(
                                         create_params['messages'][-1].get('content', '')),
                                     model_name=model_name,
                                     response=response.choices[0].message.content,
                                     prompt_tokens=response.usage.prompt_tokens,
                                     response_tokens=response.usage.completion_tokens,
                                     total_tokens=response.usage.total_tokens,
                                     fee=fee,
                                     time_comsumption_in_ms=math.ceil(
                                         elapsed_time * 1000)
                                 ))
            return ChatCompletion(
                **response.model_dump()
            )
        else:
            # 流式输出
            content_chunks = []
            tool_calls_chunks = []
            tool_call_name = ''
            final_usage = {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }

            async def generate():
                try:
                    async for chunk in response:
                        chunk: ChatCompletionChunk = chunk
                        # 从 choices[0].delta.content 获取内容
                        content = chunk.choices[0].delta.content if chunk.choices and chunk.choices[0].delta else None
                        tool_calls = chunk.choices[0].delta.tool_calls if chunk.choices and chunk.choices[
                            0].delta and chunk.choices[0].delta.tool_calls else None
                        finish_reason = chunk.choices[0].finish_reason if chunk.choices and chunk.choices[0].finish_reason else None
                        if tool_calls:
                            # 增加参数拼接
                            if tool_calls[0] and tool_calls[0].function and tool_calls[0].function.arguments:
                                tool_calls_chunks.append(
                                    tool_calls[0].function.arguments)
                            if tool_calls[0] and tool_calls[0].function and tool_calls[0].function.name:
                                tool_call_name = tool_calls[0].function.name

                        # 收集内容
                        if content:
                            content_chunks.append(content)

                        # 更新usage信息
                        if hasattr(chunk, 'usage') and chunk.usage:
                            final_usage["prompt_tokens"] = chunk.usage.prompt_tokens
                            final_usage["completion_tokens"] = chunk.usage.completion_tokens
                            final_usage["total_tokens"] = chunk.usage.total_tokens

                        # 将chunk转换为JSON字符串，然后按SSE格式输出
                        chunk_json = chunk.model_dump_json()
                        yield f"data: {chunk_json}\n\n"

                        # 如果是最后一个chunk（有finish_reason且有usage）
                        if chunk.choices and hasattr(chunk, 'usage') and chunk.usage:
                            # 计算费用
                            elapsed_time = time.time() - start_time
                            fee = 0
                            if model and model.fee:
                                fee = (model.fee['input'] * final_usage["prompt_tokens"] +
                                       model.fee['output'] * final_usage["completion_tokens"]) / 1000000
                            res = ''.join(content_chunks)
                            if finish_reason == 'tool_calls':
                                tool_calls_str = ''.join(tool_calls_chunks)
                                res = f'function: {tool_call_name}, arguments: {tool_calls_str}'
                            logger.info("OpenAI 流式代理接口处理结果, {request_body} {response}",
                                        request_body=create_params['messages'][-1], response=res, uid="uid")

                            # 创建消息记录
                            await create_message(
                                db,
                                user_id=userId or '123',
                                biz_type='agent-completion',
                                message_create=MessageCreate(
                                    kind=provider_kind,
                                    app_id=app_id,
                                    query=str(
                                         create_params['messages'][-1].get('content', '')),
                                    model_name=model_name,
                                    response=res,
                                    prompt_tokens=final_usage["prompt_tokens"],
                                    response_tokens=final_usage["completion_tokens"],
                                    total_tokens=final_usage["total_tokens"],
                                    fee=fee,
                                    time_comsumption_in_ms=math.ceil(
                                        elapsed_time * 1000)
                                )
                            )
                except Exception as e:
                    print(f"Stream processing error: {str(e)}")
                    raise e

            return StreamingResponse(
                generate(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream",
                }
            )
    except Exception as e:
        logger.error("OpenAI Chat Completions API 代理接口处理出错: {e}, {request_body}", e=str(
            e), request_body=request_body, uid="uid")
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


# models接口
@router.get("/app/{app_id}/openai/models", dependencies=[Depends(admit_by_token)])
async def get_models(app_id: str):
    """
    获取应用的模型列表
    """
    models = await fetch_all_models()
    # 按openai的模型格式返回
    return {
        "object": "list",
        "data": [
            {
                "id": model.id,
                "object": "model",
                "owned_by": model.providerKind
            }
            for model in models
        ]
    }


@router.post("/aigc/get-diff")
async def get_diff(
        body: dict = Body()
):
    """
    获取应用的模型列表
    """
    diff = generate_diff(body['diffObj'])
    # 按openai的模型格式返回
    return {
        "diff": diff
    }
