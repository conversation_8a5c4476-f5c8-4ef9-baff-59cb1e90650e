from fastapi import APIRouter, Depends, Request

from ..misc.middleware import ValidationErrorLoggingRoute

from ..misc.session import SessionUser
from ..schema.document_task import DocumentTask, DocumentTaskUpdate
from ..schema.common import ResponseData
from ..misc.db import get_db
from ..service.document_task import get as get_document_task_service, \
    update as update_document_task_service, \
    delete_with_check as delete_document_task_service


router = APIRouter(prefix='/document-task',
                   route_class=ValidationErrorLoggingRoute)


@router.get('/{document_task_id}', tags=['document-task'])
async def get_document_task(document_task_id: str, db=Depends(get_db)) -> ResponseData[DocumentTask]:
    return ResponseData(data=DocumentTask.model_validate(
        await get_document_task_service(db, document_task_id)))


@router.put('/{document_task_id}', tags=['document-task'])
async def update_document_task(document_task_id: str, document_update: DocumentTaskUpdate, request: Request,
                               db=Depends(get_db)) -> ResponseData[DocumentTask]:
    user: SessionUser = request.user
    db_document_task = await update_document_task_service(db, user.id, document_task_id, document_update)
    return ResponseData(data=DocumentTask.model_validate(db_document_task))


@router.delete('/{document_task_id}', tags=['document-task'], status_code=204)
async def delete_document_task(document_task_id: str, request: Request, db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_document_task_service(db, user.id, document_task_id)
