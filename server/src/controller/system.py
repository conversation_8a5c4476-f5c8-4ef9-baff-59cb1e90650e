from fastapi import APIRouter, Depends

from ..service.system import get_notify_service


from ..misc.middleware import ValidationErrorLoggingRoute
from ..schema.common import ResponseDataWithCode
from ..misc.db import get_db


router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/system/notify')
async def get_notify() -> ResponseDataWithCode:
    notify = get_notify_service()
    return ResponseDataWithCode(code=200, data={
        'result': notify
    })
