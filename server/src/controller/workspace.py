from typing import Annotated, List, Optional
from uuid import UUI<PERSON>
from sqlalchemy.orm import Session
from fastapi import APIRouter, Depends, Request, Query, Form, File, UploadFile
import json

from ..misc.middleware import ValidationErrorLoggingRoute
from ..config import settings
from ..schema.group import Group, GroupCreate, GroupWithRole
from ..schema.document import Document, DocumentCreate
from ..schema.document_task import DocumentTask, DocumentTaskCreate
from ..schema.member import MemberBase, MemberGet, MemberType, GroupRole
from ..schema.common import ResourceType
from ..service.group import create_with_member, list_by_workspace, sync_groups_to_redis
from ..service.document import create_document_with_member, list_document_by_knowledge, create_ada_document
from ..service.document_task import create_document_task_with_member, list_document_task_by_knowledge, \
    create_ada_document_task, check_ada_document_task
from ..schema.common import Pages, ResponseData, Items
from ..misc.session import <PERSON><PERSON><PERSON>
from ..schema.workspace import Workspace, <PERSON>pace<PERSON><PERSON>, WorkspaceUpdate
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db
from ..service.member import list_user as list_user_member_service
from ..service.workspace import access_workspace, get as get_workspace_service, \
    list as list_workspace_service, list_with_creator, sync_workspaces_to_redis, \
    update as update_workspace_service, \
    delete_with_check as delete_workspace_service, \
    create_with_initial as create_workspace_with_initial, update_creator, get_workspace_admins as get_workspace_admins_service
from ..misc.errors import InternalServerError


router = APIRouter(prefix='/workspace',
                   route_class=ValidationErrorLoggingRoute)


@router.get('', dependencies=[Depends(admit_by_cookie)], tags=['workspace'],
            description="List user's workspaces, admin can list all \
                workspaces with query param 'all'")
async def list_workspace(pages: Annotated[Pages, Depends(Pages)],
                         request: Request,
                         source: Optional[str] = Query(
                             default='mine', alias='source'),
                         db=Depends(get_db)) -> ResponseData[Items[Workspace]]:
    user: SessionUser = request.user
    workspace_ids: Optional[List[str]] = None
    user_id = user.id
    # (db_workspaces, total) = await list_with_creator(db, user_id=settings.workspace_member_id, offset=pages.offset, limit=pages.limit, creation_time_asc=True)
    (db_workspaces, total) = await list_workspace_service(db, user_id=settings.workspace_member_id, offset=pages.offset, limit=pages.limit, creation_time_asc=True)
    workspace_ids = [str(w.id) for w in db_workspaces]
    if source != 'netease':
        (db_workspaces, total) = await list_workspace_service(
            db,
            ids=workspace_ids,
            user_id=user_id,
            offset=pages.offset,
            limit=pages.limit,
            creation_time_asc=True)
    # 如果default_workspace_id不在workspace_ids中，则将该用户加入
    if settings.default_workspace_id not in workspace_ids:
        await access_workspace(db, settings.default_workspace_id, user_id)
        db_workspace = await get_workspace_service(db, settings.default_workspace_id)
        workspace_ids.append(settings.default_workspace_id)
        db_workspaces.append(db_workspace)

    return ResponseData.new_items(items=[Workspace.model_validate(db_workspace)
                                         for db_workspace in db_workspaces],
                                  total=total)


@router.post('', status_code=201, tags=['workspace'])
async def create_workspace(workspace_create: WorkspaceCreate,
                           request: Request, db=Depends(get_db)) \
        -> ResponseData[Workspace]:
    user: SessionUser = request.user
    if not user.is_authenticated:
        raise Exception("Only authenticated user can create workspace")
    db_workspace = await create_workspace_with_initial(
        db,
        workspace_create=workspace_create,
        user_id=user.id,
        user_name=user.name)
    return ResponseData(data=Workspace.model_validate(db_workspace))


@router.get("/sync", tags=['workspace', "workspace"])
async def sync_workspace(db=Depends(get_db)):
    res = await sync_workspaces_to_redis(db)
    return {"message": "success", "data": res}


@router.get('/update-creators', dependencies=[Depends(admit_by_cookie)])
async def update_workspace_creators(id: str,
                                    mail: str,
                                    request: Request,
                                    db=Depends(get_db)) \
        -> ResponseData[Items[Workspace]]:
    user: SessionUser = request.user
    ids = id.split(',')
    workspaces = []
    for id in ids:
        db_workspace = await update_creator(db,
                                            user.id,
                                            str(id),
                                            mail)
        workspaces.append(Workspace.model_validate(db_workspace))
    return ResponseData.new_items(items=workspaces, total=len(workspaces))


@router.get('/{workspace_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['workspace'])
async def get_workspace(workspace_id: UUID, db=Depends(get_db)) \
        -> ResponseData[Workspace]:
    db_workspace = await get_workspace_service(db, str(workspace_id))
    return ResponseData(data=Workspace.model_validate(db_workspace))


@router.get('/{workspace_id}/admins',
            tags=['workspace'])
async def get_workspace_admins(workspace_id: UUID, db=Depends(get_db)) \
        -> ResponseData[Items[MemberGet]]:
    members, total = await get_workspace_admins_service(db, str(workspace_id))

    member_list = []
    for member in members:
        member_list.append(MemberGet.model_validate(member))

    return ResponseData.new_items(items=member_list, total=total)


# 该接口只是为了兼容老的前端接口定义，后续会删除
@router.get('/{workspace_id}/admin',
            tags=['workspace'])
async def get_workspace_admin(workspace_id: UUID, db=Depends(get_db)) \
        -> ResponseData[Items[MemberGet]]:
    members, total = await get_workspace_admins_service(db, str(workspace_id))

    member_list = []
    for member in members:
        member_list.append(MemberGet.model_validate(member))

    return ResponseData.new_items(items=member_list, total=total)


@router.put('/{workspace_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['workspace'])
async def update_workspace(workspace_id: UUID,
                           workspace_update: WorkspaceUpdate,
                           request: Request,
                           db=Depends(get_db)) \
        -> ResponseData[Workspace]:
    user: SessionUser = request.user
    db_workspace = await update_workspace_service(db,
                                                  user.id,
                                                  str(workspace_id),
                                                  workspace_update)
    return ResponseData(data=Workspace.model_validate(db_workspace))


@router.get('/{workspace_id}/creator', dependencies=[Depends(admit_by_cookie)],
            tags=['workspace'])
async def update_workspace_creator(workspace_id: UUID,
                                   mail: str,
                                   request: Request,
                                   db=Depends(get_db)) \
        -> ResponseData[Workspace]:
    user: SessionUser = request.user
    db_workspace = await update_creator(db,
                                        user.id,
                                        str(workspace_id),
                                        mail)
    return ResponseData(data=Workspace.model_validate(db_workspace))


@router.delete('/{workspace_id}', dependencies=[Depends(admit_by_cookie)],
               tags=['workspace'], status_code=204)
async def delete_workspace(workspace_id: UUID,
                           request: Request,
                           db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_workspace_service(db, user.id, str(workspace_id))


@router.get("/{workspace_id}/group", dependencies=[Depends(admit_by_cookie)],
            tags=['workspace', "group"])
async def list_groups(workspace_id: str,
                      request: Request,
                      pages: Annotated[Pages, Depends(Pages)],
                      db: Session = Depends(get_db)) \
        -> ResponseData[Items[GroupWithRole]]:

    user: SessionUser = request.user
    # 首先看用户是不是在workspace的admin中
    _, total = await list_user_member_service(db=db, user_id=user.id, resource_id=str(workspace_id), role='admin', resource_type='workspace')
    is_admin = total > 0
    include_groups = []
    # 其次看用户有哪些 group 的权限
    members, _ = await list_user_member_service(
        db,
        user_id=user.id,
        resource_type=ResourceType.GROUP.value)
    for member in members:
        include_groups.append(member.resource_id)
    # 最后看用户有哪些 group 的权限，先全列出来
    (db_groups, count) = await list_by_workspace(db,
                                                 workspace_id,
                                                 limit=pages.limit,
                                                 offset=pages.offset)
    # 然后针对 include_groups，设置为有权限，其他的设置为无权限
    for db_group in db_groups:
        if db_group.id in include_groups or is_admin or str(db_group.name) == '默认分组':
            db_group.role = GroupRole.INTERNAL_USER.value
        else:
            db_group.role = GroupRole.EXTERNAL_USER.value
    return ResponseData.new_items(items=[
        GroupWithRole.model_validate(db_group) for db_group in db_groups
    ], total=count)


@router.post("/{workspace_id}/group", dependencies=[Depends(admit_by_cookie)],
             tags=['workspace', "group"], status_code=201)
async def create_group(workspace_id: str,
                       group_create: GroupCreate,
                       request: Request,
                       db: Session = Depends(get_db)) -> ResponseData[Group]:
    user: SessionUser = request.user
    db_group = await create_with_member(db, user.id, workspace_id,
                                        group_create)
    group = Group.model_validate(db_group)
    return ResponseData(data=group)


@router.get("/{workspace_id}/document", tags=['workspace', "document"])
async def list_document(pages: Annotated[Pages, Depends(Pages)],
                        ids=Query(
                            None, alias="ids"),
                        knowledgeId: str = Query(
                            None, alias="knowledgeId"),
                        collectionId: int = Query(
                            None, alias="collectionId"),
                        keyword: str = Query(
                            None, alias="keyword"),
                        db: Session = Depends(get_db)) \
        -> ResponseData[Items[Document]]:
    if ids is not None and len(ids) > 0:
        ids = ids.split(',')

    (db_documents, count) = await list_document_by_knowledge(db,
                                                             ids=ids,
                                                             knowledge_id=knowledgeId,
                                                             collection_id=collectionId,
                                                             keyword=keyword,
                                                             limit=pages.limit,
                                                             offset=pages.offset)
    return ResponseData.new_items(items=[
        Document.model_validate(db_document) for db_document in db_documents
    ], total=count)


@router.post("/{workspace_id}/document", tags=['workspace', "document"], status_code=201)
async def create_document(workspace_id: str,
                          knowledgeId: Annotated[str, Form()],
                          title: Annotated[str, Form()],
                          type: Annotated[str, Form()],
                          source: Annotated[str, Form()],
                          file: Annotated[List[UploadFile], File()],
                          config: Annotated[str, Form()],
                          collectionId: Annotated[str, Form()],
                          request: Request,
                          db: Session = Depends(get_db)):
    user: SessionUser = request.user
    configJson = json.loads(config)
    list = []
    for fileItem in file:
        ada_document = await create_ada_document(collectionId, files=fileItem, importConfig=config, username=user.name)
        if 'error' in ada_document:
            raise InternalServerError(ada_document['error']['message'])
        if fileItem.filename:
            title = fileItem.filename
        document_create = DocumentCreate(
            knowledge_id=knowledgeId, title=title, type=type, source=source, config=configJson,
            doc_id=ada_document['docId'])
        db_document = await create_document_with_member(db, user.id, workspace_id,
                                                        document_create)
        document = Document.model_validate(db_document)
        list.append(document)
    return ResponseData(data=list)


@router.get("/{workspace_id}/document-task", tags=['workspace', "document-task"])
async def list_document_task(pages: Annotated[Pages, Depends(Pages)],
                             ids=Query(
    None, alias="ids"),
    knowledgeId: str = Query(
    None, alias="knowledgeId"),
    keyword: str = Query(
    None, alias="keyword"),
    db: Session = Depends(get_db)) \
        -> ResponseData[Items[DocumentTask]]:
    if ids is not None and len(ids) > 0:
        ids = ids.split(',')

    (db_documentTasks, count) = await list_document_task_by_knowledge(db,
                                                                      ids=ids,
                                                                      knowledge_id=knowledgeId,
                                                                      keyword=keyword,
                                                                      limit=pages.limit,
                                                                      offset=pages.offset)
    return ResponseData.new_items(items=[
        DocumentTask.model_validate(db_documentTask) for db_documentTask in db_documentTasks
    ], total=count)


@router.post("/{workspace_id}/document-task", tags=['workspace', "document-task"], status_code=201)
async def create_document_task(workspace_id: str,
                               document_task_create: DocumentTaskCreate,
                               request: Request,
                               collectionId: int,
                               db: Session = Depends(get_db)) -> ResponseData[DocumentTask]:
    user: SessionUser = request.user
    ada_task = await create_ada_document_task(collectionId, document_task_create.config, username=user.name)
    ada_task_id = ada_task['detail']['data']['taskId']
    ada_task_state = ada_task['detail']['data']['state']
    document_task_create.task_id = ada_task_id
    document_task_create.task_state = ada_task_state
    db_documentTask = await create_document_task_with_member(db, user.id, workspace_id,
                                                             document_task_create)
    documentTask = DocumentTask.model_validate(db_documentTask)
    return ResponseData(data=documentTask)


@router.get("/{workspace_id}/sync-doc-task", tags=['workspace', "document-task"])
async def sync_doc_task():
    tasks = await check_ada_document_task()
    return tasks
