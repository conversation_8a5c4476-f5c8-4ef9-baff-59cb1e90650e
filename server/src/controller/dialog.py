from ..schema.model import ModelProviderKind
from ..service.chat import \
    get_conversation as get_conversation_service, \
    list_message as list_message_service, \
    list_message_by_app, \
    list_message_tool_call_response as list_message_tool_call_response_service, \
    update_conversation as update_conversation_service, \
    get_or_create_db_conversation as get_or_create_db_conversation_service, \
    list_conversation as list_conversation_service, \
    delete_conversation as delete_conversation_service
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db
from ..service.depends import get_basic_app
from ..service.app \
    import get as get_app_service, \
    get_config as get_app_config_service
from ..service.dialog import appendMultiModalMessages, config_merge, construct_messages, \
    construct_multi_modal_messages, dialogue, IS_CHUNK_RESPONSE, test_models_performance, user_prompt
from ..service.agent_workflow_chat_history import \
    create as create_chat_history_service, \
    get as get_chat_history_service, \
    list_component_statuses as list_component_statuses_service, \
    create_component_statuses as create_component_statuses_service, \
    update_component_status as update_component_status_service
import asyncio
import json
import logging
import traceback
from datetime import datetime
from typing import Annotated, Dict, List, Optional, Union
from xml.dom.minidom import Node
from fastapi import APIRouter, Depends, Header, Query, Request
from loguru import logger
from sse_starlette import ServerSentEvent
from sse_starlette.sse import EventSourceResponse
from sqlalchemy.orm import Session

from ..misc.middleware import ValidationErrorLoggingRoute

from ..misc.log import logger_info

from ..workflow.agent_workflow_engine import EngineRunFailed, NodeDisplayResult, NodeHTTPResult, NodeLLMJSONResult, NodeLLMStreamResult, NodeOutputResult, NodeResult, NodeRunningStatus, NodeScriptResult, StreamStage, engine

from ..models.agent_workflow import AgentWorkflowComponentStatus

from ..schema.component import ComponentType

from ..schema.agent_workflow_chat_history import \
    AWComponentLLMRunningResult, \
    AWComponentRunningStatus, \
    AWComponentStatusCreate, \
    AWComponentStatusUpdate, \
    AWHistoryCreate, \
    AWRunningStatus

from ..misc.session import SessionUser

from ..schema.plugin import Plugin

from ..lib.toolpool import get_tool_pool
from pydantic import BaseModel

from ..schema.account import user_id_from_request
from ..models.app import App
from ..schema.app import AgentConfig, AgentWorkflowConfig, AgentWorkflowDebug
from ..schema.common import Items, Pages, ResponseData
from ..misc.errors import BadRequest, InternalServerError, NotFound
from ..lib.llms.excutor import DomainLLMRunner
from ..schema.dialog import \
    CompletionChunk, \
    ConversationInnerUpdate, \
    ConversationResp, \
    ConversationChunk, \
    ConversationCreate, \
    CompletionRequest, \
    ConversationGet, \
    ConversationMessage, \
    ConversationRequest, \
    ConversationUpdate, \
    MessageItem, \
    MessageResponseType, \
    ToolCallInMessage, \
    ResponseMode, \
    CompletionResp
from fastapi.responses import StreamingResponse
import pandas as pd
from io import BytesIO
from urllib.parse import quote


router = APIRouter(route_class=ValidationErrorLoggingRoute)

CONTENT_TYPE_SSE = 'text/event-stream'
CONTENT_TYPE_JSON = 'application/json'


class Range(BaseModel):
    start: Optional[str]
    end: Optional[str]


async def range(start: Annotated[Optional[str], Query(alias='start')] = None,
                end: Annotated[Optional[str], Query(alias='end')] = None):
    return Range(start=start, end=end)


async def get_plugins_from_config(
        db: Session,
        config: AgentConfig,
) -> Optional[List[Plugin]]:
    tool_pool = await get_tool_pool()
    tools_from_config = config.tools
    if tools_from_config is None or len(tools_from_config) == 0:
        return None

    tool_ids = tools_from_config.keys()

    tools: List[Plugin] = []
    for tool_id in tool_ids:
        try:
            tool = await tool_pool.get(db, tool_id)
            tools.append(tool[0])
        except NotFound:
            logger_info(f'tool {tool_id} not found')
    return tools

# def loadUserQuery(query):
#     try:
#         contents = loads(query)
#         return [
#             ChatTextParam.model_validate(content) if content['type'] == 'text'
#             else ChatImageParam.model_validate(content) for content in contents]
#     except JSONDecodeError:
#         return query


# async def construct_messages(
#     db: Session,
#     conversation_id: Optional[str],
#     config: AgentConfig,
#     prompt_params: Optional[dict],
# ):
#     messages: List[LLMMessage] = list()

#     prompt = await system_prompt(config, prompt_params)  # type: ignore
#     if prompt is not None:
#         messages.append(prompt)

#     if conversation_id is not None:
#         db_messages, count = await list_message_service(
#             db=db,
#             conversation_id=conversation_id,
#             limit=settings.conversation_history_limit,
#             ascending=True)

#         for db_message in db_messages:
#             msg = ConversationMessage.model_validate(db_message)

#             if msg.query != '':
#                 messages.append(LLMMessage(
#                     role='user',
#                     content=loadUserQuery(msg.query),
#                 ))

#             if str(db_message.response_type) == MessageResponseType.TOOL_CALLS.value:
#                 messages.append(AILLMMessage(
#                     tool_calls=msg.response,  # type: ignore
#                 ))
#                 tool_call_repsonsess, _ = await list_message_tool_call_response_service(
#                     db,
#                     message_id=str(db_message.id),
#                     ascending=True,
#                 )
#                 for tool_call_repsonse in tool_call_repsonsess:
#                     messages.append(ToolLLMMessage(
#                         tool_call_id=str(tool_call_repsonse.tool_call_id),
#                         content=str(tool_call_repsonse.response),
#                     ))
#             else:
#                 if msg.response is not None:
#                     messages.append(AILLMMessage(
#                         content=msg.response  # type: ignore
#                     ))

#     return messages


# async def system_prompt(
#         config: Optional[AgentConfig],
#         prompt_params: Optional[dict],
# ):
#     prompt = await render_prompt_service(config, prompt_params)
#     if prompt is not None:
#         return LLMMessage(
#             role='system',
#             content=prompt
#         )


async def get_tool_calls_in_message(
    db: Session,
    tool_calls: Optional[List[dict]] = None,
    tool_response: Optional[Dict[str, str]] = None,
):

    tool_call_in_message: List[ToolCallInMessage] = []
    if tool_calls and tool_response:
        pool = await get_tool_pool()
        for tool_call in tool_calls:
            id = tool_call.get('function', {}).get("name")
            try:

                tool, _, _ = await pool.get(db, id)
                if tool is None:
                    logger.bind(tool_call=tool_call).warning(
                        "tool call not found")
                    continue
                tool_call_in_message.append(ToolCallInMessage(
                    name=tool.name,
                    arguments=tool_call.get(
                        'function', {}).get("arguments"),
                    response=tool_response.get(
                        tool_call.get('id', ''), ''),
                ))
            except NotFound:
                logger.bind(tool_id=id).warning("tool not found")

    if len(tool_call_in_message) == 0:
        return None
    return tool_call_in_message


# async def get_tool_calls_list_in_message(
#     tool_calls: Optional[List[List[dict]]] = None,
#     tool_responses: Optional[List[Dict[str, str]]] = None,
# ):

#     tool_call_in_messages: List[List[ToolCallInMessage]] = []

#     if tool_calls and tool_responses:
#         for loop_index, tool_call_list in enumerate(tool_calls):
#             tool_response = tool_responses[loop_index]
#             tool_call_in_message = await get_tool_calls_in_message(tool_call_list, tool_response)

#             if tool_call_in_message and len(tool_call_in_message) > 0:
#                 tool_call_in_messages.append(tool_call_in_message)
#     return tool_call_in_messages


async def conv_stream_response(db: Session, resp):
    if isinstance(resp, dict):
        if resp.get(IS_CHUNK_RESPONSE):
            chunk = ConversationChunk.model_validate(resp)
            chunk.message_tool_calls = await get_tool_calls_in_message(
                db, chunk.tool_calls, chunk.tool_responses)
            t = chunk.model_dump_json(by_alias=True, exclude_none=True)
            yield {'event': 'message', 'data': t}
        elif isinstance(resp, dict) and resp.get('event'):
            yield resp
        else:
            conv_resp = ConversationResp.model_validate(resp)
            if conv_resp.tool_calls is not None:
                conv_resp.message_tool_calls = await get_tool_calls_in_message(
                    db, conv_resp.tool_calls, conv_resp.tool_responses)
                yield conv_resp.model_dump_json(by_alias=True, exclude_none=True)
            elif conv_resp.content is not None:
                for content_chunk in conv_resp.content:
                    yield ConversationChunk(
                        content=content_chunk,
                        messageID=conv_resp.messageID,
                        conversationID=conv_resp.conversationID,
                    ).model_dump_json(by_alias=True, exclude_none=True)


async def comp_stream_response(db: Session, resp):
    if isinstance(resp, dict):
        if resp.get(IS_CHUNK_RESPONSE):
            chunk = CompletionChunk.model_validate(resp)
            chunk.message_tool_calls = await get_tool_calls_in_message(
                db, chunk.tool_calls, chunk.tool_responses)
            yield chunk.model_dump_json(by_alias=True, exclude_none=True)
        elif isinstance(resp, dict) and resp.get('event'):
            yield resp
        else:
            comp_resp = CompletionResp.model_validate(resp)
            if comp_resp.tool_calls is not None:
                comp_resp.message_tool_calls = await get_tool_calls_in_message(
                    db, comp_resp.tool_calls, comp_resp.tool_responses)
                yield comp_resp.model_dump_json(by_alias=True, exclude_none=True)
            elif comp_resp.content is not None:
                for content_chunk in comp_resp.content:
                    yield CompletionChunk(
                        content=content_chunk,
                        messageID=comp_resp.messageID,
                    ).model_dump_json(by_alias=True, exclude_none=True)


@router.post('/app/{app_id}/completion', tags=['dialog'])
async def completion(
    app_id: str,
    params: CompletionRequest,
    accept: Annotated[str, Header(alias="accept")] = CONTENT_TYPE_SSE,
    user_id: str = Depends(user_id_from_request),
    db: Session = Depends(get_db),
):

    app = await get_app_service(db, app_id)
    conversation = await get_or_create_db_conversation_service(
        db,
        user_id=user_id,
        conversation_create=ConversationCreate(
            app_id=app_id,
            app_config_id=str(app.app_config_id),
            override_config=params.config,
            parameters=params.parameters,
        ),
    )

    config = params.config

    app_config = await get_app_config_service(
        db,
        str(app.app_config_id))
    app_config = AgentConfig.model_validate(app_config.config)

    config = await config_merge(app_config, config)

    messages = list()
    # 通过实际测试发现，生成式使用system的效果比user效果差，可能是因为直接使用system没有紧跟user，导致无法gpt无法理解用户真正的输入的缘故
    prompt = await user_prompt(config, params.parameters)
    # prompt = await system_prompt(config, params.parameters)
    if prompt is not None:
        # 添加多模态的用户输入
        messages.append(construct_multi_modal_messages(
            params, config, prompt.content, None))
        # messages.append(prompt)

    executor = await DomainLLMRunner(
        workspace_id=str(app.workspace_id),
        group_id=str(app.group_id),
        db=db,
    )

    plugins = await get_plugins_from_config(db, config)

    resp = dialogue(
        app_id=app.id,
        config=config,
        params=params,
        biz_type='agent-completion',
        response_mode=ResponseMode.STREAMING if accept.startswith(
            CONTENT_TYPE_SSE) else ResponseMode.JSON,
        messages=messages,
        executor=executor,
        user_id=user_id,
        plugins=plugins,
        db=db,
        conversation=conversation)

    # if isinstance(resp, AsyncGenerator):
    #     return EventSourceResponse(
    #         CompletionChunk.model_validate(chunk).model_dump_json()
    #         async for chunk in resp
    #         if chunk is not None)
    # return CompletionResp.model_validate(resp)

    if accept.startswith(CONTENT_TYPE_SSE):
        return EventSourceResponse(
            streaming_reap
            async for r in resp
            async for streaming_reap in comp_stream_response(db, r)
        )
    else:
        content = ''
        responses = []
        async for r in resp:
            logger.bind(r=r).debug('response')
            if r is None:
                r = {}
            if r.get(IS_CHUNK_RESPONSE):
                chunk = CompletionChunk.model_validate(r)
                content += chunk.content  # type: ignore
                conv_resp = CompletionResp(
                    content=content,
                    messageID=chunk.messageID,
                )
                if len(responses) == 0:
                    responses.append(conv_resp)
                else:
                    responses[-1] = conv_resp
            else:
                comp_resp = CompletionResp.model_validate(r)
                if comp_resp.tool_calls is not None:
                    comp_resp.message_tool_calls = await get_tool_calls_in_message(
                        db, comp_resp.tool_calls, comp_resp.tool_responses)
                responses.append(comp_resp)
        return responses
        # final_response = None
        # async for r in resp:
        #     final_response = r
        # return final_response


@router.get("/app/{app_id}/conversation",
            dependencies=[Depends(admit_by_cookie)],
            tags=['dialog'])
async def list_conversation(
    app_id: str,
    conversation_id: Annotated[Optional[str],
                               Query(alias='conversationID')] = None,
    user_id: str = Depends(user_id_from_request),
    pages: Pages = Depends(Pages),
    db: Session = Depends(get_db),
):
    if conversation_id is not None:
        conversation = await get_conversation_service(
            db=db,
            conversation_id=conversation_id,
            user_id=user_id,
        )
        return ResponseData(data=ConversationGet.model_validate(conversation))

    db_convs, count = await list_conversation_service(
        db,
        app_id=app_id,
        user_id=user_id,
        limit=pages.limit,
        offset=pages.offset)

    return ResponseData.new_items(
        items=[ConversationGet.model_validate(conv) for conv in db_convs],
        total=count)


async def gen_name_for_conversation(
        executor,
        user_id: str,
        message: str,
        config: AgentConfig,
        db: Session,
        conversation_id: str):
    try:
        generated_name = await executor.generate_name_for_query(
            model_name=config.modelName,
            provider_kind=config.providerKind,
            query=message,
            user_id=user_id)
        await update_conversation_service(
            db=db,
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_update=ConversationInnerUpdate(
                name=generated_name.content[:120],
                prompt_tokens_for_name=generated_name.prompt_tokens,
                response_tokens_for_name=generated_name.response_tokens,
                total_tokens_for_name=generated_name.total_tokens,
            )
        )
    except Exception as e:
        logger.error('failed to generate name for query {e}', e=e, uid="uid")


@router.post('/app/{app_id}/chat', tags=['dialog'])
async def chat(
    app_id: str,
    params: ConversationRequest,
    accept: Annotated[str, Header(alias="accept")] = CONTENT_TYPE_SSE,
    gen_name: Annotated[bool, Query(alias='genName')] = True,
    conversation_id: Annotated[Optional[str],
                               Query(alias='conversationID')] = None,
    user_id: str = Depends(user_id_from_request),
    db: Session = Depends(get_db),
):
    start_time = datetime.now()
    app = await get_app_service(db, app_id)
    config = params.config or None
    conversation = await get_or_create_db_conversation_service(
        db,
        user_id=user_id,
        conversation_create=ConversationCreate(
            app_id=app_id,
            app_config_id=str(app.app_config_id),
            override_config=params.config,
            parameters=params.parameters,
        ),
        conversation_id=conversation_id,
    )

    if conversation.override_config is not None and conversation.override_config != "":
        config = AgentConfig.model_validate(conversation.override_config)
        if conversation_id is not None and params.config is not None:
            logger.bind(conversationID=conversation_id).warning(
                'conversation has have an override config, config in paramters is ignored',
                uid="uid")
    if params.config is not None:
        config = AgentConfig.model_validate(params.config)

    app_config = await get_app_config_service(
        db,
        str(app.app_config_id))
    app_config = AgentConfig.model_validate(app_config.config)

    config = await config_merge(app_config, config)

    prompt_params = params.parameters or {}
    if conversation_id is not None:
        # 更新一下conversation的parameters
        await update_conversation_service(
            db=db,
            user_id=user_id,
            conversation_id=conversation_id,
            conversation_update=ConversationUpdate(parameters=prompt_params)
        )

    modelParams = config.modelParams
    max_conversations = None
    if modelParams is not None:
        max_conversations = modelParams.get('max_conversations')
        if max_conversations is not None:
            try:
                max_conversations = int(max_conversations)
            except ValueError:
                logging.error("max_conversations 值无法转换为整数")
                max_conversations = None
    else:
        logging.error("modelParams 对象为 None")
        max_conversations = None

    messages = await construct_messages(
        db=db,
        conversation_id=conversation_id,
        config=config,
        prompt_params=prompt_params,  # type: ignore
        max_conversations=max_conversations,
    )
    # 添加多模态的用户输入
    appendMultiModalMessages(params, config, messages, prompt_params)

    plugins = await get_plugins_from_config(db, config)
    end_time1 = datetime.now()
    cost1 = int((end_time1 - start_time).total_seconds() * 1000)
    logger.info('/chat应用：{app_id} 上下文耗时: {cost1}',
                uid='uid', app_id=app_id, cost1=cost1)
    executor = await DomainLLMRunner(
        workspace_id=str(app.workspace_id),
        group_id=str(app.group_id),
        user_id=user_id,
        db=db,
    )

    try:
        resp = dialogue(
            app_id=app.id,
            params=params,
            response_mode=ResponseMode.STREAMING if accept.startswith(
                CONTENT_TYPE_SSE) else ResponseMode.JSON,
            user_id=user_id,
            config=config,
            db=db,
            biz_type='agent-conversation',
            messages=messages,
            executor=executor,
            plugins=plugins,
            conversation=conversation)

        async def handle_resp():
            if accept.startswith(CONTENT_TYPE_SSE):
                return EventSourceResponse(
                    streaming_reap
                    async for r in resp
                    async for streaming_reap in conv_stream_response(db, r)
                )
            else:
                content = ''
                responses = []
                async for r in resp:
                    logger.bind(r=r).debug('response')
                    if r is None:
                        r = {}
                    if r.get(IS_CHUNK_RESPONSE):
                        chunk = ConversationChunk.model_validate(r)
                        content += chunk.content  # type: ignore
                        conv_resp = ConversationResp(
                            content=content,
                            messageID=chunk.messageID,
                            conversationID=chunk.conversationID,
                        )
                        if len(responses) == 0:
                            responses.append(conv_resp)
                        else:
                            responses[-1] = conv_resp
                    else:
                        conv_resp = ConversationResp.model_validate(r)
                        if conv_resp.tool_calls is not None:
                            conv_resp.message_tool_calls = await get_tool_calls_in_message(
                                db, conv_resp.tool_calls, conv_resp.tool_responses)
                        responses.append(conv_resp)
                return responses

        if gen_name and conversation_id is None:
            await asyncio.create_task(gen_name_for_conversation(
                executor=executor,
                user_id=user_id,
                message=params.message,
                config=config,
                db=db,
                conversation_id=str(conversation.id),
            ))
        return await asyncio.create_task(handle_resp())
    except ExceptionGroup as e:  # noqa
        logging.error(traceback.format_exc())
        for e in e.exceptions:
            raise InternalServerError(str(e))


@router.delete('/conversation/{conversation_id}', tags=['dialog'],
               status_code=204)
async def delete_conversation(
    conversation_id: str,
    db: Session = Depends(get_db),
):
    await delete_conversation_service(db, conversation_id)
    return None


@router.get('/conversation/{conversation_id}', tags=['dialog'],
            response_model=ResponseData[ConversationGet])
async def get_conversation_controller(
    conversation_id: str,
    db: Session = Depends(get_db),
):
    conversation = await get_conversation_service(db, conversation_id)
    return ResponseData(data=ConversationGet.model_validate(conversation))


@router.put('/conversation/{conversation_id}', tags=['dialog'])
async def update_conversation(
    conversation_id: str,
    conversation_update: ConversationUpdate,
    user_id: str = Depends(user_id_from_request),
    db: Session = Depends(get_db),
):
    db_conversation = await update_conversation_service(
        db=db,
        user_id=user_id,
        conversation_id=conversation_id,
        conversation_update=conversation_update,
    )
    return ResponseData(
        data=ConversationGet.model_validate(db_conversation)
    )


@router.get('/conversation/{conversation_id}/message', tags=['dialog'],
            response_model=ResponseData[Items[ConversationMessage]])
async def list_messages(
    conversation_id: str,
    pages: Pages = Depends(Pages),
    db: Session = Depends(get_db),
):
    db_messages, count = await list_message_service(
        db=db,
        conversation_id=conversation_id,
        limit=pages.limit,
        offset=pages.offset,
        ascending=False)

    for msg in db_messages:
        ConversationMessage.model_validate(msg)
    msgs = [ConversationMessage.model_validate(msg) for msg in db_messages]

    for msg in msgs:
        responses, _ = await list_message_tool_call_response_service(
            db,
            message_id=str(msg.id),
        )

        if msg.response_type == MessageResponseType.TOOL_CALLS:
            tool_call_in_message = await get_tool_calls_in_message(
                db=db,
                tool_calls=msg.response,  # type: ignore
                tool_response={
                    str(response.tool_call_id): str(response.response)
                    for response in responses
                }
            )
            if tool_call_in_message and len(tool_call_in_message) > 0:
                msg.message_tool_calls = tool_call_in_message

    return ResponseData.new_items(
        items=msgs,
        total=count)


class MessageSearchParams(BaseModel):
    conversation_id: Optional[str] = None
    keyword: Optional[str] = None


@router.get('/app/{app_id}/messages', tags=['dialog'],
            response_model=ResponseData[Items[MessageItem]])
async def list_messages_by_app(
    app_id: str,
    conversation_id: Annotated[Optional[str],
                               Query(alias='conversationId')] = None,
    user_id: Annotated[Optional[str],
                       Query(alias='userId')] = None,
    pages: Pages = Depends(Pages),
    db: Session = Depends(get_db),
    range: Range = Depends(range),
):
    db_messages, count = await list_message_by_app(
        db=db,
        app_id=app_id,
        conversation_id=conversation_id,
        user_id=user_id,
        limit=pages.limit,
        offset=pages.offset,
        ascending=False,
        start=range.start,
        end=range.end)

    msgs = [MessageItem.model_validate(msg) for msg in db_messages]

    for msg in msgs:
        if msg.tool_call_response_ids is not None:
            responses, _ = await list_message_tool_call_response_service(
                db,
                message_id=str(msg.id),
            )
            msg.message_tool_calls = [ToolCallInMessage.model_validate(
                response) for response in responses]

        # if msg.response_type == MessageResponseType.TOOL_CALLS:
        #     tool_call_in_message = await get_tool_calls_in_message(
        #         db=db,
        #         tool_calls=msg.response,  # type: ignore
        #         tool_response={
        #             str(response.tool_call_id): str(response.response)
        #             for response in responses
        #         }
        #     )
        #     if tool_call_in_message and len(tool_call_in_message) > 0:
        #         msg.message_tool_calls = tool_call_in_message

    return ResponseData(data=Items(items=msgs, total=count))


@router.get('/basic-conversation', tags=['basic-chat'])
async def list_basic_conversation(
    pages: Annotated[Pages, Depends(Pages)],
    app: App = Depends(get_basic_app),
    user_id: str = Depends(user_id_from_request),
    db=Depends(get_db),
):
    return await list_conversation(
        app_id=app.id,
        user_id=user_id,
        pages=pages,
        db=db,
    )


@router.get('/basic-chat', tags=['basic-chat'])
async def basic_chat(
    params: ConversationRequest,
    conversation_id: str = Query(None, alias='conversationID'),
    app: App = Depends(get_basic_app),
    user_id: str = Depends(user_id_from_request),
    db=Depends(get_db),
):

    return await chat(
        app_id=app.id,
        params=params,
        conversation_id=conversation_id,
        user_id=user_id,
        db=db,
    )


# 组件解析
async def create_component_statuses(db: Session,
                                    chat_id: str,
                                    idx: int,
                                    config: AgentWorkflowConfig,
                                    create_llm_node: bool) -> List[AgentWorkflowComponentStatus]:
    component_create_requests: List[AWComponentStatusCreate] = []
    for node in config.nodes:
        # input 和 output 节点不需要记录状态，因为底层不运行这两种节点
        if node.type == ComponentType.OUTPUT or node.type == ComponentType.INPUT:
            continue
        if not create_llm_node and node.type == ComponentType.LLM:
            continue
        component_create_requests.append(
            AWComponentStatusCreate(
                component_id=node.id,
                component_type=node.type,
                # LLM 节点的 idx 始终为 0
                idx=idx if node.type != ComponentType.LLM else 0,
                status=AWComponentRunningStatus.PENDING,
                result=None
            )
        )

    statuses = await create_component_statuses_service(
        db=db,
        chat_id=chat_id,
        create_requests=component_create_requests
    )

    # 因为 LLM 节点的数据可以记在 conversation 表和 message 表里面，所以不需要再创建 LLM 节点
    # 和 agentworkflow 关联的 llm 节点的状态只会存在一个，而其他节点，比如 http 节点，
    # 会随着每次请求的产生而产生一个新运行的状态
    if not create_llm_node:
        llm_component_statuses = await list_component_statuses_service(
            db=db,
            chat_id=chat_id,
            idx=0,
            component_type=ComponentType.LLM,
        )
        statuses.extend(llm_component_statuses)

    return statuses


async def handle_stream_response(
        db: Session,
        component_status_map: Dict[str, AgentWorkflowComponentStatus],
        chatID: str,
        resp: Union[NodeLLMJSONResult, NodeHTTPResult, NodeResult,
                    NodeLLMStreamResult, NodeScriptResult,
                    NodeDisplayResult, NodeOutputResult, EngineRunFailed]):
    if isinstance(resp, NodeLLMJSONResult):
        llm_status = component_status_map[resp.node_id]
        result = AWComponentLLMRunningResult(  # type: ignore
            conversation_id=resp.conversation_id,
        )
        # TODO: 修改为异步逻辑
        await update_component_status_service(
            db=db,
            status_id=llm_status.id,
            component_status_update=AWComponentStatusUpdate(
                status=AWComponentRunningStatus.FINISHED,
                result=result,
            )
        )
        data = {
            "nodeID": resp.node_id,
            'maxConversations': resp.max_conversations,
            "chatID": chatID,
            "content": resp.response,
            "conversationID": resp.conversation_id,
            "outputs": resp.outputs,
            "status": "finished"
        }
        event = 'moduleStatus'
    elif isinstance(resp, NodeHTTPResult):
        data = {
            "nodeID": resp.node_id,
            "chatID": chatID,
            "outputs": resp.outputs,
            "status": "finished"
        }
        event = 'moduleStatus'
    elif isinstance(resp, NodeScriptResult):
        data = {
            "nodeID": resp.node_id,
            "chatID": chatID,
            "outputs": resp.outputs,
            "status": "finished"
        }
        event = 'moduleStatus'
    elif isinstance(resp, NodeLLMStreamResult):
        # Stream 输出
        if resp.stage == StreamStage.CONTENT:
            data = {
                "content": resp.response,
                "chatID": chatID,
            }
            event = 'message'
        if resp.stage == StreamStage.END:
            llm_status = component_status_map[resp.node_id]
            result = AWComponentLLMRunningResult(  # type: ignore
                conversation_id=resp.conversation_id,
            )
            await update_component_status_service(
                db=db,
                status_id=llm_status.id,
                component_status_update=AWComponentStatusUpdate(
                    status=AWComponentRunningStatus.FINISHED,
                    result=result,
                )
            )
            data = {
                "nodeID": resp.node_id,
                "chatID": chatID,
                "maxConversations": resp.max_conversations,
                "conversationID": resp.conversation_id,
                "outputs": resp.outputs,
                "status": "finished"
            }
            event = 'moduleStatus'
    elif isinstance(resp, NodeOutputResult):
        data = {
            'content': resp.response,
            "chatID": chatID,
            "status": "finished"
        }
        event = 'finish'
    elif isinstance(resp, NodeDisplayResult):
        data = {
            "nodeID": resp.node_id,
            "chatID": chatID,
            "outputs": resp.outputs,
            "status": "finished"
        }
        event = 'moduleStatus'
    elif isinstance(resp, NodeResult):
        if resp.running_status == NodeRunningStatus.RUNNING:
            data = {
                "nodeID": resp.node_id,
                "nodeName": resp.node_name,
                "status": resp.running_status.value,
                "inputs": resp.inputs,
            }
            event = 'moduleStatus'
    elif isinstance(resp, EngineRunFailed):
        data = {
            "status": "failed",
            "error": "http timeout" if isinstance(resp.args[0], asyncio.TimeoutError) else str(resp),
        }
        event = 'error'
    return ServerSentEvent(
        data=json.dumps(data),
        event=event,
    )


async def handle_json_response(
        db: Session,
        component_status_map: Dict[str, AgentWorkflowComponentStatus],
        resp: Union[NodeLLMJSONResult, NodeHTTPResult, NodeResult,
                    NodeLLMStreamResult, NodeScriptResult,
                    NodeDisplayResult, NodeOutputResult, EngineRunFailed]):
    if isinstance(resp, NodeLLMJSONResult):
        llm_status = component_status_map[resp.node_id]
        result = AWComponentLLMRunningResult(  # type: ignore
            conversation_id=resp.conversation_id,
        )
        # TODO: 修改为异步逻辑
        await update_component_status_service(
            db=db,
            status_id=llm_status.id,
            component_status_update=AWComponentStatusUpdate(
                status=AWComponentRunningStatus.FINISHED,
                result=result,
            )
        )
    elif isinstance(resp, NodeLLMStreamResult):
        if resp.stage == StreamStage.END:
            llm_status = component_status_map[resp.node_id]
            result = AWComponentLLMRunningResult(  # type: ignore
                conversation_id=resp.conversation_id,
            )
            await update_component_status_service(
                db=db,
                status_id=llm_status.id,
                component_status_update=AWComponentStatusUpdate(
                    status=AWComponentRunningStatus.FINISHED,
                    result=result,
                )
            )
    elif isinstance(resp, NodeOutputResult):
        return resp
    elif isinstance(resp, EngineRunFailed):
        raise resp


@router.post("/app/{app_id}/agentw-chat", dependencies=[Depends(admit_by_cookie)], tags=['dialog'])
async def agentw_chat(
    app_id: str,
    debug_param: AgentWorkflowDebug,
    db: Session = Depends(get_db),
    user_id: str = Depends(user_id_from_request),
):
    app = await get_app_service(db=db, app_id=app_id)
    config_id = app.app_config_id
    if debug_param.config_id:
        config_id = debug_param.config_id

    config = await get_app_config_service(db=db, config_id=str(config_id))
    if str(config.app_id) != app_id:
        raise BadRequest(
            f"config_id {config_id} not belong to app_id {app_id}")

    chat_id = debug_param.chat_id
    try:
        _ = AgentWorkflowConfig.model_validate(config.config)
    except Exception as e:
        print(e)
    aw_config = AgentWorkflowConfig.model_validate(config.config)

    component_2_conversation: Dict[str, str] = {}

    if not chat_id:
        # 新建对话场景
        history = await create_chat_history_service(
            db=db,
            user_id=user_id,
            history_create=AWHistoryCreate(
                app_id=app_id,
                inputs=debug_param.inputs,
                app_config_id=str(config_id),
                status=AWRunningStatus.RUNNING
            )
        )
        chat_id = str(history.id)
        # run_count 加一
        history.run_count = 1  # type: ignore
        component_statuses = await create_component_statuses(
            db=db,
            chat_id=history.id,
            idx=1,
            config=aw_config,
            create_llm_node=True)
    else:
        # 使用已有对话场景
        history = await get_chat_history_service(db=db, history_id=chat_id)
        if str(history.app_id) != app_id:
            raise BadRequest(
                f"chat_id {chat_id} not belong to app_id {app_id}")
        if str(history.app_config_id) != str(config_id):
            raise BadRequest(
                f"chat_id {chat_id} not belong to config_id {config_id}")
        # 运行次数加一
        history.run_count += 1  # type: ignore
        component_statuses = await create_component_statuses(
            db=db,
            chat_id=chat_id,
            idx=history.run_count,  # type: ignore
            config=aw_config,
            create_llm_node=True)
        for status in component_statuses:
            if status.component_type == ComponentType.LLM.value and status.result is not None:
                component_2_conversation[str(
                    status.component_id)] = status.result.get('conversation_id')

    component_status_map = {
        str(status.component_id): status for status in component_statuses}
    generator = engine.run(
        db=db,
        user_id=user_id,
        app=app,
        input_values=debug_param.inputs,
        app_config_id=str(config_id),
        aw_config=aw_config,
        conversation_ids=component_2_conversation)

    if debug_param.is_stream:
        # TODO 回写状态
        return EventSourceResponse((await handle_stream_response(db, component_status_map, chat_id, r))
                                   async for r in generator)
    else:
        async for r in generator:
            res = await handle_json_response(db, component_status_map, r)
            if res:
                res_dict = res.dict()
                res_dict['chat_id'] = chat_id
                return res_dict


@router.get('/app/{app_id}/messages/export', tags=['dialog'])
async def export_messages_by_app(
    app_id: str,
    app_name: Annotated[Optional[str],
                        Query(alias='appName')] = None,
    conversation_id: Annotated[Optional[str],
                               Query(alias='conversationId')] = None,
    user_id: Annotated[Optional[str],
                       Query(alias='userId')] = None,
    db: Session = Depends(get_db),
    pages: Pages = Depends(Pages),
    range: Range = Depends(range),
):
    # 使用较大的默认页大小

    db_messages, count = await list_message_by_app(
        db=db,
        app_id=app_id,
        conversation_id=conversation_id,
        limit=pages.limit,
        offset=pages.offset,
        ascending=False,
        start=range.start,
        end=range.end,
        user_id=user_id)

    # 准备Excel数据
    data = []
    for msg in db_messages:
        response = msg.response
        if isinstance(response, list):
            response = json.dumps(response, ensure_ascii=False)

        data.append({
            "消息ID": msg.id,
            "会话ID": msg.conversation_id,
            "提问内容": msg.query,
            "回答内容": response,
            "模型名称": msg.model_name,
            "消息类型": msg.kind,
            "Token总数": msg.total_tokens,
            "响应时间(ms)": msg.time_comsumption_in_ms,
            "费用": msg.fee,
            "创建时间": msg.created_at,
            "创建者": msg.createdBy['email'] if msg.createdBy else msg.created_by,
        })

    # 创建DataFrame并导出为Excel
    df = pd.DataFrame(data)
    output = BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    file_name = f"messages_{app_name}"
    if range.start:
        file_name = f"{file_name}_s{range.start[:10]}"
    if range.end:
        file_name = f"{file_name}_e{range.end[:10]}"
    if user_id:
        file_name = f"{file_name}_user_{user_id}"
    if conversation_id:
        file_name = f"{file_name}_conv_{conversation_id[:5]}"
    file_name = f"{file_name}_p{pages.page_number}_{pages.page_size}.xlsx"

    headers = {
        'Content-Disposition': f'attachment; filename="{quote(file_name)}"'
    }

    return StreamingResponse(
        output,
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers=headers
    )


@router.get("/app/{app_id}/test_model", tags=['dialog'])
async def test_model(
    provider_kind: Annotated[Optional[ModelProviderKind], Query()] = None,
    retry_count: Annotated[Optional[int], Query()] = None,
):
    return await test_models_performance(provider_kind=provider_kind, retry_count=retry_count)
