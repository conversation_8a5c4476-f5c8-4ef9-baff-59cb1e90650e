from pydantic import AliasC<PERSON><PERSON>, Field

from ..misc.errors import LangbaseEx<PERSON>
from ..schema.app import AppType
from ..schema.prompt_template import PromptTemplate, \
    PromptTemplateDeleteRequest, \
    PromptTemplateCreateRequest, \
    PromptTemplateUpdateRequest
from typing import Annotated, Optional
from fastapi import APIRouter, Depends, Request, Query
from ..misc.session import SessionUser
from ..misc.middleware import ValidationErrorLoggingRoute
from ..schema.common import ResponseData
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db
from ..service.prompt_template import list_prompt_templates as list_prompt_template_service, \
    create_prompt_template as create_prompt_template_service, \
    get_prompt_template_detail as get_prompt_template_detail, \
    delete_prompt_template as delete_prompt_template_service, \
    update_prompt_template as update_prompt_template_service

router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/prompt-template/list',
            dependencies=[Depends(admit_by_cookie)],
            tags=['prompt-template'],
            description="Get Prompt Templates List")
async def list_prompt_templates(
    workspaceId: Annotated[str, Query(min_length=1, max_length=36)],
    groupId: Annotated[str, Query(min_length=1, max_length=36)],
    db=Depends(get_db),
    appType: Optional[Annotated[AppType, Query(
        min_length=1, max_length=36)]] = None,
) -> ResponseData:

    (db_templates, total) = await list_prompt_template_service(
        db,
        workspace_id=workspaceId,
        group_id=groupId,
        app_type=appType
    )

    return ResponseData(
        data={
            "items": db_templates,
            "total": total,
        }
    )


@ router.post("/prompt-template/save",
              dependencies=[Depends(admit_by_cookie)],
              tags=['prompt-template'],
              description="save Prompt Templates")
async def save_prompt_template(
    request: Request,
    data: PromptTemplateCreateRequest,
    db=Depends(get_db)
) -> ResponseData:
    user: SessionUser = request.user

    db_template: PromptTemplate = await create_prompt_template_service(
        db=db,
        workspace_id=data.workspaceId,
        user_id=user.id,
        group_id=data.groupId,
        name=data.name,
        avatar_url=data.avatarUrl,
        desc=data.desc,
        scope=data.scope,
        prompt_type=data.promptType,
        app_type=data.appType,
        prompt=data.prompt,
        struct_prompt=data.structPrompt
    )

    return ResponseData(
        data={
            "id": db_template.id,
            "createdAt": db_template.created_at.isoformat(),
            "updatedAt": db_template.updated_at.isoformat()
        }
    )


@ router.put("/prompt-template/update",
             dependencies=[Depends(admit_by_cookie)],
             tags=['prompt-template'],
             description="update Prompt Templates")
async def update_prompt_template(
    request: Request,
    data: PromptTemplateUpdateRequest,
    db=Depends(get_db)
) -> ResponseData:
    user: SessionUser = request.user

    db_template: PromptTemplate = await update_prompt_template_service(
        db=db,
        template_data=data,
        userId=user.id)

    return ResponseData(
        data={
            "id": db_template.id,
            "createdAt": db_template.created_at.isoformat(),
            "updatedAt": db_template.updated_at.isoformat()
        }
    )


@ router.get("/prompt-template/detail",
             tags=['prompt-template'],
             dependencies=[Depends(admit_by_cookie)],
             description="Get Prompt Template Detail")
async def get_prompt_template(
    id: str = Query(..., description="The ID of the prompt template"),
    db=Depends(get_db)
) -> ResponseData:
    template_detail = await get_prompt_template_detail(db, id)
    if template_detail is None:
        raise LangbaseException(
            status_code=404, message="未找到相应模版")
    return ResponseData(data=template_detail)


@ router.delete("/prompt-template/delete",
                tags=['prompt-template'],
                dependencies=[Depends(admit_by_cookie)],
                description="delete Prompt Templates")
async def delete_template(
    request: Request,
    data: PromptTemplateDeleteRequest,
    db=Depends(get_db)
) -> ResponseData:
    user: SessionUser = request.user
    result = await delete_prompt_template_service(db, data.id, user.id)

    return ResponseData(data=result)
