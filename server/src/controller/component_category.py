from typing import Annotated
from fastapi import APIRouter, Depends, Request

from ..misc.middleware import ValidationErrorLoggingRoute

from ..schema.common import Pages, ResponseData, Items
from ..misc.session import SessionUser
from ..schema.component_category import ComponentCategory, ComponentCategoryCreate, ComponentCategoryUpdate
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db
from ..service.component_category import get as get_component_category_service, \
    list as list_component_category_service, \
    update as update_component_category_service, \
    delete as delete_component_category_service, \
    create as create_component_category_service

router = APIRouter(prefix='/component-categories',
                   route_class=ValidationErrorLoggingRoute)


@router.get('', dependencies=[Depends(admit_by_cookie)],
            tags=['component-categories'], description="list component categories")
async def list_component_category(pages: Annotated[Pages, Depends(Pages)],
                                  db=Depends(get_db)) -> ResponseData[Items[ComponentCategory]]:
    (db_component_categories, total) = await list_component_category_service(
        db,
        offset=pages.offset,
        limit=pages.limit)
    return ResponseData.new_items(items=[ComponentCategory.model_validate(db_component_category)
                                         for db_component_category in db_component_categories],
                                  total=total)


@router.post('', dependencies=[Depends(admit_by_cookie)],
             status_code=201, tags=['component-categories'])
async def create_component_category(component_category_create: ComponentCategoryCreate,
                                    request: Request, db=Depends(get_db)) \
        -> ResponseData[ComponentCategory]:
    user: SessionUser = request.user
    db_component_category = await create_component_category_service(
        db,
        component_category_create=component_category_create,
        user_id=user.id)
    return ResponseData(data=ComponentCategory.model_validate(db_component_category))


@router.get('/{component_category_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['component-categories'])
async def get_component_category(component_category_id: str, db=Depends(get_db)) \
        -> ResponseData[ComponentCategory]:
    db_component_category = await get_component_category_service(db, component_category_id)
    return ResponseData(data=ComponentCategory.model_validate(db_component_category))


@router.put('/{component_category_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['component-categories'])
async def update_component_category(component_category_id: str,
                                    component_category_update: ComponentCategoryUpdate,
                                    request: Request,
                                    db=Depends(get_db)) \
        -> ResponseData[ComponentCategory]:
    user: SessionUser = request.user
    db_component_category = await update_component_category_service(db,
                                                                    user.id,
                                                                    component_category_id,
                                                                    component_category_update)
    return ResponseData(data=ComponentCategory.model_validate(db_component_category))


@router.delete('/{component_category_id}', dependencies=[Depends(admit_by_cookie)],
               tags=['component-categories'], status_code=204)
async def delete_component_category(component_category_id: str,
                                    request: Request,
                                    db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_component_category_service(db, user.id, component_category_id)
