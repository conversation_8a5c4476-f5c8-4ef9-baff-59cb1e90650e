from typing import Annotated, Optional
from fastapi import APIRouter, Body, Depends, Query, Request

from ..misc.middleware import ValidationErrorLoggingRoute

from ..misc.errors import ParameterInvalid

from ..misc.authz import admit_by_cookie

from ..misc.session import SessionUser, rm_bindings_keys
from ..schema.common import ResourceType
from ..misc.db import get_db
from ..schema.model import BindingCreate, \
    BindingCreated, BindingGet, BindingList, BindingUpdate, \
    DefaultModelConfigGet, DefaultModelConfigUpdate, \
    GlobalProvider, ModelList, ModelType
from ..schema.common import Items, Pages, ResponseData
from ..service.model \
    import binding_with_group, binding_with_workspace, list_global_provider as list_global_provider_service, \
    create_provider_binding as create_provider_binding_service, \
    delete_provider_binding as delete_provider_binding_service, provider_binding_init, provider_binding_init_with_kind, remove_duplicate_bindings, \
    update_provider_binding as update_provider_binding_service, \
    list_provider_binding as list_provider_binding_service, \
    update_default_model_config as update_default_model_config_service, \
    list_default_model_config as list_default_model_config_service, \
    list_models as list_models_service, \
    list_models_v2 as list_models_v2_service


router = APIRouter(route_class=ValidationErrorLoggingRoute)


# 废弃，modelProvider 全由前端存储和添加，后续不再维护
@router.get("/model-provider", tags=["model-provider"])
async def list_global_model_provider(
    pages: Annotated[Pages, Depends(Pages)]
):
    providers = await list_global_provider_service()
    return ResponseData(
        data=[
            GlobalProvider.model_validate(provider)
            for provider in providers])


# 已迁移测试
@router.get("/workspace/{workspace_id}/model-provider",
            tags=["model-provider"], dependencies=[Depends(admit_by_cookie)])
async def list_workspace_model_providers(
        workspace_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        db=Depends(get_db)):
    bindings, count = await list_provider_binding_service(
        db=db,
        resource_type=ResourceType.WORKSPACE,
        resource_id=workspace_id,
        limit=pages.limit,
        offset=pages.offset
    )
    return ResponseData(
        data=Items(
            items=[
                BindingList.model_validate(binding)
                for binding in bindings
            ],
            total=count
        )
    )


# 已迁移测试
@router.post("/workspace/{workspace_id}/model-provider", status_code=201,
             tags=["model-provider"], dependencies=[Depends(admit_by_cookie)])
async def create_workspace_model_provider_binding(
        workspace_id: str,
        request: Request,
        binding_create: BindingCreate,
        db=Depends(get_db)):
    if binding_create.api_key is None and binding_create.endpoint is None:
        raise ParameterInvalid("api_key and endpoint cannot be both empty")
    user: SessionUser = request.user
    binding = await create_provider_binding_service(
        db,
        user_id=user.id,
        workspace_id=workspace_id,
        binding_create=binding_create
    )
    await rm_bindings_keys(workspace_id)
    return ResponseData(data=BindingCreated.model_validate(binding))


@router.put("/workspace/{workspace_id}/default-model", tags=["model-provider"],
            dependencies=[Depends(admit_by_cookie)])
async def update_workspace_default_model(
        workspace_id: str,
        request: Request,
        config_update: DefaultModelConfigUpdate,
        db=Depends(get_db)):
    user: SessionUser = request.user
    db_config = await update_default_model_config_service(
        db=db,
        user_id=user.id,
        resource_type=ResourceType.WORKSPACE,
        resource_id=workspace_id,
        config_update=config_update,
    )
    await rm_bindings_keys(workspace_id)
    return ResponseData(
        data=DefaultModelConfigGet.model_validate(db_config)
    )


@router.get("/workspace/{workspace_id}/default-model", tags=["model-provider"],
            dependencies=[Depends(admit_by_cookie)])
async def get_workspace_default_model(
        workspace_id: str,
        db=Depends(get_db)):
    configs, _ = await list_default_model_config_service(
        db=db,
        resource_type=ResourceType.WORKSPACE,
        resource_id=workspace_id,
    )
    return ResponseData(
        data=[DefaultModelConfigGet.model_validate(config)
              for config in configs]
    )


# 已迁移测试
@router.get("/group/{group_id}/model-provider", tags=["model-provider"],
            dependencies=[Depends(admit_by_cookie)])
async def list_group_model_providers(
        group_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        db=Depends(get_db)):
    bindings, count = await list_provider_binding_service(
        db=db,
        resource_type=ResourceType.GROUP,
        resource_id=group_id,
        limit=pages.limit,
        offset=pages.offset
    )
    return ResponseData(
        data=Items(
            items=[
                BindingList.model_validate(binding)
                for binding in bindings
            ],
            total=count
        )
    )


@router.post("/group/{group_id}/model-provider", status_code=201,
             tags=["model-provider"], dependencies=[Depends(admit_by_cookie)])
async def create_group_model_provider(
        group_id: str,
        request: Request,
        binding_create: BindingCreate,
        db=Depends(get_db)):
    user: SessionUser = request.user
    binding = await create_provider_binding_service(
        db,
        user_id=user.id,
        group_id=group_id,
        binding_create=binding_create
    )
    await rm_bindings_keys(binding_create.workspace_id)
    return ResponseData(data=BindingCreated.model_validate(binding))


# 已迁移测试
@router.delete("/model-provider/{provider_id}",
               status_code=204, tags=["model-provider"],
               dependencies=[Depends(admit_by_cookie)])
async def delete_group_model_provider(
        provider_id: str,
        workspace_id: Annotated[str, Body(embed=True, alias='workspaceId')],
        db=Depends(get_db)):
    await delete_provider_binding_service(
        db,
        provider_id,
    )
    await rm_bindings_keys(workspace_id)


@router.post("/workspace/{workspace_id}/aigw_app/{aigw_id}/bind", tags=["model-provider"],
             dependencies=[Depends(admit_by_cookie)])
async def bind_workspace_aigw_app(
        workspace_id: str,
        aigw_id: str,
        request: Request,
        db=Depends(get_db)):
    user: SessionUser = request.user
    bindings = await binding_with_workspace(db, user.id, workspace_id, aigw_id)
    await rm_bindings_keys(workspace_id)
    return ResponseData(data=[BindingGet.model_validate(binding)
                              for binding in bindings])


@router.post("/group/{group_id}/aigw_app/{aigw_id}/bind", tags=["model-provider"],
             dependencies=[Depends(admit_by_cookie)])
async def bind_group_aigw_app(
        group_id: str,
        aigw_id: str,
        request: Request,
        db=Depends(get_db)):
    user: SessionUser = request.user
    group, bindings = await binding_with_group(db, user.id, group_id, aigw_id)
    await rm_bindings_keys(group.workspace_id)
    return ResponseData(data=[BindingGet.model_validate(binding)
                              for binding in bindings])


@router.put("/model-provider/{binding_id}", tags=["model-provider"],
            dependencies=[Depends(admit_by_cookie)])
async def update_group_model_provider(
        binding_id: str,
        request: Request,
        binding_update: BindingUpdate,
        db=Depends(get_db)):
    user: SessionUser = request.user
    binding = await update_provider_binding_service(
        db=db,
        user_id=user.id,
        binding_id=binding_id,
        binding_update=binding_update,
    )
    await rm_bindings_keys(binding_update.workspace_id)
    return ResponseData(data=BindingGet.model_validate(binding))


# 迁移测试完成
@router.put("/group/{group_id}/default-model", tags=["model-provider"],
            dependencies=[Depends(admit_by_cookie)])
async def update_group_default_model(
        group_id: str,
        request: Request,
        config_update: DefaultModelConfigUpdate,
        db=Depends(get_db)):
    user: SessionUser = request.user
    db_config = await update_default_model_config_service(
        db=db,
        user_id=user.id,
        resource_type=ResourceType.GROUP,
        resource_id=group_id,
        config_update=config_update,
    )
    return ResponseData(
        data=DefaultModelConfigGet.model_validate(db_config)
    )


# 迁移测试完成
@router.get("/group/{group_id}/default-model", tags=["model-provider"],
            dependencies=[Depends(admit_by_cookie)])
async def get_group_default_model(
        group_id: str,
        db=Depends(get_db)):
    configs, _ = await list_default_model_config_service(
        db=db,
        resource_type=ResourceType.GROUP,
        resource_id=group_id,
    )
    return ResponseData(
        data=[DefaultModelConfigGet.model_validate(config)
              for config in configs]
    )


# 迁移测试完成
@router.get("/model")
async def list_models(
    type: Annotated[Optional[ModelType], Query()] = ModelType.LLM.value,
    workspaceId: Annotated[Optional[str], Query()] = None,
    groupId: Annotated[Optional[str], Query()] = None,
    db=Depends(get_db)
):

    models = await list_models_service(db, type, None, workspaceId, groupId)

    return ResponseData(
        data=[ModelList.model_validate(model)
              for model in models]
    )


@router.get("/model_v2")
async def list_models_v2(
    type: Annotated[Optional[ModelType], Query()] = ModelType.LLM.value,
    workspaceId: Annotated[Optional[str], Query()] = None,
    groupId: Annotated[Optional[str], Query()] = None,
    db=Depends(get_db)
):

    models = await list_models_v2_service(
        db=db,
        model_type=type,
        provider_kind=None,
        workspaceId=workspaceId,
        groupId=groupId
    )

    return ResponseData(
        data=[ModelList.model_validate(model)
              for model in models]
    )


@router.get("/dedup-model")
async def dedup_models(
    workspaceId: Annotated[Optional[str], Query()] = None,
    db=Depends(get_db)
):
    data = await remove_duplicate_bindings(db, workspaceId)
    return ResponseData(data=data)


@router.get("/init_model")
async def init_model(
    kind: Annotated[str, Query()],
    db=Depends(get_db)
):
    workspaces = await provider_binding_init_with_kind(db, kind)
    return ResponseData(
        data=workspaces
    )
