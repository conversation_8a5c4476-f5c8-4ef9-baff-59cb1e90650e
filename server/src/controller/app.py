from datetime import datetime
from typing import Annotated, List, Optional
import uuid
from fastapi import APIRouter, Depends, Query, HTTPException
from fastapi import Request, UploadFile
from sqlalchemy.orm import Session
import httpx

from ..schema.collection import CollectionResponse

from ..misc.redis_utils import MEDIA_CACHE_KEY, get_redis_sync, set_redis_sync

from ..service.dialog import download_and_parse_file

from ..schema.member import GroupRole

from ..service.group import get_with_role

from ..misc.http_db import get_http_db

from ..misc.middleware import ValidationErrorLoggingRoute

# from ..misc.middleware import ValidationErrorLoggingRoute

from ..config import settings
from ..models.app import App
from ..schema.model import ModelType

from ..misc.errors import FileTooLong, ParameterInvalid, BadRequest
from ..lib.llms.excutor import executor

from ..misc.session import SessionUser
from ..schema.app import App<PERSON>reate, AppCreated, AppGet, \
    AppList, AppType, AppUpdate, AppDeploy, AppVersionInfo, \
    ConfigCreate, ConfigGet, ConfigList, ConfigType, ConfigUpdate, CopyConfig, GroupAppCreate, SnapshotConfig, \
    AppTriggerRequest, AppTriggerResponse, AppWorkflowRunDetailGet, AppDebug, AgentConfig, AppWorkflowRunList, \
    AppUploadfileResponse, AppTemplateList, AppLockResponse
from ..schema.common import Items, Pages, ResponseData, ResponseDataWithCode, UseType
from ..misc.db import get_db
from ..misc.authz import admit_by_cookie, admit_by_token_or_cookie
from ..schema.env_variable import EnvVariableGet
from ..service import workflow_config
from ..service.app import copy_config, list_app as list_apps_service, \
    list_app_with_starred, \
    star_app as star_app_service, \
    unstar_app as unstar_app_service, \
    create as create_app_service, \
    get as get_app_service, \
    get_config as get_app_config_service, \
    delete as delete_app_service, \
    update as update_app_service, \
    deploy as deploy_app_service, \
    debug as debug_app_service, \
    trigger as trigger_app_service, \
    get_workflow_run as get_workflow_run_service, \
    list_workflow_run as list_workflow_run_service, \
    get_workflow_run_status as get_workflow_run_status_service, \
    list_workflow_run_events as list_workflow_run_events_service, \
    list_config as list_app_config_service, \
    create_config as create_app_config_service, \
    list_app_component_env_variable as list_app_component_env_variable_service, \
    try_lock as try_lock_app_service, \
    release_lock as release_lock_app_service, update_app_config_id, update_config
from ..service.model import \
    get_default_model_config_recursively as get_default_model_config_recursively_service
from ..service.s3 import upload_file as app_upload_file_service
from ..service.account import get_by_email

router = APIRouter(route_class=ValidationErrorLoggingRoute)


async def validate_app_create(db: Session, group_id: str, app_create: AppCreate):
    if app_create.type != AppType.WORKFLOW and app_create.config is not None:
        agent_config = AgentConfig.model_validate(app_create.config)
        if agent_config.providerKind is not None \
                and agent_config.modelParams is not None:
            await executor.validate_config(agent_config.providerKind,
                                           agent_config.modelParams)

        if agent_config.providerKind is None \
                or agent_config.modelName is None:
            config = await get_default_model_config_recursively_service(
                db=db,
                group_id=group_id,
                model_type=ModelType.LLM
            )
            if agent_config.providerKind is None:
                agent_config.providerKind = str(config.provider_kind)
            if agent_config.modelName is None:
                agent_config.modelName = str(config.model_name)

        if agent_config.modelParams is not None:
            await executor.validate_config(agent_config.providerKind,
                                           agent_config.modelParams)


async def validate_app_update(app_update: AppUpdate, db_app: App, db: Session):
    if db_app.type != AppType.WORKFLOW and app_update.config is not None:
        agent_config = AgentConfig.model_validate(app_update.config)
        if agent_config.providerKind is not None \
                and agent_config.modelParams is not None:
            await executor.validate_config(agent_config.providerKind,
                                           agent_config.modelParams)
    if db_app.type == AppType.WORKFLOW and app_update.config is not None:
        db_config = await get_app_config_service(db, db_app.app_config_id)
        if db_config.config['workflowId'] != app_update.config['workflowId']:
            raise BadRequest("workflow code can not be changed")


async def validate_app_deploy(config: dict, db_app: App, db: Session):
    if db_app.type == AppType.AGENTWORKFLOW:
        return
    if db_app.type != AppType.WORKFLOW:
        raise ParameterInvalid('Only workflow app can be deployed')

    db_config = await get_app_config_service(db, db_app.app_config_id)
    if db_config.config['workflowId'] != config['workflowId']:
        raise BadRequest("workflow code can not be changed")


# 已迁移测试
@router.get('/workspace/{workspace_id}/app',
            dependencies=[Depends(admit_by_token_or_cookie)],
            tags=['app', 'workspace'], response_model_exclude_none=True)
async def list_apps_by_workspace(
        request: Request,
        workspace_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        appType: Annotated[Optional[List[AppType]], Query()] = None,
        subType: Annotated[Optional[List[str]], Query()] = None,
        name: Annotated[Optional[List[str]], Query()] = None,
        isTemplate: Annotated[Optional[bool], Query()] = None,
        useType: Annotated[Optional[UseType], Query()] = None,
        user_id: Optional[str] = Query(default=None, alias='userID'),
        db=Depends(get_db)) -> ResponseData[Items[AppList]]:
    user = getattr(request.state, 'user', None) or getattr(
        request, 'user', None)
    userId = getattr(user, 'id', None) or user_id
    (db_apps, count) = await list_app_with_starred(
        workspace_id=workspace_id,
        limit=pages.limit,
        offset=pages.offset,
        appType=appType,
        subType=subType,
        created_by=user_id,
        names=name,
        is_template=isTemplate,
        user_id=userId,
        use_type=useType,
        db=db)
    return ResponseData.new_items(items=[
        AppList.model_validate(db_app) for db_app in db_apps
    ], total=count)


# 已迁移测试
@router.get("/group/{group_id}/app",
            dependencies=[Depends(admit_by_token_or_cookie)],
            tags=['app', 'group'], response_model_exclude_none=True)
async def list_apps_by_group(
        request: Request,
        group_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        appType: Annotated[Optional[List[AppType]], Query()] = None,
        showAll: Annotated[Optional[bool], Query()] = None,
        useType: Annotated[Optional[UseType], Query()] = None,
        subType: Annotated[Optional[List[str]], Query()] = None,
        isTemplate: Annotated[Optional[bool], Query()] = None,
        name: Annotated[Optional[List[str]], Query()] = None,
        user_id: Optional[str] = Query(default=None, alias='userID'),
        db=Depends(get_db)):
    # 先查看是否有该 group 的权限
    user = getattr(request.state, 'user', None) or getattr(
        request, 'user', None)
    userId = getattr(user, 'id', None) or user_id
    db_group = await get_with_role(db, group_id, userId)
    if db_group.role != GroupRole.INTERNAL_USER.value and str(db_group.name) not in ['默认分组', '模板应用', '示例应用']:
        raise HTTPException(
            status_code=403, detail="You are not allowed to access this group")
    (db_apps, count) = await list_app_with_starred(
        db=db,
        show_all=showAll,
        group_id=group_id,
        limit=pages.limit,
        appType=appType,
        subType=subType,
        is_template=isTemplate,
        names=name,
        created_by=user_id,
        user_id=userId,
        offset=pages.offset,
        use_type=useType
    )
    return ResponseData.new_items(items=[
        AppList.model_validate(db_app) for db_app in db_apps
    ], total=count)


# 迁移测试完成
@router.post("/group/{group_id}/app", dependencies=[Depends(admit_by_token_or_cookie)],
             status_code=201, tags=['app'], response_model_exclude_none=True)
async def create_app(
        group_id: str,
        app_create: GroupAppCreate,
        request: Request,
        db=Depends(get_db)) -> ResponseData[AppCreated]:
    user: SessionUser = request.user

    if app_create.username:
        user = await get_by_email(db, app_create.username + '@corp.netease.com')
    del app_create.username

    await validate_app_create(db=db, group_id=group_id, app_create=app_create)

    (db_app, app_config) = await create_app_service(db=db,
                                                    creater_id=user.id,
                                                    creater_name=user.name,
                                                    group_id=group_id,
                                                    app_create=app_create)
    db_app.config = app_config.config if app_config is not None else None
    app = AppCreated.model_validate(db_app, strict=False)
    return ResponseData(data=app)


@router.post("/app/{app_id}/copy", dependencies=[Depends(admit_by_token_or_cookie)],
             status_code=200, tags=['app'], response_model_exclude_none=True)
async def copy_app(
        app_id: str,
        app_copy: GroupAppCreate,
        request: Request,
        db=Depends(get_db)) -> ResponseData[AppCreated]:
    user: SessionUser = request.user
    if app_copy.username:
        user = await get_by_email(db, app_copy.username + '@corp.netease.com')
    del app_copy.username

    db_app = await get_app_service(db=db, app_id=app_id)

    await validate_app_create(db=db, group_id=db_app.group_id, app_create=app_copy)

    (db_app_new, app_config) = await create_app_service(db=db,
                                                        creater_id=user.id,
                                                        creater_name=user.name,
                                                        group_id=db_app.group_id,
                                                        app_create=app_copy)
    db_app_new.config = app_config.config if app_config is not None else None
    app = AppCreated.model_validate(db_app_new, strict=False)
    return ResponseData(data=app)


# 迁移测试完成
@router.get("/app/{app_id}", tags=['app'], response_model_exclude_none=True)
async def get_app(
        app_id: str,
        request: Request,
        db=Depends(get_db)) -> ResponseData[AppGet]:
    user: SessionUser = request.user
    db_app = await get_app_service(db=db, app_id=app_id, user_id=user.id if hasattr(user, 'id') else None)

    if db_app.app_config_id is not None:
        db_config = await get_app_config_service(db, db_app.app_config_id)  # type: ignore # noqa
        db_app.config = db_config.config
    return ResponseData(data=AppGet.model_validate(db_app))


# 迁移测试完成
@router.delete("/app/{app_id}", dependencies=[Depends(admit_by_cookie)],
               status_code=204, tags=['app'])
async def delete_app(
        app_id: str,
        request: Request,
        db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_app_service(db=db, app_id=app_id, user_id=user.id, user_name=user.name)


# 迁移测试完成
@router.put("/app/{app_id}", status_code=200, tags=['app'],
            dependencies=[Depends(admit_by_cookie)],
            response_model_exclude_none=True)
async def update_app(
        app_id: str,
        request: Request,
        app_update: AppUpdate,
        db=Depends(get_db)) -> ResponseData[AppGet]:
    user: SessionUser = request.user
    db_app = await get_app_service(db=db, app_id=app_id)
    await validate_app_update(app_update, db_app, db)

    db_app = await update_app_service(app_id=app_id, db=db, user_id=user.id,
                                      app_update=app_update)
    if db_app.app_config_id is not None:
        db_config = await get_app_config_service(db, db_app.app_config_id)  # type: ignore # noqa
        db_app.config = db_config.config
    return ResponseData(data=AppGet.model_validate(db_app))


# 迁移测试完成
@router.post("/app/{app_id}/deploy", status_code=200, tags=['app'],
             dependencies=[Depends(admit_by_cookie)],
             response_model_exclude_none=True)
async def deploy_app(
        app_id: str,
        request: Request,
        app_deploy: AppDeploy,
        db=Depends(get_db)) -> ResponseData[ConfigGet]:
    user: SessionUser = request.user

    app = await get_app_service(db, app_id)
    await validate_app_deploy(app_deploy.config, app, db)

    db_config = await deploy_app_service(db=db,
                                         workspace_id=app.workspace_id,
                                         group_id=app.group_id,
                                         app_id=app_id,
                                         user_id=user.id,
                                         user_name=user.name,
                                         app_deploy=app_deploy)

    return ResponseData(data=ConfigGet.model_validate(db_config))


# 迁移测试完成
@router.post("/app/{app_id}/debug", status_code=200, tags=['app'],
             dependencies=[Depends(admit_by_cookie)],
             response_model_exclude_none=True)
async def debug_app(
        app_id: str,
        request: Request,
        app_debug: AppDebug,
        db=Depends(get_db)) -> ResponseData[ConfigGet]:
    user: SessionUser = request.user

    app = await get_app_service(db, app_id)
    await validate_app_deploy(app_debug.config, app, db)

    db_config = await debug_app_service(db=db,
                                        workspace_id=app.workspace_id,
                                        group_id=app.group_id,
                                        app_id=app_id,
                                        user_id=user.id,
                                        user_name=user.name,
                                        app_debug=app_debug)

    return ResponseData(data=ConfigGet.model_validate(db_config))


# 迁移测试完成
@router.get("/app/{app_id}/star", status_code=200, tags=['app'],
            dependencies=[Depends(admit_by_cookie)],
            response_model_exclude_none=True)
async def star_app(
        app_id: str,
        request: Request,
        db=Depends(get_db)) -> ResponseData[CollectionResponse]:
    user: SessionUser = request.user
    resp = await star_app_service(db=db, app_id=app_id, user_id=user.id)

    return ResponseData(data=CollectionResponse.model_validate(resp))

# 迁移测试完成


@router.get("/app/{app_id}/unstar", status_code=200, tags=['app'],
            dependencies=[Depends(admit_by_cookie)],
            response_model_exclude_none=True)
async def unstar_app(
        app_id: str,
        request: Request,
        db=Depends(get_db)) -> dict:
    user: SessionUser = request.user
    await unstar_app_service(db=db, app_id=app_id, user_id=user.id)

    return {
        'code': 200,
        'data': 'success'
    }


# 迁移测试完成
@router.post("/app/{app_id}/trigger", status_code=200, tags=['app'],
             dependencies=[Depends(admit_by_cookie)],
             response_model_exclude_none=True)
async def trigger_app(
        app_id: str,
        app_trigger_request: AppTriggerRequest,
        db=Depends(get_db)) -> ResponseData[AppTriggerResponse]:
    resp = await trigger_app_service(db=db,
                                     app_id=app_id,
                                     trigger_request=app_trigger_request)

    return ResponseData(data=AppTriggerResponse.model_validate(resp))

# 通用上传接口


@router.post("/app/common/uploadfile", status_code=200, tags=['app'],
             response_model_exclude_none=True)
async def app_common_upload_file(
        file: UploadFile) -> ResponseData[AppUploadfileResponse]:
    suffix = file.filename.split('.')[-1]
    key = f'{settings.app_workflow_temp_upload_file_path}/{uuid.uuid4()}.{suffix}'
    await app_upload_file_service(s3_config=workflow_config.s3, key=key, body=file.file)

    return ResponseData(data=AppUploadfileResponse.model_validate(
        AppUploadfileResponse(key=f"{settings.workflow_config.s3.bucket}/{key}",
                              uri=f"https://{workflow_config.s3.bucket}.{workflow_config.s3.endpoint}/{key}")))

# 迁移测试完成

# 写一个获取缓存文件的接口,传入nosKey,返回文件内容


@router.get("/get-cache-file", status_code=200, tags=['app'],
            response_model_exclude_none=True)
async def get_cache_file(key: str) -> ResponseData[str]:
    file_content = get_redis_sync(MEDIA_CACHE_KEY + '::' + key)
    if file_content is None:
        uri = f"https://{workflow_config.s3.bucket}.{workflow_config.s3.endpoint}/{key.replace(workflow_config.s3.bucket, '')}"
        file_content = await download_and_parse_file(uri)
        set_redis_sync(MEDIA_CACHE_KEY + '::' + key, file_content, 86400)
    return ResponseData(data=file_content)


@router.post("/app/{app_id}/uploadfile", status_code=200, tags=['app'],
             response_model_exclude_none=True)
async def app_upload_file(
        file: UploadFile,
        permanent: Optional[bool] = Query(default=False),
        max_tokens: Optional[int] = Query(default=None),
) -> ResponseData[AppUploadfileResponse]:
    suffix = file.filename.split('.')[-1]
    key = f'{settings.app_workflow_temp_upload_file_path}/{uuid.uuid4()}.{suffix}'
    await app_upload_file_service(s3_config=workflow_config.s3, key=key, body=file.file)
    uri = f"https://{workflow_config.s3.bucket}.{workflow_config.s3.endpoint}/{key}"
    nos_key = f"{settings.workflow_config.s3.bucket}/{key}"
    if max_tokens is not None:
        file_content = await download_and_parse_file(uri)
        if len(file_content) > max_tokens:
            raise FileTooLong(f'文本长度为{len(file_content)}，超过最大长度')
        set_redis_sync(MEDIA_CACHE_KEY + '::' + nos_key, file_content, 86400)
    if permanent:
        file_info = await transfer_file(uri)
        return ResponseData(data=AppUploadfileResponse.model_validate(
            AppUploadfileResponse(key=file_info['key'],
                                  uri=file_info['uri'])))
    else:
        return ResponseData(data=AppUploadfileResponse.model_validate(
            AppUploadfileResponse(key=nos_key,
                                  uri=uri)))

# 迁移测试完成


# 删除lock删除权限校验，因为前端本身对应用已经有了一层权限校验，同时兼容settingId
@router.post("/app/{app_id}/lock", status_code=200, tags=['app'],
             #  dependencies=[Depends(admit_by_cookie)],
             response_model_exclude_none=True)
async def try_lock(request: Request,
                   app_id: str) -> ResponseData[AppLockResponse]:
    user: SessionUser = request.user
    hold_username = await try_lock_app_service(app_id=app_id, username=user.name)
    return ResponseData(data=AppLockResponse(username=hold_username))


# 迁移测试完成
@router.delete("/app/{app_id}/lock", status_code=200, tags=['app'],
               #    dependencies=[Depends(admit_by_cookie)],
               response_model_exclude_none=True)
async def release_lock(request: Request,
                       app_id: str) -> ResponseData[AppLockResponse]:
    user: SessionUser = request.user
    await release_lock_app_service(app_id=app_id, username=user.name)
    return ResponseData(data=AppLockResponse(username=None))


async def list_app_config(
        app_id: str,
        type: ConfigType,
        pages: Pages,
        db: Session) -> ResponseData[Items[ConfigList]]:
    (db_configs, count) = await list_app_config_service(
        db, app_id=app_id, type=type,
        limit=pages.limit, offset=pages.offset)

    return ResponseData.new_items(
        items=[ConfigList.model_validate(db_config)
               for db_config in db_configs],
        total=count)


@router.get("/app-config/{version_id}", tags=['app-config'], response_model_exclude_none=True)
async def get_app_config(
        version_id: str,
        db=Depends(get_http_db)) -> ResponseData[ConfigList]:
    config = await get_app_config_service(db=db, config_id=version_id)
    return ResponseData(data=ConfigList.model_validate(config))


@router.put("/app/{app_id}/config", tags=['app-config'],
            dependencies=[Depends(admit_by_cookie)],
            response_model_exclude_none=True)
async def update_app_config(
        app_id: str,
        version: AppVersionInfo,
        db=Depends(get_http_db)) -> ResponseData[AppGet]:
    app = await update_app_config_id(db=db, app_id=app_id, config_id=version.version_id)
    return ResponseData(data=app)


# 迁移测试完成
@router.get("/app/{app_id}/deploy-history", tags=['app'],
            dependencies=[Depends(admit_by_cookie)],
            response_model_exclude_none=True)
async def list_app_deploy_history(
        app_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        db=Depends(get_db)) -> ResponseData[Items[ConfigList]]:
    return await list_app_config(app_id, ConfigType.HISTORY, pages, db)


# 已迁移测试
@router.get("/app/{app_id}/config-snapshot", tags=['app'],
            dependencies=[Depends(admit_by_token_or_cookie)],
            response_model_exclude_none=True)
async def get_app_config_snapshot(
        app_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        db=Depends(get_http_db)) -> ResponseData[Items[ConfigList]]:
    return await list_app_config(app_id, ConfigType.SNAPSHOT, pages, db)


@router.post("/app/{app_id}/config-snapshot", status_code=201,
             tags=['app'], dependencies=[Depends(admit_by_cookie)],
             response_model_exclude_none=True)
async def create_app_config_snapshot(
        app_id: str,
        snapshot_create: SnapshotConfig,
        request: Request,
        db=Depends(get_db)) -> ResponseData[ConfigGet]:
    user: SessionUser = request.user
    db_config = await create_app_config_service(
        db=db,
        creater_id=user.id,
        app_id=app_id,
        config_create=ConfigCreate(
            message=f'{datetime.now().strftime("%m%d%H%M")}-保存',
            config=snapshot_create.config,
            type=ConfigType.SNAPSHOT))
    return ResponseData(data=ConfigGet.model_validate(db_config))


@router.put("/copy-config-snapshot/{config_id}", status_code=201,
            tags=['app'], dependencies=[Depends(admit_by_cookie)],
            response_model_exclude_none=True)
async def update_app_config_snapshot(
        config_id: str,
        request: Request,
        copy: CopyConfig,
        db=Depends(get_db)) -> ResponseData[ConfigGet]:
    user: SessionUser = request.user
    db_config = await copy_config(
        db=db,
        created_id=user.id,
        type=copy.type,
        config_id=config_id)
    return ResponseData(data=ConfigGet.model_validate(db_config))


@router.get("/app/{app_id}/workflow-runs", tags=['app'],
            dependencies=[Depends(admit_by_token_or_cookie)],
            response_model_exclude_none=True)
async def list_app_workflow_run(app_id: str,
                                pages: Annotated[Pages, Depends(Pages)],
                                start_time: int = Query(alias="startTime"),
                                end_time: int = Query(alias="endTime"),
                                debug: bool = False,
                                db=Depends(get_http_db)) -> ResponseData[Items[AppWorkflowRunList]]:
    (runs, count) = await list_workflow_run_service(db=db,
                                                    app_id=app_id,
                                                    page_size=pages.page_size,
                                                    page_number=pages.page_number,
                                                    start_time=start_time,
                                                    end_time=end_time,
                                                    debug=debug)
    return ResponseData.new_items(items=[
        AppWorkflowRunList.model_validate(run) for run in runs
    ], total=count)


@router.get("/app/{app_id}/workflow-runs/{run_id}", tags=['app'],
            response_model_exclude_none=True)
async def get_app_workflow_run(app_id: str,
                               run_id: str,
                               debug: bool = False,
                               db=Depends(get_http_db)) -> ResponseData[AppWorkflowRunDetailGet]:
    run_detail = await get_workflow_run_service(db=db, app_id=app_id, run_id=run_id, debug=debug)
    return ResponseData(data=AppWorkflowRunDetailGet.model_validate(run_detail))


@router.get("/app/{app_id}/status", tags=['app'],
            dependencies=[Depends(admit_by_cookie)],
            response_model_exclude_none=True)
async def get_app_workflow_run_node(app_id: str,
                                    run_id: str = Query(alias="runID"),
                                    node_id: str = Query(alias="nodeID"),
                                    index_list: str = Query(alias="indexList"),
                                    debug: bool = Query(
                                        default=False, alias="debug"),
                                    db=Depends(get_db)) -> ResponseData[dict]:
    node = await get_workflow_run_status_service(db=db,
                                                 app_id=app_id,
                                                 node_id=node_id,
                                                 run_id=run_id,
                                                 index_list=index_list,
                                                 debug=debug)
    return ResponseData(data=node)


@router.get("/app/{app_id}/events", tags=['app'],
            dependencies=[Depends(admit_by_cookie)],
            response_model_exclude_none=True)
async def list_app_workflow_run_events(app_id: str,
                                       pages: Annotated[Pages, Depends(Pages)],
                                       run_id: str = Query(alias="runID"),
                                       node_id: str = Query(alias="nodeID"),
                                       debug: bool = Query(
                                           default=False, alias="debug"),
                                       db=Depends(get_db)) -> ResponseData[AppWorkflowRunDetailGet]:
    (events, count) = await list_workflow_run_events_service(db=db,
                                                             app_id=app_id,
                                                             node_id=node_id,
                                                             run_id=run_id,
                                                             page_number=pages.page_number,
                                                             page_size=pages.page_size,
                                                             debug=debug)
    return ResponseData.new_items(items=[
        AppWorkflowRunList.model_validate(event) for event in events
    ], total=count)


@router.get('/app/{app_id}/env-variables', dependencies=[Depends(admit_by_cookie)],
            tags=['env-variables'], description="list app env variables")
async def list_app_env_variable(app_id: str,
                                db=Depends(get_db)) -> ResponseData[Items[EnvVariableGet]]:
    (db_env_variables, total) = await list_app_component_env_variable_service(db, app_id=app_id)
    return ResponseData.new_items(items=[EnvVariableGet.model_validate(db_env_variable)
                                         for db_env_variable in db_env_variables],
                                  total=total)


@router.get("/app-template", tags=['app'],
            dependencies=[Depends(admit_by_cookie)])
async def list_app_templates(
        db=Depends(get_http_db)
):
    apps, count = await list_apps_service(
        db=db,
        is_template=True,
    )

    configs, _ = await list_app_config_service(
        db,
        app_id=[app.id for app in apps],
    )

    configMap = {config.app_id: config.config for config in configs}
    for app in apps:
        app.config = configMap.get(app.id, None)

    return ResponseData.new_items(
        items=[AppTemplateList.model_validate(app) for app in apps],
        total=count,
    )


# 迁移测试完成
@router.get("/workspace/{workspace_id}/basic-app", tags=['workspace, app'],
            dependencies=[Depends(admit_by_cookie)])
async def get_basic_app(
        workspace_id: str,
        db=Depends(get_http_db),
):
    app = await get_app_service(
        db,
        is_basic=True,
        workspace_id=workspace_id,
    )
    db_config = await get_app_config_service(db, app.app_config_id)  # type: ignore # noqa
    app.config = db_config.config

    return ResponseData(
        data=AppGet.model_validate(app),
    )


async def transfer_file(url: str) -> dict:
    """调用文件转换接口并格式化返回结果"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{settings.db_service_url}/langbase/nos/file/transfer",
                params={"url": url}
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=response.status_code, detail="文件转换失败")

            data = response.json()
            if data["code"] != 200:
                raise HTTPException(
                    status_code=400, detail=data.get("message", "文件转换失败"))

            file_info = data["data"]
            return {
                "key": file_info["nosKey"],
                "uri": file_info["url"]
            }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"文件转换服务异常: {str(e['detail'])}")
