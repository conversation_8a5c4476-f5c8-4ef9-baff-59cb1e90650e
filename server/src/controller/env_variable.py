from fastapi import APIRouter, Depends, Request, Query

from ..misc.middleware import ValidationErrorLoggingRoute

from ..misc.errors import BadRequest
from ..models.group import Group
from ..schema.common import ResponseData, Items
from ..misc.session import SessionUser
from ..schema.env_variable import EnvVariableGet, EnvVariableCreate, EnvVariableUpdate, \
    EnvVariableScopeType
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db
from ..service.env_variable import get as get_env_variable_service, \
    list_workspace_component_env_variable as list_workspace_component_env_variable_service, \
    list_group_component_env_variable as list_group_component_env_variable_service, \
    list_component_env_variable as list_component_env_variable_service, \
    update as update_env_variable_service, \
    delete as delete_env_variable_service, \
    create_component_env_variable as create_component_env_variable_service

router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/components/{component_id}/env-variables', dependencies=[Depends(admit_by_cookie)],
            tags=['env-variables'], description="list component env variables")
async def list_component_env_variable(component_id: str,
                                      scope_id: str = Query(
                                          None, alias="scopeID"),
                                      scope_type: str = Query(
                                          EnvVariableScopeType.COMPONENT.value, alias="scopeType"),
                                      db=Depends(get_db)) -> ResponseData[Items[EnvVariableGet]]:
    if scope_type == EnvVariableScopeType.WORKSPACE.value:
        (db_env_variables, total) = await list_workspace_component_env_variable_service(db=db,
                                                                                        component_id=component_id,
                                                                                        workspace_id=scope_id)
    elif scope_type == EnvVariableScopeType.GROUP.value:
        group = db.query(Group).filter(Group.id == scope_id).first()
        if group is None:
            raise BadRequest("invalid group id")
        (db_env_variables, total) = await list_group_component_env_variable_service(db=db,
                                                                                    component_id=component_id,
                                                                                    group_id=scope_id,
                                                                                    workspace_id=group.workspace_id)
    elif scope_type == EnvVariableScopeType.COMPONENT.value:
        (db_env_variables, total) = await list_component_env_variable_service(db=db,
                                                                              component_id=component_id)
    else:
        raise BadRequest("invalid scope type")

    return ResponseData.new_items(items=[EnvVariableGet.model_validate(db_env_variable)
                                         for db_env_variable in db_env_variables],
                                  total=total)


@router.post('/components/{component_id}/env-variables', dependencies=[Depends(admit_by_cookie)],
             status_code=201, tags=['env-variables'])
async def create_component_env_variable(component_id: str,
                                        env_variable_create: EnvVariableCreate,
                                        request: Request, db=Depends(get_db)) \
        -> ResponseData[EnvVariableGet]:
    user: SessionUser = request.user
    db_env_variable = await create_component_env_variable_service(db,
                                                                  component_id,
                                                                  EnvVariableScopeType.COMPONENT,
                                                                  env_variable_create=env_variable_create,
                                                                  user_id=user.id)
    return ResponseData(data=EnvVariableGet.model_validate(db_env_variable))


@router.post('/workspace/{workspace_id}/env-variables', dependencies=[Depends(admit_by_cookie)],
             status_code=201, tags=['env-variables'])
async def create_workspace_env_variable(workspace_id: str,
                                        env_variable_create: EnvVariableCreate,
                                        request: Request, db=Depends(get_db)) \
        -> ResponseData[EnvVariableGet]:
    user: SessionUser = request.user
    db_env_variable = await create_component_env_variable_service(db,
                                                                  workspace_id,
                                                                  EnvVariableScopeType.WORKSPACE,
                                                                  env_variable_create=env_variable_create,
                                                                  user_id=user.id)
    return ResponseData(data=EnvVariableGet.model_validate(db_env_variable))


@router.post('/group/{group_id}/env-variables', dependencies=[Depends(admit_by_cookie)],
             status_code=201, tags=['env-variables'])
async def create_group_env_variable(group_id: str,
                                    env_variable_create: EnvVariableCreate,
                                    request: Request, db=Depends(get_db)) \
        -> ResponseData[EnvVariableGet]:
    user: SessionUser = request.user
    db_env_variable = await create_component_env_variable_service(db,
                                                                  group_id,
                                                                  EnvVariableScopeType.GROUP,
                                                                  env_variable_create=env_variable_create,
                                                                  user_id=user.id)
    return ResponseData(data=EnvVariableGet.model_validate(db_env_variable))


@router.post('/app/{app_id}/env-variables', dependencies=[Depends(admit_by_cookie)],
             status_code=201, tags=['env-variables'])
async def create_app_env_variable(app_id: str,
                                  env_variable_create: EnvVariableCreate,
                                  request: Request, db=Depends(get_db)) \
        -> ResponseData[EnvVariableGet]:
    user: SessionUser = request.user
    db_env_variable = await create_component_env_variable_service(db,
                                                                  app_id,
                                                                  EnvVariableScopeType.APP,
                                                                  env_variable_create=env_variable_create,
                                                                  user_id=user.id)
    return ResponseData(data=EnvVariableGet.model_validate(db_env_variable))


@router.get('/env-variables/{env_variable_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['env-variables'])
async def get_env_variable(env_variable_id: str, db=Depends(get_db)) \
        -> ResponseData[EnvVariableGet]:
    db_env_variable = await get_env_variable_service(db, env_variable_id)
    return ResponseData(data=EnvVariableGet.model_validate(db_env_variable))


@router.put('/env-variables/{env_variable_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['env-variables'])
async def update_env_variable(env_variable_id: str,
                              env_variable_update: EnvVariableUpdate,
                              request: Request,
                              db=Depends(get_db)) \
        -> ResponseData[EnvVariableGet]:
    user: SessionUser = request.user
    db_env_variable = await update_env_variable_service(db,
                                                        user.id,
                                                        env_variable_id,
                                                        env_variable_update)
    return ResponseData(data=EnvVariableGet.model_validate(db_env_variable))


@router.delete('/env-variables/{env_variable_id}', dependencies=[Depends(admit_by_cookie)],
               tags=['env-variables'], status_code=204)
async def delete_env_variable(env_variable_id: str,
                              request: Request,
                              db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_env_variable_service(db, user.id, env_variable_id)
