from typing import Annotated, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, Query

from ..service.account import get_by_id

from ..schema.member import MemberType, Role

from ..service.member import get_member_of_resource

from ..misc.middleware import ValidationErrorLoggingRoute

from ..schema.common import Pages, ResourceType, ResponseData, Items
from ..misc.session import SessionUser
from ..schema.component import ComponentGet, ComponentCreate, ComponentUpdate
from ..misc.authz import admit_by_cookie
from ..misc.db import get_db
from ..service.component import get as get_component_service, \
    list_components_v2 as list_component_service, \
    list_app_components as list_app_component_service, \
    update as update_component_service, \
    delete as delete_component_service, \
    create as create_component_service, \
    favorite as favorite_component_service, \
    cancel_favorite as cancel_favorite_component_service

router = APIRouter(route_class=ValidationErrorLoggingRoute)


# 已迁移
@router.get('/workspace/{workspace_id}/components', dependencies=[Depends(admit_by_cookie)],
            tags=['components'], description="list components")
async def list_component(workspace_id: str,
                         pages: Annotated[Pages, Depends(Pages)],
                         group_id: str = Query(None, alias="groupId"),
                         categories: str = Query(
                             None, alias="categories"),  # category names
                         # component types
                         types: str = Query(None, alias="types"),
                         app_types: str = Query(
                             None, alias="appTypes"),  # app types
                         has_env_var: bool = Query(
                             alias="hasEnvVar", default=False),
                         user_id: Optional[str] = Query(
                             alias="userID", default=None),
                         hide_deprecated: bool = Query(
                             alias="hideDeprecated", default=False),
                         is_favorite: bool = Query(
                             alias="isFavorite", default=False),
                         db=Depends(get_db)) -> ResponseData[Items[ComponentGet]]:
    category_names = []
    if categories is not None and len(categories) > 0:
        category_names = categories.split(',')
    if types is not None and len(types) > 0:
        types = types.split(',')
    if app_types is not None:
        app_types = app_types.split(',')

    (db_components, total) = await list_component_service(db,
                                                          workspace_id,
                                                          group_id,
                                                          category_names=category_names,
                                                          types=types,
                                                          app_types=app_types,
                                                          has_env_var=has_env_var,
                                                          user_id=user_id,
                                                          is_favorite=is_favorite,
                                                          hide_deprecated=hide_deprecated,
                                                          #                                                       offset=pages.offset,
                                                          #                                                       limit=pages.limit)
                                                          page=pages.page_number,
                                                          page_size=pages.page_size)

    return ResponseData.new_items(items=[ComponentGet.model_validate(component)
                                         for component in db_components],
                                  total=total)


@router.get('/app/{app_id}/components', dependencies=[Depends(admit_by_cookie)],
            tags=['components'], description="list components")
async def list_app_component(app_id: str,
                             pages: Annotated[Pages, Depends(Pages)],
                             categories: str = Query(
                                 None, alias="categories"),  # category names
                             has_env_var: bool = Query(
                                 alias="hasEnvVar", default=False),
                             db=Depends(get_db)) -> ResponseData[Items[ComponentGet]]:
    category_names = []
    if categories is not None:
        category_names = categories.split(',')

    (db_components, total) = await list_app_component_service(db,
                                                              app_id,
                                                              category_names=category_names,
                                                              has_env_var=has_env_var,
                                                              offset=pages.offset,
                                                              limit=pages.limit)

    return ResponseData.new_items(items=[ComponentGet.model_validate(component)
                                         for component in db_components],
                                  total=total)


# 已迁移测试
@router.post('/workspace/{workspace_id}/components', dependencies=[Depends(admit_by_cookie)],
             status_code=201, tags=['components'])
async def create_component(workspace_id: str,
                           component_create: ComponentCreate,
                           request: Request, db=Depends(get_db)) \
        -> ResponseData[ComponentGet]:
    user: SessionUser = request.user
    if component_create.scope == 'public':
        if not bool(user.is_admin):
            raise HTTPException(status_code=403, detail="您不是系统管理员，无法发布为公开组件")
    # 首先看 scope 是否是 scoped， 如果是 scoped 就需要有租户管理员权限
    if component_create.scope == 'scoped':
        # 如果是 public，则需要检查 workspace_id 是否是 public
        member = await get_member_of_resource(
            db, MemberType.USER.value, user.id, ResourceType.WORKSPACE.value, workspace_id)
        if not member or member.role != Role.ADMIN.value:
            raise HTTPException(status_code=403, detail="您不是租户管理员")
    if component_create.scope == 'group':
        if not component_create.group_id:
            raise HTTPException(status_code=400, detail="group_id is required")
        # 如果是 group，则需要检查 workspace_id 是否是 group
        member = await get_member_of_resource(
            db, MemberType.USER.value, user.id, ResourceType.GROUP.value, component_create.group_id)
        if not member or (member.role != Role.ADMIN.value and member.role != Role.DEVELOPER.value):
            raise HTTPException(status_code=403, detail="您不是组内成员")
    db_component = await create_component_service(db,
                                                  workspace_id,
                                                  component_create=component_create,
                                                  user_id=user.id)
    return ResponseData(data=ComponentGet.model_validate(db_component))


# 已迁移测试
@router.get('/components/{component_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['components'])
async def get_component(component_id: str, db=Depends(get_db)) \
        -> ResponseData[ComponentGet]:
    db_component = await get_component_service(db, component_id)
    component = ComponentGet.model_validate(db_component)
    return ResponseData(data=component)


# 迁移测试完成
@router.put('/components/{component_id}', dependencies=[Depends(admit_by_cookie)],
            tags=['components'])
async def update_component(component_id: str,
                           component_update: ComponentUpdate,
                           request: Request,
                           db=Depends(get_db)) \
        -> ResponseData[ComponentGet]:
    user: SessionUser = request.user
    # 先看用户是不是当前组件的创建者
    db_component = await get_component_service(db, component_id)
    if not db_component:
        raise HTTPException(status_code=404, detail="组件不存在")
    workspace_id = str(db_component.workspace_id)
    group_id = str(db_component.group_id)
    # 如果组件是公共组件，则需要检查用户是否系统管理员
    if str(db_component.scope) == 'public':
        if not bool(user.is_admin):
            raise HTTPException(status_code=403, detail="无法修改已经开放的组件")
    # 如果组件是租户组件，则需要检查用户是否租户管理员
    if str(db_component.scope) == 'scoped':
        member = await get_member_of_resource(
            db, MemberType.USER.value, user.id, ResourceType.WORKSPACE.value, workspace_id)
        if not member or str(member.role) != Role.ADMIN.value:
            raise HTTPException(status_code=403, detail="您不是租户管理员")
    # 如果组件是组内组件，则需要检查用户是否是创建者或管理员
    if str(db_component.scope) == 'group':
        member = await get_member_of_resource(
            db, MemberType.USER.value, user.id, ResourceType.GROUP.value, group_id)
        if not member or (str(member.role) != Role.ADMIN.value and db_component.created_by != user.id):
            raise HTTPException(status_code=403, detail="您不是当前组件的创建者或管理员")
    db_component = await update_component_service(db,
                                                  user.id,
                                                  component_id,
                                                  component_update)
    return ResponseData(data=ComponentGet.model_validate(db_component))


@router.delete('/components/{component_id}/favorite', dependencies=[Depends(admit_by_cookie)],
               tags=['components'], status_code=200)
async def cancel_favorite_component(component_id: str,
                                    request: Request,
                                    db=Depends(get_db)):
    user: SessionUser = request.user
    await cancel_favorite_component_service(db=db, user_id=user.id, component_id=component_id)


@router.post('/components/{component_id}/favorite', dependencies=[Depends(admit_by_cookie)],
             tags=['components'], status_code=200)
async def favorite_component(component_id: str,
                             request: Request,
                             db=Depends(get_db)):
    user: SessionUser = request.user
    await favorite_component_service(db=db, user_id=user.id, component_id=component_id)


# 已迁移
@router.delete('/components/{component_id}', dependencies=[Depends(admit_by_cookie)],
               tags=['components'], status_code=204)
async def delete_component(component_id: str,
                           request: Request,
                           db=Depends(get_db)):
    user: SessionUser = request.user
    await delete_component_service(db, user.id, component_id)
