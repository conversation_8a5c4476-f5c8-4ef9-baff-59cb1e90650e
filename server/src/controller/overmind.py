from fastapi import APIRouter, Depends, HTTPException, Request
from ..schema.common import ResponseData
from ..misc.authz import admit_by_cookie
from ..misc.session import SessionUser
from ..schema.overmind import ApproveCheckRequest
from ..service import overmind as overmind_service

router = APIRouter(prefix="/overmind", tags=["overmind"])


@router.post("/approve_check",
             #  dependencies=[Depends(admit_by_cookie)],
             status_code=200, tags=['overmind'], response_model_exclude_none=True)
async def approve_check(
    params: ApproveCheckRequest,
    request: Request,
):
    # user: SessionUser = request.user
    try:
        # params.operator = user.name
        res = await overmind_service.approve_check(params)
        return ResponseData(data=res)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
