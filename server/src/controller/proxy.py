import json
from math import log
from typing import List, Optional, Annotated, Any

from fastapi import APIRouter, Body, Depends, HTTPException, Request, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from ..service.app import list_config

from ..schema.app import ConfigType

from .app import list_app_config

from ..misc.db import get_db
from ..misc.middleware import ValidationErrorLoggingRoute
from ..misc.authz import admit_by_cookie
from ..misc.errors import LoginRequired
from ..misc.session import SessionUser
from ..schema.common import ResponseData, Items, Pages, ResponseDataWithCode
from ..schema.proxy import CIOPoolConfig, CIOPoolVO, CIOResourceTypes, \
    ProxyCIOPoolResourceContentRequest, \
    ProxyAirshipAlgorithmSimpleInfo, ProxyAirshipAlgorithmDetailInfo, \
    ProxyPythonScriptRequest, ProxyPythonScriptResponse, \
    ProxyScriptRequest, ProxyScriptResponse
from ..service.proxy import get_resource_type, get_skyeye_atomic_list, \
    get_skyeye_detail, get_skyeye_platform_list, list_content_pool, \
    get_pool_config, get_pool_resource_detail, \
    pool_resource_content_detail as pool_resource_content_detail_service, \
    pool_content_scene_field as pool_content_scene_field_service, \
    list_airship_rep2_algo as list_airship_rep2_algo_service, \
    get_airship_rep2_algo as get_airship_rep2_algo_service, \
    exec_python as exec_python_service, \
    aio_list_atomic as aio_list_atomic_service, \
    exec_script as exec_script_service, \
    post_agent_chat as post_agent_chat_service, \
    post_agent_chat_sse as post_agent_chat_sse_service, \
    post_common_request as post_common_request_service, \
    get_common_request as get_common_request_service, \
    get_aisong_mv_list as get_aisong_mv_list_service, \
    task_days_query_service
import time

router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get("/proxy/airship/rep2/algo", tags=['service'])
async def list_airship_rep2_algo() -> ResponseData[Items[ProxyAirshipAlgorithmSimpleInfo]]:
    (algos, count) = await list_airship_rep2_algo_service()
    return ResponseData.new_items(items=[
        ProxyAirshipAlgorithmSimpleInfo.model_validate(algo) for algo in algos
    ], total=count)


@router.get("/proxy/airship/rep2/algo/{algo_id}", tags=['service'])
async def get_airship_rep2_algo(algo_id: int) -> ResponseData[ProxyAirshipAlgorithmDetailInfo]:
    data = await get_airship_rep2_algo_service(algo_id=algo_id)
    algo = ProxyAirshipAlgorithmDetailInfo.model_validate(data)
    return ResponseData(data=algo)


@router.get("/proxy/aisong/mv/list", tags=['service'])
async def get_aisong_mv_list() -> ResponseData[List[dict]]:
    data = await get_aisong_mv_list_service()
    return ResponseData(data=data)


@router.get("/proxy/cio/resourcetypes", tags=['service'])
async def cio_list_resource_type(request: Request) -> ResponseData[CIOResourceTypes]:
    user: SessionUser = request.user
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    resource_types = await get_resource_type(user.name.split("@")[0])
    return ResponseData(data=CIOResourceTypes.model_validate(resource_types))


@router.get("/proxy/cio/pools", tags=['service'])
async def cio_list_content_pool(request: Request,
                                pages: Annotated[Pages, Depends(Pages)],
                                search_field: Optional[str] = Query(
                                    default=None, alias="searchField"),
                                keyword: Optional[str] = Query(
                                    default=None, alias="keyword"),
                                pool_code: Optional[str] = Query(
                                    default=None, alias="poolCode"),
                                resource_type: Optional[str] = Query(
                                    default=None, alias="resourceType"),
                                business: Optional[str] = Query(
                                    default=None, alias="business"),
                                creator: Optional[str] = Query(
                                    default=None, alias="creator"),
                                pool_type: Optional[str] = Query(
                                    default=None, alias="poolType"),
                                order_by: Optional[str] = Query(
                                    default=None, alias="orderBy"),
                                desc: Optional[bool] = Query(default=False, alias="desc")) \
        -> ResponseData[Items[CIOPoolVO]]:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    (pools, count) = await list_content_pool(search_field=search_field,
                                             keyword=keyword,
                                             pool_code=pool_code,
                                             resource_type=resource_type,
                                             business=business,
                                             creator=creator,
                                             pool_type=pool_type,
                                             order_by=order_by,
                                             limit=pages.limit,
                                             offset=pages.offset,
                                             desc=desc)
    return ResponseData.new_items(items=[
        CIOPoolVO.model_validate(pool) for pool in pools
    ], total=count)


@router.get("/proxy/cio/poolconfig", tags=['service'])
async def cio_get_pool_config(request: Request,
                              business: Optional[str] = Query(
                                  default=None, alias="business"),
                              resource_type: Optional[str] = Query(
                                  default=None, alias="resourceType"),
                              pool_code: Optional[str] = Query(
                                  default=None, alias="poolCode"),
                              config_types: Optional[str] = Query(
                                  default=None, alias="configTypes"),
                              merge: Optional[bool] = Query(default=False, alias="merge")) \
        -> ResponseData[Items[CIOPoolConfig]]:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    configs = await get_pool_config(business=business,
                                    resource_type=resource_type,
                                    pool_code=pool_code,
                                    config_types=config_types,
                                    merge=merge,
                                    role_permission=False)
    count = len(configs)
    return ResponseData.new_items(items=[
        CIOPoolConfig.model_validate(config) for config in configs
    ], total=count)


@router.get("/proxy/cio/resourcedetail", tags=['service'])
async def cio_pool_resource_detail(request: Request,
                                   resource_type: Optional[str] = Query(
                                       default=None, alias="resourceType"),
                                   pool_code: Optional[str] = Query(
                                       default=None, alias="poolCode"),
                                   resource_id: Optional[str] = Query(default=None, alias="resourceID")) \
        -> ResponseData[dict]:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    resource_detail = await get_pool_resource_detail(resource_type=resource_type,
                                                     pool_code=pool_code,
                                                     resource_id=resource_id)
    return ResponseData(data=resource_detail)


@router.post("/proxy/cio/pool/resource/content/detail", tags=['service'])
async def cio_pool_resource_content_detail(request: Request,
                                           cio_request: ProxyCIOPoolResourceContentRequest) \
        -> ResponseData[dict]:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    cio_request.operator = request.user.name
    resp = await pool_resource_content_detail_service(cio_request=cio_request)
    return ResponseData(data=resp)


@router.get("/proxy/cio/content/scene/langbase/field", tags=['service'])
async def cio_pool_content_scene_field(request: Request,
                                       business: Optional[str] = Query(
                                           default=None, alias="business"),
                                       resource_type: Optional[str] = Query(
                                           default=None, alias="resourceType"),
                                       pool_code: Optional[str] = Query(
                                           default=None, alias="poolCode"),
                                       scene: Optional[str] = Query(default=None, alias="scene")) \
        -> ResponseData[dict]:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    resp = await pool_content_scene_field_service(business=business, resource_type=resource_type,
                                                  pool_code=pool_code, scene=scene)
    return ResponseData(data=resp)


@router.post("/proxy/script/python", tags=['service'])
async def exec_python(request: ProxyPythonScriptRequest) -> ResponseData[ProxyPythonScriptResponse]:
    outputs = await exec_python_service(request=request)
    return ResponseData(data=outputs)


@router.post("/proxy/script/exec", tags=['service'])
async def exec_script(request: ProxyScriptRequest) -> ResponseData[ProxyScriptResponse]:
    outputs = await exec_script_service(request=request)
    return ResponseData(data=outputs)


@router.get("/proxy/aio/atomic", tags=['service'])
async def aio_list_atomic(request: Request,
                          pages: Annotated[Pages, Depends(Pages)]) \
        -> ResponseData[Items[dict]]:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    (atomics, count) = await aio_list_atomic_service(limit=pages.limit, offset=pages.offset)
    return ResponseData.new_items(items=atomics, total=count)


@router.get("/proxy/skyeye/atomic", tags=['service'])
async def skyeye_list_atomic(request: Request,
                             limit: int = Query(default=10, alias="limit"),
                             offset: int = Query(default=0, alias="offset"),
                             tag: Optional[str] = Query(
                                 default=None, alias="tag"),
                             platform_name: Optional[str] = Query(
                                 default=None, alias="platformName"),
                             name: Optional[str] = Query(default=None, alias="name")) \
        -> ResponseDataWithCode:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    outputs = await get_skyeye_atomic_list(limit=limit, offset=offset, name=name, platformName=platform_name, tag=tag)
    return outputs


@router.get("/proxy/skyeye/platform", tags=['service'])
async def skyeye_list_platform(request: Request,
                               limit: int = Query(default=10, alias="limit"),
                               offset: int = Query(default=0, alias="offset")) \
        -> ResponseDataWithCode:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    outputs = await get_skyeye_platform_list(limit=limit, offset=offset)
    return outputs


@router.get("/proxy/skyeye/detail", tags=['service'])
async def skyeye_detail(request: Request,
                        atomicServiceId: int = Query(default=10, alias="atomicServiceId")) \
        -> ResponseDataWithCode:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    outputs = await get_skyeye_detail(atomicServiceId=atomicServiceId)
    return outputs


@router.post("/app/{app_id}/property-update", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def property_save(request: Request,
                        params: dict) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = {
        **params['data'],
        'operator': request.user.id,
    }
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/setting-publish", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def setting_publish_save(request: Request,
                               params: dict) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = {
        **params['data'],
        'operator': request.user.id,
    }
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/property-search", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def property_search(request: Request,
                          params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/virtual-human-chat", tags=['service'])
async def agent_chat(request: Request,
                     params: dict[str, Any]) -> dict:
    # if not request.user.is_authenticated:
    #     raise LoginRequired("You have to login first")
    outputs = await post_agent_chat_service(
        params=params)
    return outputs


def event_generator():
    while True:
        # 模拟数据生成
        time.sleep(1)  # 每秒发送一次数据
        data = {
            "data": {
                "content": {
                    "type": 'TEXT',
                    "content": "测试"
                }
            }
        }
        yield data


@router.post("/app/{app_id}/virtual-human-chat-sse", tags=['service'])
async def agent_chat_sse(request: Request,
                         db: Session = Depends(get_db),
                         ) -> StreamingResponse:

    # print(params)
    # if not request.user.is_authenticated:
    #     raise LoginRequired("You have to login first")
    # outputs = await post_agent_chat_service(
    #     params=params)
    data = await request.json()
    if data.get('reqBizExtInfo'):
        ext_info = json.loads(data['reqBizExtInfo'])
        if not ext_info.get('configId'):
            configs, _ = await list_config(db=db, app_id=ext_info.get('appId', ''), type=ConfigType.HISTORY, offset=0, limit=1)
            if configs:
                ext_info['configId'] = configs[0].id
        if not ext_info.get('configId'):
            raise HTTPException(status_code=400, detail="configId is required")
        data['reqBizExtInfo'] = json.dumps(ext_info)
    return StreamingResponse(post_agent_chat_sse_service(data), media_type="text/event-stream")
    # return outputs


@router.post("/app/{app_id}/agent-variables", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_agent_variables(request: Request,
                               params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/publish-channels", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_publish_channels(request: Request,
                                params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/publish-rollback", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_publish_rollback(request: Request,
                                params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/publish-history", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_publish_history(request: Request,
                               params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.get("/proxy/task/days/query", tags=['service'])
async def task_days_query(request: Request) -> dict:
    params = dict(request.query_params)
    outputs = await task_days_query_service(
        params=params)
    return outputs


@router.get("/app/{app_id}/portrait-style", tags=['service'])
async def ortrait_style(request: Request) -> dict:
    path = request.query_params['proxyInfo']
    params = dict((k, v)
                  for k, v in request.query_params.items() if k != 'proxyInfo')
    outputs = await get_common_request_service(path=path,
                                               params=params)
    return outputs


@router.post("/app/{app_id}/portrait-prompt-polish", tags=['service'])
async def portrait_prompt_polish(request: Request, params: dict[str, Any]) -> dict:
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/portrait-generate", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_portrait_generate(request: Request,
                                 params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.get("/proxy/emotion-style", tags=['service'])
async def get_style_emotion(request: Request) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = request.query_params['proxyInfo']
    params = dict((k, v)
                  for k, v in request.query_params.items() if k != 'proxyInfo')
    outputs = await get_common_request_service(path=path,
                                               params=params)
    return outputs


@router.get("/proxy/emotion-recognize", tags=['service'])
async def get_recognize_emotion(request: Request) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = request.query_params['proxyInfo']
    params = dict((k, v)
                  for k, v in request.query_params.items() if k != 'proxyInfo')

    outputs = await get_common_request_service(path=path,
                                               params=params)
    return outputs


@router.get("/proxy/account-info", tags=['service'])
async def get_account_info(request: Request) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = request.query_params['proxyInfo']
    params = dict((k, v)
                  for k, v in request.query_params.items() if k != 'proxyInfo')

    outputs = await get_common_request_service(path=path,
                                               params=params)
    return outputs


@router.get("/app/{app_id}/emotion-delete", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def delete_emotion(request: Request) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = request.query_params['proxyInfo']
    # 处理掉代理信息
    params = dict((k, v)
                  for k, v in request.query_params.items() if k != 'proxyInfo')

    outputs = await get_common_request_service(path=path,
                                               params=params)
    return outputs


@router.get("/app/{app_id}/portrait-generate-result", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_portrait_generate_result(request: Request) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = request.query_params['proxyInfo']
    params = dict((k, v)
                  for k, v in request.query_params.items() if k != 'proxyInfo')

    outputs = await get_common_request_service(path=path,
                                               params=params)
    return outputs


@router.post("/app/{app_id}/portrait-add", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_portrait_add(request: Request,
                            params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/portrait-query", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_portrait_query(request: Request,
                              params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/emotion-add", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_emoil_add(request: Request,
                         params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/emotion-query", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_emoil_query(request: Request,
                           params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/save-trigger", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_save_trigger(request: Request,
                            params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/query-trigger", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_query_trigger(request: Request,
                             params: dict) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/trigger-variables", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_trigger_variables(request: Request,
                                 params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/nonuser-variables", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_nonuser_variables(request: Request,
                                 params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/emotion-query-by-ids", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_emoil_query_by_ids(request: Request,
                                  params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


@router.post("/app/{app_id}/task-query", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_task_query(request: Request,
                          params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                params=params)
    return outputs


# 通用POST代理
@router.post("/proxy/{path:path}", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_common_with_path(request: Request,
                                path: str,
                                params: dict[str, Any] = Body(...)) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    # 提取额外的代理信息
    proxy_info = params.get('proxyInfo', {})
    base = proxy_info.get('base')
    timeout = proxy_info.get('timeout')
    # 移除proxyInfo，保留其他所有参数
    if 'proxyInfo' in params:
        del params['proxyInfo']
    outputs = await post_common_request_service(path=f"/{path}",
                                                base=base,
                                                timeout=timeout,
                                                params=params)
    return outputs

# 通用GET代理


@router.get("/proxy/{path:path}", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def get_common_with_path(request: Request,
                               path: str) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    # 从query params中获取所有参数
    params = dict(request.query_params)
    # 提取代理信息
    proxy_info = params.get('proxyInfo', {})
    if isinstance(proxy_info, str):
        proxy_info = json.loads(proxy_info)
    base = proxy_info.get('base') if proxy_info else None
    timeout = proxy_info.get('timeout') if proxy_info else None
    # 移除proxyInfo，保留其他参数
    if 'proxyInfo' in params:
        del params['proxyInfo']
    outputs = await get_common_request_service(path=f"/{path}",
                                               base=base,
                                               timeout=timeout,
                                               params=params)
    return outputs


@router.post("/app/{app_id}/post-common-proxy", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def app_post_common(request: Request,
                          params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    base = params['proxyInfo']['base'] if 'base' in params['proxyInfo'] else None
    timeout = params['proxyInfo']['timeout'] if 'timeout' in params['proxyInfo'] else None
    params = params['data']
    outputs = await post_common_request_service(path=path,
                                                base=base,
                                                timeout=timeout,
                                                params=params)
    return outputs


@router.post("/post-common-proxy", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def post_common(request: Request,
                      params: dict[str, Any]) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = params['proxyInfo']['url']
    base = params['proxyInfo']['base'] if 'base' in params['proxyInfo'] else None
    timeout = params['proxyInfo']['timeout'] if 'timeout' in params['proxyInfo'] else None
    if 'data' in params:
        params = params['data']
    else:
        params = {}
    outputs = await post_common_request_service(path=path,
                                                base=base,
                                                timeout=timeout,
                                                params=params)
    return outputs


@router.get("/app/{app_id}/get-common-proxy", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def app_get_common(request: Request) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = request.query_params['proxyInfo']
    base = request.query_params['base'] if 'base' in request.query_params else None
    timeout = request.query_params['timeout'] if 'timeout' in request.query_params else None
    # 处理掉代理信息
    params = dict((k, v)
                  for k, v in request.query_params.items() if k != 'proxyInfo')
    outputs = await get_common_request_service(path=path,
                                               base=base,
                                               params=params)
    return outputs


@router.get("/get-common-proxy", dependencies=[Depends(admit_by_cookie)], tags=['service'])
async def get_common(request: Request) -> dict:
    if not request.user.is_authenticated:
        raise LoginRequired("You have to login first")
    path = request.query_params['proxyInfo']
    base = request.query_params['base'] if 'base' in request.query_params else None
    timeout = request.query_params['timeout'] if 'timeout' in request.query_params else None
    # 处理掉代理信息
    params = dict((k, v)
                  for k, v in request.query_params.items() if k != 'proxyInfo')
    outputs = await get_common_request_service(path=path,
                                               base=base,
                                               timeout=timeout,
                                               params=params)
    return outputs
