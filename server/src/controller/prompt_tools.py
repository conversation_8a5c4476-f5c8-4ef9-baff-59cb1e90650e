
from typing import List, LiteralString
from fastapi import APIRouter, Depends, Request, HTTPException
from ..misc.session import SessionUser
from ..misc.errors import LangbaseException
from ..schema.common import ResponseData
from ..misc.db import get_db
from ..misc.authz import admit_by_cookie
from ..schema.prompt_tools import AiBeautifyRequest, Convert2JsonRequest, \
    Convert2TextRequest, \
    Beautify2TextRequest
from ..service.prompt_tools import convert_prompt_to_struct, \
    convert_prompt_to_struct_text, \
    beautify_to_text, \
    ai_beautify as ai_beautify_service

router = APIRouter()


@router.post("/prompt-tools/convert2json",
             dependencies=[Depends(admit_by_cookie)],

             tags=['prompt-tools'],
             description="convert Prompt To StructPrompt JSON")
async def convert_to_json(
    request: Request,
    data: Convert2JsonRequest,
    db=Depends(get_db)
) -> ResponseData:
    user: SessionUser = request.user

    resp: List[dict[str, str]] = await convert_prompt_to_struct(db, data.prompt, user.id)

    return ResponseData(data={
        "structPrompt": resp
    })


@router.post("/prompt-tools/ai-beautify",
             dependencies=[Depends(admit_by_cookie)],

             tags=['prompt-tools'],
             description="convert Prompt To StructPrompt Text")
async def ai_beautify(
    request: Request,
    data: AiBeautifyRequest,
    db=Depends(get_db)
) -> ResponseData:
    user: SessionUser = request.user

    resp: str = await ai_beautify_service(db, data.pre_prompt, data.requirements, user.id)

    return ResponseData(data={
        "prompt": resp
    })


@router.post("/prompt-tools/convert2struct-text",
             dependencies=[Depends(admit_by_cookie)],

             tags=['prompt-tools'],
             description="convert Prompt To StructPrompt Text")
async def convert_(
    request: Request,
    data: Convert2JsonRequest,
    db=Depends(get_db)
) -> ResponseData:
    user: SessionUser = request.user

    resp: str = await convert_prompt_to_struct_text(db, data.prompt, user.id)

    return ResponseData(data={
        "prompt": resp
    })


@router.post("/prompt-tools/convert2text",
             dependencies=[Depends(admit_by_cookie)],
             tags=['prompt-tools'],
             description="convert StructPrompt To  prompt Text")
async def convert_struct_to_text(
        data: Convert2TextRequest,
) -> ResponseData:

    # 转换逻辑：将每项结构化提示转换为 ### title 和内容的格式
    prompt_parts = []
    for item in data.structPrompt:
        # 拼接字符串，以符合要求的格式
        formatted_text = f"### {item.title}\n{item.content}"
        prompt_parts.append(formatted_text)

    # 合并所有部分到一个单独的字符串
    prompt: LiteralString = "\n".join(prompt_parts)

    return ResponseData(data={"prompt": prompt})


@router.post("/prompt-tools/beautify2text",
             dependencies=[Depends(admit_by_cookie)],
             tags=['prompt-tools'],
             description="beautify StructPrompt To  prompt Text")
async def beautify_to_text_handler(data: Beautify2TextRequest, request: Request, db=Depends(get_db)) -> ResponseData:
    user: SessionUser = request.user
    result = await beautify_to_text(db,
                                    data.structPrompt,
                                    data.subStructPrompt,
                                    data.subStructPromptType, user.id)
    return ResponseData(data={"prompt": result})
