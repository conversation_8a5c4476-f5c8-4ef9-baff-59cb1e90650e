from typing import Annotated, List, Optional
from fastapi import APIRouter, Depends, Query, Request, <PERSON>ie

from ..misc.redis_utils import ACCOUNT_CACHE_KEY_ID, delete_redis_sync

from ..cache.account import init_account_cache

from ..misc.middleware import ValidationErrorLoggingRoute

from ..schema.member import UserRole
from ..service.member import list_user as list_user_member_service
from ..schema.common import Items, Pages, ResponseData, ResourceType
from ..misc.authz import admit_by_cookie
from ..misc.session import SessionUser
from ..misc.db import get_db
from ..service.account import list as list_accounts, remove_admin, set_admin
from ..schema.account import AccountWithIsAdmin as AccountSchema
from ..service.app import get as get_app_service
from ..service.group import get as get_group_service

router = APIRouter(route_class=ValidationErrorLoggingRoute)


# 已迁移
@router.get('/account', tags=['account'],
            dependencies=[Depends(admit_by_cookie)])
async def list_account(page: Annotated[Pages, Depends(Pages)],
                       id: Annotated[Optional[List[str]], Query()] = None,
                       name: Annotated[Optional[List[str]], Query()] = None,
                       db=Depends(get_db)) \
        -> ResponseData[Items[AccountSchema]]:
    (db_accounts, count) = await list_accounts(db,
                                               id, name,
                                               page.offset, page.limit)
    accounts = [AccountSchema.model_validate(db_account)
                for db_account in db_accounts]
    return ResponseData.new_items(items=accounts, total=count)


@router.get('/userinfo', tags=['account'],
            dependencies=[Depends(admit_by_cookie)])
async def get_userinfo(request: Request) -> ResponseData[SessionUser]:
    user: SessionUser = request.user
    return ResponseData(data=user)


@router.get('/pmsinfo', tags=['account'],
            dependencies=[Depends(admit_by_cookie)])
async def get_pmsinfo(pms_u: str = Cookie(None, alias="PMS_U")) -> ResponseData[dict[str, str | None]]:
    return ResponseData(data={"pms_u": pms_u})  # type: ignore


@router.get('/set_admin', tags=['account'])
async def set_user(mail: str, db=Depends(get_db)) -> ResponseData[SessionUser]:
    user = await set_admin(db, mail)
    init_account_cache()
    delete_redis_sync(ACCOUNT_CACHE_KEY_ID + str(user.id))
    return ResponseData(data=user)


# 已迁移
@router.get('/remove_admin', tags=['account'])
async def remove_user(mail: str, db=Depends(get_db)) -> ResponseData[SessionUser]:
    user = await remove_admin(db, mail)
    init_account_cache()
    delete_redis_sync(ACCOUNT_CACHE_KEY_ID + str(user.id))
    return ResponseData(data=user)


@router.get('/userroles', tags=['account'],
            dependencies=[Depends(admit_by_cookie)])
async def get_userroles(request: Request,
                        scope_type: str = Query(None, alias="scopeType"),
                        scope_id: str = Query(None, alias="scopeID"),
                        db=Depends(get_db)) -> ResponseData[List[UserRole]]:
    user: SessionUser = request.user
    db_roles, _ = await list_user_member_service(
        db,
        user_id=user.identity)

    user_role = None
    if scope_type is not None and scope_id is not None and \
            scope_type == ResourceType.WORKSPACE.value:
        for role in db_roles:
            if role.resource_type == ResourceType.WORKSPACE.value and role.resource_id == scope_id:
                user_role = UserRole.model_validate(role)

    if scope_type is not None and scope_id is not None and \
            scope_type == ResourceType.GROUP.value:
        group = await get_group_service(db=db, group_id=scope_id)
        for role in db_roles:
            if role.resource_type == ResourceType.WORKSPACE.value and role.resource_id == group.workspace_id:
                user_role = UserRole.model_validate(role)
        for role in db_roles:
            if role.resource_type == ResourceType.GROUP.value and role.resource_id == scope_id:
                user_role = UserRole.model_validate(role)

    if scope_type is not None and scope_id is not None and \
            scope_type == ResourceType.APP.value:
        app = await get_app_service(db=db, app_id=scope_id)
        for role in db_roles:
            if role.resource_type == ResourceType.WORKSPACE.value and role.resource_id == app.workspace_id:
                user_role = UserRole.model_validate(role)
        for role in db_roles:
            if role.resource_type == ResourceType.GROUP.value and role.resource_id == app.group_id:
                user_role = UserRole.model_validate(role)
        for role in db_roles:
            if role.resource_type == ResourceType.APP.value and role.resource_id == scope_id:
                user_role = UserRole.model_validate(role)

    if user_role is not None:
        return ResponseData(data=[user_role])

    return ResponseData(data=[
        UserRole.model_validate(db_role) for db_role in db_roles
    ])
