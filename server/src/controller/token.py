from typing import Annotated, <PERSON>, Optional
from uuid import uuid4
from fastapi import APIRouter, Depends, Request
from starlette.authentication import Unauthenticated<PERSON>ser
from datetime import datetime, timedelta
import jwt

from ..misc.middleware import ValidationErrorLoggingRoute

from ..config import settings

from ..misc.session import SessionUser
from ..schema.token import TEMPORARY_USER_PREFIX, \
    AnonymousJWTPayload, AuthenticatedJWTPayload, \
    JWTToken, TokenGet, TokenCreate, TokenRelatedResourceGet, TokenUpdate
from ..schema.common import ResourceType
from ..schema.common import Pages, ResponseData
from ..misc.db import get_db
from ..misc.http_db import get_http_db
from ..misc.authz import admit_by_cookie
from ..service.token import \
    delete as delete_token_service, \
    list as list_token_service, \
    create as create_token_service, \
    list_related_resources as list_token_related_resources_service
# update as update_token_service, \

router = APIRouter(route_class=ValidationErrorLoggingRoute)


@router.get('/token/{token_id}/related-resources',
            tags=['token'], status_code=200)
async def list_token_resource(
        token_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        db=Depends(get_db)):
    (related_resources, total) = await list_token_related_resources_service(db=db,
                                                                            token_id=token_id,
                                                                            offset=pages.offset,
                                                                            limit=pages.limit)

    return ResponseData.new_items(
        items=[TokenRelatedResourceGet.model_validate(
            db_resource) for db_resource in related_resources],
        total=total,
    )


@router.delete('/token/{token_id}',
               dependencies=[Depends(admit_by_cookie)],
               tags=['token'], status_code=204)
async def delete_token(
        token_id: str,
        db=Depends(get_http_db)):
    await delete_token_service(db, token_id)


# @router.put('/token/{token_id}',
#             dependencies=[Depends(admit_by_cookie)],
#             tags=['token'], status_code=204)
# async def update_token(
#         request: Request,
#         token_id: str,
#         token_update: TokenUpdate,
#         db=Depends(get_db)):
#     user: SessionUser = request.user
#     await update_token_service(db, user_id=user.id, token_id=token_id, token_update=token_update)


@router.get('/{resource_type}/{resource_id}/token',
            dependencies=[Depends(admit_by_cookie)],
            tags=['token'], response_model_exclude_none=True)
async def list_token(
        resource_type: ResourceType,
        resource_id: str,
        pages: Annotated[Pages, Depends(Pages)],
        db=Depends(get_http_db),
):
    db_tokens, count = await list_token_service(
        db,
        resource_type=resource_type,
        resource_id=resource_id,
        limit=pages.limit,
        offset=pages.offset,
    )

    return ResponseData.new_items(
        items=[TokenGet.model_validate(db_token) for db_token in db_tokens],
        total=count,
    )


@router.post('/{resource_type}/{resource_id}/token',
             dependencies=[Depends(admit_by_cookie)],
             response_model_exclude_none=True,
             tags=['token'], status_code=201)
async def create_token(
        request: Request,
        resource_type: ResourceType,
        resource_id: str,
        token_create: Optional[TokenCreate] = None,
        db=Depends(get_http_db),
):
    user: SessionUser = request.user
    token = await create_token_service(
        db,
        resource_type=resource_type,
        resource_id=resource_id,
        user_id=user.id,
        token_create=token_create
    )
    return ResponseData(
        data=TokenGet.model_validate(token),
    )


@router.post('/app/{app_id}/temp-token', tags=['token'])
async def create_temporary_token(
        app_id: str,
        request: Request,
):
    user: Union[SessionUser, UnauthenticatedUser] = request.user

    exp = datetime.utcnow() + timedelta(seconds=settings.jwt_expire_seconds)

    if user.is_authenticated:
        payload = AuthenticatedJWTPayload(
            user_id=user.id,  # type: ignore
            app_id=app_id,
            exp=exp,
            is_admin=user.is_admin,  # type: ignore
        )
    else:
        payload = AnonymousJWTPayload(
            user_id=f'{TEMPORARY_USER_PREFIX}:{str(uuid4())}',
            app_id=app_id,
            exp=exp,
        )

    jwt_token = jwt.encode(
        payload.model_dump(),
        settings.jwt_secret_key,
        algorithm=settings.jwt_algorithm)

    return ResponseData(
        data=JWTToken(
            token=jwt_token,
            expired_at=exp,
        )
    )
