from pydantic import BaseModel
from typing import List, Optional
from enum import Enum


class PromptItemType(Enum):
    ROLE = "role"
    BG = "bg"
    SKILL = "skill"
    TASK = "task"
    REQUIRE = "require"
    OUTPUT_FORMAT = "output_format"
    CUSTOM = "custom"


class Convert2JsonRequest(BaseModel):
    prompt: str


class AiBeautifyRequest(BaseModel):
    pre_prompt: str
    requirements: Optional[str] = ''


class StructPromptItem(BaseModel):
    title: str
    content: str
    type: PromptItemType


class SubStructPromptItem(BaseModel):
    title: str
    content: str
    type: Optional[str] = ''


class Beautify2TextRequest(BaseModel):
    structPrompt: List[StructPromptItem]
    subStructPrompt: List[SubStructPromptItem]
    subStructPromptType: Optional[str] = ''  # 类型 'ro


class Convert2TextRequest(BaseModel):
    structPrompt: List[StructPromptItem]
