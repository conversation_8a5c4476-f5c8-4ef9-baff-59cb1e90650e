from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from enum import Enum
from src.schema.common import ResourceType


class CollectionCreate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    user_id: str = Field(validation_alias='user_id',
                         serialization_alias='userID')
    resource_type: ResourceType = Field(validation_alias='resource_type',
                                        serialization_alias='resourceType')
    resource_id: str = Field(validation_alias='resource_id',
                             serialization_alias='resourceID')


class CollectionResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    user_id: str
    resource_id: str
    resource_type: ResourceType
    group_id: str
