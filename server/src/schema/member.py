from typing import Optional
from pydantic import BaseModel, ConfigDict, Field
from datetime import datetime
from enum import Enum

from .common import Who


class Role(Enum):
    SYS_ADMIN = "sys_admin"
    ADMIN = "admin"
    DEVELOPER = "developer"
    VIEWER = "viewer"
    EXTERNAL_USER = "external_user"


class GroupRole(Enum):
    EXTERNAL_USER = "external_user"
    INTERNAL_USER = "internal_user"


class MemberType(Enum):
    USER = "user"
    GROUP = "group"


class MemberBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    member_type: str = Field(default=MemberType.USER.value,
                             validation_alias='memberType')
    member_id: str = Field(validation_alias='memberID')
    role: str = Field(alias='role')


class MemberCreate(MemberBase):
    member_type: str = Field(default=MemberType.USER.value,
                             alias='memberType')
    member_id: str = Field(alias='memberID')
    role: str = Field(alias='role')


class MemberGet(MemberBase):
    model_config = ConfigDict(from_attributes=True)

    resource_type: str = Field(validation_alias='resource_type',
                               serialization_alias='resourceType')
    resource_id: str = Field(validation_alias='resource_id',
                             serialization_alias='resourceID')
    member_type: str = Field(default=MemberType.USER.value,
                             serialization_alias='memberType',
                             validation_alias='member_type')
    member_id: str = Field(serialization_alias='memberID',
                           validation_alias='member_id')
    member: Who = Field(alias='member')
    role: str = Field(alias='role')
    created_at: datetime = Field(serialization_alias='createdAt',
                                 validation_alias='created_at')
    updated_at: datetime = Field(serialization_alias='updatedAt',
                                 validation_alias='updated_at')
    granted_by: Who = Field(alias='grantedBy')


class MemberUpdated(MemberBase):
    model_config = ConfigDict(from_attributes=True)

    member_type: str = Field(default=MemberType.USER.value,
                             serialization_alias='memberType',
                             validation_alias='member_type')
    member_id: str = Field(serialization_alias='memberID',
                           validation_alias='member_id')
    role: str = Field(alias='role')
    created_at: datetime = Field(serialization_alias='createdAt',
                                 validation_alias='created_at')
    updated_at: datetime = Field(serialization_alias='updatedAt',
                                 validation_alias='updated_at')
    granted_by: Who = Field(alias='grantedBy')


class UserRole(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    resource_type: str = Field(validation_alias='resource_type',
                               serialization_alias='resourceType')
    resource_id: str = Field(validation_alias='resource_id',
                             serialization_alias='resourceID')
    member_id: str = Field(validation_alias='member_id',
                           serialization_alias='memberID')
    role: str = Field(alias='role')
