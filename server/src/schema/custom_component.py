from enum import Enum
from typing import Any, Literal, Optional, List
from pydantic import BaseModel, ConfigDict, Field, AliasChoices
from datetime import datetime

from .common import Creater, Updater


class ComponentType(Enum):
    INPUT = "INPUT"
    OUTPUT = "OUTPUT"
    SCRIPT = "SCRIPT"
    SWITCH = "SWITCH"
    SELECT = "SELECT"
    FOREACH = "FOREACH"
    HTTP = "HTTP"
    LLM_PLUGIN = "LLM-PLUGIN"
    DISPLAY = "DISPLAY"
    LLM = "LLM"


class ComponentAppType(str, Enum):
    WORKFLOW = "workflow"
    AGENT = "agent"
    AGENT_WORKFLOW = "agentWorkflow"


class CustomComponentBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str = Field(min_length=1, max_length=128)
    code: str = Field(min_length=1, max_length=128)
    render_key: str = Field(min_length=1, max_length=128)
    description: Optional[str] = Field(
        default=None, min_length=1, max_length=256)
    type: str = Field(min_length=1, max_length=64)
    category_id: str = Field(min_length=1, max_length=36,
                             validation_alias=AliasChoices(
                                 'category_id', 'categoryID'),
                             serialization_alias='categoryID')
    category_name: Optional[str] = Field(min_length=1, max_length=128,
                                         validation_alias=AliasChoices(
                                             'category_name', 'category'),
                                         serialization_alias='category')
    workspace_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices(
                                  'workspace_id', 'workspaceID'),
                              serialization_alias='workspaceID')
    group_id: Optional[str] = Field(min_length=0, max_length=36,
                                    validation_alias=AliasChoices(
                                        'group_id', 'groupID'),
                                    serialization_alias='groupID')
    scope: str = Field(min_length=1, max_length=20)
    config: dict = Field()
    extra_config: Optional[dict] = Field(default=None,
                                         validation_alias=AliasChoices(
                                             'extra_config', 'extraConfig'),
                                         serialization_alias='extraConfig')
    deprecated: bool = Field(default=False)
    app_types: Optional[List[str]] = Field(default=None,
                                           validation_alias=AliasChoices(
                                               'appTypes', 'app_types'),
                                           serialization_alias='appTypes')


class CustomComponentCreate(CustomComponentBase):
    model_config = ConfigDict(from_attributes=True)


class CustomComponentUpdate(BaseModel):
    id: str = Field(min_length=1, max_length=36)
    name: Optional[str] = Field(min_length=1, max_length=128)
    description: Optional[str] = Field(
        default=None, min_length=1, max_length=256)
    type: Optional[str] = Field(min_length=1, max_length=64)
    category_id: Optional[str] = Field(min_length=1, max_length=36,
                                       validation_alias=AliasChoices(
                                           'category_id', 'categoryID'),
                                       serialization_alias='categoryID')
    category_name: Optional[str] = Field(min_length=1, max_length=128,
                                         validation_alias=AliasChoices(
                                             'category_name', 'category'),
                                         serialization_alias='category')
    scope: Optional[str] = Field(min_length=1, max_length=20)
    group_id: str = Field(min_length=1, max_length=36,
                          validation_alias=AliasChoices(
                              'group_id', 'groupID'),
                          serialization_alias='groupID')
    config: dict = Field()
    deprecated: Optional[bool] = Field(default=False)
    app_types: Optional[List[str]] = Field(default=None,
                                           validation_alias=AliasChoices(
                                               'appTypes', 'app_types'),
                                           serialization_alias='appTypes')
    extra_config: dict = Field(validation_alias=AliasChoices(
        'extra_config', 'extraConfig'), serialization_alias='extraConfig')


class CustomComponentGet(CustomComponentBase, Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')
