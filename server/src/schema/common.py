from enum import Enum
from typing import Annotated, Generic, List, Optional, TypeVar
from fastapi import Query
from pydantic import BaseModel, ConfigDict


class Pages:
    def __init__(self,
                 page_size: Annotated[int, Query(
                     alias='pageSize',
                     ge=1,
                     le=5000)] = 10,
                 page_number: Annotated[int, Query(
                     alias='pageNumber',
                     ge=1)] = 1):
        self.page_size = page_size
        self.page_number = page_number

    @property
    def limit(self):
        return self.page_size

    @property
    def offset(self):
        return (self.page_number - 1) * self.page_size


class Who(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    fullname: str
    email: str


class Creater:
    createdBy: Optional[Who]


class Updater:
    updatedBy: Optional[Who]


T = TypeVar('T')
A = TypeVar('A')


class UseType(str, Enum):
    STAR = 'star'
    ALL = 'all'


class Items(BaseModel, Generic[A]):
    total: int
    items: List[A]


class ResponseData(BaseModel, Generic[T]):
    data: T

    @classmethod
    def new_items(cls, items: List[T], total: int):
        return ResponseData(data=Items(items=items, total=total))


class ResponseDataWithCode(BaseModel):
    code: int
    data: dict


class ResponseDataWithCodeMessage(BaseModel):
    code: int
    data: dict
    message: Optional[str]


class Scope(str, Enum):
    GROUP = 'group'
    SCOPED = 'scoped'
    PUBLIC = 'public'


class AigwResourceType(str, Enum):
    GROUP = 'Group'
    WORKSPACE = 'Workspace'


class ResourceType(Enum):
    WORKSPACE = "workspace"
    GROUP = "group"
    APP = "app"
    LLM_MODELS = "llm-models"
    ModelProvider = "model-provider"
    Conversation = "conversation"
    Token = "token"
    WORKFLOW_ENGINES = "workflow-engines"
    COMPONENTS = "components"
    ENV_VARIABLES = "env-variables"
    WORKFLOW_RUNS = "workflow-runs"
    AIGW_APP = "aigw_app"
    APOLLO = "apollo"
    AIGW_BIND = "aigw_bind"
    COMPONENT_CATEGORIES = "component-categories"
    APP_CONFIG = "app-config"
