from typing import Optional, List, Union, Dict, Any
from pydantic import BaseModel, ConfigDict, Field, AliasChoices
from datetime import datetime


class LLMModelBase(BaseModel):
    modelId: Optional[str] = None
    alias: Optional[str] = None
    fee: Dict[str, Any]
    name: str
    providerName: str = Field(validation_alias="providerName")
    providerKind: str = Field(validation_alias="providerKind")
    createdAt: datetime
    updatedAt: datetime
    description: Optional[str] = None
    enable: bool = True
    tag: Optional[List[str]] = None
    url: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    type: str = "llm"
    context: int = 1
    speed: int = 1
    performance: int = 1


class LLMModelCreate(BaseModel):
    modelId: Optional[str] = None
    alias: Optional[str] = None
    fee: Dict[str, Any]
    name: str
    providerName: str = Field(validation_alias="providerName")
    providerKind: str = Field(validation_alias="providerKind")
    description: Optional[str] = None
    enable: bool = True
    tag: Optional[List[str]] = None
    url: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    type: str = "llm"
    context: int = 1
    speed: int = 1
    performance: int = 1


class LLMModelsCreate(BaseModel):
    values: List[LLMModelCreate]


class LLMModelUpdate(BaseModel):
    alias: Optional[str] = None
    fee: Optional[Dict[str, Any]] = None
    name: Optional[str] = None
    providerName: Optional[str] = None
    providerKind: Optional[str] = None
    description: Optional[str] = None
    enable: Optional[bool] = None
    tag: Optional[List[str]] = None
    url: Optional[str] = None
    config: Optional[Dict[str, Any]] = None
    context: Optional[int] = None
    speed: Optional[int] = None
    performance: Optional[int] = None


class LLMModel(LLMModelBase):
    model_config = ConfigDict(from_attributes=True)
    id: str
