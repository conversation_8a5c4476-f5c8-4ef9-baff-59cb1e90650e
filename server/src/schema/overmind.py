from typing import List, Optional
from pydantic import BaseModel


class CreateApplicationRequest(BaseModel):
    app_id: int
    app_name: str
    admins: List[str]
    git_url: str
    priority: Optional[int] = None
    module_id: Optional[int] = None


class UpdateApplicationRequest(BaseModel):
    app_id: int
    app_name: Optional[str] = None
    admins: Optional[List[str]] = None
    git_url: Optional[str] = None
    priority: Optional[int] = None
    module_id: Optional[int] = None


class CreateSprintNameRequest(BaseModel):
    product_id: int
    domain_name: Optional[str] = None
    env_type: int
    offset: int
    limit: int


class IssueSearchRequest(BaseModel):
    assignee: str
    title: str


class DeployResultRequest(BaseModel):
    status: int
    app_id: int
    env_type: str
    version_snapshot_id: int


class ApproveCheckRequest(BaseModel):
    appName: str
    action: str
    appType: str
    operator: str
