from enum import Enum
from token import OP
from typing import List, Literal, Optional, Union, Any, Dict
from pydantic import BaseModel, ConfigDict, Field, model_validator
from datetime import datetime


from .component import ComponentConfigInput, ComponentConfigItem, ComponentConfigOutput, ComponentConfigVar, ComponentType

from .plugin import PluginDetail
from .knowledge import KnowledgeConfigDetail, KnowledgeDetail

from ..misc.errors import ParameterInvalid
from .common import Creater, Updater


class AppType(str, Enum):
    COMPLETE = 'agent-completion'
    CONVERSATION = 'agent-conversation'
    AGENTWORKFLOW = 'agent-workflow'
    VIRTUALHUMAN = 'virtual-human'
    WORKFLOW = 'workflow'
    EVALUATOR = 'evaluator'
    STORYLINE = 'story-line'


class AppBase(BaseModel):
    name: str = Field(min_length=1, max_length=255)
    description: Optional[str] = Field(default=None,
                                       min_length=0,
                                       max_length=255)


class PromptParamType(str, Enum):
    model_config = ConfigDict(from_attributes=True)

    LIST = 'list'
    INPUT = 'input'
    AREA = 'area'
    DATE = 'date'
    NUMBER = 'number'
    BOOLEAN = 'boolean'
    FILE = 'file'


class PromptParamListItem(BaseModel):
    value: str


class PromptParamList(BaseModel):
    options: List[PromptParamListItem]


class PromptParamInput(BaseModel):
    length: int = Field(default=48, ge=1, le=256)


class PromptParams(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    type: PromptParamType
    config: Optional[Union[PromptParamList, PromptParamInput]] = None
    key: str
    title: str
    default_val: Optional[str] = Field(default='')
    required: Optional[bool] = Field(default=True)

    @model_validator(mode='after')
    def validate_type(self) -> 'PromptParams':
        if self.type == PromptParamType.LIST \
                and not isinstance(self.config, PromptParamList):
            raise ParameterInvalid('a list is required when type is list')
        if self.type == PromptParamType.INPUT \
                and not isinstance(self.config, PromptParamInput):
            self.config = PromptParamInput()
        return self


class Retry(BaseModel):
    models: List[dict] = Field(default_factory=list)
    retryConfig: dict = Field(default_factory=dict)


class Model(BaseModel):
    modelName: Optional[str]
    modelParams: Optional[dict]
    providerKind: Optional[str]
    ratio: Optional[float]


class ModelConfig(BaseModel):
    models: List[Model] = Field(default_factory=list)
    retryConfig: dict = Field(default_factory=dict)


class AgentConfig(BaseModel):
    prePrompt: Optional[str] = Field(default=None, alias="prePrompt")
    paramsInPrompt: Optional[List[PromptParams]] = Field(
        default=None, alias="paramsInPrompt")
    prologue: Optional[str] = Field(default=None)
    tools: Optional[Dict[str, Optional[PluginDetail]]
                    ] = Field(default=None)  # for agent tools
    knowledge: Optional[KnowledgeDetail] = Field(
        default=None, alias="knowledge")  # for agent tools
    knowledgeConfig: Optional[KnowledgeConfigDetail] = Field(
        default=None, alias="knowledgeConfig")  # for agent tools
    modelName: Optional[str] = Field(default=None, alias="modelName")
    modelParams: Optional[dict] = Field(default=None, alias="modelParams")
    modelsConfig: Optional[ModelConfig] = Field(
        default=None, alias="modelsConfig")
    providerKind: Optional[str] = Field(default=None, alias="providerKind")


class WorkflowConfigParam(BaseModel):
    name: str
    type: str
    asDefault: Optional[bool] = Field(default=None)
    permanent: Optional[bool] = Field(default=None)
    title: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None,
                                       max_length=255)
    value: Optional[Any] = Field(default=None)


class WorkflowConfig(BaseModel):
    version: str = Field(min_length=1, max_length=36, alias="version")
    workflow_id: Optional[str] = Field(default=None, min_length=0, max_length=36,
                                       validation_alias='workflowId',
                                       serialization_alias="workflowId")
    workflow_engine_id: Optional[str] = Field(default=None, min_length=0, max_length=64,
                                              validation_alias='workflowEngineId',
                                              serialization_alias="workflowEngineId")
    workflow_engine_name: Optional[str] = Field(default=None, min_length=0, max_length=64,
                                                validation_alias='workflowEngineName',
                                                serialization_alias="workflowEngineName")
    inputs: Optional[List[WorkflowConfigParam]] = Field(
        default=None, alias="inputs")
    outputs: Optional[List[WorkflowConfigParam]] = Field(
        default=None, alias="outputs")
    nodes: Optional[List[dict]] = Field(default=None, alias="nodes")
    edges: Optional[List[dict]] = Field(default=None, alias="edges")


class AgentWorkflowConfigEdge(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    source: str
    target: str
    source_port: str = Field(alias="sourcePort")
    target_port: str = Field(alias="targetPort")
    source_port_id: str = Field(alias="sourcePortId")
    target_port_id: str = Field(alias="targetPortId")


class AgentWorkflowConfigInput(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    type: str
    title: Optional[str] = Field(default=None)


class AgentWorkflowConfigVar(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    enum: Optional[List[str]] = Field(default=None)
    name: str
    type: str
    value: Optional[str] = Field(default=None)
    title: Optional[str] = Field(default=None)


class AgentWorkflowConfigItem(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    type: str
    value: str


class AgentWorkflowConfigOutput(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    type: str
    title: Optional[str] = Field(default=None)


# class AgentWorkflowConfigNode(BaseModel):
#     id: str
#     code: str
#     no: Optional[str] = Field(default=None)
#     icon: str
#     name: str
#     type: str
#     vars: List[AgentWorkflowConfigVar] = Field(default=[])
#     width: Optional[int] = Field(default=None)
#     height: Optional[int] = Field(default=None)
#     inputs: List[AgentWorkflowConfigInput]
#     category: str
#     is_async: bool = Field(default=False, alias='async')
#     configs: List[AgentWorkflowConfigItem] = Field(default=[])
#     outputs: List[AgentWorkflowConfigOutput]
#     render_key: str = Field(alias="renderKey")
#     component_id: str = Field(alias="componentId")
#     description: Optional[str] = Field(default=None)

class AgentWorkflowNode(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    component_id: str = Field(validation_alias="componentId")
    code: str
    name: str
    type: ComponentType
    vars: Optional[List[ComponentConfigVar]] = Field(default=None)
    inputs: Optional[List[ComponentConfigInput]] = Field(default=None)
    outputs: Optional[List[ComponentConfigOutput]] = Field(default=None)
    configs: Optional[List[ComponentConfigItem]] = Field(default=None)
    description_for_llm: Optional[str] = Field(default=None)
    description: str

    def __hash__(self) -> int:
        return id.__hash__()

    def __eq__(self, value: object) -> bool:
        if not isinstance(value, AgentWorkflowNode):
            return False
        return self.id == value.id


class AgentWorkflowConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    workflow_id: str = Field(alias="workflowId")
    edges: List[AgentWorkflowConfigEdge] = Field()
    nodes: List[AgentWorkflowNode] = Field()
    # agent workflow 中的 inputs 和 outputs 应该只有一个
    inputs: List[AgentWorkflowConfigInput] = Field(default=[], min_length=1)
    outputs: List[AgentWorkflowConfigOutput] = Field(
        default=[], max_length=1, min_length=1)


class GroupAppCreate(AppBase):
    type: AppType
    sub_type: Optional[str] = Field(
        default=None, validation_alias="subType")
    config: Optional[dict] = None
    query_sample: Optional[str] = Field(
        default=None, validation_alias="querySample")
    is_basic: Optional[bool] = False
    is_template: Optional[bool] = Field(
        default=False, validation_alias='isTemplate')
    username: Optional[str] = Field(default='')
    extInfo: Optional[dict] = Field(default=None)


class AppCreate(AppBase):
    type: AppType
    sub_type: Optional[str] = Field(
        default=None, validation_alias="subType")
    config: Optional[dict] = None
    query_sample: Optional[str] = Field(
        default=None, validation_alias="querySample")
    is_template: Optional[bool] = Field(
        default=False, validation_alias='isTemplate')
    is_basic: Optional[bool] = False
    is_template: Optional[bool] = False
    extInfo: Optional[dict] = Field(default=None)


class AppUpdate(AppBase):
    name: Optional[str] = Field(default=None,
                                min_length=1,
                                max_length=255)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=255)
    query_sample: Optional[str] = Field(default=None,
                                        validation_alias="querySample")
    group_id: Optional[str] = Field(
        default=None, validation_alias='groupID')
    is_template: Optional[bool] = Field(
        default=False, validation_alias='isTemplate')
    config: Optional[dict] = None
    type: Optional[AppType] = Field(default=None)
    extInfo: Optional[dict] = Field(
        default=None, validation_alias="extInfo")


class AppDeploy(AppBase):
    name: Optional[str] = Field(default=None,
                                min_length=1,
                                max_length=255)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=255)
    config: Optional[dict]


class AppDebug(AppDeploy):
    name: Optional[str] = Field(default=None,
                                min_length=1,
                                max_length=255)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=255)
    config: Optional[dict]


class AppGet(AppBase, Updater, Creater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    groupID: str = Field(validation_alias="group_id")
    query_sample: Optional[str] = Field(default=None,
                                        serialization_alias="querySample")
    type: AppType
    app_config_id: Optional[str] = None
    config: Optional[dict] = None
    is_template: Optional[bool] = Field(
        default=None, serialization_alias='isTemplate')
    extInfo: Optional[dict] = Field(
        default=None, serialization_alias='extInfo')
    starred: Optional[bool] = Field(default=False)
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')


class AppCreated(AppBase, Updater, Creater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    sub_type: Optional[str] = Field(
        default=None, serialization_alias="subType")
    type: AppType
    config: Optional[dict] = None
    query_sample: Optional[str] = Field(default=None,
                                        serialization_alias="querySample")
    extInfo: Optional[dict] = Field(
        default=None, serialization_alias='extInfo')
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')


class AppList(AppBase, Updater, Creater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    sub_type: Optional[str] = Field(
        default=None, serialization_alias="subType")
    type: AppType
    extInfo: Optional[dict] = Field(
        default=None, serialization_alias='extInfo')
    is_template: Optional[bool] = Field(
        default=None, serialization_alias='isTemplate')
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')
    starred: Optional[bool] = Field(default=None)


class AppDeployStatus(int, Enum):
    UNKNOW = 2
    SUCCESS = 0
    FAILED = 1


class AppTemplateList(AppBase, Updater, Creater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    type: AppType
    config: dict
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')


class ConfigType(str, Enum):
    SNAPSHOT = 'snapshot'
    HISTORY = 'history'


class SaveSettingConfig(AppBase):
    settingId: str = Field(default=None,
                           min_length=1,
                           max_length=255)
    appId: str = Field(default=None,
                       min_length=1,
                       max_length=255)
    type: Optional[ConfigType] = Field(default=None)
    name: str = Field(default=None,
                      min_length=1,
                      max_length=255)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=255)
    config: dict


class SaveSettingRequest(AppBase):
    appId: str = Field(default=None,
                       min_length=1,
                       max_length=255)
    name: str = Field(default=None,
                      min_length=1,
                      max_length=255)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=255)
    extInfo: Optional[str] = Field(default=None,
                                   min_length=1,
                                   max_length=255)
    config: Optional[dict] = None


class UpdateSettingRequest(AppBase):
    appId: str = Field(default=None,
                       min_length=1,
                       max_length=255)
    settingId: str = Field(default=None,
                           min_length=1,
                           max_length=255)
    name: Optional[str] = Field(default=None,
                                min_length=1,
                                max_length=255)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=255)
    extInfo: Optional[str] = Field(default=None,
                                   min_length=1,
                                   max_length=255)


class DeleteSettingRequest(BaseModel):
    appId: str = Field(default=None)
    settingId: str = Field(default=None)


class ConfigBase(BaseModel):
    config: dict
    status: int = Field(default=AppDeployStatus.UNKNOW)
    message: Optional[str] = Field(default=None)
    settingId: Optional[str] = Field(default=None)


class SnapshotConfig(ConfigBase):
    pass


class CopyConfig(BaseModel):
    type: Literal['rollback', 'switch']


class ConfigCreate(ConfigBase):
    model_config = ConfigDict(from_attributes=True)
    settingId: str = ''
    type: ConfigType


class ConfigUpdate(BaseModel):
    status: Optional[int] = Field(default=None)
    message: Optional[str] = Field(default=None)


class ConfigList(ConfigBase, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str = Field(validation_alias="id")
    sha: str = Field(validation_alias="hash")
    created_at: datetime = Field(serialization_alias='createdAt')


class ConfigGet(ConfigBase, Updater):
    model_config = ConfigDict(from_attributes=True)
    id: str = Field(validation_alias="id")
    sha: str = Field(validation_alias="hash")
    config: dict
    created_at: datetime = Field(serialization_alias='createdAt')


class WorkflowCallback(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    type: str = Field(default=None)
    info: dict = Field(default=None)


class BizInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    business: str = Field(default=None)
    scene: str = Field(default=None)


class AppVersionInfo(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    version_id: str = Field(default=None)


class AppTriggerRequest(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    app_id: Optional[str] = Field(default=None, min_length=1, max_length=36,
                                  validation_alias='appID',
                                  serialization_alias='appID')
    inputs: Optional[dict] = Field(default=None, alias="inputs")
    callback: Optional[WorkflowCallback] = Field(default=None)
    search_key: Optional[str] = Field(default=None,
                                      validation_alias='searchKey',
                                      serialization_alias='searchKey')
    biz_context: str = Field(default=None,
                             validation_alias='bizContext',
                             serialization_alias='bizContext')

    biz_info: Optional[BizInfo] = Field(default=None,
                                        validation_alias='bizInfo',
                                        serialization_alias='bizInfo')

    debug: bool = Field(default=False)


class AppTriggerResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    app_id: Optional[str] = Field(default=None, min_length=1, max_length=36,
                                  validation_alias='appID',
                                  serialization_alias='appID')
    run_id: str = Field(min_length=1,
                        validation_alias='runID',
                        serialization_alias='runID')
    outputs: Optional[dict] = Field(default=None)


class AppWorkflowRunList(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    flow_id: str = Field(min_length=1,
                         validation_alias='flowId',
                         serialization_alias='flowId')
    version: str = Field(min_length=1)
    run_id: str = Field(min_length=1,
                        validation_alias='runId',
                        serialization_alias='runId')
    create_time: int = Field(validation_alias='createTime',
                             serialization_alias='createTime')
    start_time: int = Field(validation_alias='startTime',
                            serialization_alias='startTime')
    end_time: int = Field(validation_alias='endTime',
                          serialization_alias='endTime')
    status: str = Field(min_length=1)
    message: Optional[str] = Field(default=None)
    node: Optional[dict] = Field(default=None)
    inputs: Optional[dict] = Field(default=None)
    outputs: Optional[dict] = Field(default=None)


class AppWorkflowRunNodeStatus(BaseModel):
    flow_id: Optional[str] = Field(default=None,
                                   validation_alias='flowId',
                                   serialization_alias='flowId')
    version: Optional[str] = Field(default=None)
    run_id: Optional[str] = Field(default=None,
                                  validation_alias='runId',
                                  serialization_alias='runId')
    node_id: Optional[str] = Field(default=None,
                                   validation_alias='nodeId',
                                   serialization_alias='nodeId')
    total: Optional[int] = Field(default=None)  # foreach total
    index: Optional[int] = Field(default=None)  # foreach index
    start_time: int = Field(validation_alias='startTime',
                            serialization_alias='startTime')
    end_time: int = Field(validation_alias='endTime',
                          serialization_alias='endTime')
    status: Optional[str] = Field(default=None)
    message: Optional[str] = Field(default=None)
    count: Optional[int] = Field(default=None)
    inputs: Optional[dict] = Field(default=None)
    outputs: Optional[dict] = Field(default=None)


class AppWorkflowRunDetailGet(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    flow_id: Optional[str] = Field(default=None,
                                   validation_alias='flowId',
                                   serialization_alias='flowId')
    version: Optional[str] = Field(default=None)
    run_id: Optional[str] = Field(default=None,
                                  validation_alias='runId',
                                  serialization_alias='runId')
    create_time: int = Field(validation_alias='createTime',
                             serialization_alias='createTime')
    start_time: int = Field(validation_alias='startTime',
                            serialization_alias='startTime')
    end_time: int = Field(validation_alias='endTime',
                          serialization_alias='endTime')
    status: Optional[str] = Field(default=None)
    message: Optional[str] = Field(default=None)

    inputs: Optional[dict] = Field(default=None)
    outputs: Optional[dict] = Field(default=None)

    nodes: List[AppWorkflowRunNodeStatus]


class AppUploadfileResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    key: str = Field(min_length=1,
                     validation_alias='key',
                     serialization_alias='key')
    uri: str = Field(min_length=1)


class VersionedAppConfigComponentsCreate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    app_id: str = Field()
    app_config_id: str
    component_id: str


class AppLockResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    username: Optional[str] = Field(default=None)


class AgentWorkflowRunBase(BaseModel):
    chat_id: Optional[str] = Field(default=None, validation_alias='chatID')
    is_stream: Optional[bool] = Field(default=False)
    inputs: dict = Field(default=None)


class AgentWorkflowRun(AgentWorkflowRunBase):
    pass


class AgentWorkflowDebug(AgentWorkflowRunBase):
    config_id: Optional[str] = Field(default=None, validation_alias='configID')
