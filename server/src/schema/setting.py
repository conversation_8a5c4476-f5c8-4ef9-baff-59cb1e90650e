from pydantic import AliasChoices, BaseModel, Field
from typing import List, Optional
from enum import Enum


class Setting(BaseModel):
    id: str = Field(min_length=1, max_length=36)
    workspaceId: Optional[str] = Field(default=None, min_length=1, max_length=36,
                                       validation_alias=AliasChoices(
                                           'workspace_id', 'workspaceId'),
                                       serialization_alias='workspaceId')
    groupId: Optional[str] = Field(default=None, min_length=1, max_length=36,
                                   validation_alias=AliasChoices(
                                       'group_id', 'groupId'),
                                   serialization_alias='groupId')
    name: Optional[str] = Field(default=None, min_length=1, max_length=128)
    desc: Optional[str] = Field(default=None, min_length=0, max_length=256)
    avatarUrl: Optional[str] = None


class SettingConfig(BaseModel):
    settingId: str = Field(min_length=1, max_length=36)
    config: dict
    name: Optional[str] = Field(default=None, min_length=1, max_length=128)
    desc: Optional[str] = Field(default=None, min_length=0, max_length=256)
    extInfo: Optional[dict] = Field(default=None)
