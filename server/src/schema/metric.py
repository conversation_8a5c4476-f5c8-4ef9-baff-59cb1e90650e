from datetime import date
from decimal import Decimal
from typing import Optional, List
from pydantic import BaseModel, ConfigDict


class ConversationCount(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    count: float
    date: date


class Metric(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    app_id: str
    total_tokens: int
    users: int
    messages: int
    conversations: int
    token_per_message: int
    token_per_second: int
    model_name: Optional[str] = ''
    time: Optional[int]
    fee: Optional[Decimal]
    date: str | date
    extra: Optional[dict]


class SimpleMetric(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    # config: dict
    name: Optional[str]
    app_id: str
    group_id: Optional[str]
    workspace_id: Optional[str]
    fee: Optional[Decimal]
    messages: int
    tokens: int


class MessagePerConv(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    count: float
    date: date


class UserUsageCount(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    count: int
    date: date


class AverageInteractionCount(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    count: int
    date: date


class TokensPerSeconds(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    count: float
    date: date


class TokenCount(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    count: int
    date: date


class MetricItem(BaseModel):
    name: str
    fee: float
    totalTokens: int
    modelName: str
    messages: int
    totalTime: int
    demo_url: str
    users: Optional[int] = None
    conversations: Optional[int] = None


class MetricRequest(BaseModel):
    items: List[MetricItem]
