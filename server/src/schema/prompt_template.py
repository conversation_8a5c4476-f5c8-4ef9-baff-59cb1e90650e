from pydantic import AliasChoices, BaseModel, Field
from typing import List, Optional
from enum import Enum
from ..schema.app import AppType


class PromptItemType(Enum):
    ROLE = "role"
    BG = "bg"
    SKILL = "skill"
    TASK = "task"
    REQUIRE = "require"
    OUTPUT_FORMAT = "output_format"
    CUSTOM = "custom"


class PromptType(Enum):
    STRUCT = "struct"
    RAW = "raw"


class ScopeType(Enum):
    SCOPED = "scoped"
    WORKSPACE = "workspace"


class StructPromptItem(BaseModel):
    title: str
    type: PromptItemType
    content: str


class PromptTemplateModel(BaseModel):
    id: int
    name: str
    avatar_url: str
    desc: Optional[str] = None
    prompt: str
    structPrompt: Optional[List[StructPromptItem]] = None
    prompt_type: PromptType
    app_type: Optional[AppType] = None

    class Config:
        orm_mode = True


class PromptTemplateBase(BaseModel):
    name: str
    avatar_url: str
    desc: Optional[str] = None
    workspace_id: str
    group_id: Optional[str] = None
    prompt: str
    prompt_type: PromptType
    app_type: Optional[AppType] = None


class PromptTemplateDeleteRequest(BaseModel):
    id: str = Field(min_length=1, max_length=36)


class PromptTemplateCreateRequest(BaseModel):
    groupId: str = Field(min_length=1, max_length=36,
                         validation_alias=AliasChoices(
                             'group_id', 'groupId'),
                         serialization_alias='groupId')
    workspaceId: str = Field(min_length=1, max_length=36,
                             validation_alias=AliasChoices(
                                 'workspace_id', 'workspaceId'),
                             serialization_alias='workspaceId')
    name: str = Field(min_length=1, max_length=128)
    scope: ScopeType = ScopeType.SCOPED
    desc: Optional[str] = Field(default=None, min_length=0, max_length=256)
    avatarUrl: str
    promptType: PromptType
    appType: Optional[AppType] = None
    prompt: str
    structPrompt: Optional[List[StructPromptItem]] = None


class PromptTemplateUpdateRequest(BaseModel):
    id: str = Field(min_length=1, max_length=36)
    workspaceId: Optional[str] = Field(default=None, min_length=1, max_length=36,
                                       validation_alias=AliasChoices(
                                           'workspace_id', 'workspaceId'),
                                       serialization_alias='workspaceId')
    groupId: Optional[str] = Field(default=None, min_length=1, max_length=36,
                                   validation_alias=AliasChoices(
                                       'group_id', 'groupId'),
                                   serialization_alias='groupId')
    name: Optional[str] = Field(default=None, min_length=1, max_length=128)
    desc: Optional[str] = Field(default=None, min_length=0, max_length=256)
    avatarUrl: Optional[str] = None
    promptType: Optional[PromptType] = None
    appType: Optional[AppType] = None
    prompt: Optional[str] = None
    structPrompt: Optional[List[StructPromptItem]] = None


class PromptTemplateQueryRequest(BaseModel):
    groupId: str = Field(min_length=1, max_length=36,
                         validation_alias=AliasChoices(
                             'group_id', 'groupId'),
                         serialization_alias='groupId')
    workspaceId: str = Field(min_length=1, max_length=36,
                             validation_alias=AliasChoices(
                                 'workspace_id', 'workspaceId'),
                             serialization_alias='workspaceId')


class PromptTemplateCreate(PromptTemplateBase):
    struct_prompt: Optional[List[StructPromptItem]] = None


class PromptTemplateUpdate(PromptTemplateBase):
    struct_prompt: Optional[List[StructPromptItem]] = None


class PromptTemplateInDBBase(PromptTemplateBase):
    id: int

    class Config:
        orm_mode = True


class PromptTemplate(PromptTemplateInDBBase):
    pass


class PromptTemplateInDB(PromptTemplateInDBBase):
    created_by: str
    updated_by: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
