from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union
from click import File
from pydantic import BaseModel, ConfigDict, Field

from .common import Creater, Updater

from ..schema.app import AgentConfig


class ImageUrl(BaseModel):
    url: str


class AudioUrl(BaseModel):
    url: str


class VideoUrl(BaseModel):
    url: str


class NewChatCompletionContentPartImageParam(BaseModel):
    image_url: Optional[ImageUrl] = Field(default=None)

    type: Literal["image_url"]


class NewChatCompletionContentPartAudioParam(BaseModel):
    audio_url: Optional[AudioUrl] = Field(default=None)

    type: Literal["audio_url"]


class NewChatCompletionContentPartVideoParam(BaseModel):
    video_url: Optional[VideoUrl] = Field(default=None)

    type: Literal["video_url"]


class ChatCompletionContentPartImageParam(BaseModel):
    image_url: Optional[str] = Field(default=None)

    type: Literal["image_url"]


class ChatCompletionContentPartTextParam(BaseModel):
    text: Optional[str] = Field(default=None)

    type: Literal["text"]


ChatCompletionContentPartParam = Union[ChatCompletionContentPartTextParam,
                                       ChatCompletionContentPartImageParam]

NewChatCompletionContentPartParam = Union[ChatCompletionContentPartTextParam,
                                          NewChatCompletionContentPartAudioParam,
                                          NewChatCompletionContentPartVideoParam,
                                          NewChatCompletionContentPartImageParam]


class LLMMessage(BaseModel):
    role: Literal['user', "system"]
    content: Union[str, List[ChatCompletionContentPartParam],
                   List[NewChatCompletionContentPartParam], None] = Field(default='')


class ToolLLMMessage(LLMMessage):
    role: Literal['tool'] = Field(default='tool')
    tool_call_id: str


class AILLMMessage(LLMMessage):
    role: Literal['assistant'] = Field(default='assistant')
    name: Optional[str] = Field(default=None)
    tool_calls: Optional[List[dict]] = Field(default=None)


class ResponseMode(str, Enum):
    JSON = 'json'
    STREAMING = 'streaming'


class CompletionRequest(BaseModel):
    config: Optional[AgentConfig] = Field(default=None)

    # depressed，将在后续版本移除，请使用 images 参数或imageUrl参数（服务端是这个结构)
    image_url: Optional[str] = None
    images: Optional[list[str]] = None
    imageUrl: Optional[str] = None
    audioUrl: Optional[str] = None
    videoUrl: Optional[str] = None
    parameters: Optional[dict]


class ConversationRequest(BaseModel):
    config: Optional[AgentConfig] = Field(default=None)

    message: str
    image_url: Optional[str] = None  # depressed，将在后续版本移除，请使用 images 参数
    images: Optional[list[str]] = None
    imageUrl: Optional[str] = None
    parameters: Optional[dict] = None


class Response(BaseModel):
    content: str = Field(default='')
    prompt_tokens: int
    response_tokens: int
    total_tokens: int
    time_comsumed: float
    is_tool_calls: bool = Field(default=False)
    tool_calls: Optional[List[Any]] = Field(default=None)


class ResponseChunk(BaseModel):
    content: Optional[str] = Field(default=None)
    reasoning_content: Optional[str] = Field(default=None)
    references: Optional[List[dict]] = Field(default=None)
    prompt_tokens: Optional[int] = Field(default=None)
    response_tokens: Optional[int] = Field(default=None)
    total_tokens: Optional[int] = Field(default=None)
    time_comsumed: Optional[float] = Field(default=None)
    is_tool_calls: bool = Field(default=False)
    tool_calls: Optional[List[Any]] = Field(default=None)


class MessageResponseType(str, Enum):
    TEXT = 'text'
    TOOL_CALLS = 'tool_calls'


class MessageUpdate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    response: Optional[List[Dict[str, Any]] | str] = Field(default=None)
    response_type: Optional[MessageResponseType] = Field(default=None)
    tool_call_response_ids: Optional[List[str]] = Field(default=None)

    prompt_tokens: Optional[int] = Field(default=None)
    response_tokens: Optional[int] = Field(default=None)
    time_comsumption_in_ms: Optional[int] = Field(
        default=None)
    total_tokens: Optional[int] = Field(default=None)


class MessageCreate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    app_id: Optional[str] = Field(default=None)
    conversation_id: Optional[str] = Field(default=None)
    query: str

    response: Optional[List[Dict[str, Any]] | str] = Field(default=None)
    response_type: MessageResponseType = Field(
        default=MessageResponseType.TEXT)
    tool_call_response_ids: Optional[List[str]] = Field(default=None)
    kind: Optional[str] = Field(default='')
    model_name: Optional[str] = Field(default='')
    prompt_tokens: int = Field(default=0)
    response_tokens: int = Field(default=0)
    total_tokens: int = Field(default=0)
    fee: Optional[float] = Field(default=0)
    time_comsumption_in_ms: int = Field(
        default=0)


class MessageToolCallResponseCreate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    message_id: str
    tool_call_id: str
    response: str


class ConversationGet(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    id: str
    app_config_id: str
    override_config: Optional[AgentConfig] = Field(default=None)
    parameters: Optional[dict] = Field(default=None)


class ConversationCreate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    app_id: str
    app_config_id: str
    override_config: Optional[AgentConfig] = Field(default=None)
    parameters: Optional[dict] = Field(default=None)


class ConversationUpdate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: Optional[str] = None
    override_config: Optional[AgentConfig] = Field(
        default=None, validation_alias='overrideConfig')
    parameters: Optional[dict] = Field(default=None)


class ConversationInnerUpdate(ConversationUpdate):
    model_config = ConfigDict(from_attributes=True)

    prompt_tokens_for_name: Optional[int] = None
    response_tokens_for_name: Optional[int] = None
    total_tokens_for_name: Optional[int] = None


class ToolCallInMessage(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    message_id: str
    arguments: Optional[str]
    response: str


class MessageWithToolCalls(BaseModel):
    message_tool_calls: Optional[List[ToolCallInMessage]] = Field(
        default=None, alias='toolCalls')
    tool_calls: Optional[List[dict]] = Field(
        default=None, exclude=True)
    tool_responses: Optional[Dict[str, str]] = Field(
        default=None, exclude=True)


class CompletionChunk(MessageWithToolCalls):
    model_config = ConfigDict(from_attributes=True)

    content: Optional[str] = None
    reasoning_content: Optional[str] = Field(default=None)
    references: Optional[List[dict]] = Field(default=None)
    messageID: str


class CompletionResp(MessageWithToolCalls):
    model_config = ConfigDict(from_attributes=True)

    content: Optional[str]
    messageID: str


class ConversationChunk(MessageWithToolCalls):
    model_config = ConfigDict(from_attributes=True)

    content: Optional[str]
    reasoning_content: Optional[str] = Field(default=None)
    references: Optional[List[dict]] = Field(default=None)
    messageID: str
    conversationID: str


class ConversationResp(MessageWithToolCalls):
    model_config = ConfigDict(from_attributes=True)

    content: Optional[str] = Field(default=None)
    messageID: str
    conversationID: str
    total_tokens: Optional[int] = Field(default=0)


class ConversationMessage(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str = Field(default=None, serialization_alias='messageID')
    query: str
    message_tool_calls: Optional[List[ToolCallInMessage]] = Field(
        default=None, serialization_alias='toolCalls')
    response_type: MessageResponseType = Field(exclude=True)
    response: Optional[List[Dict[str, Any]] | str] = Field(
        serialization_alias='response', default=None)
    conversation_id: str = \
        Field(default=None, serialization_alias='conversationID')
    time_comsumption_in_ms: Optional[int] = Field(default=None)
    total_tokens: Optional[int] = Field(default=None)


class MessageItem(BaseModel, Updater, Creater):
    model_config = ConfigDict(from_attributes=True)

    id: str = Field(default=None, serialization_alias='messageID')
    query: str
    kind: Optional[str] = Field(default=None)
    model_name: Optional[str] = Field(
        default=None, serialization_alias='modelName')
    fee: Optional[float] = Field(default=None)
    total_tokens: Optional[int] = Field(default=None)
    time_comsumption_in_ms: Optional[int] = Field(default=None)
    tool_call_response_ids: Optional[str] = Field(default=None)
    message_tool_calls: Optional[List[ToolCallInMessage]] = Field(
        default=None, serialization_alias='toolCalls')
    response_type: MessageResponseType = Field(exclude=True)
    response: Optional[List[Dict[str, Any]] | str] = Field(
        serialization_alias='response', default=None)
    conversation_id: Optional[str] = \
        Field(default=None, serialization_alias='conversationID')
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')
