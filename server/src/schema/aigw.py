from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict

from .common import AigwResourceType


class AigwAppCreate(BaseModel):
    app_code: str = Field(..., max_length=36)
    name: str = Field(..., max_length=36)
    resource_type: Optional[AigwResourceType] = Field(
        AigwResourceType.WORKSPACE, validation_alias='resourceType')
    resource_id: Optional[str] = Field(
        None, max_length=36, validation_alias='resourceId')


class AigwAppBind(BaseModel):
    app_code: str = Field(..., max_length=36)
    token: str = Field()
    resource_type: Optional[AigwResourceType] = Field(
        AigwResourceType.WORKSPACE, validation_alias='resourceType')
    resource_id: Optional[str] = Field(
        None, max_length=36, validation_alias='resourceId')


class AigwAppUpdate(BaseModel):
    app_code: Optional[str] = Field(None, max_length=36)
    quota: Optional[str] = Field(None, max_length=10)
    token: Optional[str] = Field(None, max_length=36)


class AigwAppResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: Optional[str] = Field(None, max_length=36)
    app_code: str
    resource_type: AigwResourceType
    resource_id: str
    quota: str
    token: str
    extra: Optional[dict] = None
