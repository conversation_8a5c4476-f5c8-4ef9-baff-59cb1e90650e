from typing import Optional
from pydantic import BaseModel, ConfigDict, Field
from datetime import datetime

from .common import Creater, Updater


class WorkspaceBase(BaseModel):
    name: str = Field(min_length=1, max_length=128)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=256)


class WorkspaceList(BaseModel):
    source: Optional[str] = Field(default='mine')


class WorkspaceCreate(WorkspaceBase):
    pass


class WorkspaceUpdate(BaseModel):
    name: Optional[str] = Field(default=None,
                                min_length=1, max_length=128)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=256)


class WorkspaceUpdateCreator(BaseModel):
    mail: str = Field(default=None,
                      min_length=1, max_length=128)


class Workspace(WorkspaceBase, Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')
