from typing import Optional
from pydantic import BaseModel, ConfigDict, Field
from datetime import datetime

from .member import GroupRole

from .common import Creater, Updater


class GroupBase(BaseModel):
    name: str = Field(min_length=1, max_length=128)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=256)


class GroupCreate(GroupBase):
    pass


class GroupUpdate(BaseModel):
    name: Optional[str] = Field(default=None,
                                min_length=1,
                                max_length=128)
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=256)


class Group(GroupBase, Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')


class GroupWithRole(Group):
    role: Optional[GroupRole] = Field(serialization_alias='role')
