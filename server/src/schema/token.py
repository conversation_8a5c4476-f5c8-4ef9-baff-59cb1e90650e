from datetime import datetime
from typing import Optional, Union

from pydantic import BaseModel, ConfigDict, Field

from .rbac import Resource
from .common import Creater


class TokenGet(BaseModel, Creater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    token: str = Field(validation_alias='value')
    last_used_at: Optional[datetime] = \
        Field(default=None, serialization_alias='lastUsedAt')
    created_at: datetime = Field(serialization_alias='createdAt')


class TokenCreate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    resources: Optional[list[Resource]] = Field(validation_alias='resources')


class TokenUpdate(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    resources: Optional[list[Resource]] = Field(validation_alias='resources')


class TokenRelatedResourceGet(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    token_id: str = Field(validation_alias='token_id',
                          serialization_alias='tokenID')
    resource_type: str = Field(validation_alias='resource_type',
                               serialization_alias='resourceType')
    resource_id: str = Field(validation_alias='resource_id',
                             serialization_alias='resourceID')
    created_at: datetime = Field(serialization_alias='createdAt')


class Payload(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    app_id: str
    exp: datetime


class AuthenticatedJWTPayload(Payload):
    model_config = ConfigDict(from_attributes=True)

    user_id: str
    is_admin: bool
    _logined: bool = True


TEMPORARY_USER_PREFIX = 'temp'


def is_temporary_user(user_id: str):
    return user_id.startswith(TEMPORARY_USER_PREFIX)


class AnonymousJWTPayload(Payload):
    model_config = ConfigDict(from_attributes=True)

    user_id: str
    _anonymous: bool = True


class JWTToken(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    token: str
    expired_at: datetime = Field(serialization_alias='expiredAt')


class TwoKindPayload(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    data: Union[AnonymousJWTPayload, AuthenticatedJWTPayload]
