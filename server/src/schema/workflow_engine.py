from typing import Optional

from pydantic import BaseModel, Field, AliasChoices


class WorkflowEngine(BaseModel):
    id: str
    workspaceId: str = Field(min_length=1, max_length=36,
                             validation_alias=AliasChoices('workspaceId', 'workspaceID'),
                             serialization_alias='workspaceID')
    name: str
    endpoint: str
    token: Optional[str] = Field(default=None)
    description: str
