from typing import Optional
from pydantic import BaseModel, ConfigDict, Field


class PluginDetail(BaseModel):
    description: Optional[str] = Field(
        default=None,
        serialization_alias='description_for_llm')
    parameters: dict = Field(default={})


class Plugin(PluginDetail):
    model_config = ConfigDict(from_attributes=True)

    # meta
    workspace_id: Optional[str] = None

    id: str  # uuid
    name: str
