from typing import Optional, List
from pydantic import BaseModel, ConfigDict, Field, AliasChoices
from datetime import datetime

from .common import Creater, Updater
from .document import DocumentDetail


class KnowledgeBase(BaseModel):
    name: str = Field(min_length=1, max_length=128)
    group_id: str = Field(min_length=1, max_length=36,
                          validation_alias=AliasChoices(
                              'group_id', 'groupId'),
                          serialization_alias='groupId')
    workspace_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices(
                                  'workspace_id', 'workspaceId'),
                              serialization_alias='workspaceId')
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=256)


class KnowledgeCreate(KnowledgeBase):
    collection_id: Optional[int] = Field(default=None,
                                         validation_alias=AliasChoices(
                                             'collection_id', 'collectionId'))


class KnowledgeCreateBatchItem(BaseModel):
    collection_id: Optional[int] = Field(default=None,
                                         validation_alias=AliasChoices(
                                             'collection_id', 'id'))
    name: Optional[str] = Field(default=None,
                                validation_alias=AliasChoices(
                                    'name', 'name'))


class KnowledgeCreateBatch(BaseModel):
    group_id: str = Field(min_length=1, max_length=36,
                          validation_alias=AliasChoices(
                              'group_id', 'groupId'),
                          serialization_alias='groupId')
    workspace_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices(
                                  'workspace_id', 'workspaceId'),
                              serialization_alias='workspaceId')
    items: List[KnowledgeCreateBatchItem]


class KnowledgeUpdate(BaseModel):
    name: Optional[str] = Field(default=None,
                                min_length=1,
                                max_length=128)
    group_id: str = Field(min_length=1, max_length=36,
                          validation_alias=AliasChoices(
                              'group_id', 'groupId'),
                          serialization_alias='groupId')
    workspace_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices(
                                  'workspace_id', 'workspaceId'),
                              serialization_alias='workspaceId')
    description: Optional[str] = Field(default=None,
                                       min_length=1,
                                       max_length=256)


class Knowledge(KnowledgeBase, Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    documentCount: Optional[int]
    collectionId: Optional[int] = Field(validation_alias=AliasChoices(
        'collection_id', 'collectionId'))
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')


class KnowledgeDetail(KnowledgeBase):
    id: str
    collectionId: Optional[int] = Field(validation_alias=AliasChoices(
        'collection_id', 'collectionId'))
    docs: Optional[List[DocumentDetail]] = Field(default=None)
    documentCount: Optional[int]


class KnowledgeConfig(BaseModel):
    knowledgeId: int
    knowledgeName: str
    knowledgeItemIds: Optional[List[int]] = Field(default=None)
    type: str
    useAll: bool


class KnowledgeConfigDetail(BaseModel):
    searchConfig: Optional[dict] = Field(default=None)
    knowledge: Optional[List[KnowledgeConfig]] = Field(default=None)
