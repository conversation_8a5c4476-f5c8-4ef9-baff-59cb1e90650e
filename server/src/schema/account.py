from typing import Optional
from fastapi import Request
from pydantic import BaseModel, ConfigDict, Field
from datetime import datetime


class AccountBase(BaseModel):
    name: str = Field(min_length=1, max_length=128)
    fullname: str = Field(min_length=1, max_length=128)
    email: str = Field(min_length=1, max_length=128)


class AccountCreate(AccountBase):
    pass


class Account(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    name: str
    fullname: str
    email: str
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')


class AccountWithIsAdmin(Account):
    is_admin: bool = Field(serialization_alias='isAdmin')


OUTTER_USER_PREFIX = 'outter'


def is_outter_user(user: str):
    return user.startswith(OUTTER_USER_PREFIX)


def user_from_outter(user: Optional[str] = None):
    if user is None:
        return None
    return f'{OUTTER_USER_PREFIX}:{user}'


def user_id_from_request(request: Request) -> str:
    if request.user.is_authenticated:
        return request.user.id
    return ''
