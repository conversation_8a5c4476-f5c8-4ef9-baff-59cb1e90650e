from enum import Enum
from typing import Optional, Union
from pydantic import BaseModel, Field

from .component import ComponentType


class AWRunningStatus(str, Enum):
    RUNNING = 'running'
    FINISHED = 'finished'
    FAILED = 'failed'


class AWHistoryCreate(BaseModel):
    app_id: str
    app_config_id: str
    inputs: dict
    status: Optional[AWRunningStatus] = Field(default=None)


class AWHistoryUpdate(BaseModel):
    status: Optional[AWRunningStatus] = Field(default=None)
    run_count: Optional[int] = Field(default=None)


class AWComponentRunningStatus(str, Enum):
    PENDING = 'pending'
    RUNNING = 'running'
    FINISHED = 'finished'
    FAILED = 'failed'


class AWComponentLLMRunningResult(BaseModel):
    conversation_id: str


class AWComponentHTTPRunningResult(BaseModel):
    response: str
    status_code: int


class ComponentScriptRunningResult(BaseModel):
    output: str


class ComponentStatus(BaseModel):
    id: str
    chat_id: str
    idx: int
    component_type: str
    status: AWComponentRunningStatus
    result: Optional[Union[AWComponentHTTPRunningResult,
                           ComponentScriptRunningResult, AWComponentLLMRunningResult]]


class AWComponentStatusCreate(BaseModel):
    component_id: str
    component_type: ComponentType
    idx: int
    status: AWComponentRunningStatus
    result: Optional[Union[AWComponentHTTPRunningResult,
                           ComponentScriptRunningResult, AWComponentLLMRunningResult]]


class AWComponentStatusUpdate(BaseModel):
    status: Optional[AWComponentRunningStatus] = Field(default=None)
    result: Optional[Union[AWComponentHTTPRunningResult, ComponentScriptRunningResult, AWComponentLLMRunningResult]] = \
        Field(default=None)
