from enum import Enum
from typing import Any, Literal, Optional, List
from pydantic import BaseModel, ConfigDict, Field, AliasChoices
from datetime import datetime

from .common import Creater, Updater


class ComponentType(Enum):
    INPUT = "INPUT"
    OUTPUT = "OUTPUT"
    SCRIPT = "SCRIPT"
    SWITCH = "SWITCH"
    SELECT = "SELECT"
    FOREACH = "FOREACH"
    HTTP = "HTTP"
    LLM_PLUGIN = "LLM-PLUGIN"
    DISPLAY = "DISPLAY"
    LLM = "LLM"


class ComponentAppType(str, Enum):
    WORKFLOW = "workflow"
    AGENT = "agent"
    AGENT_WORKFLOW = "agentWorkflow"


class ComponentBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str = Field(min_length=1, max_length=128)
    code: str = Field(min_length=1, max_length=128)
    description: Optional[str] = Field(
        default=None, min_length=1, max_length=256)
    type: str = Field(min_length=1, max_length=64)
    category_id: str = Field(min_length=1, max_length=36,
                             validation_alias=AliasChoices(
                                 'category_id', 'categoryID'),
                             serialization_alias='categoryID')
    category_name: Optional[str] = Field(min_length=1, max_length=128,
                                         validation_alias=AliasChoices(
                                             'category_name', 'category'),
                                         serialization_alias='category')
    workspace_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices(
                                  'workspace_id', 'workspaceID'),
                              serialization_alias='workspaceID')
    group_id: Optional[str] = Field(min_length=0, max_length=36,
                                    validation_alias=AliasChoices(
                                        'group_id', 'groupID'),
                                    serialization_alias='groupID')
    scope: str = Field(min_length=1, max_length=20)
    config: dict = Field()
    deprecated: bool = Field(default=False)
    app_types: Optional[List[str]] = Field(default=None,
                                           validation_alias=AliasChoices(
                                               'appTypes', 'app_types'),
                                           serialization_alias='appTypes')


class EnvPair(BaseModel):
    name: str
    param: str


class ComponentCreate(ComponentBase):
    model_config = ConfigDict(from_attributes=True)

    envs: Optional[List[EnvPair]] = Field(default=None)


class ComponentUpdate(BaseModel):
    name: Optional[str] = Field(min_length=1, max_length=128)
    code: Optional[str] = Field(min_length=1, max_length=128)
    description: Optional[str] = Field(
        default=None, min_length=1, max_length=256)
    type: Optional[str] = Field(min_length=1, max_length=64)
    category_id: Optional[str] = Field(min_length=1, max_length=36,
                                       validation_alias=AliasChoices(
                                           'category_id', 'categoryID'),
                                       serialization_alias='categoryID')
    category_name: Optional[str] = Field(min_length=1, max_length=128,
                                         validation_alias=AliasChoices(
                                             'category_name', 'category'),
                                         serialization_alias='category')
    scope: Optional[str] = Field(min_length=1, max_length=20)
    group_id: str = Field(min_length=1, max_length=36,
                          validation_alias=AliasChoices(
                              'group_id', 'groupID'),
                          serialization_alias='groupID')
    config: dict = Field()
    deprecated: Optional[bool] = Field(default=False)
    app_types: Optional[List[str]] = Field(default=None,
                                           validation_alias=AliasChoices(
                                               'appTypes', 'app_types'),
                                           serialization_alias='appTypes')
    envs: Optional[List[EnvPair]] = Field(default=None)


class ComponentGet(ComponentBase, Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')


class ComponentConfigVar(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    type: Optional[str] = None
    title: Optional[str] = None
    value: Optional[Any] = None
    enum: Optional[List[str | dict]] = None
    description: Optional[str] = None


class ComponentConfigInput(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: Optional[str] = None
    name: str
    type: str
    title: Optional[str] = None
    description: Optional[str] = None


class ComponentConfigItem(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    value: Optional[Any] = None


class ComponentConfigHTTPURL(ComponentConfigItem):
    name: Literal['url']


class ComponentConfigHTTPMethod(ComponentConfigItem):
    name: Literal['method']


class ComponentConfigHTTPTimeout(ComponentConfigItem):
    name: Literal['timeout']


class ComponentConfigOutput(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: Optional[str] = None
    name: str
    type: str
    title: Optional[str] = Field(default=None)


class ComponentConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: str
    code: str
    name: str
    type: ComponentType
    vars: Optional[List[ComponentConfigVar]] = Field(default=None)
    inputs: Optional[List[ComponentConfigInput]] = Field(default=None)
    outputs: Optional[List[ComponentConfigOutput]] = Field(default=None)
    configs: List[ComponentConfigHTTPURL | ComponentConfigHTTPMethod |  # noqa
                  ComponentConfigHTTPTimeout | ComponentConfigItem]
    app_types: List[ComponentAppType] = Field(
        validation_alias=AliasChoices('appTypes', 'app_types'))
    description_for_llm: Optional[str] = Field(default=None)
    description: str
