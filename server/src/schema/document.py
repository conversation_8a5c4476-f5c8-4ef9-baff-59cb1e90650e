from typing import Optional, List, Any
from pydantic import BaseModel, ConfigDict, Field, AliasChoices
from datetime import datetime

from .common import Creater, Updater


class DocumentBase(BaseModel):
    knowledge_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices(
                                  'knowledge_id', 'knowledgeId'),
                              serialization_alias='knowledgeId')
    title: str = Field(min_length=1, max_length=128)
    type: Optional[str] = Field(default=None,
                                max_length=128)
    tag_name: Optional[str] = Field(default=None,
                                    max_length=128,
                                    validation_alias=AliasChoices(
                                        'tag_name', 'tagName'))
    source: Optional[str] = Field(default=None,
                                  max_length=128)
    config: Optional[dict] = Field(default=None)
    doc_id: Optional[int] = Field(default=None)
    state: Optional[int] = Field(default=None)


class DocumentCreate(DocumentBase):
    pass


class DocumentDetail(BaseModel):
    id: str
    title: Optional[str] = Field(default=None,
                                 max_length=128)
    type: Optional[str] = Field(default=None,
                                max_length=128)
    source: Optional[str] = Field(default=None,
                                  max_length=128)
    doc_id: Optional[int] = Field(default=None)
    state: Optional[int] = Field(default=None)


class DocumentUpdate(BaseModel):
    title: Optional[str] = Field(default=None,
                                 max_length=128)
    type: Optional[str] = Field(default=None,
                                max_length=128)
    tag_name: Optional[str] = Field(default=None,
                                    max_length=128)
    source: Optional[str] = Field(default=None,
                                  max_length=128)
    config: Optional[dict] = Field(default=None)
    doc_id: Optional[int] = Field(default=None)
    state: Optional[int] = Field(default=None)


class Document(DocumentBase, Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')
    segment: Optional[dict] = Field(default=None)


class RecallResult(BaseModel):
    knowledgeItemId: int
    fragmentId: str
    serialNum: int
    content: str
    extData: Any
    knowledgeItemMetaData: Any


class RecallResponse(BaseModel):
    type: Any
    results: List[RecallResult]
    knowledgeConfig: Any
