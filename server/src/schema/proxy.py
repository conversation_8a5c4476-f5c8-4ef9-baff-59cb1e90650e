from enum import Enum
from typing import Optional, List, Union, Dict, Any
from pydantic import BaseModel, ConfigDict, Field


class PmsResourceTypeInfoVO(BaseModel):
    id: Optional[int] = Field(default=None,
                              validation_alias='id',
                              serialization_alias="ID")
    label: Optional[str] = Field(default=None)
    value: Optional[str] = Field(default=None)


class CIOPmsBusinessInfoVO(BaseModel):
    label: Optional[str] = Field(default=None)
    value: Optional[str] = Field(default=None)
    children: Optional[List[PmsResourceTypeInfoVO]] = Field(default=None)


class CIOResourceTypes(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    lesseeId: Optional[int] = Field(default=None,
                                    validation_alias='lesseeId',
                                    serialization_alias='lesseeID')
    config: Optional[List[CIOPmsBusinessInfoVO]] = Field(default=None)


class CIOPoolVO(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: Optional[int] = Field(default=None,
                              validation_alias='id',
                              serialization_alias="ID")
    business: Optional[str] = Field(default=None)
    pool_code: Optional[str] = Field(default=None,
                                     validation_alias='poolCode',
                                     serialization_alias="poolCode")
    resource_type: Optional[str] = Field(default=None, validation_alias='resourceType',
                                         serialization_alias="resourceType")
    name: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)
    create_time: Optional[int] = Field(default=None,
                                       validation_alias='createTime',
                                       serialization_alias="createTime")
    update_time: Optional[int] = Field(default=None,
                                       validation_alias='updateTime',
                                       serialization_alias="updateTime")
    creator: Optional[str] = Field(default=None)
    updater: Optional[str] = Field(default=None)
    resource_count: Optional[int] = Field(default=None,
                                          validation_alias='resourceCount',
                                          serialization_alias="resourceCount")
    pool_type: Optional[str] = Field(default=None,
                                     validation_alias='poolType',
                                     serialization_alias="poolType")


class CIOPoolConfig(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    business: Optional[str] = Field(default=None)
    resource_type: Optional[str] = Field(default=None, validation_alias='resourceType',
                                         serialization_alias="resourceType")
    pool_code: Optional[str] = Field(default=None,
                                     validation_alias='poolCode',
                                     serialization_alias="poolCode")
    config_type: Optional[str] = Field(default=None,
                                       validation_alias='configType',
                                       serialization_alias="configType")
    config: Optional[str] = Field(default=None)
    check_result: Optional[str] = Field(default=None,
                                        validation_alias='checkResult',
                                        serialization_alias="checkResult")


class ProxyChatRequest(BaseModel):
    user_id: str = Field(default="langbase",
                         validation_alias='userID',
                         serialization_alias='userID')

    model_name: Optional[str] = Field(
        default="gpt-3.5-turbo-1106", alias="modelName")
    model_params: Optional[dict] = Field(default=None, alias="modelParams")
    model_provider: Optional[str] = Field(
        default="openai", alias="modelProvider")

    async_api: Optional[bool] = Field(default=False, alias="asyncApi")

    system: Optional[str] = Field(default=None)  # system
    message: str                                 # user

    images: Optional[List[str]] = Field(default=None, alias="images")

    temperature: Optional[float] = Field(default=None)
    max_tokens: Optional[int] = Field(default=None)
    stop: Union[Optional[str], List[str], None] = Field(default=None)
    top_p: Optional[float] = Field(default=None)
    frequency_penalty: Optional[float] = Field(default=None)
    presence_penalty: Optional[float] = Field(default=None)

    api_key: Optional[str] = Field(default=None, alias="apiKey")
    api_base: Optional[str] = Field(default=None, alias="apiBase")


class ProxyChatResponse(BaseModel):
    content: Optional[str]


class ProxyImageResponseFormat(str, Enum):
    URL = 'url'
    B64JSON = 'b64_json'


class ProxyGenSingleImageRequest(BaseModel):
    user_id: str = Field(default="langbase",
                         validation_alias='userID',
                         serialization_alias='userID')
    prompt: str
    model_name: Optional[str] = Field(default="dall-e-3", alias="modelName")
    model_provider: Optional[str] = Field(
        default="openai", alias="modelProvider")
    quality: Optional[str] = Field(default="standard")
    size: Optional[str] = Field(default="1024x1024")
    style: Optional[str] = Field(default="vivid")
    api_key: Optional[str] = Field(default=None, alias="apiKey")
    api_base: Optional[str] = Field(default=None, alias="apiBase")


class ProxyImageRequest(ProxyGenSingleImageRequest):
    n: Optional[int] = Field(default=1)


class ProxyImageItem(BaseModel):
    url: Optional[str]
    revised_prompt: Optional[str]


class ProxyImageResponse(BaseModel):
    created: int
    images: Optional[List[ProxyImageItem]] = None


class ProxySingleImageResponse(BaseModel):
    created: int
    url: Optional[str] = None


class ProxyCIOPoolResourceContentRequest(BaseModel):
    business: Optional[str] = Field(default=None)
    resource_type: Optional[str] = Field(default=None, alias="resourceType")
    pool_code: Optional[str] = Field(default=None, alias="poolCode")
    resource_id: Optional[str] = Field(default=None, alias="resourceId")
    query_code: Optional[List[str]] = Field(default=None, alias="queryCode")
    operator: Optional[str] = Field(default=None)


class ProxyMJSubmitImagineRequest(BaseModel):
    timeout: Optional[int] = Field(default=600000)    # ms

    mj_app_id: str = Field(alias="appId")
    mj_app_key: str = Field(alias="appKey")

    async_api: bool = Field(default=True, alias="asyncApi")

    images: Optional[List[str]] = Field(default=None, alias="images")
    content: Optional[str] = None
    notify_hook: Optional[str] = Field(default=None, alias="notifyHook")
    prompt: str
    state: Optional[str] = Field(default=None)


class ProxyMJSubmitImagineResponse(BaseModel):
    image: Optional[str] = None


class ProxyMJSubmitBlendRequest(BaseModel):
    timeout: Optional[int] = Field(default=600000)    # ms

    mj_app_id: str = Field(alias="appId")
    mj_app_key: str = Field(alias="appKey")

    async_api: bool = Field(default=True, alias="asyncApi")

    images: Optional[List[str]] = Field(default=None, alias="images")
    content: Optional[str] = None
    dimensions: Optional[str] = Field(default='LANDSCAPE')
    notify_hook: Optional[str] = Field(default=None, alias="notifyHook")
    state: Optional[str] = Field(default=None)


class ProxyMJSubmitBlendResponse(BaseModel):
    image: Optional[str] = None


class ProxyAirshipAlgorithmSimpleInfo(BaseModel):
    id: Optional[int] = Field(default=None)
    code: Optional[str] = Field(default=None)
    mode: Optional[int] = Field(default=None)
    algName: Optional[str] = Field(default=None)
    service_name: Optional[str] = Field(default=None, alias="serviceName")
    assigner: Optional[str] = Field(default=None)
    create_time: Optional[int] = Field(default=None, alias="createTime")
    update_time: Optional[int] = Field(default=None, alias="updateTime")
    description: Optional[str] = Field(default=None)


class AirshipDataTypeDetailDto(BaseModel):
    id: Optional[int] = Field(default=None)
    name: Optional[str] = Field(default=None)
    code: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)
    type_flag: Optional[int] = Field(default=None, alias="typeFlag")
    create_time: Optional[int] = Field(default=None, alias="createTime")
    update_time: Optional[int] = Field(default=None, alias="updateTime")
    assigner: Optional[str] = Field(default=None)


class AirshipDataField(BaseModel):
    id: Optional[int] = Field(default=None)
    field_flag: Optional[int] = Field(default=None, alias="fieldFlag")
    field_name: Optional[str] = Field(default=None, alias="fieldName")
    description: Optional[str] = Field(default=None)
    properties: Optional[str] = Field(default=None)
    create_time: Optional[int] = Field(default=None, alias="createTime")
    update_time: Optional[int] = Field(default=None, alias="updateTime")

    data_type_detail_dto: AirshipDataTypeDetailDto = Field(
        default=None, alias="dataTypeDetailDto")


class AirshipDataType(BaseModel):
    id: Optional[int] = Field(default=None)
    name: Optional[str] = Field(default=None)
    code: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)
    type_flag: Optional[int] = Field(default=None, alias="typeFlag")
    assigner: Optional[str] = Field(default=None)
    create_time: Optional[int] = Field(default=None, alias="createTime")
    update_time: Optional[int] = Field(default=None, alias="updateTime")

    data_field_dto_list: List[AirshipDataField] = Field(
        default=None, alias="dataFieldDtoList")


class AirshipDataScene(BaseModel):
    id: Optional[int] = Field(default=None)
    service_code: Optional[str] = Field(default=None, alias="serviceCode")
    title: Optional[str] = Field(default=None)
    content: Optional[str] = Field(default=None)
    icon_image: Optional[str] = Field(default=None, alias="iconImage")


class ProxyAirshipAlgorithmDetailInfo(BaseModel):
    id: Optional[int] = Field(default=None)
    code: Optional[str] = Field(default=None)
    mode: Optional[int] = Field(default=None)
    algName: Optional[str] = Field(default=None)
    service_name: Optional[str] = Field(default=None, alias="serviceName")
    assigner: Optional[str] = Field(default=None)
    create_time: Optional[int] = Field(default=None, alias="createTime")
    update_time: Optional[int] = Field(default=None, alias="updateTime")
    description: Optional[str] = Field(default=None)

    tags: Optional[List[int]] = Field(default=None)
    cpu: Optional[int] = Field(default=None)
    memory: Optional[int] = Field(default=None)
    concurrency: Optional[int] = Field(default=None)

    sample_scenes: Optional[List[AirshipDataScene]] = Field(
        default=None, alias="sampleScenes")
    request_param: Optional[AirshipDataType] = Field(
        default=None, alias="requestParam")
    response_param: Optional[AirshipDataType] = Field(
        default=None, alias="responseParam")


class AioCallbackResult(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    success: bool = Field(default=True)
    output_data: Optional[dict] = Field(default=None, alias="outputData"),
    fail_reason: Optional[str] = Field(default=None, alias="failReason")
    code: Optional[str] = Field(default="SUCCESS")
    workflow_context: Optional[str] = Field(
        default=None, alias="workflowContext")


class ProxyDMTextToImageRequest(BaseModel):
    timeout: Optional[int] = Field(default=600000)    # ms

    alwayson_scripts: Optional[dict] = Field(default=None)
    asset_id: Optional[str] = Field(default=None)
    batch_size: Optional[float] = Field(default=1)
    cfg_scale: Optional[float] = Field(default=7)
    denoising_strength: Optional[float] = Field(default=0.7)
    enable_hr: Optional[bool] = Field(default=False)
    hr_resize_x: Optional[int] = Field(default=0)
    hr_resize_y: Optional[int] = Field(default=0)
    hr_scale: Optional[float] = Field(default=1)
    hr_second_pass_steps: Optional[int] = Field(default=0)
    hr_upscaler: Optional[str] = Field(default="Latent")
    image_classification_id: Optional[str] = Field(default=None)
    model_name: Optional[str] = Field(default="Counterfeit-V2.5")
    n_iter: Optional[int] = Field(default=1)
    negative_prompt: Optional[str] = Field(default=None)
    prompt: Optional[str] = Field(default=None)
    override_settings: Optional[dict] = Field(default=None)
    restore_faces: Optional[bool] = Field(default=False)
    sampler_name: Optional[str] = Field(default="DPM++ 2S a Karras")
    script_args: Optional[str] = Field(default=None)
    script_name: Optional[str] = Field(default=None)
    seed: Optional[int] = Field(default=-1)
    seed_resize_from_h: Optional[int] = Field(default=0)
    seed_resize_from_w: Optional[int] = Field(default=0)
    steps: Optional[int] = Field(default=20)
    tiling: Optional[bool] = Field(default=False)
    vae: Optional[str] = Field(default="Automatic")
    width: Optional[int] = Field(default=512)
    height: Optional[int] = Field(default=512)


class ProxyDMTextToImageResponse(BaseModel):
    task_id: Optional[str] = Field(default=None)


class ProxyDMImageToImageRequest(BaseModel):
    timeout: Optional[int] = Field(default=600000)    # ms

    alwayson_scripts: Optional[dict] = Field(default=None)
    asset_id: Optional[str] = Field(default=None)
    batch_size: Optional[float] = Field(default=1)
    cfg_scale: Optional[float] = Field(default=7)
    denoising_strength: Optional[float] = Field(default=0.75)
    image_cfg_scale: Optional[float] = Field(default=1.5)
    image_classification_id: Optional[str] = Field(default=None)
    image_mask: Optional[str] = Field(default=None)
    init_images: List[str] = Field(default=None)
    inpaint_full_res: Optional[int] = Field(default=0)
    inpaint_full_res_padding: Optional[int] = Field(default=32)
    inpainting_fill: Optional[int] = Field(default=1)
    inpainting_mask_invert: Optional[int] = Field(default=0)
    mask: Optional[str] = Field(default=None)
    mask_alpha: Optional[int] = Field(default=0)
    mask_blur: Optional[int] = Field(default=4)
    model_name: Optional[str] = Field(default="Anything-V3.0")
    n_iter: Optional[int] = Field(default=1)
    override_settings: Optional[dict] = Field(default=None)
    negative_prompt: Optional[str] = Field(default=None)
    prompt: Optional[str] = Field(default=None)
    resize_mode: Optional[int] = Field(default=1)
    restore_faces: Optional[bool] = Field(default=False)
    restore_faces: Optional[bool] = Field(default=False)
    sampler_name: Optional[str] = Field(default="DPM++ 2S a Karras")
    script_args: Optional[str] = Field(default=None)
    script_name: Optional[str] = Field(default=None)
    seed: Optional[int] = Field(default=-1)
    seed_resize_from_h: Optional[int] = Field(default=0)
    seed_resize_from_w: Optional[int] = Field(default=0)
    steps: Optional[int] = Field(default=20)
    tiling: Optional[bool] = Field(default=False)
    vae: Optional[str] = Field(default="Automatic")
    width: Optional[int] = Field(default=512)
    height: Optional[int] = Field(default=512)


class ProxyDMImageToImageResponse(BaseModel):
    task_id: Optional[str] = Field(default=None)


class ProxyDMTaskResponse(BaseModel):
    current_image: Optional[str] = Field(default=None)
    eta_relative: Optional[float] = Field(default=None)
    progress: Optional[float] = Field(default=None)
    result: Optional[str] = Field(default=None)
    state: Optional[dict] = Field(default=None)
    status: Optional[str] = Field(default=None)
    task_type: Optional[str] = Field(default=None)
    textinfo: Optional[str] = Field(default=None)


class ProxyDMModel(BaseModel):
    id: Optional[str] = Field(
        default=None, validation_alias="_id", serialization_alias="id")
    real_model_name: Optional[str] = Field(default=None)
    description: Optional[str] = Field(default=None)
    cover: Optional[str] = Field(default=None)
    network_type: Optional[str] = Field(default=None)
    encrypted: Optional[bool] = Field(default=None)
    create_time: Optional[int] = Field(default=None)
    update_time: Optional[int] = Field(default=None)
    group_name: Optional[str] = Field(default=None)
    virtual_model_name: Optional[str] = Field(default=None)
    group_id: Optional[str] = Field(default=None)


class ProxyDMPBRemRequest(BaseModel):
    cascadePSP_enabled: Optional[bool] = Field(default=False)
    img: Optional[str] = Field(default=None)
    model_name: Optional[str] = Field(default="sam_vit_h_4b8939.pth")
    query: Optional[str] = Field(default=None)  # 提示词(Segmentation Prompt)
    sa_enabled: Optional[bool] = Field(default=False)


class ProxyPBRemResponse(BaseModel):
    image: Optional[str] = Field(default=None)
    mask: Optional[str] = Field(default=None)


ProxyPropertySaveRequest = Dict[str, Any]

ProxySettingPublishSaveRequest = Dict[str, Any]

ProxyPropertyListRequest = Dict[str, Any]

ProxySystemVariablesRequest = Dict[str, Any]

ProxyPythonScriptRequest = Dict[str, Any]


ProxyPythonScriptResponse = Dict[str, Any]


ProxyScriptRequest = Dict[str, Any]


ProxyScriptResponse = Dict[str, Any]
