from enum import Enum
from typing import Optional, Dict, Any, List, Literal
from pydantic import BaseModel, Field


class ApproveStatus(int, Enum):
    ERROR = -1
    START = 0
    COMPLETE = 100
    WITHDRAW = 20


class BizVariables(BaseModel):
    dynamicAssign: dict


class ProcVariables(BaseModel):
    noDeployWindow: bool
    customAdmit: bool


class MyField(BaseModel):
    fieldChineseName: str
    fieldValue: str


class FormContent(BaseModel):
    app: str
    setting: str
    channel: str
    deployReason: str
    mainApprover: str
    reason: str
    procTitle: str
    procPriority: Literal[10, 20, 30]  # 流程优先级(10:高 20:中 30:低)
    cardCustomField: Optional[List[MyField]] = Field(default=[])


class CreateApproveRequest(BaseModel):
    operator: Optional[str] = Field(default='')
    priority: Literal[10, 20, 30]  # 10, 20, 30
    procVariables: ProcVariables
    bizVariables: BizVariables
    formContent: FormContent


class ResetApproveRequest(BaseModel):
    sprint_id: str


class WithdrawApproveRequest(BaseModel):
    approve_id: str
    reason: str


class ApproveDetailRequest(BaseModel):
    proc_inst_serials: List[str]


class ApproveResponse(BaseModel):
    proc_inst_serial: str
    proc_no: str
    proc_inst_id: str
    status: int
