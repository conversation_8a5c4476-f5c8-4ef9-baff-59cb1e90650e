from enum import Enum
from typing import Optional
from pydantic import BaseModel, ConfigDict, Field, AliasChoices
from datetime import datetime

from .common import Creater, Updater


class EnvVariableScopeType(Enum):
    COMPONENT = 'component'
    WORKSPACE = 'workspace'
    GROUP = 'group'
    APP = 'app'


class EnvVariableBase(BaseModel):
    name: str = Field(min_length=1, max_length=128)
    value: Optional[str] = Field(default=None, max_length=1024)
    component_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices('component_id', 'componentID'),
                              serialization_alias='componentID')
    param: Optional[str] = Field(default=None, min_length=1, max_length=128)
    scope_type: str = Field(min_length=1, max_length=64,
                            validation_alias=AliasChoices('scope_type', 'scopeType'),
                            serialization_alias='scopeType')
    scope_id: str = Field(min_length=1, max_length=36,
                          validation_alias=AliasChoices('scope_id', 'scopeID'),
                          serialization_alias='scopeID')


class EnvVariableCreate(BaseModel):
    name: str = Field(min_length=1, max_length=128)
    value: Optional[str] = Field(default=None, min_length=1, max_length=1024)
    component_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices('component_id', 'componentID'),
                              serialization_alias='componentID')
    param: Optional[str] = Field(default=None, min_length=1, max_length=128)


class EnvVariableUpdate(BaseModel):
    value: Optional[str] = Field(default=None, min_length=1, max_length=1024)


class EnvVariableGet(EnvVariableBase, Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    scope_type: str = Field(min_length=1, max_length=64,
                            validation_alias=AliasChoices('scope_type', 'scopeType'),
                            serialization_alias='scopeType')
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')
