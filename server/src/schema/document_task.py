from typing import Optional
from pydantic import BaseModel, ConfigDict, Field, AliasChoices
from datetime import datetime

from .common import Creater, Updater


class DocumentTaskBase(BaseModel):
    knowledge_id: str = Field(min_length=1, max_length=36,
                              validation_alias=AliasChoices(
                                  'knowledge_id', 'knowledgeId'),
                              serialization_alias='knowledgeId')
    title: Optional[str] = Field(default=None, min_length=0, max_length=128)
    type: Optional[str] = Field(default=None,
                                min_length=0,
                                max_length=128)
    tag_name: Optional[str] = Field(default=None,
                                    min_length=0,
                                    max_length=128)
    source: Optional[str] = Field(default=None,
                                  min_length=1,
                                  max_length=128)
    config: Optional[dict] = None
    task_id: Optional[str] = Field(default=None,
                                   min_length=1,
                                   max_length=128)
    task_state: Optional[int] = Field(default=None)


class DocumentTaskCreate(DocumentTaskBase):
    pass


class DocumentTaskUpdate(BaseModel):
    title: Optional[str] = Field(default=None,
                                 min_length=0,
                                 max_length=128)
    type: Optional[str] = Field(default=None,
                                min_length=0,
                                max_length=128)
    tag_name: Optional[str] = Field(default=None,
                                    min_length=0,
                                    max_length=128)
    source: Optional[str] = Field(default=None,
                                  min_length=1,
                                  max_length=128)
    config: Optional[dict] = None
    task_id: Optional[str] = Field(default=None,
                                   min_length=1,
                                   max_length=128)
    task_state: Optional[int] = Field(default=None)


class DocumentTask(DocumentTaskBase, Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    created_at: datetime = Field(serialization_alias='createdAt')
    updated_at: datetime = Field(serialization_alias='updatedAt')
