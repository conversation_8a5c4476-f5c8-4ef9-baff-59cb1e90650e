from datetime import datetime
from enum import Enum
from pydantic import BaseModel, ConfigDict, Field, field_serializer
from typing import Any, Optional

from .common import Creater, Updater


class ModelProviderKind(Enum):
    OPENAI = "openai"
    WENXIN = "wenxin"
    MOONSHOT = "moonshot"
    DEEPSEEK = "deepseek"
    MINIMAX = "minimax"
    DOUBAO = "doubao"
    ANTHROPIC = "anthropic"
    QWEN = "qwen"
    GOOGLE = "google"
    TMAX = "tmax"
    AIGC = "aigc"
    YOUDAO = "youdao"
    ARK = "ark"
    MINIMAX_OUTER = "minimax_outer"
    ALIYUN = "aliyun"


class ModelType(Enum):
    LLM = "llm"
    EMBEDDING = "embedding"
    IMAGE = 'image'


class MultiModalType(Enum):
    IMAGE = "image"


class GlobalProvider(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    kind: ModelProviderKind
    icon: Optional[str] = None
    description: str
    bindable: Optional[bool] = True


class ModelProviderBindingBase(BaseModel):
    description: Optional[str] = None
    endpoint: Optional[str] = None
    config: Optional[Any] = None


class BindingUpdate(ModelProviderBindingBase):
    api_key: Optional[str] = Field(default=None, validation_alias="apiKey")
    workspace_id: Optional[str] = Field(
        default=None, validation_alias="workspaceId")
    pass


class BindingCreate(ModelProviderBindingBase):
    api_key: Optional[str] = Field(default=None, validation_alias="apiKey")
    workspace_id: Optional[str] = Field(
        default=None, validation_alias="workspaceId")
    kind: ModelProviderKind = Field(validation_alias="providerKind")


class BindingGet(ModelProviderBindingBase):
    model_config = ConfigDict(from_attributes=True)

    kind: str = Field(serialization_alias="providerKind")


class BindingCreated(BindingGet):
    model_config = ConfigDict(from_attributes=True)

    id: str


class BindingList(BindingGet):
    model_config = ConfigDict(from_attributes=True)

    id: str


class DefaultModelConfigBase(BaseModel):
    model_name: str = Field(serialization_alias="modelName")
    model_type: ModelType = Field(alias="modelType")
    provider_kind: ModelProviderKind = \
        Field(serialization_alias="providerKind")


class DefaultModelConfigGet(DefaultModelConfigBase,
                            Creater, Updater):
    model_config = ConfigDict(from_attributes=True)

    id: str
    model_name: str = Field(serialization_alias="modelName")
    model_type: ModelType = Field(serialization_alias="modelType")
    provider_kind: ModelProviderKind = \
        Field(serialization_alias="providerKind")

    created_at: datetime = Field(serialization_alias="createdAt")
    updated_at: datetime = Field(serialization_alias="updatedAt")


class DefaultModelConfigUpdate(DefaultModelConfigBase):
    model_name: Optional[str] = \
        Field(default=None, alias="modelName")
    provider_kind: Optional[ModelProviderKind] = \
        Field(default=None, alias="providerKind")

    @field_serializer('provider_kind')
    def serilize_model_name(self, provider_kind: ModelProviderKind, _info):
        return provider_kind.value

    @field_serializer('model_type')
    def serilize_model_type(self, model_type: ModelType, _info):
        return model_type.value


class DefaultModelGet(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    name: str
    type: ModelType


class ModelList(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    fee: dict
    url: Optional[str] = None
    model_test_result: Optional[dict] = None
    name: str
    description: str
    enable: bool
    type: ModelType
    context: int
    speed: int
    disableReason: Optional[str] = None
    alias: Optional[str] = None
    performance: int
    tag: list
    providerKind: str
    providerName: str
    config: dict
    createdAt: datetime
    updatedAt: datetime
