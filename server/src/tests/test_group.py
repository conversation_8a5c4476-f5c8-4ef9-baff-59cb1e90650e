import pytest
from ..schema.member import <PERSON><PERSON><PERSON>, MemberType, Role
from ..schema.common import ResourceType
from ..service.member import create as create_member_service

from . import test_db, user, root_user, client  # noqa


@pytest.mark.asyncio
async def test_group(test_db, user, root_user, client):  # noqa
    db = test_db
    ac = client

    cookie_name = user[0]
    cookie_val = user[1]
    normal_user_id = user[2]
    normal_user_cookie = {f"{cookie_name}": str(cookie_val)}

    root_cookie_name = root_user[0]
    root_cookie_val = root_user[1]
    root_user_cookie = {f"{root_cookie_name}": str(root_cookie_val)}

    response = await ac.post("/api/v1/workspace", json={
        "name": "test",
        "description": "test"},
        cookies=root_user_cookie)

    assert response.status_code == 201
    workspace = response.json()['data']
    workspace_id = workspace['id']

    response = await ac.post(f"/api/v1/workspace/{workspace_id}/group",
                             json={"name": "test_group",
                                   "description": "test_group"},
                             cookies=normal_user_cookie)

    assert response.status_code == 403

    response = await ac.post(f"/api/v1/workspace/{workspace_id}/group",
                             json={"name": "test_group",
                                   "description": "test_group"},
                             cookies=root_user_cookie)

    assert response.status_code == 201
    group = response.json()['data']
    group_id = group['id']

    await create_member_service(
        db,
        user_id=normal_user_id,
        resource_type=ResourceType.GROUP,
        resource_id=group_id,
        member_create=MemberCreate(
            memberType=MemberType.USER.value,
            memberID=normal_user_id,
            role=Role.DEVELOPER.value,
        ))

    response = await ac.put(f"/api/v1/group/{group_id}", json={
        "name": "test_group_edit",
    }, cookies=normal_user_cookie)

    assert response.status_code == 403

    response = await ac.delete(f"/api/v1/group/{group_id}",
                               cookies=normal_user_cookie)

    assert response.status_code == 403

    response = await ac.put(f"/api/v1/group/{group_id}", json={
        "name": "test_group_edit",
    }, cookies=root_user_cookie)

    assert response.status_code == 200
    group = response.json()['data']
    assert group['name'] == "test_group_edit"
    assert group['description'] == "test_group"

    response = await ac.put(f"/api/v1/group/{group_id}", json={},
                            cookies=root_user_cookie)

    assert response.status_code == 200
    group = response.json()['data']
    assert group['name'] == "test_group_edit"
    assert group['description'] == "test_group"

    response = await ac.put(f"/api/v1/group/{group_id}", json={
        "description": "test_group_edit",
    }, cookies=root_user_cookie)

    assert response.status_code == 200
    group = response.json()['data']
    assert group['name'] == "test_group_edit"
    assert group['description'] == "test_group_edit"

    response = await ac.get(f"/api/v1/group/{group_id}",
                            cookies=normal_user_cookie)

    assert response.status_code == 200
    group = response.json()['data']
    assert group['name'] == "test_group_edit"
    assert group['description'] == "test_group_edit"

    response = await ac.delete(f"/api/v1/group/{group_id}",
                               cookies=root_user_cookie)

    assert response.status_code == 204
