import time
from copy import deepcopy
import pytest
import pytest_asyncio
from httpx import AsyncClient

from . import client, workspace, group, \
    test_db, user, root_user, group_admin_user  # noqa
from ..config import settings
from ..workflow.workflow_client import WORKFLOW_PATH, WORKFLOW_RUN_PATH
from ..schema.app import AppType, AppDeployStatus

agent_config = {
    "prePrompt": "hello",
    "paramsInPrompt": [{
        "key": "key",
        "title": "title",
        "type": "list",
        "config": {
            "options": [{"value": "hello"}, {"value": "world"}]
        }
    }],
    "prologue": "hello",
    "modelName": "chatgpt3.5",
    "providerKind": "opanai"
}

# WorkflowConfig example
test_workflow_config_no_nodes = {
    "version": "1.0",
    "workflowId": "xx",
    "workflowEngineId": "f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea",
    "workflowEngineName": "",
    "inputs": [],
    "outputs": [],
    "nodes": [],
    "edges": []
}

test_workflow_config = {
    "version": "1.0",
    "workflowId": "TEST_WORKFLOW",
    "workflowEngineId": "f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea",
    "workflowEngineName": "aio",
    "inputs": [
        {
            "name": "input1",
            "type": "string"
        },
        {
            "name": "input2",
            "type": "number",
            "title": "Input 2",
            "description": "This is input 2"
        }
    ],
    "outputs": [
        {
            "name": "output1",
            "type": "boolean",
            "title": "Output 1"
        }
    ],
    "nodes": [
        {
            "id": "cdb3bc77",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 190,
            "label": "Switch组件",
            "icon": "icon-fork",
            "category": "logic",
            "code": "SWITCH",
            "type": "SWITCH",
            "description": "可以根据输入条件，拆分成多条分支",
            "inputs": [
                {
                    "name": "switchVal",
                    "type": "string"
                }
            ],
            "vars": [
            ],
            "outputs": [
                {
                    "name": "default",
                    "dataType": "__logic__"
                },
            ],
            "x": -170,
            "y": -270,
            "ports": {}
        },
    ],
    "edges": []
}


@pytest_asyncio.fixture(scope='function')
async def app(group, user, client):  # noqa
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'test_app',
                                     'description': 'test_app',
                                     'type': 'agent-completion',
                                     'config': agent_config,
                                 },
                                 cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201
    return response.json()['data']


@pytest.mark.asyncio
async def test_workflow_app(group, user, group_admin_user, root_user, client, requests_mock):  # noqa
    # 0. test create without config
    mock_create_success_response = {
        "code": 200,
        "message": "success",
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/create",
                       status_code=200,
                       json=mock_create_success_response)
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'test_workflow_app',
                                     'description': 'test_workflow_app',
                                     'type': AppType.WORKFLOW,
                                     'config': test_workflow_config_no_nodes,
                                 },
                                 cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201
    app_id = response.json()['data']['id']
    response = await client.get(f'/api/v1/app/{app_id}/deploy-history',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0
    # 1. test create
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'test_workflow_app-1',
                                     'description': 'test_workflow_app',
                                     'type': AppType.WORKFLOW,
                                     'config': test_workflow_config,
                                 },
                                 cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201
    app_id = response.json()['data']['id']

    response = await client.get(f'/api/v1/app/{app_id}',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    data = response.json()['data']
    assert data['config'] == test_workflow_config
    assert data['name'] == 'test_workflow_app-1'
    assert data['description'] == 'test_workflow_app'
    assert data['type'] == AppType.WORKFLOW

    response = await client.get(f'/api/v1/app/{app_id}/config-snapshot',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1

    # list history
    response = await client.get(f'/api/v1/app/{app_id}/deploy-history',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0

    # 2. test deploy workflow app
    mock_deploy_success_response = {
        "code": 200,
        "message": "success",
        "data": {
        }
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}",
                       status_code=200,
                       json=mock_deploy_success_response)
    response = await client.post(f'/api/v1/app/{app_id}/deploy',
                                 json={
                                     'name': 'test_workflow_app',
                                     'description': 'test_workflow_app 1',
                                     'config': test_workflow_config,
                                 },
                                 cookies={f"{user[0]}": root_user[1]})
    assert response.status_code == 200
    assert response.json()['data']['status'] == AppDeployStatus.SUCCESS

    response = await client.get(f'/api/v1/app/{app_id}/deploy-history',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    config_get = response.json()['data']['items'][0]
    assert config_get['status'] == AppDeployStatus.SUCCESS

    response = await client.get(f'/api/v1/app/{app_id}/config-snapshot',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 2

    # 3. test update app
    response = await client.put(f'/api/v1/app/{app_id}',
                                json={
                                    'name': 'test_workflow_app 2',
                                    'description': 'test_workflow_app 2',
                                    'type': AppType.WORKFLOW,
                                    'config': test_workflow_config,
                                },
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    app_id = response.json()['data']['id']
    assert response.status_code == 200
    response = await client.get(f'/api/v1/app/{app_id}/deploy-history',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    config_get = response.json()['data']['items'][0]
    assert config_get['status'] == AppDeployStatus.SUCCESS

    response = await client.get(f'/api/v1/app/{app_id}/config-snapshot',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 3

    response = await client.get(f'/api/v1/app/{app_id}',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    data = response.json()['data']
    assert data['config'] == test_workflow_config
    assert data['name'] == 'test_workflow_app 2'
    assert data['description'] == 'test_workflow_app 2'
    assert data['type'] == AppType.WORKFLOW

    # 4. test trigger workflow run
    mock_trigger_request = {
        "appID": app_id,
        "inputs": {
            "input1": "input1",
            "input2": 2
        },
        "callback": {

        },
        "bizContext": "business context",
    }
    flow_id = "TEST_WORKFLOW"
    flow_run_id = "run_id_1"
    mock_trigger_success_response = {
        "code": 200,
        "message": "success",
        "data": {
            "flowId": flow_id,
            "runId": flow_run_id,
            "outputs": {
                "output1": "output1",
                "output2": 3
            },
            "bizContext": "business context",
        }
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/trigger",
                       status_code=200,
                       json=mock_trigger_success_response)
    response = await client.post(f'/api/v1/app/{app_id}/trigger',
                                 json=mock_trigger_request,
                                 cookies={f"{group_admin_user[0]}": group_admin_user[1]})
    assert response.status_code == 200
    assert response.json()['data']['appID'] == app_id
    assert response.json()['data']['runID'] == flow_run_id
    assert response.json()['data']['outputs']['output1'] == "output1"
    assert response.json()['data']['outputs']['output2'] == 3

    # 5. test get workflow run detail
    mock_get_run_detail_response = {
        "code": 200,
        "message": "success",
        "data": {
            "flowId": flow_id,
            "version": "1",
            "runId": flow_run_id,
            "createTime": 1697082843,
            "startTime": 1697082847,
            "endTime": 1697082849,
            "status": "completed",
            "message": "success",
            "inputs": {
            },
            "outputs": {
            },
            "nodes": [
                {
                    "flowId": flow_id,
                    "version": "1",
                    "runId": flow_run_id,
                    "nodeId": "uuid1",
                    "startTime": 1697082847,
                    "endTime": 1697082849,
                    "status": "completed",
                    "message": "success",
                    "count": 2,
                    "inputs": {
                        "input1": "input1",
                        "input2": 2
                    },
                    "outputs": {
                        "output1": "output1",
                        "output2": 3
                    }
                },
            ]
        }
    }
    # '/api/v1/workflowengine/flows/run/detail'
    requests_mock.get(f"{settings.workflow_config.engines[0].endpoint}/"
                      f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}"
                      f"/run/detail?flowId={flow_id}&runId={flow_run_id}",
                      status_code=200,
                      json=mock_get_run_detail_response)
    response = await client.get(f'/api/v1/app/{app_id}/workflow-runs/{flow_run_id}',
                                cookies={f"{user[0]}": root_user[1]})
    assert response.status_code == 200
    assert response.json()['data']['flowId'] == flow_id
    assert response.json()['data']['version'] == "1"
    assert response.json()['data']['runId'] == flow_run_id
    assert response.json()['data']['createTime'] == 1697082843
    assert response.json()['data']['startTime'] == 1697082847
    assert response.json()['data']['endTime'] == 1697082849
    assert response.json()['data']['status'] == "completed"
    assert response.json()['data']['message'] == "success"
    assert response.json()['data']['nodes'][0]['count'] == 2

    # 6. list workflow runs test
    mock_list_run_response = {
        "code": 200,
        "message": "success",
        "data": {
            "count": 100,
            "list": [
                {
                    "flowId": flow_id + "1",
                    "version": "1.0",
                    "runId": flow_run_id,
                    "createTime": 1697082843,
                    "startTime": 1697082847,
                    "endTime": 1697082849,
                    "status": "completed",
                    "message": "success",
                },
                {
                    "flowId": flow_id + "2",
                    "version": "1.0",
                    "runId": flow_run_id,
                    "createTime": 1697082843,
                    "startTime": 1697082847,
                    "endTime": 1697082849,
                    "status": "completed",
                    "message": "success",
                }
            ]
        }
    }
    requests_mock.get(f"{settings.workflow_config.engines[0].endpoint}/"
                      f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/"
                      f"{WORKFLOW_RUN_PATH}",
                      status_code=200,
                      json=mock_list_run_response)
    response = await client.get(f'/api/v1/app/{app_id}/workflow-runs?startTime=1697082847&endTime=1697082849',
                                cookies={f"{user[0]}": root_user[1]})
    assert response.status_code == 200

    # 7. test query node status
    node_id = "uuid1"
    mock_query_node_status_response = {
        "code": 200,
        "data": {
            "outputs": {},
            "inputs": {},
            "count": 51,
            "startTime": 26,
            "runId": "zv6i0",
            "endTime": 63,
            "message": "av50s",
            "list": [
                {
                    "outputs": {},
                    "inputs": {},
                    "count": 91,
                    "startTime": 83,
                    "runId": "wgjsx",
                    "endTime": 52,
                    "message": "9di4m",
                    "list": [
                    ],
                    "flowId": "svtp5",
                    "version": "vdidv",
                    "nodeId": "0qlwt",
                    "status": "8xk0r"
                }
            ],
            "flowId": "9v3j0",
            "version": "79tzz",
            "nodeId": "32syx",
            "status": "fiar1"
        },
        "message": "afu16"
    }
    mock_url = f"{settings.workflow_config.engines[0].endpoint}/" \
               f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/" \
               f"run/node?flowId={flow_id}&runId={flow_run_id}" \
               f"&nodeId={node_id}&indexList=xxx"
    requests_mock.get(url=mock_url,
                      status_code=200,
                      json=mock_query_node_status_response)
    response = await client.get(f'/api/v1/app/{app_id}/status?runID={flow_run_id}&nodeID={node_id}&indexList=xxx',
                                cookies={f"{user[0]}": root_user[1]})
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_workflow_app_debug(group, user, root_user, client, requests_mock):  # noqa
    # 1. test create
    mock_create_success_response = {
        "code": 200,
        "message": "success",
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/create",
                       status_code=200,
                       json=mock_create_success_response)
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'test_workflow_app',
                                     'description': 'test_workflow_app',
                                     'type': AppType.WORKFLOW,
                                     'config': test_workflow_config,
                                 },
                                 cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201
    app_id = response.json()['data']['id']

    response = await client.get(f'/api/v1/app/{app_id}',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    data = response.json()['data']
    assert data['config'] == test_workflow_config
    assert data['name'] == 'test_workflow_app'
    assert data['description'] == 'test_workflow_app'
    assert data['type'] == AppType.WORKFLOW

    response = await client.get(f'/api/v1/app/{app_id}/deploy-history',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0

    response = await client.get(f'/api/v1/app/{app_id}/config-snapshot',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert response.json()['data']['items'][0]['config']['workflowId'] == test_workflow_config['workflowId']

    debug_flow_id = test_workflow_config['workflowId'] + settings.debug_workflow_id_suffix

    # 2. test debug workflow
    mock_debug_success_response = {
        "code": 200,
        "message": "success",
        "data": {
        }
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}",
                       status_code=200,
                       json=mock_debug_success_response)
    response = await client.post(f'/api/v1/app/{app_id}/debug',
                                 json={
                                     'name': 'test_workflow_app',
                                     'description': 'test_workflow_app 1',
                                     'config': test_workflow_config,
                                 },
                                 cookies={f"{user[0]}": root_user[1]})
    assert response.status_code == 200
    assert response.json()['data']['status'] == AppDeployStatus.SUCCESS

    response = await client.get(f'/api/v1/app/{app_id}/deploy-history',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0

    response = await client.get(f'/api/v1/app/{app_id}/config-snapshot',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 2

    # 3. test trigger workflow run
    mock_debug_trigger_request = {
        "appID": app_id,
        "inputs": {
            "input1": "input1",
            "input2": 2
        },
        "callback": {

        },
        "bizContext": "business context",
        "debug": True,
    }
    flow_run_id = "run_id_1"
    mock_debug_trigger_success_response = {
        "code": 200,
        "message": "success",
        "data": {
            "flowId": debug_flow_id,
            "runId": flow_run_id,
            "outputs": {
                "output1": "output1",
                "output2": 3
            }
        }
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/trigger",
                       status_code=200,
                       json=mock_debug_trigger_success_response)
    response = await client.post(f'/api/v1/app/{app_id}/trigger',
                                 json=mock_debug_trigger_request,
                                 cookies={f"{user[0]}": root_user[1]})
    assert response.status_code == 200
    assert response.json()['data']['appID'] == app_id
    assert response.json()['data']['runID'] == flow_run_id
    assert response.json()['data']['outputs']['output1'] == "output1"
    assert response.json()['data']['outputs']['output2'] == 3

    # 4. test get workflow run detail
    mock_get_run_detail_response = {
        "code": 200,
        "message": "success",
        "data": {
            "flowId": debug_flow_id,
            "version": "1",
            "runId": flow_run_id,
            "createTime": 1697082843,
            "startTime": 1697082847,
            "endTime": 1697082849,
            "status": "completed",
            "message": "success",
            "nodes": [
                {
                    "flowId": debug_flow_id,
                    "version": "1",
                    "runId": flow_run_id,
                    "nodeId": "uuid1",
                    "startTime": 1697082847,
                    "endTime": 1697082849,
                    "status": "completed",
                    "message": "success",
                    "inputs": {
                        "input1": "input1",
                        "input2": 2
                    },
                    "outputs": {
                        "output1": "output1",
                        "output2": 3
                    }
                },
            ]
        }
    }
    requests_mock.get(f"{settings.workflow_config.engines[0].endpoint}/{settings.workflow_url_prefix}/{WORKFLOW_PATH}/"
                      f"run/detail",
                      status_code=200,
                      json=mock_get_run_detail_response)
    response = await client.get(f'/api/v1/app/{app_id}/workflow-runs/{flow_run_id}?debug=true',
                                cookies={f"{user[0]}": root_user[1]})
    assert response.status_code == 200
    assert response.json()['data']['flowId'] == debug_flow_id
    assert response.json()['data']['version'] == "1"
    assert response.json()['data']['runId'] == flow_run_id
    assert response.json()['data']['createTime'] == 1697082843
    assert response.json()['data']['startTime'] == 1697082847
    assert response.json()['data']['endTime'] == 1697082849
    assert response.json()['data']['status'] == "completed"
    assert response.json()['data']['message'] == "success"


@pytest.mark.asyncio
async def test_app_lock(client: AsyncClient, workspace, group, group_admin_user, root_user): # noqa
    if settings.testing:
        return
    cookie_name = root_user[0]
    root_cookie_val = root_user[1]

    response = await client.get(f'/api/v1/workspace/{workspace["id"]}/app',
                                cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0

    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'test_app',
                                     'description': 'test_app',
                                     'type': 'agent-completion',
                                     'config': {
                                         'hello': 'world'
                                     }
                                 },
                                 cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 201
    app_id = response.json()['data']['id']
    response = await client.post(f'/api/v1/app/{app_id}/lock', cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 200
    assert response.json()['data']['username'] == 'test'

    response = await client.post(f'/api/v1/app/{app_id}/lock', cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 200
    assert response.json()['data']['username'] == 'test'

    response = await client.delete(f'/api/v1/app/{app_id}/lock', cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 200

    response = await client.post(f'/api/v1/app/{app_id}/lock', cookies={f"{group_admin_user[0]}": group_admin_user[1]})
    assert response.status_code == 200
    assert response.json()['data']['username'] == 'group_admin_test'
    response = await client.post(f'/api/v1/app/{app_id}/lock', cookies={f"{group_admin_user[0]}": group_admin_user[1]})
    assert response.status_code == 200
    assert response.json()['data']['username'] == 'group_admin_test'

    response = await client.post(f'/api/v1/app/{app_id}/lock', cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 200
    assert response.json()['data']['username'] == 'group_admin_test'

    time.sleep(10)
    response = await client.post(f'/api/v1/app/{app_id}/lock', cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 200
    assert response.json()['data']['username'] == 'test'


@pytest.mark.asyncio
async def test_list_apps_by_workspace(client: AsyncClient, workspace, group, root_user):  # noqa
    cookie_name = root_user[0]
    root_cookie_val = root_user[1]

    response = await client.get(f'/api/v1/workspace/{workspace["id"]}/app',
                                cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0

    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'test_app',
                                     'description': 'test_app',
                                     'type': 'agent-completion',
                                     'config': {
                                         'hello': 'world'
                                     }
                                 },
                                 cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 201
    data = response.json()['data']

    response = await client.get(f'/api/v1/workspace/{workspace["id"]}/app',
                                cookies={f"{cookie_name}": root_cookie_val})
    assert response.status_code == 200
    apps = response.json()['data']["items"]
    assert len(apps) == 1
    assert apps[0]['id'] == data['id']


@pytest.mark.asyncio
async def test_create_app(client: AsyncClient, group, user, test_db):  # noqa
    cookie_name = user[0]
    user_cookie_val = user[1]
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'test_app',
                                     'description': 'test_app',
                                     'type': 'agent-completion',
                                     'config': {
                                         'hello': 'world'
                                     }
                                 },
                                 cookies={f"{cookie_name}": user_cookie_val})

    assert response.status_code == 201
    assert response.json()['data']['name'] == 'test_app'


@pytest.mark.asyncio
async def test_create_app_with_invalid_input(client: AsyncClient, group, user):  # noqa
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': '',
                                     'description': 'test_app',
                                     'type': 'unknown_type',
                                     'config': {
                                         'hello': 'world'
                                     }
                                 }, cookies={f"{user[0]}": user[1]})
    assert response.status_code == 400


@pytest.mark.asyncio
async def test_create_app_with_config(client: AsyncClient, group, user, requests_mock):  # noqa
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': '',
                                     'description': 'test_app',
                                     'type': 'agent',
                                 }, cookies={f"{user[0]}": user[1]})
    assert response.status_code == 400

    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'hello',
                                     'description': 'test_app',
                                     'type': 'agent-completion',
                                     'config': {"x": ["hello", "world"]}
                                 }, cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201
    data = response.json()['data']

    response = await client.get(f'/api/v1/app/{data["id"]}',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    data = response.json()['data']
    data['config'] = {}

    mock_create_success_response = {
        "code": 200,
        "message": "success",
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/create",
                       status_code=200,
                       json=mock_create_success_response)
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'hello-1',
                                     'description': 'test_app',
                                     'type': 'workflow',
                                     'config': test_workflow_config,
                                 }, cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201

    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'hello-1',
                                     'description': 'test_app',
                                     'type': 'workflow',
                                     'config': "",
                                 }, cookies={f"{user[0]}": user[1]})
    assert response.status_code == 400

    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'hello-2',
                                     'description': 'test_app',
                                     'type': 'workflow',
                                     'config': test_workflow_config
                                 }, cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201

    response = await client.get(f'/api/v1/app/{data["id"]}',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    data = response.json()['data']
    data['config'] = agent_config


@pytest.mark.asyncio
async def test_get_app(client: AsyncClient, user, app):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]

    response = await client.get(f'/api/v1/app/{app["id"]}',
                                cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 200
    assert response.json()['data']['name'] == app['name']


@pytest.mark.asyncio
async def test_get_app_with_invalid_id(client: AsyncClient, user):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]
    response = await client.get('/api/v1/app/invalid_id', cookies={
        f"{cookie_name}": cookie_val
    })
    assert response.status_code == 404


@pytest.mark.asyncio
async def test_update_app(client: AsyncClient, user, app):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]

    response = await client.put(f'/api/v1/app/{app["id"]}', json={
        "type": "workflow",
    }, cookies={f"{cookie_name}": cookie_val})

    assert response.status_code == 200
    assert response.json()['data']['type'] == 'agent-completion'

    response = await client.put(f'/api/v1/app/{app["id"]}', json={
        'description': 'this is a test app',
    }, cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 200
    updated_app = response.json()['data']
    assert updated_app['name'] == 'test_app'
    assert updated_app['description'] == 'this is a test app'
    assert updated_app['type'] == 'agent-completion'

    c = deepcopy(agent_config)
    c['prePrompt'] = 'world'
    response = await client.put(f'/api/v1/app/{app["id"]}', json={
        'name': 'updated_app',
        "config": c,
    }, cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 200
    updated_app = response.json()['data']
    assert updated_app['name'] == 'updated_app'
    assert updated_app['description'] == 'this is a test app'
    assert updated_app['config']['prePrompt'] == c['prePrompt']
    assert updated_app['type'] == 'agent-completion'


@pytest.mark.asyncio
async def test_update_app_with_invalid_input(client: AsyncClient, user, app):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]

    response = await client.put(f'/api/v1/app/{app["id"]}', json={
        'name': '',
    }, cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 400


@pytest.mark.asyncio
async def test_delete_app(client: AsyncClient, user, app, requests_mock):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]
    mock_delete_request = {
        "flowId": "xxx",
        "operator": "xxx",
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}",
                       status_code=200,
                       json=mock_delete_request)
    response = await client.delete(f'/api/v1/app/{app["id"]}',
                                   cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 204


@pytest.mark.asyncio
async def test_list_deploy_histroy(client, user, app):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]

    response = await client.put(f'/api/v1/app/{app["id"]}', json={
        "config": agent_config,
    }, cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 200

    response = await client.get(f'/api/v1/app/{app["id"]}/deploy-history',
                                cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 200
    data = response.json()['data']
    assert len(data['items']) == 2
    assert data['items'][1]['config']['prePrompt'] == agent_config['prePrompt']


@pytest.mark.asyncio
async def test_create_app_snapshot(client: AsyncClient, user, app):  # noqa
    response = await client.post(f'/api/v1/app/{app["id"]}/config-snapshot',
                                 json={
                                     'config': agent_config,
                                 }, cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201
    assert response.json()['data']['config']['prePrompt'] \
           == agent_config['prePrompt']


@pytest.mark.asyncio
async def test_list_app_config_snapshot(client: AsyncClient, user, app):  # noqa
    response = await client.post(f'/api/v1/app/{app["id"]}/config-snapshot',
                                 json={
                                     'config': agent_config,
                                 }, cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201

    response = await client.get(f'/api/v1/app/{app["id"]}/config-snapshot',
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    assert len(response.json()['data']['items']) == 1
    assert response.json()['data']['items'][0]['config']['prePrompt'] \
           == agent_config['prePrompt']
