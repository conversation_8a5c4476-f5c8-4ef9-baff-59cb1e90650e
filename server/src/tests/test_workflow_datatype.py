import pytest

from . import user, root_user, test_db, client  # noqa


@pytest.mark.asyncio
async def test_workflow_datatype(user, root_user, client):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]
    root_cookie_val = root_user[1]
    root_id = root_user[2]
    root_username = root_user[3]

    ac = client
    # create workflow datatype with non-admin user
    response = await ac.post("/api/v1/workflow-datatypes",
                             json={
                                 "name": "test",
                                 "config": {
                                     "type": "string",
                                     "extend": "any"
                                 },
                                 "description": "test"
                             },
                             cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403
    assert response.json() == {
        'code': 'PermissionDenied',
        'message': 'You have no permission to access this resource'
    }

    admin_cookie = {f"{cookie_name}": str(root_cookie_val)}
    response = await ac.get("/api/v1/workflow-datatypes",
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0
    assert response.json()['data']['items'] == []

    response = await ac.post("/api/v1/workflow-datatypes",
                             json={
                                 "name": "test",
                                 "config": {
                                     "type": "string",
                                     "extend": "any"
                                 },
                                 "description": "test"
                             },
                             cookies=admin_cookie)
    assert response.status_code == 201
    workflow_datatype = response.json()
    workflow_datatype = workflow_datatype['data']
    assert workflow_datatype['name'] == "test"
    assert workflow_datatype['config']['type'] == "string"
    assert workflow_datatype['description'] == "test"

    response = await ac.get("/api/v1/workflow-datatypes",
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1

    workflow_datatype_id = response.json()['data']['items'][0]['id']

    # update workflow datatype with non-member user
    response = await ac.put(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                            json={"name": "test1"},
                            cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403

    root_cookie_val_2 = root_user[1]
    root_id_2 = root_user[2]
    root_username_2 = root_user[3]

    # update workflow datatype name with another admin user
    response = await ac.put(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                            json={"name": "test1"},
                            cookies={f"{cookie_name}": str(root_cookie_val_2)})

    assert response.status_code == 200
    workflow_datatype = response.json()['data']
    assert workflow_datatype_id == workflow_datatype['id']
    assert workflow_datatype['name'] == 'test1'
    assert workflow_datatype['description'] == 'test'
    assert workflow_datatype['createdBy']['id'] == root_id
    assert workflow_datatype['createdBy']['fullname'] == root_username
    assert workflow_datatype['updatedBy']['id'] == root_id_2
    assert workflow_datatype['updatedBy']['fullname'] == root_username_2

    # update workflow datatype description with admin user
    response = await ac.put(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                            json={"description": "test2"},
                            cookies=admin_cookie)

    assert response.status_code == 200
    workflow_datatype = response.json()['data']
    assert workflow_datatype_id == workflow_datatype['id']
    assert workflow_datatype['name'] == 'test1'
    assert workflow_datatype['description'] == 'test2'
    assert workflow_datatype['createdBy']['id'] == root_id
    assert workflow_datatype['createdBy']['fullname'] == root_username
    assert workflow_datatype['updatedBy']['id'] == root_id
    assert workflow_datatype['updatedBy']['fullname'] == root_username

    response = await ac.put(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                            json={"name": "test", "description": "test"},
                            cookies=admin_cookie)

    assert response.status_code == 200

    # get workflow datatype with admin user
    response = await ac.get(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                            cookies=admin_cookie)

    assert response.status_code == 200
    workflow_datatype = response.json()['data']
    assert workflow_datatype_id == workflow_datatype['id']
    assert workflow_datatype['name'] == 'test'
    assert workflow_datatype['description'] == 'test'
    assert workflow_datatype['createdBy']['id'] == root_id
    assert workflow_datatype['createdBy']['fullname'] == root_username
    assert workflow_datatype['updatedBy']['id'] == root_id
    assert workflow_datatype['updatedBy']['fullname'] == root_username

    # get workflow datatype with non-member user
    response = await ac.get(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                            cookies={f"{cookie_name}": cookie_val})

    assert response.status_code == 403

    # get workflow datatype with another admin user
    response = await ac.get(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                            cookies={f"{cookie_name}": root_cookie_val_2})

    assert response.status_code == 200

    # delete workflow datatype with non-member user
    response = await ac.delete(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                               cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 403

    # delete workflow datatype with another admin user
    response = await ac.delete(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                               cookies={f"{cookie_name}": root_cookie_val_2})
    assert response.status_code == 204

    # delete the deleted workflow datatype with admin user
    response = await ac.delete(f"/api/v1/workflow-datatypes/{workflow_datatype_id}",
                               cookies=admin_cookie)

    assert response.status_code == 404
