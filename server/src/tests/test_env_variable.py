import pytest

from . import user, external_user, root_user, test_db, client, workspace_admin_user, workspace  # noqa
from ..schema.component import ComponentType
from ..schema.common import Scope
from ..schema.env_variable import EnvVariableScopeType


@pytest.mark.asyncio
async def test_env_variable(workspace, external_user, root_user, workspace_admin_user, client):  # noqa
    cookie_name = external_user[0]
    cookie_val = external_user[1]
    root_cookie_val = root_user[1]
    root_id = root_user[2]
    root_username = root_user[3]
    # workspace_root_cookie_val = workspace_admin_user[1]

    ac = client
    workspace_id = str(workspace["id"])

    # get component with admin user
    admin_cookie = {f"{cookie_name}": str(root_cookie_val)}
    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components',
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0
    assert response.json()['data']['items'] == []

    # create workspace with admin user
    test_category = "test-cateory"
    response = await ac.post("/api/v1/component-categories",
                             json={
                                 "name": test_category,
                                 "description": "test"},
                             cookies=admin_cookie)
    assert response.status_code == 201
    component_category = response.json()
    component_category = component_category['data']
    assert component_category['name'] == test_category
    assert component_category['description'] == "test"
    category_id = component_category['id']
    category_name = component_category['name']

    response = await ac.post(f'/api/v1/workspace/{workspace_id}/components',
                             json={
                                 "name": "test",
                                 "alias": "测试",
                                 "code": "TEST",
                                 "description": "test",
                                 "type": ComponentType.INPUT.value,
                                 "categoryID": category_id,
                                 "category": category_name,
                                 "workspaceID": workspace_id,
                                 "scope": Scope.SCOPED.value,
                                 "config": {
                                     "code": "TEST",
                                     "inputs": [
                                         {
                                             "name": "test-1",
                                             "type": "string",
                                             "title": "test",
                                         },
                                         {
                                             "name": "test-2",
                                             "type": "string",
                                             "title": "test",
                                         }
                                     ],
                                     "configs": [
                                         {
                                             "name": "API_token1",
                                             "param": "x",
                                         },
                                         {
                                             "name": "API_token2",
                                             "param": "xxx",
                                         },
                                     ],
                                     "outputs": [
                                     ],
                                 },
                                 "envs": [
                                     {
                                         "name": "API_token1",
                                         "param": "x",
                                     },
                                     {
                                         "name": "API_token2",
                                         "param": "xxx",
                                     },
                                 ],
                             },
                             cookies=admin_cookie)
    assert response.status_code == 201
    component = response.json()
    component = component['data']
    component_id = str(component['id'])
    assert component['name'] == "test"
    assert component['description'] == "test"
    assert component['type'] == ComponentType.INPUT.value

    response = await ac.get(f'/api/v1/components/{component_id}/env-variables',
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 2
    assert len(response.json()['data']['items']) == 2
    env = response.json()['data']['items'][0]
    assert env['name'] in ["API_token1", "API_token2"]
    assert env['componentID'] == component_id
    assert env['param'] in ["x", "xxx"]
    assert env['scopeType'] == EnvVariableScopeType.COMPONENT.value

    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components',
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1

    response = await ac.get(f'/api/v1/components/{component_id}',
                            cookies=admin_cookie)
    assert response.status_code == 200

    # create env variable with non-member user
    response = await ac.post(f'/api/v1/components/{component_id}/env-variables',
                             json={
                                 "name": "OPENAPI_TOKEN",
                                 "component_id": component_id,
                                 "param": "param1",
                                 "value": "xxxxx1",
                                 "scopeID": component_id,
                             },
                             cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403

    # create env variable with admin user
    response = await ac.post(f'/api/v1/components/{component_id}/env-variables',
                             json={
                                 "name": "OPENAPI_TOKEN",
                                 "componentID": component_id,
                                 "param": "param1",
                                 "value": "xxxxx1",
                                 "scopeID": component_id,
                                 "scopeType": EnvVariableScopeType.COMPONENT.value,
                             },
                             cookies=admin_cookie)
    assert response.status_code == 201
    env_variable = response.json()
    env_variable = env_variable['data']
    env_variable_id = env_variable['id']
    assert env_variable['name'] == "OPENAPI_TOKEN"
    assert env_variable['value'] == "xxxxx1"
    assert env_variable['scopeType'] == EnvVariableScopeType.COMPONENT.value
    assert env_variable['scopeID'] == component_id

    response = await ac.get(f'/api/v1/components/{component_id}/env-variables',
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 3
    assert len(response.json()['data']['items']) == 3

    response = await ac.get(f'/api/v1/env-variables/{env_variable_id}',
                            cookies=admin_cookie)
    assert response.status_code == 200
    env_variable = response.json()
    env_variable = env_variable['data']
    assert env_variable_id == env_variable['id']
    assert env_variable['name'] == "OPENAPI_TOKEN"
    assert env_variable['value'] == "xxxxx1"

    # update env variable with non-member user
    response = await ac.put(f'/api/v1/env-variables/{env_variable_id}',
                            json={"value": "xxxxx2"},
                            cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403

    root_cookie_val_2 = root_user[1]
    root_id_2 = root_user[2]
    root_username_2 = root_user[3]

    # update component name with another admin user
    response = await ac.put(f"/api/v1/env-variables/{env_variable_id}",
                            json={"value": "xxxxx2"},
                            cookies={f"{cookie_name}": str(root_cookie_val_2)})

    assert response.status_code == 200
    env_variable = response.json()['data']
    assert env_variable_id == env_variable['id']
    assert env_variable['value'] == 'xxxxx2'
    assert env_variable['createdBy']['id'] == root_id
    assert env_variable['createdBy']['fullname'] == root_username
    assert env_variable['updatedBy']['id'] == root_id_2
    assert env_variable['updatedBy']['fullname'] == root_username_2

    # update env variable with admin user
    response = await ac.put(f"/api/v1/env-variables/{env_variable_id}",
                            json={"value": "xxxxx3"},
                            cookies=admin_cookie)

    assert response.status_code == 200
    env_variable = response.json()['data']
    assert env_variable_id == env_variable['id']
    assert env_variable['value'] == 'xxxxx3'
    assert env_variable['createdBy']['id'] == root_id
    assert env_variable['createdBy']['fullname'] == root_username
    assert env_variable['updatedBy']['id'] == root_id
    assert env_variable['updatedBy']['fullname'] == root_username

    response = await ac.put(f"/api/v1/env-variables/{env_variable_id}",
                            json={"value": "xxxxx4"},
                            cookies=admin_cookie)

    assert response.status_code == 200

    # get env variable with admin user
    response = await ac.get(f"/api/v1/env-variables/{env_variable_id}",
                            cookies=admin_cookie)

    assert response.status_code == 200
    env_variable = response.json()['data']
    assert env_variable_id == env_variable['id']
    assert env_variable['value'] == 'xxxxx4'
    assert env_variable['createdBy']['id'] == root_id
    assert env_variable['createdBy']['fullname'] == root_username
    assert env_variable['updatedBy']['id'] == root_id
    assert env_variable['updatedBy']['fullname'] == root_username

    # get env variable with non-member user
    response = await ac.get(f"/api/v1/env-variables/{env_variable_id}",
                            cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 403

    # get env variable with another admin user
    response = await ac.get(f"/api/v1/env-variables/{env_variable_id}",
                            cookies={
                                f"{cookie_name}": root_cookie_val_2})

    assert response.status_code == 200

    # delete env variable  with non-member user
    response = await ac.delete(f"/api/v1/env-variables/{env_variable_id}",
                               cookies={
                                   f"{cookie_name}": cookie_val})
    assert response.status_code == 403

    # delete env variable  with another admin user
    response = await ac.delete(f"/api/v1/env-variables/{env_variable_id}",
                               cookies={
                                   f"{cookie_name}": root_cookie_val_2})
    assert response.status_code == 204

    # delete the deleted env variable  with admin user
    response = await ac.delete(f"/api/v1/env-variables/{env_variable_id}",
                               cookies=admin_cookie)
    assert response.status_code == 404

    # test group
    response = await ac.post(f"/api/v1/workspace/{workspace_id}/group",
                             json={"name": "test_group",
                                   "description": "test_group"},
                             cookies=admin_cookie)

    assert response.status_code == 201
    group = response.json()['data']
    group_id = group['id']
    response = await ac.post(f'/api/v1/group/{group_id}/env-variables',
                             json={
                                 "name": "OPENAPI_TOKEN",
                                 "componentID": component_id,
                                 "param": "param1",
                                 "value": "xxxxx1",
                                 "scopeID": component_id,
                                 "scopeType": EnvVariableScopeType.COMPONENT.value,
                             },
                             cookies=admin_cookie)
    assert response.status_code == 201
