import asyncio

import pytest

from . import user, root_user, test_db, client  # noqa

from ..config import settings

from pytest_httpx import HTTPXMock


@pytest.fixture
def assert_all_responses_were_requested() -> bool:
    return False


script_content = """
import json
import sys


# 完善用户自定义函数, 该函数的输入和输出都是json dict格式，
# 且不要在控制台有任何输出
def user_self_define_func(params: dict) -> dict:
    outputs = {"__langbase__": "test"}
    return outputs


# 下面main函数不要修改
def main(args):
    if len(args) != 2:
        raise Exception(f"Invalid number of arguments {len(args)}")
    inputs = args[1]
    try:
        params = json.loads(inputs)
    except Exception as e:
        raise Exception(f"Invalid inputs not json format {inputs}")
    outputs = user_self_define_func(params=params)
    print(json.dumps(outputs))


if __name__ == "__main__":
    main(sys.argv)
"""


@pytest.mark.asyncio
async def test_proxy(user, root_user, client, httpx_mock: HTTPXMock):  # noqa
    root_cookie_val = root_user[1]

    ac = client
    admin_cookie = {f"{user[0]}": str(root_cookie_val)}

    # 1. test cio list resource type
    mock_list_resource_response = {
        "code": 200,
        "data": {
            "lesseeId": 99,
            "config": [
                {
                    "children": [
                        {
                            "label": "lxvue",
                            "id": 24,
                            "value": "7fm0e"
                        }
                    ],
                    "label": "1i362",
                    "value": "yy8ai"
                }
            ]
        },
        "message": "success"
    }

    assert_list_resource_response = {
        "code": 200,
        "data": {
            "lesseeID": 99,
            "config": [
                {
                    "children": [
                        {
                            "label": "lxvue",
                            "ID": 24,
                            "value": "7fm0e"
                        }
                    ],
                    "label": "1i362",
                    "value": "yy8ai"
                }
            ]
        },
        "message": "success"
    }

    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/business/resourcetype/langbase/get?userName=test"
    httpx_mock.add_response(url=url, method="GET", json=mock_list_resource_response)

    response = await client.get("api/v1/proxy/cio/resourcetypes",
                                cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['lesseeID'] == assert_list_resource_response['data']['lesseeID']

    # test cio get pool config
    mock_get_pool_config_response = {
        "code": 200,
        "data": [
            {
                "business": "6i9gi",
                "poolCode": "pb05i",
                "configType": "wnjv7",
                "config": "3kogv",
                "checkResult": "0cyyy",
                "resourceType": "v3ffw"
            }
        ],
        "message": "success"
    }
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/config/get?"
    params = {
        "business": "frt6d",
        "resourceType": "3h624",
        "poolCode": "ezaqt",
        "configTypes": [
            "x215m"
        ],
        "merge": False,
        "rolePermission": True,
    }
    url += f"business={params['business']}&resourceType={params['resourceType']}&poolCode={params['poolCode']}" \
           f"&configTypes={params['configTypes'][0]}&merge=false&rolePermission=false"

    httpx_mock.add_response(url=url,
                            method="GET",
                            json=mock_get_pool_config_response)
    langbase_url = f"/api/v1/proxy/cio/poolconfig?" \
                   f"business={params['business']}&resourceType={params['resourceType']}" \
                   f"&poolCode={params['poolCode']}" \
                   f"&configTypes={params['configTypes'][0]}&merge={params['merge']}"
    response = await client.get(langbase_url,
                                cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['items'][0]['business'] == mock_get_pool_config_response['data'][0]['business']
    assert response.json()['data']['total'] == 1

    # 3. test cio content pool list
    mock_content_pool_list_response = {
        "code": 200,
        "data": {
            "offset": 43,
            "limit": 84,
            "count": 6,
            "list": [
                {
                    "creator": "0duq4",
                    "business": "mq5p9",
                    "createTime": 3,
                    "resourceCount": 82,
                    "name": "s4a76",
                    "description": "vssuq",
                    "poolCode": "rojug",
                    "updateTime": 27,
                    "id": 55,
                    "poolType": "8fh2z",
                    "resourceType": "7iuis",
                    "updater": "flpgj"
                }
            ]
        },
        "message": "success"
    }
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/list?"
    params = {
        "searchField": "1",
        "keyword": "1",
        "poolCode": "1",
        "resourceType": "1",
        "business": "1",
        "creator": "1",
        "poolType": "1",
        "orderBy": "1",
        "limit": 10,
        "offset": 0,
        "desc": True,
    }
    url += f"searchField={params['searchField']}&keyword={params['keyword']}&poolCode={params['poolCode']}" \
           f"&resourceType={params['resourceType']}&business={params['business']}&creator={params['creator']}" \
           f"&poolType={params['poolType']}&orderBy={params['orderBy']}&limit={params['limit']}" \
           f"&offset={params['offset']}&desc=true"

    httpx_mock.add_response(url=url,
                            method="GET",
                            json=mock_content_pool_list_response)
    langbase_url3 = f"/api/v1/proxy/cio/pools?" \
                    f"searchField={params['searchField']}&keyword={params['keyword']}&poolCode={params['poolCode']}" \
                    f"&resourceType={params['resourceType']}" \
                    f"&business={params['business']}&creator={params['creator']}" \
                    f"&poolType={params['poolType']}&orderBy={params['orderBy']}&pageSize=10" \
                    f"&pageNumber=1&desc={params['desc']}"
    response = await ac.get(langbase_url3,
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['items'][0]['business'] == \
           mock_content_pool_list_response['data']['list'][0]['business']
    assert response.json()['data']['total'] == 6

    # 4. test cio pool resource detail get
    mock_pool_resource_detail_response = {
        "code": 200,
        "data": {
            "resourcePublishTime": "2026-09-26 21:54:33",
            "resourceId": "90im1",
            "resourceContent": "0i2wa",
            "privacyStatus": "o0ayu",
            "extInfo": "9txcn",
            "auditTag": [
                {
                    "tagGroup": [
                        {
                            "groupName": "zmqj4",
                            "groupDesc": "uq3qq",
                            "groupId": "1b0b4"
                        }
                    ],
                    "parentTagId": "a5spq",
                    "tagId": "o0q3q",
                    "level": "00p9j",
                    "children": [
                        {
                            "tagGroup": [
                                {
                                    "groupName": "9q9j9",
                                    "groupDesc": "u1gsx",
                                    "groupId": "mjdyq"
                                }
                            ],
                            "parentTagId": "ta20k",
                            "tagId": "qbun4",
                            "level": "exeis",
                            "children": [
                                {
                                    "tagGroup": [
                                    ],
                                    "parentTagId": "50h9y",
                                    "tagId": "dvne6",
                                    "level": "4na1e",
                                    "children": [
                                    ],
                                    "tagName": "ik0yy",
                                    "tagSource": "rcq7u"
                                }
                            ],
                            "tagName": "bvdjh",
                            "tagSource": "mimiq"
                        }
                    ],
                    "tagName": "mt861",
                    "tagSource": "qjo5m"
                }
            ],
            "extTag": {
                "key": [
                    {
                        "tagGroup": [
                            {
                                "groupName": "hw09z",
                                "groupDesc": "ab67m",
                                "groupId": "ulvss"
                            }
                        ],
                        "parentTagId": "jetlt",
                        "tagId": "h7pgk",
                        "level": "asgxe",
                        "children": [
                            {
                                "tagGroup": [
                                ],
                                "parentTagId": "pko27",
                                "tagId": "a8g07",
                                "level": "eipjq",
                                "children": [
                                ],
                                "tagName": "kwu3w",
                                "tagSource": "lyss8"
                            }
                        ],
                        "tagName": "ei93a",
                        "tagSource": "y3n3t"
                    }
                ]
            },
            "id": 90,
            "resourceSource": "14uhv",
            "extStatus": {
                "key": "c6bmp"
            },
            "resourceCreateTime": "2028-09-04 11:57:14",
            "fieldMap": {},
            "business": "pn52r",
            "authorType": "w3vum",
            "author": "yyxnp",
            "resourceName": "33c3c",
            "updateTime": "2021-11-07 15:08:11",
            "selectorIds": [
                53
            ],
            "auditTaskIds": [
                8
            ],
            "relatedResource": {
                "key": [
                    {
                        "resourceId": "7g4xx",
                        "resourceName": "rtncb",
                        "resourceType": "zoz1q"
                    }
                ]
            },
            "interveneMatchInfo": [
                {
                    "interveneId": 34,
                    "matchProperty": "2eb0p"
                }
            ],
            "interveneIds": [
                73
            ],
            "createTime": "2028-04-14 03:53:47",
            "authorName": "9f2oj",
            "auditStatus": "l4v4o",
            "poolCode": "05ydr",
            "resourceType": "r7wd0"
        },
        "message": "success"
    }
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/resource/langbase/detail?"
    params = {
        "resourceType": "r7wd0",
        "poolCode": "05ydr",
        "resourceId": "90im1",
    }
    url += f"resourceType={params['resourceType']}&poolCode={params['poolCode']}&resourceId={params['resourceId']}"
    httpx_mock.add_response(url=url,
                            method="GET",
                            json=mock_pool_resource_detail_response)
    langbase_url4 = f"/api/v1/proxy/cio/resourcedetail" \
                    f"?resourceType={params['resourceType']}" \
                    f"&poolCode={params['poolCode']}&resourceID={params['resourceId']}"
    response = await ac.get(langbase_url4,
                            cookies=admin_cookie)
    assert response.status_code == 200

    # 5. test cio pool resource detail get
    request = {
        "queryCode": [
            "z6ih6"
        ],
        "resourceId": "82unh",
        "business": "56lyl",
        "poolCode": "621y8",
        "operator": "uraej",
        "resourceType": "q3dje"
    }
    mock_response = {
        "code": 200,
        "data": {
            "queryResponse": {},
            "status": ""
        },
        "message": "y36du"
    }
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/resource/content/detail"
    httpx_mock.add_response(url=url,
                            method="POST",
                            json=mock_response)
    langbase_url5 = "/api/v1/proxy/cio/pool/resource/content/detail"
    response = await ac.post(langbase_url5,
                             json=request,
                             cookies=admin_cookie)
    assert response.status_code == 200

    # 6. test cio pool resource detail get
    mock_response = {
        "code": 200,
        "data": {
            "business": "he2u4",
            "fieldGroupList": [
                {
                    "groupName": "i4s31",
                    "fieldList": [
                        {
                            "parentName": "xncny",
                            "code": "3lesc",
                            "fieldSource": "84b62",
                            "name": "6nzlf",
                            "description": "kg5zn",
                            "index": "7ic14",
                            "subType": "azbip",
                            "hasPrivilege": False,
                            "type": "16nhm"
                        }
                    ]
                }
            ],
            "poolCode": "guj28",
            "resourceType": "sics2"
        },
        "message": "rpt8o"
    }
    url = f"{settings.workflow_config.cio.endpoint}/api/backend/content/scene/langbase/field?"
    params = {
        "business": "gx47q",
        "poolCode": "e6jwi",
        "resourceType": "jm156",
        "scene": "e6uf7"
    }
    url += f"business={params['business']}&resourceType={params['resourceType']}" \
           f"&poolCode={params['poolCode']}&scene={params['scene']}"
    httpx_mock.add_response(url=url,
                            method="GET",
                            json=mock_response)
    langbase_url6 = f"/api/v1/proxy/cio/content/scene/langbase/field?" \
                    f"business={params['business']}&poolCode={params['poolCode']}" \
                    f"&resourceType={params['resourceType']}&scene={params['scene']}"
    response = await ac.get(langbase_url6,
                            cookies=admin_cookie)
    assert response.status_code == 200

    # 7. test get rep2 alg detail
    mock_response = {
        "code": 200,
        "data": {
            "code": "dzgbb",
            "memory": 48,
            "assigner": "8ept3",
            "cpu": 10,
            "description": "cr6rz",
            "updateTime": 51,
            "requestParam": {
                "code": "cbrv5",
                "createTime": 85,
                "dataFieldDtoList": [
                    {
                        "dataTypeDetailDto": {
                            "code": "q0bik",
                            "createTime": 78,
                            "dataFieldDtoList": [
                            ],
                            "name": "4o97p",
                            "assigner": "601pg",
                            "description": "zhzi2",
                            "updateTime": 91,
                            "id": 41,
                            "typeFlag": 53
                        },
                        "fieldName": "lqtnf",
                        "fieldFlag": 55,
                        "createTime": 60,
                        "description": "mru4h",
                        "updateTime": 93,
                        "id": 78
                    }
                ],
                "name": "wwhzc",
                "assigner": "rjtrd",
                "description": "phnnh",
                "updateTime": 30,
                "id": 85,
                "typeFlag": 82
            },
            "serviceName": "hqhrv",
            "tags": [
                70
            ],
            "concurrency": 43,
            "mode": 22,
            "sampleScenes": [
                {
                    "iconImage": "3d32r",
                    "serviceCode": "39np0",
                    "id": 54,
                    "title": "b4q6j",
                    "content": "c95fa"
                }
            ],
            "createTime": 74,
            "responseParam": {
                "code": "9a0bm",
                "createTime": 83,
                "dataFieldDtoList": [
                    {
                        "dataTypeDetailDto": {
                            "code": "sapwi",
                            "createTime": 46,
                            "dataFieldDtoList": [
                            ],
                            "name": "r1sc3",
                            "assigner": "1zvhw",
                            "description": "di1y7",
                            "updateTime": 69,
                            "id": 78,
                            "typeFlag": 26
                        },
                        "fieldName": "bfele",
                        "fieldFlag": 94,
                        "createTime": 82,
                        "description": "tgjy1",
                        "updateTime": 69,
                        "id": 99
                    }
                ],
                "name": "tptcn",
                "assigner": "w1qo2",
                "description": "rjgww",
                "updateTime": 25,
                "id": 24,
                "typeFlag": 54
            },
            "id": 17,
            "algName": "zanjj"
        },
        "message": "gsmf0"
    }
    url = f"{settings.workflow_config.airship.endpoint}/api/rep2/algo/detail?id=22"

    httpx_mock.add_response(url=url,
                            method="GET",
                            json=mock_response)
    langbase_url7 = "/api/v1/proxy/airship/rep2/algo/22"
    response = await ac.get(langbase_url7,
                            cookies=admin_cookie)
    assert response.status_code == 200

    # 8. test get rep2 alg list
    mock_response = {
        "code": 200,
        "data": {
            "records": [
            ],
            "totalRecord": 35
        },
        "message": "0b30z"
    }
    url = f"{settings.workflow_config.airship.endpoint}/api/rep2/algo/list"

    httpx_mock.add_response(url=url,
                            method="POST",
                            json=mock_response)
    langbase_url7 = "/api/v1/proxy/airship/rep2/algo"
    response = await ac.get(langbase_url7,
                            cookies=admin_cookie)
    assert response.status_code == 200

    # 9. test python script
    data = {
        "scriptType": "python3",
        "script": script_content,
        "name": "test-name",
        "age": 18,
        "properties": {
            "name": "test-name",
            "description": "test-description",
            "age": 18,
        }
    }
    mock_response = {
        "code": 200,
        "data": {
            "name": "test",
            "content": "hello world",
        },
        "message": "0b30z"
    }
    url = f"{settings.workflow_config.langbase_proxy.endpoint}/api/v1/proxy/script/python"

    httpx_mock.add_response(url=url,
                            method="POST",
                            json=mock_response)
    langbase_url8 = "/api/v1/proxy/script/python"
    response = await ac.post(langbase_url8,
                             json=data,
                             cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']["code"] == 200


@pytest.mark.asyncio
async def test_proxy_aio(user, root_user, client):  # noqa
    root_cookie_val = root_user[1]
    admin_cookie = {f"{user[0]}": str(root_cookie_val)}
    response = await client.get(url="/api/v1/proxy/aio/atomic?pageNumber=1&pageSize=100",
                                cookies=admin_cookie)
    assert response.status_code == 200
    assert len(response.json()['data']['items']) > 0
    print("\n", response.json()['data'])


@pytest.mark.asyncio
async def test_proxy_mj(client, httpx_mock: HTTPXMock):  # noqa
    task_id = 'xxxx'
    mock_response = {
        "code": 1,
        "result": task_id,
    }
    request = {
        "appId": "5319385164880538611",
        "appKey": "tMPxmog7W6dsCSTXKtZvLTvyeiMYp5iz",
        "timeout": 300000,
        "asyncApi": True,
        "prompt": "finops 2023",
        "images": [
            "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/29723941964/b8ad/e936/bd3c/ad11ecd9ea284697e751fe91447c34c6.jpg"] # noqa
    }
    url = f"{settings.workflow_config.midjourney.endpoint}/mj/submit/imagine"

    httpx_mock.add_response(url=url,
                            method="POST",
                            json=mock_response)
    mock_task_response = {
        "code": 200,
        "data": {
            "status": "SUCCESS",
            "imageUrl": "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/29723941964/b8ad/e936/bd3c/ad11ecd9ea284697e751fe91447c34c6.jpg", # noqa
        }
    }
    url = f"{settings.workflow_config.midjourney.endpoint}/mj/task/{task_id}/fetch"
    httpx_mock.add_response(url=url,
                            method="GET",
                            json=mock_task_response)
    response = await client.post(url="/api/v1/proxy/mj/submit/imagine",
                                 json=request)
    assert response.status_code == 500
    request = {
        "appId": "5319385164880538611",
        "appKey": "tMPxmog7W6dsCSTXKtZvLTvyeiMYp5iz",
        "timeout": 300000,
        "asyncApi": True,
        "prompt": "finops 2023",
    }
    response = await client.post(url="/api/v1/proxy/mj/submit/imagine",
                                 json=request)
    assert response.status_code == 200
    await asyncio.sleep(2)


@pytest.mark.asyncio
async def test_proxy_mj_imagine(client):  # noqa
    request = {
        "appId": "5319385164880538611",
        "appKey": "tMPxmog7W6dsCSTXKtZvLTvyeiMYp5iz",
        "timeout": 300000,
        "asyncApi": True,
        "prompt": "finops 2023",
        # "images": ["https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/29723941964/b8ad/e936/bd3c/ad11ecd9ea284697e751fe91447c34c6.jpg"], # noqa
        "images": ["https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/29723941964/b8ad/e936/bd3c/ad11ecd9ea"],  # noqa
        "state": "--v 5.1 --ar 16:9"
    }
    response = await client.post(url="/api/v1/proxy/mj/submit/imagine",
                                 json=request)
    print(response.json())
    assert response.status_code == 500


@pytest.mark.asyncio
async def test_proxy_chat(client):  # noqa
    request = {
        "modelName": "gpt-4-vision-preview",
        "system": " ",
        "message": "What are in this image?",
        "images": [
            "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/31962205336/9e92/45aa/7c4d/d5e45193f8d67c239cd14ec692a24199.jpeg", # noqa
        ],
        "apiBase": "https://aigc-api.hz.netease.com/openai/v1",
        "apiKey": "sk-JRVjpsPPAimYOVGZ3mLH3hm7Yv7Hl0TqV0tKl60mcx0Gtuge",
        "asyncApi": False,
    }
    resp = await client.post(url="/api/v1/proxy/chat", json=request)
    assert resp.status_code == 200
    print(resp.json())

    request = {
        "modelName": "gpt-3.5-turbo-1106",
        "system": " ",
        "message": "hello",
        "apiBase": "https://aigc-api.hz.netease.com/openai/v1",
        "apiKey": "sk-JRVjpsPPAimYOVGZ3mLH3hm7Yv7Hl0TqV0tKl60mcx0Gtuge",
    }
    resp = await client.post(url="/api/v1/proxy/chat", json=request)
    assert resp.status_code == 200


@pytest.mark.asyncio
async def test_proxy_open_dall_e_3(client):  # noqa
    request = {
        "modelName": "dall-e-3",
        "prompt": "夏天",
        "apiBase": "https://aigc-api.hz.netease.com/openai/v1",
        "apiKey": "sk-JRVjpsPPAimYOVGZ3mLH3hm7Yv7Hl0TqV0tKl60mcx0Gtuge",
        "asyncApi": False,
    }
    resp = await client.post(url="/api/v1/proxy/image/single-gen", json=request)
    assert resp.status_code == 200


@pytest.mark.asyncio
async def test_proxy_mj_blend(client):  # noqa
    request = {
        "appId": "5319385164880538611",
        "appKey": "tMPxmog7W6dsCSTXKtZvLTvyeiMYp5iz",
        "timeout": 300000,
        "asyncApi": True,
        "dimensions": 'ERROR',
        "images": [
            "https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/29723941964/b8ad/e936/bd3c/ad11ecd9ea284697e751fe91447c34c6.jpg", # noqa
            "https://p5.music.126.net/obj/wo3DlcOGw6DClTvDisK1/29728508917/2816/b649/fca0/6ff15e0916f733329556baaca358a5d3.jpg" # noqa
        ]
    }
    response = await client.post(url="/api/v1/proxy/mj/submit/blend",
                                 json=request)
    assert response.status_code == 500


@pytest.mark.asyncio
async def test_proxy_airship(client):  # noqa
    response = await client.get(url="/api/v1/proxy/airship/rep2/algo")
    assert response.status_code == 500

    algo_id = 1035
    response = await client.get(url=f"/api/v1/proxy/airship/rep2/algo/{algo_id}")
    assert response.status_code == 500


@pytest.mark.asyncio
async def test_proxy_dream_maker(client):  # noqa
    data = {
        "enable_hr": False,
        "prompt": "a white cat, sunny day",
        "negative_prompt": "",
        "width": 512,
        "height": 512,
        "model_name": "Stable-diffusion-v1-5",
    }
    response = await client.post(url="/api/v1/proxy/dm/txt2img", json=data)
    assert response.status_code == 500
    await asyncio.sleep(2)

    data = {
        "prompt": "a white cat, sunny day",
        "negative_prompt": "",
        "width": 512,
        "height": 512,
        "model_name": "Stable-diffusion-v1-5",
        "init_images": [
            'https://p6.music.126.net/obj/wo3DlcOGw6DClTvDisK1/29723941964/b8ad/e936/bd3c/ad11ecd9ea284697e751fe91447c34c6.jpg' # noqa
        ]
    }
    response = await client.post(url="/api/v1/proxy/dm/img2img", json=data)
    assert response.status_code == 500
    await asyncio.sleep(2)

    response = await client.get(url="/api/v1/proxy/dm/models")
    assert response.status_code == 500


@pytest.mark.asyncio
async def test_proxy_dream_maker_pb_rem(client):  # noqa
    data = {
        "cascadePSP_enabled": False,
        "img": "https://t7.baidu.com/it/u=1362366007,2277133945&fm=193&f=GIF",  # noqa
        "model_name": "sam_vit_h_4b8939.pth",
        "query": "去除背景图",
        "sa_enabled": False
    }
    response = await client.post(url="/api/v1/proxy/dm/pbrem", json=data)
    assert response.status_code == 500
    print(response.json())


@pytest.mark.asyncio
async def test_proxy_dream_maker_mock(client, httpx_mock: HTTPXMock):  # noqa
    mock_token_response = {
        "token": "xxxx",
    }

    url = f"{settings.workflow_config.dream_maker.auth_endpoint}"
    httpx_mock.add_response(url=url,
                            method="POST",
                            json=mock_token_response)
    mock_models_response = [
        {
            "_id": "64af9e2d45a8deadedc2ecf9",
            "real_model_name": "d1facd9a2b7c5e46d4d54de5a7441c370804c0c5727ca04a0708319d04047c58",
            "description": "二次元模型，社区大佬炼制的融合模型，比Novel的二次元浓度更高",
            "cover": "http://smartowl.bluefp.ps.netease.com/file/63f2e8ac66ee281c7f703b96BqLm6s2v01",
            "network_type": "CHECKPOINT",
            "encrypted": False,
            "create_time": 16892308938,
            "update_time": 16892308938,
            "group_name": "",
            "virtual_model_name": "Anything-V3.0",
            "group_id": ""
        }
    ]
    url = f"{settings.workflow_config.dream_maker.endpoint}/api/v1/models"
    httpx_mock.add_response(url=url,
                            method="GET",
                            json=mock_models_response)
    response = await client.get(url="/api/v1/proxy/dm/models")
    assert response.status_code == 200
