import pytest

from . import user, root_user, test_db, client # noqa


@pytest.mark.asyncio
async def test_workspace(user, root_user, client):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]
    root_cookie_val = root_user[1]
    root_id = root_user[2]
    root_username = root_user[3]

    ac = client

    admin_cookie = {f"{cookie_name}": str(root_cookie_val)}
    response = await ac.get("/api/v1/workspace",
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0
    assert response.json()['data']['items'] == []

    # create workspace with admin user
    # TODO: check related group
    response = await ac.post("/api/v1/workspace", json={
                                    "name": "test",
                                    "description": "test"},
                             cookies=admin_cookie)
    assert response.status_code == 201
    workspace = response.json()
    workspace = workspace['data']
    assert workspace['name'] == "test"
    assert workspace['description'] == "test"

    response = await ac.get("/api/v1/workspace",
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1

    # list group by workspace
    response = await ac.get(f"/api/v1/workspace/{workspace['id']}/group",
                            cookies=admin_cookie)

    assert response.status_code == 200
    groups = response.json()['data']['items']
    total = response.json()['data']['total']
    assert total == 1
    group = groups[0]
    group_id = group['id']

    # check userroles of workspace
    response = await ac.get('/api/v1/userroles', cookies=admin_cookie)
    assert response.status_code == 200
    workspace_id = workspace['id']
    roles = response.json()['data']
    for role in roles:
        if role['resourceType'] == 'workspace':
            assert role['role'] == 'admin'
            assert role['resourceID'] == workspace_id
            assert role['memberID'] == root_id
        if role['resourceType'] == 'group':
            assert role['role'] == 'admin'
            assert role['resourceID'] == group_id
            assert role['memberID'] == root_id

    # update workspace with non-member user
    response = await ac.put(f"/api/v1/workspace/{workspace_id}", json={
                                    "name": "test1"},
                            cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403

    root_cookie_val_2 = root_user[1]
    root_id_2 = root_user[2]
    root_username_2 = root_user[3]

    # update workspace name with another admin user
    response = await ac.put(f"/api/v1/workspace/{workspace_id}", json={
                                    "name": "test1"},
                            cookies={
                                f"{cookie_name}": str(root_cookie_val_2)})

    assert response.status_code == 200
    workspace = response.json()['data']
    assert workspace_id == workspace['id']
    assert workspace['name'] == 'test1'
    assert workspace['description'] == 'test'
    assert workspace['createdBy']['id'] == root_id
    assert workspace['createdBy']['fullname'] == root_username
    assert workspace['updatedBy']['id'] == root_id_2
    assert workspace['updatedBy']['fullname'] == root_username_2

    # update workspace description with admin user
    response = await ac.put(f"/api/v1/workspace/{workspace_id}", json={
                                    "description": "test2"},
                            cookies=admin_cookie)

    assert response.status_code == 200
    workspace = response.json()['data']
    assert workspace_id == workspace['id']
    assert workspace['name'] == 'test1'
    assert workspace['description'] == 'test2'
    assert workspace['createdBy']['id'] == root_id
    assert workspace['createdBy']['fullname'] == root_username
    assert workspace['updatedBy']['id'] == root_id
    assert workspace['updatedBy']['fullname'] == root_username

    response = await ac.put(f"/api/v1/workspace/{workspace_id}", json={
                                    "name": "test",
                                    "description": "test"},
                            cookies=admin_cookie)

    assert response.status_code == 200

    # get workspace with admin user
    response = await ac.get(f"/api/v1/workspace/{workspace_id}",
                            cookies=admin_cookie)

    assert response.status_code == 200
    workspace = response.json()['data']
    assert workspace_id == workspace['id']
    assert workspace['name'] == 'test'
    assert workspace['description'] == 'test'
    assert workspace['createdBy']['id'] == root_id
    assert workspace['createdBy']['fullname'] == root_username
    assert workspace['updatedBy']['id'] == root_id
    assert workspace['updatedBy']['fullname'] == root_username

    # get workspace with non-member user
    response = await ac.get(f"/api/v1/workspace/{workspace_id}",
                            cookies={
                                f"{cookie_name}": cookie_val})

    assert response.status_code == 403

    # get workspace with another admin user
    response = await ac.get(f"/api/v1/workspace/{workspace_id}",
                            cookies={
                                f"{cookie_name}": root_cookie_val_2})

    assert response.status_code == 200

    # delete workspace with non-member user
    response = await ac.delete(f"/api/v1/workspace/{workspace_id}",
                               cookies={
                                    f"{cookie_name}": cookie_val})
    assert response.status_code == 403

    # delete workspace with another admin user
    response = await ac.delete(f"/api/v1/workspace/{workspace_id}",
                               cookies={
                                    f"{cookie_name}": root_cookie_val_2})
    assert response.status_code == 204

    # delete the deleted workspace with admin user
    response = await ac.delete(f"/api/v1/workspace/{workspace_id}",
                               cookies=admin_cookie)

    assert response.status_code == 404
