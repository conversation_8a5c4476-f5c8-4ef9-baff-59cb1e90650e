from typing import Optional
from uuid import uuid4
import pytest
from ..controller.oauth import user_register
from ..schema.account import AccountCreate
from ..config import settings
from ..misc.session import SessionUser, backend, cookie

from . import test_db, client # noqa


@pytest.mark.asyncio
async def test_account(test_db, client):  # noqa
    db = test_db
    ac = client

    account_create = AccountCreate(
        email="<EMAIL>",
        name="tester",
        fullname="tester.fullname")

    account = await user_register(account_create, db)
    session_id = uuid4()

    session_user = SessionUser.model_validate(account)
    await backend.create(session_id, session_user)
    cookie_id = str(cookie.signer.dumps(session_id.hex))

    response = await ac.get("/api/v1/userroles",
                            cookies={f"{settings.cookie_key}": cookie_id})

    assert response.status_code == 200
    roles = response.json()['data']
    assert len(roles) == 3
    workspace_id: Optional[str] = None
    for role in roles:
        if role['resourceType'] == 'workspace':
            assert role['role'] == 'admin'
            assert role['memberID'] == session_user.id
            workspace_id = role['resourceID']
        if role['resourceType'] == 'group':
            assert role['role'] == 'admin'
            assert role['memberID'] == session_user.id

    # 测试可以进行接口调用
    response = await ac.get(f"/api/v1/workspace/{workspace_id}",
                            cookies={f"{settings.cookie_key}": cookie_id})

    assert response.status_code == 200
