import jwt
import pytest
from httpx import AsyncClient

from ..workflow.workflow_client import WORKFLOW_PATH
from ..schema.app import AppType
from ..schema.token import AnonymousJWTPayload

from ..schema.dialog import ConversationCreate, MessageCreate
from ..service.dialog import \
    create_conversation as create_conversation_service, \
    create_message as create_message_service

from . import user, group, workspace, root_user, \
    test_db, client, app  # noqa

from ..config import settings

test_workflow_config = {
    "version": "1.0",
    "workflowId": "TEST_WORKFLOW",
    "workflowEngineId": "f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea",
    "workflowEngineName": "aio",
    "inputs": [
        {
            "name": "input1",
            "type": "string"
        },
        {
            "name": "input2",
            "type": "number",
            "title": "Input 2",
            "description": "This is input 2"
        }
    ],
    "outputs": [
        {
            "name": "output1",
            "type": "boolean",
            "title": "Output 1"
        }
    ],
    "nodes": [
        {
            "id": "cdb3bc77",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 190,
            "label": "Switch组件",
            "icon": "icon-fork",
            "category": "logic",
            "code": "SWITCH",
            "type": "SWITCH",
            "description": "可以根据输入条件，拆分成多条分支",
            "inputs": [
                {
                    "name": "switchVal",
                    "type": "string"
                }
            ],
            "vars": [
            ],
            "outputs": [
                {
                    "name": "default",
                    "dataType": "__logic__"
                },
            ],
            "x": -170,
            "y": -270,
            "ports": {}
        },
    ],
    "edges": []
}


@pytest.mark.asyncio
async def test_token_and_service(user, client: AsyncClient, app, test_db):  # noqa
    resp = await client.get(f"/api/v1/app/{app['id']}/token",
                            cookies={f"{user[0]}": str(user[1])})
    assert resp.status_code == 200

    data = resp.json()['data']
    assert data['total'] == 0

    # create a token
    resp = await client.post(f"/api/v1/app/{app['id']}/token",
                             cookies={f"{user[0]}": str(user[1])})

    assert resp.status_code == 201

    data = resp.json()['data']
    assert data['token'] is not None

    # create the second token
    resp = await client.post(f"/api/v1/app/{app['id']}/token",
                             cookies={f"{user[0]}": str(user[1])})

    assert resp.status_code == 201

    # list tokens
    resp = await client.get(f"/api/v1/app/{app['id']}/token",
                            cookies={f"{user[0]}": str(user[1])})
    assert resp.status_code == 200

    token = data['token']
    data = resp.json()['data']
    assert data['total'] == 2

    assert data['items'][0]['token'] == token \
           or data['items'][1]['token'] == token

    # delete token
    for item in data['items']:
        if item['token'] == token:
            continue
        resp = await client.delete(f"/api/v1/token/{item['id']}",
                                   cookies={f"{user[0]}": str(user[1])})

        assert resp.status_code == 204

    resp = await client.get(f"/api/v1/app/{app['id']}/token",
                            cookies={f"{user[0]}": str(user[1])})
    assert resp.status_code == 200

    data = resp.json()['data']
    assert data['total'] == 1
    assert data['items'][0]['token'] == token

    # create conversation and message
    conv = await create_conversation_service(test_db, "outter:external_user",
                                             ConversationCreate(
                                                 app_id=app['id'],
                                                 app_config_id='',
                                                 override_config=None,
                                                 parameters=None,
                                             ))
    resp = await client.get("/api/v1/conversation",
                            params={
                                "user": "external_user"
                            },
                            headers={
                                'Authorization': f"Bearer {token}"
                            })

    assert resp.status_code == 200
    assert resp.json()['data']['total'] == 1

    await create_message_service(
        test_db,
        user_id="outter:external_user",
        message_create=MessageCreate(
            conversation_id=conv.id,
            query="hello",
            response="world",
        )
    )
    resp = await client.get('/api/v1/message',
                            params={
                                "conversationID": conv.id
                            },
                            headers={
                                'Authorization': f"Bearer {token}"
                            })

    assert resp.status_code == 200
    data = resp.json()['data']
    assert data['total'] == 1

    # login with anonymous user
    resp = await client.post(f'/api/v1/app/{app["id"]}/temp-token')
    assert resp.status_code == 200
    token = resp.json()['data']['token']

    payload = jwt.decode(
        token,
        algorithms=["HS256"],
        options={"verify_signature": False})
    payload = AnonymousJWTPayload.model_validate(payload)

    conv = await create_conversation_service(test_db, payload.user_id,
                                             ConversationCreate(
                                                 app_id=app['id'],
                                                 app_config_id='',
                                                 override_config=None,
                                                 parameters=None,
                                             ))

    resp = await client.get("/api/v1/conversation",
                            params={
                                "user": payload.user_id
                            },
                            headers={
                                'Authorization': f"Bearer {token}"
                            })


@pytest.mark.asyncio
async def test_token_create_with_related_resources(root_user, user, client: AsyncClient, app, test_db,  # noqa
                                                   requests_mock):  # noqa
    root_cookie_name = root_user[0]
    root_cookie_val = root_user[1]
    root_user_cookie = {f"{root_cookie_name}": str(root_cookie_val)}
    # noqa
    resp = await client.get(f"/api/v1/app/{app['id']}/token",
                            cookies={f"{user[0]}": str(user[1])})
    assert resp.status_code == 200

    data = resp.json()['data']
    assert data['total'] == 0

    # create a token with related resources
    resp = await client.post(f"/api/v1/app/{app['id']}/token",
                             cookies={f"{user[0]}": str(user[1])})

    assert resp.status_code == 201

    data = resp.json()['data']
    assert data['token'] is not None
    assert data['id'] is not None
    token_id = data['id']

    resp = await client.get(f"/api/v1/token/{token_id}/related-resources",
                            cookies={f"{user[0]}": str(user[1])})
    assert resp.status_code == 200
    data = resp.json()['data']
    assert data['total'] == 1
    related_resource = data['items'][0]
    assert related_resource['resourceType'] == 'app'
    assert related_resource['tokenID'] == token_id

    # create a token
    response = await client.post("/api/v1/workspace",
                                 json={
                                     "name": "test",
                                     "description": "test"},
                                 cookies=root_user_cookie)

    assert response.status_code == 201
    workspace_id = response.json()['data']['id']

    response = await client.post(f"/api/v1/workspace/{workspace_id}/group",
                                 json={"name": "test_group",
                                       "description": "test_group"},
                                 cookies=root_user_cookie)
    group_id = response.json()['data']['id']

    resp = await client.post(f"/api/v1/workspace/{workspace_id}/token",
                             json={
                                 "resources": [
                                     {"type": "group", "id": group_id},
                                     {"type": "group", "id": "group2"},
                                     {"type": "group", "id": "group3"},
                                 ]
                             },
                             cookies=root_user_cookie)

    assert resp.status_code == 201
    data = resp.json()['data']
    assert data['token'] is not None
    assert data['id'] is not None
    token_id = data['id']
    workspace_token = data['token']
    resp = await client.get(f"/api/v1/token/{token_id}/related-resources",
                            cookies={f"{user[0]}": str(user[1])})
    assert resp.status_code == 200
    data = resp.json()['data']
    assert data['total'] == 3
    for item in data['items']:
        assert item['tokenID'] == token_id
        if item['resourceType'] == 'workspace':
            assert item['resourceID'] == workspace_id
        elif item['resourceType'] == 'group':
            assert item['resourceID'] in [group_id, 'group2', 'group3']

    # 2. test app trigger
    mock_create_success_response = {
        "code": 200,
        "message": "success",
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/create",
                       status_code=200,
                       json=mock_create_success_response)
    response = await client.post(f'/api/v1/group/{group_id}/app',
                                 json={
                                     'name': 'test_workflow_app',
                                     'description': 'test_workflow_app',
                                     'type': AppType.WORKFLOW,
                                     'config': test_workflow_config,
                                 },
                                 cookies=root_user_cookie)
    assert response.status_code == 201
    app_id = response.json()['data']['id']

    mock_trigger_request = {
        "appID": app_id,
        "inputs": {
            "input1": "input1",
            "input2": 2
        },
        "callback": {

        },
        "bizContext": "business context",
    }
    flow_id = "TEST_WORKFLOW"
    flow_run_id = "run_id_1"
    mock_trigger_success_response = {
        "code": 200,
        "message": "success",
        "data": {
            "flowId": flow_id,
            "runId": flow_run_id,
            "outputs": {
                "output1": "output1",
                "output2": 3
            },
            "bizContext": "business context",
        }
    }
    requests_mock.post(f"{settings.workflow_config.engines[0].endpoint}/"
                       f"{settings.workflow_url_prefix}/{WORKFLOW_PATH}/trigger",
                       status_code=200,
                       json=mock_trigger_success_response)
    response = await client.post('/api/v1/app/trigger',
                                 headers={
                                     "Authorization": f"Bearer {workspace_token}"
                                 },
                                 json=mock_trigger_request)
    assert response.status_code == 200
    assert response.json()['data']['appID'] == app_id
    assert response.json()['data']['runID'] == flow_run_id
    assert response.json()['data']['outputs']['output1'] == "output1"
    assert response.json()['data']['outputs']['output2'] == 3
