import uuid
import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.pool import StaticPool

from ..schema.member import MemberType, Role, MemberCreate
from ..schema.common import ResourceType
from ..service.member import create as create_member_service, delete as delete_member
from ..models.base import Base
from ..misc.db import get_db, _custom_json_searializer
from ..langbase import app as langbase_app
from ..misc.session import SessionUser, backend, cookie
from ..schema.account import AccountCreate
from ..service.account import create as create_account, \
    delete as delete_account
from ..service.model import provider_binding_init
from server.src.lib import toolpool


@pytest_asyncio.fixture(scope="function")
async def client():
    async with AsyncClient(app=langbase_app,
                           base_url='http://127.0.0.1:8000') as client:
        yield client


@pytest.fixture(scope="function")
def test_db():
    SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        json_serializer=_custom_json_searializer,
    )
    TestingSessionLocal = sessionmaker(autocommit=False,
                                       autoflush=False,
                                       bind=engine)
    TestingScopedSession = scoped_session(TestingSessionLocal)
    scoped_db = TestingScopedSession()

    Base.metadata.create_all(bind=engine)

    def override_get_db():
        db = TestingSessionLocal()
        try:
            yield db
        finally:
            db.close()

    provider_binding_init(scoped_db)

    langbase_app.dependency_overrides[get_db] = override_get_db

    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


@pytest_asyncio.fixture(scope="function")
async def root_user(test_db):
    db = test_db
    account = await create_account(db, AccountCreate(
        email="test",
        name="test",
        fullname="tester",
    ))
    id = uuid.UUID(account.id)
    await backend.create(id, SessionUser(
        id=str(id),
        name="test",
        fullname="tester",
        email="test",
        is_admin=True,
    ))
    yield (cookie.model.name,
           str(cookie.signer.dumps(id.hex)),
           str(id), "tester")
    await delete_account(db, account.id)
    await backend.delete(id)


@pytest_asyncio.fixture(scope="function")
async def user(test_db):
    db = test_db
    account = await create_account(db, AccountCreate(
        email="test",
        name="test",
        fullname="tester",
    ))
    id = uuid.UUID(account.id)
    await backend.create(id, SessionUser(
        id=str(id),
        name="test",
        fullname="tester",
        email="test",
        is_admin=False
    ))
    yield (cookie.model.name,
           str(cookie.signer.dumps(id.hex)),
           str(id), "tester")
    await delete_account(db, account.id)
    await backend.delete(id)


@pytest_asyncio.fixture(scope="function")
async def external_user(test_db):
    db = test_db
    account = await create_account(db, AccountCreate(
        email="external",
        name="external",
        fullname="external",
    ))
    id = uuid.UUID(account.id)
    await backend.create(id, SessionUser(
        id=str(id),
        name="external",
        fullname="external",
        email="external",
        is_admin=False
    ))
    yield (cookie.model.name,
           str(cookie.signer.dumps(id.hex)),
           str(id), "external")
    await delete_account(db, account.id)
    await backend.delete(id)


@pytest_asyncio.fixture(scope='function')
async def workspace(client, root_user, user, test_db):
    response = await client.post('/api/v1/workspace', json={
        'name': 'test',
        'description': 'test',
    }, cookies={f"{root_user[0]}": root_user[1]})
    assert response.status_code == 201
    workspace = response.json()['data']
    await create_member_service(
        test_db,
        user_id=root_user[2],
        resource_type=ResourceType.WORKSPACE,
        resource_id=workspace["id"],
        member_create=MemberCreate(
            memberType=MemberType.USER.value,
            memberID=user[2],
            role=Role.ADMIN.value,
        ))
    return workspace


@pytest_asyncio.fixture(scope='function')
async def workspace_admin_user(workspace, test_db):
    db = test_db
    account = await create_account(db, AccountCreate(
        email="workspace_admin_test",
        name="workspace_admin_test",
        fullname="workspace_admin_tester",
    ))
    id = uuid.UUID(account.id)
    await backend.create(id, SessionUser(
        id=str(id),
        email="workspace_admin_test",
        name="workspace_admin_test",
        fullname="workspace_admin_tester",
        is_admin=False
    ))
    member = await create_member_service(
        db,
        user_id=str(id),
        resource_type=ResourceType.WORKSPACE,
        resource_id=workspace["id"],
        member_create=MemberCreate(
            memberType=MemberType.USER.value,
            memberID=account.id,
            role=Role.ADMIN.value,
        ))
    yield (cookie.model.name,
           str(cookie.signer.dumps(id.hex)),
           str(id), "workspace_admin_tester")
    await delete_account(db, account.id)
    await delete_member(db, ResourceType.WORKSPACE.value, workspace["id"], member.member_type, member.id)
    await backend.delete(id)


@pytest_asyncio.fixture(scope='function')
async def group(client, workspace, user):
    response = await client.get(f"/api/v1/workspace/{workspace['id']}/group",
                                cookies={f"{user[0]}": user[1]})
    assert response.status_code == 200
    groups = response.json()['data']['items']
    assert len(groups) == 1
    group = groups[0]
    return group


@pytest_asyncio.fixture(scope='function')
async def group_admin_user(group, test_db):
    db = test_db
    account = await create_account(db, AccountCreate(
        email="group_admin_test",
        name="group_admin_test",
        fullname="group_admin_tester",
    ))
    id = uuid.UUID(account.id)
    await backend.create(id, SessionUser(
        id=str(id),
        email="group_admin_test",
        name="group_admin_test",
        fullname="group_admin_tester",
        is_admin=False
    ))
    member = await create_member_service(
        db,
        user_id=str(id),
        resource_type=ResourceType.GROUP,
        resource_id=group["id"],
        member_create=MemberCreate(
            memberType=MemberType.USER.value,
            memberID=account.id,
            role=Role.ADMIN.value,
        ))
    yield (cookie.model.name,
           str(cookie.signer.dumps(id.hex)),
           str(id), "group_admin_tester")
    await delete_account(db, account.id)
    await delete_member(db, ResourceType.WORKSPACE.value, group["id"], member.member_type, member.id)
    await backend.delete(id)


@pytest_asyncio.fixture(scope='function')
async def app(group, user, client):  # noqa
    config = {
        "prePrompt": "hello",
        "paramsInPrompt": [{
            "key": "key",
            "title": "title",
            "type": "list",
            "config": {
                "options": [{"value": "hello"}, {"value": "world"}]
            }
        }],
        "prologue": "hello",
        "modelName": "chatgpt3.5",
        "providerKind": "opanai"
    }
    response = await client.post(f'/api/v1/group/{group["id"]}/app',
                                 json={
                                     'name': 'test_app',
                                     'description': 'test_app',
                                     'type': 'agent-completion',
                                     'config': config,
                                 },
                                 cookies={f"{user[0]}": user[1]})
    assert response.status_code == 201
    return response.json()['data']

toolpool.get_db = test_db
