import pytest

from . import user, external_user, root_user, test_db, client, workspace_admin_user, workspace  # noqa
from ..schema.component import ComponentType
from ..schema.common import Scope


@pytest.mark.asyncio
async def test_component(workspace, external_user, root_user, workspace_admin_user, client):  # noqa
    cookie_name = external_user[0]
    root_cookie_val = root_user[1]
    workspace_root_cookie_val = workspace_admin_user[1]

    ac = client
    workspace_id = str(workspace["id"])

    admin_cookie = {f"{cookie_name}": str(root_cookie_val)}
    test_category = "test-cateory"
    response = await ac.post("/api/v1/component-categories",
                             json={
                                 "name": test_category,
                                 "description": "test"},
                             cookies=admin_cookie)
    assert response.status_code == 201
    component_category = response.json()
    component_category = component_category['data']
    assert component_category['name'] == test_category
    assert component_category['description'] == "test"
    category_id = component_category['id']
    category_name = component_category['name']

    workspace_admin_cookie = {f"{cookie_name}": str(workspace_root_cookie_val)}
    response = await ac.post(f'/api/v1/workspace/{workspace_id}/components',
                             json={
                                 "name": "test",
                                 "code": "TEST",
                                 "description": "test",
                                 "type": ComponentType.INPUT.value,
                                 "categoryID": category_id,
                                 "category": category_name,
                                 "workspaceID": workspace_id,
                                 "scope": Scope.SCOPED.value,
                                 "config": {
                                     "code": "TEST",
                                     "inputs": [
                                         {
                                             "name": "test-1",
                                             "type": "string",
                                             "title": "test",
                                         },
                                         {
                                             "name": "test-2",
                                             "type": "string",
                                             "title": "test",
                                         }
                                     ],
                                     "outputs": [
                                     ],
                                 },
                                 "appTypes": ['agent', 'workflow'],
                             },
                             cookies=workspace_admin_cookie)
    assert response.status_code == 201
    component = response.json()['data']
    component_id = component['id']
    assert component_id == component['id']

    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components',
                            cookies=workspace_admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1
    component = response.json()['data']['items'][0]
    assert component['category'] == test_category

    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components?'
                            f'isFavorite=true&userID={workspace_admin_user[2]}',
                            cookies=workspace_admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0

    response = await ac.post(f'/api/v1/components/{component_id}/favorite',
                             cookies=workspace_admin_cookie)
    assert response.status_code == 200

    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components?'
                            f'isFavorite=true&userID={workspace_admin_user[2]}',
                            cookies=workspace_admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1

    response = await ac.delete(f'/api/v1/components/{component_id}/favorite',
                               cookies=workspace_admin_cookie)
    assert response.status_code == 200
    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components?'
                            f'isFavorite=true&userID={workspace_admin_user[2]}',
                            cookies=workspace_admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0
