from httpx import AsyncClient
import pytest

from ..lib.llms.base import Providers
from . import test_db, user, root_user, client, workspace, group  # noqa


# Test list_global_model_provider endpoint
@pytest.mark.asyncio
async def test_list_global_model_provider(client: AsyncClient, test_db):  # noqa
    response = await client.get("/api/v1/model-provider")
    assert response.status_code == 200
    data = response.json()["data"]
    assert len(data) == len(Providers)
    data = data[0]
    assert data["kind"] == "openai"


# Test create_workspace_model_provider_binding endpoint
@pytest.mark.asyncio
async def test_operate_workspace_model_provider_binding(client: AsyncClient, workspace, user):  # noqa
    response = await client.post(
        f"/api/v1/workspace/{workspace['id']}/model-provider",
        json={
            "providerKind": "openai",
            "apiKey": "my_key",
            "description": "test_description",
            "endpoint": "http://test.com",
            "config": {"hello": "world"}
        },
        cookies={user[0]: user[1]})
    assert response.status_code == 201
    data = response.json()["data"]

    created_item_id = data['id']
    assert "id" in data
    del data["id"]
    assert data == {
        "providerKind": "openai",
        "config": {"hello": "world"},
        "description": "test_description",
        "endpoint": "http://test.com",
    }

    response = await client.get(
        f"/api/v1/workspace/{workspace['id']}/model-provider",
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    items = response.json()['data']['items']
    # 过滤出 openai 相关的 providerBinding
    items = [item for item in items if item['providerKind'] == 'openai']
    assert len(items) == 2
    default_item = dict()
    for item in items:
        if item['description'] == 'test_description':
            assert item['providerKind'] == 'openai'
            assert item['endpoint'] == 'http://test.com'
            assert item['config'] == {'hello': 'world'}
        else:
            default_item = item
            assert item['providerKind'] == 'openai'
            assert item['config'] is None
            assert item['description'] is None
            assert item['endpoint'] is None

    response = await client.put(
        f"/api/v1/model-provider/{created_item_id}",
        json={"description": "edit"},
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    assert data == {
        "providerKind": "openai",
        "config": {"hello": "world"},
        "description": "edit",
        "endpoint": "http://test.com",
    }

    response = await client.put(
        f"/api/v1/model-provider/{created_item_id}",
        json={
            "description": "edit2",
            "config": None,
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    assert data == {
        "providerKind": "openai",
        "config": None,
        "description": "edit2",
        "endpoint": "http://test.com",
    }

    response = await client.put(
        f"/api/v1/model-provider/{default_item['id']}",
        json={
            "description": "edit2",
            "config": None,
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 400
    data = response.json()
    assert data['code'] == 'BadRequest'
    assert data['message'] == "Cannot update default provider binding"

    response = await client.delete(
        f"/api/v1/model-provider/{default_item['id']}",
        cookies={user[0]: user[1]})
    assert response.status_code == 400
    data = response.json()
    assert data['code'] == 'BadRequest'
    assert data['message'] == "Cannot delete default provider binding"

    response = await client.delete(
        f"/api/v1/model-provider/{created_item_id}",
        cookies={user[0]: user[1]})

    assert response.status_code == 204


@pytest.mark.asyncio
async def test_operate_group_model_provider_binding(client: AsyncClient, group, user):  # noqa
    response = await client.post(
        f"/api/v1/group/{group['id']}/model-provider",
        json={
            "providerKind": "openai",
            "apiKey": "my_key",
            "description": "test_description",
            "endpoint": "http://test.com",
            "config": {"hello": "world"}
        },
        cookies={user[0]: user[1]})
    assert response.status_code == 201
    data = response.json()["data"]

    created_item_id = data['id']
    assert "id" in data
    del data["id"]
    assert data == {
        "providerKind": "openai",
        "config": {"hello": "world"},
        "description": "test_description",
        "endpoint": "http://test.com",
    }

    response = await client.get(
        f"/api/v1/group/{group['id']}/model-provider",
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    items = response.json()['data']['items']
    assert len(items) == 1
    assert items == [
        {
            "id": created_item_id,
            "providerKind": "openai",
            "config": {"hello": "world"},
            "description": "test_description",
            "endpoint": "http://test.com",
        }
    ]

    response = await client.put(
        f"/api/v1/model-provider/{created_item_id}",
        json={"description": "edit"},
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    assert data == {
        "providerKind": "openai",
        "config": {"hello": "world"},
        "description": "edit",
        "endpoint": "http://test.com",
    }

    response = await client.put(
        f"/api/v1/model-provider/{created_item_id}",
        json={
            "description": "edit2",
            "config": None,
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    assert data == {
        "providerKind": "openai",
        "config": None,
        "description": "edit2",
        "endpoint": "http://test.com",
    }

    response = await client.delete(
        f"/api/v1/model-provider/{created_item_id}",
        cookies={user[0]: user[1]})

    assert response.status_code == 204


@pytest.mark.asyncio
async def test_update_workspace_default_model(client, workspace, user):  # noqa
    response = await client.put(
        f"/api/v1/workspace/{workspace['id']}/default-model",
        json={
            "providerKind": "openai",
            "modelName": "chatgpt3.5",
            "modelType": "llm",
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    del data["id"]
    del data["createdAt"]
    del data['updatedAt']
    del data['createdBy']
    del data['updatedBy']
    assert data == {
        "modelName": "chatgpt3.5",
        "modelType": "llm",
        "providerKind": "openai",
    }

    response = await client.put(
        f"/api/v1/workspace/{workspace['id']}/default-model",
        json={
            "providerKind": "openai",
            "modelName": "chatgpt4.0",
            "modelType": "llm",
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    del data['id']
    del data["createdAt"]
    del data['updatedAt']
    del data['createdBy']
    del data['updatedBy']
    assert data == {
        "modelName": "chatgpt4.0",
        "modelType": "llm",
        "providerKind": "openai",
    }

    response = await client.put(
        f"/api/v1/workspace/{workspace['id']}/default-model",
        json={
            "providerKind": "openai",
            "modelName": "chatgpt4.0",
            "modelType": "llm",
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    del data['id']
    del data["createdAt"]
    del data['updatedAt']
    del data['createdBy']
    del data['updatedBy']
    assert data == {
        "modelName": "chatgpt4.0",
        "modelType": "llm",
        "providerKind": "openai",
    }

    response = await client.put(
        f"/api/v1/workspace/{workspace['id']}/default-model",
        json={
            "providerKind": "openai",
            "modelName": "s2t",
            "modelType": "embedding",
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200

    response = await client.get(
        f"/api/v1/workspace/{workspace['id']}/default-model",
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    assert len(data) == 2
    for item in data:
        if item['modelType'] == 'llm':
            assert item['modelName'] == 'chatgpt4.0'
        else:
            assert item['modelName'] == 's2t'


@pytest.mark.asyncio
async def test_update_group_default_model(client, group, user):  # noqa
    response = await client.put(
        f"/api/v1/group/{group['id']}/default-model",
        json={
            "providerKind": "openai",
            "modelName": "chatgpt3.5",
            "modelType": "llm",
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    del data['id']
    del data["createdAt"]
    del data['updatedAt']
    del data['createdBy']
    del data['updatedBy']
    assert data == {
        "modelName": "chatgpt3.5",
        "modelType": "llm",
        "providerKind": "openai",
    }

    response = await client.put(
        f"/api/v1/group/{group['id']}/default-model",
        json={
            "providerKind": "openai",
            "modelName": "chatgpt4.0",
            "modelType": "llm",
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    del data['id']
    del data["createdAt"]
    del data['updatedAt']
    del data['createdBy']
    del data['updatedBy']
    assert data == {
        "modelName": "chatgpt4.0",
        "modelType": "llm",
        "providerKind": "openai",
    }

    response = await client.put(
        f"/api/v1/group/{group['id']}/default-model",
        json={
            "providerKind": "openai",
            "modelName": "chatgpt4.0",
            "modelType": "llm",
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    del data['id']
    del data["createdAt"]
    del data['updatedAt']
    del data['createdBy']
    del data['updatedBy']
    assert data == {
        "modelName": "chatgpt4.0",
        "modelType": "llm",
        "providerKind": "openai",
    }

    response = await client.put(
        f"/api/v1/group/{group['id']}/default-model",
        json={
            "providerKind": "openai",
            "modelName": "s2t",
            "modelType": "embedding",
        },
        cookies={user[0]: user[1]})

    assert response.status_code == 200

    response = await client.get(
        f"/api/v1/group/{group['id']}/default-model",
        cookies={user[0]: user[1]})

    assert response.status_code == 200
    data = response.json()['data']
    assert len(data) == 2
    for item in data:
        if item['modelType'] == 'llm':
            assert item['modelName'] == 'chatgpt4.0'
        else:
            assert item['modelName'] == 's2t'


# Test list_models endpoint
@pytest.mark.asyncio
async def test_list_models(test_db, client):  # noqa
    response = await client.get("/api/v1/model")
    assert response.status_code == 200
    data = response.json()["data"]
    data = data[0]
    assert data['name'] == 'gpt-3.5-turbo-1106'
