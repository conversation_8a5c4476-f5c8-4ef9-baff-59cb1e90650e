import pytest
from . import user, root_user, test_db, client  # noqa
from ..service.app import workflow_clients


workflow_config_test = {
    "nodes": [
        {
            "id": "d5554fe1",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 100,
            "label": "文本组件",
            "category": "INPUT",
            "code": "INPUT_TEXT",
            "type": "INPUT",
            "icon": "FontSizeOutlined",
            "description": "用于输入/展示文本",
            "outputs": [
                {
                    "name": "type",
                    "type": "string",
                    "title": "类型",
                    "description": "风格类型"
                }
            ],
            "popoverContent": {
                "props": {
                    "name": "文本组件",
                    "description": "用于输入/展示文本"
                },
                "_store": {}
            },
            "x": -490,
            "y": -280,
            "ports": {
                "groups": {
                    "group1": {
                        "position": {
                            "name": "absolute"
                        }
                    }
                },
                "items": [
                    {
                        "type": "input",
                        "id": "d5554fe1-逻辑连接点-_logic_-input-0",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 25
                        },
                        "dataType": "_logic_",
                        "keyName": "逻辑连接点",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "逻辑连接点"
                        },
                        "attrs": {
                            "text": {
                                "text": ""
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "d5554fe1-type-string-output-0",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 64
                        },
                        "dataType": "string",
                        "keyName": "type",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "type"
                        },
                        "attrs": {
                            "text": {
                                "text": "type(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    }
                ]
            },
            "vars": []
        },
        {
            "id": "fcdddb09",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 100,
            "label": "文本组件",
            "category": "INPUT",
            "code": "INPUT_TEXT",
            "type": "INPUT",
            "icon": "FontSizeOutlined",
            "description": "用于输入/展示文本",
            "outputs": [
                {
                    "name": "description",
                    "type": "string",
                    "title": "描述",
                    "description": "文生图语言描述"
                }
            ],
            "popoverContent": {
                "props": {
                    "name": "文本组件",
                    "description": "用于输入/展示文本"
                },
                "_store": {}
            },
            "x": -190,
            "y": -505,
            "ports": {
                "groups": {
                    "group1": {
                        "position": {
                            "name": "absolute"
                        }
                    }
                },
                "items": [
                    {
                        "type": "input",
                        "id": "fcdddb09-逻辑连接点-_logic_-input-0",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 25
                        },
                        "dataType": "_logic_",
                        "keyName": "逻辑连接点",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "逻辑连接点"
                        },
                        "attrs": {
                            "text": {
                                "text": ""
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "fcdddb09-description-string-output-0",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 64
                        },
                        "dataType": "string",
                        "keyName": "description",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "description"
                        },
                        "attrs": {
                            "text": {
                                "text": "description(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    }
                ]
            },
            "vars": []
        },
        {
            "id": "19a422c8",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 100,
            "label": "输出节点",
            "category": "OUTPUT",
            "code": "OUTPUT",
            "type": "OUTPUT",
            "icon": "CodeOutlined",
            "description": "用于上传/展示图片",
            "inputs": [
                {
                    "name": "cover",
                    "type": "Image",
                    "title": "封面",
                    "description": "封面"
                }
            ],
            "popoverContent": {
                "props": {
                    "name": "输出节点",
                    "description": "用于上传/展示图片"
                },
                "_store": {}
            },
            "x": 1110,
            "y": -295,
            "ports": {
                "groups": {
                    "group1": {
                        "position": {
                            "name": "absolute"
                        }
                    }
                },
                "items": [
                    {
                        "type": "input",
                        "id": "19a422c8-逻辑连接点-_logic_-input-0",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 25
                        },
                        "dataType": "_logic_",
                        "keyName": "逻辑连接点",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "逻辑连接点"
                        },
                        "attrs": {
                            "text": {
                                "text": ""
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "input",
                        "id": "19a422c8-cover-Image-input-1",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 64
                        },
                        "dataType": "Image",
                        "keyName": "cover",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "Image",
                            "key": "cover"
                        },
                        "attrs": {
                            "text": {
                                "text": "cover(Image)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#3bedc9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    }
                ]
            },
            "vars": []
        },
        {
            "id": "f3156c59",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 190,
            "label": "Switch组件",
            "icon": "icon-fork",
            "category": "logic",
            "code": "SWITCH",
            "type": "SWITCH",
            "description": "可以根据输入条件，拆分成多条分支",
            "inputs": [
                {
                    "name": "switchVal",
                    "type": "string"
                }
            ],
            "vars": [
                {
                    "name": "类型",
                    "type": "string",
                    "value": "string",
                    "enum": ["string", "boolean", "number"]
                }
            ],
            "outputs": [
                {
                    "name": "default",
                    "type": "_logic_"
                },
                {
                    "name": "古风",
                    "type": "_logic_"
                },
                {
                    "name": "现代",
                    "type": "_logic_"
                }
            ],
            "popoverContent": {
                "props": {
                    "name": "Switch组件",
                    "description": "可以根据输入条件，拆分成多条分支"
                },
                "_store": {}
            },
            "x": -210,
            "y": -260,
            "ports": {
                "groups": {
                    "group1": {
                        "position": {
                            "name": "absolute"
                        }
                    }
                },
                "items": [
                    {
                        "type": "input",
                        "id": "f3156c59-逻辑连接点-_logic_-input-0",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 25
                        },
                        "dataType": "_logic_",
                        "keyName": "逻辑连接点",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "逻辑连接点"
                        },
                        "attrs": {
                            "text": {
                                "text": ""
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "input",
                        "id": "f3156c59-switchVal-string-input-1",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 64
                        },
                        "dataType": "string",
                        "keyName": "switchVal",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "switchVal"
                        },
                        "attrs": {
                            "text": {
                                "text": "switchVal(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "f3156c59-default-_logic_-output-0",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 94
                        },
                        "dataType": "_logic_",
                        "keyName": "default",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "default"
                        },
                        "attrs": {
                            "text": {
                                "text": "default"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "f3156c59-古风-_logic_-output-1",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 124
                        },
                        "dataType": "_logic_",
                        "keyName": "古风",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "古风"
                        },
                        "attrs": {
                            "text": {
                                "text": "古风"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "f3156c59-现代-_logic_-output-2",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 154
                        },
                        "dataType": "_logic_",
                        "keyName": "现代",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "现代"
                        },
                        "attrs": {
                            "text": {
                                "text": "现代"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    }
                ]
            }
        },
        {
            "id": "9d8e59dd",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 130,
            "label": "ChatGPT",
            "parentId": "5",
            "category": "AIGC",
            "code": "HTTP",
            "type": "HTTP",
            "icon": "MessageOutlined",
            "inputs": [
                {
                    "name": "text",
                    "type": "string"
                }
            ],
            "configs": [
                {
                    "name": "url",
                    "value": "aio://AIRSHIP_JNI_ASYNC_SERVICE/pyinfoprober"
                },
                {
                    "name": "method",
                    "value": "POST"
                }
            ],
            "outputs": [
                {
                    "name": "message",
                    "type": "string"
                }
            ],
            "popoverContent": {
                "props": {
                    "name": "ChatGPT",
                    "description": ""
                },
                "_store": {}
            },
            "x": 142,
            "y": -515,
            "ports": {
                "groups": {
                    "group1": {
                        "position": {
                            "name": "absolute"
                        }
                    }
                },
                "items": [
                    {
                        "type": "input",
                        "id": "9d8e59dd-逻辑连接点-_logic_-input-0",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 25
                        },
                        "dataType": "_logic_",
                        "keyName": "逻辑连接点",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "逻辑连接点"
                        },
                        "attrs": {
                            "text": {
                                "text": ""
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "input",
                        "id": "9d8e59dd-text-string-input-1",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 64
                        },
                        "dataType": "string",
                        "keyName": "text",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "text"
                        },
                        "attrs": {
                            "text": {
                                "text": "text(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "9d8e59dd-message-string-output-0",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 94
                        },
                        "dataType": "string",
                        "keyName": "message",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "message"
                        },
                        "attrs": {
                            "text": {
                                "text": "message(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    }
                ]
            },
            "vars": [
                {
                    "name": "temperature",
                    "type": "number",
                    "value": 0.8
                },
                {
                    "name": "template",
                    "type": "string",
                    "value": "模板"
                }
            ]
        },
        {
            "id": "36d69033",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 130,
            "label": "ChatGPT",
            "parentId": "5",
            "category": "AIGC",
            "code": "HTTP",
            "type": "HTTP",
            "icon": "MessageOutlined",
            "inputs": [
                {
                    "name": "text",
                    "type": "string"
                }
            ],
            "configs": [
                {
                    "name": "url",
                    "value": "aio://AIRSHIP_SYNC_SERVICE/aaaaa"
                },
                {
                    "name": "method",
                    "value": "POST"
                }
            ],
            "outputs": [
                {
                    "name": "message",
                    "type": "string"
                }
            ],
            "popoverContent": {
                "props": {
                    "name": "ChatGPT",
                    "description": ""
                },
                "_store": {}
            },
            "x": 142,
            "y": -135,
            "ports": {
                "groups": {
                    "group1": {
                        "position": {
                            "name": "absolute"
                        }
                    }
                },
                "items": [
                    {
                        "type": "input",
                        "id": "36d69033-逻辑连接点-_logic_-input-0",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 25
                        },
                        "dataType": "_logic_",
                        "keyName": "逻辑连接点",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "逻辑连接点"
                        },
                        "attrs": {
                            "text": {
                                "text": ""
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "input",
                        "id": "36d69033-text-string-input-1",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 64
                        },
                        "dataType": "string",
                        "keyName": "text",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "text"
                        },
                        "attrs": {
                            "text": {
                                "text": "text(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "36d69033-message-string-output-0",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 94
                        },
                        "dataType": "string",
                        "keyName": "message",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "message"
                        },
                        "attrs": {
                            "text": {
                                "text": "message(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    }
                ]
            },
            "vars": [
                {
                    "name": "temperature",
                    "type": "number",
                    "value": 0.5
                },
                {
                    "name": "template",
                    "type": "string",
                    "value": "模板"
                }
            ]
        },
        {
            "id": "e353bcdb",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 130,
            "label": "Select组件",
            "icon": "icon-merge",
            "description": "用于switch的多分支结果合并后进行后续处理",
            "category": "logic",
            "code": "SELECT",
            "type": "SELECT",
            "inputs": [
                {
                    "name": "message",
                    "type": "string"
                }
            ],
            "vars": [
                {
                    "name": "类型",
                    "type": "string",
                    "value": "boolean",
                    "enum": ["string", "boolean", "number"]
                }
            ],
            "outputs": [
                {
                    "name": "message",
                    "type": "string"
                }
            ],
            "popoverContent": {
                "props": {
                    "name": "Select组件",
                    "description": "用于switch的多分支结果合并后进行后续处理"
                },
                "_store": {}
            },
            "x": 450,
            "y": -325,
            "ports": {
                "groups": {
                    "group1": {
                        "position": {
                            "name": "absolute"
                        }
                    }
                },
                "items": [
                    {
                        "type": "input",
                        "id": "e353bcdb-逻辑连接点-_logic_-input-0",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 25
                        },
                        "dataType": "_logic_",
                        "keyName": "逻辑连接点",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "逻辑连接点"
                        },
                        "attrs": {
                            "text": {
                                "text": ""
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "input",
                        "id": "e353bcdb-message-string-input-1",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 64
                        },
                        "dataType": "string",
                        "keyName": "message",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "message"
                        },
                        "attrs": {
                            "text": {
                                "text": "message(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "e353bcdb-message-string-output-0",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 94
                        },
                        "dataType": "string",
                        "keyName": "message",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "message"
                        },
                        "attrs": {
                            "text": {
                                "text": "message(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    }
                ]
            }
        },
        {
            "id": "a1dee0b1",
            "renderKey": "ATOMIC_NODE",
            "width": 200,
            "height": 130,
            "label": "文本转图片",
            "parentId": "AIGC1",
            "icon": "ReloadOutlined",
            "category": "AIGC",
            "code": "HTTP",
            "type": "HTTP",
            "inputs": [
                {
                    "name": "text",
                    "type": "string"
                }
            ],
            "outputs": [
                {
                    "name": "pic",
                    "type": "Image"
                }
            ],
            "configs": [
                {
                    "name": "url",
                    "value": "http://music.163.com/test"
                },
                {
                    "name": "method",
                    "value": "POST"
                }
            ],
            "popoverContent": {
                "props": {
                    "name": "文本转图片",
                    "description": ""
                },
                "_store": {}
            },
            "x": 784,
            "y": -310,
            "ports": {
                "groups": {
                    "group1": {
                        "position": {
                            "name": "absolute"
                        }
                    }
                },
                "items": [
                    {
                        "type": "input",
                        "id": "a1dee0b1-逻辑连接点-_logic_-input-0",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 25
                        },
                        "dataType": "_logic_",
                        "keyName": "逻辑连接点",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "_logic_",
                            "key": "逻辑连接点"
                        },
                        "attrs": {
                            "text": {
                                "text": ""
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#bbb",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "input",
                        "id": "a1dee0b1-text-string-input-1",
                        "group": "group1",
                        "args": {
                            "x": 0,
                            "y": 64
                        },
                        "dataType": "string",
                        "keyName": "text",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "string",
                            "key": "text"
                        },
                        "attrs": {
                            "text": {
                                "text": "text(string)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#0929f9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    },
                    {
                        "type": "output",
                        "id": "a1dee0b1-pic-Image-output-0",
                        "group": "group1",
                        "args": {
                            "x": "100%",
                            "y": 94
                        },
                        "dataType": "Image",
                        "keyName": "pic",
                        "label": {
                            "position": "inside"
                        },
                        "data": {
                            "type": "Image",
                            "key": "pic"
                        },
                        "attrs": {
                            "text": {
                                "text": "pic(Image)"
                            },
                            "circle": {
                                "r": 6,
                                "stroke": "#3bedc9",
                                "strokeWidth": 2,
                                "fill": "#fff"
                            }
                        }
                    }
                ]
            }
        }
    ],
    "edges": [
        {
            "id": "d5554fe1:d5554fe1-type-string-output-0-f3156c59:f3156c59-switchVal-string-input-1",
            "targetPortId": "f3156c59-switchVal-string-input-1",
            "sourcePortId": "d5554fe1-type-string-output-0",
            "source": "d5554fe1",
            "target": "f3156c59",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "077d45da-0eed-49d9-9f04-6a2b79f44528",
                "source": {
                    "cell": "d5554fe1",
                    "port": "d5554fe1-type-string-output-0"
                },
                "target": {
                    "cell": "f3156c59",
                    "port": "f3156c59-switchVal-string-input-1"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "d5554fe1-type-string-output-0",
            "targetPort": "f3156c59-switchVal-string-input-1"
        },
        {
            "id": "f3156c59:f3156c59-古风-_logic_-output-1-9d8e59dd:9d8e59dd-逻辑连接点-_logic_-input-0",
            "targetPortId": "9d8e59dd-逻辑连接点-_logic_-input-0",
            "sourcePortId": "f3156c59-古风-_logic_-output-1",
            "source": "f3156c59",
            "target": "9d8e59dd",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "4ba7cc7a-cc4e-429a-905d-d092db7618cf",
                "source": {
                    "cell": "f3156c59",
                    "port": "f3156c59-古风-_logic_-output-1"
                },
                "target": {
                    "cell": "9d8e59dd",
                    "port": "9d8e59dd-逻辑连接点-_logic_-input-0"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "f3156c59-古风-_logic_-output-1",
            "targetPort": "9d8e59dd-逻辑连接点-_logic_-input-0"
        },
        {
            "id": "f3156c59:f3156c59-现代-_logic_-output-2-36d69033:36d69033-逻辑连接点-_logic_-input-0",
            "targetPortId": "36d69033-逻辑连接点-_logic_-input-0",
            "sourcePortId": "f3156c59-现代-_logic_-output-2",
            "source": "f3156c59",
            "target": "36d69033",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "d60944c8-5d63-4ef1-b4ac-3a5a9b7f09f5",
                "source": {
                    "cell": "f3156c59",
                    "port": "f3156c59-现代-_logic_-output-2"
                },
                "target": {
                    "cell": "36d69033",
                    "port": "36d69033-逻辑连接点-_logic_-input-0"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "f3156c59-现代-_logic_-output-2",
            "targetPort": "36d69033-逻辑连接点-_logic_-input-0"
        },
        {
            "id": "fcdddb09:fcdddb09-description-string-output-0-9d8e59dd:9d8e59dd-text-string-input-1",
            "targetPortId": "9d8e59dd-text-string-input-1",
            "sourcePortId": "fcdddb09-description-string-output-0",
            "source": "fcdddb09",
            "target": "9d8e59dd",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "2cccdc86-2f92-4ba9-973e-64a872d6f336",
                "source": {
                    "cell": "fcdddb09",
                    "port": "fcdddb09-description-string-output-0"
                },
                "target": {
                    "cell": "9d8e59dd",
                    "port": "9d8e59dd-text-string-input-1"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "fcdddb09-description-string-output-0",
            "targetPort": "9d8e59dd-text-string-input-1"
        },
        {
            "id": "fcdddb09:fcdddb09-description-string-output-0-36d69033:36d69033-text-string-input-1",
            "targetPortId": "36d69033-text-string-input-1",
            "sourcePortId": "fcdddb09-description-string-output-0",
            "source": "fcdddb09",
            "target": "36d69033",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "538ed011-7dd6-475e-b7ab-ca9f38804a86",
                "source": {
                    "cell": "fcdddb09",
                    "port": "fcdddb09-description-string-output-0"
                },
                "target": {
                    "cell": "36d69033",
                    "port": "36d69033-text-string-input-1"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "fcdddb09-description-string-output-0",
            "targetPort": "36d69033-text-string-input-1"
        },
        {
            "id": "a1dee0b1:a1dee0b1-pic-Image-output-0-19a422c8:19a422c8-cover-Image-input-1",
            "targetPortId": "19a422c8-cover-Image-input-1",
            "sourcePortId": "a1dee0b1-pic-Image-output-0",
            "source": "a1dee0b1",
            "target": "19a422c8",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "2f7cb64a-845a-4619-b5d1-74d68c994a2c",
                "source": {
                    "cell": "a1dee0b1",
                    "port": "a1dee0b1-pic-Image-output-0"
                },
                "target": {
                    "cell": "19a422c8",
                    "port": "19a422c8-cover-Image-input-1"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "a1dee0b1-pic-Image-output-0",
            "targetPort": "19a422c8-cover-Image-input-1"
        },
        {
            "id": "9d8e59dd:9d8e59dd-message-string-output-0-e353bcdb:e353bcdb-message-string-input-1",
            "targetPortId": "e353bcdb-message-string-input-1",
            "sourcePortId": "9d8e59dd-message-string-output-0",
            "source": "9d8e59dd",
            "target": "e353bcdb",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "50ba29b7-aa1f-40de-86a1-36ffead0e7dc",
                "source": {
                    "cell": "9d8e59dd",
                    "port": "9d8e59dd-message-string-output-0"
                },
                "target": {
                    "cell": "e353bcdb",
                    "port": "e353bcdb-message-string-input-1"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "9d8e59dd-message-string-output-0",
            "targetPort": "e353bcdb-message-string-input-1"
        },
        {
            "id": "36d69033:36d69033-message-string-output-0-e353bcdb:e353bcdb-message-string-input-1",
            "targetPortId": "e353bcdb-message-string-input-1",
            "sourcePortId": "36d69033-message-string-output-0",
            "source": "36d69033",
            "target": "e353bcdb",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "8e46cdb2-bf2a-4030-a659-6732eff359ed",
                "source": {
                    "cell": "36d69033",
                    "port": "36d69033-message-string-output-0"
                },
                "target": {
                    "cell": "e353bcdb",
                    "port": "e353bcdb-message-string-input-1"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "36d69033-message-string-output-0",
            "targetPort": "e353bcdb-message-string-input-1"
        },
        {
            "id": "e353bcdb:e353bcdb-message-string-output-0-a1dee0b1:a1dee0b1-text-string-input-1",
            "targetPortId": "a1dee0b1-text-string-input-1",
            "sourcePortId": "e353bcdb-message-string-output-0",
            "source": "e353bcdb",
            "target": "a1dee0b1",
            "edge": {
                "shape": "xflow-edge",
                "attrs": {
                    "line": {
                        "stroke": "#d5d5d5",
                        "strokeWidth": 1,
                        "targetMarker": "",
                        "strokeDasharray": ""
                    }
                },
                "zIndex": 1,
                "id": "3e73c1b5-f425-47e5-9ab2-069a2e976b13",
                "source": {
                    "cell": "e353bcdb",
                    "port": "e353bcdb-message-string-output-0"
                },
                "target": {
                    "cell": "a1dee0b1",
                    "port": "a1dee0b1-text-string-input-1"
                }
            },
            "connector": {
                "name": "rounded"
            },
            "sourcePort": "e353bcdb-message-string-output-0",
            "targetPort": "a1dee0b1-text-string-input-1"
        }
    ],
    "inputs": [
        [
            {
                "name": "type",
                "type": "string",
                "title": "类型",
                "description": "风格类型"
            }
        ],
        [
            {
                "name": "description",
                "type": "string",
                "title": "描述",
                "description": "文生图语言描述"
            }
        ]
    ],
    "outputs": [
        [
            {
                "name": "cover",
                "type": "Image",
                "title": "封面",
                "description": "封面"
            }
        ]
    ]
}


@pytest.mark.asyncio
async def test_workflow_client(user, root_user, client):  # noqa
    workflow_client = workflow_clients['f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea']
    assert workflow_client is not None
    '''
    # 1. test sumbit workflow
    test_workflow_id = "TEST_WORKFLOW"
    test_version = "f4f9f02f-4e5c-4f35-bf8e-9fda4aebfh3a"
    test_operator = "admin"
    resp = workflow_client.submit_workflow(test_workflow_id, test_version,
                                           json.dumps(workflow_config_test), test_operator)
    assert resp is not None

    # 2. test trigger workflow
    mock_trigger_request = {
        "flowId": test_workflow_id,
        "inputs": {
            "input1": "input1",
            "input2": 2
        },
        "callback": {
        },
        "bizContext": "business context",
    }
    resp = workflow_client.trigger_workflow_run(test_workflow_id, mock_trigger_request['inputs'],
                                                None, mock_trigger_request['callback'],
                                                mock_trigger_request['bizContext'])

    # 4. test delete workflow
    resp = workflow_client.delete_workflow(test_workflow_id)
    assert resp is not None
    '''
