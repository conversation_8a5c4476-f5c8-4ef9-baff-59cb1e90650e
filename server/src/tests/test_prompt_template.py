import pytest
from httpx import AsyncClient
from . import client, root_user, test_db  # 假设这些是在 conftest.py 中配置的（或者其他测试配置文件）


@pytest.mark.asyncio
async def test_save_prompt_template(client, root_user):  # noqa
    # 从 root_user fixture 获取需要的用户认证信息
    cookie_name = root_user[0]  # type: ignore
    cookie_val = root_user[1]  # type: ignore

    payload = {
        "workspaceId": "123",
        "groupId": "123213",
        "name": "默认模版",
        "avatarUrl": "https://xxx",
        "promptType": "struct",
        "prompt": "### 角色 你是一名画家 ### 背景 你服务于一家AI公司",
        "structPrompt": [
            {
                "title": "角色",
                "type": "role",
                "content": "你是一名画家"
            },
            {
                "title": "背景",
                "type": "bg",
                "content": "你服务于一家AI公司"
            }
        ]
    }

    # 发出 POST 请求到 /api/prompt-template/save
    response = await client.post("/api/prompt-template/save", json=payload, cookies={f"{cookie_name}": str(cookie_val)})

    # 验证响应
    assert response.status_code == 200
    data = response.json()
    assert "data" in data
    assert "id" in data["data"]
    assert "createdAt" in data["data"]
    assert "updatedAt" in data["data"]

    # 根据需要可以增加其他字段或内容的检查
    # 删除已创建的模板或其他清理操作如需要（例如对 GET API 的测试）
