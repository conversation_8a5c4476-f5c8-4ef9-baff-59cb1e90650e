import pytest

from . import user, root_user, test_db, client  # noqa

from ..cache.env_variable_cache import env_variable_cache
from ..models.env_variable import EnvVariable
from ..schema.env_variable import EnvVariableScopeType


@pytest.mark.asyncio
async def test_env_variable_cache(test_db):  # noqa
    app_id1 = "app_id1"
    group_id1 = "group_id1"
    workspace_id1 = "workspace_id1"
    component_id1 = "component_id1"
    component_env_var_name1 = "test-component-id1-env-var-name1"
    env_var_component_value1 = "test-value-component-value1"
    env_var_app_value1 = "test-value-app-value1"
    env_var_group_value1 = "test-value-group-value1"
    env_var_workspace_value1 = "test-value-workspace-value1"

    app_id2 = "app_id2"
    group_id2 = "group_id2"
    workspace_id2 = "workspace_id2"
    component_id2 = "component_id2"
    component_env_var_name2 = "test-component-id1-env-var-name2"
    env_var_component_value2 = "test-value-component-value2"
    env_var_app_value2 = "test-value-app-value2"
    env_var_group_value2 = "test-value-group-value2"
    env_var_workspace_value2 = "test-value-workspace-value2"

    component_values1 = [
        EnvVariable(
            name=component_env_var_name1,
            value=env_var_component_value1,
            param="test-param1",
            component_id=component_id1,
            scope_id=component_id1,
            scope_type=EnvVariableScopeType.COMPONENT.value),
    ]

    env_variable_cache.update(component_values=component_values1,
                              app_values={},
                              group_values={},
                              workspace_values={})
    env_values = env_variable_cache.get(components=[component_id1],
                                        app_id=app_id1,
                                        group_id=group_id1,
                                        workspace_id=workspace_id1)
    assert env_values[component_env_var_name1] == env_var_component_value1

    workspace_values1 = [
        EnvVariable(
            name=component_env_var_name1,
            value=env_var_workspace_value1,
            param="test-param1",
            component_id=component_id1,
            scope_id=workspace_id1,
            scope_type=EnvVariableScopeType.WORKSPACE.value),
    ]
    env_variable_cache.update(component_values=component_values1,
                              app_values={},
                              group_values={},
                              workspace_values=workspace_values1)
    env_values = env_variable_cache.get(components=[component_id1],
                                        app_id=app_id1,
                                        group_id=group_id1,
                                        workspace_id=workspace_id1)
    assert env_values[component_env_var_name1] == env_var_workspace_value1

    group_values1 = [
        EnvVariable(
            name=component_env_var_name1,
            value=env_var_group_value1,
            param="test-param1",
            component_id=component_id1,
            scope_id=group_id1,
            scope_type=EnvVariableScopeType.GROUP.value),
    ]
    env_variable_cache.update(component_values=component_values1,
                              app_values={},
                              group_values=group_values1,
                              workspace_values={})
    env_values = env_variable_cache.get(components=[component_id1],
                                        app_id=app_id1,
                                        group_id=group_id1,
                                        workspace_id=workspace_id1)
    assert env_values[component_env_var_name1] == env_var_group_value1
    app_values1 = [
        EnvVariable(
            name=component_env_var_name1,
            value=env_var_app_value1,
            param="test-param1",
            component_id=component_id1,
            scope_id=app_id1,
            scope_type=EnvVariableScopeType.APP.value),
    ]
    env_variable_cache.update(component_values=component_values1,
                              app_values=app_values1,
                              group_values={},
                              workspace_values={})
    env_values = env_variable_cache.get(components=[component_id1],
                                        app_id=app_id1,
                                        group_id=group_id1,
                                        workspace_id=workspace_id1)
    assert env_values[component_env_var_name1] == env_var_app_value1

    component_values2 = [
        EnvVariable(
            name=component_env_var_name2,
            value=env_var_component_value2,
            param="test-param2",
            component_id=component_id2,
            scope_id=component_id2,
            scope_type=EnvVariableScopeType.COMPONENT.value),
    ]
    app_values2 = [
        EnvVariable(
            name=component_env_var_name2,
            value=env_var_app_value2,
            param="test-param2",
            component_id=component_id2,
            scope_id=app_id2,
            scope_type=EnvVariableScopeType.APP.value),
    ]
    group_values2 = [
        EnvVariable(
            name=component_env_var_name2,
            value=env_var_group_value2,
            param="test-param2",
            component_id=component_id2,
            scope_id=group_id2,
            scope_type=EnvVariableScopeType.GROUP.value),
    ]
    workspace_values2 = [
        EnvVariable(
            name=component_env_var_name2,
            value=env_var_workspace_value2,
            param="test-param2",
            component_id=component_id2,
            scope_id=workspace_id2,
            scope_type=EnvVariableScopeType.WORKSPACE.value),
    ]
    env_variable_cache.update(component_values=component_values2,
                              app_values=app_values2,
                              group_values=group_values2,
                              workspace_values=workspace_values2)
    env_values = env_variable_cache.get(components=[component_id2],
                                        app_id=app_id2,
                                        group_id=group_id2,
                                        workspace_id=workspace_id2)
    assert env_values[component_env_var_name2] == env_var_app_value2

    env_values = env_variable_cache.get(components=[component_id1],
                                        app_id=app_id1,
                                        group_id=group_id1,
                                        workspace_id=workspace_id1)
    assert env_values[component_env_var_name1] == env_var_app_value1
