import pytest

from . import user, root_user, test_db, client  # noqa


@pytest.mark.asyncio
async def test_workflow_engine(user, root_user, client):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]
    root_cookie_val = root_user[1]

    ac = client
    test_workspace_id = "d144183b-1363-446f-99a3-12cddd767da8"
    test_workflow_engine_id = "f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea"
    admin_cookie = {f"{cookie_name}": str(root_cookie_val)}
    response = await ac.get("/api/v1/workspace/{0}/workflow-engines".format(test_workspace_id),
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1

    workflow_engine = response.json()['data']['items'][0]
    assert test_workspace_id == workflow_engine['workspaceID']
    assert workflow_engine['id'] == test_workflow_engine_id
    assert workflow_engine['name'] not in [None, ""]
    assert workflow_engine['endpoint'] not in [None, ""]
    assert workflow_engine['token'] in [None, ""]
    assert workflow_engine['description'] not in [None, ""]

    response = await ac.get("/api/v1/workflow-engines/{0}".format(test_workflow_engine_id),
                            cookies=admin_cookie)
    assert response.status_code == 200
    workflow_engine = response.json()['data']
    assert test_workspace_id == workflow_engine['workspaceID']
    assert workflow_engine['id'] == test_workflow_engine_id
    assert workflow_engine['name'] not in [None, ""]
    assert workflow_engine['endpoint'] not in [None, ""]
    assert workflow_engine['token'] in [None, ""]
    assert workflow_engine['description'] not in [None, ""]
    # get workflow engines with non-member user, test workspace id not found, so resp 404
    response = await ac.get("/api/v1/workflow-engines/{0}".format(test_workflow_engine_id),
                            cookies={f"{cookie_name}": cookie_val})

    assert response.status_code == 404

    # list workflow engine with non-member user, test workspace id not found, so resp 404
    response = await ac.get("/api/v1/workflow-engines/{0}".format(test_workspace_id),
                            cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 403
