import pytest

from . import user, external_user, root_user, test_db, client, workspace_admin_user, workspace  # noqa
from ..schema.component import ComponentType
from ..schema.common import Scope


@pytest.mark.asyncio
async def test_component(workspace, external_user, root_user, workspace_admin_user, client):  # noqa
    cookie_name = external_user[0]
    cookie_val = external_user[1]
    root_cookie_val = root_user[1]
    root_id = root_user[2]
    root_username = root_user[3]
    workspace_root_cookie_val = workspace_admin_user[1]

    ac = client
    workspace_id = str(workspace["id"])

    admin_cookie = {f"{cookie_name}": str(root_cookie_val)}
    test_category = "test-cateory"
    response = await ac.post("/api/v1/component-categories",
                             json={
                                 "name": test_category,
                                 "description": "test"},
                             cookies=admin_cookie)
    assert response.status_code == 201
    component_category = response.json()
    component_category = component_category['data']
    assert component_category['name'] == test_category
    assert component_category['description'] == "test"
    category_id = component_category['id']
    category_name = component_category['name']

    # create component with external user
    response = await ac.post(f'/api/v1/workspace/{workspace_id}/components',
                             json={
                                 "name": "test",
                                 "code": "TEST",
                                 "description": "test",
                                 "type": ComponentType.INPUT.value,
                                 "categoryID": category_id,
                                 "categoryName": category_name,
                                 "workspaceID": workspace_id,
                                 "scope": Scope.SCOPED.value,
                                 "config": {
                                     "inputs": [
                                         {
                                             "name": "test-1",
                                             "type": "string",
                                             "title": "test",
                                         },
                                         {
                                             "name": "test-2",
                                             "type": "string",
                                             "title": "test",
                                         }
                                     ],
                                     "configs": [
                                         {
                                             "name": "API_token1",
                                             "param": "x",
                                         },
                                         {
                                             "name": "API_token2",
                                             "param": "xxx",
                                         },
                                     ],
                                     "outputs": [
                                     ],
                                 },
                                 "envs": [
                                     {
                                         "name": "API_token1",
                                         "param": "x",
                                     },
                                     {
                                         "name": "API_token2",
                                         "param": "xxx",
                                     },
                                 ],
                             },
                             cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403
    assert response.json() == {
        'code': 'PermissionDenied',
        'message': 'You have no permission to access this resource'}

    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components',
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0
    assert response.json()['data']['items'] == []

    # create component with admin user
    response = await ac.post(f'/api/v1/workspace/{workspace_id}/components',
                             json={
                                 "name": "test",
                                 "code": "TEST",
                                 "description": "test",
                                 "type": ComponentType.INPUT.value,
                                 "categoryID": category_id,
                                 "category": category_name,
                                 "workspaceID": workspace_id,
                                 "scope": Scope.SCOPED.value,
                                 "config": {
                                     "code": "TEST",
                                     "inputs": [
                                         {
                                             "name": "test-1",
                                             "type": "string",
                                             "title": "test",
                                         },
                                         {
                                             "name": "test-2",
                                             "type": "string",
                                             "title": "test",
                                         }
                                     ],
                                     "outputs": [
                                     ],
                                 },
                                 "appTypes": ['agent', 'workflow'],
                             },
                             cookies=admin_cookie)
    assert response.status_code == 201
    component = response.json()
    component = component['data']
    component_id = component['id']
    assert component['name'] == "test"
    assert component['description'] == "test"
    assert component['config']["inputs"][0]["name"] == "test-1"
    assert component['config']["id"] == component_id
    assert component['config']["outputs"] == []

    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components',
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1
    component = response.json()['data']['items'][0]
    assert component['category'] == test_category

    response = await ac.get(f'/api/v1/components/{component_id}',
                            cookies=admin_cookie)
    assert response.status_code == 200

    # update component with non-member user
    response = await ac.put(f"/api/v1/components/{component_id}",
                            json={
                                "name": "test1",
                                "categoryID": category_id,
                                "workspaceID": workspace_id,
                            },
                            cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403

    root_cookie_val_2 = root_user[1]
    root_id_2 = root_user[2]
    root_username_2 = root_user[3]

    # update component name with another admin user
    response = await ac.put(f"/api/v1/components/{component_id}",
                            json={
                                "name": "test1",
                                "code": "TEST",
                                "type": ComponentType.INPUT.value,
                                "categoryID": category_id,
                                "category": category_name,
                                "scope": Scope.SCOPED.value,
                                "config": {
                                    "code": "TEST",
                                    "inputs": [
                                        {
                                            "name": "test-1",
                                            "type": "string",
                                            "title": "test",
                                        },
                                        {
                                            "name": "test-2",
                                            "type": "string",
                                            "title": "test",
                                        }
                                    ],
                                    "outputs": [
                                    ],
                                }
                            },
                            cookies={f"{cookie_name}": str(root_cookie_val_2)})

    assert response.status_code == 200
    component = response.json()['data']
    assert component_id == component['id']
    assert component['name'] == 'test1'
    assert component['createdBy']['id'] == root_id
    assert component['createdBy']['fullname'] == root_username
    assert component['updatedBy']['id'] == root_id_2
    assert component['updatedBy']['fullname'] == root_username_2

    # update component description with admin user
    response = await ac.put(f"/api/v1/components/{component_id}",
                            json={
                                "description": "test2",
                                "name": "test1",
                                "code": "TEST",
                                "type": ComponentType.INPUT.value,
                                "categoryID": category_id,
                                "category": category_name,
                                "scope": Scope.SCOPED.value,
                                "config": {
                                    "code": "TEST",
                                    "inputs": [
                                        {
                                            "name": "test-1",
                                            "type": "string",
                                            "title": "test",
                                        },
                                        {
                                            "name": "test-2",
                                            "type": "string",
                                            "title": "test",
                                        }
                                    ],
                                    "outputs": [
                                    ],
                                }
                            },
                            cookies=admin_cookie)

    assert response.status_code == 200
    component = response.json()['data']
    assert component_id == component['id']
    assert component['name'] == 'test1'
    assert component['description'] == 'test2'
    assert component['createdBy']['id'] == root_id
    assert component['createdBy']['fullname'] == root_username
    assert component['updatedBy']['id'] == root_id
    assert component['updatedBy']['fullname'] == root_username

    response = await ac.put(f"/api/v1/components/{component_id}",
                            json={
                                "name": "test",
                                "code": "TEST",
                                "description": "test",
                                "categoryID": category_id,
                                "category": category_name,
                                "type": ComponentType.INPUT.value,
                                "scope": Scope.SCOPED.value,
                                "config": {
                                    "code": "TEST",
                                    "inputs": [
                                        {
                                            "name": "test-1",
                                            "type": "string",
                                            "title": "test",
                                        },
                                        {
                                            "name": "test-2",
                                            "type": "string",
                                            "title": "test",
                                        }
                                    ],
                                    "outputs": [
                                    ],
                                }
                            },
                            cookies=admin_cookie)

    assert response.status_code == 200

    # get component with admin user
    response = await ac.get(f"/api/v1/components/{component_id}",
                            cookies=admin_cookie)

    assert response.status_code == 200
    component = response.json()['data']
    assert component_id == component['id']
    assert component['name'] == 'test'
    assert component['description'] == 'test'
    assert component['createdBy']['id'] == root_id
    assert component['createdBy']['fullname'] == root_username
    assert component['updatedBy']['id'] == root_id
    assert component['updatedBy']['fullname'] == root_username

    # get component with non-member user
    response = await ac.get(f"/api/v1/components/{component_id}",
                            cookies={f"{cookie_name}": cookie_val})

    assert response.status_code == 403

    # get component with another admin user
    response = await ac.get(f"/api/v1/components/{component_id}",
                            cookies={
                                f"{cookie_name}": root_cookie_val_2})

    assert response.status_code == 200

    # delete component with non-member user
    response = await ac.delete(f"/api/v1/components/{component_id}",
                               cookies={
                                   f"{cookie_name}": cookie_val})
    assert response.status_code == 403

    # delete component with another admin user
    response = await ac.delete(f"/api/v1/components/{component_id}",
                               cookies={
                                   f"{cookie_name}": root_cookie_val_2})
    assert response.status_code == 204

    # delete the deleted component with admin user
    response = await ac.delete(f"/api/v1/components/{component_id}",
                               cookies=admin_cookie)
    assert response.status_code == 404

    # create component with workspace admin user
    workspace_admin_cookie = {f"{cookie_name}": str(workspace_root_cookie_val)}
    response = await ac.post(f'/api/v1/workspace/{workspace_id}/components',
                             json={
                                 "name": "test-1",
                                 "code": "TEST-1",
                                 "description": "test",
                                 "categoryID": category_id,
                                 "category": category_name,
                                 "workspaceID": workspace_id,
                                 "type": ComponentType.INPUT.value,
                                 "scope": Scope.SCOPED.value,
                                 "config": {
                                     "code": "TEST-1",
                                     "inputs": [
                                         {
                                             "name": "test-1",
                                             "type": "string",
                                             "title": "test",
                                         },
                                         {
                                             "name": "test-2",
                                             "type": "string",
                                             "title": "test",
                                         }
                                     ],
                                     "outputs": [
                                     ],
                                 }
                             },
                             cookies=workspace_admin_cookie)
    assert response.status_code == 201

    # list components with workspace admin user
    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components?categories=',
                            cookies=workspace_admin_cookie)

    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1

    # list components with workspace admin user
    response = await ac.get(f'/api/v1/workspace/{workspace_id}/components?categories={test_category}',
                            cookies=workspace_admin_cookie)

    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1

    # get component with workspace admin user
    component_id = response.json()['data']['items'][0]['id']
    response = await ac.get(f'/api/v1/components/{component_id}',
                            cookies=workspace_admin_cookie)
    assert response.status_code == 200

    # update component with workspace admin user
    response = await ac.put(f"/api/v1/components/{component_id}",
                            json={
                                "name": "test-1",
                                "code": "TEST-1",
                                "description": "test test",
                                "categoryID": category_id,
                                "category": category_name,
                                "workspaceID": workspace_id,
                                "type": ComponentType.INPUT.value,
                                "scope": Scope.SCOPED.value,
                                "config": {
                                    "code": "TEST-1",
                                    "inputs": [
                                        {
                                            "name": "test-1",
                                            "type": "string",
                                            "title": "test",
                                        },
                                        {
                                            "name": "test-2",
                                            "type": "string",
                                            "title": "test",
                                        }
                                    ],
                                    "outputs": [
                                    ],
                                }
                            },
                            cookies=workspace_admin_cookie)
    assert response.status_code == 200
