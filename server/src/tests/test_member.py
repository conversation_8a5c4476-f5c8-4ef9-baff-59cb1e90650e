from typing import Any
import pytest

from ..schema.member import Role

from . import test_db, user, root_user, client, external_user, workspace  # noqa


@pytest.mark.asyncio
async def test_member_list(user, client, workspace, external_user):  # noqa
    response = await client.get(f"/api/v1/workspace/{workspace['id']}/member",
                                cookies={f"{user[0]}": user[1]})

    assert response.status_code == 200

    data = response.json()['data']
    assert data['total'] == 2

    for member in data['items']:
        assert member['role'] == Role.ADMIN.value

    response = await client.put(f"/api/v1/workspace/{workspace['id']}/member",
                                json={
                                    "memberID": external_user[2],
                                    "role": Role.ADMIN.value,
                                },
                                cookies={f"{user[0]}": user[1]})

    data = response.json()
    assert response.status_code == 200

    response = await client.get(f"/api/v1/workspace/{workspace['id']}/member",
                                cookies={f"{user[0]}": user[1]})

    assert response.status_code == 200

    data = response.json()['data']
    assert data['total'] == 3

    member: Any = None
    for m in data['items']:
        if m['memberID'] == external_user[2]:
            member = m
            break

    assert member is not None
    assert member['role'] == Role.ADMIN.value
    assert member['grantedBy']['id'] == user[2]

    response = await client.put(f"/api/v1/workspace/{workspace['id']}/member",
                                json={
                                    "memberID": external_user[2],
                                    "role": Role.DEVELOPER.value,
                                },
                                cookies={f"{user[0]}": user[1]})

    data = response.json()
    assert response.status_code == 200

    response = await client.get(f"/api/v1/workspace/{workspace['id']}/member",
                                cookies={f"{user[0]}": user[1]})

    assert response.status_code == 200

    data = response.json()['data']
    assert data['total'] == 3

    member: Any = None
    for m in data['items']:
        if m['memberID'] == external_user[2]:
            member = m
            break

    assert member is not None
    assert member['role'] == Role.DEVELOPER.value
    assert member['grantedBy']['id'] == user[2]

    response = await client.delete(
        f"/api/v1/workspace/{workspace['id']}/member",
        params={
            "memberID": external_user[2],
        },
        cookies={f"{user[0]}": user[1]})
    assert response.status_code == 204

    response = await client.get(f"/api/v1/workspace/{workspace['id']}/member",
                                cookies={f"{user[0]}": user[1]})

    assert response.status_code == 200

    data = response.json()['data']
    assert data['total'] == 2
