import pytest

from . import user, root_user, test_db, client  # noqa


@pytest.mark.asyncio
async def test_component_category(user, root_user, client):  # noqa
    cookie_name = user[0]
    cookie_val = user[1]
    root_cookie_val = root_user[1]
    root_id = root_user[2]
    root_username = root_user[3]
    admin_cookie = {f"{cookie_name}": str(root_cookie_val)}

    ac = client
    # create component category with non-admin user
    response = await ac.post("/api/v1/component-categories",
                             json={
                                 "name": "test",
                                 "description": "test"
                             },
                             cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403
    assert response.json() == {
        'code': 'PermissionDenied',
        'message': 'You have no permission to access this resource'}

    response = await ac.get("/api/v1/component-categories",
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 0
    assert response.json()['data']['items'] == []

    response = await ac.post("/api/v1/component-categories",
                             json={
                                 "name": "test",
                                 "description": "test"},
                             cookies=admin_cookie)
    assert response.status_code == 201
    component_category = response.json()
    component_category = component_category['data']
    assert component_category['name'] == "test"
    assert component_category['description'] == "test"

    response = await ac.get("/api/v1/component-categories",
                            cookies=admin_cookie)
    assert response.status_code == 200
    assert response.json()['data']['total'] == 1
    assert len(response.json()['data']['items']) == 1

    component_category_id = response.json()['data']['items'][0]['id']

    # update component category with non-member user
    response = await ac.put(f"/api/v1/component-categories/{component_category_id}",
                            json={"name": "test1"},
                            cookies={f"{cookie_name}": str(cookie_val)})
    assert response.status_code == 403

    root_cookie_val_2 = root_user[1]
    root_id_2 = root_user[2]
    root_username_2 = root_user[3]

    # update component category name with another admin user
    response = await ac.put(f"/api/v1/component-categories/{component_category_id}",
                            json={"name": "test1"},
                            cookies={f"{cookie_name}": str(root_cookie_val_2)})

    assert response.status_code == 200
    component_category = response.json()['data']
    assert component_category_id == component_category['id']
    assert component_category['name'] == 'test1'
    assert component_category['description'] == 'test'
    assert component_category['createdBy']['id'] == root_id
    assert component_category['createdBy']['fullname'] == root_username
    assert component_category['updatedBy']['id'] == root_id_2
    assert component_category['updatedBy']['fullname'] == root_username_2

    # update component category description with admin user
    response = await ac.put(f"/api/v1/component-categories/{component_category_id}",
                            json={"description": "test2"},
                            cookies=admin_cookie)

    assert response.status_code == 200
    component_category = response.json()['data']
    assert component_category_id == component_category['id']
    assert component_category['name'] == 'test1'
    assert component_category['description'] == 'test2'
    assert component_category['createdBy']['id'] == root_id
    assert component_category['createdBy']['fullname'] == root_username
    assert component_category['updatedBy']['id'] == root_id
    assert component_category['updatedBy']['fullname'] == root_username

    response = await ac.put(f"/api/v1/component-categories/{component_category_id}",
                            json={
                                "name": "test",
                                "description": "test"
                            },
                            cookies=admin_cookie)

    assert response.status_code == 200

    # get component category with admin user
    response = await ac.get(f"/api/v1/component-categories/{component_category_id}",
                            cookies=admin_cookie)

    assert response.status_code == 200
    component_category = response.json()['data']
    assert component_category_id == component_category['id']
    assert component_category['name'] == 'test'
    assert component_category['description'] == 'test'
    assert component_category['createdBy']['id'] == root_id
    assert component_category['createdBy']['fullname'] == root_username
    assert component_category['updatedBy']['id'] == root_id
    assert component_category['updatedBy']['fullname'] == root_username

    # get component category with non-member user
    response = await ac.get(f"/api/v1/component-categories/{component_category_id}",
                            cookies={f"{cookie_name}": cookie_val})

    assert response.status_code == 200

    # list component category with non-member user
    response = await ac.get("/api/v1/component-categories",
                            cookies={f"{cookie_name}": cookie_val})

    assert response.status_code == 200

    # get component category with another admin user
    response = await ac.get(f"/api/v1/component-categories/{component_category_id}",
                            cookies={f"{cookie_name}": root_cookie_val_2})

    assert response.status_code == 200

    # delete component category with non-member user
    response = await ac.delete(f"/api/v1/component-categories/{component_category_id}",
                               cookies={f"{cookie_name}": cookie_val})
    assert response.status_code == 403

    # delete component category with another admin user
    response = await ac.delete(f"/api/v1/component-categories/{component_category_id}",
                               cookies={f"{cookie_name}": root_cookie_val_2})
    assert response.status_code == 204

    # delete the deleted component category with admin user
    response = await ac.delete(f"/api/v1/component-categories/{component_category_id}",
                               cookies=admin_cookie)

    assert response.status_code == 404
