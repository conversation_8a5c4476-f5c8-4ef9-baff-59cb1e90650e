import pytest

from . import user, root_user, test_db, client  # noqa
from ..config import settings


@pytest.mark.asyncio
async def test_pprof(client): # noqa
    settings.enable_profile = True

    resp = await client.get(url="/api/v1/pprof/trace-malloc/start")
    assert resp.status_code == 200

    resp = await client.get(url="/api/v1/pprof/trace-malloc/reset")
    assert resp.status_code == 200

    resp = await client.get(url="/api/v1/pprof/trace-malloc/compare")
    assert resp.status_code == 200

    resp = await client.get(url="/api/v1/pprof/trace-malloc/topk")
    assert resp.status_code == 200

    resp = await client.get(url="/api/v1/pprof/trace-malloc/topk")
    assert resp.status_code == 200

    resp = await client.get(url="/api/v1/pprof/objgraph/most-common-types")
    assert resp.status_code == 200

    resp = await client.get(url="/api/v1/pprof/objgraph/growth")
    assert resp.status_code == 200
