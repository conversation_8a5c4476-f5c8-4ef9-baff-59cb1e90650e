import httpx
import json
import re
from typing import Dict, Optional
from ..config import settings

# 配置
API_URL = 'http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes/api/langbase/agent/completion'
CHATFLOW_API_URL = 'http://music-langbase-service-online-gz.yf-online-gy1.service.gy.ntes/api/langbase/agent/workflow'
APP_ID = settings.popo_score_app_id
API_TOKEN = '2v6Mt7VEhueSc8bEfeeWArGVKxy56kAIjLLHGxtz_4o'
CHATFLOW_APP_ID = settings.popo_score_chatflow_app_id


async def call_chatflow_api(message: str, extra_params: Optional[Dict] = None, app_id: Optional[str] = None) -> Dict:
    """
    调用大模型评分 API

    Args:
        text_content: 文档文本内容

    Returns:
        API 响应的 JSON 结果
    """
    headers = {
        'Authorization': f'Bearer {API_TOKEN}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    data = {
        'inputs': {
            'message': message,
            **(extra_params or {})
        },
        'appId': app_id or CHATFLOW_APP_ID,
        'userId': '123'
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(CHATFLOW_API_URL, headers=headers, json=data, timeout=1000)
        return response.json()


async def call_completion_api(text_content: str, app_id: Optional[str] = None, api_token: Optional[str] = None, input_key: Optional[str] = None) -> Dict:
    """
    调用大模型评分 API

    Args:
        text_content: 文档文本内容

    Returns:
        API 响应的 JSON 结果
    """
    headers = {
        'Authorization': f'Bearer {api_token or API_TOKEN}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

    data = {
        'parameters': {},
        'appId': app_id or APP_ID,
        'userId': '123'
    }
    if input_key:
        data['parameters'][input_key] = text_content
    else:
        data['parameters']['document'] = text_content

    async with httpx.AsyncClient() as client:
        response = await client.post(API_URL, headers=headers, json=data, timeout=1000)
        return response.json()


def process_result(result: dict, raw: bool = False) -> Dict:
    """
    处理大模型评分 API 的返回结果

    Args:
        result: API 返回的结果

    Returns:
        处理后的结构化数据
    """
    # 初始化数据
    content = None
    ai_score = 0
    total_reason = ""
    advice = ""
    scores_data = {}
    result_json = result

    # 检查是否存在 data?.messageList?[0]?.content
    if 'data' in result_json and result_json['data']:
        data = result_json['data']
        if 'messageList' in data and data['messageList'] and len(data['messageList']) > 0:
            if 'content' in data['messageList'][0]:
                raw_content = data['messageList'][0]['content']

                # 去除markdown符号 ```code```
                if raw_content:
                    # 替换开始标记 ```语言名称
                    content_text = re.sub(r'```(\w*)', '', raw_content)
                    # 替换结束标记 ```
                    content_text = re.sub(r'```', '', content_text)

                    # 尝试将处理后的内容转换为JSON
                    try:
                        # 去除可能的前导和尾随空白
                        cleaned_content = content_text.strip()
                        content_json = json.loads(cleaned_content)
                        content = content_json  # 如果成功转换，使用转换后的JSON对象

                        # 如果raw为True，直接返回content
                        if raw:
                            return content

                        # 提取scores数据
                        if 'scores' in content:
                            scores = content['scores']
                            scores_data = scores  # 保存完整的scores数据

                            # 计算ai_score
                            total_score = 0
                            total_weight = 0

                            for category, score_data in scores.items():
                                if 'score' in score_data and 'weight' in score_data:
                                    total_score += score_data['score'] * \
                                        score_data['weight']
                                    total_weight += score_data['weight']

                            if total_weight > 0:
                                ai_score = round(total_score / total_weight, 2)

                        # 提取totalReason和advice
                        if 'evaluation' in content:
                            total_reason = content['evaluation']
                        if 'advice' in content:
                            advice = content['advice']

                    except json.JSONDecodeError:
                        # 如果不是有效的JSON，保留原始处理后的字符串
                        content = cleaned_content
                        if raw:
                            return {
                                'error': True,
                                'error_message': 'JSONDecodeError',
                                'json_content': cleaned_content,
                                'raw_content': raw_content
                            }

    return {
        'score': ai_score,
        'reason': total_reason,
        'advice': advice,
        'scores_data': scores_data
    }
