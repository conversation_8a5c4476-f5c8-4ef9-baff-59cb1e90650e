from typing import Tuple, List, Literal
from sqlalchemy.orm import Session
import json

from ..misc.redis_utils import ENV_OPERATION_CACHE_KEY, set_redis_sync, get_redis_sync

from ..misc.errors import NotFound, LangbaseException, BadRequest
from datetime import datetime
from ..schema.env_variable import EnvVariableScopeType, EnvVariableCreate, \
    EnvVariableUpdate
from ..models.env_variable import EnvVariable


async def get(db: Session, env_variable_id: str) -> EnvVariable:
    db_env_variable = db.query(EnvVariable) \
        .filter(EnvVariable.id == env_variable_id).first()
    if db_env_variable is None:
        raise NotFound(f"EnvVariable({env_variable_id}) not found")
    return db_env_variable


async def deleteEnvs(db: Session,
                     env_variable_ids: List[str]):
    db.query(EnvVariable).filter(EnvVariable.id.in_(env_variable_ids)).delete()
    db.commit()
    await update_env_operation_cache("remove", env_variable_ids)


async def delete(db: Session,
                 user_id: str,
                 env_variable_id: str):
    env = await get(db, str(env_variable_id))
    env_values_count = db.query(EnvVariable).filter(
        EnvVariable.name == env.name).count()
    if env.scope_type != EnvVariableScopeType.COMPONENT:
        db.query(EnvVariable).filter(
            EnvVariable.id == env_variable_id).delete()
        db.commit()
        await update_env_operation_cache("remove", [env_variable_id])
    elif env_values_count == 1:
        db.query(EnvVariable).filter(EnvVariable.id == env_variable_id).update(
            {EnvVariable.deleted_at: datetime.now(), EnvVariable.deleted_by: user_id}
        )
        db.commit()
        await update_env_operation_cache("remove", [env_variable_id])
    else:
        raise LangbaseException(
            "Can't delete component env variable if there are more than one values")


async def update(db: Session,
                 user_id: str,
                 env_variable_id: str,
                 env_variable_update: EnvVariableUpdate):
    db_env_variable = await get(db, env_variable_id)
    columns = env_variable_update.model_dump(exclude_unset=True)
    columns[EnvVariable.updated_by] = user_id
    db.query(EnvVariable).filter(EnvVariable.id == env_variable_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_env_variable)
    await update_env_operation_cache("update", [env_variable_id])
    return db_env_variable


async def list_component_env_variable(db: Session,
                                      component_id: str) \
        -> Tuple[List[EnvVariable], int]:
    stat = db.query(EnvVariable).filter(EnvVariable.component_id == component_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.COMPONENT.value)
    component_envs = stat.all()

    return component_envs, len(component_envs)


async def list_workspace_component_env_variable(db: Session,
                                                component_id: str,
                                                workspace_id: str) -> Tuple[List[EnvVariable], int]:
    stat = db.query(EnvVariable).filter(EnvVariable.component_id == component_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.COMPONENT.value)
    component_envs = stat.all()
    stat = db.query(EnvVariable).filter(EnvVariable.component_id == component_id,
                                        EnvVariable.scope_id == workspace_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.WORKSPACE.value)
    workspace_envs = stat.all()
    for env in workspace_envs:
        for component_env in component_envs:
            if env.name == component_env.name:
                component_envs.remove(component_env)
                component_envs.append(env)

    return component_envs, len(component_envs)


async def list_group_component_env_variable(db: Session,
                                            component_id: str,
                                            group_id: str,
                                            workspace_id: str) -> Tuple[List[EnvVariable], int]:
    stat = db.query(EnvVariable).filter(EnvVariable.component_id == component_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.COMPONENT.value)
    component_envs = stat.all()
    stat = db.query(EnvVariable).filter(EnvVariable.component_id == component_id,
                                        EnvVariable.scope_id == workspace_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.WORKSPACE.value)
    workspace_envs = stat.all()
    for env in workspace_envs:
        for component_env in component_envs:
            if env.name == component_env.name:
                component_envs.remove(component_env)
                component_envs.append(env)

    stat = db.query(EnvVariable).filter(EnvVariable.component_id == component_id,
                                        EnvVariable.scope_id == group_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.GROUP.value)
    group_envs = stat.all()
    for env in group_envs:
        for component_env in component_envs:
            if env.name == component_env.name:
                component_envs.remove(component_env)
                component_envs.append(env)

    return component_envs, len(component_envs)


async def list_app_component_env_variable(db: Session,
                                          components: List[str],
                                          app_id: str,
                                          group_id: str,
                                          workspace_id: str) -> Tuple[List[EnvVariable], int]:
    stat = db.query(EnvVariable).filter(EnvVariable.component_id.in_(components),
                                        EnvVariable.scope_type == EnvVariableScopeType.COMPONENT.value)
    component_envs = stat.all()
    stat = db.query(EnvVariable).filter(EnvVariable.component_id.in_(components),
                                        EnvVariable.scope_id == workspace_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.WORKSPACE.value)
    workspace_envs = stat.all()
    for env in workspace_envs:
        for component_env in component_envs:
            if env.name == component_env.name:
                component_envs.remove(component_env)
                component_envs.append(env)

    stat = db.query(EnvVariable).filter(EnvVariable.component_id.in_(components),
                                        EnvVariable.scope_id == group_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.GROUP.value)
    group_envs = stat.all()
    for env in group_envs:
        for component_env in component_envs:
            if env.name == component_env.name:
                component_envs.remove(component_env)
                component_envs.append(env)

    stat = db.query(EnvVariable).filter(EnvVariable.component_id.in_(components),
                                        EnvVariable.scope_id == app_id,
                                        EnvVariable.scope_type == EnvVariableScopeType.APP.value)
    app_envs = stat.all()
    for env in app_envs:
        for component_env in component_envs:
            if env.name == component_env.name:
                component_envs.remove(component_env)
                component_envs.append(env)

    return component_envs, len(component_envs)


async def create_component_env_variable(db: Session,
                                        scope_id: str,
                                        scope_type: EnvVariableScopeType,
                                        env_variable_create: EnvVariableCreate,
                                        user_id: str):
    env_variable = EnvVariable(**env_variable_create.model_dump())
    env_variable.scope_id = scope_id
    env_variable.scope_type = scope_type.value
    env_variable.created_by = user_id
    env_variable.updated_by = user_id
    db.add(env_variable)
    db.commit()
    db.refresh(env_variable)
    await update_env_operation_cache("create", [env_variable.id])
    return env_variable


async def create_component_env_variables(db: Session,
                                         env_variables: List[EnvVariableCreate],
                                         user_id: str):
    if env_variables is None or len(env_variables) == 0:
        return

    env_names = []
    env_names_map = {}
    for env in env_variables:
        env_names.append(env.name)
        if env.name in env_names_map:
            raise BadRequest("Env variable name already exists")
        env_names_map[env.name] = env

    envs = db.query(EnvVariable).filter(EnvVariable.name.in_(env_names),
                                        EnvVariable.scope_type == EnvVariableScopeType.COMPONENT.value).all()
    if envs is not None and len(envs) > 0:
        raise BadRequest("Env variable name already exists")

    created_ids = []
    for env in env_variables:
        env_variable = EnvVariable(**env.model_dump())
        env_variable.created_by = user_id
        env_variable.updated_by = user_id
        env_variable.scope_id = env.component_id
        env_variable.scope_type = EnvVariableScopeType.COMPONENT.value
        db.add(env_variable)
        created_ids.append(env_variable.id)

    db.commit()
    await update_env_operation_cache("create", created_ids)


async def check_app_env_variable_values_setting(db: Session,
                                                components: List[str],
                                                workspace_id: str,
                                                group_id: str,
                                                app_id: str):
    if components is None or len(components) == 0:
        return True

    stat = db.query(EnvVariable)
    stat = stat.filter(EnvVariable.component_id.in_(components))
    component_values = stat.all()
    envs = []
    values = {}
    for value in component_values:
        values[value.name] = value.value
        envs.append(value.name)

    stat = db.query(EnvVariable)
    stat = stat.filter(EnvVariable.scope_id == workspace_id,
                       EnvVariable.name.in_(envs),
                       EnvVariable.scope_type == EnvVariableScopeType.WORKSPACE.value)
    workspace_values = stat.all()
    for value in workspace_values:
        if value.name in values:
            values[value.name] = value.value

    stat = db.query(EnvVariable)
    stat = stat.filter(EnvVariable.scope_id == group_id,
                       EnvVariable.name.in_(envs),
                       EnvVariable.scope_type == EnvVariableScopeType.GROUP.value)
    group_values = stat.all()
    for value in group_values:
        if value.name in values:
            values[value.name] = value.value

    stat = db.query(EnvVariable)
    stat = stat.filter(EnvVariable.scope_id == app_id,
                       EnvVariable.name.in_(envs),
                       EnvVariable.scope_type == EnvVariableScopeType.APP.value)
    app_values = stat.all()
    for value in app_values:
        if value.name in values:
            values[value.name] = value.value

    not_setting_envs = []
    for key, value in values.items():
        if value is None or value == "":
            not_setting_envs.append(key)

    if len(not_setting_envs) > 0:
        raise LangbaseException(
            f"EnvVariable({','.join(not_setting_envs)}) not setting")


def list_latest_update_variable_values(db: Session,
                                       scope_type: str,
                                       query_update_time: datetime = None) -> list[EnvVariable]:
    offset = 0
    limit = 100
    size = limit
    values = []
    while size >= limit:
        stat = db.query(EnvVariable)
        stat = stat.filter(EnvVariable.scope_type == scope_type)
        if query_update_time is not None:
            stat = stat.filter(EnvVariable.updated_at > query_update_time)
        stat = stat.offset(offset).limit(limit)
        list_values = stat.all()
        values.extend(list_values)
        size = len(list_values)

    return values


async def update_env_operation_cache(operation: Literal["create", "update", "remove"], env_ids: List[str]):
    """更新 Redis 中的环境变量操作缓存

    Args:
        operation: 操作类型，可以是 "create", "update" 或 "remove"
        env_ids: 环境变量 ID 列表
    """
    cache_key = ENV_OPERATION_CACHE_KEY
    cached_data = get_redis_sync(cache_key)

    if cached_data:
        operations = json.loads(cached_data)
    else:
        operations = {
            "create": [],
            "update": [],
            "remove": []
        }

    # 将新的 ID 添加到对应操作列表中
    operations[operation].extend(env_ids)
    # 去重
    operations[operation] = list(set(operations[operation]))

    set_redis_sync(cache_key, json.dumps(operations))
