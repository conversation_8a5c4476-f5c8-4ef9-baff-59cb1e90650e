from datetime import datetime
from typing import List, Optional, Tuple
from uuid import uuid4
from sqlalchemy import or_
from sqlalchemy.orm import Session
import json

from ..models.workspace import Workspace

from ..config import settings

from ..models.member import Member
from .member import create as create_member_service, list as list_user_member_service

from ..misc.errors import NotFound
from ..schema.member import GroupRole, MemberCreate, MemberType, Role
from ..schema.common import ResourceType
from ..schema.group import GroupCreate, GroupUpdate
from ..models.group import Group
from . import app
from ..misc.redis_utils import set_redis_sync, GROUPS_CACHE_KEY


async def list_by_workspace(db: Session, workspace_id: str,
                            ids: Optional[List[str]] = None,
                            exclude_ids: Optional[List[str]] = None,
                            limit: Optional[int] = None,
                            offset: Optional[int] = None):
    stat = db.query(Group) \
        .filter(Group.workspace_id == workspace_id).filter(or_(Group.deleted_at == 0, Group.deleted_at.is_(None)))
    if exclude_ids is not None:
        stat = stat.filter(Group.id.notin_(exclude_ids))
    if ids is not None:
        stat = stat.filter(Group.id.in_(ids))
    count = stat.count()
    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    return (stat.all(), count)


async def list_group(db: Session,
                     ids: Optional[List[str]] = None,
                     exclude_ids: Optional[List[str]] = None,
                     limit: Optional[int] = None,
                     offset: Optional[int] = None):
    stat = db.query(Group)

    if exclude_ids is not None:
        stat = stat.filter(Group.id.notin_(exclude_ids))
    if ids is not None:
        stat = stat.filter(Group.id.in_(ids))
    count = stat.count()
    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    return (stat.all(), count)


async def create(db: Session,
                 user_id: str,
                 workspace_id: str,
                 group_create: GroupCreate,
                 group_id: Optional[str] = None):
    group = Group(**group_create.model_dump())
    if group_id is not None:
        group.id = group_id
    group.workspace_id = workspace_id  # type: ignore
    group.created_by = user_id
    group.updated_by = user_id
    db.add(group)
    db.commit()
    db.refresh(group)
    await sync_groups_to_redis(db)
    return group


async def access_group(db: Session, group_id: str, user_id: str):
    # 判断用户是否有该group的权限
    members, _ = await list_user_member_service(
        db,
        member_id=user_id,
        resource_id=group_id,
        resource_type=ResourceType.GROUP.value)
    if len(members) > 0 and str(members[0].role) != Role.EXTERNAL_USER.value:
        return False
    else:
        _ = await create_member_service(
            db,
            resource_type=ResourceType.GROUP,
            user_id=user_id,
            resource_id=group_id,
            member_create=MemberCreate(
                memberType=MemberType.USER.value,
                memberID=user_id,
                role=Role.DEVELOPER.value,
            ))
        return True


async def create_with_member(
    db: Session,
    user_id: str,
    workspace_id: str,
    group_create: GroupCreate,
):
    group_id = str(uuid4())
    _ = await create_member_service(
        db,
        resource_type=ResourceType.GROUP,
        user_id=user_id,
        resource_id=group_id,
        member_create=MemberCreate(
            memberType=MemberType.USER.value,
            memberID=user_id,
            role=Role.ADMIN.value,
        ))
    group = await create(
        db,
        user_id,
        workspace_id,
        group_create,
        group_id=group_id)
    return group


async def get(db: Session, group_id: str) -> Group:
    db_group = db.query(Group).filter(Group.id == group_id).first()
    if db_group is None:
        raise NotFound(f"Group({group_id}) not found")
    return db_group


async def get_with_role(db: Session, group_id: str, user_id: Optional[str] = None):
    db_group = await get(db, group_id)
    # 首先看用户是不是在workspace的admin中
    if user_id is None:
        db_group.role = GroupRole.EXTERNAL_USER.value
        return db_group
    _, total = await list_user_member_service(db=db, member_id=user_id, resource_id=str(db_group.workspace_id), role='admin', resource_type='workspace')
    is_admin = total > 0
    if is_admin:
        db_group.role = GroupRole.INTERNAL_USER.value
    else:
        # 其次看用户是否有该 group 的权限
        members, _ = await list_user_member_service(
            db,
            member_id=user_id,
            resource_id=group_id,
            resource_type=ResourceType.GROUP.value)
        if len(members) > 0 and members[0].role != Role.EXTERNAL_USER.value:
            db_group.role = GroupRole.INTERNAL_USER.value
        else:
            db_group.role = GroupRole.EXTERNAL_USER.value
    return db_group


async def update(db: Session, user_id: str,
                 group_id: str, group_update: GroupUpdate):
    db_group = await get(db, group_id)

    columns = group_update.model_dump(exclude_unset=True)
    columns[Group.updated_by] = user_id
    db.query(Group).filter(Group.id == group_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_group)
    await sync_groups_to_redis(db)
    return db_group


async def delete(db: Session, user_id: str, group_id: str):
    await get(db, group_id)
    db.query(Group).filter(Group.id == group_id).update(
        {Group.deleted_at: datetime.now(),
         Group.updated_by: user_id}
    )
    db.commit()
    await sync_groups_to_redis(db)


async def delete_with_check(
    db: Session, user_id: str, group_id: str
):
    _, count = await app.list_app(
        db, group_id=group_id, limit=1
    )
    if count > 0:
        raise Exception("group still has app, can't delete")
    await delete(db, user_id, group_id)


async def get_group_admins(db: Session, group_id: str) -> Tuple[List[Member], int]:
    db_group = await get(db, group_id)
    members, total = await list_user_member_service(
        db,
        resource_id=str(group_id),
        role='admin',
        resource_type='group',
        with_detail=True,
        member_type=MemberType.USER.value)
    return members, total


async def get_all_workspaces(db: Session) -> List[Workspace]:
    return db.query(Workspace).join(Member, Workspace.id == Member.resource_id).filter(
        Member.member_id == settings.workspace_member_id, Member.resource_type == 'workspace'
    ).all()


async def sync_groups_to_redis(db: Session):
    """
    同步所有group到redis缓存
    将以id:name的map形式存储在langbase::groups中
    """
    try:
        workspaces = await get_all_workspaces(db)
        workspace_ids = [str(workspace.id) for workspace in workspaces]
        # 获取所有未删除的group
        groups = db.query(Group).filter(
            or_(Group.deleted_at == 0, Group.deleted_at.is_(None))
        ).filter(Group.workspace_id.in_(workspace_ids)).all()

        # 构建id:name的映射
        group_map = {str(group.id): group.name for group in groups}

        # 写入redis
        set_redis_sync(GROUPS_CACHE_KEY, json.dumps(group_map))
        return group_map
    except Exception as e:
        print(f"Sync groups to redis error: {e}")
        return False
