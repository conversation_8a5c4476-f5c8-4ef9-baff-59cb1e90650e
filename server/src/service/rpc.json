{"edges": [{"id": "a43f79c28:a43f79c28-8d65569c-any-output-o1a0e9544:o1a0e9544-5a94b841-string-input-1", "targetPortId": "o1a0e9544-5a94b841-string-input-1", "sourcePortId": "a43f79c28-8d65569c-any-output", "source": "a43f79c28", "target": "o1a0e9544", "edge": {"shape": "xflow-edge", "attrs": {"line": {"stroke": "#d5d5d5", "strokeWidth": 1, "targetMarker": "", "strokeDasharray": ""}}, "zIndex": 1, "highlight": false, "id": "3e7e225d-1ee3-418e-9c97-f13860aa45c7", "source": {"cell": "a43f79c28", "port": "a43f79c28-8d65569c-any-output"}, "target": {"cell": "o1a0e9544", "port": "o1a0e9544-5a94b841-string-input-1"}}, "attrs": {"line": {"strokeDasharray": "", "targetMarker": {"name": "circle", "r": 5}, "stroke": "#abc", "strokeWidth": 1}}, "sourcePort": "a43f79c28-8d65569c-any-output", "targetPort": "o1a0e9544-5a94b841-string-input-1"}, {"id": "afdeffdf9:afdeffdf9-72ea3382-integer-output-a43f79c28:a43f79c28-53c8cb53-integer-input", "targetPortId": "a43f79c28-53c8cb53-integer-input", "sourcePortId": "afdeffdf9-72ea3382-integer-output", "source": "afdeffdf9", "target": "a43f79c28", "edge": {"shape": "xflow-edge", "attrs": {"line": {"stroke": "#d5d5d5", "strokeWidth": 1, "targetMarker": "", "strokeDasharray": ""}}, "zIndex": 1, "highlight": false, "id": "80f50189-6eec-4cb6-b379-8020a52b9ec9", "source": {"cell": "afdeffdf9", "port": "afdeffdf9-72ea3382-integer-output"}, "target": {"cell": "a43f79c28", "port": "a43f79c28-53c8cb53-integer-input"}}, "attrs": {"line": {"strokeDasharray": "", "targetMarker": {"name": "circle", "r": 5}, "stroke": "#f4a958", "strokeWidth": 1}}, "sourcePort": "afdeffdf9-72ea3382-integer-output", "targetPort": "a43f79c28-53c8cb53-integer-input"}], "nodes": [{"id": "o1a0e9544", "category": "OUTPUT", "code": "OUTPUT", "type": "OUTPUT", "x": -130, "y": -80, "height": 100, "width": 240, "renderKey": "OUTPUT", "icon": "CodeOutlined", "inputs": [{"id": "5a94b841", "name": "param1", "type": "string"}], "vars": [], "componentId": "49048006-4c0c-4258-896e-6520b9c8dcaa", "name": "输出节点", "ports": {"items": [{"id": "o1a0e9544-ab780565-_logic_-input-0", "args": {"x": 0, "y": 25}, "data": {"key": "逻辑连接点", "type": "_logic_"}, "type": "input", "attrs": {"text": {"text": ""}, "circle": {"r": 6, "fill": "#fff", "magnet": true, "stroke": "#bbb", "strokeWidth": 2}}, "group": "group1", "label": {"position": "inside"}, "keyName": "逻辑连接点", "tooltip": null, "dataType": "_logic_"}, {"id": "o1a0e9544-5a94b841-string-input-1", "args": {"x": 0, "y": 64}, "data": {"key": "param1", "type": "string"}, "type": "input", "attrs": {"text": {"text": "param1(string)"}, "circle": {"r": 6, "fill": "#fff", "magnet": true, "stroke": "#0929f9", "strokeWidth": 2}}, "group": "group1", "label": {"position": "inside"}, "keyName": "param1", "tooltip": null, "dataType": "string"}], "groups": {"group1": {"position": {"name": "absolute"}}}}, "groupChildren": null, "groupCollapsedSize": null}, {"no": null, "id": "a43f79c28", "category": "请求", "code": "RPC", "type": "HTTP", "description": "RPC通用调用", "x": -490, "y": -110, "height": 260, "width": 240, "renderKey": "ATOMIC_NODE", "inputs": [{"name": "podcastId", "title": "podcastId", "type": "integer", "id": "53c8cb53"}], "outputs": [{"name": "data", "title": "data", "type": "any", "id": "8d65569c"}, {"name": "message", "title": "message", "type": "string", "id": "db996b4a"}, {"name": "code", "title": "code", "type": "integer", "id": "aeac1f63"}, {"name": "success", "title": "success", "type": "boolean", "id": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ignore", "title": "ignore", "type": "boolean", "id": "6217dad0"}], "configs": [{"name": "appName", "type": "string", "value": "music-dj"}, {"name": "apiId", "type": "number", "value": 714337}, {"name": "interfaceName", "type": "string", "value": "com.netease.music.app.pub.voice"}, {"name": "name", "type": "string", "value": "getById"}, {"name": "method", "type": "string", "value": "getById"}, {"name": "registerCenter", "type": "string", "value": "zk_1"}, {"name": "tenant", "type": "boolean", "value": false}, {"name": "rpcKey", "type": "object", "value": {"reg": "dj_rpc_netty_server_test", "pre": "dj_rpc_netty_server_pre", "online": "dj_rpc_netty_server_online"}}], "vars": [{"type": "string", "name": "tenant", "value": false}], "componentId": "4742b93d-ff7f-4eed-8a56-05073644ad16", "name": "getById", "ports": {"groups": {"group1": {"position": {"name": "absolute"}}}, "items": [{"type": "input", "id": "a43f79c28-abcdef-_logic_-input", "connected": false, "group": "group1", "args": {"x": 0, "y": 25}, "tooltip": "123", "dataType": "_logic_", "keyName": "逻辑连接点", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "_logic_", "key": "逻辑连接点"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#bbb", "fill": "#fff"}, "text": {"text": ""}}}, {"type": "input", "id": "a43f79c28-53c8cb53-integer-input", "connected": false, "group": "group1", "args": {"x": 0, "y": 64}, "tooltip": "123", "dataType": "integer", "keyName": "podcastId", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "integer", "key": "podcastId"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#f4a958", "fill": "#fff"}, "text": {"text": "podcastId(integer)"}}}, {"type": "output", "id": "a43f79c28-8d65569c-any-output", "connected": true, "group": "group1", "args": {"x": "100%", "y": 94}, "tooltip": "123", "dataType": "any", "keyName": "data", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "any", "key": "data"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#abc", "fill": "#fff"}, "text": {"text": "data(any)"}}}, {"type": "output", "id": "a43f79c28-db996b4a-string-output", "connected": true, "group": "group1", "args": {"x": "100%", "y": 124}, "tooltip": "123", "dataType": "string", "keyName": "message", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "string", "key": "message"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#0929f9", "fill": "#fff"}, "text": {"text": "message(string)"}}}, {"type": "output", "id": "a43f79c28-aeac1f63-integer-output", "connected": true, "group": "group1", "args": {"x": "100%", "y": 154}, "tooltip": "123", "dataType": "integer", "keyName": "code", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "integer", "key": "code"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#f4a958", "fill": "#fff"}, "text": {"text": "code(integer)"}}}, {"type": "output", "id": "a43f79c28-83efceaf-boolean-output", "connected": true, "group": "group1", "args": {"x": "100%", "y": 184}, "tooltip": "123", "dataType": "boolean", "keyName": "success", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "boolean", "key": "success"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#024363", "fill": "#fff"}, "text": {"text": "success(boolean)"}}}, {"type": "output", "id": "a43f79c28-6217dad0-boolean-output", "connected": true, "group": "group1", "args": {"x": "100%", "y": 214}, "tooltip": "123", "dataType": "boolean", "keyName": "ignore", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "boolean", "key": "ignore"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#024363", "fill": "#fff"}, "text": {"text": "ignore(boolean)"}}}]}, "groupChildren": null, "groupCollapsedSize": null}, {"no": null, "id": "afdeffdf9", "category": "输入", "code": "INPUT_NUMBER", "type": "INPUT", "description": "用于输入/展示整形数字", "x": -810, "y": -110, "height": 140, "width": 240, "renderKey": "ATOMIC_NODE", "icon": "FieldNumberOutlined", "inputs": [], "outputs": [{"name": "number", "type": "integer", "title": "数值", "id": "72ea3382"}], "configs": [{"name": "url", "value": "1"}, {"name": "url", "value": "1"}, {"name": "method", "value": "POST"}], "vars": [], "componentId": "76a39076-35a8-48fa-96f5-8f2f4b464702", "name": "整数输入", "ports": {"groups": {"group1": {"position": {"name": "absolute"}}}, "items": [{"type": "input", "id": "afdeffdf9-abcdef-_logic_-input", "connected": false, "group": "group1", "args": {"x": 0, "y": 25}, "tooltip": "123", "dataType": "_logic_", "keyName": "逻辑连接点", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "_logic_", "key": "逻辑连接点"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#bbb", "fill": "#fff"}, "text": {"text": ""}}}, {"type": "output", "id": "afdeffdf9-72ea3382-integer-output", "connected": true, "group": "group1", "args": {"x": "100%", "y": 64}, "tooltip": "123", "dataType": "integer", "keyName": "number", "label": {"position": "inside"}, "markup": [{"tagName": "rect", "selector": "rect", "className": "xflow-port"}, {"tagName": "circle", "selector": "dot"}, {"tagName": "circle", "selector": "circle", "className": "xflow-port"}], "data": {"type": "integer", "key": "number"}, "attrs": {"circle": {"r": 6, "magnet": true, "strokeWidth": 2, "stroke": "#f4a958", "fill": "#fff"}, "text": {"text": "数值(integer)"}}}]}, "originValue": 31231028, "value": 31231028}], "inputs": [{"name": "number", "type": "integer", "title": "数值", "id": "72ea3382"}], "outputs": [{"id": "5a94b841", "name": "param1", "type": "string"}], "version": "1.0", "workflowId": "test_rpc_01", "workflowEngineId": "f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea", "workflowEngineName": "f4f9f02f-4e5c-4f35-bf8e-9fda4aebf6ea"}