import json
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy import or_, func, and_
from sqlalchemy.orm import Session

from ..misc.errors import NotFound, BadRequest
from datetime import datetime
from ..models.custom_component import CustomComponent
from ..schema.custom_component import CustomComponentCreate, CustomComponentUpdate
from ..schema.common import Scope
from .app import get_app_latest_config, get_app_latest_history_config, is_component_ref_by_app_config
from .collection import list as list_collection_service, create as create_collection_service, \
    get as get_collection_service, delete as delete_collection_service
from ..schema.collection import CollectionCreate
from ..schema.common import ResourceType


async def get(db: Session, custom_component_id: str) -> CustomComponent:
    db_custom_component = db.query(CustomComponent) \
        .filter(CustomComponent.id == custom_component_id).first()
    if db_custom_component is None:
        raise NotFound(f"CustomComponent({custom_component_id}) not found")
    return db_custom_component


async def cancel_favorite(db: Session,
                          user_id: str,
                          component_id: str):
    db_collection = await get_collection_service(db=db, user_id=user_id, resource_id=component_id,
                                                 resource_type=ResourceType.COMPONENTS.value)
    if db_collection is not None:
        await delete_collection_service(db=db, collection_id=db_collection.id)


async def favorite(db: Session,
                   user_id: str,
                   component_id: str):
    db_collection = await get_collection_service(db=db, user_id=user_id, resource_id=component_id,
                                                 resource_type=ResourceType.COMPONENTS.value)
    if db_collection is None:
        _ = await create_collection_service(db=db,
                                            collection=CollectionCreate(
                                                user_id=user_id,
                                                resource_type=ResourceType.COMPONENTS.value,
                                                resource_id=component_id,
                                            ))
    else:
        await delete_collection_service(db=db, collection_id=db_collection.id)


async def delete(db: Session,
                 user_id: str,
                 custom_component_id: str):
    if await is_component_ref_by_app_config(db, custom_component_id):
        raise BadRequest(
            f"component {custom_component_id} is ref by app config, cannot delete")
    db.query(CustomComponent).filter(CustomComponent.id == custom_component_id).update(
        {CustomComponent.deleted_by: user_id, CustomComponent.deleted_at: datetime.now()})
    db.commit()


async def validate_component_update(db: Session,
                                    db_component: CustomComponent,
                                    component_update: CustomComponentUpdate):
    if 'id' not in component_update.config \
            or component_update.config['id'] != db_component.id:
        raise BadRequest(
            f"component id{component_update.config['id']} not match db id")
    if db_component.name != component_update.name:
        stat = db.query(CustomComponent).filter(
            CustomComponent.name == component_update.name)
        if stat.count() > 0:
            raise BadRequest("component name already exist")


async def update(db: Session,
                 user_id: str,
                 custom_component_id: str,
                 custom_component_update: CustomComponentUpdate):
    db_custom_component = await get(db, custom_component_id)
    await validate_component_update(db=db, db_component=db_custom_component, component_update=custom_component_update)

    # update component
    custom_component_update.config['id'] = custom_component_id
    columns = custom_component_update.model_dump(exclude={"envs"})
    columns[CustomComponent.updated_by] = user_id
    db.query(CustomComponent).filter(CustomComponent.id == custom_component_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_custom_component)

    return db_custom_component


async def list_custom_components(db: Session,
                                 workspace_id: Optional[str] = None,
                                 group_id: Optional[str] = None,
                                 category_names: Optional[List[str]] = None,
                                 types: Optional[List[str]] = None,
                                 app_types: Optional[List[str]] = None,
                                 user_id: Optional[str] = None,
                                 is_favorite: Optional[bool] = None,
                                 offset: Optional[int] = None,
                                 limit: Optional[int] = None,
                                 ids: Optional[List[str]] = None) \
        -> Tuple[List[CustomComponent], int]:
    stat = db.query(CustomComponent)
    stat = db.query(CustomComponent).order_by(
        CustomComponent.created_at.desc())

    if is_favorite is not None and user_id is not None and is_favorite:
        (db_collections, total) = await list_collection_service(db=db, user_id=user_id,
                                                                resource_type=ResourceType.COMPONENTS.value)
        favorite_ids = []
        for collection in db_collections:
            favorite_ids.append(collection.resource_id)
        stat = stat.filter(CustomComponent.id.in_(favorite_ids))

    if ids is not None:
        stat = stat.filter(CustomComponent.id.in_(ids))

    if workspace_id is not None:
        stat = stat.filter(or_(CustomComponent.workspace_id == workspace_id,
                               CustomComponent.scope == Scope.PUBLIC.value))

    # 过滤本组才能见的
    if group_id is not None:
        stat = stat.filter(or_(CustomComponent.scope != Scope.GROUP.value, and_(
            CustomComponent.scope == Scope.GROUP.value, CustomComponent.group_id == group_id)))

    if category_names is not None and len(category_names) > 0:
        stat = stat.filter(CustomComponent.category_name.in_(category_names))

    if types is not None and len(types) > 0:
        stat = stat.filter(CustomComponent.type.in_(types))

    if app_types is not None and len(app_types) > 0:
        stat = stat.filter(func.json_contains(
            CustomComponent.app_types, json.dumps(app_types)))

    print(stat)
    count = stat.count()
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)

    return (stat.all(), count)


def validate_component_create(db: Session, custom_component_create: CustomComponentCreate):
    if 'code' not in custom_component_create.config or custom_component_create.code != custom_component_create.config['code']:
        raise BadRequest(
            f"component code{custom_component_create.code} not match config code")

    stat = db.query(CustomComponent).filter(
        CustomComponent.name == custom_component_create.name)
    if stat.count() > 0:
        raise BadRequest("component name already exist")

    stat = db.query(CustomComponent).filter(
        CustomComponent.code == custom_component_create.code)
    if stat.count() > 0:
        raise BadRequest("component code already exist")


async def create(db: Session,
                 workspace_id: str,
                 custom_component_create: CustomComponentCreate,
                 user_id: str):
    validate_component_create(
        db=db, custom_component_create=custom_component_create)

    # insert component
    custom_component = CustomComponent(**custom_component_create.model_dump())
    custom_component.workspace_id = workspace_id
    custom_component.created_by = user_id
    custom_component.updated_by = user_id
    db.add(custom_component)
    db.commit()
    db.refresh(custom_component)

    custom_component_create.config['id'] = custom_component.id
    columns = custom_component_create.model_dump(exclude={"envs"})
    db.query(CustomComponent).filter(
        CustomComponent.id == custom_component.id).update(columns)
    db.commit()
    db.refresh(custom_component)

    return custom_component


async def list_app_custom_components(db: Session,
                                     app_id: str,
                                     category_names: List[str],
                                     offset: Optional[int] = None,
                                     limit: Optional[int] = None) \
        -> Tuple[List[CustomComponent], int]:
    app_config = await get_app_latest_config(db=db, app_id=str(app_id))
    comps = json.loads(
        app_config.components) if app_config.components is not None else []

    try:
        app_config = await get_app_latest_history_config(db=db, app_id=str(app_id))
        comps.extend(json.loads(app_config.components)
                     if app_config.components is not None else [])
    except NotFound:
        pass

    components = list(set(comps))
    stat = db.query(CustomComponent).filter(CustomComponent.id.in_(components))

    if category_names is not None and len(category_names) > 0:
        stat = stat.filter(CustomComponent.category_name.in_(category_names))

    count = stat.count()
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)

    return (stat.all(), count)
