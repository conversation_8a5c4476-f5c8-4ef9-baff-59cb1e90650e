from datetime import datetime
import j<PERSON>
from typing import Dict, List, Optional, Set, Tuple
import httpx
from sqlalchemy import inspect
from sqlalchemy.orm import Session

from ..models.group import Group

from ..lib.llms.get_models import fetch_all_models

from ..utils.normalize import normalize_fields
from .aigw import get as get_aigw_app
from ..misc.redis_utils import MODEL_DISABLE_KEY, MODEL_TEST_KEY, get_redis_sync, set_redis_sync
from ..misc.http_db import get_http_db
from ..lib.llms.base import Provider, Providers, Model
from ..models.workspace import Workspace
from ..schema.common import ResourceType
from ..misc.errors import BadRequest, NotFound
from ..models.model import DefaultModelConfig, ModelProviderBinding
from ..schema.llm_model import LLMModel
from ..schema.model import BindingCreate, BindingUpdate, \
    DefaultModelConfigUpdate, ModelProviderBindingBase, ModelProviderKind, \
    ModelType
from .workspace import get as get_workspace_service
from .group import get as get_group_service
from ..config import settings


# 删除重复的bindings
async def remove_duplicate_bindings(db: Session, workspaceId: Optional[str]):
    http_db = get_http_db('tb_model_provider_binding')
    bindings_map = {}
    dup_bindings = []
    bindings = []
    if workspaceId is None:
        bindings = http_db.query(ModelProviderBinding) \
            .filter(ModelProviderBinding.group_id.is_(None)) \
            .all()
    else:
        bindings = http_db.query(ModelProviderBinding) \
            .filter(ModelProviderBinding.workspace_id.is_(workspaceId)) \
            .filter(ModelProviderBinding.group_id.is_(None)) \
            .all()
    # 查找bindings中每个workspace重复的
    for binding in bindings:
        binding_id = f"{binding.workspace_id}-{binding.kind}"
        # 说明之前有重复的
        if binding_id in bindings_map:
            # 如果当前api_key存在，则删除之前的
            if bool(binding.api_key):
                dup_bindings.append(bindings_map[binding_id].id)
            else:
                dup_bindings.append(binding.id)
        else:
            bindings_map[binding_id] = binding

    # 现在删除在DDB,先不处理删除逻辑，到时候如果需要删除，联系服务端进行脚本删除
    # http_db.query(ModelProviderBinding).filter(
    #     ModelProviderBinding.id.in_(dup_bindings)).delete()
    # http_db.commit()
    return dup_bindings


async def provider_binding_init_with_kind(db: Session, kind: str):
    http_db = get_http_db('tb_model_provider_binding')
    add_bindings = []
    provider_map: Dict[str, Provider] = dict()
    for member in Providers:
        provider_map[member.value.kind] = member.value
    # 如果提供的kind不在map中，则返回错误
    if kind not in provider_map:
        raise BadRequest(f"provider kind {kind} not found")
    db_workspaces = db.query(Workspace).all()
    workspace_dict: Dict[str, Tuple[Workspace, str]] \
        = dict()
    for workspace in db_workspaces:
        workspace_dict[workspace.id] = \
            (workspace, kind)

    # 判断group_id为None的就是每个workspace的默认
    default_bindings = http_db.query(ModelProviderBinding) \
        .filter(ModelProviderBinding.kind == kind) \
        .filter(ModelProviderBinding.group_id.is_(None)) \
        .all()

    changed = False

    # 把所有包含kind的workspace_id删掉，只留下需要添加的workspace_id
    for binding in default_bindings:
        if binding.workspace_id in workspace_dict:
            del workspace_dict[binding.workspace_id]

    for workspace_id in workspace_dict.keys():
        binding = ModelProviderBinding(
            kind=kind,
            workspace_id=workspace_id,
            created_by=workspace.created_by,
            updated_by=workspace.created_by,
        )
        add_bindings.append(workspace_id)
        http_db.add(binding)
        changed = True
    if changed:
        http_db.commit()
    return add_bindings


async def provider_binding_init(db: Session):
    http_db = get_http_db('tb_model_provider_binding')
    add_bindings = []
    provider_map: Dict[str, Provider] = dict()
    for member in Providers:
        provider_map[member.value.kind] = member.value

    db_workspaces = http_db.query(Workspace).all()
    # id -> (workspace, (kind))
    workspace_dict: Dict[str, Tuple[Workspace, Set[str]]] \
        = dict()
    for workspace in db_workspaces:
        workspace_dict[workspace.id] = \
            (workspace, set(provider_map.keys()))

    # 判断group_id为None的就是每个workspace的默认
    default_bindings = http_db.query(ModelProviderBinding) \
        .filter(ModelProviderBinding.group_id.is_(None)) \
        .all()

    changed = False

    for binding in default_bindings:
        if binding.workspace_id in workspace_dict:
            workspace, kinds = workspace_dict[binding.workspace_id]
            if binding.kind in kinds:
                kinds.remove(binding.kind)
                if len(kinds) == 0:
                    del workspace_dict[binding.workspace_id]

    for workspace_id, (workspace, kinds) in workspace_dict.items():
        for kind in kinds:
            binding = ModelProviderBinding(
                kind=kind,
                workspace_id=workspace_id,
                created_by=workspace.created_by,
                updated_by=workspace.created_by,
            )
            add_bindings.append(workspace_id)
            http_db.add(binding)
            changed = True
    if changed:
        http_db.commit()
    return add_bindings


# 废弃不再使用
async def list_global_provider() -> List[Provider]:
    return [p.value for p in Providers.__members__.values() if p.value.kind]


aigw_provider_endpoint_map = {
    'moonshot': 'https://aigw-int.netease.com/moonshot/simulation/v1',
    'openai': 'https://aigw-int.netease.com/v1',
    'deepseek': 'https://aigw-int.netease.com/v1',
    'qwen': 'https://aigw-int.netease.com/aliyun/simulation/v1',
    'anthropic': 'https://aigw-int.netease.com/vertexai/simulation/v1',
    'doubao': 'https://aigw-int.netease.com/v1',
    'wenxin': 'https://aigw-int.netease.com/v1',
    'minimax': 'https://aigw-int.netease.com/minimax/simulation/v1',
    'google': 'https://aigw-int.netease.com/v1',
    'tmax': 'https://aigw-int.netease.com/v1',
    'aigc': 'http://ai-text-service-test.apps-hangyan.danlu.netease.com/v1',
}


async def binding_with_workspace(db: Session, user_id: str, resource_id: str, aigw_id: str) -> List[ModelProviderBinding]:
    bindings, count = await list_provider_binding(db, resource_id, ResourceType.WORKSPACE, limit=1000)
    aigw_app = await get_aigw_app(db, aigw_id)
    if not aigw_app:
        raise NotFound(f"AigwApp({aigw_id}) not found")

    # 依次 update_provider_binding
    for binding in bindings:
        binding_update = BindingUpdate(
            description='一键绑定',
            endpoint=aigw_provider_endpoint_map[binding.kind],
            apiKey=aigw_app.token,
        )
        await update_provider_binding(db=db, user_id=user_id, binding_id=binding.id, binding_update=binding_update)

    return bindings


async def binding_with_group(db: Session, user_id: str, group_id: str, aigw_id: str) -> Tuple[Group, List[ModelProviderBinding]]:
    group = await get_group_service(db, group_id)
    if not group:
        raise NotFound(f"Group({group_id}) not found")
    aigw_app = await get_aigw_app(db, aigw_id)
    if not aigw_app:
        raise NotFound(f"AigwApp({aigw_id}) not found")

    bindings, count = await list_provider_binding(db, group_id, ResourceType.GROUP, limit=1000)
    # 依次 update_provider_binding
    for binding in bindings:
        binding_update = BindingUpdate(
            description='一键绑定',
            endpoint=aigw_provider_endpoint_map[binding.kind],
            apiKey=aigw_app.token,
        )
        await update_provider_binding(db=db, user_id=user_id, binding_id=binding.id, binding_update=binding_update)
    # 如果还有不在上述列表中的provider，则创建
    providers = [p.value for p in Providers]
    for provider in providers:
        if not provider.bindable:
            continue
        if provider.kind not in [binding.kind for binding in bindings]:
            await create_provider_binding(db=db, user_id=user_id, binding_create=BindingCreate(
                providerKind=provider.kind,
                workspaceId=group.workspace_id,
                description='一键绑定',
                endpoint=aigw_provider_endpoint_map[provider.kind],
                apiKey=aigw_app.token,
            ), group_id=group_id, workspace_id=group.workspace_id)
    return group, bindings


# 迁移完成
async def list_provider_binding(
    db: Optional[Session] = None,
    resource_id: Optional[str] = None,
    resource_type: Optional[ResourceType] = None,
    offset: Optional[int] = None,
    limit: Optional[int] = None,
) -> Tuple[List[ModelProviderBinding], int]:
    http_db = get_http_db('tb_model_provider_binding')

    stat = http_db.query(ModelProviderBinding)
    if resource_type == ResourceType.WORKSPACE:
        stat = stat.filter(ModelProviderBinding.workspace_id == resource_id) \
            .filter(ModelProviderBinding.group_id.is_(None))
    if resource_type == ResourceType.GROUP:
        stat = stat.filter(ModelProviderBinding.group_id == resource_id)

    count = stat.count()

    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)

    return (stat.all(), count)


async def create_provider_binding(
    db: Session,
    user_id: str,
    binding_create: BindingCreate,
    group_id: Optional[str] = None,
    workspace_id: Optional[str] = None,
):
    http_db = get_http_db('tb_model_provider_binding')
    if group_id is None and workspace_id is None:
        raise BadRequest("group_id or workspace_id must be provided")

    if workspace_id is not None:
        workspace = await get_workspace_service(db, workspace_id)  # type: ignore # noqa

    if group_id is not None:
        group = await get_group_service(db, group_id)  # type: ignore # noqa
        workspace = await get_workspace_service(db, group.workspace_id)  # type: ignore # noqa
        workspace_id = workspace.id

    binding = ModelProviderBinding(
        kind=binding_create.kind.value,
        workspace_id=workspace_id,
        description=binding_create.description,
        group_id=group_id,
        endpoint=binding_create.endpoint,
        api_key=binding_create.api_key,
        config=binding_create.config,
        created_by=user_id,
        updated_by=user_id,
    )

    http_db.add(binding)
    http_db.commit()
    http_db.refresh(binding)
    return binding


async def create_default_provider_binding(
    db: Session,
    user_id: str,
    provider_kind: str,
    workspace_id: str,
):
    http_db = get_http_db('tb_model_provider_binding')
    bindings = http_db.query(ModelProviderBinding) \
        .filter(ModelProviderBinding.workspace_id == workspace_id) \
        .filter(ModelProviderBinding.kind == provider_kind) \
        .filter(ModelProviderBinding.config.is_(None)) \
        .filter(ModelProviderBinding.api_key.is_(None)) \
        .filter(ModelProviderBinding.endpoint.is_(None)) \
        .all()
    if len(bindings) > 0:
        return bindings[0]

    binding = ModelProviderBinding(
        kind=provider_kind,
        workspace_id=workspace_id,
        created_by=user_id,
        updated_by=user_id,
    )
    http_db.add(binding)
    http_db.commit()
    http_db.refresh(binding)
    return binding


async def get_provider_binding(
    db: Optional[Session] = None,
    binding_id: Optional[str] = None,
):
    http_db = get_http_db('tb_model_provider_binding')
    binding = http_db.query(ModelProviderBinding) \
        .filter(ModelProviderBinding.id == binding_id) \
        .first()
    if binding is None:
        raise NotFound(f"ModelProviderBinding {binding_id} is not found")
    return binding


# 迁移完成
async def delete_provider_binding(
    db: Session,
    binding_id: str,
):
    http_db = get_http_db('tb_model_provider_binding')
    binding = await get_provider_binding(db, binding_id)
    if binding.is_default:
        raise BadRequest("Cannot delete default provider binding")

    _, count = await list_provider_binding(
        db,
        str(binding.workspace_id),
        ResourceType.WORKSPACE,
        limit=0)
    if count == 1:
        raise BadRequest("Cannot delete the last provider binding")

    http_db.query(ModelProviderBinding) \
        .filter(ModelProviderBinding.id == binding_id) \
        .delete()
    http_db.commit()


# 迁移完成
async def update_provider_binding(
    db: Session,
    user_id: str,
    binding_id: str,
    binding_update: BindingUpdate,
):
    http_db = get_http_db('tb_model_provider_binding')
    db_binding = await get_provider_binding(db, binding_id)
    # if db_binding.is_default:
    #     raise BadRequest("Cannot update default provider binding")
    columns = binding_update.model_dump(exclude_unset=True)
    columns[ModelProviderBinding.updated_by] = user_id
    http_db.query(ModelProviderBinding) \
        .filter(ModelProviderBinding.id == binding_id) \
        .update(columns)  # type: ignore
    http_db.commit()
    http_db.refresh(db_binding)
    return db_binding


async def get_default_model_config(
    db: Session,
    resource_type: ResourceType,
    resource_id: str,
    model_type: ModelType,
):
    stat = db.query(DefaultModelConfig) \
        .filter(DefaultModelConfig.model_type == model_type.value)
    if resource_type == ResourceType.WORKSPACE:
        stat = stat.filter(DefaultModelConfig.workspace_id == resource_id)
    if resource_type == ResourceType.GROUP:
        stat = stat.filter(DefaultModelConfig.group_id == resource_id)
    config = stat.first()
    if config is None:
        raise NotFound(f"DefaultModelConfig "
                       f"{resource_type}-{resource_id}-{model_type} not found")
    return config


async def update_default_model_config(
    db: Session,
    user_id: str,
    resource_type: ResourceType,
    resource_id: str,
    config_update: DefaultModelConfigUpdate,
):
    try:
        config = await get_default_model_config(
            db,
            resource_type,
            resource_id,
            config_update.model_type)
    except NotFound:
        if config_update.provider_kind is None \
           or config_update.model_type is None:
            raise BadRequest("providerKind and modelType must be provided "
                             "if there's no default config")
        workspace_id = resource_id
        group_id = None
        if resource_type == ResourceType.GROUP:
            from .group import get as get_group_service
            group = await get_group_service(db, resource_id)
            workspace_id = str(group.workspace_id)
            group_id = resource_id
        config = DefaultModelConfig(
            provider_kind=config_update.provider_kind.value,
            model_name=config_update.model_name,
            model_type=config_update.model_type.value,
            workspace_id=workspace_id,
            group_id=group_id,
            created_by=user_id,
            updated_by=user_id,
        )
        db.add(config)
        db.commit()
        db.refresh(config)
        return config

    columns = config_update.model_dump(exclude_unset=True)

    columns[DefaultModelConfig.updated_by] = user_id
    db.query(DefaultModelConfig) \
        .filter(DefaultModelConfig.id == config.id) \
        .update(columns)  # type: ignore
    db.commit()
    db.refresh(config)
    return config


async def list_default_model_config(
    db: Session,
    resource_type: ResourceType,
    resource_id: str,
    model_type: Optional[ModelType] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
):
    stat = db.query(DefaultModelConfig)
    if resource_type == ResourceType.WORKSPACE:
        stat = stat.filter(DefaultModelConfig.workspace_id == resource_id)
    if resource_type == ResourceType.GROUP:
        stat = stat.filter(DefaultModelConfig.group_id == resource_id)
    if model_type is not None:
        stat = stat.filter(DefaultModelConfig.model_type == model_type.value)

    count = stat.count()
    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    return (stat.all(), count)


async def get_default_model_config_recursively(
    db: Session,
    group_id: str,
    model_type: ModelType,
):
    configs, _ = await list_default_model_config(
        db,
        resource_type=ResourceType.GROUP,
        resource_id=group_id,
        model_type=model_type,
    )
    if len(configs) > 0:
        return configs[0]

    group = await get_group_service(db, group_id)

    configs, _ = await list_default_model_config(
        db,
        resource_type=ResourceType.WORKSPACE,
        resource_id=str(group.workspace_id),
        model_type=model_type,
    )
    if len(configs) > 0:
        return configs[0]
    raise NotFound("DefaultModelConfig not found: "
                   f"group_id={group_id} "
                   f"model_type={model_type}")


# 废弃不再使用
async def list_models(
    db: Session,
    model_type: Optional[ModelType] = None,
    provider_kind: Optional[ModelProviderKind] = None,
    workspaceId: Optional[str] = None,
    groupId: Optional[str] = None,
):
    providers: List[Provider] = \
        [p.value for p in Providers]
    if provider_kind is None:
        filter(lambda x: x.kind == provider_kind, providers)

    disable_models = get_redis_sync(MODEL_DISABLE_KEY)
    if disable_models is None:
        disable_models = []
    else:
        disable_models = disable_models.split(',')
    # type: ignore

    workspaceBindings = []
    groupBindings = []
    # 查找group和workspace的绑定
    if workspaceId:
        workspaceBindings, _ = await list_provider_binding(
            db,
            resource_id=workspaceId,
            resource_type=ResourceType.WORKSPACE)

    if groupId:
        groupBindings, _ = await list_provider_binding(
            db,
            resource_id=groupId,
            resource_type=ResourceType.GROUP)
    bindings = workspaceBindings + groupBindings

    models = list()
    for provider in providers:
        # 先要确认该模型是否有绑定
        validProvider = next(
            filter(lambda x: x.kind == provider.kind and bool(x.api_key), bindings), None)

        # 有绑定的才是可用的
        def isDisabled(model: Model):
            if model.name in disable_models:
                return '该模型暂不可用'
            if validProvider is not None:
                return ''
            if provider.kind == 'openai' and 'gpt-4-' not in model.name and 'gpt-4o-2024-08-06' not in model.name:
                return '该模型未绑定账号，请联系管理员添加对应账号'
            return ''

        m = list(provider.models)
        if model_type is not None:
            m = filter(lambda x: x.type.value == model_type, m)
        models.extend([dict(**model.model_dump(exclude={'enable'}),
                            **dict(providerKind=provider.kind,
                                   providerName=provider.description,
                                   disableReason=isDisabled(model),
                                   enable=bool(model.enable) and not isDisabled(model)))
                       for model in m])

    typedModels = models

    return typedModels


async def list_models_v2(
    db: Session,
    model_type: Optional[ModelType] = None,
    provider_kind: Optional[ModelProviderKind] = None,
    workspaceId: Optional[str] = None,
    groupId: Optional[str] = None,
):
    # 先获取所有模型
    all_models = await fetch_all_models()
    model_test_cache_key = f"{MODEL_TEST_KEY}::{datetime.now().strftime('%m-%d')}"
    model_test_cache_data = get_redis_sync(model_test_cache_key)
    model_test_results = {}
    if model_test_cache_data:
        model_test_results = json.loads(model_test_cache_data)

    if provider_kind is not None:
        all_models = filter(lambda x: x.providerKind ==
                            provider_kind, all_models)

    disable_models = get_redis_sync(MODEL_DISABLE_KEY)
    if disable_models is None:
        disable_models = []
    else:
        disable_models = disable_models.split(',')

    workspaceBindings = []
    groupBindings = []
    # 查找group和workspace的绑定
    if workspaceId:
        workspaceBindings, _ = await list_provider_binding(
            db,
            resource_id=workspaceId,
            resource_type=ResourceType.WORKSPACE)

    if groupId:
        groupBindings, _ = await list_provider_binding(
            db,
            resource_id=groupId,
            resource_type=ResourceType.GROUP)
    bindings = workspaceBindings + groupBindings

    models = list()

    for model in all_models:
        # 先要确认该模型是否有绑定
        model_test_result = None
        validProvider = next(
            filter(lambda x: x.kind == model.providerKind and bool(x.api_key), bindings), None)
        if model.name in model_test_results:
            model_test_result = model_test_results[model.name]
        # 有绑定的才是可用的

        def isDisabled(model: LLMModel):
            if model.name in disable_models:
                return '该模型暂不可用'
            if validProvider is not None:
                return ''
            if model.providerKind == 'openai' and 'gpt-4-' not in model.name and 'gpt-4o-2024-08-06' not in model.name:
                return '该模型未绑定账号，请联系管理员添加对应账号'
            return ''

        if model_type is not None:
            type_value = isinstance(
                model_type, str) and model_type or model_type.value
            if model.type != type_value:
                continue
        models.append(dict(**model.model_dump(exclude={'enable'}),
                           **dict(disableReason=isDisabled(model),
                                  enable=bool(
                                      model.enable) and not isDisabled(model),
                                  model_test_result=model_test_result)))

    return models


def model_to_dict(model):
    result = {}
    # 处理 ModelProxy 对象
    if hasattr(model, '_data'):
        return model._data

    # 处理 SQLAlchemy 模型
    for column in inspect(model).mapper.column_attrs:
        value = getattr(model, column.key)
        if isinstance(value, datetime):
            value = value.isoformat()
        result[column.key] = value
    return result


def dict_to_model(list) -> list[ModelProviderBinding]:
    model_bindings = []
    for binding_dict in list:
        # 将字符串转换回 datetime 对象
        if 'update_time' in binding_dict:
            binding_dict['update_time'] = datetime.fromisoformat(
                binding_dict['update_time'])
        if 'create_time' in binding_dict:
            binding_dict['create_time'] = datetime.fromisoformat(
                binding_dict['create_time'])

        # 删除binding_dict中的createdBy和updatedBy
        binding_dict.pop('createdBy', None)
        binding_dict.pop('updatedBy', None)
        binding = ModelProviderBinding(**binding_dict)
        model_bindings.append(binding)
    return model_bindings


async def getBinding(workspace_id: str,
                     group_id: str,
                     db: Session):
    redis_key = f"bindings::{workspace_id}::{group_id}"
    if settings.debug:
        redis_key += "::debug"
    # print(f"redis_key {redis_key}")
    cache = get_redis_sync(redis_key)
    cache = json.loads(cache) if cache else None
    bindings = []
    if cache:
        bindings = dict_to_model(cache)
        return bindings
    workspaceBindings, _ = await list_provider_binding(
        db,
        resource_id=workspace_id,
        resource_type=ResourceType.WORKSPACE)

    groupBindings, _ = await list_provider_binding(
        db,
        resource_id=group_id,
        resource_type=ResourceType.GROUP)
    # 优先读取 group 内的绑定，再读取 workspace 的绑定

    bindings = groupBindings + workspaceBindings
    bindings_dict = []
    for binding in bindings:
        binding_dict = model_to_dict(binding)
        bindings_dict.append(binding_dict)
    set_redis_sync(redis_key, json.dumps(bindings_dict), ex=31536000)
    return bindings
