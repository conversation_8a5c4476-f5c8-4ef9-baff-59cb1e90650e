import json
from typing import List, Optional, Tuple
from sqlalchemy import or_
from sqlalchemy.orm import Session

from ..models.operator_mixin import get_or_set_account

from ..cache.account import init_account_cache

from ..misc.errors import NotFound

from ..schema.account import AccountCreate
from ..models.account import Account


async def get_by_email(db: Session, email: str):
    return db.query(Account).filter(Account.email == email).first()


async def get_by_id(db: Session, id: str):
    account = get_or_set_account(id, db)
    if account is None:
        raise NotFound("Account not found")
    return account


async def set_admin(db: Session, mail: str):
    user = await get_by_email(db, mail)
    if user is None:
        raise NotFound("Account not found")
    user.is_admin = True
    db.commit()
    return user


async def remove_admin(db: Session, mail: str):
    user = await get_by_email(db, mail)
    if user is None:
        raise NotFound("Account not found")
    user.is_admin = False
    db.commit()
    return user


async def create(db: Session, account: AccountCreate) -> Account:
    db_account = Account(**account.model_dump())
    db.add(db_account)
    db.commit()
    db.refresh(db_account)
    init_account_cache()
    return db_account


async def delete(db: Session, id: str) -> None:
    db.query(Account).filter(Account.id == id).delete()
    db.commit()


async def list(db: Session,
               ids: Optional[List[str]] = None,
               names: Optional[List[str]] = None,
               offset: Optional[int] = None,
               limit: Optional[int] = None,) -> Tuple[List[Account], int]:

    statement = db.query(Account)
    if ids is not None:
        statement = statement.filter(Account.id.in_(ids))
    if names is not None:
        statement = statement.filter(
            or_(
                (or_(*[Account.email.like(f"%{name}%") for name in names])),
                (or_(*[Account.name.like(f"%{name}%") for name in names])),
                (or_(*[Account.fullname.like(f"%{name}%") for name in names]))
            )
        )
    total = statement.count()
    if offset is not None:
        statement = statement.offset(offset)
    if limit is not None:
        statement = statement.limit(limit)
    return (statement.all(), total)
