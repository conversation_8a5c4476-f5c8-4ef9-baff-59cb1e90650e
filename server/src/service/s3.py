import typing

from aiobotocore.session import get_session
import boto3
from botocore.config import Config

from ..config import S3


async def upload_file(s3_config: S3, key: str, body: typing.BinaryIO):
    s3_client = boto3.client("s3",
                             region_name=s3_config.region,
                             use_ssl=not s3_config.disable_ssl,
                             verify=not s3_config.skip_verify,
                             endpoint_url=f"http://{s3_config.endpoint}",
                             aws_access_key_id=s3_config.access_key,
                             aws_secret_access_key=s3_config.secret_key,
                             config=Config(s3={'addressing_style': 'virtual'}))
    s3_client.put_object(Bucket=s3_config.bucket, Key=key, Body=body)


def upload_file_v2(s3_config: S3, bucket: str, key: str, body: typing.BinaryIO):
    s3_client = boto3.client("s3",
                             region_name=s3_config.region,
                             use_ssl=not s3_config.disable_ssl,
                             verify=not s3_config.skip_verify,
                             endpoint_url=f"http://{s3_config.endpoint}",
                             aws_access_key_id=s3_config.access_key,
                             aws_secret_access_key=s3_config.secret_key,
                             config=Config(s3={'addressing_style': 'virtual'}))
    s3_client.put_object(Bucket=bucket, Key=key, Body=body)


async def async_upload_file(s3_config: S3, key: str, body: typing.BinaryIO):
    async with get_session().create_client("s3",
                                           region_name=s3_config.region,
                                           use_ssl=not s3_config.disable_ssl,
                                           verify=not s3_config.skip_verify,
                                           endpoint_url=f"http://{s3_config.endpoint}",
                                           aws_access_key_id=s3_config.access_key,
                                           aws_secret_access_key=s3_config.secret_key,
                                           config=Config(s3={'addressing_style': 'virtual'})) as client:
        return await client.put_object(Bucket=s3_config.bucket, Key=key, Body=body)
