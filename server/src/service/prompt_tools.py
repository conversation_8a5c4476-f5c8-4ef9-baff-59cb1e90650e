# 假设 executor 是一个可用的模块并且有一个 chat 方法
from enum import Enum
import re
import json

from sqlalchemy import JSO<PERSON>

from ..schema.app import AgentConfig

from .dialog import user_prompt

from ..models.app import VersionedAppConfig

# from server.src.models.app import VersionedAppConfig
from ..schema.dialog import LLMMessage
from ..lib.llms.excutor import Executor  # 请确保路径正确
from typing import List, Optional
from ..schema.prompt_tools import SubStructPromptItem, \
    StructPromptItem
from ..service.app import get_app_latest_config
from ..config import settings
from ..configs.prompts import DEFAULT_PROMPT_TO_STRUCT, \
    DEFAULT_BEAUTIFY_TO_TEXT_ROLE, \
    DEFAULT_BEAUTIFY_TO_TEXT, \
    DEFAULT_BEAUTIFY_TO_TEXT_OUTPUT
from ..configs.prompts_appid import ONLINE_PROMPT_APP_ID, \
    TESTING_PROMPT_APP_ID


def clean_json_string(json_str):
    # 去除字符串中的多余空格和换行符
    # \s* 表示可能的任意多的空白字符（包括空格、制表符、换行符等）
    # 在冒号和逗号的前后使用 \s* 表示想要忽略这些字符
    return re.sub(r'\s+', '', json_str)

# 线上环境的配置


def get_prompt_app_id(type) -> str:
    if settings.debug or settings.testing:
        return TESTING_PROMPT_APP_ID[type]
    else:
        return ONLINE_PROMPT_APP_ID[type]


class CustomSubStructPromptJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, SubStructPromptItem):
            return obj.dict()  # 使用 Pydantic 的 dict 方法将其转换为字典
        return super().default(obj)


class CustomStructPromptJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, StructPromptItem):
            return obj.dict()  # 使用 Pydantic 的 dict 方法将其转换为字典

        return super().default(obj)


async def ai_beautify(db, prompt: str, requires: str, user_id: str) -> str:

    requirements: str = requires or '帮我优化一下'

    # 获取配置
    appConfig = await get_app_latest_config(
        db,
        app_id=get_prompt_app_id(type='ai_beautify'))

    app_config = AgentConfig.model_validate(appConfig.config)

    messages: List[LLMMessage] = list()

    messages.append(LLMMessage(
        role='system', content=app_config.prePrompt))
    messages.append(LLMMessage(
        role='user', content=f'原提示词\n{prompt}\n需求\n{requirements}'))

    message: LLMMessage | None = await user_prompt(app_config, {'pre_prompt': prompt, 'requirements': requirements})
    if message is not None:
        messages.append(message)

    config = {
        "model_name": app_config.modelName,
        "provider_kind": app_config.providerKind,
        "model_params": app_config.modelParams,
        "response_mode": 'json',
        "user_id": user_id,
        "messages": messages
    }
    executor = Executor()

    # 调用大模型的chat方法，并返回结果
    json_response = await executor.chat(
        model_name=config["model_name"],
        provider_kind=config["provider_kind"],
        model_params=config["model_params"],
        response_mode=config["response_mode"],
        user_id=config["user_id"],
        messages=config["messages"]
    )
    content: str = json_response.content  # type: ignore
    return content


async def convert_prompt_to_struct_text(db, prompt: str, user_id: str) -> str:

    prePrompt: str = ''

    # 获取配置
    appConfig: VersionedAppConfig = await get_app_latest_config(
        db,
        app_id=get_prompt_app_id(type='convert_prompt_to_struct_text'))

    if appConfig and appConfig.config:  # type: ignore
        promptConfig = appConfig.config
    # 如果配置中有prePrompt，则使用配置中的prePrompt，否则使用默认的prePrompt
        prePrompt = promptConfig['prePrompt'] if 'prePrompt' in promptConfig else defaultPrePrompt
    else:
        prePrompt = DEFAULT_PROMPT_TO_STRUCT

    messages: List[LLMMessage] = list()
    messages.append(LLMMessage(
        role='system', content=prePrompt))
    messages.append(LLMMessage(role='user', content=f'请转换如下文本\n{prompt}'))

    config = {
        "model_name": 'gpt-4o-mini-2024-07-18',
        "provider_kind": 'openai',
        "model_params": {
            "temperature": 0.1,
            "top_p": 0.85,
            "frequency_penalty": 0.1,
            "presence_penalty": 0.1,
            "max_tokens": 4096,
            "json_object": False,
        },
        "response_mode": 'json',
        "user_id": user_id,
        "messages": messages
    }
    executor = Executor()

    # 调用大模型的chat方法，并返回结果
    json_response = await executor.chat(
        model_name=config["model_name"],
        provider_kind=config["provider_kind"],
        model_params=config["model_params"],
        response_mode=config["response_mode"],
        user_id=config["user_id"],
        messages=config["messages"]
    )
    content: str = json_response.content  # type: ignore
    return content


async def convert_prompt_to_struct(db, prompt: str, user_id: str) -> List[dict[str, str]]:

    prePrompt: str = ''

    # 获取配置
    appConfig: VersionedAppConfig = await get_app_latest_config(
        db,
        app_id=get_prompt_app_id(type='convert_prompt_to_struct'))

    if appConfig and appConfig.config:  # type: ignore
        promptConfig = appConfig.config
    # 如果配置中有prePrompt，则使用配置中的prePrompt，否则使用默认的prePrompt
        prePrompt = promptConfig['prePrompt'] if 'prePrompt' in promptConfig else defaultPrePrompt
    else:
        prePrompt = DEFAULT_PROMPT_TO_STRUCT

    messages: List[LLMMessage] = list()
    messages.append(LLMMessage(
        role='system', content=prePrompt))
    messages.append(LLMMessage(role='user', content=f'请转换如下文本\n{prompt}'))

    config = {
        "model_name": 'gpt-4o-mini-2024-07-18',
        "provider_kind": 'openai',
        "model_params": {
            "temperature": 0.1,
            "top_p": 0.85,
            "frequency_penalty": 0.1,
            "presence_penalty": 0.1,
            "max_tokens": 4096,
            "json_object": True,
        },
        "response_mode": 'json',
        "user_id": user_id,
        "messages": messages
    }
    executor = Executor()

    # 调用大模型的chat方法，并返回结果
    json_response = await executor.chat(
        model_name=config["model_name"],
        provider_kind=config["provider_kind"],
        model_params=config["model_params"],
        response_mode=config["response_mode"],
        user_id=config["user_id"],
        messages=config["messages"]
    )

    cleaned_json_string = clean_json_string(
        json_response.content)  # type: ignore
    try:
        jsonRes = json.loads(cleaned_json_string)
        return jsonRes["data"]
    except (json.JSONDecodeError, KeyError) as e:
        print(f"Error decoding JSON or accessing 'data' key: {e}")
        return []


async def beautify_to_text(db,
                           structPrompt: List[StructPromptItem],
                           subStructPrompt: List[SubStructPromptItem],
                           subStructPromptType: Optional[str],
                           user_id: str) -> str:
    defaultPrePrompt = ''
    appId = ''
    if subStructPromptType == 'output_format':
        appId = get_prompt_app_id(type='beautify_to_text_output')
        defaultPrePrompt = DEFAULT_BEAUTIFY_TO_TEXT_OUTPUT
    elif subStructPromptType == 'role':
        appId = get_prompt_app_id(type='beautify_to_text_role')
        defaultPrePrompt = DEFAULT_BEAUTIFY_TO_TEXT_ROLE
    else:
        appId = get_prompt_app_id(type='beautify_to_text')
        defaultPrePrompt = DEFAULT_BEAUTIFY_TO_TEXT
    prePrompt: str = ''

    # roleSettingsStr = json.dumps(
    #     structPrompt, cls=CustomStructPromptJSONEncoder)

    prompt_parts = []
    for item in structPrompt:
        # 拼接字符串，以符合要求的格式
        formatted_text = f"### {item.title}\n{item.content}"
        prompt_parts.append(formatted_text)

    # 合并所有部分到一个单独的字符串
    roleSettingsStr = "\n".join(prompt_parts)

    roleSettings = f'''
    ## 角色描述
        {roleSettingsStr}
    '''

    # 获取配置
    appConfig: VersionedAppConfig = await get_app_latest_config(db, app_id=appId)

    if appConfig and appConfig.config:  # type: ignore
        promptConfig = appConfig.config
        # 如果配置中有prePrompt，则使用配置中的prePrompt，否则使用默认的prePrompt
        prePrompt = promptConfig['prePrompt'] if 'prePrompt' in promptConfig else defaultPrePrompt
    else:
        prePrompt = defaultPrePrompt

    subStructPromptStr: str = json.dumps(
        subStructPrompt, cls=CustomSubStructPromptJSONEncoder)

    messages: List[LLMMessage] = list()
    messages.append(LLMMessage(
        role='system', content=f"{roleSettings}"))

    messages.append(LLMMessage(
        role='system', content=f"{prePrompt}"))

    messages.append(LLMMessage(
        role='user', content=f'***指定主题{subStructPromptStr}***'))

    config = {
        "model_name": 'gpt-4o-mini-2024-07-18',
        "provider_kind": 'openai',
        "model_params": {
            "temperature": 0.3,
            "top_p": 0.85,
            "frequency_penalty": 0.1,
            "presence_penalty": 0.1,
            "max_tokens": 4096,
            "json_object": False,
        },
        "response_mode": 'json',
        "user_id": user_id,
        "messages": messages
    }
    executor = Executor()

    # 调用大模型的chat方法，并返回结果
    json_response = await executor.chat(
        model_name=config["model_name"],
        provider_kind=config["provider_kind"],
        model_params=config["model_params"],
        response_mode=config["response_mode"],
        user_id=config["user_id"],
        messages=config["messages"]
    )

    content: str = json_response.content  # type: ignore
    return content
