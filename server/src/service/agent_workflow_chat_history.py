from typing import List, Optional
from uuid import uuid4
from sqlalchemy.orm import Session

from ..config import settings

from ..schema.component import ComponentType

from ..misc.errors import NotFound

from ..models.agent_workflow import AgentWorkflowChatHistory, AgentWorkflowComponentStatus

from ..schema.agent_workflow_chat_history import AWComponentStatusCreate, AWComponentStatusUpdate, AWHistoryCreate


async def create(db: Session, user_id: str, history_create: AWHistoryCreate):
    if settings.disable_mysql:
        return AgentWorkflowChatHistory(id="no_store")
    history = AgentWorkflowChatHistory(
        app_id=history_create.app_id,
        app_config_id=history_create.app_config_id,
        status=history_create.status,
        created_by=user_id,
    )

    db.add(history)
    db.commit()
    db.refresh(history)
    return history


async def get(db: Session, history_id: str):
    history = db.query(AgentWorkflowChatHistory).filter(
        AgentWorkflowChatHistory.id == history_id).first()
    if history is None:
        raise NotFound(
            f"AgentWorkflowChatHistory(id = {history_id}) not found")
    return history


async def update(db: Session, history_id: str, history_update: AWHistoryCreate):
    history = await get(db, history_id)

    columns = history_update.model_dump(exclude_unset=True)
    db.query(AgentWorkflowChatHistory).filter(
        AgentWorkflowChatHistory.id == history_id).update(columns)  # type: ignore
    db.commit()
    db.refresh(history)
    return history


async def create_component_statuses(db: Session, chat_id: str, create_requests: List[AWComponentStatusCreate]):
    statuses: List[AgentWorkflowComponentStatus] = []
    for request in create_requests:
        status = AgentWorkflowComponentStatus(
            id=str(uuid4()),
            chat_id=chat_id,
            component_id=request.component_id,
            component_type=request.component_type.value,
            idx=request.idx,
            status=request.status,
            result=request.result
        )
        statuses.append(status)

    db.bulk_save_objects(statuses, return_defaults=True)
    db.commit()
    return statuses


async def get_component_status(db: Session, status_id: str):
    status = db.query(AgentWorkflowComponentStatus).filter(
        AgentWorkflowComponentStatus.id == status_id).first()
    if status is None:
        raise NotFound(
            f"AgentWorkflowComponentStatus(id = {status_id}) not found")
    return status


async def update_component_status(db: Session,
                                  status_id: str,
                                  component_status_update: AWComponentStatusUpdate):
    status = await get_component_status(db, status_id)
    db.query(AgentWorkflowComponentStatus).filter(
        AgentWorkflowComponentStatus.id == status_id).update(
        component_status_update.model_dump(exclude_unset=True))  # type: ignore
    db.commit()
    db.refresh(status)
    return status


async def list_component_statuses(db: Session,
                                  chat_id: str,
                                  idx: Optional[int] = None,
                                  component_type: Optional[ComponentType] = None,
                                  ):
    stat = db.query(AgentWorkflowComponentStatus).filter(
        AgentWorkflowComponentStatus.chat_id == chat_id)
    if idx is not None:
        stat = stat.filter(AgentWorkflowComponentStatus.idx == idx)
    if component_type is not None:
        stat = stat.filter(
            AgentWorkflowComponentStatus.component_type == component_type.value)
    statuses = stat.all()
    return statuses
