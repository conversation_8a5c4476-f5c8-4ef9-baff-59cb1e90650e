from datetime import datetime
import hashlib
import json
from typing import Dict, List, Optional
import httpx
from loguru import logger
from sqlalchemy.orm import Session

from ..misc.errors import InternalServerError
from ..schema.apollo import (
    CreateApproveRequest,
    ResetApproveRequest,
    WithdrawApproveRequest,
    ApproveDetailRequest,
    ApproveStatus
)
from ..config import settings


def get_apollo_auth_headers():
    appkey = settings.pms_app_key
    appSecret = settings.pms_app_secret
    time = str(datetime.now().timestamp())[:10]
    body = {}
    md5Value = hashlib.md5(json.dumps(body).encode()).hexdigest()

    # SHA1(appSecret + md5 + time), 三个参数拼接的字符串，进行SHA1哈希计算，转化成16进制字符(String，小写)
    checksum = appSecret + md5Value + time
    checksum = hashlib.sha1(checksum.encode()).hexdigest()

    return {
        'Cookie': f'PMS_U=tokenVer2|{appkey}|{time}|{md5Value}|{checksum}',
        'x-pms-app-key': appkey,
        'x-pms-time': time,
        'x-pms-checksum': checksum,
    }


async def request(url: str, method: str, body: dict):
    headers = get_apollo_auth_headers()
    prefix = settings.music_proxy_prefix
    async with httpx.AsyncClient() as client:
        response = None
        try:
            response = await client.request(method, f"{prefix}{url}", headers=headers, json=body)
        except Exception as e:
            logger.error("apollo request exception: {e}", e=e, uid="uid")
            raise InternalServerError(
                f"apollo request exception: {e}")

        if response is None:
            logger.error("apollo request response is None", uid="uid")
            raise InternalServerError(
                f"apollo request response is None")

        if response.json()['code'] != 200:
            logger.error(
                f"apollo request failed: return code({response.json()['code']}) message({response.json()['message']})", uid="uid")
            raise InternalServerError(f"apollo request failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']


async def create_approve(params: CreateApproveRequest):
    data = await request(
        url='/api/apollo/forms/submit',
        method='POST',
        body={
            **params.model_dump(exclude={'customField'}),
            'procDefKey': settings.apollo_proc_def_key,
            'procTaskType': 10
        }
    )

    return data


async def withdraw_approve(db: Session, params: WithdrawApproveRequest):
    data = await request(
        url='/api/apollo/process/revocation',
        method='POST',
        body={
            **params.model_dump(),
            'procDefKey': settings.apollo_proc_def_key,
        }
    )
    return data


async def approve_detail(db: Session, params: ApproveDetailRequest):
    data = await request(
        url='/api/apollo/process/list',
        method='POST',
        body={
            'type': 10,  # 10:全部流程, 不做权限过滤 0:我的全部 1:我的发起 2:我的待办 3:我的已办 4:跟踪任务 5/6:快速搜索
            'procDefKey': settings.apollo_proc_def_key,
            'procInstSerials': params.proc_inst_serials
        }
    )
    return data
