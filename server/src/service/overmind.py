from datetime import datetime
import hashlib
import base64
import hmac
import json
from typing import Dict, List, Optional
import httpx
from loguru import logger

from ..config import settings
from ..misc.errors import InternalServerError
from ..schema.overmind import (
    ApproveCheckRequest
)


def get_overmind_auth_headers():
    client_id = settings.overmind_client_id
    secret = settings.overmind_secret
    timestamp = str(int(datetime.now().timestamp() * 1000))
    token = f"{client_id}{timestamp}"

    # 使用HMAC-SHA256计算签名
    signature = hmac.new(
        secret.encode(),
        token.encode(),
        hashlib.sha256
    ).digest()
    sign = base64.b64encode(signature).decode()

    return {
        'Overmind-Auth-Client': base64.b64encode(client_id.encode()).decode(),
        'Overmind-Auth-Timestamp': timestamp,
        'Overmind-Auth-Sign': sign
    }


async def request(url: str, method: str, body: Optional[dict] = None, params: Optional[dict] = None):
    headers = get_overmind_auth_headers()
    async with httpx.AsyncClient() as client:
        try:
            response = await client.request(
                method,
                f"{settings.overmind_host}{url}",
                headers=headers,
                json=body,
                params=params
            )

            if response.status_code != 200:
                raise InternalServerError("overmind request failed")

            return response.json()

        except Exception as e:
            logger.error("overmind request exception {error} {body} {header}", error=str(
                e), uid='uid', body=body, header=headers)
            raise InternalServerError(f"overmind request exception: {str(e)}")


async def approve_check(params: ApproveCheckRequest):
    return await request(
        url='/api/data/open/version/release/rule/check',
        method='POST',
        body={
            **params.model_dump(),
            'platform': 'FEBASE'
        }
    )
