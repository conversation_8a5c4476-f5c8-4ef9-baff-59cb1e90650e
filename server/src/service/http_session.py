import json
from loguru import logger
import httpx
from typing import Any, Optional

from ..utils.model_proxy import BaseModelProxy
from ..misc.log import logger_error
from ..utils.normalize import normalize_fields, normalize_table_name, extract_value


class ModelProxy(BaseModelProxy):
    """业务相关的模型代理类，包含提交更改等功能"""

    def __init__(self, session: 'HTTPSession', data: dict):
        self._session = session
        self._changes = {}
        super().__init__(data)

    def set_value(self, name, value):
        """提供一个方法设置值不会触发commit"""
        self._data[name] = value

    def __setattr__(self, name, value):
        if name.startswith('_'):
            super().__setattr__(name, value)
            return
        if name not in self._data or self._data[name] != value:
            self._changes[name] = value
            self._data[name] = value
            self._session._to_commit.append(('update', self))

        super().__setattr__(name, value)


class HTTPQuery:
    def __init__(self, session: 'HTTPSession', model_class: Any):
        self.session = session
        self.model_class, self.id_field = normalize_table_name(
            model_class.__tablename__)
        self._filters = []
        self._offset = None
        self._limit = None
        self._order_by = None
        self._is_asc = True
        self._last_query_result = None  # 用于存储最后一次查询结果

    def order_by(self, field: str):
        # 获取字段的字符串表示，例如 tb_token.created_at DESC,
        field_str = field.__str__()

        # 判断是否为降序
        if 'DESC' in field_str:
            self._order_by = field_str.split(
                '.')[1].replace(' DESC', '')  # 提取字段名并去掉 DESC
            self._is_asc = False  # 设置为降序
        else:
            self._order_by = field_str.split(
                '.')[1].replace(' ASC', '')  # 提取字段名
            self._is_asc = True  # 默认升序

        return self

    def _get_operator_condition(self, op):
        """将 SQLAlchemy 操作符转换为对应的条件字符串"""
        operator_map = {
            '>=': 'gte',
            '<=': 'lte',
            '>': 'gt',
            '<': 'lt',
            '!=': 'ne',
            '==': 'eq',
            'in_op': 'in',
            'not_in_op': 'notIn',
            'like_op': 'like'
        }
        return operator_map.get(op, op)

    def filter(self, *args):
        # 解析过滤条件
        for arg in args:
            if hasattr(arg, 'left') and hasattr(arg, 'right'):
                value = extract_value(arg.right, True)
                condition = self._get_operator_condition(arg.operator.__name__)
                if value == 'null':
                    condition = 'isNull'
                # 使用 id_field 替代硬编码的字段名
                field_name = self.id_field if arg.left.name == 'id' else arg.left.name

                filter_item = {
                    "name": field_name,
                    "condition": condition,
                }

                # 根据条件类型决定使用 value 还是 values
                if condition == 'in' or condition == 'notIn':
                    filter_item['values'] = value if isinstance(value, list) else [
                        value]
                else:
                    filter_item['value'] = value

                self._filters.append(filter_item)
        return self

    def first(self):
        # 构建查询参数
        params = self._build_params()
        params['page'] = 1
        params['pageSize'] = 1
        response = self.session.getHttp(params=params)

        # 解析嵌套的响应结构
        if response and response.get('code') == 200:
            data = response.get('data', {}).get('values', [])
            if data:
                # 修改 id 字段
                for item in data:
                    model_id_key = self.id_field
                    if model_id_key in item:
                        item['origin_id'] = item['id']
                        item['id'] = item.pop(model_id_key)

                # 处理 created_by 字段
                data = normalize_fields(self.id_field, data)

                # 记录查询结果
            self._last_query_result = data
            # 返回代理对象而不是原始字典
            return ModelProxy(self.session, data[0]) if data else None
        return None

    def all(self):
        params = self._build_params()
        response = self.session.getHttp(params=params)

        if response and response.get('code') == 200:
            data = response.get('data', {})
            values = data.get('values', [])

            # 处理 id 字段映射
            values = normalize_fields(self.id_field, values)
            data['values'] = values

            # 记录查询结果
            self._last_query_result = data

            # 返回包含分页信息的字典
            return [ModelProxy(self.session, item) for item in values]
        return []

    def count(self):
        # 如果已经有查询结果，直接返回total
        if self._last_query_result:
            return self._last_query_result.get('total', 0)

        # 否则执行一次查询以获取total
        params = {
            "filters": self._filters,
            "page": 1,
            "pageSize": 1
        }
        response = self.session.getHttp(params=params)
        if response and response.get('code') == 200:
            data = response.get('data', {})
            values = data.get('values', [])
            data['total'] = data.get('total', len(values))
            self._last_query_result = data

            return data.get('total', 0)
        return 0

    def offset(self, offset: int):
        self._offset = offset
        return self

    def limit(self, limit: int):
        self._limit = limit
        return self

    def delete(self):
        params = {
            "filters": self._filters,
            "page": 1,
            "pageSize": 1
        }
        # 检查是否有 id 字段的过滤条件
        id_filter = next(
            (f for f in self._filters if f.get('name') == self.id_field),
            None
        )
        if id_filter:
            # 如果有 id 过滤条件，直接调用 updateHttp
            self.session.deleteHttp({
                "uniqueKey": id_filter['value'],
            })
        else:
            res = self.session.getHttp(params=params)
            if res and res.get('code') == 200:
                self.session.deleteHttp({
                    "uniqueKey": res.get('data', {}).get('values', [{}])[0].get(self.id_field),
                })

    def update(self, change: Optional[dict] = None):
        params = {
            "filters": self._filters,
            "page": 1,
            "pageSize": 1
        }

        # 查是否有 id 字段的过条件
        id_filter = next(
            (f for f in self._filters if f.get('name') == self.id_field),
            None
        )

        # 对 change 的每个属性值进行 extract_value 处理
        if change is not None:
            # 处理 change 的 key
            new_columns = {}
            for key, value in change.items():
                if hasattr(key, 'key'):
                    new_columns[key.key] = value
                else:
                    new_columns[key] = value
            change = {k: extract_value(
                v) for k, v in new_columns.items() if isinstance(k, str)}

        if id_filter:
            # 如果有 id 过滤条件，直接调用 updateHttp
            self.session.updateHttp({
                "uniqueKey": id_filter['value'],
                "value": change
            })
        else:
            # 否则走 upsert 逻辑
            self.session.upsert(query=params, change=change)

    def _build_params(self):
        # 基础参数
        params = {
            "filters": self._filters,
            "page": 1,
            "pageSize": 100  # 默认页大小
        }

        # 添加分页参数
        if self._limit is not None:
            params['pageSize'] = self._limit
        if self._offset is not None:
            params['page'] = (self._offset // params['pageSize']) + 1
            params['offset'] = self._offset

        # 添加排序参数
        if self._order_by:
            params['orderBy'] = self._order_by
            params['asc'] = self._is_asc

        return params


class HTTPSession:
    def __init__(self, base_url: str, table_name: Optional[str] = None):
        self.base_url = base_url
        if table_name:
            self.name, self.id_field = normalize_table_name(table_name)
        else:
            self.name = None
            self.id_field = None
        self.client = httpx.Client(timeout=90.0)
        self._to_commit = []

    def query(self, model_class):
        self.name, self.id_field = normalize_table_name(
            model_class.__tablename__)
        return HTTPQuery(self, model_class)

    def add(self, obj):
        self._to_commit.append(('add', obj))  # 添加操作标签

    def delete(self, obj):
        self._to_commit.append(('delete', obj))  # 删除操作标签

    def update(self, obj):
        self._to_commit.append(('update', obj))  # 更新操作标签

    def upsert(self, query: dict, change: Optional[dict] = None):
        self._to_commit.append(
            ('upsert', {'query': query, 'change': change}))  # 更新操作标签

    def commit(self):
        for operation, obj in self._to_commit:
            if operation == 'upsert':
                data = self.getHttp(obj['query'])
                dataId = data.get('data', {}).get(
                    'values', [{}])[0].get(self.id_field)
                self.updateHttp({
                    "uniqueKey": dataId,
                    "value": {
                        **obj['change']
                    }
                })
            elif operation == 'delete':
                request_data = {
                    "uniqueKey": obj.id
                }
                self.deleteHttp(request_data)
            elif operation == 'add':
                request_data = {
                    "values": [{
                        # 如果字段是 id 且存在 id_field，则使用 id_field 作为键名
                        (self.id_field if c.name == 'id' and self.id_field else c.name): getattr(obj, c.name)
                        for c in obj.__table__.columns
                        if not c.name.startswith('_')
                    }]
                }
                json = self.createHttp(request_data)
                data = json.get('data', {})
                obj.__dict__.update(normalize_fields(
                    self.id_field, data.get('values', [{}])[0]))
            elif operation == 'update':
                request_data = {
                    "uniqueKey": obj._data["id"],
                    "value": {
                        k: extract_value(v) for k, v in obj._changes.items() if v is not None
                    }
                }
                self.updateHttp(request_data)
                obj._changes.clear()

        self._to_commit.clear()

    def refresh(self, obj):
        if hasattr(obj, 'id'):
            return obj

    def _handle_response(self, response, params: Optional[dict] = None):
        """统一处理响应"""
        response.raise_for_status()
        res = response.json()
        if res.get('code') != 200:
            error_msg = res.get('debugInfo') or res.get(
                'message')  # 优先使用 debugInfo
            logger.error("DDB请求出错：{error_msg} params={params}",
                         error_msg=error_msg, uid='uid', params=params)
            raise Exception(f"DDB请求出错：{self.name} {error_msg}")
        return res

    def getHttp(self, params: Optional[dict] = None) -> Any:
        url = f"{self.base_url}/langbase/inner/common/search/{self.name}/"
        logger.info("getHttp url: {url}", url=url, uid='uid')
        response = self.client.post(url, json=params)
        return self._handle_response(response, params)

    def createHttp(self, params: Optional[dict] = None):
        url = f"{self.base_url}/langbase/inner/common/add/{self.name}/"
        logger.info("createHttp url: {url}", url=url, uid='uid')
        # 直接在这边进行values的处理，过滤None
        if params and 'values' in params:
            params['values'] = [
                {k: (extract_value(v)[:36] if k in ['created_by', 'updated_by'] and v is not None else extract_value(v))
                 for k, v in item.items() if v is not None}
                for item in params['values']
            ]
        # 把 params 用json格式打印出来
        # print(f"createHttp params: {params}")
        response = self.client.post(url, json=params)
        return self._handle_response(response, params)

    def updateHttp(self, params: Optional[dict] = None):
        url = f"{self.base_url}/langbase/inner/common/update/{self.name}/"
        # 直接在这边进行value的处理，过滤None
        if params and 'value' in params:
            params['value'] = {
                k: (extract_value(v)[:36] if k in [
                    'created_by', 'updated_by'] and v is not None else extract_value(v))
                for k, v in params['value'].items() if v is not None
            }
        logger.info("updateHttp url: {url} {params}",
                    url=url, uid='uid', params=json.dumps(params))

        # print('___________', url, params, '______________')
        response = self.client.post(url, json=params)
        # print('___________', response, '______________')
        return self._handle_response(response, params)

    def deleteHttp(self, params: Optional[dict] = None):
        url = f"{self.base_url}/langbase/inner/common/delete/{self.name}/"
        logger.info("deleteHttp url: {url}", url=url, uid='uid')
        response = self.client.post(url, json=params)
        return self._handle_response(response, params)

    def listComponentsHttp(self, params: Optional[dict] = None):
        url = f"{self.base_url}/langbase/inner/common/complex/component"
        response = self.client.post(url, json=params)
        logger.info(
            "listComponentsHttp url: {url} {params}", url=url, uid='uid', params=params)
        response = self._handle_response(response, params)
        if response and response.get('code') == 200:
            data = response.get('data', {})
            values = data.get('values', [])

            # 处理 id 字段映射
            values = normalize_fields(self.id_field, values)
            # data['values'] = values
            data['values'] = [ModelProxy(self, item) for item in values]

            # 返回包含分页信息的字典
            return data
        return None
