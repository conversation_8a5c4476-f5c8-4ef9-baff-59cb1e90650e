from typing import List, Optional, Tuple
from sqlalchemy.orm import Session

from ..misc.errors import NotFound
from datetime import datetime

from ..models.component import Component
from ..models.component_category import ComponentCategory
from ..schema.component_category import ComponentCategoryCreate, ComponentCategoryUpdate


async def get(db: Session, component_category_id: str):
    db_component_category = db.query(ComponentCategory) \
        .filter(ComponentCategory.id == component_category_id).first()
    if db_component_category is None:
        raise NotFound(f"ComponentCategory({component_category_id}) not found")
    return db_component_category


async def delete(db: Session,
                 user_id: str,
                 component_category_id: str):
    _ = await get(db, str(component_category_id))
    db.query(ComponentCategory).filter(ComponentCategory.id == component_category_id).update(
        {ComponentCategory.deleted_at: datetime.now(),
         ComponentCategory.deleted_by: user_id}
    )
    db.commit()


async def update(db: Session,
                 user_id: str,
                 component_category_id: str,
                 component_category_update: ComponentCategoryUpdate):
    db_component_category = await get(db, component_category_id)
    # update component category
    columns = component_category_update.model_dump(exclude_unset=True)
    columns[ComponentCategory.updated_by] = user_id
    db.query(ComponentCategory).filter(ComponentCategory.id == component_category_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_component_category)

    if db_component_category.name != component_category_update.name:
        db.query(Component).filter(Component.category_id == component_category_id).update(
            {Component.category_name: component_category_update.name}
        )
        db.commit()

    return db_component_category


async def list(db: Session,
               offset: Optional[int] = None,
               limit: Optional[int] = None) \
        -> Tuple[List[ComponentCategory], int]:
    stat = db.query(ComponentCategory)
    count = stat.count()
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)

    return (stat.all(), count)


async def create(db: Session,
                 component_category_create: ComponentCategoryCreate,
                 user_id: str):
    component_category = ComponentCategory(**component_category_create.model_dump())
    component_category.created_by = user_id
    component_category.updated_by = user_id
    db.add(component_category)
    db.commit()
    db.refresh(component_category)
    return component_category
