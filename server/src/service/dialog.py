import asyncio
from datetime import datetime
from json import JSONDecodeError, dumps, loads
import json
import os
import tempfile
import time
from typing import Any, AsyncGenerator, Dict, List, Optional

from fastapi import Depends
import httpx
from loguru import logger
from sqlalchemy.orm import Session

from ..lib.llms.get_models import fetch_all_models

from ..misc.redis_utils import MEDIA_CACHE_KEY, MODEL_TEST_KEY, get_redis_sync, set_redis_sync
from .app import get as get_app_service, get_config as get_app_config_service

from ..misc.errors import BadRequest

from ..misc.log import logger_error, logger_info

from ..config import settings
from ..lib.llms.base import Model, isModelSupportMultiModal
from ..lib.llms.excutor import DomainLLMRunner
from ..lib.llms.openai import ToolCall
from ..lib.toolpool import get_tool_pool
from ..misc.db import get_db
from ..models.dialog import Conversation as DBConversation
from ..utils.model_test_cases import test_scenarios
from ..schema.app import AgentConfig, ModelConfig
from ..schema.dialog import (AILLMMessage, AudioUrl, ChatCompletionContentPartImageParam,
                             ChatCompletionContentPartParam,
                             ChatCompletionContentPartTextParam, CompletionChunk,
                             CompletionRequest, CompletionResp, ConversationCreate, ConversationMessage, ConversationRequest, ImageUrl,
                             LLMMessage, MessageCreate,
                             MessageResponseType,
                             MessageToolCallResponseCreate, MessageUpdate, NewChatCompletionContentPartAudioParam, NewChatCompletionContentPartImageParam, NewChatCompletionContentPartParam, NewChatCompletionContentPartVideoParam, VideoUrl)
from ..schema.dialog import Response as LLMResponse
from ..schema.dialog import ResponseChunk as LLMResponseChunk
from ..schema.dialog import ResponseMode, ToolLLMMessage
from ..schema.model import ModelProviderKind, ModelType, MultiModalType
from ..schema.plugin import Plugin
from ..util import template_render
from .chat import (create_message,
                   create_message_tool_call_responses, get_or_create_db_conversation,
                   list_message, list_message_tool_call_response, llm_request_log_error,
                   update_message)
from ..service.knowledge import get_match_content, get_match_content_v2
from markitdown import MarkItDown

TOOL_RESPONSES = 'tool_responses'
REFERENCES = 'references'
TOOL_CALLS = 'tool_calls'

IS_CHUNK_RESPONSE = 'is_chunk_response'


async def config_merge(
    app_config: AgentConfig,
    config: Optional[AgentConfig],
):
    if config is None:
        return app_config
    if config.modelName is not None:
        app_config.modelName = config.modelName
    if config.modelParams is not None:
        app_config.modelParams = config.modelParams
    if config.providerKind is not None:
        app_config.providerKind = config.providerKind
    if config.tools is not None:
        app_config.tools = config.tools
    if config.prePrompt is not None:
        app_config.prePrompt = config.prePrompt
    if config.paramsInPrompt is not None:
        app_config.paramsInPrompt = config.paramsInPrompt
    if config.prologue is not None:
        app_config.prologue = config.prologue
    if config.knowledge is not None:
        app_config.knowledge = config.knowledge
    if config.knowledgeConfig is not None:
        app_config.knowledgeConfig = config.knowledgeConfig
    if config.modelsConfig is not None:
        app_config.modelName = config.modelsConfig.models[0].modelName
        app_config.modelParams = config.modelsConfig.models[0].modelParams
        app_config.providerKind = config.modelsConfig.models[0].providerKind
        app_config.modelsConfig = config.modelsConfig
    # 说明是新版的，需要从modelConfig中获取
    if app_config.modelName is None and app_config.modelsConfig is not None:
        app_config.modelName = app_config.modelsConfig.models[0].modelName
        app_config.modelParams = app_config.modelsConfig.models[0].modelParams
        app_config.providerKind = app_config.modelsConfig.models[0].providerKind
    # 现在新版如果app_config.modelsConfig不为空，则默认会用该数据
    if app_config.modelsConfig is not None:
        app_config.modelsConfig.models[0].modelName = config.modelName or app_config.modelsConfig.models[0].modelName
        app_config.modelsConfig.models[0].modelParams = config.modelParams or app_config.modelsConfig.models[0].modelParams
        app_config.modelsConfig.models[0].providerKind = config.providerKind or app_config.modelsConfig.models[0].providerKind
    return app_config


async def render_prompt(
        config: Optional[AgentConfig],
        parameters: Optional[dict]):
    if config is None \
            or config.prePrompt is None:
        return None
    if parameters is None:
        parameters = {}

    # 补充默认值
    for param in config.paramsInPrompt if config.paramsInPrompt is not None else []:
        if (param.key not in parameters and param.default_val):
            parameters[param.key] = param.default_val

    return template_render(config.prePrompt, **parameters)

# 新版构造多模态消息（aigw格式）


def construct_multi_modal_messages(params, config, text=None, prompt_params=None):
    """
    构造多模态消息（aigw格式）

    Args:
        params: 请求参数
        config: 应用配置
        text: 文本内容，如果为None则从params中获取

    Returns:
        构造好的LLMMessage对象
    """
    # 收集所有图片URL
    images = []
    audios = []
    videos = []
    # 如果未提供text，则从params中获取
    if text is None and hasattr(params, 'message'):
        text = params.message

    # prompt_params 中如果有_mediaParam，则将_mediaParam中的items中的file类型内容添加到images中
    if prompt_params is not None and '_mediaParam' in prompt_params:
        media_param = prompt_params['_mediaParam']
        if media_param is not None:
            if 'items' in media_param and isinstance(media_param['items'], list):
                for item in media_param['items']:
                    if item.get('type') == 'image':
                        images.append(item.get('content', {}).get('url', ''))
                    elif item.get('type') == 'audio':
                        audios.append(item.get('content', {}).get('url', ''))
                    elif item.get('type') == 'video':
                        videos.append(item.get('content', {}).get('url', ''))

    # 添加用户输入的多模态信息
    if isModelSupportMultiModal(config.modelName, 'view'):
        contents = []

        # 从params.images获取图片
        if hasattr(params, 'images') and params.images:
            images.extend(params.images)

        # 从params.imageUrl获取图片
        if hasattr(params, 'imageUrl') and params.imageUrl:
            images.extend(params.imageUrl.split(','))

        if hasattr(params, 'audioUrl') and params.audioUrl:
            audios.extend(params.audioUrl.split(','))

        if hasattr(params, 'videoUrl') and params.videoUrl:
            videos.extend(params.videoUrl.split(','))

        # 从params.image_url获取图片
        if hasattr(params, 'image_url') and params.image_url:
            images.append(params.image_url)
        if text:
            contents.append(ChatCompletionContentPartTextParam(
                type='text', text=text))
        # 添加所有图片到内容中
        for image in images:
            contents.append(NewChatCompletionContentPartImageParam(
                type="image_url", image_url=ImageUrl(url=image)))
        for audio in audios:
            contents.append(NewChatCompletionContentPartAudioParam(
                type="audio_url", audio_url=AudioUrl(url=audio)))
        for video in videos:
            contents.append(NewChatCompletionContentPartVideoParam(
                type="video_url", video_url=VideoUrl(url=video)))
        return LLMMessage(
            role='user',
            content=contents,
        )
    else:
        # 非多模态模型，只返回文本内容
        return LLMMessage(
            role='user',
            content=text or '',
        )


def appendMultiModalMessages(params, config, messages, prompt_params):
    """
    将多模态消息添加到消息列表中

    Args:
        params: 请求参数
        config: 应用配置
        messages: 消息列表
    """
    message = construct_multi_modal_messages(
        params, config, None, prompt_params)
    messages.append(message)


async def tools_run(db: Session, app_id: str, tool_calls: List[ToolCall]):
    error_results = {}
    logger.debug(f'tool calls: {tool_calls}')
    args_dict = {}
    for tool_call in tool_calls:
        try:
            tool_args = loads(tool_call.function.arguments)
            args_dict[tool_call.id] = (tool_call.function.name, tool_args)
        except Exception as e:
            logger.bind(tool_call=tool_call).error(
                f'failed to load arguments: {e}')
            error_results[tool_call.id] = f"arguments are invalid: {tool_call.function.arguments} \
                is not a valid json string"

    tool_pool = await get_tool_pool()
    results = await tool_pool.run(db=db, app_id=app_id, tool_call_dict=args_dict)

    results = {
        call_id: dumps(result) if not isinstance(result, str) else result
        for call_id, result in results.items()
    }
    for call_id, result in error_results.items():
        results[call_id] = result
    return results


def loadUserQuery(query):
    try:
        contents = loads(query)
        if isinstance(contents, list) and len(contents) == 1 and isinstance(contents[0], dict) and contents[0].get('type') == 'text':
            return contents[0].get('text', '')
        return [
            ChatCompletionContentPartTextParam.model_validate(content) if content.get('type') == 'text'
            else ChatCompletionContentPartImageParam.model_validate(content) for content in contents]
    except Exception:
        return query


async def construct_messages(
    db: Session,
    conversation_id: Optional[str],
    config: Optional[AgentConfig],
    prompt_params: Optional[dict],
    max_conversations: Optional[int] = None,
):
    messages: List[LLMMessage] = list()

    prompt = await system_prompt(config, prompt_params)  # type: ignore
    if prompt is not None:
        messages.append(prompt)

    if not max_conversations:
        max_conversations = settings.conversation_history_limit

    if conversation_id is not None:
        # 拿最新的max_conversations条消息
        db_messages, count = await list_message(
            db=db,
            conversation_id=conversation_id,
            limit=(max_conversations + 1) // 2,
            ascending=False)

        # 因为是按照时间倒序排列的，所以要倒序输出
        for db_message in db_messages[::-1]:
            msg = ConversationMessage.model_validate(db_message)

            if msg.query != '':
                messages.append(LLMMessage(
                    role='user',
                    content=loadUserQuery(msg.query),
                ))

            if str(db_message.response_type) == MessageResponseType.TOOL_CALLS.value:
                messages.append(AILLMMessage(
                    tool_calls=msg.response,  # type: ignore
                ))
                tool_call_repsonsess, _ = await list_message_tool_call_response(
                    db,
                    message_id=str(db_message.id),
                    ascending=True,
                )
                for tool_call_repsonse in tool_call_repsonsess:
                    messages.append(ToolLLMMessage(
                        tool_call_id=str(tool_call_repsonse.tool_call_id),
                        content=str(tool_call_repsonse.response),
                    ))
            else:
                if msg.response is not None:
                    if isinstance(msg.response, list):
                        messages.append(AILLMMessage(
                            content=msg.response[0].content  # type: ignore
                        ))
                    else:
                        messages.append(AILLMMessage(
                            content=msg.response  # type: ignore
                        ))

    return messages


async def media_param_render(file_content: str):
    # 先获取系统提示词，从特定的appId中获取
    app_id = settings.rag_app_id
    app = await get_app_service(db=None, app_id=app_id)
    app_config = await get_app_config_service(db=None, config_id=str(app.app_config_id))
    if app_config.config is None:
        raise BadRequest('media_param_render: app_config is None')
    app_config = AgentConfig.model_validate(app_config.config)
    if app_config.prePrompt is None:
        raise BadRequest('media_param_render: app_config.prePrompt is None')

    return template_render(app_config.prePrompt, **{'docs': file_content})


async def get_file_content(file_url: str, file_nos_key: str) -> Optional[str]:
    """
    获取文件内容，优先从缓存获取，缓存不存在则下载并解析

    Args:
        file_url: 文件URL
        file_nos_key: NOS存储的文件key

    Returns:
        解析后的文件内容
    """
    if not file_url and not file_nos_key:
        logger.error(
            "get_file_content: file_url and file_nos_key are both empty", uid='uid')
        return None

    cache_key = MEDIA_CACHE_KEY + '::' + (file_nos_key or file_url)
    cache_content = get_redis_sync(cache_key)

    if cache_content:
        return cache_content
    else:
        file_content = await download_and_parse_file(url=file_url, nos_key=file_nos_key)
        if file_content:
            # 设置1天缓存
            set_redis_sync(cache_key, file_content, 86400)
            return file_content

    return None


async def process_media_item(item: dict) -> Optional[str]:
    """
    处理单个媒体项，获取并渲染内容

    Args:
        item: 媒体项数据

    Returns:
        渲染后的提示词
    """
    if item.get('type') == 'file':
        content = item.get('content', {})
        file_url = content.get('url', '')
        file_nos_key = content.get('key', '')

        file_content = await get_file_content(file_url, file_nos_key)
        if file_content:
            return await media_param_render(file_content)

    return None


async def system_prompt(
        config: Optional[AgentConfig],
        prompt_params: Optional[dict],
):
    prompt = ''
    # 说明需要特殊组装的提示词
    if prompt_params is not None and '_mediaParam' in prompt_params:
        media_param = prompt_params['_mediaParam']

        # 兼容新的结构，检查是否有items字段
        if media_param is not None:
            # 新结构: 有items字段，是一个数组
            if 'items' in media_param and isinstance(media_param['items'], list):
                for item in media_param['items']:
                    if item.get('type') == 'file':
                        rendered_prompt = await process_media_item(item)
                        if rendered_prompt:
                            prompt = rendered_prompt
                        break  # 处理第一个文件即可

            # 旧结构: 直接是文件信息
            elif media_param.get('type') == 'file':
                rendered_prompt = await process_media_item(media_param)
                if rendered_prompt:
                    prompt = rendered_prompt
                else:
                    raise BadRequest(
                        'media_param_render: failed to process file')
    else:
        prompt = await render_prompt(config, prompt_params)

    if prompt:
        return LLMMessage(
            role='system',
            content=prompt
        )


async def user_prompt(
        config: Optional[AgentConfig],
        prompt_params: Optional[dict],
):
    prompt = await render_prompt(config, prompt_params)
    if prompt:
        return LLMMessage(
            role='user',
            content=prompt
        )

default_model_params = {
    'max_tokens': 512,
    'temperature': 0.5,
    'top_p': 0.85,
    'frequency_penalty': 0.1,
    'presences_penalty': 0.1,
}


async def completion_v2(
    completion_request: CompletionRequest,
    app_id: str,
    user_id: str,
    authorization: Optional[str] = None
):
    # 改成直接请求后端服务（同步）POST http://qa-aleut.igame.163.com/langbase/agent/chat
    async with httpx.AsyncClient() as client:
        data = {
            "appId": app_id,
            "imageUrl": completion_request.imageUrl,
            "message": "",
            "userId": user_id,
            "parameters": completion_request.parameters,
        }

        response = await client.post(
            url=f"{settings.service_proxy_prefix}/api/langbase/agent/completion",
            json=data,
            timeout=100,
            headers={"Authorization": authorization} if authorization else {},
        )
        if response.content:
            logger.info("completion_v2-proxy response: {response} {data}",
                        response=response.json(), data=data, uid='uid')
            # 返回data中的messageList
            message_list = extract_message_list(response)
            return message_list
        else:
            return response.content


async def agentw_chat_v2(
    app_id: str,
    inputs: dict,
    user_id: str,
    chat_id: Optional[str] = None,
    authorization: Optional[str] = None
):
    async with httpx.AsyncClient() as client:
        try:
            data = {
                "appId": app_id,
                "inputs": inputs,
                "userId": user_id,
            }
            if chat_id:
                data["chatId"] = chat_id

            response = await client.post(
                url=f"{settings.service_proxy_prefix}/api/langbase/agent/workflow",
                json=data,
                timeout=100,
                headers={"Authorization": authorization} if authorization else {},
            )
            if response.content:
                logger.info("agentw_chat_v2-proxy response: {response} {data}",
                            response=response.json(), data=data, uid='uid')
                # 返回data中的messageList
                message_list = extract_agentw_message_list(response)
                return message_list
            else:
                return response.content
        finally:
            # 确保response资源被正确关闭
            if 'response' in locals() and hasattr(response, 'aclose'):
                await response.aclose()


async def chat_v2(
    chat_request: ConversationRequest,
    app_id: str,
    user_id: str,
    conversation_id: Optional[str] = None,
    authorization: Optional[str] = None
):
    # 改成直接请求后端服务（同步）POST http://qa-aleut.igame.163.com/langbase/agent/chat
    async with httpx.AsyncClient() as client:
        data = {
            "message": chat_request.message,
            "appId": app_id,
            "userId": user_id,
            "parameters": chat_request.parameters,
        }
        if conversation_id:
            data["conversationId"] = conversation_id

        response = await client.post(
            url=f"{settings.service_proxy_prefix}/api/langbase/agent/chat",
            json=data,
            timeout=100,
            headers={"Authorization": authorization} if authorization else {},
        )
        if response.content:
            logger.info("chat_v2-proxy response: {response} {data}",
                        response=response.json(), data=data, uid='uid')
            # 返回data中的messageList
            message_list = extract_message_list(response)
            return message_list
        else:
            return response.content

# 一个文档下载解析的模块（利用markdownit解析)


async def download_and_parse_file(url: Optional[str] = None, nos_key: Optional[str] = None):
    """
    根据nosKey下载文件并解析为markdown

    Args:
        nos_key: NOS存储的文件key

    Returns:
        解析后的markdown文本
    """
    if not url and not nos_key:
        raise BadRequest('download_and_parse_file: url or nos_key is required')
    if url:
        url = url
    else:
        if nos_key is None:
            raise BadRequest('download_and_parse_file: nos_key is None')
        parts = nos_key.split('/')
        bucket = parts[0]
        file_path = '/'.join(parts[1:])
        url = f"https://{bucket}.nos-jd.163yun.com/{file_path}"

    try:
        # 下载文件
        # async with httpx.AsyncClient() as client:
        #     response = await client.get(url)
        # response.raise_for_status()

        # # 创建临时文件保存下载内容
        # with tempfile.NamedTemporaryFile(delete=False, suffix='.md') as temp_file:
        #     temp_file.write(response.content)
        #     temp_path = temp_file.name

        # 解析文件内容
        parsed_content = parse_text_file(url)
        text_content = parsed_content.text_content

        return text_content
    except Exception as e:
        logger.error("download_and_parse_file error: {e}", e, uid='uid')
        return ""


def parse_text_file(file_path: str):
    """解析文本文件为markdown格式"""
    # 使用markitdown解析
    md = MarkItDown()
    return md.convert(file_path)


async def simple_completion(
    app_id: str,
    user_id: str,
    db: Session,
    messages: List[LLMMessage],
    model_name: Optional[str] = None,
    provider_kind: Optional[str] = None,
):
    app = await get_app_service(db=None, app_id=app_id)
    conversation = await get_or_create_db_conversation(
        db,
        user_id=user_id,
        conversation_create=ConversationCreate(
            app_id=app_id,
            app_config_id=str(app.app_config_id),
            parameters={},
        ),
    )

    app_config = await get_app_config_service(
        db,
        str(app.app_config_id))
    app_config = AgentConfig.model_validate(app_config.config)

    config = AgentConfig(
        modelName=model_name,
        providerKind=provider_kind,
    )

    executor = await DomainLLMRunner(
        workspace_id=str(app.workspace_id),
        group_id=str(app.group_id),
        db=db,
    )

    try:
        resp = dialogue(
            app_id=app.id,
            config=config,
            biz_type='agent-completion',
            params=CompletionRequest(parameters={}),
            response_mode=ResponseMode.JSON,
            messages=messages,
            executor=executor,
            user_id=user_id,
            plugins=None,
            db=db,
            conversation=conversation)
        responses = []
        async for r in resp:
            responses.append(r)
        if hasattr(resp, 'aclose'):
            await resp.aclose()
        return responses[-1]
    finally:
        if 'resp' in locals() and hasattr(resp, 'aclose'):
            await resp.aclose()


async def dialogue(
    app_id: str,
    params: CompletionRequest | ConversationRequest,
    response_mode: ResponseMode,
    user_id: str,
    conversation: DBConversation,
    config: AgentConfig,
    executor: DomainLLMRunner,
    messages: List[LLMMessage],
    plugins: Optional[List[Plugin]] = None,
    db: Session = Depends(get_db),
    api_key: Optional[str] = None,
    api_base: Optional[str] = None,
    biz_type: Optional[str] = None,
):
    resp = None
    try:
        message = messages[-1]
        query = ''
        if config.modelsConfig is not None:
            config.modelName = config.modelsConfig.models[0].modelName
            config.modelParams = config.modelsConfig.models[0].modelParams
            config.providerKind = config.modelsConfig.models[0].providerKind
        content = message.content
        if isinstance(params, CompletionRequest):
            query = json.dumps(params.parameters, ensure_ascii=False)
        else:
            if isinstance(message.content, list):
                content = message.content[0]
                if isinstance(content, ChatCompletionContentPartTextParam):
                    query = content.text
            if isinstance(content, str):
                query = content
        app = await get_app_service(db=db, app_id=app_id)
        # 先判断新的知识库匹配
        if (config.knowledgeConfig is not None):
            messages[0].content = await get_match_content_v2(config, messages, app.group_id, query)
        else:
            messages[0].content = await get_match_content(config, messages)
        # 多模态模型，content值类型为列表
        if isinstance(message.content, list):
            query: str = dumps([content.model_dump()
                               for content in message.content])
        elif isinstance(params, CompletionRequest):
            if settings.enable_query:
                query = json.dumps(params.parameters, ensure_ascii=False)
            else:
                query = ''
        else:
            query = params.message

        is_tool_call_response = False
        model_params = default_model_params

        if config.modelParams is not None:
            model_params.update(config.modelParams)

        # 如果params中有模型参数（agent-workflow）
        if params is not None and isinstance(params.parameters, dict):
            model_params = {k: params.parameters.get(
                k, default_model_params[k]) for k in default_model_params}

        while True:
            start_time = datetime.now()
            logger.info("model_params: {model_params} {config} {params}",
                        model_params=model_params, config=config, params=params, uid="uid")

            try:
                resp = await executor.chat(  # type: ignore
                    model_name=config.modelName,
                    model_params=model_params,
                    response_mode=response_mode,
                    user_id=user_id,
                    provider_kind=config.providerKind,
                    plugins=plugins,
                    messages=messages,
                    api_base=api_base,
                    api_key=api_key,
                )
            except Exception as e:
                # 如果 e.message 包含 "code: 429，则等待10秒后重试
                await llm_request_log_error(
                    "code: 429" in str(e), str(biz_type), str(app_id), str(config.modelName))
                if response_mode == ResponseMode.STREAMING:
                    message = str(e)
                    dic = {'event': 'message',
                           'data': json.dumps({'content': message})}
                    yield dic
                    return
                else:
                    raise e
            end_time = datetime.now()

            time_comsumption = int(
                (end_time - start_time).total_seconds() * 1000)
            # 找到messages中第一个role为user的消息
            user_message = next(
                (msg for msg in reversed(messages) if msg.role == 'user'), None)
            if user_message is None:
                raise BadRequest('user_message is None')
            user_content = (user_message.content[0].text
                            if isinstance(user_message.content, list) and user_message.content[0].type == 'text'
                            else user_message.content if isinstance(user_message.content, str)
                            else '')
            message = await create_message(
                db,
                user_id=user_id,
                biz_type=biz_type,
                message_create=MessageCreate(
                    query=user_content if not is_tool_call_response else '',
                    conversation_id=str(conversation.id),
                    model_name=config.modelName,
                    kind=config.providerKind,
                ))

            if isinstance(resp, AsyncGenerator):
                content: str = ''
                start_time = datetime.now()

                async def add_list(chunk: LLMResponseChunk):
                    if ((chunk.reasoning_content is None and chunk.content is None) or
                            (chunk.total_tokens is not None and chunk.total_tokens > 0)):
                        if chunk.total_tokens is None or chunk.total_tokens == 0:
                            return
                        end_time = datetime.now()
                        inner_time_comsumption = \
                            int((end_time - start_time).total_seconds() * 1000)
                        nonlocal content, message
                        logger.info("update message content: {content}",
                                    content=content, uid='uid')

                        await update_message(
                            db=db,
                            biz_type=biz_type,
                            message_id=str(message.id) if hasattr(
                                message, 'id') else message,
                            message_update=MessageUpdate(
                                response=content,
                                response_type=MessageResponseType.TEXT,
                                total_tokens=chunk.total_tokens if chunk.total_tokens is not None else 0,
                                prompt_tokens=chunk.prompt_tokens if chunk.prompt_tokens is not None else 0,
                                response_tokens=chunk.response_tokens if chunk.response_tokens is not None else 0,
                                time_comsumption_in_ms=(
                                    time_comsumption + inner_time_comsumption),
                            )
                        )

                        return
                    content += chunk.content or ''
                    chunk_dict = chunk.model_dump()
                    chunk_dict['messageID'] = str(message.id) if hasattr(
                        message, 'id') else message
                    chunk_dict['conversationID'] = str(conversation.id)
                    chunk_dict[IS_CHUNK_RESPONSE] = True
                    return chunk_dict

                try:
                    async for chunk in resp:
                        if chunk.is_tool_calls:
                            tool_responses = await tools_run(db, app_id, chunk.tool_calls)

                            await update_message(
                                db,
                                biz_type=biz_type,
                                message_id=message.id,
                                message_update=MessageUpdate(
                                    response=[tool_call.model_dump()
                                              for tool_call in chunk.tool_calls],
                                    response_type=MessageResponseType.TOOL_CALLS,
                                    total_tokens=chunk.total_tokens if chunk.total_tokens is not None else 0,
                                    prompt_tokens=chunk.prompt_tokens if chunk.prompt_tokens is not None else 0,
                                    response_tokens=chunk.response_tokens if chunk.response_tokens is not None else 0,
                                    time_comsumption_in_ms=time_comsumption,
                                )
                            )

                            await create_message_tool_call_responses(
                                db,
                                message_id=message.id,
                                responses=[
                                    MessageToolCallResponseCreate(
                                        message_id=str(message.id),
                                        tool_call_id=tool_call.id,
                                        response=tool_responses.get(
                                            tool_call.id, ''),
                                    )
                                    for tool_call in chunk.tool_calls
                                ]
                            )

                            messages.append(
                                AILLMMessage(
                                    tool_calls=[tool.model_dump()
                                                for tool in chunk.tool_calls],
                                )
                            )

                            if tool_responses is not None:
                                for (id, tool_response) in tool_responses.items():
                                    messages.append(
                                        ToolLLMMessage(
                                            tool_call_id=id,
                                            content=tool_response,
                                        )
                                    )
                            is_tool_call_response = True

                            resp_dict = chunk.model_dump()

                            resp_dict['messageID'] = str(message.id)
                            resp_dict['conversationID'] = str(conversation.id)
                            resp_dict[TOOL_CALLS] = [
                                tool_call.model_dump()
                                for tool_call in chunk.tool_calls]
                            resp_dict[IS_CHUNK_RESPONSE] = True
                            resp_dict[TOOL_RESPONSES] = tool_responses
                            yield resp_dict
                        else:
                            is_tool_call_response = False
                            yield await add_list(chunk)
                finally:
                    # 确保异步生成器被正确关闭
                    if hasattr(resp, 'aclose'):
                        await resp.aclose()
                if not is_tool_call_response:
                    return
            elif isinstance(resp, LLMResponse):
                tool_responses = None
                if resp.is_tool_calls and resp.tool_calls:
                    tool_responses = await tools_run(db, app_id, resp.tool_calls)

                    _ = await update_message(
                        db,
                        biz_type=biz_type,
                        message_id=message.id,
                        message_update=MessageUpdate(
                            response=[tool_call.model_dump()
                                      for tool_call in resp.tool_calls],
                            response_type=MessageResponseType.TOOL_CALLS,
                            total_tokens=resp.total_tokens if resp.total_tokens is not None else 0,
                            prompt_tokens=resp.prompt_tokens if resp.prompt_tokens is not None else 0,
                            response_tokens=resp.response_tokens if resp.response_tokens is not None else 0,
                            time_comsumption_in_ms=time_comsumption,
                        ))

                    await create_message_tool_call_responses(
                        db,
                        message_id=message.id,
                        responses=[
                            MessageToolCallResponseCreate(
                                message_id=str(message.id),
                                tool_call_id=tool_call.id,
                                response=tool_responses.get(tool_call.id, ''),
                            )
                            for tool_call in resp.tool_calls
                        ]
                    )
                else:

                    _ = await update_message(
                        db,
                        biz_type=biz_type,
                        message_id=message.id,
                        message_update=MessageUpdate(
                            response=resp.content,
                            response_type=MessageResponseType.TEXT,
                            total_tokens=resp.total_tokens if resp.total_tokens is not None else 0,
                            prompt_tokens=resp.prompt_tokens if resp.prompt_tokens is not None else 0,
                            response_tokens=resp.response_tokens if resp.response_tokens is not None else 0,
                            time_comsumption_in_ms=time_comsumption,
                        ))

                response_dict = resp.model_dump()
                response_dict['messageID'] = str(message.id)
                response_dict['conversationID'] = str(conversation.id)
                response_dict[IS_CHUNK_RESPONSE] = False
                if tool_responses:
                    response_dict[TOOL_RESPONSES] = tool_responses

                if TOOL_RESPONSES in response_dict:
                    messages.append(
                        AILLMMessage(
                            tool_calls=response_dict[TOOL_CALLS],
                        )
                    )
                    for (id, tool_response) in response_dict[TOOL_RESPONSES].items():
                        messages.append(
                            ToolLLMMessage(
                                tool_call_id=id,
                                content=tool_response,
                            )
                        )
                    is_tool_call_response = True
                else:
                    is_tool_call_response = False
                yield response_dict
                if not is_tool_call_response:
                    return
    finally:
        # 确保resp被正确关闭
        if resp and hasattr(resp, 'aclose'):
            await resp.aclose()


def extract_message_list(response_data):
    json_data = response_data.json()
    if json_data.get('code') != 200:
        logger.error(
            "extract_message_list response: {data}", data=json_data, uid='uid')
        raise BadRequest(
            f"extract_message_list response: {json.dumps(json_data)}")
    data = json_data.get('data', {})
    if not data:
        logger_error("No 'data' field in response")
        return []

    message_list = data.get('messageList', [])
    if not isinstance(message_list, list):
        logger_error("messageList is not a list")
        return []

    # 获取额外信息
    conversation_id = data.get('conversationId')
    messageId = data.get('messageId')
    total_token = data.get('totalTokens')

    # 将额外信息添加到每个消息项中
    enriched_message_list = []
    for message in message_list:
        if isinstance(message, dict):
            message.update({
                'conversationID': conversation_id,
                'messageID': messageId,
                'total_tokens': total_token
            })
            enriched_message_list.append(message)

    return enriched_message_list


def extract_agentw_message_list(response_data):
    json_data = response_data.json()
    # 解析原始响应
    code = json_data.get("code")
    message = json_data.get("message")
    data = json_data.get("data", {})
    chat_id = data.get("chatId")
    result = data.get("result", {})
    output = result.get("output", "")

    # 构建新的响应格式
    transformed_response = {
        "node_id": "o1a0e9544",  # 假设的固定值
        "inputs": {},  # 假设没有输入
        "outputs": {
            "output": output if code == 200 else message
        },
        "response": output if code == 200 else message,
        "running_status": "success" if code == 200 else "failed",
        "node_name": "结束",  # 假设的固定值
        "chat_id": chat_id
    }

    return transformed_response


# 测试单个模型在特定场景下的性能
async def test_model_speed(
    model_name: str,
    provider_kind: str,
    scenario: Dict[str, str],
    db: Optional[Session] = None,
) -> Dict[str, Any]:
    """测试单个模型在特定场景下的性能"""
    messages = [LLMMessage(**scenario)]

    start_time = time.time()
    try:
        response = await simple_completion(
            app_id=settings.test_model_app_id,
            model_name=model_name,
            provider_kind=provider_kind,
            user_id='test',
            messages=messages,
            db=db)

        end_time = time.time()

        completion_tokens = response.get('response_tokens', 0)
        total_tokens = response.get('total_tokens', 0)
        return {
            'success': True,
            'completion_tokens': completion_tokens,
            'total_tokens': total_tokens,
            'time_taken': end_time - start_time,
            'tokens_per_second': completion_tokens / (end_time - start_time)
        }
    except Exception as e:
        # 如果错误信息包含 "please try again later"，则等待10秒后重试
        if 'please try again later' in str(e):
            await asyncio.sleep(10)
            return await test_model_speed(model_name, provider_kind, scenario)
        return {
            'success': False,
            'error': str(e)
        }
    finally:
        # 清理任何可能的资源
        if 'response' in locals() and hasattr(response, 'aclose'):
            await response.aclose()


# 测试所有模型的性能并将结果存储到Redis
async def test_models_performance(
    retry_count: Optional[int] = 2,
    provider_kind: Optional[ModelProviderKind] = None,
):
    """测试所有模型的性能并将结果存储到Redis"""
    # 获取所有可用模型
    models = await fetch_all_models()

    results = {}
    # 先看缓存中是否存在
    cache_key = f"{MODEL_TEST_KEY}::{datetime.now().strftime('%m-%d')}"
    cache_data = get_redis_sync(cache_key)
    if cache_data:
        results = json.loads(cache_data)

    for model in models:
        if not model.enable:
            continue

        if provider_kind is not None and model.providerKind != provider_kind:
            continue

        # 如果缓存中结果存在，则跳过
        if results.get(model.name) is not None:
            continue

        model_results = {}
        model_results_count = {}
        total_tokens_per_second = 0
        total_valid_tests = 0

        # 对每个场景进行测试
        for scenario_name, scenario in test_scenarios.items():
            # 对每个场景进行2次测试
            for _ in range(retry_count or 2):
                try:
                    result = await test_model_speed(
                        model.name,
                        model.providerKind,
                        scenario
                    )
                    if result['success']:
                        tokens_per_second = result['tokens_per_second']

                        model_results_count[scenario_name] = model_results_count.get(
                            scenario_name, 0) + 1
                        model_results[scenario_name] = tokens_per_second + \
                            model_results.get(scenario_name, 0)

                        total_valid_tests += 1
                        total_tokens_per_second += tokens_per_second
                    else:
                        logger.error(
                            "测试模型 {model} 在场景 {scenario_name} 时出错: {error}",
                            model=model.name,
                            scenario_name=scenario_name,
                            error=result['error'],
                            uid='uid'
                        )
                except Exception as e:
                    logger.error(
                        "测试模型 {model} 在场景 {scenario_name} 时出错: {error}",
                        model=model.name,
                        scenario_name=scenario_name,
                        error=str(e),
                        uid='uid'
                    )
                    continue
            if model_results_count.get(scenario_name, 0) > 0:
                model_results[scenario_name] = model_results.get(
                    scenario_name, 0) / model_results_count.get(scenario_name, 1)
        # 如果有有效的测试结果，计算平均值并保存
        if total_valid_tests > 0:
            model_results['avg'] = total_tokens_per_second / total_valid_tests
            results[model.name] = model_results
            # 将结果保存到Redis
            set_redis_sync(cache_key, json.dumps(
                results, ensure_ascii=False))

    return results
