from venv import create
import json
from sqlalchemy import Column
from sqlalchemy.orm import Session
from typing import Optional, Tu<PERSON>, List

from sqlalchemy.orm.query import Query
from sqlalchemy import or_
from sqlalchemy.sql import and_
from ..schema.app import AppType
from ..misc.errors import LangbaseException
from ..models.prompt_template import PromptTemplate
from ..schema.prompt_template import PromptType, ScopeType, StructPromptItem, PromptTemplateUpdateRequest


async def list_prompt_templates(
    db: Session,
    workspace_id: str,
    group_id: str,
    app_type: Optional[AppType],
) -> <PERSON><PERSON>[List[PromptTemplate], int]:
    # 执行查询以获取匹配条件的模版
    query: Query[PromptTemplate] = db.query(PromptTemplate).filter(
        PromptTemplate.workspace_id == workspace_id,
        or_(
            PromptTemplate.scope != ScopeType.SCOPED.value,
            and_(
                PromptTemplate.scope == ScopeType.SCOPED.value,
                PromptTemplate.group_id == group_id
            )
        )
    )

    # 根据应用类型进行过滤
    if app_type is not None:
        query = query.filter(PromptTemplate.app_type == app_type.value)

    total: int = query.count()  # 获取总数
    templates: List[PromptTemplate] = query.all()

    prompt_templates = []
    for template in templates:
        # 遍历并处理每项数据
        prompt_template = {
            "id": template.id,
            "name": template.name,
            "desc": template.description,
            "avatarUrl": template.avatar_url,
            "prompt": template.text_prompt,
            "structPrompt": template.struct_prompt,  # 假设这是 JSON 类型，SQLAlchemy 自动解析
            "promptType": template.prompt_type,
            "appType": template.app_type,
        }
        prompt_templates.append(prompt_template)

    return prompt_templates, total


async def create_prompt_template(
    db: Session,
    workspace_id: str,
    group_id: str,
    user_id: str,
    name: str,
    avatar_url: str,
    desc: Optional[str],
    prompt_type: PromptType,
    app_type: Optional[AppType],
    prompt: str,
    scope: ScopeType,

    struct_prompt: Optional[List[StructPromptItem]]
) -> PromptTemplate:

    # 检查是否存在重复的模板名称
    existing_template: PromptTemplate | None = db.query(PromptTemplate).filter(
        PromptTemplate.name == name).first()

    if existing_template:
        # 抛出HTTP冲突异常，表示名称重复
        raise LangbaseException(status_code=409, message="模版名称已存在，请修改名称重新提交")

    new_template = PromptTemplate(
        workspace_id=workspace_id,
        group_id=group_id,
        name=name,
        scope=scope.value,
        description=desc,
        avatar_url=avatar_url,
        prompt_type=prompt_type.value,
        app_type=app_type.value if app_type is not None else None,
        text_prompt=prompt,
        struct_prompt=struct_prompt,
        created_by=user_id,
        updated_by=user_id
    )
    db.add(new_template)
    db.commit()
    db.refresh(new_template)

    return new_template


async def update_prompt_template(db: Session, template_data: PromptTemplateUpdateRequest, userId: str):
    template: PromptTemplate | None = db.query(PromptTemplate).filter(
        PromptTemplate.id == template_data.id).first()
    if not template:
        raise LangbaseException(status_code=404, message="模版未找到")

    if template.created_by != userId:
        raise LangbaseException(status_code=403, message="无权限修改该模版")

    # 更新提供的字段
    updateable_fields = {
        "workspace_id": template_data.workspaceId,
        "group_id": template_data.groupId,
        "name": template_data.name,
        "description": template_data.desc,
        "avatar_url": template_data.avatarUrl,
        "prompt_type": template_data.promptType,
        "text_prompt": template_data.prompt,
        "struct_prompt": template_data.structPrompt,
    }

    # 只更新指定字段
    for field, value in updateable_fields.items():
        if value is not None:
            setattr(template, field, value)

    db.commit()
    db.refresh(template)

    return template


async def get_prompt_template_detail(db: Session, template_id: str):
    # 查询数据库获取模板详情，通过模板 ID 进行查找
    template: PromptTemplate | None = db.query(PromptTemplate).filter(
        PromptTemplate.id == template_id).first()
    if template:
        return {
            "name": template.name,
            "id": template.id,
            "desc": template.description,  # 确保数据库模型包含此字段
            "avatarUrl": template.avatar_url,
            "promptType": template.prompt_type,
            "appType": template.app_type,
            "prompt": template.text_prompt,
            "structPrompt": template.struct_prompt  # 假设数据库中正确格式化该字段
        }
    return None


async def delete_prompt_template(db: Session, template_id: str, userId: str):
    # 删除指定 id 的模板
    template: PromptTemplate | None = db.query(PromptTemplate).filter(
        PromptTemplate.id == template_id).first()
    if template:
        if template.created_by != userId:
            raise LangbaseException(status_code=403, message="无权限删除该模版")
        # 删除模板数据库记录
        else:
            db.delete(template)
            db.commit()
            return {"id": template_id}
    else:
        raise LangbaseException(status_code=404, message="模版ID不存在")
