from typing import List, Optional, <PERSON><PERSON>
from uuid import uuid4
from sqlalchemy.orm import Session
import json

from ..models.member import Member
from .member import list as list_member_service
from .account import get_by_email

from ..schema.app import AppCreate, AppType, AgentConfig

from ..schema.model import DefaultModelConfigUpdate

from ..config import settings

from ..misc.errors import NotFound
from datetime import datetime
from .member import create as create_member_service
from ..schema.member import MemberCreate, MemberType, \
    Role
from ..schema.common import ResourceType

from ..models.workspace import Workspace
from ..schema.workspace import WorkspaceCreate, WorkspaceUpdate, WorkspaceUpdateCreator
from ..lib.llms.base import Providers
from .group import \
    create_with_member as create_group_with_member
from .group import GroupCreate
from . import model
from .app import list_app as list_app_service, \
    create as create_app_service
from ..misc.redis_utils import set_redis_sync, WORKSPACES_CACHE_KEY


async def get(db: Session, workspace_id: str):
    db_workspace = db.query(Workspace) \
        .filter(Workspace.id == workspace_id).first()
    if db_workspace is None:
        raise NotFound(f"Workspace({workspace_id}) not found")
    return db_workspace


async def delete(db: Session,
                 user_id: str,
                 workspace_id: str):
    _ = await get(db, str(workspace_id))
    db.query(Workspace).filter(Workspace.id == workspace_id).update(
        {Workspace.deleted_at: datetime.now(),
         Workspace.deleted_by: user_id}
    )
    db.commit()
    # 删除后同步缓存
    await sync_workspaces_to_redis(db)


async def access_workspace(db: Session, workspace_id: str, user_id: str):
    # 判断用户是否有该workspace的权限
    members, _ = await list_member_service(
        db,
        member_id=user_id,
        resource_id=workspace_id,
        resource_type=ResourceType.WORKSPACE.value)
    if len(members) > 0 and str(members[0].role) != Role.EXTERNAL_USER.value:
        return False
    else:
        _ = await create_member_service(
            db,
            resource_type=ResourceType.WORKSPACE,
            user_id=user_id,
            resource_id=workspace_id,
            member_create=MemberCreate(
                memberType=MemberType.USER.value,
                memberID=user_id,
                role=Role.DEVELOPER.value,
            ))
        return True


async def delete_with_check(db: Session,
                            user_id: str,
                            workspace_id: str):
    _, count = await list_app_service(
        db,
        workspace_id=workspace_id,
        limit=0
    )

    if count > 0:
        raise Exception("workspace still has app, can not delete")
    await delete(db, user_id, workspace_id)


async def update(db: Session,
                 user_id: str,
                 workspace_id: str,
                 workspace_update: WorkspaceUpdate):
    db_workspace = await get(db, workspace_id)
    columns = workspace_update.model_dump(exclude_unset=True)
    columns[Workspace.updated_by] = user_id
    db.query(Workspace).filter(Workspace.id == workspace_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_workspace)
    # 更新后同步缓存
    await sync_workspaces_to_redis(db)
    return db_workspace


async def update_creator(db: Session,
                         user_id: str,
                         workspace_id: str,
                         mail: str):
    db_workspace = await get(db, workspace_id)
    if db_workspace is None:
        raise NotFound(f"Workspace({workspace_id}) not found")
    user = await get_by_email(db, mail)
    if user is None:
        raise NotFound(f"User({mail}) not found")
    # update workspace
    db_workspace.created_by = user.id
    db_workspace.updated_by = user_id
    db.commit()
    db.refresh(db_workspace)
    return db_workspace


async def list_with_creator(db: Session,
                            user_id: str,
                            offset: Optional[int] = None,
                            limit: Optional[int] = None,
                            creation_time_asc: Optional[bool] = None) \
        -> Tuple[List[Workspace], int]:
    stat = db.query(Workspace).filter(Workspace.created_by ==
                                      user_id).filter(Workspace.description != None)
    count = stat.count()
    if creation_time_asc is not None:
        stat = stat.order_by(Workspace.created_at.asc())
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)
    return (stat.all(), count)


async def list(db: Session,
               ids: Optional[List[str]] = None,
               offset: Optional[int] = None,
               limit: Optional[int] = None,
               user_id: Optional[str] = None,
               description: Optional[str] = None,
               creation_time_asc: Optional[bool] = None) \
        -> Tuple[List[Workspace], int]:
    stat = db.query(Workspace)
    if user_id is not None:
        stat = stat.join(Member, Workspace.id == Member.resource_id).filter(
            Member.member_id == user_id, Member.resource_type == 'workspace')
    if ids is not None:
        stat = stat.filter(Workspace.id.in_(ids))
    if description is not None:
        stat = stat.filter(Workspace.description == description)
    count = stat.count()
    if creation_time_asc is not None:
        stat = stat.order_by(Workspace.created_at.asc()
                             if creation_time_asc
                             else Workspace.created_at.desc())
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)
    return (stat.all(), count)


async def create(db: Session,
                 workspace_create: WorkspaceCreate,
                 user_id: str,
                 workspace_id: Optional[str] = None):
    workspace = Workspace(**workspace_create.model_dump())
    if workspace_id is not None:
        workspace.id = workspace_id
    workspace.created_by = user_id
    workspace.updated_by = user_id
    db.add(workspace)
    db.commit()
    db.refresh(workspace)
    return workspace


async def create_with_member(db: Session,
                             workspace_create: WorkspaceCreate,
                             user_id: str,
                             workspace_id: Optional[str] = None):
    if workspace_id is None:
        workspace_id = str(uuid4())
    member_create = MemberCreate(
        memberType=MemberType.USER.value,
        memberID=user_id,
        role=Role.ADMIN.value)
    await create_member_service(
        db,
        user_id=user_id,
        resource_type=ResourceType.WORKSPACE,
        resource_id=str(workspace_id),
        member_create=member_create)

    workspace = await create(db, workspace_create, user_id, workspace_id)
    # 创建后同步缓存
    await sync_workspaces_to_redis(db)
    return workspace


async def create_with_initial(db: Session,
                              workspace_create: WorkspaceCreate,
                              user_id: str,
                              user_name: str):
    workspace_id = str(uuid4())
    db_group = await create_group_with_member(
        db,
        user_id,
        workspace_id,
        GroupCreate(
            name=settings.default_group_name,
            description=None,))

    for provider in Providers:
        await model.create_default_provider_binding(
            db=db,
            user_id=user_id,
            provider_kind=provider.value.kind,
            workspace_id=workspace_id
        )

    await create_app_service(
        db=db,
        creater_id=user_id,
        creater_name=user_name,
        group_id=str(db_group.id),
        app_create=AppCreate(
            name=settings.default_basic_app_name,
            type=AppType.CONVERSATION,
            is_basic=True,
            config=AgentConfig(
                modelName=settings.default_basic_model,
                modelParams=settings.default_model_params,
                providerKind=settings.default_basic_provider,
            ).model_dump(),
        )
    )

    for model_config in settings.default_model:
        await model.update_default_model_config(
            db=db,
            user_id=user_id,
            resource_type=ResourceType.WORKSPACE,
            resource_id=workspace_id,
            config_update=DefaultModelConfigUpdate(
                modelName=model_config.model_name,
                modelType=model_config.model_type,
                providerKind=model_config.provider_kind),
        )

    db_workspace = await create_with_member(
        db,
        workspace_create=workspace_create,
        user_id=user_id,
        workspace_id=workspace_id)
    return db_workspace


async def get_workspace_admins(db: Session, workspace_id: str) -> Tuple[List[Member], int]:
    members, total = await list_member_service(
        db,
        resource_id=str(workspace_id),
        role='admin',
        resource_type='workspace',
        with_detail=True,
        member_type=MemberType.USER.value
    )
    return members, total


async def get_all_workspaces(db: Session) -> List[Workspace]:
    stat = db.query(Workspace)
    stat = stat.join(Member, Workspace.id == Member.resource_id).filter(
        Member.member_id == settings.workspace_member_id, Member.resource_type == 'workspace'
    )
    return stat.all()


async def sync_workspaces_to_redis(db: Session):
    """
    同步所有workspace到redis缓存
    将以id:name的map形式存储在langbase::workspaces中
    """
    try:
        # 获取所有未删除的workspace
        workspaces = await get_all_workspaces(db)
        # workspaces, _ = await list(db, limit=300, user_id=settings.workspace_member_id)

        # 构建id:name的映射
        workspace_map = {
            str(workspace.id): workspace.name for workspace in workspaces}

        # 写入redis
        set_redis_sync(WORKSPACES_CACHE_KEY, json.dumps(workspace_map))
        return workspace_map
    except Exception as e:
        print(f"Sync workspaces to redis error: {e}")
        return False
