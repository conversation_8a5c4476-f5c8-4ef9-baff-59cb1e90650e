import re
from typing import Optional
import httpx
from markitdown import MarkItDown
from ..misc.errors import InternalServerError
from ..misc.redis_utils import POPO_DOC_CACHE_KEY, get_redis_sync, set_redis_sync


async def parse_popo_document(popo_url: Optional[str] = None, popo_title: Optional[str] = None):
    if not popo_url and not popo_title:
        raise InternalServerError("popo_url or popo_title is required")
    title_cache_key = f"{POPO_DOC_CACHE_KEY}::{popo_title}"
    # 说明想通过标题读取文档内容
    if popo_title:
        if get_redis_sync(title_cache_key):
            popo_url = get_redis_sync(title_cache_key)
        else:
            raise InternalServerError("popo_title not found")
    cache_key = f"{POPO_DOC_CACHE_KEY}::{popo_url}"
    if get_redis_sync(cache_key):
        return {
            'code': 200,
            'data': {
                'text_content': get_redis_sync(cache_key),
            },
            'isLinxiDoc': False,
        }
    page_id_match = re.search(r'pageDetail/([^/?]+)', popo_url)
    team_space_key = re.search(r'team/pc/([^/?]+)', popo_url)
    is_linxi_doc = False
    # 如果是灵犀文档，包含/lingxi/xxx，则获取灵犀文档的id
    if not page_id_match and '/lingxi/' in popo_url:
        is_linxi_doc = True
        page_id_match = re.search(r'/lingxi/([^/?]+)', popo_url)
        if not page_id_match:
            raise InternalServerError("Invalid popo URL format")
    else:
        team_space_key = team_space_key.group(1)

    page_id = page_id_match.group(1)

    # 调用popo API获取文档URL
    async with httpx.AsyncClient() as client:
        popo_api_url = "http://ada-popo-rollout-online-gz.yf-online-gy1.service.gy.ntes/api/ada/popo/get-popo-doc"
        params = {
            "popoId": "t8D0Cl1YrOtbHed8oz",
            "isLinxiDoc": 'true' if is_linxi_doc else 'false',
            "teamSpaceKey": team_space_key or "technology_center",
            "pageId": page_id,
            "withDetail": 'true',
            "exportType": "1"
        }

        response = await client.get(popo_api_url, params=params, timeout=100)
        result = response.json()
        if response.status_code != 200:
            return result
        if result.get('code') != 200:
            return result

        if 'data' not in result:
            raise InternalServerError("No document URL in response")
        # 把这个链接下载下来，然后获取其标题（或者不下载下来也行，就是要获取标题）

        md = MarkItDown()
        title = ''
        if 'url' in result['data']:
            html_content = md.convert(result['data']['url'])
            pageInfo = result['data'].get('pageInfo', {})
            title = pageInfo.get('name') or pageInfo.get('pageName') or ''
        else:
            html_content = md.convert(result['data'])
        content = f'# {title}\n{html_content.text_content}'
        title_cache_key = f"{POPO_DOC_CACHE_KEY}::{title}"
        set_redis_sync(cache_key, content, 5 * 60)
        set_redis_sync(title_cache_key, popo_url)
        return {
            'code': 200,
            'data': {
                'text_content': content,
            },
            'isLinxiDoc': is_linxi_doc,
        }
