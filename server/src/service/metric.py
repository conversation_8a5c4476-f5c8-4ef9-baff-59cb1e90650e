import json
from typing import Dict, Optional, List
from click import group
from loguru import logger
from sqlalchemy.orm import Session
from sqlalchemy import asc, desc, func, case
from datetime import datetime, timedelta
import httpx
import pandas as pd
from io import BytesIO
import math

from ..util import format_number, get_string_width

from ..models.group import Group

from ..misc.redis_utils import ALERT_POPO_KEY, APP_CACHE_KEY, get_redis_sync, set_redis_sync, FEE_ALERT_KEY

from ..misc.session import get_redis

from ..misc.log import logger_info, logger_error

from ..models.app_metric import AppMetric

from ..misc.db import get_db

from ..models.dialog import Conversation, Message

from ..config import settings

from ..service.s3 import upload_file

DEFAULT_DATE_OFFSET = timedelta(days=7)

# 性能问题 废弃


async def get_conversation_count_per_user(
        db: Session,
        app_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None):

    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - DEFAULT_DATE_OFFSET

    # 计算每天每个人的平均会话量
    date = func.date_format(Conversation.created_at, "%Y-%m-%d").label('date')
    return db.query(
        date,
        (func.count(Conversation.id)
         / func.count(Conversation.created_by.distinct())).label('count')) \
        .filter(
            # Conversation.override_config.is_(None),
            Conversation.app_id == app_id,
            Conversation.created_at >= start_date,
            Conversation.created_at <= end_date
    ) \
        .group_by('date').all()


# 性能问题 废弃
async def get_message_count_per_conv(
        db: Session,
        app_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None):

    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - DEFAULT_DATE_OFFSET

    # 计算每天每个会话的平均消息数量
    date = func.date_format(Conversation.created_at, "%Y-%m-%d").label('date')
    return db.query(
        date,
        (func.count(Message.id)
         / func.count(Conversation.id)).label('count')) \
        .join(Conversation, Conversation.id == Message.conversation_id) \
        .filter(
            # Conversation.override_config.is_(None),
            Conversation.app_id == app_id,
            Conversation.created_at >= start_date,
            Conversation.created_at <= end_date
    ) \
        .group_by('date').all()


# 性能问题 废弃
# TODO: support outside user
async def get_user_usage_count(
        db: Session,
        app_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None):

    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - DEFAULT_DATE_OFFSET

    date = func.date_format(Conversation.created_at, "%Y-%m-%d").label('date')
    return db.query(
        date,
        func.count(Conversation.created_by.distinct()).label('count')) \
        .filter(
            # Conversation.override_config.is_(None),
            Conversation.app_id == app_id,
            Conversation.created_at >= start_date,
            Conversation.created_at <= end_date
    ) \
        .group_by('date').all()


# 性能问题 废弃
async def get_tokens_per_seconds(
        db: Session,
        app_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None):

    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - DEFAULT_DATE_OFFSET
    return (
        db.query(
            func.date_format(Message.created_at, "%Y-%m-%d").label('date'),
            case(
                *[
                    (Message.time_comsumption_in_ms == 0, 0)
                ],
                else_=func.sum(Message.response_tokens)
                / (func.sum(Message.time_comsumption_in_ms) / 1000),
            ).label('count')
        ).filter(
            Message.app_id == app_id,
            func.date(Message.created_at) >= start_date,
            func.date(Message.created_at) <= end_date,
        ).group_by('date').all()
    )


# 性能问题 废弃
async def get_token_count(
        db: Session,
        app_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None):

    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - DEFAULT_DATE_OFFSET
    return (
        db.query(
            func.date_format(Message.created_at, "%Y-%m-%d").label('date'),
            func.sum(Message.total_tokens).label('count')
        ).filter(
            Message.app_id == app_id,
            func.date(Message.created_at) >= start_date,
            func.date(Message.created_at) <= end_date,
        ).group_by('date').all()
    )


# 计算每天平均会话数
async def get_conversation_count_by_date(
        db: Session,
        app_id: str,
        date: datetime):
    start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)
    # 查询当天的messages，对应了多少个conversation
    query = (db.query(
        func.count(Message.conversation_id.distinct())
    ).filter(
        Message.app_id == app_id,
        Message.created_at >= start_date,
        Message.created_at <= end_date
    ).scalar())
    return query


# 获取每日用户数
async def get_user_usage_count_by_date(
        db: Session,
        app_id: str,
        date: datetime):
    start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)

    return db.query(
        func.count(Message.created_by.distinct()).label('count')) \
        .filter(
            # Conversation.override_config.is_(None),
            Message.app_id == app_id,
            Message.created_at >= start_date,
            Message.created_at <= end_date
    ).scalar()


# 获取每日token数
async def get_token_count_by_date(
        db: Session,
        app_id: str,
        date: datetime):
    start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)
    return (
        db.query(
            func.count(Message.id).label('count'),
            func.sum(Message.total_tokens).label('tokens'),
            func.sum(Message.response_tokens).label('res_tokens'),
            func.sum(Message.time_comsumption_in_ms).label('times'),
            func.sum(Message.time_comsumption_in_ms /
                     Message.response_tokens).label('avg'),
            func.sum(Message.fee).label('fee')
        ).filter(
            Message.app_id == app_id,
            Message.created_at >= start_date,
            Message.created_at <= end_date,
        ).one()
    )


# 获取每日消息数
async def get_message_count_by_date(
        db: Session,
        app_id: str,
        date: datetime):
    start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)
    start_date = end_date - DEFAULT_DATE_OFFSET

    # 计算每天每个会话的平均消息数量
    return db.query(
        func.count(Message.id)
    ).filter(
        # Conversation.override_config.is_(None),
        Message.app_id == app_id,
        Message.created_at >= start_date,
        Message.created_at <= end_date
    ).scalar()


# 获取每秒token数
async def get_tokens_per_seconds_by_date(
        db: Session,
        app_id: str,
        date: datetime):
    start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)
    start_date = end_date - DEFAULT_DATE_OFFSET
    return (
        db.query(
            case(
                *[
                    (Message.time_comsumption_in_ms == 0, 0)
                ],
                else_=func.sum(Message.response_tokens)
                / (func.sum(Message.time_comsumption_in_ms) / 1000),
            ).label('count')
        ).filter(
            Message.app_id == app_id,
            Message.created_at >= start_date,
            Message.created_at <= end_date,
        ).scalar()
    )


# 获取每日活跃的应用
async def get_active_app_by_date(
        db: Session,
        date: datetime):
    start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)
    query = (
        db.query(
            Message.app_id,
        ).filter(
            Message.created_at >= start_date,
            Message.created_at <= end_date,
        ).group_by('app_id')
    )
    # print('query', str(query))
    return query.all()


async def get_metric_by_date(db: Session,
                             date: datetime):
    start_date = date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)
    return db.query(AppMetric).filter(
        AppMetric.date >= start_date,
        AppMetric.date < end_date,
    ).first()


async def get_app_metric(db: Session,
                         sort_filed: str,
                         sort_order: str,
                         group_id: Optional[str] = None,
                         workspace_id: Optional[str] = None,
                         start_date: Optional[datetime] = None,
                         end_date: Optional[datetime] = None):
    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - DEFAULT_DATE_OFFSET
    start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=0)
    query = db.query(
        AppMetric.app_id.label('app_id'),
        AppMetric.group_id.label('group_id'),
        AppMetric.workspace_id.label('workspace_id'),
        AppMetric.name.label('name'),
        AppMetric.total_tokens.label('tokens'),
        AppMetric.messages.label('messages'),
        AppMetric.fee.label('fee'),
        AppMetric.model_name.label('model_name'),
        AppMetric.date.label('date'),
    ).filter(
        AppMetric.date >= start_date,
        AppMetric.date <= end_date)
    # .join(VersionedAppConfig, App.app_config_id == VersionedAppConfig.id)\
    if group_id != 'all' and group_id is not None:
        query = query.filter(AppMetric.group_id == group_id)
    if workspace_id != 'all' and workspace_id is not None:
        query = query.filter(AppMetric.workspace_id == workspace_id)
    order_rule = asc
    if sort_order == 'descend':
        order_rule = desc
    # stat = query.group_by('name').order_by(order_rule(sort_filed))
    stat = query.order_by(order_rule(sort_filed))
    stats = stat.all()
    # 根据name聚合
    name_map: Dict[str, Dict] = {}
    for row in stats:
        if row.name not in name_map:
            name_map[row.name] = {
                'app_id': row.app_id,
                'group_id': row.group_id,
                'workspace_id': row.workspace_id,
                'name': row.name,
                'tokens': row.tokens,
                'messages': row.messages,
                'fee': float(row.fee),
                'details': {}
            }
            # 初始化该model_name的聚合数据
            name_map[row.name]['details'][row.model_name] = {
                'model_name': row.model_name,
                'tokens': row.tokens,
                'messages': row.messages,
                'fee': float(row.fee)
            }
        else:
            item = name_map[row.name]
            item['tokens'] += row.tokens
            item['messages'] += row.messages
            item['fee'] += float(row.fee)

            # 按model_name聚合details
            if row.model_name not in item['details']:
                item['details'][row.model_name] = {
                    'model_name': row.model_name,
                    'tokens': row.tokens,
                    'messages': row.messages,
                    'fee': float(row.fee)
                }
            else:
                model_item = item['details'][row.model_name]
                model_item['tokens'] += row.tokens
                model_item['messages'] += row.messages
                model_item['fee'] += float(row.fee)

    # 将details从字典转为列表
    for item in name_map.values():
        item['details'] = list(item['details'].values())

    # 将查询结果转换为字典列表
    return list(name_map.values())


async def get_metrics(db: Session,
                      app_id: str,
                      start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None):
    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - DEFAULT_DATE_OFFSET
    start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=0)
    metrics = db.query(AppMetric).filter(
        AppMetric.app_id == app_id,
        AppMetric.date >= start_date,
        AppMetric.date <= end_date
    ).group_by('date').all()
    # 需要根据选中的时间范围，生成数组
    formatted_metrics = [
        {**metric.__dict__, 'date': metric.date.strftime('%Y-%m-%d')}
        for metric in metrics
    ]

    return formatted_metrics


async def refresh_metrics():
    db = next(get_db())
    app_cache = get_redis_sync(APP_CACHE_KEY)
    if app_cache is None:
        app_cache = '{}'
    app_cache = json.loads(app_cache)
    # metrics = db.query(AppMetric).all()
    condition1 = (AppMetric.fee == None) & (AppMetric.extra != None)
    condition2 = (AppMetric.workspace_id == None)
    metrics = db.query(AppMetric).filter(
        condition1 | condition2).limit(1000).all()
    not_updated = []
    # 收集需要更新的记录
    for metric in metrics:
        # 解析extra字段中的JSON数据
        app_info = app_cache.get(metric.app_id)
        extra_data = metric.extra
        # 检查extra中是否包含fee键
        if extra_data is not None and 'fee' in extra_data:
            # 将extra中的fee值赋给fee字段
            metric.fee = float(extra_data['fee'] or 0)
            # metrics_to_update.append(metric)
        # 提交修改到数据库
        if app_info is not None:
            metric.workspace_id = app_info['workspace_id']
            metric.group_id = app_info['group_id']
            metric.name = app_info['name']
        else:
            if str(metric.app_id)[:2] != 'w-':
                not_updated.append(metric.app_id)
                print(f"app_info is None, app_id: {metric.app_id}")
    db.commit()
    return metrics, len(metrics), not_updated


async def list_group_by_ids(db: Session, ids: List[str]):
    stat = db.query(Group)
    stat = stat.filter(Group.id.in_(ids))
    return stat.all()


async def list_groups_by_workspace_id(db: Session, workspace_id: Optional[str] = None):
    stat = db.query(Group)
    if workspace_id is not None:
        stat = stat.filter(Group.workspace_id == workspace_id)
    return stat.all()


async def alert_popo(content: List[dict], to_popo: Optional[str] = None):
    """
    对高费用应用发送POPO预警

    Args:
        apps: 包含高费用应用信息的列表，每个元素应该包含 app_id, name, fee 等信息
    """

    POPO_API = "http://ada-popo.ft.netease.com/api/ada/popo/sendMsg"
    POPO_ID = "Ie33QQ3xUBgED1pecdet"
    # TO_EMAIL = "<EMAIL>"
    TO_POPO = settings.alert_popo_id

    payload = {
        "msgType": "rich_text",
        "message": {
            "content": content
        },
        "isGroup": True,
        "to": to_popo or TO_POPO
    }

    headers = {
        "Content-Type": "application/json",
        "Accept": "*/*",
        "Host": "ada-popo.ft.netease.com",
        "Connection": "keep-alive"
    }

    success = False

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{POPO_API}?popoId={POPO_ID}",
                json=payload,
                headers=headers
            )

            if response.is_success:
                success = True
            else:
                print(f"Failed to send alert, response: {response.text}")

        except Exception as e:
            print(f"Error sending high fee alert: {e}")
    return success


async def alert_high_fee_apps(apps: List[dict], to_popo: Optional[str] = None):
    """
    对高费用应用发送POPO预警

    Args:
        apps: 包含高费用应用信息的列表，每个元素应该包含 app_id, name, fee 等信息
    """
    if not apps:
        return

    url_prefix = "https://langbase.netease.com/app/dev?appId="
    if settings.debug:
        url_prefix = "http://langbase-workflow.yf-onlinetest4.netease.com/app/dev?appId="

    # 构建消息内容
    content = []
    add_content(
        content, f"== {datetime.now().strftime('%Y-%m-%d')} {settings.debug and '测试' or '正式'}环境费用报警 ==\n\n\n", bold=True, underline=True)

    for app in apps:
        app_name = app.get('应用名称', app.get('app_id', 'Unknown'))
        fee = float(app.get('总费用(元)', 0))

        # 添加应用名称（带链接）
        if app.get('app_id', '') is not None:
            add_content(content, app_name, bold=True,
                        href=url_prefix + app.get('app_id', ''))
        else:
            add_content(content, app_name, bold=True)
        # 添加费用（带颜色）
        add_content(content, f"  {fee:.2f}元\n", bold=True,
                    color="red" if fee >= 50 else "green")

    await alert_popo(content, to_popo=to_popo)
    return True


def add_content(contents: list, text: str, bold: Optional[bool] = True, color: Optional[str] = None, underline: Optional[bool] = False,  href: Optional[str] = None):
    content = {}
    style = []
    if bold:
        style.append("bold")
    if underline:
        style.append("underline")
    if href:
        style.append("link")
        content['href'] = href
    if color is not None:
        style.append("font_color")
        content['fontColor'] = color
    content['tag'] = "text"
    content['style'] = style
    content['text'] = text
    contents.append(content)


async def alert_group_fee_by_date(start_date: datetime, end_date: datetime, workspace_id: Optional[str] = None, to_popo: Optional[str] = None):
    """
    对业务组费用发送POPO预警
    """
    if workspace_id is None:
        workspace_id = settings.test_model_workspace_id

    db = next(get_db())
    data = await get_excel_of_metric(db=db, start_date=start_date, end_date=end_date, workspace_id=workspace_id)
    nos_url = data['nos_url']
    group_rows = data['group_rows']
    model_rows = data['model_rows']
    groups = await list_groups_by_workspace_id(db=db, workspace_id=workspace_id)
    # 做成一个map
    group_map = {group.name: group for group in groups}

    url_prefix = "https://langbase.netease.com/workspace/overview?workspaceId=f97bf87b-a8ec-4bc9-a8e3-662e111cd2e4&groupId="
    total_fee = sum(g.get('总费用(元)', 0) for g in group_rows)
    total_messages = sum(m.get('总消息数', 0) for m in model_rows)
    total_tokens = sum(m.get('总Token数', 0) for m in model_rows)

    # 构建消息内容
    content = []
    add_content(
        content, f"== {start_date.strftime('%Y-%m-%d')} ~ {end_date.strftime('%Y-%m-%d')} 费用情况 ==\n\n", bold=True, underline=True)
    add_content(content, "总费用：", bold=False)
    add_content(content, f"\t\t{total_fee:.2f}元\n\n", color="red")
    add_content(content, "总消息数：", bold=False)
    add_content(
        content, f"\t{format_number(total_messages)}条\n\n", color="orange")
    add_content(content, "总Token数：", bold=False)
    add_content(
        content, f"\t{format_number(total_tokens)}个\n\n", color="green")

    add_content(
        content, "\n\n== Top10模型费用情况 ==\n\n", bold=True, underline=True)

    for m in model_rows:
        tabs = getTabs(m.get('模型名称'), 32)
        fee = float(m.get('总费用(元)', 0))
        add_content(content, m.get('模型名称'), bold=True)
        add_content(content, f"{tabs}{fee:.2f}元\n", bold=True,
                    color="red" if fee >= 50 else "green")

    # 只取前10个模型
    content = content[:28]
    await alert_popo(content, to_popo)
    content = []
    add_content(content, "== Top10 业务组费用情况 ==\n\n", bold=True, underline=True)

    for g in group_rows:
        group = group_map.get(g.get('业务组'))
        group_name = g.get('业务组')
        fee = float(g.get('总费用(元)', 0))
        tabs = getTabs(group_name, 24)

        # 添加应用名称（带链接）
        add_content(content, group_name, bold=True, href=url_prefix + group.id)

        # 添加费用（带颜色）
        add_content(content, f"{tabs}{fee:.2f}元\n", bold=True,
                    color="red" if fee >= 50 else "green")
    # 只取前10个应用
    content = content[:21]
    add_content(content, "\n更多详情请点击下方链接查看\n", bold=False)
    add_content(content, "下载费用报表\t", bold=False, href=nos_url)
    add_content(content, "查看AI大盘\t", bold=False, href=url_prefix)

    await alert_popo(content, to_popo)
    return True


async def remove_metric_data(db: Session, date: datetime):
    db.query(AppMetric).filter(
        AppMetric.date == date,
        AppMetric.app_id.isnot(None)
    ).delete()
    db.commit()


def getTabs(string: str, max_length: int) -> str:
    width = get_string_width(string)
    # 对特定字符进行宽度修正
    special_chars = string.count('-') + string.count('.') + string.count('_')
    adjusted_width = width + special_chars * 0.25  # 减小特殊字符的权重

    # 添加一个小的补偿值
    compensation = 0.5
    tabs = math.ceil((max_length - adjusted_width + compensation) / 4)

    return "\t" * tabs


"""
获取费用报表
"""


async def get_excel_of_metric(db: Session, start_date: datetime, end_date: datetime, workspace_id: Optional[str] = None, group_id: Optional[str] = None, no_excel: Optional[bool] = False):
    if workspace_id is None:
        workspace_id = settings.test_model_workspace_id
    groups = await list_groups_by_workspace_id(db=db, workspace_id=workspace_id)
    group_map = {group.id: group for group in groups}
    # 获取应用维度的数据
    app_metrics = await get_app_metric(
        db=db,
        sort_filed='fee',
        sort_order='descend',
        start_date=start_date,
        end_date=end_date,
        workspace_id=workspace_id,
        group_id=group_id
    )

    # 获取模型维度的数据
    model_metrics = await get_model_metric(
        db=db,
        sort_filed='fee',
        sort_order='descend',
        start_date=start_date,
        end_date=end_date,
        workspace_id=workspace_id,
        group_id=group_id
    )

    # 处理业务维度数据
    group_metrics = {}
    for metric in app_metrics:
        group_id = metric['group_id']
        group_name = group_map.get(
            group_id).name if group_map.get(group_id) else group_id

        if group_id not in group_metrics:
            group_metrics[group_id] = {
                '业务组': group_name,
                '总费用(元)': 0,
                '应用数量': 0,
                # 用于统计模型使用情况，格式: {model_name: [(fee, app_name), ...]}
                'models': {}
            }

        group_metrics[group_id]['总费用(元)'] += round(metric['fee'], 2)
        group_metrics[group_id]['应用数量'] += 1

        # 统计模型使用情况，同时记录应用名
        for detail in metric['details']:
            model_name = detail['model_name']
            if model_name not in group_metrics[group_id]['models']:
                group_metrics[group_id]['models'][model_name] = []
            group_metrics[group_id]['models'][model_name].append(
                (round(detail['fee'], 2), metric['name']))

    # 转换为列表并按费用排序
    group_rows = []
    for group_id, data in group_metrics.items():
        # 获取费用前3的模型（需要先合并同一模型的费用）
        model_total_fees = {}
        model_top_apps = {}  # 记录每个模型费用最高的应用
        for model_name, fee_app_list in data['models'].items():
            total_fee = sum(fee for fee, _ in fee_app_list)
            model_total_fees[model_name] = total_fee
            # 按费用排序找出该模型费用最高的应用
            top_app = sorted(fee_app_list, key=lambda x: x[0], reverse=True)[0]
            model_top_apps[model_name] = top_app[1]  # 存储应用名

        top_models = sorted(
            [(model, fee) for model, fee in model_total_fees.items()],
            key=lambda x: x[1],
            reverse=True
        )[:3]

        row = {
            '业务组': data['业务组'],
            'group_id': group_id,
            '总费用(元)': data['总费用(元)'],
            '应用数量': data['应用数量']
        }

        # 添加Top3模型信息，包含对应的应用名
        for i, (model_name, fee) in enumerate(top_models, 1):
            row[f'Top{i}应用'] = model_top_apps[model_name]
            row[f'Top{i}模型'] = model_name
            row[f'Top{i}费用(元)'] = round(fee, 2)

        group_rows.append(row)

    # 处理应用维度数据
    app_rows = []
    for metric in app_metrics:
        # 基础数据
        row = {
            '业务组': group_map.get(metric['group_id']).name if group_map.get(metric['group_id']) else metric['group_id'],
            '应用名称': metric['name'],
            'app_id': metric['app_id'],
            '总Token数': metric['tokens'],
            '总消息数': metric['messages'],
            '总费用(元)': round(metric['fee'], 2)
        }

        # 处理模型明细，只展示Top3模型
        top_models = sorted(metric['details'],
                            key=lambda x: x['fee'], reverse=True)[:3]
        for i, detail in enumerate(top_models, 1):
            row[f'Top{i}模型'] = detail['model_name']
            row[f'Top{i}费用(元)'] = round(detail['fee'], 2)

        app_rows.append(row)

    # 处理模型维度数据
    model_rows = []
    for metric in model_metrics:
        # 基础数据
        row = {
            '模型名称': metric['model_name'],
            '总Token数': metric['tokens'],
            '总消息数': metric['messages'],
            '总费用(元)': round(metric['fee'], 2),
            '应用数量': len(metric['details'])
        }

        # 添加前5个应用的使用情况
        top_apps = sorted(metric['details'],
                          key=lambda x: x['fee'], reverse=True)[:3]
        for i, app in enumerate(top_apps, 1):
            group_name = group_map.get(app['group_id']).name if group_map.get(
                app['group_id']) else app['group_id']
            row[f'Top{i}应用'] = f"{group_name} - {app['name']}"
            row[f'Top{i}费用(元)'] = round(app['fee'], 2)

        model_rows.append(row)

    # 创建DataFrame
    app_rows = sorted(app_rows, key=lambda x: x['总费用(元)'], reverse=True)
    group_rows = sorted(group_rows, key=lambda x: x['总费用(元)'], reverse=True)
    model_rows = sorted(model_rows, key=lambda x: x['总费用(元)'], reverse=True)
    if no_excel:
        return {
            'nos_url': '',
            'group_rows': group_rows,
            'app_rows': app_rows,
            'model_rows': model_rows
        }
    app_df = pd.DataFrame(app_rows)
    model_df = pd.DataFrame(model_rows)
    group_df = pd.DataFrame(group_rows)

    # 创建内存中的Excel文件
    excel_buffer = BytesIO()
    with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
        # 写入两个sheet
        group_df.to_excel(writer, sheet_name='业务组维度统计', index=False)
        app_df.to_excel(writer, sheet_name='应用维度统计', index=False)
        model_df.to_excel(writer, sheet_name='模型维度统计', index=False)
        # 设置不同sheet的列宽
        group_widths = [30, 5, 15, 5, 30, 30, 15,
                        30, 30, 15, 30, 30, 15]  # 业务组维度列宽
        app_widths = [30, 30, 5, 15, 15, 15, 30, 15, 30, 15, 30, 15]  # 应用维度列宽
        model_widths = [30, 15, 15, 15, 5, 30, 15, 30, 15, 30, 15]  # 模型维度列宽

        width_maps = {
            '业务组维度统计': group_widths,
            '应用维度统计': app_widths,
            '模型维度统计': model_widths
        }

        # 调整列宽
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            widths = width_maps[sheet_name]
            for idx, width in enumerate(widths, 1):
                column = worksheet.column_dimensions[chr(64 + idx)]  # 获取列
                column.width = width

    # 获取BytesIO的内容
    excel_buffer.seek(0)
    file_content = excel_buffer.getvalue()
    # 获取 BinaryIO
    file_content = BytesIO(file_content)

    # 生成文件名
    file_name = f'metric_report_{start_date.strftime("%Y%m%d")}-{end_date.strftime("%Y%m%d")}.xlsx'

    # 上传到NOS
    nos_url = await upload_file(
        s3_config=settings.workflow_config.s3,
        key=file_name,
        body=file_content
    )
    nos_url = f"https://{settings.workflow_config.s3.bucket}.{settings.workflow_config.s3.endpoint}/{file_name}"

    return {
        'nos_url': nos_url,
        'group_rows': group_rows,
        'app_rows': app_rows,
        'model_rows': model_rows
    }


async def join_mertic_data_V2(date_string=None, need_alert: Optional[bool] = True):
    redis_client = get_redis()
    # 抢锁
    lock = await redis_client.set('langbase-prod::metric_lock', '1', nx=True, ex=30)
    logger_info(f"[join metric data]lock: {lock}")
    if lock is None:
        return []

    db = next(get_db())
    last_day = datetime.now() - timedelta(days=1)
    if date_string is not None:
        last_day = datetime.strptime(date_string, '%Y-%m-%d')

    # 看当天是否已经记录了metric
    has_metric = await get_metric_by_date(db, last_day)
    # 删除当天数据
    await remove_metric_data(db, last_day)
    logger.info(
        "[join metric data]hasMetric: {has_metric}",
        has_metric=has_metric.id if has_metric else None,
        uid="uid"
    )

    # 从Redis获取聚合数据
    date_str = last_day.strftime('%Y-%m-%d')
    redis_key = f"langbase_message_model_agg_{date_str}"
    raw_data = await redis_client.get(redis_key)

    if not raw_data:
        logger.error(
            f"[join metric] No data found in Redis for date: {date_str}", uid="uid")
        return []

    try:
        metrics_data = json.loads(raw_data)
        app_cache = get_redis_sync(APP_CACHE_KEY)
        if app_cache is None:
            app_cache = '{}'
        app_cache = json.loads(app_cache)
        metrics = []

        for data in metrics_data:
            # 转换数据类型
            count = int(data['messages'])
            total_tokens = int(data['total_tokens'])
            time_ms = int(data['time'])
            app_info = app_cache.get(data['app_id'])

            metricDict = {
                'app_id': data['app_id'],
                'name': app_info['name'] if app_info else None,
                'group_id': app_info['group_id'] if app_info else None,
                'workspace_id': app_info['workspace_id'] if app_info else None,
                'users': int(data['users']),
                'conversations': int(data['conversations']),
                'model_name': data['model_name'],
                'total_tokens': total_tokens,
                'token_per_message': int(total_tokens / count) if count > 0 else 0,
                'token_per_second': int((total_tokens * 1000) / time_ms) if time_ms > 0 else 0,
                'time': int(time_ms / count) if count > 0 else 0,
                'date': last_day.replace(hour=0, minute=0, second=0, microsecond=0),
                'messages': count,
                'fee': float(data['fee']),
                'extra': {'fee': float(data['fee'])}
            }

            # 创建并保存到数据库
            metric = AppMetric(**metricDict)
            db.add(metric)
            metrics.append(metricDict)

        db.commit()
        logger_info(f"[join metric] data success: {len(metrics)}")
        # 格式化日期
        formatted_metrics = [
            {**metric, 'date': metric['date'].strftime('%Y-%m-%d')}
            for metric in metrics
        ]

    except Exception as e:
        logger.error("[join metric] data error: {e}", e=e, uid="uid")
        db.rollback()
        return []
    finally:
        if need_alert:
            await alert_fee(date_string=date_str)
        return formatted_metrics


async def alert_fee(date_string: Optional[str] = None, force: Optional[bool] = False):
    """
    根据日期字符串查询metric信息，并对高费用应用发送预警
    """
    popo_keys = get_redis_sync(ALERT_POPO_KEY)
    if popo_keys is not None:
        popo_keys = json.loads(popo_keys)
        popo_keys = popo_keys.get('high_fee_alert', [settings.alert_popo_id])
    else:
        popo_keys = [settings.alert_popo_id]

    for popo_key in popo_keys:
        await alert_high_fee_by_date(date_string=date_string, force=force, to_popo=popo_key)
    return True


async def alert_group_fee(start_date: Optional[str] = None, end_date: Optional[str] = None, workspace_id: Optional[str] = None, to_popo: Optional[str] = None):
    """
    根据日期字符串查询metric信息，并对高费用应用发送预警
    """
    start = datetime.now() - timedelta(days=8)
    start = start.replace(hour=0, minute=0, second=0, microsecond=0)
    end = datetime.now() - timedelta(days=1)
    end = end.replace(hour=0, minute=0, second=0, microsecond=0)

    if start_date is not None:
        start = datetime.strptime(start_date, '%Y-%m-%d')
    if end_date is not None:
        end = datetime.strptime(end_date, '%Y-%m-%d')

    if to_popo is not None:
        return await alert_group_fee_by_date(start_date=start, end_date=end, workspace_id=workspace_id, to_popo=to_popo)
    popo_keys = get_redis_sync(ALERT_POPO_KEY)
    if popo_keys is not None:
        popo_keys = json.loads(popo_keys)
        popo_keys = popo_keys.get('group_fee_alert', [settings.alert_popo_id])
    else:
        popo_keys = [settings.alert_popo_id]
    for popo_key in popo_keys:
        await alert_group_fee_by_date(start_date=start, end_date=end, workspace_id=workspace_id, to_popo=popo_key)
    return True


async def alert_high_fee_by_date(date_string: Optional[str] = None, force: Optional[bool] = False, to_popo: Optional[str] = None):
    """
    根据日期字符串查询metric信息，并对高费用应用发送预警

    Args:
        date_string: 日期字符串，格式为'YYYY-MM-DD'，如果为None则使用昨天的日期
    """
    db = next(get_db())
    target_date = datetime.now() - timedelta(days=1)
    if date_string is not None:
        try:
            target_date = datetime.strptime(date_string, '%Y-%m-%d')
        except ValueError:
            logger.error(
                f"[alert_high_fee_by_date] Invalid date format: {date_string}", uid="uid")
            return
    start_date = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = start_date + timedelta(days=1)
    data = await get_excel_of_metric(db=db, start_date=start_date, end_date=end_date, no_excel=True)
    app_rows = data['app_rows']
    app_rows = [app for app in app_rows if app['总费用(元)'] > 10]

    date_str = target_date.strftime('%Y-%m-%d')
    alert_key = f"{FEE_ALERT_KEY}::{date_str}"

    # 检查当天是否已经发送过报警
    has_alerted = get_redis_sync(alert_key)
    if has_alerted and not force:
        logger_info(
            f"[alert_high_fee_by_date] Alert already sent for date: {date_str}")
        return []

    all_success = True
    # 每次最多发送8个，如果超过8个则分批发送
    for i in range(0, len(app_rows), 10):
        batch_apps = app_rows[i:i+10]
        # 调用预警函数
        success = await alert_high_fee_apps(apps=batch_apps, to_popo=to_popo)
        if not success:
            all_success = False
            logger_error(
                f"[alert_high_fee_by_date] Failed to send alerts for batch {i} of {len(high_fee_apps)} high fee apps")
            break
        else:
            logger_info(
                f"[alert_high_fee_by_date] Sent alerts for {len(batch_apps)} high fee apps")
    if all_success:
        # 设置Redis标记，表示当天已发送报警，有效期一周
        set_redis_sync(alert_key, "1", ex=604800)
        logger_info(
            f"[alert_high_fee_by_date] Sent alerts for {len(app_rows)} high fee apps")
        return app_rows
    else:
        logger_error("[alert_high_fee_by_date] Failed to send alerts")
    return []


async def get_metric_by_model_name(db: Session, date: datetime, name: Optional[str] = None, model_name: Optional[str] = None):
    query = db.query(AppMetric).filter(
        AppMetric.date == date
    )
    if name:
        query = query.filter(AppMetric.name == name)
    if model_name:
        query = query.filter(AppMetric.model_name == model_name)
    return query.all()


def clean_model_name(model_name: str) -> str:
    """清理模型名称中的特定前缀"""
    prefixes_to_remove = [
        "字节跳动大模型服务（豆包大模型）-",
        "开源LLM模型-",
        "推理（输入）",
        "推理（输出）",
        "以内",
    ]
    result = model_name or ''
    for prefix in prefixes_to_remove:
        result = result.replace(prefix, "")
    return result


async def modify_fee(db: Session, app_id: str, date: datetime, type: str, fee: float):
    metric = db.query(AppMetric).filter(
        AppMetric.app_id == app_id,
        AppMetric.date == date
    ).first()

    if metric is None:
        return
    if type == 'add':
        metric.fee = float(metric.fee) + fee
    elif type == 'sub':
        metric.fee = float(metric.fee) - fee
    elif type == 'set':
        metric.fee = fee
    db.commit()
    return metric


async def add_metrics_batch(db: Session, items: List[dict]):
    """
    批量添加指标数据

    Args:
        db: 数据库会话
        items: 指标数据列表
    """
    metrics = []
    # 取前一天的日期
    date = datetime.now().replace(hour=0, minute=0, second=0,
                                  microsecond=0) - timedelta(days=1)

    try:
        # 用于聚合的字典，key为name+model_name
        aggregated_data: dict[str, dict] = {}

        # 第一步：聚合数据
        for item in items:
            cleaned_model_name = clean_model_name(item['modelName'])
            key = f"{item['name']}::{cleaned_model_name}"

            if key not in aggregated_data:
                aggregated_data[key] = {
                    'name': item['name'],
                    'model_name': cleaned_model_name,
                    'fee': 0.0,
                    'total_tokens': 0,
                    'messages': 0,
                    'time': 0,
                    'users': 0,
                    'conversations': 0,
                    'demo_url': item.get('demo_url', '')
                }

            # 累加各项数据
            agg_item: dict = aggregated_data[key]
            agg_item['fee'] += float(item['fee'] or 0)
            agg_item['total_tokens'] += int(item['totalTokens'] or 0)
            agg_item['messages'] += int(item['messages'] or 0)
            agg_item['time'] += int(item['totalTime'] or 0)
            agg_item['users'] += int(item.get('users', 0))
            agg_item['conversations'] += int(item.get('conversations', 0))

        # 第二步：过滤并写入数据库
        for key, agg_data in aggregated_data.items():
            # 过滤掉费用小于1的数据
            if agg_data['fee'] < 1:
                continue

            # 检查是否已存在
            find_metrics = await get_metric_by_model_name(db, date, agg_data['name'], agg_data['model_name'])
            if len(find_metrics) > 0:
                continue

            metric_dict = {
                'name': agg_data['name'],
                'workspace_id': 'f97bf87b-a8ec-4bc9-a8e3-662e111cd2e4',
                'group_id': '97897edb-df1d-4ecf-9bb0-414afa73f733',
                'model_name': agg_data['model_name'],
                'fee': agg_data['fee'],
                'total_tokens': agg_data['total_tokens'],
                'messages': agg_data['messages'],
                'time': agg_data['time'],
                'date': date,
                'users': agg_data['users'],
                'conversations': agg_data['conversations'],
                'extra': {
                    'model_name': agg_data['model_name'],
                    'demo_url': agg_data['demo_url'],
                    'fee': agg_data['fee']
                }
            }

            metric = AppMetric(**metric_dict)
            db.add(metric)
            metrics.append(metric_dict)

        db.commit()
        logger_info(
            f"[add metrics batch] Successfully added {len(metrics)} metrics")

        # 格式化日期以便返回
        formatted_metrics = [
            {**metric, 'date': date.strftime('%Y-%m-%d')}
            for metric in metrics
        ]

        return formatted_metrics
    except Exception as e:
        logger_error(f"[add metrics batch] Failed to add metrics: {str(e)}")
        db.rollback()
        raise e


async def get_model_metric(db: Session,
                           sort_filed: str,
                           sort_order: str,
                           group_id: Optional[str] = None,
                           workspace_id: Optional[str] = None,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None):
    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - DEFAULT_DATE_OFFSET
    start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
    end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=0)

    # 查询基础数据
    query = db.query(
        AppMetric.model_name.label('model_name'),
        AppMetric.app_id.label('app_id'),
        AppMetric.group_id.label('group_id'),
        AppMetric.workspace_id.label('workspace_id'),
        AppMetric.name.label('name'),
        AppMetric.total_tokens.label('tokens'),
        AppMetric.messages.label('messages'),
        AppMetric.fee.label('fee'),
        AppMetric.date.label('date'),
    ).filter(
        AppMetric.date >= start_date,
        AppMetric.date <= end_date)

    # 添加过滤条件
    if group_id != 'all' and group_id is not None:
        query = query.filter(AppMetric.group_id == group_id)
    if workspace_id != 'all' and workspace_id is not None:
        query = query.filter(AppMetric.workspace_id == workspace_id)

    # 排序
    order_rule = asc
    if sort_order == 'descend':
        order_rule = desc
    stat = query.order_by(order_rule(sort_filed))
    stats = stat.all()

    # 按model_name聚合
    model_map: Dict[str, Dict] = {}
    for row in stats:
        model_name = clean_model_name(
            row.model_name)  # 使用clean_model_name清理模型名称
        if model_name not in model_map:
            model_map[model_name] = {
                'model_name': model_name,
                'tokens': row.tokens,
                'messages': row.messages,
                'fee': float(row.fee or 0),
                'details': {}
            }
            # 初始化该应用的聚合数据
            model_map[model_name]['details'][row.name] = {
                'app_id': row.app_id,
                'name': row.name,
                'group_id': row.group_id,
                'workspace_id': row.workspace_id,
                'tokens': row.tokens,
                'messages': row.messages,
                'fee': float(row.fee or 0)
            }
        else:
            item = model_map[model_name]
            item['tokens'] += row.tokens
            item['messages'] += row.messages
            item['fee'] += float(row.fee or 0)

            # 按应用名聚合details
            if row.name not in item['details']:
                item['details'][row.name] = {
                    'app_id': row.app_id,
                    'name': row.name,
                    'group_id': row.group_id,
                    'workspace_id': row.workspace_id,
                    'tokens': row.tokens,
                    'messages': row.messages,
                    'fee': float(row.fee or 0)
                }
            else:
                app_item = item['details'][row.name]
                app_item['tokens'] += row.tokens
                app_item['messages'] += row.messages
                app_item['fee'] += float(row.fee or 0)

    # 将details从字典转为列表
    for item in model_map.values():
        item['details'] = list(item['details'].values())

    # 将查询结果转换为列表并返回
    return list(model_map.values())
