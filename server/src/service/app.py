import difflib
import json

from typing import List, Literal, Optional, Tuple, Union

from uuid import uuid4
from loguru import logger
from sqlalchemy import or_, desc
from sqlalchemy.orm import Session
from datetime import datetime

from .http_session import HTTPQuery

from ..misc.http_db import get_http_db

from ..misc.session import get_redis

from ..models.env_variable import EnvVariable

from ..cache.env_variable_cache import env_variable_cache
from ..workflow.workflow_client import WorkflowClient
from ..schema.member import MemberCreate, Role
from ..schema.common import ResourceType, UseType
from ..util import to_sha256
from ..misc.errors import NotFound, BadRequest
from ..schema.app import AgentConfig, AppCreate, AppType, \
    AppUpdate, ConfigCreate, ConfigUpdate, ConfigType, \
    AppDeploy, AppDeployStatus, \
    AppTriggerRequest, WorkflowConfig, AppDebug
from .group import get as get_group_service
from ..models.app import App, VersionedAppConfig, VersionedAppConfigComponents
from ..service.env_variable import \
    check_app_env_variable_values_setting as check_app_env_variable_values_setting_service
from ..config import settings

from ..service import member as member_service
from ..service.setting import create_setting as create_setting_service
from ..service.env_variable import \
    list_app_component_env_variable as list_app_component_env_variable_service

from ..models.collection import Collection
from ..schema.collection import ResourceType


def init_workflow_clients() -> dict[str, WorkflowClient]:
    clients: Optional[dict[str, WorkflowClient]] = {}
    for engine in settings.workflow_config.engines:
        clients[engine.id] = WorkflowClient(engine)
    return clients


workflow_clients = init_workflow_clients()


def app_config_component_ids(config: dict) -> List[str]:
    ids = []
    if config is None:
        return ids
    if "nodes" in config:
        for node in config['nodes']:
            if "componentId" in node:
                ids.append(node["componentId"])
    return ids


# 迁移测试完成
async def create_config(
        db: Session,
        creater_id: str,
        app_id: str,
        config_create: ConfigCreate,
):
    components = app_config_component_ids(config_create.config)
    http_db = get_http_db('tb_versioned_app_config')

    sha = to_sha256(json.dumps(config_create.config))
    app_config = VersionedAppConfig(
        app_id=app_id,
        hash=sha,
        type=config_create.type,
        config=config_create.config,
        status=config_create.status,
        settingId=config_create.settingId,
        message=config_create.message if hasattr(
            config_create, 'message') and config_create.message is not None else datetime.now().strftime("%m%d%H%M"),
        components=components,
        created_by=creater_id,
        updated_by=creater_id)
    http_db.add(app_config)
    http_db.commit()
    http_db.refresh(app_config)
    # 删除旧关联
    db.query(VersionedAppConfigComponents) \
        .filter(VersionedAppConfigComponents.app_id == app_id).delete()
    # 更新关联
    for component_id in set(components):
        db.add(VersionedAppConfigComponents(app_config_id=app_config.id,
                                            app_id=app_id,
                                            component_id=component_id))
    db.commit()
    return app_config


# 迁移测试完成
async def copy_config(
        db: Session,
        config_id: str,
        created_id: str,
        type: Literal['rollback', 'switch']
):
    type_dict = {
        'rollback': '回滚',
        'switch': '切换'
    }
    http_db = get_http_db('tb_versioned_app_config')
    db_config = await get_config(db, config_id)
    app_config = VersionedAppConfig(
        app_id=db_config.app_id,
        hash=db_config.hash,
        type='snapshot',
        config=db_config.config,
        status=db_config.status,
        message=datetime.now().strftime("%m%d%H%M") + f'-{type_dict[type]}',
        components=db_config.components,
        created_by=created_id,
        updated_by=created_id,
    )
    http_db.add(app_config)
    http_db.commit()
    http_db.refresh(app_config)
    return app_config


# 迁移测试完成
async def update_config(
        db: Session,
        update_id: str,
        config_id: str,
        config_update: ConfigUpdate):
    http_db = get_http_db('tb_versioned_app_config')
    db_config = await get_config(db, config_id)
    columns = config_update.model_dump(exclude_unset=True)
    columns[VersionedAppConfig.updated_by] = update_id

    http_db.query(VersionedAppConfig).filter(VersionedAppConfig.id == config_id).update(columns)  # type: ignore # noqa
    http_db.commit()
    http_db.refresh(db_config)
    return db_config


# 迁移测试完成
async def update_config_status(
        db: Session,
        update_id: str,
        config_id: str,
        config_update: ConfigUpdate):
    http_db = get_http_db('tb_versioned_app_config')
    db_config = await get_config(db, config_id)
    columns = config_update.model_dump(exclude_unset=True)
    columns[VersionedAppConfig.updated_by] = update_id
    columns[VersionedAppConfig.status] = config_update.status
    if config_update.message is not None:
        columns[VersionedAppConfig.message] = config_update.message

    http_db.query(VersionedAppConfig).filter(VersionedAppConfig.id == config_id).update(columns)  # type: ignore # noqa
    http_db.commit()
    http_db.refresh(db_config)
    return db_config


async def create(db: Session, creater_id: str, creater_name: str,
                 group_id: str, app_create: AppCreate) \
        -> Tuple[App, VersionedAppConfig]:
    group = await get_group_service(db, group_id)
    new_config = app_create.config
    http_db = get_http_db()

    # 说明有templateId
    if app_create.config is not None and 'templateId' in app_create.config:
        new_config = await get_app_latest_config(http_db, app_id=app_create.config['templateId'])
        new_config = new_config.config or app_create.config
        if 'businessConfig' in app_create.config:
            new_config['businessConfig'] = app_create.config['businessConfig']
        # workflow应用需要把workflowId设置为创建填入的
        if 'workflowId' in new_config and 'workflowId' in app_create.config:
            new_config['workflowId'] = app_create.config['workflowId']

    stat = http_db.query(App).filter(App.name == app_create.name,
                                     App.workspace_id == group.workspace_id)
    if stat.count() > 0:
        raise BadRequest(
            f"App name ({app_create.name}) already exists in the same workspace")

    app_id = uuid4()

    app = App(**app_create.model_dump(exclude={"config"}))

    setting = None

    if app.type == AppType.VIRTUALHUMAN:
        setting = await create_setting_service(db, str(app_id), app_create.name, creater_id)

    app_config: VersionedAppConfig = await create_config(db, creater_id, str(app_id), ConfigCreate(
        type=ConfigType.SNAPSHOT
        if app.type == AppType.WORKFLOW or app.type == AppType.AGENTWORKFLOW
        else ConfigType.HISTORY,
        message="initial",
        config=new_config,
        status=AppDeployStatus.UNKNOW if app.type == AppType.WORKFLOW else AppDeployStatus.SUCCESS,
        settingId=setting.settingId if setting is not None else '',
    ))

    if app.type == AppType.WORKFLOW:
        config = WorkflowConfig.model_validate(app_create.config)
        if config.workflow_engine_id not in workflow_clients:
            raise NotFound(
                f"Workflow engine({config.workflow_engine_id}) client not found")
        client = workflow_clients[config.workflow_engine_id]

        client.create_workflow(
            flow_id=config.workflow_id, operator=creater_name)

    await member_service.create(
        db,
        resource_type=ResourceType.APP,
        user_id=creater_id,
        resource_id=str(app_id),
        member_create=MemberCreate(
            memberID=creater_id,
            role=Role.ADMIN.value,
        )
    )
    app.id = str(app_id)
    app.group_id = group.id
    app.workspace_id = group.workspace_id

    app.created_by = creater_id
    app.updated_by = creater_id
    if app.type != AppType.WORKFLOW or (app.app_config_id is None or app.app_config_id == ""):
        app.app_config_id = app_config.id

    if app.type == AppType.VIRTUALHUMAN:
        app.app_config_id = None

    http_db.add(app)
    http_db.commit()
    http_db.refresh(app)

    # if isinstance(app.extInfo, str):
    #     app.extInfo = json.loads(app.extInfo)
    # print(app)
    return (app, app_config)


# 迁移测试完成
async def list_app_with_starred(db: Session,
                                workspace_id: Optional[str] = None,
                                group_id: Optional[str] = None,
                                appType: Optional[List[AppType]] = None,
                                subType: Optional[List[str]] = None,
                                names: Optional[List[str]] = None,
                                created_by: Optional[str] = None,
                                user_id: Optional[str] = None,
                                is_template: Optional[bool] = None,
                                show_all: Optional[bool] = None,
                                is_basic: Optional[bool] = False,
                                use_type: Optional[UseType] = None,
                                offset: Optional[int] = None,
                                limit: Optional[int] = None,) -> Tuple[List[App], int]:
    star_app_ids, _ = await list_starred_app_ids(db=db, user_id=user_id, group_id=group_id)

    # 获取所有starred应用（不分页）并按顺序排列
    ordered_star_apps = []
    if len(star_app_ids) > 0:
        star_apps_raw, _ = await list_app(db=db,
                                          app_ids=star_app_ids,
                                          workspace_id=workspace_id,
                                          group_id=group_id,
                                          appType=appType,
                                          subType=subType,
                                          names=names,
                                          created_by=created_by,
                                          is_template=is_template,
                                          show_all=show_all,
                                          is_basic=is_basic)

        # 创建app_id到app对象的映射
        app_dict = {app.id: app for app in star_apps_raw}

        # 按照star_app_ids顺序重新排列starred应用
        for app_id in star_app_ids:
            if app_id in app_dict:
                app = app_dict[app_id]
                app.starred = True  # 设置starred标记
                ordered_star_apps.append(app)

    # 如果只要starred应用，直接返回
    if use_type == UseType.STAR:
        start_idx = offset or 0
        end_idx = start_idx + (limit or len(ordered_star_apps))
        paginated_apps = ordered_star_apps[start_idx:end_idx]
        return paginated_apps, len(ordered_star_apps)

    # 计算精确的分页参数
    offset = offset or 0
    limit = limit or 20
    star_count = len(ordered_star_apps)

    # 计算starred应用的切片范围
    star_start = max(0, min(offset, star_count))
    star_end = min(offset + limit, star_count)
    needed_star_apps = ordered_star_apps[star_start:star_end]

    # 计算non-starred应用的查询参数
    if offset < star_count:
        # 部分或全部在starred范围内，non-starred从头开始取
        not_star_offset = 0
        not_star_limit = limit - len(needed_star_apps)
    else:
        # 完全在non-starred范围内
        not_star_offset = offset - star_count
        not_star_limit = limit
        needed_star_apps = []  # 不需要starred应用

    # 查询non-starred应用（如果需要的话）
    if not_star_limit > 0:
        not_star_apps, not_star_total = await list_app(db=db,
                                                       not_app_ids=star_app_ids,
                                                       workspace_id=workspace_id,
                                                       group_id=group_id,
                                                       appType=appType,
                                                       subType=subType,
                                                       created_by=created_by,
                                                       names=names,
                                                       is_template=is_template,
                                                       show_all=show_all,
                                                       is_basic=is_basic,
                                                       offset=not_star_offset,
                                                       limit=not_star_limit)
    else:
        not_star_apps, not_star_total = [], 0

    # 如果查询的non-starred应用超过了实际需要的数量，进行裁剪
    if len(not_star_apps) > not_star_limit:
        not_star_apps = not_star_apps[:not_star_limit]

    # 合并结果
    result_apps = needed_star_apps + not_star_apps
    total = star_count + not_star_total

    return result_apps, total

# 迁移测试完成


async def list_app(db: Session,
                   app_ids: Optional[List[str]] = None,
                   not_app_ids: Optional[List[str]] = None,
                   workspace_id: Optional[str] = None,
                   group_id: Optional[str] = None,
                   appType: Optional[List[AppType]] = None,
                   subType: Optional[List[str]] = None,
                   names: Optional[List[str]] = None,
                   created_by: Optional[str] = None,
                   is_template: Optional[bool] = None,
                   show_all: Optional[bool] = None,
                   is_basic: Optional[bool] = False,
                   offset: Optional[int] = None,
                   limit: Optional[int] = None,
                   ) -> Tuple[List[App], int]:
    http_db = get_http_db()
    stat: HTTPQuery = http_db.query(App)

    if workspace_id is not None:
        stat = stat.filter(App.workspace_id == workspace_id)

    if group_id is not None:
        stat = stat.filter(App.group_id == group_id)

    if appType is not None:
        stat = stat.filter(App.type.in_(appType))
    elif not show_all:
        stat = stat.filter(App.type.in_([AppType.COMPLETE, AppType.CONVERSATION,
                           AppType.WORKFLOW, AppType.AGENTWORKFLOW, AppType.VIRTUALHUMAN, AppType.STORYLINE]))

    if subType is not None:
        stat = stat.filter(App.sub_type == subType)

    if names is not None:
        stat = stat.filter(
            or_(*[App.name.like(f"%{name}%") for name in names])
        )

    if created_by is not None and created_by != "":
        stat = stat.filter(App.created_by == created_by)

    if is_template is not None:
        stat = stat.filter(App.is_template == is_template)
        is_basic = None

    if is_basic is not None:
        stat = stat.filter(App.is_basic == is_basic)

    if app_ids is not None and len(app_ids) > 0:
        stat = stat.filter(App.id.in_(app_ids))
    if not_app_ids is not None and len(not_app_ids) > 0:
        stat = stat.filter(~App.id.in_(not_app_ids))

    stat = stat.order_by(App.updated_at.desc())

    total = stat.count()

    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)

    return (stat.all(), total)


# 迁移测试完成
async def get(db: Optional[Session] = None,
              app_id: Optional[str] = None,
              is_basic: Optional[bool] = None,
              workspace_id: Optional[str] = None,
              user_id: Optional[str] = None,
              ) -> App:
    http_db = get_http_db('tb_app')
    stat = http_db.query(App)
    if app_id is not None:
        stat = stat.filter(App.id == app_id)
    if is_basic is not None:
        stat = stat.filter(App.is_basic == is_basic)
    if workspace_id is not None:
        stat = stat.filter(App.workspace_id == workspace_id)

    db_app = stat.first()
    # if not hasattr(db_app, 'config') or db_app.config is None and hasattr(db_app, 'set_value'):
    #     config = await get_config(db, db_app.app_config_id)
    #     db_app.set_value('config', config.config)
    if db_app is None:
        raise NotFound(f"App({app_id}) not found")
    if user_id is not None and db is not None:
        starApp = db.query(Collection).filter(Collection.user_id == user_id, Collection.resource_id ==
                                              app_id, Collection.resource_type == ResourceType.APP.value).first()
        if starApp is not None:
            db_app.starred = True
    return db_app


# 迁移测试完成
async def get_config(db: Session, config_id: str) -> VersionedAppConfig:
    http_db = get_http_db('tb_versioned_app_config')
    config = http_db.query(VersionedAppConfig) \
        .filter(config_id == VersionedAppConfig.id).first()
    if config is None:
        raise NotFound(f"Config({config_id}) not found")
    # 如果config.config是str，则转成json
    if isinstance(config.config, str):
        config.config = json.loads(config.config)
    return config


# 迁移测试完成
async def get_config_by_app(db: Session, app_id: str) -> VersionedAppConfig:
    http_db = get_http_db()
    app = await get(
        http_db,
        app_id=app_id)
    return await get_config(
        http_db,
        config_id=str(app.app_config_id))


# 迁移测试完成
async def get_app_latest_config(db: Session, app_id: str) -> VersionedAppConfig:
    http_db = get_http_db('tb_versioned_app_config')
    config = http_db.query(VersionedAppConfig) \
        .filter(VersionedAppConfig.app_id == app_id).order_by(VersionedAppConfig.created_at.desc()).first()
    if config is None:
        raise NotFound(f"Config({app_id}) latest config not found")
    return config


# 迁移测试完成
async def get_app_latest_history_config(db: Session, app_id: str) -> VersionedAppConfig:
    http_db = get_http_db('tb_versioned_app_config')
    config = http_db.query(VersionedAppConfig) \
        .filter(VersionedAppConfig.app_id == app_id) \
        .filter(VersionedAppConfig.type == ConfigType.HISTORY.value) \
        .order_by(VersionedAppConfig.created_at.desc()).first()
    if config is None:
        raise NotFound(f"Config({app_id}) latest config not found")
    return config


# 迁移测试完成
async def delete(db: Session, app_id: str, user_id: str, user_name: str):
    db_app = await get(db, app_id)
    http_db = get_http_db()
    if str(db_app.type) == AppType.WORKFLOW.value:
        db_version_config = await get_config(http_db, db_app.app_config_id)
        config = WorkflowConfig.model_validate(db_version_config.config)
        if config.workflow_engine_id not in workflow_clients:
            raise NotFound(
                f"Workflow engine({config.workflow_engine_id}) client not found")
        # client = workflow_clients[config.workflow_engine_id]
        # client.delete_workflow(flow_id=config.workflow_id, operator=user_name)
        # client.delete_workflow(flow_id=config.workflow_id + settings.debug_workflow_id_suffix,
        #                        operator=user_name)

    # config数据不做操作删除，本身DDB也是软删除，相关数据也保留
    # db.query(VersionedAppConfig).filter(VersionedAppConfig.app_id == app_id).update(
    #     {VersionedAppConfig.deleted_at: datetime.now(),
    #      VersionedAppConfig.deleted_by: user_id}
    # )
    db.query(VersionedAppConfigComponents).filter(
        VersionedAppConfigComponents.app_id == app_id).delete()
    http_db.query(App).filter(App.id == app_id).delete()
    http_db.commit()


# 更新app的appId
# 迁移完成
async def update_app_config_id(db: Session,
                               app_id: str,
                               config_id: str):
    http_db = get_http_db()
    # 确认config_id存在
    config = await get_config(db, config_id)
    if config is None:
        raise NotFound(f"Config({config_id}) not found")
    http_db.query(App).filter(App.id == app_id).update(
        {App.app_config_id: config.id})
    http_db.commit()
    app = await get(db, app_id)
    app.config = config.config
    return app


# 迁移完成
async def update(db: Session,
                 app_id: str,
                 user_id: str,
                 app_update: AppUpdate):
    http_db = get_http_db()
    app = await get(db, app_id)
    columns = app_update.model_dump(exclude_unset=True, exclude={"config"})
    if app_update.config is not None:
        app_config = await create_config(
            db=db,
            creater_id=user_id,
            app_id=app_id,
            config_create=ConfigCreate(
                type=ConfigType.SNAPSHOT
                if app.type == AppType.WORKFLOW or app.type == AppType.AGENTWORKFLOW else
                ConfigType.HISTORY,
                config=app_update.config,
                message=app_update.config['message'] if 'message' in app_update.config else '',
                status=AppDeployStatus.UNKNOW if app.type == AppType.WORKFLOW else AppDeployStatus.SUCCESS,
            ))
        if app.type != AppType.WORKFLOW:
            columns[App.app_config_id] = app_config.id

    if len(columns) != 0:
        columns[App.updated_by] = user_id
        http_db.query(App).filter(App.id == app_id).update(
            columns)  # type: ignore
        http_db.commit()
    return await get(db, app_id)


# 迁移测试完成
async def deploy(db: Session,
                 workspace_id: str,
                 group_id: str,
                 app_id: str,
                 user_id: str,
                 user_name: str,
                 app_deploy: AppDeploy):
    if app_deploy.config is None:
        return None
    http_db = get_http_db()

    app = await get(db, app_id)

    await create_config(
        db=db,
        creater_id=user_id,
        app_id=app_id,
        config_create=ConfigCreate(
            type=ConfigType.SNAPSHOT,
            config=app_deploy.config,
            message=app_deploy.config['message'] if 'message' in app_deploy.config else None,
            status=AppDeployStatus.UNKNOW if app.type == AppType.WORKFLOW else AppDeployStatus.SUCCESS,
        ))

    app_config = await create_config(
        db=db,
        creater_id=user_id,
        app_id=app_id,
        config_create=ConfigCreate(
            type=ConfigType.HISTORY,
            config=app_deploy.config,
            message=app_deploy.config['message'] if 'message' in app_deploy.config else None,
            status=AppDeployStatus.UNKNOW if app.type == AppType.WORKFLOW else AppDeployStatus.SUCCESS,
        ))

    columns = app_deploy.model_dump(exclude_unset=True, exclude={"config"})
    columns[App.app_config_id] = app_config.id
    columns[App.updated_by] = user_id
    http_db.query(App).filter(App.id == app_id).update(columns)  # type: ignore
    http_db.commit()

    if app.type == AppType.WORKFLOW:
        config = WorkflowConfig.model_validate(app_config.config)

        components = app_config.components if app_config.components is not None else []
        await check_app_env_variable_values_setting_service(db=db,
                                                            components=components,
                                                            workspace_id=workspace_id,
                                                            group_id=group_id,
                                                            app_id=app_id)

        if config.workflow_engine_id not in workflow_clients:
            raise NotFound(
                f"Workflow engine({config.workflow_engine_id}) client not found")
        client = workflow_clients[config.workflow_engine_id]

        client.submit_workflow(flow_id=config.workflow_id,
                               version=app_config.id,
                               flow_json=config.model_dump_json(),
                               operator=user_name)

    return await update_config_status(db=db, update_id=user_id, config_id=app_config.id,
                                      config_update=ConfigUpdate(status=AppDeployStatus.SUCCESS))


# 迁移测试完成
async def debug(db: Session,
                workspace_id: str,
                group_id: str,
                app_id: str,
                user_id: str,
                user_name: str,
                app_debug: AppDebug):
    if app_debug.config is None:
        return None

    app_config = await get_app_latest_config(db, app_id)
    # 创建配置的深拷贝，避免修改原始数据
    config1 = json.loads(json.dumps(app_config.config))
    config2 = json.loads(json.dumps(app_debug.config))

    # 过滤掉 nodes 中 type 为 INPUT 的节点，因为相关节点在debug中会通过trigger接口传入
    if 'nodes' in config1:
        config1['nodes'] = [node for node in config1['nodes']
                            if node.get('type') != 'INPUT']
    if 'nodes' in config2:
        config2['nodes'] = [node for node in config2['nodes']
                            if node.get('type') != 'INPUT']

    # 比较处理后的配置是否相同
    if json.dumps(config1) != json.dumps(config2):
        app_config = await create_config(
            db=db,
            creater_id=user_id,
            app_id=app_id,
            config_create=ConfigCreate(
                type=ConfigType.SNAPSHOT,
                config=app_debug.config,
                message=f'{datetime.now().strftime("%m%d%H%M")}-调试',
                status=AppDeployStatus.UNKNOW,
            ))

    config = WorkflowConfig.model_validate(app_config.config)

    components = app_config.components if app_config.components is not None else []
    await check_app_env_variable_values_setting_service(db=db,
                                                        components=components,
                                                        workspace_id=workspace_id,
                                                        group_id=group_id,
                                                        app_id=app_id)

    if config.workflow_engine_id not in workflow_clients:
        raise NotFound(
            f"Workflow engine({config.workflow_engine_id}) client not found")
    client = workflow_clients[config.workflow_engine_id]

    client.submit_workflow(flow_id=config.workflow_id + settings.debug_workflow_id_suffix,
                           version=app_config.id,
                           flow_json=config.model_dump_json(),
                           operator=user_name)

    return await update_config_status(db=db, update_id=user_id, config_id=app_config.id,
                                      config_update=ConfigUpdate(status=AppDeployStatus.SUCCESS))


def set_default_inputs_value_for_trigger(trigger_request: AppTriggerRequest, config: WorkflowConfig):
    for param in config.inputs if config.inputs is not None else []:
        if param.value is not None and param.asDefault is True and param.name not in trigger_request.inputs:
            trigger_request.inputs[param.name] = param.value


async def star_app(db: Session, app_id: str, user_id: str) -> Collection:
    """收藏应用

    Args:
        db (Session): 数据库会话
        app_id (str): 应用ID
        user_id (str): 用户ID

    Returns:
        Collection: 收藏记录
    """
    # 检查应用是否存在
    app = await get(db, app_id)
    if app is None:
        raise NotFound(f"App({app_id}) not found")

    # 检查是否已经收藏
    existing = db.query(Collection).filter(
        Collection.user_id == user_id,
        Collection.resource_id == app_id,
        Collection.resource_type == ResourceType.APP.value,
        Collection.group_id == app.group_id
    ).first()

    if existing:
        return existing

    # 创建新的收藏记录
    collection = Collection(
        id=str(uuid4()),
        user_id=user_id,
        resource_id=app_id,
        resource_type=ResourceType.APP.value,
        group_id=app.group_id
    )

    db.add(collection)
    db.commit()
    db.refresh(collection)

    return collection


async def unstar_app(db: Session, app_id: str, user_id: str) -> None:
    """取消收藏应用

    Args:
        db (Session): 数据库会话
        app_id (str): 应用ID
        user_id (str): 用户ID
    """
    app = await get(db, app_id)
    if app is None:
        raise NotFound(f"App({app_id}) not found")
    # 删除收藏记录
    db.query(Collection).filter(
        Collection.user_id == user_id,
        Collection.resource_id == app_id,
        Collection.resource_type == ResourceType.APP.value,
        Collection.group_id == app.group_id
    ).delete()

    db.commit()


async def list_starred_app_ids(db: Session, user_id: Optional[str] = None, group_id: Optional[str] = None, offset: Optional[int] = None, limit: Optional[int] = None) -> tuple[list[str], int]:
    """获取用户收藏的应用列表

    Args:
        db (Session): 数据库会话
        user_id (str): 用户ID
        offset (Optional[int], optional): 分页偏移量. Defaults to None.
        limit (Optional[int], optional): 分页大小. Defaults to None.

    Returns:
        tuple[list[App], int]: 应用列表和总数
    """
    if user_id is None:
        return [], 0
    # 查询收藏记录
    collections = db.query(Collection).filter(Collection.user_id == user_id)
    if group_id is not None:
        collections = collections.filter(
            Collection.group_id == group_id
        )

    total = collections.count()

    if offset is not None:
        collections = collections.offset(offset)
    if limit is not None:
        collections = collections.limit(limit)

    collections = collections.order_by(Collection.created_at.desc())
    collections = collections.all()

    # 获取应用详情
    app_ids = [c.resource_id for c in collections]

    return app_ids, total


async def trigger(db: Session,
                  app_id: str,
                  trigger_request: AppTriggerRequest) -> dict:
    db_app = await get(db, app_id)

    if trigger_request.debug:
        db_version_config = await get_app_latest_config(db, db_app.id)
    else:
        db_version_config = await get_config(db, db_app.app_config_id)

    config = WorkflowConfig.model_validate(db_version_config.config)

    if config.workflow_engine_id not in workflow_clients:
        raise NotFound(
            f"Workflow engine({db_version_config.workflowEngineId}) client not found")
    client = workflow_clients[config.workflow_engine_id]

    components = db_version_config.components if db_version_config.components is not None else []
    if not trigger_request.debug:
        envs = env_variable_cache.get(components=components,
                                      app_id=db_app.id,
                                      group_id=db_app.group_id,
                                      workspace_id=db_app.workspace_id)
        logger.info('debug envs: {envs}', envs=envs, uid='uid')
    else:
        (env_vars, total) = await list_app_component_env_variable_service(db=db,
                                                                          components=components,
                                                                          app_id=db_app.id,
                                                                          group_id=db_app.group_id,
                                                                          workspace_id=db_app.workspace_id)
        logger.info('trigger envs: {env_vars}', env_vars=env_vars, uid='uid')
        envs = {}
        for env_var in env_vars:
            envs[env_var.name] = env_var.value

    flow_id = config.workflow_id \
        if not trigger_request.debug else config.workflow_id + settings.debug_workflow_id_suffix
    callback = trigger_request.callback.model_dump(
    ) if trigger_request.callback is not None else {}
    biz_info = trigger_request.biz_info.model_dump(
    ) if trigger_request.biz_info is not None else {}
    set_default_inputs_value_for_trigger(
        trigger_request=trigger_request, config=config)

    resp = client.trigger_workflow_run(flow_id=flow_id,
                                       inputs=trigger_request.inputs,
                                       envs=envs,
                                       callback=callback,
                                       biz_info=biz_info,
                                       biz_context=trigger_request.biz_context)
    return {
        "appID": app_id,
        "runID": resp["runId"] if resp["runId"] is not None else None,
        "outputs": resp["outputs"] if resp["outputs"] is not None else {},
    }


# 迁移测试完成
async def list_workflow_run(db: Session,
                            app_id: str,
                            page_size: Optional[int],
                            page_number: Optional[int],
                            start_time: int,
                            end_time: int,
                            debug: Optional[bool] = False) -> (List[dict], int):
    db_app = await get(db, app_id)
    db_version_config = await get_config(db, db_app.app_config_id)
    config = WorkflowConfig.model_validate(db_version_config.config)

    client = workflow_clients[config.workflow_engine_id]
    if client is None:
        raise NotFound(
            f"Workflow engine({db_version_config.workflowEngineId}) client not found")

    flow_id = config.workflow_id if not debug else config.workflow_id + \
        settings.debug_workflow_id_suffix

    return client.list_workflow_run(flow_id=flow_id, page_size=page_size, page_number=page_number,
                                    start_time=start_time, end_time=end_time)


# 迁移测试完成
async def get_workflow_run(db: Session,
                           app_id: str,
                           run_id: str,
                           debug: Optional[bool] = False) -> dict:
    db_app = await get(db, app_id)
    db_version_config = await get_app_latest_config(db, db_app.id)
    config = WorkflowConfig.model_validate(db_version_config.config)
    client = workflow_clients[config.workflow_engine_id]
    if client is None:
        raise NotFound(
            f"Workflow engine({db_version_config.workflowEngineId}) client not found")

    flow_id = config.workflow_id if not debug else config.workflow_id + \
        settings.debug_workflow_id_suffix

    return client.get_workflow_run(flow_id=flow_id, run_id=run_id)


# 迁移测试完成
async def get_workflow_run_status(db: Session,
                                  app_id: str,
                                  run_id: str,
                                  node_id: str,
                                  index_list: str,
                                  debug: Optional[bool] = False) -> dict:
    db_app = await get(db, app_id)
    db_version_config = await get_app_latest_config(db, db_app.id)
    config = WorkflowConfig.model_validate(db_version_config.config)
    client = workflow_clients[config.workflow_engine_id]
    if client is None:
        raise NotFound(
            f"Workflow engine({db_version_config.workflowEngineId}) client not found")

    flow_id = config.workflow_id if not debug else config.workflow_id + \
        settings.debug_workflow_id_suffix

    return client.get_workflow_run_node(flow_id=flow_id, run_id=run_id, node_id=node_id, index_list=index_list)


# 迁移测试完成
async def list_workflow_run_events(db: Session,
                                   app_id: str,
                                   run_id: str,
                                   node_id: str,
                                   page_number: int,
                                   page_size: int,
                                   debug: Optional[bool] = False) -> (List[dict], int):
    db_app = await get(db, app_id)
    db_version_config = await get_app_latest_config(db, db_app.id)
    config = WorkflowConfig.model_validate(db_version_config.config)
    client = workflow_clients[config.workflow_engine_id]
    if client is None:
        raise NotFound(
            f"Workflow engine({db_version_config.workflowEngineId}) client not found")

    flow_id = config.workflow_id if not debug else config.workflow_id + \
        settings.debug_workflow_id_suffix

    return client.list_workflow_run_events(flow_id=flow_id, run_id=run_id, node_id=node_id,
                                           page_number=page_number, page_size=page_size)


# 迁移测试完成
async def list_config(db: Session,
                      type: ConfigType = ConfigType.HISTORY,
                      app_id: Optional[Union[str, List[str]]] = None,
                      offset: Optional[int] = None,
                      limit: Optional[int] = None,
                      order: Optional[str] = "desc"):
    http_db = get_http_db('tb_versioned_app_config')
    stat = http_db.query(VersionedAppConfig) \
        .filter(VersionedAppConfig.type == type.value)
    if app_id is not None:
        if isinstance(app_id, str):
            stat = stat.filter(VersionedAppConfig.app_id == app_id)
        if isinstance(app_id, list):
            stat = stat.filter(VersionedAppConfig.app_id.in_(app_id))

    if order == "asc":
        stat = stat.order_by(VersionedAppConfig.created_at.asc())
    elif order == "desc":
        stat = stat.order_by(VersionedAppConfig.created_at.desc())

    count = stat.count()

    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)
    res = stat.all()
    # 所有的config如果是str都转成json
    for item in res:
        if isinstance(item.config, str):
            item.config = json.loads(item.config)

    return (res, count)


# 迁移测试完成
async def is_component_ref_by_app_config(db: Session, component_id: str) -> bool:
    stat = db.query(VersionedAppConfigComponents) \
        .filter(VersionedAppConfigComponents.component_id == component_id)
    stat = stat.limit(1)
    return len(stat.all()) != 0


# 迁移测试完成
async def list_app_component_env_variable(db: Session, app_id: str) -> Tuple[List[EnvVariable], int]:
    app = await get(db, app_id)
    app_config = await get_app_latest_config(db=db, app_id=str(app_id))
    if str(app.type) == AppType.AGENTWORKFLOW:
        return ([], 0)
    elif str(app.type) != AppType.WORKFLOW:
        components = []
        config = AgentConfig.model_validate(app_config.config)
        if config.tools is not None:
            components = [tool_id for tool_id in config.tools.keys()]
    else:
        components = app_config.components if app_config.components is not None else []
    return await list_app_component_env_variable_service(db=db, components=components,
                                                         app_id=app.id, group_id=app.group_id,
                                                         workspace_id=app.workspace_id)


# 迁移测试完成
async def try_lock(app_id: str, username: str) -> str:
    redis_client = get_redis()
    hold_username = await redis_client.get(app_id)
    if hold_username is not None:
        if hold_username != username:
            return hold_username
        else:
            await redis_client.expire(app_id, settings.app_lock_expire_seconds)
        return hold_username

    resp = await redis_client.set(app_id, username, nx=True, ex=settings.app_lock_expire_seconds)
    if resp is True:
        return username
    hold_username = await redis_client.get(app_id)
    return hold_username


async def release_lock(app_id: str, username: str):
    hold_username = await redis_client.get(app_id)
    if hold_username is not None and hold_username == username:
        await redis_client.delete(app_id)


async def save_setting_config_service(db: Session, user_id: str, appId, settingId, config: dict):
    db_config = await create_config(
        db=db,
        creater_id=user_id,
        app_id=appId,
        config_create=ConfigCreate(
            type=ConfigType.SNAPSHOT,
            settingId=settingId,
            config=config,  # type: ignore
        ))
    return db_config


def generate_plus_minus_diff(text1, text2):
    """
    生成 + 和 - 格式的 diff。
    """
    diff = difflib.Differ().compare(text1.splitlines(), text2.splitlines())
    result = []
    for line in diff:
        if line.startswith('  '):  # Unchanged
            continue
        elif line.startswith('+ '):  # Added
            result.append('+ ' + line[2:])
        elif line.startswith('- '):  # Removed
            result.append('- ' + line[2:])
        elif line.startswith('? '):  # Ignored
            continue
    return '\n'.join(result)


def generate_diff(mock: dict):
    result = ''
    for key, value in mock.items():
        diff_output = generate_plus_minus_diff(
            value["oldValue"], value["newValue"])
        result += (key + ':\n' + diff_output)
    return result
