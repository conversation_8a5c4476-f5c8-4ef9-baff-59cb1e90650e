import json
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>, Dict
from loguru import logger
from sqlalchemy import and_
from sqlalchemy.orm import Session

from ..misc.redis_utils import AIGW_ACCOUNT_MAP_KEY, set_redis_sync

from ..models.aigw import AigwApp
from ..schema.aigw import AigwAppBind, AigwAppCreate, AigwAppUpdate
from ..schema.common import AigwResourceType
from ..misc.errors import NotFound, PermissionDenied
from .aigw_client import AigwClient


async def bind(
    db: Session,
    user_id: str,
    aigw_bind: AigwAppBind,
) -> AigwApp:
    """Create a new aigw app"""
    # First create app in AIGW system
    # 先查询一下当前账号的详细信息
    new_app = await query_aigw_app(aigw_bind.app_code)
    if new_app['app_id'] != aigw_bind.token.split('.')[0]:
        raise Exception("app_code和token不匹配")

    # Create local record
    db_aigw = AigwApp(
        **aigw_bind.model_dump(),
        quota=new_app['credit']['quota'],
        extra=new_app,
        created_by=user_id,
        updated_by=user_id
    )

    db.add(db_aigw)
    db.commit()
    return db_aigw


async def create(
    db: Session,
    user_id: str,
    aigw_create: AigwAppCreate,
) -> AigwApp:
    """Create a new aigw app"""
    # First create app in AIGW system
    client = AigwClient()
    aigw_response = None
    try:
        aigw_response = await client.create_app(
            code='_langbase_' + aigw_create.app_code,
            name='_langbase_' + aigw_create.name,
            project="langbase_" + aigw_create.app_code,
            usage="Created from LangBase platform"
        )
    except Exception as e:
        logger.error("Failed to create AIGW app: {e}", e=e, uid="uid")
        raise Exception(f"Failed to create AIGW app: {str(e)}")

    logger.info("AIGW app created: {aigw_response}",
                aigw_response=aigw_response, uid="uid")
    # aigw_response = {'code': '_langbase_workflow_dev1', 'name': '_langbase_云音乐-workflow-dev', 'type': 'project', 'app_id': 'pbbj0wzu075p4bfb', 'app_key': 'w39m5yjk6yailrriaoogo0vdbs74y7lu',
    #                  'authorization_header': 'Bearer pbbj0wzu075p4bfb.w39m5yjk6yailrriaoogo0vdbs74y7lu', 'cost_code': '', 'cost_name': '', 'credit': {'quota': 0}}

    # Create local record
    db_aigw = AigwApp(
        **aigw_create.model_dump(exclude={'name', 'app_code'}),
        quota=aigw_response['credit']['quota'],
        app_code=aigw_response['code'],
        token=aigw_response['app_id'] + '.' + aigw_response['app_key'],
        extra=aigw_response,
        created_by=user_id,
        updated_by=user_id
    )

    db.add(db_aigw)
    db.commit()
    return db_aigw


async def get(
    db: Session,
    aigw_id: str,
) -> AigwApp:
    """Get aigw app by id"""
    db_aigw = db.query(AigwApp).filter(
        AigwApp.id == aigw_id
    ).first()
    if db_aigw is None:
        raise NotFound(f"AigwApp({aigw_id}) not found")
    return db_aigw


async def list_aigw_apps(
    db: Session,
    resource_type: AigwResourceType,
    resource_id: str,
    token: Optional[str] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = None
) -> Tuple[List[AigwApp], int]:
    """List aigw apps with filters"""
    query = db.query(AigwApp).filter(
        and_(
            AigwApp.resource_type == resource_type,
            AigwApp.resource_id == resource_id
        )
    )

    if token:
        query = query.filter(AigwApp.token == token)

    total = query.count()

    if limit is not None:
        query = query.limit(limit)
    if offset is not None:
        query = query.offset(offset)

    return query.all(), total


async def get_aigw_app(
    db: Session,
    resource_type: AigwResourceType,
    resource_id: str,
    token: Optional[str] = None,
) -> Optional[AigwApp]:
    """Get aigw app by token"""
    query = db.query(AigwApp).filter(
        and_(
            AigwApp.resource_type == resource_type,
            AigwApp.resource_id == resource_id
        )
    )

    if token:
        query = query.filter(AigwApp.token == token)
    app = query.first()
    if app is None:
        return None
    new_app = await query_aigw_app(app.app_code)
    app.extra = new_app
    app.quota = new_app['credit']['quota']
    db.commit()
    return app


async def refresh_aigw_account_map(
    db: Session,
) -> None:
    """Refresh aigw account map"""
    cache_key = f"{AIGW_ACCOUNT_MAP_KEY}"
    # 获取所有aigw app
    aigw_apps = db.query(AigwApp).all()
    aigw_map = {}
    # 将aigw app的token作为key，app_code作为value，存储到redis中
    for aigw_app in aigw_apps:
        key = f"{aigw_app.resource_type.lower()}_{aigw_app.resource_id}"
        aigw_map[key] = {
            "appCode": aigw_app.app_code,
        }
    # 存储到redis中
    set_redis_sync(cache_key, json.dumps({
        "accountMap": aigw_map,
    }))


async def update(
    db: Session,
    aigw_id: int,
    resource_type: AigwResourceType,
    resource_id: str,
    aigw_update: AigwAppUpdate,
) -> AigwApp:
    """Update aigw app"""
    db_aigw = await get(db, aigw_id, resource_type, resource_id)

    update_data = aigw_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_aigw, key, value)

    db.commit()
    db.refresh(db_aigw)
    return db_aigw


async def delete(
    db: Session,
    aigw_id: str,
) -> None:
    """Delete aigw app"""
    db_aigw = await get(db, aigw_id)
    db.delete(db_aigw)
    db.commit()


async def query_aigw_app(
    app_code: str,
) -> Dict:
    """Query app details from AIGW system"""
    client = AigwClient()
    try:
        return await client.query_app(app_code)
    except Exception as e:
        logger.error("Failed to query AIGW app: {e}", e=e, uid="uid")
        raise Exception(f"Failed to query AIGW app: {str(e)}")


async def get_or_query_aigw_app(
    db: Session,
    resource_type: AigwResourceType,
    resource_id: str,
    token: Optional[str] = None,
) -> AigwApp:
    """Get app from local DB or query from AIGW system"""
    try:
        # First try to get from local DB
        app = await get_aigw_app(db, resource_type, resource_id, token)
        return app
    except NotFound:
        # If not found in local DB, try to query from AIGW
        if token:
            app_code = token.split('.')[0]  # 从token中提取app_code
            aigw_response = await query_aigw_app(db, app_code)

            # Create local record
            db_aigw = AigwApp(
                app_code=app_code,
                resource_type=resource_type,
                resource_id=resource_id,
                quota=str(aigw_response.get('credit', {}).get('quota', 0)),
                token=token,
                extra=aigw_response
            )

            db.add(db_aigw)
            db.commit()
            db.refresh(db_aigw)
            return db_aigw
        raise
