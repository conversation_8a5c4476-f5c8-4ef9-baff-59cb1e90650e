from datetime import datetime
from typing import List, Optional
from uuid import uuid4
from sqlalchemy.orm import Session
import requests
import json
from loguru import logger

from ..misc.redis_utils import DISABLE_DOCUMENT_TASK_CACHE_KEY, DOCUMENT_TASKS_CACHE_KEY, get_redis_sync

from ..misc.log import logger_info

from ..misc.db import get_db
from ..misc.errors import InternalServerError, NotFound
from ..schema.document_task import DocumentTaskCreate, DocumentTaskUpdate
from ..models.document_task import DocumentTask
from ..schema.document import DocumentCreate
from .document import create as create_document
from ..models.document import Document
from ..config import settings


async def list_document_task_by_knowledge(db: Session,
                                          ids: Optional[List[str]] = None,
                                          knowledge_id: Optional[str] = None,
                                          keyword: Optional[str] = None,
                                          limit: Optional[int] = None,
                                          offset: Optional[int] = None):
    stat = db.query(DocumentTask) \
        .filter(DocumentTask.knowledge_id == knowledge_id)

    # await check_ada_document_task(knowledge_id)

    if keyword is not None:
        stat = stat.filter(DocumentTask.title.contains(keyword))
    if ids is not None:
        stat = stat.filter(DocumentTask.id.in_(ids))
    count = stat.count()
    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    return (stat.all(), count)


async def create(db: Session,
                 user_id: str,
                 workspace_id: str,
                 document_task_create: DocumentTaskCreate,
                 document_task_id: Optional[str] = None):
    document_task = DocumentTask(**document_task_create.model_dump())
    if document_task_id is not None:
        document_task.id = document_task_id
    document_task.workspace_id = workspace_id
    document_task.created_by = user_id
    document_task.updated_by = user_id
    db.add(document_task)
    db.commit()
    db.refresh(document_task)
    return document_task


async def create_document_task_with_member(
    db: Session,
    user_id: str,
    workspace_id: str,
    document_task_create: DocumentTaskCreate,
):
    document_task_id = str(uuid4())
    document_task = await create(
        db,
        user_id,
        workspace_id,
        document_task_create,
        document_task_id=document_task_id)
    return document_task


async def get(db: Session, document_task_id: str):
    db_document_task = db.query(DocumentTask).filter(
        DocumentTask.id == document_task_id).first()
    if db_document_task is None:
        raise NotFound(f"DocumentTask({document_task_id}) not found")
    return db_document_task


async def update(db: Session, user_id: Optional[str],
                 document_task_id: str, document_task_update: DocumentTaskUpdate):
    db_document_task = await get(db, document_task_id)

    columns = document_task_update.model_dump(exclude_unset=True)
    if user_id is not None:
        columns[DocumentTask.updated_by] = user_id
    db.query(DocumentTask).filter(DocumentTask.id == document_task_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_document_task)
    return db_document_task


async def delete(db: Session, user_id: str, document_task_id: str):
    await get(db, document_task_id)
    db.query(DocumentTask).filter(DocumentTask.id == document_task_id).update(
        {DocumentTask.deleted_at: datetime.now(),
         DocumentTask.updated_by: user_id,
         DocumentTask.deleted_by: user_id}
    )
    db.commit()


async def delete_with_check(
    db: Session, user_id: str, document_task_id: str
):
    await delete(db, user_id, document_task_id)


async def create_ada_document_task(collectionId, config, username):
    url = settings.ada_api_base + '/ada/doc/proxy/import-remote'
    headers = {'uid': username or 'huangkai1'}
    try:
        ret = requests.post(
            url, data={'collectionId': collectionId,
                       'remoteConfig': json.dumps(config['remoteConfig']),
                       'importConfig': json.dumps(config['importConfig'])},
            headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        return content['data']
    except Exception as e:
        raise InternalServerError('create ada document error' + str(e))


async def get_ada_document_task(taskIds):
    url = settings.ada_api_base + '/ada/doc/proxy/list-tasks'
    headers = {'uid': 'huangkai1'}
    try:
        ret = requests.get(
            url, params={'taskIds': taskIds},
            headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        return content['data']
    except Exception as e:
        raise InternalServerError('get ada document error' + str(e))


async def get_ada_document_detail(collectionId, docIds):
    url = settings.ada_api_base + '/ada/doc/proxy/detail'
    headers = {'uid': 'huangkai1'}
    try:
        ret = requests.get(
            url, params={'collectionId': collectionId, 'docIds': docIds},
            headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        return content['data']
    except Exception as e:
        raise InternalServerError('get ada document error' + str(e))


async def check_ada_document_task(knowledgeId: Optional[str] = None):
    # logger_info('ADA_TASK: start')
    disable = get_redis_sync(DISABLE_DOCUMENT_TASK_CACHE_KEY)
    if disable:
        logger_info('ADA_TASK: disable')
        return []
    db = next(get_db())
    tasks = db.query(DocumentTask).filter(
        DocumentTask.task_state.in_([0, 1, 2, 3]), DocumentTask.deleted_by.is_(None))
    # tasks = tasks.filter(DocumentTask.deleted_by.is_(None))
    if knowledgeId is not None:
        tasks = tasks.filter(DocumentTask.knowledge_id == knowledgeId)
    taskIds = []
    taskid2id = {}
    taskid2creator = {}
    taskid2knowledge = {}
    taskid2workspace = {}
    for task in tasks:
        taskIds.append(task.task_id)
        taskid2id[task.task_id] = task.id
        taskid2creator[task.task_id] = task.created_by
        taskid2knowledge[task.task_id] = task.knowledge_id
        taskid2workspace[task.task_id] = task.workspace_id
    taskIds_string = ",".join(str(element) for element in taskIds)
    logger_info('ADA_TASK: taskIds_string=' + taskIds_string)
    if taskIds_string == '' or taskIds_string is None:
        logger_info('ADA_TASK: end, no task, everything is ok!')
        return 'no task, everything is ok!'
    ada_tasks = await get_ada_document_task(taskIds_string)
    if ada_tasks is None:
        logger_info('ADA_TASK: end, no ada task, everything is ok!')
        return 'no ada task, everything is ok!'
    for task in ada_tasks:
        state = task['status']
        config = json.loads(task['config'])
        result = json.loads(task['result'])
        if state < 0:
            # 状态加20，转为正数
            state += 20
        document_task_update = DocumentTaskUpdate(task_state=state)
        task_id = taskid2id.get(task['taskId'])
        await update(db, None, task_id, document_task_update)

        if state == 4:
            docIds = ",".join(str(element) for element in result['docIds'])
            ada_doc_details = await get_ada_document_detail(config['collectionId'], docIds)
            for detail in ada_doc_details:
                document_id = str(uuid4())
                creator = taskid2creator.get(task['taskId'])
                knowledge_id = taskid2knowledge.get(task['taskId'])
                workspace_id = taskid2workspace.get(task['taskId'])
                document_create = DocumentCreate(
                    knowledge_id=knowledge_id,
                    title=detail['title'],
                    type=detail['type'],
                    source=detail['source'],
                    tag_name=",".join(str(element)
                                      for element in result.get('tags', [])),
                    config=config,
                    doc_id=detail['id'],
                    state=2)
                exist_doc = db.query(Document).filter(
                    Document.title == detail['title'], Document.knowledge_id == knowledge_id,
                    Document.deleted_by.is_(None)).first()
                if exist_doc:
                    logger_info(detail['title'] + '：已存在')
                else:
                    await create_document(db, creator, workspace_id, document_create, document_id)
    # logger_info('ADA_TASK: end')
    return ada_tasks
