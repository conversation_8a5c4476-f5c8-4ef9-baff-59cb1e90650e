from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from src.models.model import LLMModel
from src.schema.llm_model import LLMModelCreate, LLMModelUpdate
from src.misc.errors import NotFound


async def create(
    db: Session,
    model_data: LLMModelCreate,
    user_id: str
) -> LLMModel:
    db_model = LLMModel(**model_data.model_dump())
    db_model.createdBy = user_id
    db.add(db_model)
    db.commit()
    db.refresh(db_model)
    return db_model


async def get(db: Session, model_id: str) -> LLMModel:
    db_model = db.query(LLMModel).filter(
        LLMModel.id == model_id,
        LLMModel.deleted == False
    ).first()
    if db_model is None:
        raise NotFound(f"Model({model_id}) not found")
    return db_model


async def list(
    db: Session,
    limit: Optional[int] = None,
    offset: Optional[int] = None
) -> Tuple[List[LLMModel], int]:
    query = db.query(LLMModel).filter(LLMModel.deleted == False)
    count = query.count()

    if limit is not None:
        query = query.limit(limit)
    if offset is not None:
        query = query.offset(offset)

    return query.all(), count


async def update(
    db: Session,
    model_id: str,
    model_update: LLMModelUpdate
) -> LLMModel:
    db_model = await get(db, model_id)

    columns = model_update.model_dump(exclude_unset=True)
    db.query(LLMModel).filter(LLMModel.id == model_id).update(columns)
    db.commit()
    return db_model


async def delete(db: Session, model_id: str) -> None:
    db_model = await get(db, model_id)
    db.delete(db_model)
    db.commit()


async def update_all_model_configs(db: Session) -> None:
    # 获取所有未删除的模型
    query = db.query(LLMModel).filter(LLMModel.deleted == False)
    models = query.all()

    for model in models:
        if model.config:
            # 修改frequency_penalty的默认值
            if 'frequency_penalty' in model.config and model.config['frequency_penalty']['default'] != 0:
                model.config['frequency_penalty']['default'] = 0

            # 修改presence_penalty的默认值
            if 'presence_penalty' in model.config and model.config['presence_penalty']['default'] != 0:
                model.config['presence_penalty']['default'] = 0

            # 更新数据库
            db.query(LLMModel).filter(LLMModel.id == model.id).update({
                'config': model.config
            })
            db.commit()
