import json
from typing import List, <PERSON><PERSON>, <PERSON><PERSON>

import httpx
from sqlalchemy import or_, func, and_
from sqlalchemy.orm import Session

from ..misc.http_db import get_http_db, get_http_db_client

from ..misc.errors import NotFound, BadRequest
from datetime import datetime
from ..models.component import Component
from ..models.env_variable import EnvVariable
from ..schema.component import ComponentCreate, ComponentUpdate
from ..schema.common import Scope
from .app import is_component_ref_by_app_config, get_app_latest_config, \
    get_app_latest_history_config
from .env_variable import create_component_env_variables
from ..schema.env_variable import EnvVariableCreate, EnvVariableScopeType
from .env_variable import list_component_env_variable as list_component_env_variable_service, \
    create_component_env_variables as create_component_env_variables_service, \
    deleteEnvs as delete_component_env_variables_service
from .collection import list as list_collection_service, create as create_collection_service, \
    get as get_collection_service, delete as delete_collection_service
from ..schema.collection import CollectionCreate
from ..schema.common import ResourceType


async def get(db: Session, component_id: str) -> Component:
    http_db = get_http_db('tb_component')
    db_component = http_db.query(Component) \
        .filter(Component.id == component_id).first()
    if db_component is None:
        raise NotFound(f"Component({component_id}) not found")
    return db_component


async def cancel_favorite(db: Session,
                          user_id: str,
                          component_id: str):
    db_collection = await get_collection_service(db=db, user_id=user_id, resource_id=component_id,
                                                 resource_type=ResourceType.COMPONENTS.value)
    if db_collection is not None:
        await delete_collection_service(db=db, collection_id=db_collection.id)


async def favorite(db: Session,
                   user_id: str,
                   component_id: str):
    db_collection = await get_collection_service(db=db, user_id=user_id, resource_id=component_id,
                                                 resource_type=ResourceType.COMPONENTS.value)
    if db_collection is None:
        _ = await create_collection_service(db=db,
                                            collection=CollectionCreate(
                                                user_id=user_id,
                                                resource_type=ResourceType.COMPONENTS.value,
                                                resource_id=component_id,
                                            ))
    else:
        await delete_collection_service(db=db, collection_id=db_collection.id)


async def delete(db: Session,
                 user_id: str,
                 component_id: str):
    http_db = get_http_db('tb_component')
    if await is_component_ref_by_app_config(db, component_id):
        raise BadRequest(
            f"component {component_id} is ref by app config, cannot delete")
    # _: Component = await get(http_db, str(component_id))
    http_db.deleteHttp({"uniqueKey": component_id})
    # delete component envs
    db.query(EnvVariable).filter(
        EnvVariable.component_id == component_id).delete()
    db.commit()


async def validate_component_update(db: Session,
                                    db_component: Component,
                                    component_update: ComponentUpdate):
    http_db = get_http_db('tb_component')
    # if 'code' not in component_update.config \
    #         or component_update.code != component_update.config['code'] \
    #         or component_update.code != db_component.code:
    #     raise BadRequest(
    #         f"component code{component_update.code} not match config code or db code")
    if db_component.name != component_update.name:
        stat = http_db.query(Component).filter(
            Component.name == component_update.name)
        if stat.count() > 0:
            raise BadRequest("component name already exist")


async def update(db: Session,
                 user_id: str,
                 component_id: str,
                 component_update: ComponentUpdate):
    http_db = get_http_db('tb_component')
    db_component = await get(http_db, component_id)
    await validate_component_update(db=db, db_component=db_component, component_update=component_update)

    # cannot change env name and param, since it maybe has been used by app config
    envs, count = await list_component_env_variable_service(db=db,
                                                            component_id=component_id)
    envs_map = {}
    for env in envs:
        envs_map[env.name] = {
            "removed": True,
            "id": env.id,
            "param": env.param
        }

    added_envs = []

    # 找新增的环境变量
    if component_update.envs is not None:
        for env in component_update.envs:
            if env.name in envs_map:
                if env.param != envs_map[env.name]["param"]:
                    raise BadRequest(f"env {env.name} param cannot be changed")
                envs_map[env.name]["removed"] = False
            else:
                added_envs.append(EnvVariableCreate(
                    name=env.name,
                    component_id=component_id,
                    param=env.param,
                ))
    # 找删除的环境变量
    removed_envs = [env_info['id'] for env_name,
                    env_info in envs_map.items() if env_info["removed"]]

    if removed_envs is not None and len(removed_envs) > 0:
        await delete_component_env_variables_service(db=db, env_variable_ids=removed_envs)
    if added_envs is not None and len(added_envs) > 0:
        await create_component_env_variables_service(db=db, env_variables=added_envs, user_id=user_id)

    # update component
    component_update.config['id'] = component_id
    columns = component_update.model_dump(exclude={"envs"})
    columns[Component.updated_by] = user_id
    http_db.query(Component).filter(Component.id == component_id).update(columns)  # type: ignore # noqa
    http_db.commit()
    http_db.refresh(db_component)

    return db_component


async def list_components_v2(db: Session,
                             workspace_id: Optional[str] = None,
                             group_id: Optional[str] = None,
                             category_names: Optional[List[str]] = None,
                             types: Optional[List[str]] = None,
                             app_types: Optional[List[str]] = None,
                             hide_deprecated: Optional[bool] = None,
                             has_env_var: Optional[bool] = None,
                             user_id: Optional[str] = None,
                             is_favorite: Optional[bool] = None,
                             page: Optional[int] = None,
                             page_size: Optional[int] = None,
                             ids: Optional[List[str]] = None) \
        -> Tuple[List[Component], int]:
    client = get_http_db('tb_component')
    # stat = http_db.query(Component).order_by(Component.created_at.desc())
    params = {}

    # if has_env_var:
    #     stat = stat.join(EnvVariable, EnvVariable.component_id == Component.id) \
    #         .distinct(Component.id)

    # if is_favorite is not None and user_id is not None and is_favorite:
    #     (db_collections, total) = await list_collection_service(db=db, user_id=user_id,
    #                                                             resource_type=ResourceType.COMPONENTS.value)
    #     favorite_ids = []
    #     for collection in db_collections:
    #         favorite_ids.append(collection.resource_id)
    #     stat = stat.filter(Component.id.in_(favorite_ids))

    if ids is not None:
        # stat = stat.filter(Component.id.in_(ids))
        params['ids'] = ids

    if workspace_id is not None:
        params['workspace_id'] = workspace_id
        # stat = stat.filter(or_(Component.workspace_id == workspace_id,
        #                        Component.scope == Scope.PUBLIC.value))

    # 隐藏已弃用的组件
    if hide_deprecated is not None:
        params['hide_deprecated'] = hide_deprecated

    # 过滤本组才能见的
    if group_id is not None:
        params['group_id'] = group_id
        # stat = stat.filter(or_(Component.scope != Scope.GROUP.value, and_(
        # Component.scope == Scope.GROUP.value, Component.group_id == group_id)))

    if category_names is not None and len(category_names) > 0:
        params['category_names'] = category_names
        # stat = stat.filter(Component.category_name.in_(category_names))

    if types is not None and len(types) > 0:
        params['types'] = types
        # stat = stat.filter(Component.type.in_(types))

    # count = stat.count()
    if page is not None:
        params['page'] = page
    if page_size is not None:
        params['pageSize'] = page_size

    response = client.listComponentsHttp(params)

    # DDB无法过滤数组，只能服务处理
    if app_types is not None and len(app_types) > 0:
        response['values'] = [
            item for item in response['values']
            if all(app_type in (item.app_types if hasattr(item, 'app_types') else item['app_types'])
                   for app_type in app_types)
        ]
    return response['values'], response['total']


async def list_components(db: Session,
                          workspace_id: Optional[str] = None,
                          group_id: Optional[str] = None,
                          category_names: Optional[List[str]] = None,
                          types: Optional[List[str]] = None,
                          app_types: Optional[List[str]] = None,
                          has_env_var: Optional[bool] = None,
                          user_id: Optional[str] = None,
                          is_favorite: Optional[bool] = None,
                          offset: Optional[int] = None,
                          limit: Optional[int] = None,
                          ids: Optional[List[str]] = None) \
        -> Tuple[List[Component], int]:
    stat = db.query(Component).order_by(Component.created_at.desc())

    if has_env_var:
        stat = stat.join(EnvVariable, EnvVariable.component_id == Component.id) \
            .distinct(Component.id)

    if is_favorite is not None and user_id is not None and is_favorite:
        (db_collections, total) = await list_collection_service(db=db, user_id=user_id,
                                                                resource_type=ResourceType.COMPONENTS.value)
        favorite_ids = []
        for collection in db_collections:
            favorite_ids.append(collection.resource_id)
        stat = stat.filter(Component.id.in_(favorite_ids))

    if ids is not None:
        stat = stat.filter(Component.id.in_(ids))

    if workspace_id is not None:
        stat = stat.filter(or_(Component.workspace_id == workspace_id,
                               Component.scope == Scope.PUBLIC.value))

    # 过滤本组才能见的
    if group_id is not None:
        stat = stat.filter(or_(Component.scope != Scope.GROUP.value, and_(
            Component.scope == Scope.GROUP.value, Component.group_id == group_id)))

    if category_names is not None and len(category_names) > 0:
        stat = stat.filter(Component.category_name.in_(category_names))

    if types is not None and len(types) > 0:
        stat = stat.filter(Component.type.in_(types))

    if app_types is not None and len(app_types) > 0:
        stat = stat.filter(func.json_contains(
            Component.app_types, json.dumps(app_types)))

    count = stat.count()
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)

    return (stat.all(), count)


async def list_app_components(db: Session,
                              app_id: str,
                              category_names: List[str],
                              has_env_var: bool,
                              offset: Optional[int] = None,
                              limit: Optional[int] = None) \
        -> Tuple[List[Component], int]:
    app_config = await get_app_latest_config(db=db, app_id=str(app_id))
    comps = json.loads(
        app_config.components) if app_config.components is not None else []

    try:
        app_config = await get_app_latest_history_config(db=db, app_id=str(app_id))
        comps.extend(json.loads(app_config.components)
                     if app_config.components is not None else [])
    except NotFound:
        pass

    components = list(set(comps))
    stat = db.query(Component).filter(Component.id.in_(components))

    if category_names is not None and len(category_names) > 0:
        stat = stat.filter(Component.category_name.in_(category_names))
    if has_env_var:
        stat = stat.join(EnvVariable, EnvVariable.component_id == Component.id)

    count = stat.count()
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)

    return (stat.all(), count)


def validate_component_create(db: Session, component_create: ComponentCreate):
    http_db = get_http_db('tb_component')
    # if 'code' not in component_create.config or component_create.code != component_create.config['code']:
    #     raise BadRequest(
    #         f"component code{component_create.code} not match config code")

    stat = http_db.query(Component).filter(
        Component.name == component_create.name)
    if stat.count() > 0:
        raise BadRequest("component name already exist")

    stat = http_db.query(Component).filter(
        Component.code == component_create.code)
    if stat.count() > 0:
        raise BadRequest("component code already exist")


async def create(db: Session,
                 workspace_id: str,
                 component_create: ComponentCreate,
                 user_id: str):
    http_db = get_http_db('tb_component')
    validate_component_create(db=db, component_create=component_create)

    # insert component
    component = Component(**component_create.model_dump(exclude="envs"))
    component.workspace_id = workspace_id
    component.created_by = user_id
    component.updated_by = user_id
    http_db.add(component)
    http_db.commit()
    http_db.refresh(component)

    component_create.config['id'] = component.id
    columns = component_create.model_dump(exclude="envs")
    http_db.query(Component).filter(
        Component.id == component.id).update(columns)
    http_db.commit()
    http_db.refresh(component)

    envs = component_create.envs
    env_variables = []
    if envs is not None and len(envs) > 0:
        for env in envs:
            env_variables.append(EnvVariableCreate(
                name=env.name,
                component_id=component.id,
                param=env.param,
                scope_type=EnvVariableScopeType.COMPONENT.value,
                scope_id=component.id,
            ))
        await create_component_env_variables(db, env_variables, user_id)

    return component
