from datetime import datetime
import json
from typing import List, Optional
from uuid import uuid4
from loguru import logger
from sqlalchemy.orm import Session
import requests

from ..schema.knowledge import KnowledgeConfigDetail

from ..misc.redis_utils import ADA_DOC_MAP_KEY, get_redis_sync

from ..misc.errors import InternalServerError, NotFound
from ..schema.document import DocumentCreate, DocumentUpdate, RecallResponse
from ..models.document import Document
from ..config import settings
from ..service.account import get_by_id

SEGMENT_SUCCESS_STATE = 2
SEGMENT_FAIL_STATE = -1

DOCUMENT_SUCCESS_STATE = 2
DOCUMENT_END_STATE = 3


async def list_document_by_knowledge(db: Session,
                                     ids: Optional[List[str]] = None,
                                     knowledge_id: Optional[str] = None,
                                     collection_id: Optional[int] = None,
                                     keyword: Optional[str] = None,
                                     limit: Optional[int] = None,
                                     offset: Optional[int] = None):
    stat = db.query(Document) \
        .filter(Document.knowledge_id == knowledge_id)

    if keyword is not None:
        stat = stat.filter(Document.title.contains(keyword))
    if ids is not None:
        stat = stat.filter(Document.id.in_(ids))
    count = stat.count()
    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    documents = stat.all()
    if documents is not None:
        for document in documents:
            if document.state != DOCUMENT_END_STATE and document.state != DOCUMENT_SUCCESS_STATE:
                doc_segments = await get_ada_document_sgement_status(collection_id, document.doc_id)
                if doc_segments is not None:
                    success_count = 0
                    fail_count = 0
                    for segment in doc_segments:
                        if segment['status'] == SEGMENT_SUCCESS_STATE:
                            success_count += 1
                        if segment['status'] == SEGMENT_FAIL_STATE:
                            fail_count += 1
                    if success_count == len(doc_segments):
                        document.state = DOCUMENT_SUCCESS_STATE
                        db.commit()
                        db.refresh(document)
                    if success_count + fail_count == len(doc_segments) and fail_count > 0:
                        document.state = DOCUMENT_END_STATE
                        db.commit()
                        db.refresh(document)
                    document.segment = {'total': len(
                        doc_segments), 'success': success_count}
    return (documents, count)


async def create(db: Session,
                 user_id: str,
                 workspace_id: str,
                 document_create: DocumentCreate,
                 document_id: Optional[str] = None):
    document = Document(**document_create.model_dump())
    if document_id is not None:
        document.id = document_id
    document.workspace_id = workspace_id
    document.created_by = user_id
    document.updated_by = user_id
    db.add(document)
    db.commit()
    db.refresh(document)
    return document


async def create_document_with_member(
    db: Session,
    user_id: str,
    workspace_id: str,
    document_create: DocumentCreate,
):
    document_id = str(uuid4())
    document = await create(
        db,
        user_id,
        workspace_id,
        document_create,
        document_id=document_id)
    return document


async def get(db: Session, document_id: str):
    db_document = db.query(Document).filter(
        Document.id == document_id).first()
    if db_document is None:
        raise NotFound(f"Document({document_id}) not found")
    return db_document


async def get_segment(db: Session, collectionId: int, docId: int):
    ada_document_segment = await get_ada_document_sgement(collectionId, docId)
    return ada_document_segment


async def update(db: Session, user_id: str,
                 document_id: str, document_update: DocumentUpdate):
    db_document = await get(db, document_id)
    columns = document_update.model_dump(exclude_unset=True)
    columns[Document.updated_by] = user_id
    db.query(Document).filter(Document.id == document_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_document)
    return db_document


async def delete(db: Session, user_id: str, document_id: str):
    await get(db, document_id)
    db.query(Document).filter(Document.id == document_id).update(
        {Document.deleted_at: datetime.now(),
         Document.updated_by: user_id,
         Document.deleted_by: user_id}
    )
    db.commit()


async def delete_with_check(
    db: Session, user_id: str, document_id: str, collectionId, docId
):
    # print(document_id, collectionId, docId)
    user = await get_by_id(db, user_id)
    if docId:
        await remove_ada_document(collectionId, docId, user.name)
    await delete(db, user_id, document_id)


async def remove_ada_document(collectionId, docId, username):
    url = settings.ada_api_base + '/ada/doc/proxy/remove'
    headers = {'uid': username or 'huangkai1'}
    try:
        ret = requests.post(
            url, data={'collectionId': collectionId, 'docId': docId}, headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        return content['data']
    except Exception as e:
        raise InternalServerError('remove ada document error' + str(e))


async def create_ada_document(collectionId, files, importConfig, username):
    url = settings.ada_api_base + '/ada/doc/proxy/import-upload'
    headers = {'uid': username or 'huangkai1'}
    content = files.file.read()
    try:
        ret = requests.post(
            url, data={'collectionId': collectionId,
                       'importConfig': importConfig},
            files={'file': (files.filename, content, files.content_type)},
            headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        return content['data'][0]
    except Exception as e:
        raise InternalServerError('create ada document error' + str(e))


async def update_ada_document(collectionId, docId, files, importConfig, username):
    url = settings.ada_api_base + '/ada/doc/proxy/import-upload'
    headers = {'uid': username or 'huangkai1'}
    content = files.file.read()
    try:
        ret = requests.post(
            url, data={'collectionId': collectionId,
                       'docId': docId, 'importConfig': importConfig},
            files={'file': (files.filename, content, files.content_type)},
            headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        return content['data'][0]
    except Exception as e:
        raise InternalServerError('update ada document error' + str(e))


async def get_ada_document_sgement(collectionId, docId):
    url = settings.ada_api_base + '/ada/doc/proxy/list-segments'
    headers = {'uid': 'huangkai1'}
    try:
        ret = requests.get(
            url, params={'collectionId': collectionId, 'docId': docId}, headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        if 'data' in content:
            return content['data']
        else:
            return []
    except Exception as e:
        raise InternalServerError('get ada document segment error' + str(e))


async def get_ada_document_sgement_status(collectionId, docId):
    url = settings.ada_api_base + '/ada/doc/proxy/list-segments-status'
    headers = {'uid': 'huangkai1'}
    try:
        ret = requests.get(
            url, params={'collectionId': collectionId, 'docId': docId}, headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        if 'data' in content:
            return content['data']
        else:
            return []
    except Exception as e:
        raise InternalServerError(
            'get ada document segment status error' + str(e))


async def match_ada_document_sgement(collectionId, input, count, model, docId):
    url = settings.ada_api_base + '/ada/doc/proxy/get-docs'
    headers = {'uid': 'huangkai1'}
    try:
        ret = requests.get(
            url, params={'collectionId': collectionId, 'input': input,
                         'count': count, 'model': model, 'docId': docId},
            headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        if 'data' in content and content['data'] is not None:
            return content['data']
        logger.error(
            'match ada document segment error {content}', uid="uid", content=content)
        return []
    except Exception as e:
        raise InternalServerError('match ada document segment error' + str(e))


async def match_document_sgement_v2(
    knowledgeIds: List[int],
    query: str,
    groupId: str,
    knowledgeItemIds: Optional[List[int]] = None,
    knowledgeConfig: Optional[KnowledgeConfigDetail] = None,
    queryRewrite: Optional[dict] = None
) -> RecallResponse:
    url = settings.db_service_url + '/langbase/chat/rag/knowledge/recall'
    headers = {'uid': 'huangkai1'}
    filter = {'limit': 10, 'similarity': 0.7}
    searchType = 'SEMANTIC'
    if knowledgeConfig is not None and knowledgeConfig.searchConfig is not None:
        filter = knowledgeConfig.searchConfig.get(
            'filter', {'limit': 10, 'similarity': 0.7})
        searchType = knowledgeConfig.searchConfig.get('searchType', 'SEMANTIC')

    # 构建请求参数
    params = {
        'knowledgeIds': knowledgeIds,
        'query': query,
        'groupId': groupId,
        'searchType': searchType,
        'knowledgeItemIds': knowledgeItemIds,
        'filter': filter,
        'queryRewrite': queryRewrite or {'use': False, 'prompt': ''}
    }

    logger.info(
        'match document sgement v2 params {params}', params=params, uid='uid')

    try:
        ret = requests.post(url, json=params, headers=headers)
        content = json.loads(ret.content.decode('utf-8'))
        if 'data' in content and content['data'] is not None:
            return RecallResponse(**content['data'])
        logger.error(
            'match ada document segment error {content}', uid="uid", content=content)
        return RecallResponse(type="", results=[], knowledgeConfig={})
    except Exception as e:
        raise InternalServerError('match ada document segment error' + str(e))


def get_knowledge_ids(appId: str):
    # 从redis中获取知识库id
    ada_doc_map = get_redis_sync(ADA_DOC_MAP_KEY)
    if ada_doc_map:
        ada_doc_map = json.loads(ada_doc_map)
        return [ada_doc_map[appId]]
    return [27027]


async def get_most_matched_docs_service(input: str, count: int, appId: str):
    knowledge_ids = get_knowledge_ids(appId)
    search_response = await match_document_sgement_v2(
        knowledgeIds=knowledge_ids,
        query=input,
        groupId=appId,
        knowledgeConfig=KnowledgeConfigDetail(searchConfig={'filter': {
                                              'limit': count, 'similarity': 0.7}, 'searchType': 'SEMANTIC'}),
        knowledgeItemIds=None,
    )
    return search_response.results
