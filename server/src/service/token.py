import http
from typing import Optional

from sqlalchemy.orm import Session
from secrets import token_urlsafe

from ..misc.http_db import get_http_db

from ..misc.errors import InternalServerError, NotFound, ParameterInvalid
from ..models.app import App
from ..models.group import Group

from ..models.token import Token

from ..schema.common import ResourceType

from ..schema.token import TokenCreate


def gen_token(length: Optional[int] = None) -> str:
    return token_urlsafe(length)


async def get(
        db: Session,
        id: Optional[str] = None,
        token_value: Optional[str] = None,
):
    http_db = get_http_db()
    if id is None \
            and token_value is None:
        raise ParameterInvalid("id or token_value must be provided")

    stat = http_db.query(Token)
    if id is not None:
        stat = stat.filter(
            Token.id == id
        )
    if token_value is not None:
        stat = stat.filter(
            Token.value == token_value
        )
    token = stat.first()
    if token is None:
        raise NotFound("Token not found")

    return token


async def create(
        db: Session,
        resource_type: ResourceType,
        resource_id: str,
        user_id: str,
        token_create: TokenCreate
):
    http_db = get_http_db('tb_token')
    token_value = gen_token()

    try:
        await get(
            db=db,
            token_value=token_value)
        raise InternalServerError(f"Token already exists: {token_value}")
    except NotFound:
        pass

    token = Token(
        resource_type=resource_type.value,
        resource_id=resource_id,
        value=token_value,
        created_by=user_id)
    http_db.add(token)
    http_db.commit()
    http_db.refresh(token)

    # 删除relate细粒度调节逻辑
    # if token_create is not None:
    #     for resource in token_create.resources:
    #         db.add(TokenRelatedResource(token_id=token.id,
    #                                     resource_type=resource.type,
    #                                     resource_id=resource.id,
    #                                     created_by=user_id))
    # else:
    #     db.add(TokenRelatedResource(token_id=token.id,
    #                                 resource_type=resource_type.value,
    #                                 resource_id=resource_id,
    #                                 created_by=user_id))

    # db.commit()

    return token


async def delete(
        db: Session,
        id: Optional[str] = None,
        token_value: Optional[str] = None,
):
    token = await get(
        db=db,
        id=id,
        token_value=token_value
    )
    http_db = get_http_db('tb_token')

    http_db.delete(token)
    # db.query(TokenRelatedResource).filter(TokenRelatedResource.token_id == token.id).delete()
    http_db.commit()


# async def update(
#         db: Session,
#         user_id: str,
#         token_id: str,
#         token_update: TokenUpdate):
#     db.query(TokenRelatedResource).filter(
#         TokenRelatedResource.token_id == token_id).delete()
#     if token_update is not None and len(token_update.resources) > 0:
#         for resource in token_update.resources:
#             db.add(TokenRelatedResource(token_id=token_id,
#                                         resource_type=resource.type,
#                                         resource_id=resource.id,
#                                         created_by=user_id))
#     db.commit()


async def list(
        db: Session,
        resource_type: Optional[ResourceType] = None,
        resource_id: Optional[str] = None,
        offset: Optional[int] = None,
        limit: Optional[int] = None,
):
    http_db = get_http_db('tb_token')
    stat = http_db.query(Token)
    if resource_type is not None:
        stat = stat.filter(
            Token.resource_type == resource_type.value
        )
    if resource_id is not None:
        stat = stat.filter(
            Token.resource_id == resource_id
        )
    stat = stat.order_by(Token.created_at.desc())
    count = stat.count()
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)
    results = stat.all()

    return (results, count)


async def get_related_resource(db: Session,
                               resource_type: ResourceType,
                               resource_id: str,
                               token_id: str) -> (str, str):
    resources = []
    http_db = get_http_db()
    if resource_type == ResourceType.APP:
        app = http_db.query(App).filter(App.id == resource_id).first()
        if app is None:
            return None, None
        stat = http_db.query(Token) \
            .filter(Token.id == token_id)
        resources = stat.all()
        resources = filter(lambda x: x.resource_id in [
                           resource_id, app.group_id, app.workspace_id], resources)

    elif resource_type == ResourceType.GROUP:
        group = db.query(Group).filter(Group.id == resource_id).first()
        if group is None:
            return None, None
        stat = http_db.query(Token)
        stat.filter(Token.token_id == token_id)
        resources = stat.all()
        resources = filter(lambda x: x.resource_id in [
                           resource_id, group.workspace_id], resources)

    elif resource_type == ResourceType.WORKSPACE:
        stat = http_db.query(Token) \
            .filter(Token.id == token_id) \
            .filter(Token.resource_id == resource_id)
        resources = stat.all()
    else:
        return None, None

    res_type = None
    res_id = None
    for resource in resources:
        if resource_type == ResourceType.APP:
            if resource.resource_type == ResourceType.APP.value:
                res_type = resource.resource_type
                res_id = resource.resource_id
                return res_type, res_id
            if resource.resource_type == ResourceType.GROUP.value:
                if res_type is None:
                    res_type = resource.resource_type
                    res_id = resource.resource_id
            if resource.resource_type == ResourceType.WORKSPACE.value:
                if res_type is None:
                    res_type = resource.resource_type
                    res_id = resource.resource_id
        if resource_type == ResourceType.GROUP:
            if resource.resource_type == ResourceType.GROUP.value:
                res_type = resource.resource_type
                res_id = resource.resource_id
                return res_type, res_id
            if resource.resource_type == ResourceType.WORKSPACE.value:
                if res_type is None:
                    res_type = resource.resource_type
                    res_id = resource.resource_id
        if resource_type == ResourceType.WORKSPACE:
            if resource.resource_type == ResourceType.WORKSPACE.value:
                res_type = resource.resource_type
                res_id = resource.resource_id

    return res_type, res_id


async def list_related_resources(db: Session,
                                 token_id: str,
                                 offset: Optional[int] = None,
                                 limit: Optional[int] = None):
    # stat = db.query(TokenRelatedResource)
    # stat = stat.filter(TokenRelatedResource.token_id == token_id)

    # total = stat.count()
    return [], 0
    # return stat.all(), total
