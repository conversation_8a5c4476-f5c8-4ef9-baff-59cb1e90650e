from uuid import uuid4
from sqlalchemy.orm import Session
from typing import Optional, Tuple, List

from ..schema.app import ConfigType, ConfigCreate
from ..service.http_session import HTTPQuery, HTTPSession
from ..misc.http_db import get_http_db
from ..misc.errors import NotFound

from ..misc.errors import LangbaseException
from ..models.app import VersionedAppConfig
from ..models.setting import Setting
from ..service.account import get_by_id as get_account_by_id_service


async def list_setting_service(
    db: Session,
    appId: str,
) -> Tuple[List, int]:
    # 执行查询以获取匹配条件的模版
    http_db: HTTPSession = get_http_db('setting')
    query: HTTPQuery = http_db.query(Setting).filter(
        Setting.appId == appId
    ).order_by(Setting.createTime)  # type: ignore

    total: int = query.count()  # 获取总数
    settings_db = query.all()

    settings = []
    for setting in settings_db:
        # 遍历并处理每项数据
        setting = {
            "settingId": setting.id,
            "name": setting.name,
            "description": setting.description,
            "extInfo": setting.extInfo,  # 假设这是 JSON 类型，SQLAlchemy 自动解析
            "updatedAt": setting.updateTime,
            "createdAt": setting.createTime,
        }
        settings.append(setting)

    return settings, total


async def get_setting_all_snapshot_config_service(
    db: Session,
    settingId: str,
    page: int = 1,
    pageSize: int = 20
) -> Tuple[List[dict], int]:
    http_db = get_http_db('tb_versioned_app_config')

    base_query = http_db.query(VersionedAppConfig) \
        .filter(VersionedAppConfig.settingId == settingId) \
        .filter(VersionedAppConfig.type == ConfigType.SNAPSHOT)

    total_count = base_query.count()

    skip = (page - 1) * pageSize

    configs_db = base_query \
        .order_by(VersionedAppConfig.created_at.desc()) \
        .offset(skip) \
        .limit(pageSize) \
        .all()

    configs = []
    for config in configs_db:
        # type: ignore
        user_info = await get_account_by_id_service(db, config.created_by)
        config_dict = {
            "updatedAt": config.updated_at,
            "createdAt": config.created_at,
            "configId": config.id,
            "config": config.config,
            "settingId": config.settingId,
            "createdBy": {
                "id": user_info.id if user_info else config.created_by,
                "name": user_info.name if user_info else "",
                "fullname": user_info.fullname if user_info else "",
                "email": user_info.email if user_info else ""
            }
        }
        configs.append(config_dict)

    return configs, total_count

# async def get_setting_config(db: Session, settingId: str):
#     # 查询数据库获取模板详情，通过模板 ID 进行查找
#     template: Setting | None = db.query(Setting).filter(
#         Setting.settingId == settingId).first()
#     if template:
#         return {
#             "name": template.name,
#             "id": template.id,
#             "desc": template.description,  # 确保数据库模型包含此字段
#             "avatarUrl": template.avatar_url,
#             "promptType": template.prompt_type,
#             "prompt": template.text_prompt,
#             "structPrompt": template.struct_prompt  # 假设数据库中正确格式化该字段
#         }
#     return None


async def get_setting_lastest_config_service(db: Session, settingId: str) -> dict:
    http_db = get_http_db('tb_versioned_app_config')
    config_db = http_db.query(VersionedAppConfig) \
        .filter(VersionedAppConfig.settingId == settingId).order_by(VersionedAppConfig.created_at.desc()).first()
    if config_db is None:
        raise NotFound(f"Config({settingId}) latest config not found")
    if config_db:
        return {
            "updatedAt": config_db.updated_at,
            "createdAt": config_db.created_at,
            "condigId": config_db.id,
            "config": config_db.config,
            "settingId": config_db.settingId,
        }


async def create_setting_config(db: Session,
                                app_id: str,
                                name: str,
                                creater_id: str,
                                description: Optional[str] = None,
                                extInfo: Optional[str] = None,
                                config: Optional[dict] = None) -> dict:
    setting = await create_setting(db, str(app_id), name, creater_id, description, extInfo)

    if setting is not None:
        from ..service.app import create_config
        app_config: VersionedAppConfig = await create_config(db, creater_id, str(app_id), ConfigCreate(
            type=ConfigType.SNAPSHOT,
            message="initial",
            config=config if config is not None else {},
            settingId=setting.settingId if setting is not None else '',
        ))
        return {
            "settingId": setting.settingId,
            "name": setting.name,
            "description": setting.description,
            "extInfo": setting.extInfo,
            "config": app_config.config,
        }

    raise LangbaseException(status_code=500, message="create Setting failed")


async def create_setting(db: Session, app_id: str, name: str, creater_id: str, description: Optional[str] = None, extInfo: Optional[str] = None):
    http_db = get_http_db('setting')

    setting = Setting()
    setting.appId = app_id  # type: ignore
    setting.name = name  # type: ignore
    setting.creator = creater_id  # type: ignore
    setting.updater = creater_id  # type: ignore
    setting.description = description  # type: ignore
    setting.settingId = str(uuid4())  # type: ignore
    setting.extInfo = extInfo  # type: ignore
    http_db.add(setting)
    http_db.commit()
    http_db.refresh(setting)
    return setting


async def update_setting(db: Session, app_id: str,
                         settingId: str,
                         creater_id: str,
                         name: Optional[str] = None,
                         extInfo: Optional[str] = None,
                         description: Optional[str] = None):
    http_db = get_http_db('setting')

    setting = http_db.query(Setting).filter(
        Setting.settingId == settingId)

    if setting is None:
        raise LangbaseException(status_code=404, message="设置不存在")

    setting.update({
        Setting.name: name,
        Setting.description: description,
        Setting.updater: creater_id,
        Setting.extInfo: extInfo,
    })  # type: ignore
    http_db.commit()

    http_db.refresh(setting)

    # print(setting)

    return {
        "settingId": settingId,
        "name": name,  # type: ignore
        "description": description,  # type: ignore
        "extInfo": extInfo
    }


async def delete_setting(db: Session, settingId: str):
    http_db = get_http_db('setting')

    setting_db = http_db.query(Setting).filter(
        Setting.settingId == settingId).first()

    if setting_db:
        # if template.created_by != userId:
        #     raise LangbaseException(status_code=403, message="无权限删除该模版")
        # 删除模板数据库记录
        # else:
        http_db.delete(setting_db)
        http_db.commit()
        http_db.refresh(setting_db)
        return {"id": setting_db.settingId}
    else:
        raise LangbaseException(status_code=404, message="模版ID不存在")
