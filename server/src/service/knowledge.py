from datetime import datetime
from typing import List, Optional
from uuid import uuid4
from sqlalchemy.orm import Session
import requests
import json

from ..misc.errors import NotFound
from ..schema.knowledge import KnowledgeCreate, KnowledgeUpdate
from ..schema.app import AgentConfig
from ..models.knowledge import Knowledge
from ..models.document import Document
from ..service.document import match_ada_document_sgement, match_document_sgement_v2
from ..service.account import get_by_id
from ..schema.dialog import ChatCompletionContentPartTextParam, LLMMessage
from ..config import settings


async def list_knowledge_by_workspace(db: Session, workspace_id: str,
                                      ids: Optional[List[str]] = None,
                                      group_id: Optional[str] = None,
                                      keyword: Optional[str] = None,
                                      limit: Optional[int] = None,
                                      offset: Optional[int] = None):
    stat = db.query(Knowledge) \
        .filter(Knowledge.workspace_id == workspace_id)

    if keyword is not None:
        stat = stat.filter(Knowledge.name.contains(keyword))
    if group_id is not None:
        stat = stat.filter(Knowledge.group_id == group_id)
    if ids is not None:
        stat = stat.filter(Knowledge.id.in_(ids))
    count = stat.count()
    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    knowledges = stat.all()
    for knowledge in knowledges:
        doc_count = db.query(Document).filter(
            Document.knowledge_id == knowledge.id).count()
        knowledge.documentCount = doc_count
    return (knowledges, count)


async def create(db: Session,
                 user_id: str,
                 knowledge_create: KnowledgeCreate,
                 knowledge_id: Optional[str] = None):
    knowledge = Knowledge(**knowledge_create.model_dump())
    if knowledge_id is not None:
        knowledge.id = knowledge_id
    knowledge.created_by = user_id
    knowledge.updated_by = user_id
    db.add(knowledge)
    db.commit()
    db.refresh(knowledge)
    return knowledge


async def create_knowledge_with_member(
    db: Session,
    user_id: str,
    knowledge_create: KnowledgeCreate,
):
    knowledge_id = str(uuid4())
    knowledge = await create(
        db,
        user_id,
        knowledge_create,
        knowledge_id=knowledge_id)
    return knowledge


async def get(db: Session, knowledge_id: str):
    db_knowledge = db.query(Knowledge).filter(
        Knowledge.id == knowledge_id).first()
    if db_knowledge is None:
        raise NotFound(f"Knowledge({knowledge_id}) not found")
    doc_count = db.query(Document).filter(
        Document.knowledge_id == knowledge_id).count()
    db_knowledge.documentCount = doc_count
    return db_knowledge


async def update(db: Session, user_id: str,
                 knowledge_id: str, knowledge_update: KnowledgeUpdate):
    db_knowledge = await get(db, knowledge_id)

    columns = knowledge_update.model_dump(exclude_unset=True)
    columns[Knowledge.updated_by] = user_id
    db.query(Knowledge).filter(Knowledge.id == knowledge_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_knowledge)
    return db_knowledge


async def delete(db: Session, user_id: str, knowledge_id: str):
    await get(db, knowledge_id)
    db.query(Knowledge).filter(Knowledge.id == knowledge_id).update(
        {Knowledge.deleted_at: datetime.now(),
         Knowledge.updated_by: user_id,
         Knowledge.deleted_by: user_id}
    )
    db.commit()


async def delete_with_check(
    db: Session, user_id: str, knowledge_id: str
):
    knowledge = await get(db, knowledge_id)
    user = await get_by_id(db, user_id)
    collectionId = knowledge.collectionId
    if collectionId:
        await remove_ada_collection(collectionId, user.name)
    await delete(db, user_id, knowledge_id)


async def remove_ada_collection(collectionId, username):
    url = settings.ada_api_base + '/ada/collection/proxy/remove'
    headers = {'uid': username or 'huangkai1'}
    ret = requests.post(
        url, data={'collectionId': collectionId}, headers=headers)
    content = json.loads(ret.content.decode('utf-8'))
    return content['data']


async def get_match_content(config: AgentConfig, messages: List[LLMMessage]):
    knowledge = config.knowledge
    first_message = messages[0]
    last_message = messages[-1]
    if knowledge is not None:
        match_docs = []
        collectionId = knowledge.collectionId
        docIds = []
        if collectionId:
            if knowledge.docs:
                for doc in knowledge.docs:
                    docIds.append(doc.doc_id)
            docId_str = ",".join(str(element) for element in docIds)
            match_docs = await match_ada_document_sgement(collectionId, last_message.content, 5,
                                                          config.modelName, docId_str)
            if match_docs is None:
                match_docs = []
        match_content = "\n".join(str(element['pageContent'])
                                  for element in (match_docs or []))
        if isinstance(first_message.content, str):
            if '{{docs}}' in first_message.content:
                return first_message.content.replace('{{docs}}', match_content)
            else:
                return first_message.content + '请结合以下内容进行回答' + match_content
    return first_message.content


async def get_match_content_v2(config: AgentConfig, messages: List[LLMMessage], groupId: str, query: str):
    knowledge = config.knowledgeConfig.knowledge if config.knowledgeConfig else None
    if knowledge is None:
        return messages[0].content
    first_message = messages[0]
    content = ''
    last_message = messages[-1]
    if knowledge is not None and len(knowledge) > 0:
        match_docs = []
        knowledgeIds = [knowledge.knowledgeId for knowledge in knowledge]
        match_docs = await match_document_sgement_v2(knowledgeIds=knowledgeIds,
                                                     query=query,
                                                     knowledgeConfig=config.knowledgeConfig,
                                                     groupId=groupId,
                                                     knowledgeItemIds=[])
        match_docs = match_docs.results
        if match_docs is None:
            match_docs = []
        match_content = "\n".join(str(element.content)
                                  for element in (match_docs or []))
        if isinstance(first_message.content, list):
            content = first_message.content[0]
            if isinstance(content, ChatCompletionContentPartTextParam):
                content = content.text
        if isinstance(first_message.content, str):
            content = first_message.content
        if '{{docs}}' in content:
            return [ChatCompletionContentPartTextParam(
                type='text',
                text=content.replace('{{docs}}', match_content))]
        else:
            return [ChatCompletionContentPartTextParam(
                type='text',
                text=content + '请结合以下内容进行回答' + match_content)]
    return first_message.content
