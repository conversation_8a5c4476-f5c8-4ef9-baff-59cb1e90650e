from typing import List, Optional, Tuple
from sqlalchemy.orm import Session

from ..misc.errors import NotFound
from datetime import datetime
from ..models.workflow_datatype import WorkflowDataType
from ..schema.workflow_datatype import WorkflowDataTypeCreate, WorkflowDataTypeUpdate


async def get(db: Session, workflow_datatype_id: str):
    db_workflow_datatype = db.query(WorkflowDataType) \
        .filter(WorkflowDataType.id == workflow_datatype_id).first()
    if db_workflow_datatype is None:
        raise NotFound(f"WorkflowDataType({workflow_datatype_id}) not found")
    return db_workflow_datatype


async def delete(db: Session,
                 user_id: str,
                 workflow_datatype_id: str):
    _ = await get(db, str(workflow_datatype_id))
    db.query(WorkflowDataType).filter(WorkflowDataType.id == workflow_datatype_id).update(
        {WorkflowDataType.deleted_at: datetime.now(),
         WorkflowDataType.deleted_by: user_id}
    )
    db.commit()


async def update(db: Session,
                 user_id: str,
                 workflow_datatype_id: str,
                 workflow_datatype_update: WorkflowDataTypeUpdate):
    db_workflow_datatype = await get(db, workflow_datatype_id)
    # update workflow datatype
    columns = workflow_datatype_update.model_dump(exclude_unset=True)
    columns[WorkflowDataType.updated_by] = user_id
    db.query(WorkflowDataType).filter(WorkflowDataType.id == workflow_datatype_id).update(columns)  # type: ignore # noqa
    db.commit()
    db.refresh(db_workflow_datatype)
    return db_workflow_datatype


async def list(db: Session,
               offset: Optional[int] = None,
               limit: Optional[int] = None) \
        -> Tuple[List[WorkflowDataType], int]:
    stat = db.query(WorkflowDataType)
    count = stat.count()
    if offset is not None:
        stat = stat.offset(offset)
    if limit is not None:
        stat = stat.limit(limit)

    return (stat.all(), count)


async def create(db: Session,
                 workflow_datatype_create: WorkflowDataTypeCreate,
                 user_id: str):
    workflow_datatype = WorkflowDataType(**workflow_datatype_create.model_dump())
    workflow_datatype.created_by = user_id
    workflow_datatype.updated_by = user_id
    db.add(workflow_datatype)
    db.commit()
    db.refresh(workflow_datatype)
    return workflow_datatype
