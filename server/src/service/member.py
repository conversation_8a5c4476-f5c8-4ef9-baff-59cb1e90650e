from typing import List, Optional, Tuple
from sqlalchemy import or_
from sqlalchemy.orm import Session

from ..misc.http_db import get_http_db

# from .chat import get_conversation as get_conversation_service
from . import chat
from .token import get as get_token_service
from ..config import settings

from ..models.account import Account
from ..schema.env_variable import EnvVariableScopeType

from ..misc.errors import NotFound
from ..schema.member import MemberCreate, \
    MemberType, Role
from ..schema.common import ResourceType, Scope
from ..models.member import Member


async def create(db: Session,
                 resource_type: ResourceType,
                 user_id: str,
                 resource_id: str,
                 member_create: MemberCreate):
    member = Member(**member_create.model_dump(by_alias=False))
    member.resource_type = resource_type.value  # type: ignore
    member.resource_id = resource_id  # type: ignore
    member.granted_by = user_id
    db.add(member)
    db.commit()
    db.refresh(member)
    return member


async def update(db: Session,
                 resource_type: ResourceType,
                 resource_id: str,
                 member_create: MemberCreate):
    member = await get_direct_member_of_resource(
        db,
        member_type=member_create.member_type,
        member_id=member_create.member_id,
        resource_type=resource_type.value,
        resource_id=resource_id,
    )
    if member is None:
        raise NotFound("Member record not found")

    member.role = member_create.role  # type: ignore
    db.query(Member).filter(Member.id == member.id).update({
        Member.role: member_create.role,
    })
    db.commit()
    db.refresh(member)
    return member


async def delete(
        db: Session,
        resource_type: str,
        resource_id: str,
        member_type: str,
        member_id: str):
    member = await get_direct_member_of_resource(
        db,
        member_type,
        member_id,
        resource_type,
        resource_id
    )
    if member is not None:
        db.delete(member)
        db.commit()


async def get_direct_member_of_resource(
    db: Session,
    member_type: str,
    member_id: str,
    resource_type: str,
    resource_id: str
):
    return db.query(Member).filter(
        Member.member_type == member_type,
        Member.member_id == member_id,
        Member.resource_type == resource_type,
        Member.resource_id == resource_id,
    ).first()


async def get_direct_member_of_resource_by_user(db: Session, user_id: str,
                                                resource_type: str,
                                                resource_id: str):
    return await get_direct_member_of_resource(
        db,
        MemberType.USER.value,
        user_id,
        resource_type,
        resource_id)


async def get_member_of_resource(
        db: Session,
        member_type: str,
        member_id: str,
        resource_type: str,
        resource_id: str):
    if resource_type == ResourceType.Token.value:
        token = await get_token_service(
            db=get_http_db(),
            id=resource_id)
        resource_type = str(token.resource_type)
        resource_id = str(token.resource_id)

    if resource_type == ResourceType.Conversation.value:
        conversation = await chat.get_conversation(db, resource_id)
        resource_type = ResourceType.APP.value
        resource_id = str(conversation.app_id)

    if resource_type == ResourceType.WORKFLOW_ENGINES.value:
        for engine in settings.workflow_config.engines:
            if engine.id == resource_id:
                resource_type = ResourceType.WORKSPACE.value
                resource_id = engine.workspaceId

    if resource_type == ResourceType.COMPONENTS.value:
        from .component import get as get_component_service
        resource = await get_component_service(db, resource_id)
        if resource is not None:
            if resource.created_by == member_id:
                return Member(role=Role.ADMIN.value)
            elif resource.scope == Scope.GROUP.value:
                resource_type = ResourceType.GROUP.value
                resource_id = str(resource.group_id)
            else:
                resource_type = ResourceType.WORKSPACE.value
                resource_id = str(resource.workspace_id)

    if resource_type == ResourceType.ENV_VARIABLES.value:
        from .env_variable import get as get_env_variable_service
        env_variable = await get_env_variable_service(db, resource_id)
        if env_variable is not None:
            if env_variable.scope_type == EnvVariableScopeType.COMPONENT.value:
                from .component import get as get_component_service
                resource = await get_component_service(db, env_variable.component_id)
                if resource is not None:
                    resource_type = ResourceType.WORKSPACE.value
                    resource_id = str(resource.workspace_id)
            elif env_variable.scope_type == EnvVariableScopeType.WORKSPACE.value:
                resource_type = ResourceType.WORKSPACE.value
                resource_id = str(env_variable.scope_id)
            elif env_variable.scope_type == EnvVariableScopeType.GROUP.value:
                resource_type = ResourceType.GROUP.value
                resource_id = str(env_variable.scope_id)
            elif env_variable.scope_type == EnvVariableScopeType.APP.value:
                resource_type = ResourceType.APP.value
                resource_id = str(env_variable.scope_id)

    if resource_type == ResourceType.APP_CONFIG.value:
        from .app import get_config as get_app_config_service
        app_config = await get_app_config_service(db, resource_id)
        if app_config is not None:
            resource_type = ResourceType.APP.value
            resource_id = app_config.app_id

    if resource_type == ResourceType.ModelProvider.value:
        from .model import get_provider_binding
        binding = await get_provider_binding(db, resource_id)
        if binding.group_id is None:
            resource_type = ResourceType.WORKSPACE.value
            resource_id = str(binding.workspace_id)
        else:
            resource_type = ResourceType.GROUP.value
            resource_id = str(binding.group_id)

    if resource_type == ResourceType.APP.value:
        from .app import get as get_app_service
        resource = await get_app_service(db, resource_id)
        if resource is None:
            raise NotFound(f"App({resource_id}) not found")
        member = await get_direct_member_of_resource(
            db,
            member_id=member_id,
            member_type=member_type,
            resource_type=ResourceType.APP.value,
            resource_id=resource_id)
        if member is not None:
            return member
        resource_type = ResourceType.GROUP.value
        resource_id = str(resource.group_id)

    if resource_type == ResourceType.GROUP.value:
        from .group import get as get_group_service
        resource = await get_group_service(db, resource_id)
        if resource is None:
            raise NotFound(f"App({resource_id}) not found")
        member = await get_direct_member_of_resource(
            db,
            member_id=member_id,
            member_type=member_type,
            resource_type=ResourceType.GROUP.value,
            resource_id=resource_id)
        if member is not None:
            return member
        resource_type = ResourceType.WORKSPACE.value
        resource_id = str(resource.workspace_id)

    if resource_type == ResourceType.WORKSPACE.value:
        from .workspace import get as get_workspace_service
        resource = await get_workspace_service(db, resource_id)
        return await get_direct_member_of_resource(
            db,
            member_id=member_id,
            member_type=member_type,
            resource_type=ResourceType.WORKSPACE.value,
            resource_id=resource_id)


async def get_member_of_resource_by_user(db: Session, user_id: str,
                                         resource_type: str,
                                         resource_id: str):
    role = await get_member_of_resource(
        db,
        MemberType.USER.value,
        user_id,
        resource_type,
        resource_id)
    # 针对workspace，如果没有找到权限，则默认是viewer
    if resource_type == 'workspace' and role is None:
        return Member(role=Role.VIEWER.value)
    return role


async def list_user(db: Session,
                    user_id: str,
                    resource_type: Optional[str] = None,
                    resource_id: Optional[str] = None,
                    role: Optional[str] = None,
                    limit: Optional[int] = None,
                    offset: Optional[int] = None) -> Tuple[List[Member], int]:

    return await list(db,
                      member_type=MemberType.USER.value,
                      member_id=user_id,
                      resource_type=resource_type,
                      resource_id=resource_id,
                      role=role,
                      limit=limit,
                      offset=offset)


async def list(db: Session,
               member_type: Optional[str] = None,
               member_id: Optional[str] = None,
               names: Optional[List[str]] = None,
               resource_type: Optional[str] = None,
               resource_id: Optional[str] = None,
               with_detail: Optional[bool] = False,
               role: Optional[str] = None,
               limit: Optional[int] = None,
               offset: Optional[int] = None) -> Tuple[List[Member], int]:
    if with_detail and member_type == MemberType.USER.value:
        stat = db.query(Member, Account).join(
            Account, Account.id == Member.member_id)
    else:
        stat = db.query(Member)

    if resource_type is not None:
        stat = stat.filter(Member.resource_type == resource_type)
    if resource_id is not None:
        stat = stat.filter(Member.resource_id == resource_id)
    if role is not None:
        stat = stat.filter(Member.role == role)
    if member_type is not None:
        stat = stat.filter(Member.member_type == member_type)
    if member_id is not None:
        stat = stat.filter(Member.member_id == member_id)
    if names is not None and member_type == MemberType.USER.value:
        stat = stat.filter(
            or_(*[
                or_(
                    Account.name.like(f"%{name}%"),
                    Account.fullname.like(f"%{name}%")
                ) for name in names
            ])
        )
    count = stat.count()

    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)

    results = stat.all()

    # 处理结果，将Account信息添加到Member对象中
    if with_detail and member_type == MemberType.USER.value:
        members = []
        for member, account in results:
            member.member = account
            members.append(member)
        return (members, count)
    else:
        return (results, count)
