import time
from typing import List, Optional, Tuple
from datetime import datetime

from loguru import logger
from sqlalchemy.orm import Session
from sqlalchemy import select, func, or_

from ..schema.common import ResourceType

from ..models.collection import Collection

from ..models.app import App

from ..lib.llms.get_models import get_all_models

from ..misc.http_db import get_http_db

from ..config import settings

from ..lib.llms.base import Providers

from ..misc.errors import NotFound
from ..models.dialog import Conversation as DBConversation
from ..models.dialog import Message, MessageToolCallResponse
from ..schema.dialog import (ConversationCreate,
                             ConversationUpdate, MessageCreate,
                             MessageToolCallResponseCreate, MessageUpdate)
from . import app
from pydantic import BaseModel, Field
import httpx


class LLMRequestStatus(BaseModel):
    """LLM请求状态信息"""
    success: bool = Field(description="请求是否成功")
    restricted: bool = Field(description="是否受限")
    stream: bool = Field(description="是否为流式请求")
    start_time: int = Field(description="开始时间戳")
    end_time: int = Field(description="结束时间戳")


class LLMRequestApp(BaseModel):
    """LLM请求应用信息"""
    app_id: str = Field(description="应用ID")
    setting_id: Optional[str] = Field(default=None, description="设置ID")
    group_id: str = Field(description="组ID")
    workspace_id: str = Field(description="工作空间ID")
    biz_type: str = Field(description="业务类型")


class LLMRequestModel(BaseModel):
    """LLM请求模型信息"""
    model_name: str = Field(description="模型名称")
    provider_kind: Optional[str] = Field(default=None, description="提供商类型")
    model_alias: Optional[str] = Field(default=None, description="模型别名")
    binding_id: Optional[int] = Field(default=None, description="绑定ID")


class LLMRequestFee(BaseModel):
    """LLM请求费用信息"""
    prompt_tokens: Optional[int] = Field(default=None, description="提示词token数")
    completion_tokens: Optional[int] = Field(
        default=None, description="补全token数")
    total_tokens: Optional[int] = Field(default=None, description="总token数")
    total_fee: Optional[float] = Field(default=None, description="总费用")


class LLMRequestLog(BaseModel):
    """LLM请求日志完整信息"""
    status: LLMRequestStatus
    app: LLMRequestApp
    model: LLMRequestModel
    fee: LLMRequestFee


async def get_app_by_id(db: Optional[Session] = None,
                        app_id: Optional[str] = None,
                        is_basic: Optional[bool] = None,
                        workspace_id: Optional[str] = None,
                        user_id: Optional[str] = None,
                        ):
    http_db = get_http_db('tb_app')
    stat = http_db.query(App)
    if app_id is not None:
        stat = stat.filter(App.id == app_id)
    if is_basic is not None:
        stat = stat.filter(App.is_basic == is_basic)
    if workspace_id is not None:
        stat = stat.filter(App.workspace_id == workspace_id)

    db_app = stat.first()
    # if not hasattr(db_app, 'config') or db_app.config is None and hasattr(db_app, 'set_value'):
    #     config = await get_config(db, db_app.app_config_id)
    #     db_app.set_value('config', config.config)
    if db_app is None:
        raise NotFound(f"App({app_id}) not found")
    if user_id is not None and db is not None:
        starApp = db.query(Collection).filter(Collection.user_id == user_id, Collection.resource_id ==
                                              app_id, Collection.resource_type == ResourceType.APP.value).first()
        if starApp is not None:
            db_app.starred = True
    return db_app


def find_provider_model(
    model_name: Optional[str] = None,
    kind: Optional[str] = None
):
    models = get_all_models()
    if not kind and not model_name:
        return None
    # 找到匹配的模型
    for model in models:
        if model.name == model_name:
            if not kind or model.providerKind == kind:
                return model
            else:
                continue
    return None


# 迁移测试完成
async def create_conversation(
    db: Session,
    user_id: str,
    conversation_create: ConversationCreate,
):
    http_db = get_http_db('tb_conversation')
    if settings.disable_mysql:
        return DBConversation(id="no_store")
    conv = DBConversation(**conversation_create.model_dump(exclude_none=True))
    conv.name = "New Chat"  # type: ignore
    conv.created_by = user_id  # type: ignore # noqa
    conv.updated_by = user_id  # type: ignore # noqa

    http_db.add(conv)
    http_db.commit()
    http_db.refresh(conv)
    return conv


# 迁移测试完成
async def update_conversation(
    db: Session,
    user_id: str,
    conversation_id: str,
    conversation_update: ConversationUpdate,
):
    http_db = get_http_db('tb_conversation')
    if conversation_id == 'no_store':
        return DBConversation(id="no_store")
    conversation = await get_conversation(db, conversation_id)
    cols = conversation_update.model_dump(exclude_unset=True)  # type: ignore
    cols[DBConversation.updated_by] = user_id
    http_db.query(DBConversation).filter(DBConversation.id == conversation_id).update(
        cols  # type: ignore
    )
    http_db.commit()
    http_db.refresh(conversation)
    return conversation


# 迁移测试完成
async def get_conversation(
    db: Session,
    conversation_id: str,
    user_id: Optional[str] = None,
    app_id: Optional[str] = None,
):
    http_db = get_http_db('tb_conversation')
    stat = http_db.query(DBConversation) \
        .filter(DBConversation.id == conversation_id)

    if user_id is not None:
        stat = stat.filter(DBConversation.created_by == user_id)

    if app_id is not None:
        stat = stat.filter(DBConversation.app_id == app_id)

    conv = stat.first()

    if conv is None:
        raise NotFound("Conversation not found")
    return conv


# 迁移测试完成
async def list_conversation(
    db: Session,
    app_id: str,
    user_id: str,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
) -> Tuple[List[DBConversation], int]:
    await app.get(db, app_id)
    http_db = get_http_db('tb_conversation')
    stat = http_db.query(DBConversation) \
        .filter(DBConversation.app_id == app_id) \
        .filter(DBConversation.created_by == user_id) \
        .order_by(
            DBConversation.created_at.desc())
    count = stat.count()
    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    return (stat.all(), count)


# 迁移测试完成
async def delete_conversation(
    db: Session,
    conversation_id: str,
    user_id: Optional[str] = None,
    app_id: Optional[str] = None,
):
    http_db = get_http_db('tb_conversation')
    conversation = await get_conversation(
        db,
        conversation_id,
        user_id=user_id,
        app_id=app_id)
    http_db.delete(conversation)
    http_db.commit()
    return conversation


# 迁移测试完成
async def create_message(
    db: Session,
    user_id: str,
    message_create: MessageCreate,
    biz_type: Optional[str] = None,
):
    http_db = get_http_db('tb_message')
    # 如果发现conversation_id为test，则不进行任何操作
    if message_create.conversation_id == 'no_store':
        return Message(id='no_store')
    message = Message(**message_create.model_dump())
    if message_create.app_id is None:
        conversation = await get_conversation(db, message_create.conversation_id)
        message.app_id = conversation.app_id
    app = await get_app_by_id(db, str(message.app_id))
    message.created_by = user_id
    model = find_provider_model(
        model_name=message_create.model_name,
        kind=message_create.kind
    )
    fee = 0
    if model and model.fee and message_create.prompt_tokens:
        fee = model.fee['input'] * message_create.prompt_tokens + \
            model.fee['output'] * message_create.response_tokens
        fee = fee / 1000000
        try:
            await llm_request_log(LLMRequestLog(
                status=LLMRequestStatus(
                    success=True,
                    restricted=False,
                    stream=False,
                    start_time=int(
                        time.time() - message_create.time_comsumption_in_ms / 1000),
                    end_time=int(time.time()),
                ),
                app=LLMRequestApp(
                    app_id=app.id,
                    group_id=str(app.group_id),
                    workspace_id=str(app.workspace_id),
                    biz_type=biz_type or 'agent-completion',
                ),
                model=LLMRequestModel(
                    model_name=str(message.model_name),
                    provider_kind=str(message.kind),
                ),
                fee=LLMRequestFee(
                    prompt_tokens=message_create.prompt_tokens,
                    completion_tokens=message_create.response_tokens,
                    total_tokens=message_create.total_tokens,
                    total_fee=fee
                )))
        except Exception as e:
            logger.error("记录LLM请求日志失败: {error}", error=str(e), uid='uid')
    message.fee = fee

    http_db.add(message)
    http_db.commit()
    http_db.refresh(message)
    return message


# 迁移测试完成
async def list_message(
    db: Session,
    conversation_id: str,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
    ascending: bool = True,
) -> Tuple[List[Message], int]:
    http_db = get_http_db('tb_message')
    await get_conversation(db, conversation_id)

    stat = http_db.query(Message). \
        filter(Message.conversation_id == conversation_id)
    count = stat.count()

    if ascending:
        stat = stat.order_by(Message.created_at.asc())
    else:
        stat = stat.order_by(Message.created_at.desc())

    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    return (stat.all(), count)


# 迁移测试完成
async def list_message_by_app(
    db: Session,
    app_id: str,
    limit: int = 20,
    offset: int = 0,
    ascending: bool = True,
    user_id: Optional[str] = None,
    start: Optional[datetime] = None,
    end: Optional[datetime] = None,
    conversation_id: Optional[str] = None,
) -> Tuple[List[Message], int]:
    http_db = get_http_db('tb_message')

    stat = http_db.query(Message). \
        filter(Message.app_id == app_id)
    if user_id:
        stat = stat.filter(Message.created_by == user_id)
    if conversation_id:
        stat = stat.filter(Message.conversation_id == conversation_id)
    if start:
        stat = stat.filter(Message.created_at >= start)
    if end:
        stat = stat.filter(Message.created_at <= end)

    if ascending:
        stat = stat.order_by(Message.created_at.asc())
    else:
        stat = stat.order_by(Message.created_at.desc())

    count = stat.count()
    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    return (stat.all(), count)


async def get_message(
    db: Session,
    message_id: str,
):
    http_db = get_http_db('tb_message')
    message = http_db.query(Message).filter(Message.id == message_id).first()
    if message is None:
        raise NotFound("Message not found")
    return message


# 迁移测试完成
async def update_message(
    db: Session,
    message_id: str,
    message_update: MessageUpdate,
    biz_type: Optional[str] = None,
):
    http_db = get_http_db('tb_message')
    if message_id == 'no_store':
        return Message(id='no_store')
    message = await get_message(db, message_id)
    app = await get_app_by_id(db, message.app_id)
    model = find_provider_model(
        model_name=message.model_name,
        kind=message.kind
    )
    fee = 0
    if model and model.fee and message_update.prompt_tokens:
        fee = model.fee['input'] * message_update.prompt_tokens + \
            model.fee['output'] * message_update.response_tokens
        fee = fee / 1000000
    # print(fee)
    http_db.query(Message).filter(Message.id == message_id).update(
        {**message_update.model_dump(exclude_none=True), 'fee': fee})
    # http_db.query(Message).filter(Message.id == message_id).update(
    #     values={**message_update.model_dump(exclude_none=True), 'fee': fee}
    # )
    await llm_request_log(LLMRequestLog(
        status=LLMRequestStatus(
            success=True,
            restricted=False,
            stream=False,
            start_time=int(datetime.strptime(
                str(message.created_at), "%Y-%m-%d %H:%M:%S").timestamp()),
            end_time=int(time.time()),
        ),
        app=LLMRequestApp(
            app_id=str(app.id),
            group_id=str(app.group_id),
            workspace_id=str(app.workspace_id),
            biz_type=biz_type or 'agent-completion',
        ),
        model=LLMRequestModel(
            model_name=str(message.model_name),
            provider_kind=str(message.kind),
        ),
        fee=LLMRequestFee(
            prompt_tokens=message_update.prompt_tokens,
            completion_tokens=message_update.response_tokens,
            total_tokens=message_update.total_tokens,
            total_fee=fee,
        )
    ))
    http_db.commit()
    http_db.refresh(message)
    return message


async def get_or_create_db_conversation(
    db: Session,
    user_id: str,
    conversation_create: ConversationCreate,
    conversation_id: Optional[str] = None,
):
    conversation: DBConversation
    if conversation_id is not None:
        conversation = await get_conversation(db, conversation_id)
    else:
        conversation = await create_conversation(
            db,
            user_id=user_id,
            conversation_create=conversation_create,
        )
    return conversation


async def create_message_tool_call_response(
    db: Session,
    message_tool_call_response_create: MessageToolCallResponseCreate,
):
    http_db = get_http_db('tb_message_tool_call_response')
    message_tool_call_response = MessageToolCallResponse(
        **message_tool_call_response_create.model_dump())

    http_db.add(message_tool_call_response)
    http_db.commit()
    http_db.refresh(message_tool_call_response)
    return message_tool_call_response


async def get_message_tool_call_response(
    db: Session,
    message_tool_call_response_id: Optional[str],
    tool_call_id: Optional[str],
    message_id: Optional[str],
):
    http_db = get_http_db('tb_message_tool_call_response')
    stat = http_db.query(MessageToolCallResponse)

    if message_tool_call_response_id is not None:
        stat = stat.filter(
            MessageToolCallResponse.id == message_tool_call_response_id)

    if tool_call_id is not None:
        stat = stat.filter(
            MessageToolCallResponse.tool_call_id == tool_call_id)

    if message_id is not None:
        stat = stat.filter(
            MessageToolCallResponse.message_id == message_id)

    message_tool_call_response = stat.first()

    if message_tool_call_response is None:
        raise NotFound("Message Tool Call Response not found")
    return stat


async def list_message_tool_call_response(
    db: Session,
    message_id: Optional[str],
    ascending: Optional[bool] = None,
    offset: Optional[int] = None,
    limit: Optional[int] = None,
):
    http_db = get_http_db('tb_message_tool_call_response')
    stat = http_db.query(MessageToolCallResponse)

    if message_id is not None:
        stat = stat.filter(
            MessageToolCallResponse.message_id == message_id)

    count = stat.count()

    if ascending is not None:
        if ascending:
            stat = stat.order_by(MessageToolCallResponse.created_at.asc())
        else:
            stat = stat.order_by(MessageToolCallResponse.created_at.desc())

    if limit is not None:
        stat = stat.limit(limit)
    if offset is not None:
        stat = stat.offset(offset)
    return stat.all(), count


async def create_message_tool_call_responses(
    db: Session,
    message_id: str,
    responses: List[MessageToolCallResponseCreate],
):
    http_db = get_http_db('tb_message_tool_call_response')
    db_responses = [
        MessageToolCallResponse(
            **response.model_dump(exclude={'message_id'}),
            message_id=message_id,
        )
        for response in responses
    ]
    http_db.add_all(db_responses)
    http_db.commit()

    db_responses, _ = await list_message_tool_call_response(
        http_db, message_id=message_id)
    response_ids = [response.id for response in db_responses]

    await update_message(
        db,
        message_id,
        MessageUpdate(
            tool_call_response_ids=response_ids,
        ),
    )


async def llm_request_log_error(restricted: bool, biz_type: str, app_id: str, model_name: str) -> None:
    """记录LLM请求的错误日志信息"""
    app = await get_app_by_id(app_id=app_id)
    log_data = LLMRequestLog(
        status=LLMRequestStatus(
            success=False,
            restricted=restricted,
            stream=False,
            start_time=int(time.time()),
            end_time=int(time.time()),
        ),
        app=LLMRequestApp(
            app_id=str(app_id),
            group_id=str(app.group_id),
            workspace_id=str(app.workspace_id),
            biz_type=biz_type,
        ),
        model=LLMRequestModel(
            model_name=model_name,
        ),
        fee=LLMRequestFee(
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            total_fee=0,
        )
    )
    await llm_request_log(log_data)


async def llm_request_log(log_data: LLMRequestLog) -> None:
    """记录LLM请求的日志信息

    Args:
        log_data (LLMRequestLog): 完整的日志信息对象
    """
    model = find_provider_model(model_name=log_data.model.model_name)
    try:
        # 构建日志内容
        log_dict = {
            # 状态信息
            "success": log_data.status.success,
            "restricted": log_data.status.restricted,
            "stream": log_data.status.stream,
            "startTime": log_data.status.start_time,
            "endTime": log_data.status.end_time,

            # 应用信息
            "appIdStr": log_data.app.app_id,
            "settingIdStr": log_data.app.setting_id,
            "groupId": log_data.app.group_id,
            "workSpaceId": log_data.app.workspace_id,
            "bizType": log_data.app.biz_type,

            # 模型信息
            "modelName": log_data.model.model_name,
            "providerKind": log_data.model.provider_kind or str(model.providerKind if model else None),
            "modelAlias": log_data.model.model_alias or str(model.alias if model else None),
            "bindingId": log_data.model.binding_id or 0,

            # 费用信息
            "promptTokens": log_data.fee.prompt_tokens,
            "completionTokens": log_data.fee.completion_tokens,
            "totalTokens": log_data.fee.total_tokens,
            "totalFee": float(log_data.fee.total_fee) if log_data.fee.total_fee is not None else None,
        }
        logger.info("LLM请求日志: {log_dict} {url}", log_dict=log_dict,
                    url=f"{settings.db_service_url}/langbase/inner/common/llmLog/model", uid="uid")

        # 调用内部服务记录日志
        async with httpx.AsyncClient() as client:
            url = f"{settings.db_service_url}/langbase/inner/common/llmLog/model"
            response = await client.post(url, json=log_dict, timeout=1000)
            response.raise_for_status()

    except Exception as e:
        logger.error("记录LLM请求日志失败: {error}", error=str(e), uid='uid')
