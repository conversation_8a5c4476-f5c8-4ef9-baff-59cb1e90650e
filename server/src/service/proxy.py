import httpx
from typing import Optional
from aiohttp import ClientTimeout

from pytest import param

from ..misc.log import logger_error
from ..misc.http_client import HTTPClient
from ..config import settings
from ..misc.errors import InternalServerError
from ..schema.proxy import ProxyCIOPoolResourceContentRequest, \
    ProxyPythonScriptRequest, \
    ProxyScriptRequest, \
    ProxyPropertySaveRequest, \
    ProxySettingPublishSaveRequest

http_client = HTTPClient(headers={"Content-Type": "application/json"})


async def list_airship_rep2_algo():
    async with httpx.AsyncClient() as client:
        try:
            resp = await client.post(url=f"{settings.workflow_config.airship.endpoint}/api/rep2/algo/list",
                                     json={
                                         "page": {
                                             "cursor": 0,
                                             "size": 1000,
                                             "from": 0,
                                             "to": 1
                                         }
                                     },
                                     headers={
                                         "Content-Type": "application/json"
                                     })
        except Exception as e:
            logger_error(f"list airship rep2 algo exception: {e}")
            raise InternalServerError(f"list airship rep2 algo exception: {e}")

        if not resp.is_success:
            logger_error(f"list airship rep2 algo failed: {resp.text}")
            raise InternalServerError(
                f"list airship rep2 algo failed: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"list airship rep2 algo error: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(
                f"list airship rep2 algo error: {resp.json()['code']} : {resp.json()['message']}")

        return resp.json()['data']['records'], resp.json()['data']['totalRecord']


async def get_aisong_mv_list():
    async with httpx.AsyncClient() as client:
        try:
            resp = await client.get(
                url=f"{settings.workflow_config.aisong.endpoint}/api/vip-rights/aisong/mv/image2video/template/list")
        except Exception as e:
            logger_error(f"get ai-mv template exception: {e}")
            raise InternalServerError(
                f"get ai-mv template exception: {e}")

        if not resp.is_success:
            logger_error(f"get ai-mv template failed: {resp.text}")
            raise InternalServerError(
                f"get ai-mv template failed: {resp.text}")

        res = resp.json()
        if res['code'] != 200:
            logger_error(
                f"get ai-mv template error: {res['code']} : {res['message']}")
            raise InternalServerError(f"get ai-mv template error: "
                                      f"{res['code']} : {res['message']}")

        return res['data']


async def get_airship_rep2_algo(algo_id: int):
    async with httpx.AsyncClient() as client:
        try:
            resp = await client.get(
                url=f"{settings.workflow_config.airship.endpoint}/api/rep2/algo/detail?id={algo_id}")
        except Exception as e:
            logger_error(f"get airship rep2 algo {algo_id} exception: {e}")
            raise InternalServerError(
                f"get airship rep2 algo {id} exception: {e}")

        if not resp.is_success:
            logger_error(f"list airship rep2 algo {id} failed: {resp.text}")
            raise InternalServerError(
                f"list airship rep2 algo {id} failed: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"list airship rep2 algo {id} error: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"list airship rep2 algo {id} error: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")

        return resp.json()['data']


async def get_resource_type(user_name: str):
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/business/resourcetype/langbase/get?"
    params = {}
    if user_name is not None:
        params['userName'] = user_name

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url=url, params=params)
        except Exception as e:
            logger_error(f"CIO list resource types exception: {e}")
            raise InternalServerError(
                f"CIO list resource types exception: {e}")

        if not response.is_success:
            logger_error(f"CIO list resource types failed: {response.text}")
            raise InternalServerError(
                f"CIO list resource types failed: {response.text}")

        if response.json()['code'] != 200:
            logger_error(
                f"CIO list resource types failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"CIO list resource types failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']


async def get_pool_resource_detail(resource_type: str, pool_code: str, resource_id: str):
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/resource/langbase/detail?"
    params = {}
    if resource_type is not None:
        params['resourceType'] = resource_type
    if pool_code is not None:
        params['poolCode'] = pool_code
    if resource_id is not None:
        params['resourceId'] = resource_id
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url=url, params=params)
        except Exception as e:
            logger_error(f"CIO get pool resource detail failed: {e}")
            raise InternalServerError(f"CIO get pool resource detail: {e}")

        if not response.is_success:
            logger_error(
                f"CIO get pool resource detail failed: {response.text}")
            raise InternalServerError(
                f"CIO get pool resource detail: {response.text}")

        if response.json()['code'] != 200:
            logger_error(
                f"CIO get pool resource detail failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"CIO get pool resource detail failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']


async def list_content_pool(search_field: str, keyword: str, pool_code: str,
                            resource_type: str, business: str, creator: str, pool_type: str,
                            order_by: str, limit: int, offset: int, desc: bool):
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/list?"
    params = {}
    if search_field is not None:
        params['searchField'] = search_field
    if keyword is not None:
        params['keyword'] = keyword
    if pool_code is not None:
        params['poolCode'] = pool_code
    if resource_type is not None:
        params['resourceType'] = resource_type
    if business is not None:
        params['business'] = business
    if creator is not None:
        params['creator'] = creator
    if pool_type is not None:
        params['poolType'] = pool_type
    if order_by is not None:
        params['orderBy'] = order_by
    if limit is not None:
        params['limit'] = limit
    if offset is not None:
        params['offset'] = offset
    if desc is not None:
        params['desc'] = desc

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url=url, params=params)
        except Exception as e:
            logger_error(f"CIO list content pool exception: {e}")
            raise InternalServerError(f"CIO list content pool exception: {e}")

        if not response.is_success:
            logger_error(f"CIO list content pool failed: {response.text}")
            raise InternalServerError(
                f"CIO list content pool failed: {response.text}")

        if response.json()['code'] != 200:
            logger_error(
                f"CIO list content pool failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"CIO list content pool failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")
        if response.json()['data']['count'] == 0:
            return [], 0

        return response.json()['data']['list'], response.json()['data']['count']


async def get_pool_config(business: str, resource_type: str, pool_code: str,
                          config_types: str, merge: bool, role_permission: bool) -> dict:
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/config/get?"
    params = {}
    if business is not None:
        params['business'] = business
    if resource_type is not None:
        params['resourceType'] = resource_type
    if pool_code is not None:
        params['poolCode'] = pool_code
    if config_types is not None:
        params['configTypes'] = config_types
    if merge is not None:
        params['merge'] = merge
    if role_permission is not None:
        params['rolePermission'] = role_permission

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url=url, params=params)
        except Exception as e:
            logger_error(f"CIO get pool config exception: {e}")
            raise InternalServerError(f"CIO get pool config exception: {e}")

        if not response.is_success:
            logger_error(
                f"CIO get pool config failed: code({response.status_code}) message({response.text})")
            raise InternalServerError(f"CIO get pool config "
                                      f"failed: code({response.status_code}) message({response.text})")

        if response.json()['code'] != 200:
            logger_error(
                f"CIO get pool config failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"CIO get pool config failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']


async def pool_resource_content_detail(cio_request: ProxyCIOPoolResourceContentRequest) -> dict:
    url = f"{settings.workflow_config.cio.endpoint}/api/content/pool/resource/content/detail"
    async with httpx.AsyncClient(headers={
        'Content-Type': 'application/json',
    }) as client:
        try:
            data = {
                'resourceType': cio_request.resource_type,
                'poolCode': cio_request.pool_code,
                'resourceId': cio_request.resource_id,
                'business': cio_request.business,
                'operator': cio_request.operator,
                'queryCode': cio_request.query_code,
            }
            response = await client.post(url=url, json=data)
        except Exception as e:
            logger_error(
                f"CIO get pool resource content detail exception: {e}, request json: {data}")
            raise InternalServerError(
                f"CIO get pool resource content detail exception: {e}")

        if not response.is_success:
            logger_error(
                f"CIO get pool resource content detail failed, request json: {data}: code({response.status_code}) message({response.text})")
            raise InternalServerError(f"CIO get pool resource content detail failed: "
                                      f"code({response.status_code}) message({response.text})")
        if response.json()['code'] != 200:
            logger_error(
                f"CIO get pool resource content detail failed, request json: {data}, return: code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"CIO get pool resource content detail failed, return: "
                                      f"code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']


async def pool_content_scene_field(business: str,
                                   resource_type: str,
                                   pool_code: str,
                                   scene: str) -> dict:
    url = f"{settings.workflow_config.cio.endpoint}/api/backend/content/scene/langbase/field?"
    params = {}
    if business is not None:
        params['business'] = business
    if resource_type is not None:
        params['resourceType'] = resource_type
    if pool_code is not None:
        params['poolCode'] = pool_code
    if scene is not None:
        params['scene'] = scene

    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url=url, params=params)
        except Exception as e:
            logger_error(f"CIO pool content scene field exception: {e}")
            raise InternalServerError(
                f"CIO pool content scene field exception: {e}")

        if not response.is_success:
            logger_error(
                f"CIO pool content scene field failed: code({response.status_code}) message({response.text})")
            raise InternalServerError(f"CIO pool content scene field "
                                      f"failed: code({response.status_code}) message({response.text})")
        if response.json()['code'] != 200:
            logger_error(
                f"CIO pool content scene field failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"CIO pool content scene field failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")

        return response.json()['data']


async def exec_python(request: ProxyPythonScriptRequest) -> dict:
    url = f"{settings.workflow_config.langbase_proxy.endpoint}/api/v1/proxy/script/python"
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url=url, json=request)
        except Exception as e:
            logger_error(f"exec python exception: {e}")
            raise InternalServerError(f"exec python exception: {e}")

        if not response.is_success:
            logger_error(
                f"exec python failed: code({response.status_code}) message({response.text})")
            raise InternalServerError(
                f"exec python failed: code({response.status_code}) message({response.text})")
        if response.json()['code'] != 200:
            logger_error(
                f"exec python failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"exec python failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")
        return response.json()


async def exec_script(request: ProxyScriptRequest) -> dict:
    url = f"{settings.workflow_config.langbase_proxy.endpoint}/api/v1/proxy/script/exec"
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(url=url, json=request)
        except Exception as e:
            logger_error(f"exec script exception: {e}")
            raise InternalServerError(f"exec script exception: {e}")

        if not response.is_success:
            logger_error(
                f"exec script failed: code({response.status_code}) message({response.text})")
            raise InternalServerError(
                f"exec script failed: code({response.status_code}) message({response.text})")
        if response.json()['code'] != 200:
            logger_error(
                f"exec script failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"exec script failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")
        return response.json()


async def aio_list_atomic(limit: int, offset: int):
    url = f"{settings.workflow_config.engines[0].endpoint}/api/v1/workflowengine/aio/taskdefs/get?"
    params = {}
    if limit is not None:
        params['limit'] = limit
    if offset is not None:
        params['offset'] = offset

    headers = {
        "Content-Type": "application/json",
        "LANGBASE-FLOW-TOKEN": settings.workflow_config.engines[0].token,
    }
    async with httpx.AsyncClient(headers=headers) as client:
        try:
            response = await client.get(url=url, params=params)
        except Exception as e:
            logger_error(f"AIO list atomic exception: {e}")
            raise InternalServerError(f"AIO list atomic exception: {e}")

        if not response.is_success:
            logger_error(
                f"AIO list atomic failed: code({response.status_code}) message({response.text})")
            raise InternalServerError(
                f"AIO list atomic failed: code({response.status_code}) message({response.text})")

        if response.json()['code'] != 200:
            logger_error(
                f"AIO list atomic failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"AIO list atomic failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")
        if response.json()['data']['count'] == 0:
            return [], 0

        return response.json()['data']['list'], response.json()['data']['count']


async def get_skyeye_platform_list(limit: int, offset: int):
    async with httpx.AsyncClient() as client:
        params = {}
        if limit is not None:
            params['limit'] = limit
        if offset is not None:
            params['offset'] = offset
        params['status'] = 'ACTIVE'
        try:
            resp = await client.get(
                url=f"{settings.workflow_config.skyeye.endpoint}/api/skyeye/platform/platform/list",
                params=params)
        except Exception as e:
            logger_error(f"get skyeye list error exception: {e}")
            raise InternalServerError(f"get skyeye list error exception: {e}")

        if not resp.is_success:
            logger_error(f"get skyeye list error {resp.text}")
            raise InternalServerError(f"get skyeye list error: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"get skyeye list error: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"get skyeye list error: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")

        # return resp.json()['code'], resp.json()['data']
        return resp.json()


async def get_skyeye_atomic_list(limit: int, offset: int, name: Optional[str], platformName: Optional[str], tag: Optional[str]):
    async with httpx.AsyncClient() as client:
        params = {}
        if limit is not None:
            params['limit'] = limit
        if offset is not None:
            params['offset'] = offset
        if name is not None:
            params['name'] = name
        if tag is not None:
            params['tag'] = tag
        if platformName is not None:
            params['platformName'] = platformName
        params['status'] = 'ACTIVE'

        try:
            resp = await client.get(
                url=f"{settings.workflow_config.skyeye.endpoint}/api/skyeye/platform/atomic/service/list",
                params=params)
        except Exception as e:
            logger_error(f"get skyeye atomic error exception: {e}")
            raise InternalServerError(
                f"get skyeye atomic error exception: {e}")

        if not resp.is_success:
            logger_error(f"get skyeye atomic error {resp.text}")
            raise InternalServerError(f"get skyeye atomic error: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"get skyeye atomic error: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"get skyeye atomic error: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")

        # return resp.json()['code'], resp.json()['data']
        return resp.json()


async def get_skyeye_detail(atomicServiceId: int):
    async with httpx.AsyncClient() as client:
        params = {}
        if atomicServiceId is not None:
            params['atomicServiceId'] = atomicServiceId
        try:
            resp = await client.get(
                url=f"{settings.workflow_config.skyeye.endpoint}/api/skyeye/platform/atomic/service/detail",
                params=params)
        except Exception as e:
            logger_error(f"get skyeye detail error exception: {e}")
            raise InternalServerError(
                f"get skyeye detail error exception: {e}")

        if not resp.is_success:
            logger_error(f"get skyeye detail error {resp.text}")
            raise InternalServerError(f"get skyeye detail error: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"get skyeye detail error: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"get skyeye detail error: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")

        return resp.json()


async def post_property_save(proxyPropertySave: ProxyPropertySaveRequest, user_id) -> dict:
    url = f"{settings.db_service_url}/langbase/inner/property/saveOrUpdate"
    async with httpx.AsyncClient() as client:
        try:
            params: dict = {
                **proxyPropertySave,
                'operator': user_id,
            }
            response = await client.post(url=url, json=params)
        except Exception as e:
            logger_error(
                f"langbase/inner/property/saveOrUpdate exception: {e}")
            raise InternalServerError(
                f"langbase/inner/property/saveOrUpdate exception: {e}")

        if not response.is_success:
            logger_error(
                f"langbase/inner/property/saveOrUpdate failed: code({response.status_code}) message({response.text})")
            raise InternalServerError(
                f"langbase/inner/property/saveOrUpdate failed: code({response.status_code}) message({response.text})")
        if response.json()['code'] != 200:
            logger_error(
                f"langbase/inner/property/saveOrUpdate failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"langbase/inner/property/saveOrUpdate failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")
        return response.json()


async def post_setting_publish(proxySettingPublishSave: ProxySettingPublishSaveRequest, user_id) -> dict:
    url = f"{settings.db_service_url}/langbase/inner/setting/publish/save"
    async with httpx.AsyncClient() as client:
        try:
            params: dict = {
                **proxySettingPublishSave,
                'operator': user_id,
            }
            response = await client.post(url=url, json=params)
        except Exception as e:
            logger_error(
                f"/langbase/inner/setting/publish/save exception: {e}")
            raise InternalServerError(
                f"/langbase/inner/setting/publish/save exception: {e}")

        if not response.is_success:
            logger_error(
                f"/langbase/inner/setting/publish/save failed: code({response.status_code}) message({response.text})")
            raise InternalServerError(
                f"/langbase/inner/setting/publish/save failed: code({response.status_code}) message({response.text})")
        if response.json()['code'] != 200:
            logger_error(
                f"/langbase/inner/setting/publish/save failed: return code({response.json()['code']}) message({response.json()['message']})")
            raise InternalServerError(f"/langbase/inner/setting/publish/save failed: "
                                      f"return code({response.json()['code']}) message({response.json()['message']})")
        return response.json()


async def get_system_variables(params: dict):
    async with httpx.AsyncClient() as client:
        try:
            resp = await client.get(
                url=f"{settings.db_service_url}/langbase/inner/system/variables",
                params=params)
        except Exception as e:
            logger_error(
                f"langbase/inner/system/variables error exception: {e}")
            raise InternalServerError(
                f"langbase/inner/system/variables error exception: {e}")

        if not resp.is_success:
            logger_error(f"langbase/inner/system/variables error {resp.text}")
            raise InternalServerError(
                f"langbase/inner/system/variables error: {resp.text}")

        # print(resp.json(), resp.json()['code'], resp.json()['code'] != 200)
        if resp.json()['code'] != 200:
            logger_error(
                f"langbase/inner/system/variables error: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"langbase/inner/system/variables error: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")
        return resp.json()


async def post_property_search(params: dict):
    async with httpx.AsyncClient() as client:
        try:
            resp = await client.post(
                url=f"{settings.db_service_url}/langbase/inner/property/search",
                json=params)
        except Exception as e:
            logger_error(f"langbase/inner/property/search exception: {e}")
            raise InternalServerError(
                f"langbase/inner/property/search exception: {e}")

        if not resp.is_success:
            logger_error(f"langbase/inner/property/search {resp.text}")
            raise InternalServerError(
                f"langbase/inner/property/search: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"langbase/inner/property/search: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"langbase/inner/property/search: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")

        return resp.json()


async def task_days_query_service(params: dict):
    async with httpx.AsyncClient() as client:
        try:
            resp = await client.get(
                url=f"{settings.workflow_config.engines[0].endpoint}/{settings.workflow_url_prefix}/aio/has/task/days/query",
                params=params)
            return resp.json()
        except Exception as e:
            logger_error(
                f"workflowengine/aio/has/task/days/query exception: {e}")
            raise InternalServerError(
                f"workflowengine/aio/has/task/days/query exception: {e}")


async def post_agent_variables(params: dict):
    async with httpx.AsyncClient() as client:
        try:
            resp = await client.post(
                url=f"{settings.db_service_url}/langbase/inner/agent/variables",
                json=params)
        except Exception as e:
            logger_error(f"langbase/inner/agent/variables exception: {e}")
            raise InternalServerError(
                f"langbase/inner/agent/variables exception: {e}")

        if not resp.is_success:
            logger_error(f"langbase/inner/agent/variables {resp.text}")
            raise InternalServerError(
                f"langbase/inner/agent/variables: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"langbase/inner/agent/variables: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"langbase/inner/agent/variables: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")

        return resp.json()


async def post_agent_chat(params: dict):
    async with httpx.AsyncClient() as client:
        try:
            # print(f"{settings.db_service_url}/langbase/inner/agent/chat————", params)
            resp = await client.post(
                url=f"{settings.db_service_url}/langbase/inner/agent/chat",
                json=params)
        except Exception as e:
            logger_error(f"langbase/inner/agent/chat exception: {e}")
            raise InternalServerError(
                f"langbase/inner/agent/chat exception: {e}")

        if not resp.is_success:
            logger_error(f"langbase/inner/agent/chat {resp.text}")
            raise InternalServerError(
                f"langbase/inner/agent/chat: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"langbase/inner/agent/chat: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"langbase/inner/agent/chat: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")

        return resp.json()


async def post_agent_chat_sse(data: dict):
    async with httpx.AsyncClient(timeout=120) as client:
        try:
            async with client.stream(
                    method="POST",
                    url=f"{settings.sse_api_base}/streamapi/common/virtual/ai/talk/b",
                    json=data) as response:

                async for line in response.aiter_lines():
                    if line:
                        yield f"data: {line}\n\n"
        except Exception as e:
            logger_error(f"streamapi/common/virtual/ai/talk/b Error: {e}")


# 通用post service
async def post_common_request(path: str, params: dict, base: Optional[str] = None, timeout: Optional[int] = 120):
    async with httpx.AsyncClient() as client:
        url = f"{base or settings.db_service_url}{path}"
        try:
            resp = await client.post(
                url=url,
                json=params,
                timeout=timeout
            )
        except Exception as e:
            logger_error(f"{url} exception: {e}")
            raise InternalServerError(
                f"{url} exception: {e}")

        if not resp.is_success:
            logger_error(f"{url} {resp.text}")
            raise InternalServerError(
                f"{url}: {resp.text}")

        if resp.json()['code'] != 200:
            logger_error(
                f"{url}: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"{url}: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")

        ret = resp.json()
        ret['url'] = url
        return ret

# 通用get service


async def get_common_request(path: str, params: dict, base: Optional[str] = None, timeout: Optional[int] = 120):
    async with httpx.AsyncClient() as client:
        url = f"{base or settings.db_service_url}{path}"
        try:
            resp = await client.get(
                url=url,
                params=params,
                timeout=timeout
            )
        except Exception as e:
            logger_error(
                f"{url} error exception: {e}")
            raise InternalServerError(
                f"{url} error exception: {e}")

        if not resp.is_success:
            logger_error(f"{url} error {resp.text}")
            raise InternalServerError(
                f"{url} error: {resp.text}")

        # print(resp.json(), resp.json()['code'], resp.json()['code'] != 200)
        if resp.json()['code'] != 200:
            logger_error(
                f"{url} error: {resp.json()['code']} : {resp.json()['message']}")
            raise InternalServerError(f"{url} error: "
                                      f"{resp.json()['code']} : {resp.json()['message']}")
        ret = resp.json()
        ret['url'] = url
        return ret
