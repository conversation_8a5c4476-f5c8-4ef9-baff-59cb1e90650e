from typing import List, Optional, Tuple
from sqlalchemy.orm import Session

from ..models.collection import Collection
from ..schema.collection import CollectionCreate


async def get(db: Session, user_id: str, resource_id: str, resource_type: str) -> Collection:
    db_collection = db.query(Collection).filter(user_id == Collection.user_id,
                                                resource_id == Collection.resource_id,
                                                resource_type == Collection.resource_type).first()
    return db_collection


async def create(db: Session, collection: CollectionCreate) -> Collection:
    db_collection = Collection(**collection.model_dump())
    db.add(db_collection)
    db.commit()
    db.refresh(db_collection)
    return db_collection


async def delete(db: Session,
                 collection_id: str):
    db.query(Collection).filter(Collection.id == collection_id).delete()
    db.commit()


async def list(db: Session,
               user_id: Optional[str] = None,
               resource_type: Optional[str] = None,
               ids: Optional[List[str]] = None,
               offset: Optional[int] = None,
               limit: Optional[int] = None,) -> Tuple[List[Collection], int]:

    statement = db.query(Collection)

    if resource_type is not None:
        statement = statement.filter(Collection.resource_type == resource_type)

    if user_id is not None:
        statement = statement.filter(Collection.user_id == user_id)

    if ids is not None:
        statement = statement.filter(Collection.id.in_(ids))

    total = statement.count()

    if offset is not None:
        statement = statement.offset(offset)
    if limit is not None:
        statement = statement.limit(limit)

    return (statement.all(), total)
