
from typing import Annotated, Optional
from fastapi import Depends, Query, Request
from pydantic import BaseModel

from .http_session import ModelProxy

from ..misc.db import get_db
from ..service.app import get as get_app_service
from ..models.token import Token
from ..misc.errors import BadRequest
from ..schema.common import ResourceType
from ..misc.authz import admit_by_token
from ..schema.account import user_from_outter


async def get_basic_app(
    workspace_id: Annotated[str, Query(alias='workspaceID')],
    db=Depends(get_db)
):
    await get_app_service(
        db,
        is_basic=True,
        workspace_id=workspace_id,
    )


class Context(BaseModel):
    user: Optional[str]
    app_id: str


async def validate_and_get_ctx(
        request: Request,
        app_id: Annotated[Optional[str], Query(alias='appID')] = None,
        user: Annotated[Optional[str], Depends(user_from_outter)] = None,
        db=Depends(get_db)):
    token = await admit_by_token(request, db)

    if isinstance(token, Token) or isinstance(token, ModelProxy):
        if str(token.resource_type) == ResourceType.APP.value:
            if app_id is None:
                app_id = str(token.resource_id)
            elif app_id != str(token.resource_id):
                raise BadRequest("appID does not match token")
    else:
        if app_id is None:
            app_id = token.app_id
        user = token.user_id
    if app_id is None:
        raise BadRequest("param appID is required when chatting")
    return Context(
        user=user,
        app_id=app_id)
