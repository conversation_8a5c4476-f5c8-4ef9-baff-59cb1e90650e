from datetime import timed<PERSON><PERSON>
from typing import Dict, Optional
import httpx
from ..config import settings
from ..misc import redis_utils


class AigwClient:
    REDIS_TOKEN_KEY = "aigw:access_token"

    def __init__(self):
        self.base_url = settings.aigw_base_url
        self.auth_url = settings.aigw_auth_url

    async def get_token(self) -> str:
        """Get token from redis or create new one"""
        # Try to get from redis first
        token = redis_utils.get_redis_sync(self.REDIS_TOKEN_KEY)
        if token:
            return token

        # Create new token if not exists
        token = await self._create_token()

        # Store in redis with 1 day expiration
        redis_utils.set_redis_sync(
            self.REDIS_TOKEN_KEY,
            token,
            int(timedelta(days=1).total_seconds())
        )

        return token

    async def _create_token(self) -> str:
        """Create new token from auth service"""
        url = f"{self.auth_url}/api/v2/tokens"
        headers = {
            "Content-Type": "application/json"
        }
        data = {
            "user": "_dep768_langbase",
            "key": settings.aigw_auth_key
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=data)
            response.raise_for_status()
            return response.json()["token"]

    async def create_app(self, code: str, name: str, project: str, usage: str) -> Dict:
        """Create app in AIGW system"""
        url = f"{self.base_url}/aigw/v1/apps"

        # Get token from token manager
        access_token = await self.get_token()

        headers = {
            "X-Access-Token": access_token,
            "Content-Type": "application/json"
        }

        data = {
            "code": code,
            "name": name,
            "type": "project",
            "project": '',
            "usage": usage
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=data)
            response.raise_for_status()
            return response.json()

    async def query_app(self, app_code: str) -> Dict:
        """Query app details from AIGW system"""
        url = f"{self.base_url}/aigw/v1/apps/{app_code}"

        # Get token from token manager
        access_token = await self.get_token()

        headers = {
            "X-Access-Token": access_token,
            "Content-Type": "application/json"
        }

        params = {
            "query_roles": ""
        }

        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            return response.json()
