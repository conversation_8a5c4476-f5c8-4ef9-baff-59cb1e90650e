import time
import asyncio
from openai import AsyncOpenAI
import csv
import json
from datetime import datetime
from pathlib import Path
from speed_test_models import models, scenarios

# ... [previous scenario definitions remain the same] ...

# 设置一个provider 的白名单，只测试白名单中的provider
# provider_white_list = ['openai', 'google', 'anthropic', 'qwen', 'deepseek',
#                        'moonshot', 'doubao', 'minimax']
provider_white_list = ['deepseek']

model_white_list = ['claude-opus-4-20250514', 'claude-sonnet-4-20250514']


async def test_model_speed(model_name, api_key, api_base, scenario):
    openai_client = AsyncOpenAI(api_key=api_key, base_url=api_base)
    messages = [scenario]

    start_time = time.time()
    try:
        response = await openai_client.chat.completions.create(
            model=model_name,
            messages=messages,
            extra_body={
                "enable_thinking": True
            }
        )
        end_time = time.time()

        # 如果 response.usage.completion_tokens_details.reasoning_tokens 存在，说明返回的token里包含了reasoning_tokens
        # completion_tokens_details可能为None
        reasoning_tokens = response.usage.completion_tokens_details.reasoning_tokens if response.usage.completion_tokens_details else 0
        if reasoning_tokens:
            completion_tokens = response.usage.completion_tokens - reasoning_tokens
        else:
            completion_tokens = response.usage.completion_tokens

        return {
            'success': True,
            'text': response.choices[0].message.content,
            'completion_tokens': completion_tokens,
            'total_tokens': response.usage.total_tokens,
            'time_taken': end_time - start_time,
            'tokens_per_second': completion_tokens / (end_time - start_time)
        }
    except Exception as e:
        # 如果错误信息包含 "please try again later"，则等待10秒后重试
        if 'please try again later' in str(e):
            await asyncio.sleep(10)
            return await test_model_speed(model_name, api_key, api_base, scenario)
        return {
            'success': False,
            'error': str(e)
        }


def save_result(result, csv_path):
    file_exists = Path(csv_path).exists()
    with open(csv_path, 'a', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=[
            'timestamp', 'model', 'scenario', 'iteration',
            'success', 'completion_tokens', 'total_tokens',
            'time_taken', 'tokens_per_second', 'text', 'error'
        ])
        if not file_exists:
            writer.writeheader()
        writer.writerow(result)


async def main():
    # Configuration
    test_iterations = 2  # Number of times to test each scenario
    timestamp = datetime.now().strftime("%Y%m%d-%H%M")
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)

    # Progress tracking file
    progress_file = results_dir / "progress.json"
    if progress_file.exists():
        with open(progress_file, 'r') as f:
            progress = json.load(f)
        print("Resuming from previous progress...")
    else:
        progress = {
            "completed": []
        }

    # 根据是否有model白名单来决定过滤逻辑
    if model_white_list:
        filtered_models = [m for m in models if m["name"]
                           in model_white_list and m.get("enable", True)]
    else:
        filtered_models = [m for m in models if m["provider_kind"]
                           in provider_white_list and m.get("enable", True)]

    total_tests = len(filtered_models) * len(scenarios) * test_iterations
    completed = 0

    for model in filtered_models:
        model_name = model["name"]
        api_key = model["api_key"]
        api_base = model["api_base"]
        provider = model["provider_kind"]

        csv_path = results_dir / \
            f"speed_test_results_{provider}_{timestamp}.csv"

        for scenario_name in scenarios:
            for iteration in range(test_iterations):
                # Skip if this test was already completed
                test_id = f"{model_name}_{scenario_name}_{iteration}"
                if test_id in progress["completed"]:
                    print(f"Skipping completed test: {test_id}")
                    completed += 1
                    continue

                print(
                    f"Progress: {completed}/{total_tests} - Testing {model_name} on {scenario_name} (iteration {iteration + 1})")

                result = await test_model_speed(model_name, api_key, api_base, scenarios[scenario_name])

                print(
                    f"Testing {model_name} on {scenario_name} (iteration {iteration + 1}) - {result['success']}")

                # Prepare result row
                row = {
                    'model': model_name,
                    'scenario': scenario_name,
                    'iteration': iteration + 1,
                    'success': result['success'],
                    'completion_tokens': result.get('completion_tokens', None),
                    'total_tokens': result.get('total_tokens', None),
                    'time_taken': result.get('time_taken', None),
                    'tokens_per_second': result.get('tokens_per_second', None),
                    'text': result.get('text', None),
                    'error': result.get('error', None)
                }

                # Save result immediately
                save_result(row, csv_path)

                # Update progress
                progress["completed"].append(test_id)
                with open(progress_file, 'w') as f:
                    json.dump(progress, f)

                completed += 1

                # Add a small delay between requests
                await asyncio.sleep(1)

    print("\nTesting completed!")

if __name__ == "__main__":
    asyncio.run(main())
