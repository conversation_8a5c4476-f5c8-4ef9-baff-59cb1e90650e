import openai

API_KEY = 'zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh'
END_POINT = 'https://aigw.netease.com/v1'


model = 'o3-mini-2025-01-31'

client = openai.OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

resp = client.chat.completions.create(
    model=model,
    messages=[
        {
            'role': 'user',
            'content': '''你是一个云音乐智能客服服务质量评估助手，请根据以下对话内容，从用户提问的意图识别、响应质量、问题解决程度等维度，对【客服机器人】的服务进行打分与分析。请根据以下要求输出评估结果：


- **客服机器人的职责**：在用户咨询初期，代替人工客服完成问题引导与解答，尽可能在前置环节解决用户需求，减少人工转接。如果机器人已多轮答复仍无法满足用户诉求，说明其未能有效识别或解决用户问题。

- **输入格式**

  ```
  用户：YYYY-MM-DD HH:MM:SS（执行手段）
  [用户的第一句话]
  
  客服：YYYY-MM-DD HH:MM:SS（耗时 X 秒）
  [客服的回复]
  
  （对话内容依次类推，可以有多轮交互）
  ```

- **输出格式和对应字段的要求**：使用 JSON 格式。

  ```json
  {
    "用户核心问题": "（简要描述用户最关心的问题）",# 优先总结用户实际的问题，要求进人工只是不相信机器人的一种表现
    "AI问题归类": "（对用户问题进行分类，如“会员/订单进度查询”）",
    "AI识别打分": "是",# 客服的回答是否有在合理的解决用户的提问。牛头不对马嘴可以认定为未识别
    "AI识别打分解释": "（解释原因）",
    "AI解决情况打分": 95,# 分数范围为 **1～100 分**，需考虑用户情绪是否得到满足，其中 **80分 为问题解决分，20 为情绪得分**。
    "AI打分解释": "（说明客服在解决问题和情绪安抚方面的表现）",
    "是否转人工": "否",# 3~5次要求进人工后没有更多信息，可以认定为是。或者出现“很抱歉机器人不能很好解决您的需求，请点击：[转接在线人工客服](qiyu://action.qiyukf.com?command=applyHumanStaff&groupid=0)”也是是。
    "AI改进建议": "（针对不足之处提出改进建议）" #结合客服的指责
  }
  ```

- **关于执行手段的枚举如下**，通常会影响后面客服的回答链路（知晓即可，可用于改进建议的部分参考）：    
  NLP_FAQ("NLP_知识库"),WORKFLOW_FAQ("workflow_知识库")# 命中了唯一的问题
  NLP_SIMILAR("NLP_相似匹配"),WORKFLOW_GUIDE("workflow_引导")# 命中多个或没有，机器人引导更多回复
  NLP_BOT("NLP_一触即达"),WORKFLOW_BOT("workflow_一触即达")#进入唯一问题的专项流程处理
  WORKFLOW_AGENT("由我们的 Agent 处理")#由 agent 机器人定制化回复
  WORKFLOW_HUMAN("由人工处理")#转由人工
  NLP_KEYWORD("NLP_BOT紧急关键词"),
  NLP_GREETING("NLP_寒暄库"),
  NLP_UNKNOWN("NLP为空，未知错误，走默认处理"),WORKFLOW_UNKNOWN("未知错误，走默认处理")#出现异常


---

### ✅ 示例

案例一：
  输入：
    用户：2025-05-21 11:38:31（NLP_UNKNOWN）
    转人工

    客服：2025-05-21 11:38:31（耗时 0 秒）
    请先一句话简单描述您的问题。小客服会尽力为您推荐合适的解决方案哦！
    
    用户：2025-05-21 11:38:35（NLP_UNKNOWN）
    转人工
    
    客服：2025-05-21 11:38:35（耗时 0 秒）
    您可以具体描述下遇到的问题，小客服会努力为您解答哦~
    
    用户：2025-05-21 11:38:38（NLP_UNKNOWN）
    滚你妈，转人工
    
    客服：2025-05-21 11:38:38（耗时 0 秒）
    很抱歉机器人不能很好解决您的需求，请转接在线人工客服

  ```json
{
  "用户核心问题": "用户直接请求转接人工客服",
  "AI问题归类": "直接请求人工服务",
  "AI识别打分": "是",
  "AI识别打分解释": "准确识别是要进人工，并给于了一定引导，转给七鱼现有逻辑控制了",
  "AI解决情况打分": 60,
  "AI打分解释": "多次尝试拦截了用户进人工的诉求，并且引导用户给于更多信息，用户不接受，最终进入了人工，符合机器人的角色：问题解决给到55分。用户情绪较差，5",
  "是否转人工": "是",
  "AI改进建议": "可以引导用户提供更多信息，尽量由机器人进行回复"
}
  ```


案例二：
  输入：
    用户：2025-05-21 00:01:17（NLP_FAQ）
    手机号不用了如何换绑？

    客服：2025-05-21 00:01:17（耗时 0 秒）
    您好，请将App更新至最新版，点击 我要换绑手机号>>> 若原手机号已不再使用，请选择【当前手机号已停用】进行更换（见下图） 温馨提示：修改手机号需验证信息判断账号归属 1、若结果提示修改失败或当前账号不支持换绑手机号，是由于账号信息未通过校验或账号内无信息可供核实。建议使用常用设备登录账号后操作换绑试试。 2、若近期已有更换记录，建议过段时间再进行修改。
    -----
    
    * [可以解绑手机号吗？](Message)
    * [换绑手机号后会员还在吗？](Message)
    * [换绑手机号次数超限>>](Message)
    * [怎么找回账号？](Message)
    * [已绑定手机号，仍提示需要绑定](Message)
    * [如何注销账号？](Message)
    * [为什么要登录超过7天才能修改信息？](Message)


  ```json
{
  "用户核心问题": "如何更换绑定的手机号",
  "AI问题归类": "账号管理/手机号换绑",
  "AI识别打分": "是",
  "AI识别打分解释": "客服准确识别了用户关于更换绑定手机号的意图，并提供了详细的步骤和注意事项。",
  "AI解决情况打分": 95,
  "AI打分解释": "客服回复内容，详细说明了换绑手机号的步骤和可能遇到的问题，用户无更多问题，直接离开。问题解决打分80。情绪中等偏好，15",
  "是否转人工": "否",
  "AI改进建议": "无"
}
  ```

案例三：
  输入：
  用户：2025-05-21 11:02:40
  为什么我开通的一个月会员（WORKFLOW_GUIDE）

  客服：2025-05-21 11:02:43（耗时 3 秒）
   您好，您是想要咨询以下哪种情况呢？

----

   - [会员未到账](Message) 
   - [联通puls会员领的黑胶vip未到账](Message) 
   - [移动免流包送的黑胶月卡没有到账怎么办？](Message) 
   - [战网积分兑换28天的会员未到账](Message) 
   - [购买的会员礼品卡没到账](Message) 
   - [会员标识上显示下月18元续费](Message) 


  用户：2025-05-21 11:02:41（NLP_GREETING）
  没了

  客服：2025-05-21 11:02:41（耗时 0 秒）
  嗯嗯~希望可以帮到您~

  ```json
{
  "用户核心问题": "会员开通丢失问题",
  "AI问题归类": "账号管理/手机号换绑",
  "AI识别打分": "否",
  "AI识别打分解释": "客服第一次给出的引导用户并未点击，发的第二句话其实是对第一句的补充。但客服未能准确识别用户的具体问题，用户的问题描述不完整，但客服提供的选项并未完全匹配用户的问题。",
  "AI解决情况打分": 35,
  "AI打分解释": "客服未能有效解决用户的问题，用户的问题描述不完整，但客服提供的选项并未完全匹配用户的问题，情绪安抚不足。",
  "是否转人工": "否",
  "AI改进建议": "客服结合用户上下提问，综合识别用户的问题。第二次被寒暄库拦截，没走到agent workflow，需要优化"
}
  ```


案例四：
  用户：2025-05-22 14:15:19
  查询账号内的会员是哪里来的？

  客服：2025-05-22 14:15:19
  您好，请点此查看账号内黑胶会员来源 >> 可通过页面的【开通来源】查看渠道，具体支付账号无法核实，请您结合来源自行判断。
  温馨提示：①链接需在APP端打开；②赠送会员不会自动续费；③请勿共享账号信息。

  用户：2025-05-22 14:15:20
  怎样才能成为音乐合伙人？

  客服：2025-05-22 14:15:20
  您好，音乐合伙人为平台定向邀请，建议多听歌、多收藏，保持活跃，后续有机会收到邀请。
  发放周期：每4周一次，通常在周一，请关注APP私信通知。

  用户：2025-05-22 14:15:33
  已有会员再买会员，时长会顺延吗？

  客服：2025-05-22 14:15:33
  您好，若已有会员，再领取或购买新会员，时长会自动累加，请放心。
  例如：黑胶会员自动续费中，再买月卡或领88VIP权益，都会顺延有效期。

  用户：2025-05-22 14:15:38
  会员自动续费扣款规则>>

  客服：2025-05-22 14:15:38
  您好，规则如下：
  安卓：会员到期前一天自动扣费，若有其他会员卡，优先使用后再续费。
  苹果：按固定扣款日扣费，不看是否到期。扣费成功后时长会自动累加。

  用户：2025-05-22 14:15:49
  音乐合伙人收到邀请函但没席位了

  客服：2025-05-22 14:15:49
  您好，音乐合伙人席位为限量开放，本期已满，请关注APP私信等待下期。周期为每4周，通常在周一发起邀请。

  用户：2025-05-22 14:16:20
  音乐合伙人入口在哪里？

  客服：2025-05-22 14:16:20
  您好，入口在：

  APP首页下拉点击固定入口（如未显示，技术处理中）；
  可在APP内搜索“音乐合伙人”进入。

  用户：2025-05-22 14:17:22
  人工客服

  客服：2025-05-22 14:17:22
  您可以具体描述下遇到的问题，小客服会努力为您解答哦~

  用户：2025-05-22 14:17:28
  人工客服

  客服：2025-05-22 14:17:28
  您可以具体描述下遇到的问题，小客服会努力为您解答哦~

  用户：2025-05-22 14:17:32
  人工客服

  客服：2025-05-22 14:17:32
  很抱歉机器人不能很好解决您的需求，请转接在线人工客服。


  ```json
{
"用户核心问题": "1. 咨询如何成为音乐合伙人；2. 了解会员相关规则（来源、续费、时长顺延等）",
"AI问题归类": "音乐合伙人、会员权益",
"AI识别打分": "否",
"AI识别打分解释": "虽然机器人回答了多个问题，但用户仍多次请求转人工，说明未准确识别用户的核心诉求，也未建立有效对话闭环。",
"AI解决情况打分": 30,
"AI打分解释": "机器人回复内容基本准确，但多为用户点击关联问题触发，缺乏主动引导和整合解答，未真正解决用户核心问题。解决率打分20分，用户情绪中等偏低，得分10分。",
"是否转人工": "是",
"AI改进建议": "1. 优化【关联问题】的推荐逻辑，结合用户上下文意图动态调整，避免用户反复点击仍找不到答案；2. 用户多次请求人工时，应结合历史提问，提供更具针对性的引导话术，提升用户接受度和满意度。"
}
  ```


---

以下是用户的输入:
客服：2025-05-27 11:08:42 猜你想问 【热门】 - 如何取消会员自动续费？ - 会员自动续费可以退款吗？ - 我的会员冲到哪里了？ - 查询账号内的会员是哪里来的？ - 如何购买学生特惠会员？ - 会员自动扣款怎么都不通知？  用户：2025-05-27 11:08:46（NLP_UNKNOWN） 39573603429958  客服：2025-05-27 11:08:47（耗时 0 秒） 不好意思，小智还在学习，暂时理解不了您的问题，您可以尝试重新描述一下你的问题，我会尽力为您解决哦！  用户：2025-05-27 11:09:10（NLP_FAQ） 兑换码兑换  客服：2025-05-27 11:09:11（耗时 0 秒） 会员兑换码兑换方法：  1、APP端：点击 直达黑胶VIP兑换页面 >>  2、网页端：点击链接 https://music.163.com/#/member 使用兑换码→输入兑换码→立即兑换  3、PC端：顶部头像右侧的倒三角→我的会员→兑换优惠券→输入兑换码→立即兑换  *若兑换码无法兑换，需联系发放兑换码的平台核实。  PS：目前云音乐平台的兑换码只有9位数，如果您收到的兑换码非9位数或者是卡密形式，需要您查看下兑换规则，在对应发码平台操作兑换~ 相关问题: 会员兑换码显示兑换失效 站外买的云音乐联合会员怎么领取？ 海牙湾会员怎么兑换？ 中石油积分换的会员在哪里兑换？ 会员福利券在哪里？ 88vip送的云音乐会员怎么领取？  用户：2025-05-27 11:11:30（NLP_FAQ） 会员兑换码兑换时间如何查看？  客服：2025-05-27 11:11:31（耗时 1 秒） 您可以使用兑换码兑换试下，若能兑换成功，表示未使用。  关于兑换码的兑换时间及使用有效期，建议您咨询发放兑换码的平台核实。  用户：2025-05-27 11:13:10（NLP_FAQ） 兑换码兑换  客服：2025-05-27 11:13:10（耗时 0 秒） 会员兑换码兑换方法：  1、APP端：点击 直达黑胶VIP兑换页面 >>  2、网页端：点击链接 https://music.163.com/#/member 使用兑换码→输入兑换码→立即兑换  3、PC端：顶部头像右侧的倒三角→我的会员→兑换优惠券→输入兑换码→立即兑换  *若兑换码无法兑换，需联系发放兑换码的平台核实。  PS：目前云音乐平台的兑换码只有9位数，如果您收到的兑换码非9位数或者是卡密形式，需要您查看下兑换规则，在对应发码平台操作兑换~ 相关问题: 会员兑换码显示兑换失效 站外买的云音乐联合会员怎么领取？ 海牙湾会员怎么兑换？ 中石油积分换的会员在哪里兑换？ 会员福利券在哪里？ 88vip送的云音乐会员怎么领取？  用户：2025-05-27 11:15:51（WORKFLOW_UNKNOWN） 39573603429958  客服：2025-05-27 11:15:57（耗时 5 秒） 不好意思，小智还在学习，暂时理解不了您的问题，您可以尝试重新描述一下你的问题，我会尽力为您解决哦！''',
        }
    ],
    temperature=1,
    stream=True,
    max_tokens=8000,
)

# print(resp.choices[0].message.content)

# if hasattr(resp.choices[0].message, 'reasoning_content') and resp.choices[0].message.reasoning_content:
#     print(resp.choices[0].message.reasoning_content)

for chunk in resp:
    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
        print('reasoning_content: ' + chunk.choices[0].delta.reasoning_content)
    elif chunk.choices[0].delta.content:
        print('content: ' + chunk.choices[0].delta.content)
