
import asyncio
from email import message
from openai import OpenAI

# API_KEY = 'zuv0s75y-1ipj-hi.uz07cwq2lfc4g00n70t8tgb583jpzqzh'
# END_POINT = 'https://aigw.netease.com/aliyun/simulation/v1'

API_KEY = 'lzN9p6mrCVbgHQBhyYhgx2_pSNb0dDhq1EkJcWe2juw'
# END_POINT = 'https://langbase-pre.netease.com/api/v1/app/8a6bb323-d0f4-4ef5-b8e1-7ce1c46c597a/openai-proxy'
END_POINT = 'http://localhost:8000/api/v1/app/8a6bb323-d0f4-4ef5-b8e1-7ce1c46c597a/openai'

model = 'deepseek-r1-latest'

client = OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

completion = client.chat.completions.create(
    model=model,
    stream=True,
    messages=[
        {
            "role": "user",
            "content": "我叫勾勾"
        },
        {
            "role": "assistant",
            "content": "好的，你叫勾勾"
        },
        {
            "role": "user",
            "content": "我叫什么名字"
        }
    ]
)

for chunk in completion:
    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
        print('reasoning_content: ' + chunk.choices[0].delta.reasoning_content)
    elif chunk.choices[0].delta.content:
        print('content: ' + chunk.choices[0].delta.content)
