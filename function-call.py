#!/usr/bin/env python3
# -*- coding: utf-8 -*-


import openai
import json

API_KEY = 'lzN9p6mrCVbgHQBhyYhgx2_pSNb0dDhq1EkJcWe2juw'
# END_POINT = 'https://langbase-pre.netease.com/api/v1/app/8a6bb323-d0f4-4ef5-b8e1-7ce1c46c597a/openai'
END_POINT = 'http://langbase-local.netease.com:8000/api/v1/app/8a6bb323-d0f4-4ef5-b8e1-7ce1c46c597a/openai'

model = 'claude-3-7-sonnet-20250219'


def get_current_weather(location, format='celsius'):
    if location == 'San Francisco, CA':
        return 'sunny, 30' + format
    return 'rain, 20' + format


available_functions = {
    'get_current_weather': get_current_weather,
}

model = 'claude-3-7-sonnet-20250219'

tools = [
    {
        "type": "function",
        "function": {
            "name": "get_current_weather",
            "description": "Get the current weather",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA",
                    },
                    "format": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "The temperature unit to use. Infer this from the users location.",
                    },
                },
                "required": ["location", "format"],
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_n_day_weather_forecast",
            "description": "Get an N-day weather forecast",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "The city and state, e.g. San Francisco, CA",
                    },
                    "format": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "The temperature unit to use. Infer this from the users location.",
                    },
                    "num_days": {
                        "type": "integer",
                        "description": "The number of days to forecast",
                    }
                },
                "required": ["location", "format", "num_days"]
            },
        }
    },
]

messages = [
    {
        "role": "system",
        "content": "Don't make assumptions about what values to plug into functions. Ask for clarification if a user request is ambiguous.",
    },
    {
        "role": "user",
        "content": "What's the weather like today in celsius? I'm in San Francisco, CA",
    },
]

extra_body = {
    'thinking': {
        'type': 'enabled',
        'budget_tokens': 1024
    }
}

client = openai.OpenAI(
    api_key=API_KEY,
    base_url=END_POINT,
)

resp = client.chat.completions.create(
    model=model,
    messages=messages,
    tool_choice={"type": "auto"},
    tools=tools,
    stream=False,
    max_tokens=3000,
    extra_body=extra_body,
)

resp_message = resp.choices[0].message
tool_calls = resp_message.tool_calls

reasoning_content = ''
thinking_signature = ''
redacted_thinking = ''

if hasattr(resp_message, 'reasoning_content'):
    reasoning_content = resp_message.reasoning_content
    print('思维链输出：')
    print(reasoning_content, sep="\n\n")
    print('思维链输出结束')

if hasattr(resp_message, 'thinking_signature'):
    thinking_signature = resp_message.thinking_signature

if hasattr(resp_message, 'redacted_thinking'):
    redacted_thinking = resp_message.redacted_thinking

if tool_calls:

    resp_message.reasoning_content = reasoning_content
    resp_message.thinking_signature = thinking_signature
    resp_message.redacted_thinking = redacted_thinking

    # print(resp_message.model_dump_json(include=['reasoning_content', 'thinking_signature', 'redacted_thinking']))

    messages.append(resp_message.model_dump())
    for tool_call in tool_calls:
        fn_name = tool_call.function.name
        if fn_name not in available_functions:
            continue

        fn_to_call = available_functions[fn_name]
        fn_args = json.loads(tool_call.function.arguments)
        fn_resp = fn_to_call(
            location=fn_args.get('location'),
            format=fn_args.get('format'),
        )

        messages.append(
            {
                'role': 'tool',
                'tool_call_id': tool_call.id,
                'content': fn_resp,
            }
        )

    # print(messages)

    resp = client.chat.completions.create(
        model=model,
        messages=messages,
        tools=tools,  # 已拿到结果，也需要携带 tools
        stream=False,
        max_tokens=3000,
        extra_body=extra_body,
    )

    # 输出类似：
    # It's currently sunny in San Francisco with a temperature of 30°C.
    print(resp.choices[0].message.content)
    print(resp.model_dump_json())
